'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { toast } from 'react-hot-toast'
import { CheckCircle, AlertTriangle, RefreshCw, Database, Settings } from 'lucide-react'

export default function SetupCaseMovementsPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [setupResult, setSetupResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  const handleSetup = async () => {
    setIsLoading(true)
    setError(null)
    setSetupResult(null)

    try {
      // جرب الطريقة الأولى: API endpoint
      let response = await fetch('/api/setup-case-movements', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      let result = await response.json()

      // إذا فشلت، جرب الطريقة الثانية: تشغيل السكريبت
      if (!result.success) {
        console.log('جاري المحاولة بالطريقة الثانية...')
        response = await fetch('/api/run-setup-script', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        })
        result = await response.json()
      }

      if (result.success) {
        setSetupResult(result)
        toast.success('تم إعداد نظام حركة القضايا بنجاح!')
      } else {
        setError(result.error || 'فشل في الإعداد')
        toast.error(result.error || 'فشل في الإعداد')
      }
    } catch (error) {
      console.error('خطأ في الإعداد:', error)
      setError('فشل في الاتصال بالخادم')
      toast.error('فشل في الاتصال بالخادم')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            إعداد نظام حركة القضايا والتنبيهات
          </h1>
          <p className="text-gray-600">
            قم بإنشاء الجداول المطلوبة لنظام تتبع حركة القضايا والتنبيهات
          </p>
        </div>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              إعداد قاعدة البيانات
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="font-medium text-blue-900 mb-2">الجداول التي سيتم إنشاؤها:</h3>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• <strong>case_movements</strong> - جدول حركة القضايا</li>
                  <li>• <strong>notification_templates</strong> - قوالب التنبيهات</li>
                  <li>• <strong>scheduled_notifications</strong> - التنبيهات المجدولة</li>
                  <li>• <strong>notification_logs</strong> - سجل التنبيهات</li>
                  <li>• <strong>user_notification_preferences</strong> - تفضيلات المستخدمين</li>
                  <li>• <strong>notification_rules</strong> - قواعد التنبيهات</li>
                </ul>
              </div>

              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h3 className="font-medium text-green-900 mb-2">الميزات المتضمنة:</h3>
                <ul className="text-sm text-green-800 space-y-1">
                  <li>• تتبع تلقائي لجميع أنشطة القضايا</li>
                  <li>• نظام تنبيهات متقدم مع قوالب قابلة للتخصيص</li>
                  <li>• تنبيهات الجلسات (3 أيام، يوم واحد قبل الموعد)</li>
                  <li>• كشف القضايا الخاملة وإرسال تنبيهات</li>
                  <li>• إحصائيات شاملة ومؤشرات أداء</li>
                  <li>• بيانات تجريبية للاختبار</li>
                </ul>
              </div>

              <Button
                onClick={handleSetup}
                disabled={isLoading}
                className="w-full"
                size="lg"
              >
                {isLoading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    جاري الإعداد...
                  </>
                ) : (
                  <>
                    <Settings className="h-4 w-4 mr-2" />
                    بدء الإعداد
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* نتيجة الإعداد */}
        {setupResult && (
          <Card className="mb-6 border-green-200 bg-green-50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-green-800">
                <CheckCircle className="h-5 w-5" />
                تم الإعداد بنجاح!
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-green-700">{setupResult.message}</p>
                
                <div className="bg-white rounded-lg p-4 border border-green-200">
                  <h4 className="font-medium text-green-900 mb-2">الإحصائيات:</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">حركات القضايا:</span>
                      <span className="font-medium ml-2">{setupResult.data.statistics.case_movements_count}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">قوالب التنبيهات:</span>
                      <span className="font-medium ml-2">{setupResult.data.statistics.notification_templates_count}</span>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-lg p-4 border border-green-200">
                  <h4 className="font-medium text-green-900 mb-2">الجداول المنشأة:</h4>
                  <div className="flex flex-wrap gap-2">
                    {setupResult.data.tables_created.map((table: string) => (
                      <span
                        key={table}
                        className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full"
                      >
                        {table}
                      </span>
                    ))}
                  </div>
                </div>

                <div className="flex gap-4">
                  <Button
                    onClick={() => window.open('/movements', '_blank')}
                    className="flex-1"
                  >
                    عرض صفحة حركة القضايا
                  </Button>
                  <Button
                    onClick={() => window.location.href = '/'}
                    variant="outline"
                    className="flex-1"
                  >
                    العودة للرئيسية
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* رسالة الخطأ */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-800">
                <AlertTriangle className="h-5 w-5" />
                خطأ في الإعداد
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-red-700 mb-4">{error}</p>
              <Button
                onClick={handleSetup}
                variant="outline"
                className="border-red-300 text-red-700 hover:bg-red-100"
              >
                إعادة المحاولة
              </Button>
            </CardContent>
          </Card>
        )}

        {/* معلومات إضافية */}
        <Card>
          <CardHeader>
            <CardTitle>معلومات مهمة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm text-gray-600">
              <p>
                <strong>قاعدة البيانات:</strong> سيتم إنشاء الجداول في قاعدة البيانات المرتبطة بالمنفذ 3300 (mohammidev)
              </p>
              <p>
                <strong>البيانات التجريبية:</strong> سيتم إدراج بيانات تجريبية لاختبار النظام
              </p>
              <p>
                <strong>الأمان:</strong> جميع الجداول محمية بقيود البيانات والفهارس المناسبة
              </p>
              <p>
                <strong>الأداء:</strong> تم إنشاء فهارس محسنة لضمان سرعة الاستعلامات
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
