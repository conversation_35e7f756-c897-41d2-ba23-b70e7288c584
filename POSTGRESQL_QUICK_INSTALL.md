# دليل التثبيت السريع لـ PostgreSQL

## 🚨 المشكلة الحالية:
PostgreSQL غير مشغل أو غير مثبت على النظام.

## ⚡ الحلول السريعة:

### **الحل الأول: تشغيل الخدمة (إذا كان مثبت)**

#### أ) عبر الملفات المرفقة:
```batch
# شغل هذا الملف:
start-postgresql.bat

# أو للفحص:
check-postgresql-installation.bat
```

#### ب) يدوياً:
1. اضغط `Win + R` واكتب `services.msc`
2. ابحث عن خدمة تحتوي على "postgresql"
3. انقر بالزر الأيمن واختر "Start"
4. أو استخدم سطر الأوامر:
```cmd
net start postgresql-x64-15
# أو
net start postgresql-x64-14
# أو
net start postgresql-x64-13
```

---

### **الحل الثاني: تثبيت PostgreSQL (الأسرع)**

#### **الطريقة الأولى: التثبيت الرسمي (15 دقيقة)**
1. **حمل PostgreSQL:**
   - اذهب إلى: https://www.postgresql.org/download/windows/
   - حمل أحدث إصدار (PostgreSQL 15 أو 16)

2. **شغل المثبت:**
   - شغل الملف المحمل
   - اختر المجلد الافتراضي: `C:\Program Files\PostgreSQL\15`
   - **مهم:** احفظ كلمة مرور المستخدم `postgres`
   - اختر المنفذ الافتراضي: `5432`
   - اختر اللغة: `English` أو `Arabic`

3. **بعد التثبيت:**
   - ستبدأ الخدمة تلقائياً
   - جرب الاتصال مرة أخرى

#### **الطريقة الثانية: XAMPP (10 دقائق)**
1. **حمل XAMPP:**
   - اذهب إلى: https://www.apachefriends.org/
   - حمل أحدث إصدار

2. **التثبيت:**
   - شغل المثبت
   - **تأكد من تحديد PostgreSQL** أثناء التثبيت
   - اختر المجلد الافتراضي: `C:\xampp`

3. **التشغيل:**
   - شغل XAMPP Control Panel
   - ابدأ خدمة PostgreSQL
   - كلمة المرور الافتراضية عادة فارغة أو `admin`

#### **الطريقة الثالثة: Docker (5 دقائق)**
إذا كان Docker مثبت:
```bash
# تشغيل PostgreSQL في حاوية
docker run --name postgres-dev \
  -e POSTGRES_PASSWORD=yemen123 \
  -p 5432:5432 \
  -d postgres:15

# للتحقق من التشغيل
docker ps
```

---

### **الحل الثالث: استخدام قاعدة بيانات بديلة مؤقتاً**

إذا كنت تريد حلاً فورياً:

#### **SQLite (دقيقة واحدة)**
```bash
# تثبيت SQLite
npm install better-sqlite3

# تشغيل النظام مع SQLite
node create-sqlite-fallback.js
```

#### **MySQL (إذا كان متاح)**
```bash
# إذا كان MySQL مثبت
npm install mysql2
# ثم تحديث إعدادات الاتصال
```

---

## 🎯 **الخطوات الموصى بها:**

### **للحل السريع (5 دقائق):**
1. شغل `check-postgresql-installation.bat`
2. إذا وجد PostgreSQL، شغل `start-postgresql.bat`
3. إذا لم يوجد، حمل XAMPP

### **للحل الشامل (15 دقيقة):**
1. حمل PostgreSQL الرسمي
2. ثبته مع كلمة المرور `yemen123`
3. تأكد من تشغيل الخدمة
4. جرب الاتصال مرة أخرى

### **للحل المؤقت (دقيقة واحدة):**
1. استخدم SQLite كبديل
2. شغل `node create-sqlite-fallback.js`
3. حدث إعدادات المشروع لاستخدام SQLite

---

## 📋 **بعد إصلاح المشكلة:**

1. **اختبر الاتصال:**
   - افتح: http://localhost:3300/test-postgres
   - اضغط "بدء اختبار الاتصال"

2. **إنشاء الجداول:**
   - افتح: http://localhost:3300/setup-case-movements
   - اضغط "بدء الإعداد"

3. **تشغيل النظام:**
   - افتح: http://localhost:3300/movements
   - تأكد من عمل صفحة حركة القضايا

---

## 🆘 **إذا استمرت المشكلة:**

1. **تحقق من الجدار الناري:**
   - أضف استثناء للمنفذ 5432
   - أو أوقف الجدار الناري مؤقتاً للاختبار

2. **تحقق من برامج مكافحة الفيروسات:**
   - قد تحجب الاتصالات المحلية
   - أضف استثناء لـ PostgreSQL

3. **جرب منافذ أخرى:**
   - 5433, 5434, 5435
   - قد يكون PostgreSQL يعمل على منفذ مختلف

---

**💡 نصيحة:** إذا كنت تريد البدء فوراً، استخدم SQLite كحل مؤقت حتى تثبت PostgreSQL بشكل صحيح.

**🎯 الهدف:** الحصول على PostgreSQL يعمل على المنفذ 5432 مع كلمة المرور `yemen123` للوصول لقواعد البيانات الموجودة.
