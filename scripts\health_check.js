#!/usr/bin/env node
/**
 * Simple production health check:
 * - Fetches /api/departments, /api/employees, /api/clients
 * - Prints counts and sample ids/names
 * Usage: node scripts/health_check.js http://localhost:3000 http://localhost:3001
 */
const fetchFn = globalThis.fetch || (async (url, opts) => {
  const mod = await import('node-fetch');
  return mod.default(url, opts);
});

const targets = process.argv.slice(2);
if (targets.length === 0) {
  targets.push('http://localhost:3000');
}

async function getJson(url){
  const res = await fetchFn(url, { timeout: 8000 });
  const text = await res.text();
  try { return { ok: res.ok, status: res.status, json: JSON.parse(text) }; }
  catch { return { ok: res.ok, status: res.status, text } }
}

async function runOne(base){
  console.log(`\n=== Health Check @ ${base} ===`);
  const endpoints = [
    { path: '/api/departments', key: 'departments', idField: 'id', nameField: 'dept_name' },
    { path: '/api/employees', key: 'employees', idField: 'id', nameField: 'name' },
    { path: '/api/clients', key: 'clients', idField: 'id', nameField: 'name' },
    { path: '/api/chat', key: 'data', idField: 'welcomeMessage', nameField: 'welcomeMessage', method: 'GET' },
  ];
  for (const ep of endpoints){
    try {
      const url = base + ep.path;
      const result = await getJson(url);
      if (!result.ok) {
        console.log(`- ${ep.path}: HTTP ${result.status}`);
        continue;
      }
      const data = result.json || {};
      const arr = Array.isArray(data[ep.key]) ? data[ep.key] : [];
      const count = arr.length;
      const sample = arr.slice(0, 5).map(x => ({ id: x[ep.idField], name: x[ep.nameField] }));
      console.log(`- ${ep.path}: ${count} items`);
      console.log(`  sample:`, sample);
    } catch (e){
      console.log(`- ${ep.path}: ERROR ${e.message}`);
    }
  }

  // POST check for /api/chat
  try {
    const res = await fetchFn(base + '/api/chat', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ message: 'مرحبا', sessionId: 'health-check' })
    });
    const text = await res.text();
    const ok = res.ok;
    let payload;
    try { payload = JSON.parse(text) } catch { payload = { text } }
    console.log(`- POST /api/chat: ${ok ? 'OK' : 'HTTP ' + res.status}`);
    if (ok) {
      console.log('  type:', payload?.data?.type, 'message:', (payload?.data?.message||'').slice(0,80));
    } else {
      console.log('  resp:', payload);
    }
  } catch (e) {
    console.log(`- POST /api/chat: ERROR ${e.message}`)
  }
}

(async () => {
  for (const t of targets){
    await runOne(t);
  }
  console.log('\nHealth check finished.');
})();
