#!/usr/bin/env node
/**
 * Simple production health check:
 * - Fetches /api/departments, /api/employees, /api/clients
 * - Prints counts and sample ids/names
 * Usage: node scripts/health_check.js http://localhost:3000 http://localhost:3001
 */
const fetch = require('node-fetch');

const targets = process.argv.slice(2);
if (targets.length === 0) {
  targets.push('http://localhost:3000');
}

async function getJson(url){
  const res = await fetch(url, { timeout: 8000 });
  const text = await res.text();
  try { return { ok: res.ok, status: res.status, json: JSON.parse(text) }; }
  catch { return { ok: res.ok, status: res.status, text } }
}

async function runOne(base){
  console.log(`\n=== Health Check @ ${base} ===`);
  const endpoints = [
    { path: '/api/departments', key: 'departments', idField: 'id', nameField: 'dept_name' },
    { path: '/api/employees', key: 'employees', idField: 'id', nameField: 'name' },
    { path: '/api/clients', key: 'clients', idField: 'id', nameField: 'name' },
  ];
  for (const ep of endpoints){
    try {
      const result = await getJson(base + ep.path);
      if (!result.ok) {
        console.log(`- ${ep.path}: HTTP ${result.status}`);
        continue;
      }
      const data = result.json || {};
      const arr = Array.isArray(data[ep.key]) ? data[ep.key] : [];
      const count = arr.length;
      const sample = arr.slice(0, 5).map(x => ({ id: x[ep.idField], name: x[ep.nameField] }));
      console.log(`- ${ep.path}: ${count} items`);
      console.log(`  sample:`, sample);
    } catch (e){
      console.log(`- ${ep.path}: ERROR ${e.message}`);
    }
  }
}

(async () => {
  for (const t of targets){
    await runOne(t);
  }
  console.log('\nHealth check finished.');
})();
