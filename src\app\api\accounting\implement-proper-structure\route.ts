import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {

    // 1. إنشاء الحسابات الرئيسية للعملاء والموظفين والموردين

    // حساب العملاء الرئيسي
    const clientsMainAccount = await query(`
      INSERT INTO chart_of_accounts (
        account_code, account_name, account_type, account_level,
        is_main_account, is_control_account, linked_table,
        auto_create_sub_accounts, account_nature, allow_posting
      ) VALUES (
        '********', 'العملاء', 'أصول', 3,
        true, true, 'clients',
        true, 'مدين', false
      ) 
      ON CONFLICT (account_code) DO UPDATE SET
        account_name = EXCLUDED.account_name,
        linked_table = EXCLUDED.linked_table,
        auto_create_sub_accounts = EXCLUDED.auto_create_sub_accounts,
        is_control_account = EXCLUDED.is_control_account
      RETURNING id
    `)

    // حساب الموظفين الرئيسي
    const employeesMainAccount = await query(`
      INSERT INTO chart_of_accounts (
        account_code, account_name, account_type, account_level,
        is_main_account, is_control_account, linked_table,
        auto_create_sub_accounts, account_nature, allow_posting
      ) VALUES (
        '********', 'الموظفين', 'خصوم', 3,
        true, true, 'employees',
        true, 'دائن', false
      )
      ON CONFLICT (account_code) DO UPDATE SET
        account_name = EXCLUDED.account_name,
        linked_table = EXCLUDED.linked_table,
        auto_create_sub_accounts = EXCLUDED.auto_create_sub_accounts,
        is_control_account = EXCLUDED.is_control_account
      RETURNING id
    `)

    // حساب الموردين الرئيسي
    const suppliersMainAccount = await query(`
      INSERT INTO chart_of_accounts (
        account_code, account_name, account_type, account_level,
        is_main_account, is_control_account, linked_table,
        auto_create_sub_accounts, account_nature, allow_posting
      ) VALUES (
        '********', 'الموردين', 'خصوم', 3,
        true, true, 'suppliers',
        true, 'دائن', false
      )
      ON CONFLICT (account_code) DO UPDATE SET
        account_name = EXCLUDED.account_name,
        linked_table = EXCLUDED.linked_table,
        auto_create_sub_accounts = EXCLUDED.auto_create_sub_accounts,
        is_control_account = EXCLUDED.is_control_account
      RETURNING id
    `)

    // 2. التأكد من وجود عمود account_id في جداول العملاء والموظفين

    // إضافة عمود account_id للعملاء إذا لم يكن موجوداً
    try {
      await query(`
        ALTER TABLE clients 
        ADD COLUMN IF NOT EXISTS account_id INTEGER REFERENCES chart_of_accounts(id)
      `)
    } catch (error) {

    }

    // إضافة عمود account_id للموظفين إذا لم يكن موجوداً
    try {
      await query(`
        ALTER TABLE employees 
        ADD COLUMN IF NOT EXISTS account_id INTEGER REFERENCES chart_of_accounts(id)
      `)
    } catch (error) {

    }

    // إنشاء جدول الموردين إذا لم يكن موجوداً
    await query(`
      CREATE TABLE IF NOT EXISTS suppliers (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        phone VARCHAR(20),
        email VARCHAR(255),
        address TEXT,
        tax_number VARCHAR(50),
        supplier_type VARCHAR(100),
        account_id INTEGER REFERENCES chart_of_accounts(id),
        status VARCHAR(20) DEFAULT 'active',
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // 3. إنشاء حسابات فرعية للعملاء الموجودين

    const clients = await query('SELECT * FROM clients WHERE account_id IS NULL')
    const clientsMainAccountId = clientsMainAccount.rows[0].id

    let clientsLinked = 0
    for (const client of clients.rows) {
      const subAccountCode = `12010${String(client.id).padStart(3, '0')}`
      const subAccountName = `العميل - ${client.name}`

      // إنشاء حساب فرعي للعميل
      const subAccount = await query(`
        INSERT INTO chart_of_accounts (
          account_code, account_name, account_type, account_level,
          parent_id, is_sub_account, account_nature, allow_posting,
          linked_table, linked_record_id
        ) VALUES (
          $1, $2, 'أصول', 4,
          $3, true, 'مدين', true,
          'clients', $4
        ) 
        ON CONFLICT (account_code) DO UPDATE SET
          account_name = EXCLUDED.account_name,
          parent_id = EXCLUDED.parent_id,
          linked_record_id = EXCLUDED.linked_record_id
        RETURNING id
      `, [subAccountCode, subAccountName, clientsMainAccountId, client.id])

      // ربط العميل بالحساب الفرعي
      await query(
        'UPDATE clients SET account_id = $1 WHERE id = $2',
        [subAccount.rows[0].id, client.id]
      )
      clientsLinked++
    }

    // 4. إنشاء حسابات فرعية للموظفين الموجودين

    const employees = await query('SELECT * FROM employees WHERE account_id IS NULL')
    const employeesMainAccountId = employeesMainAccount.rows[0].id

    let employeesLinked = 0
    for (const employee of employees.rows) {
      const subAccountCode = `21010${String(employee.id).padStart(3, '0')}`
      const subAccountName = `الموظف - ${employee.name}`

      // إنشاء حساب فرعي للموظف
      const subAccount = await query(`
        INSERT INTO chart_of_accounts (
          account_code, account_name, account_type, account_level,
          parent_id, is_sub_account, account_nature, allow_posting,
          linked_table, linked_record_id
        ) VALUES (
          $1, $2, 'خصوم', 4,
          $3, true, 'دائن', true,
          'employees', $4
        )
        ON CONFLICT (account_code) DO UPDATE SET
          account_name = EXCLUDED.account_name,
          parent_id = EXCLUDED.parent_id,
          linked_record_id = EXCLUDED.linked_record_id
        RETURNING id
      `, [subAccountCode, subAccountName, employeesMainAccountId, employee.id])

      // ربط الموظف بالحساب الفرعي
      await query(
        'UPDATE employees SET account_id = $1 WHERE id = $2',
        [subAccount.rows[0].id, employee.id]
      )
      employeesLinked++
    }

    // 5. إحصائيات النتائج
    const stats = {
      clientsMainAccountId: clientsMainAccount.rows[0].id,
      employeesMainAccountId: employeesMainAccount.rows[0].id,
      suppliersMainAccountId: suppliersMainAccount.rows[0].id,
      clientsLinked,
      employeesLinked,
      message: 'تم تطبيق التصميم المحاسبي الصحيح بنجاح'
    }

    return NextResponse.json({
      success: true,
      message: 'تم تطبيق التصميم المحاسبي الصحيح بنجاح',
      data: stats
    })

  } catch (error) {
    console.error('❌ خطأ في تطبيق التصميم المحاسبي:', error)
    return NextResponse.json({
      success: false,
      message: 'فشل في تطبيق التصميم المحاسبي',
      error: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
