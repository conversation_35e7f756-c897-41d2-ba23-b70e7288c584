import { NextRequest, NextResponse } from 'next/server'
import { Client } from 'pg'

export async function GET() {
  try {
    console.log('🔍 مقارنة قواعد البيانات mohammi و mohammidev...')

    const databases = ['mohammi', 'mohammidev']
    const results: any = {}

    for (const dbName of databases) {
      console.log(`📊 فحص قاعدة البيانات: ${dbName}`)

      const client = new Client({
        host: 'localhost',
        port: 5432,
        user: 'postgres',
        password: 'yemen123',
        database: dbName,
        connectTimeoutMillis: 10000
      })

      try {
        await client.connect()
        console.log(`✅ متصل بقاعدة البيانات ${dbName}`)

        // 1. عدد الجداول
        const tablesResult = await client.query(`
          SELECT COUNT(*) as table_count 
          FROM information_schema.tables 
          WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
        `)
        const tableCount = parseInt(tablesResult.rows[0].table_count)

        // 2. قائمة الجداول
        const tableListResult = await client.query(`
          SELECT table_name, 
                 (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name AND table_schema = 'public') as column_count
          FROM information_schema.tables t
          WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
          ORDER BY table_name
        `)

        // 3. فحص الجداول المهمة
        const importantTables = [
          'users', 'clients', 'issues', 'case_movements', 'companies', 
          'chart_of_accounts', 'journal_entries', 'journal_entry_details',
          'vouchers', 'payments', 'documents', 'notifications'
        ]

        const tableDetails: any = {}
        for (const tableName of importantTables) {
          try {
            const countResult = await client.query(`SELECT COUNT(*) as count FROM ${tableName}`)
            const count = parseInt(countResult.rows[0].count)
            
            // آخر تحديث إذا كان هناك عمود created_at أو updated_at
            let lastUpdate = null
            try {
              const updateResult = await client.query(`
                SELECT MAX(GREATEST(
                  COALESCE(created_at, '1970-01-01'::timestamp),
                  COALESCE(updated_at, '1970-01-01'::timestamp)
                )) as last_update 
                FROM ${tableName}
              `)
              lastUpdate = updateResult.rows[0].last_update
            } catch (e) {
              // الجدول قد لا يحتوي على هذه الأعمدة
            }

            tableDetails[tableName] = { count, lastUpdate }
          } catch (error) {
            tableDetails[tableName] = { error: 'الجدول غير موجود' }
          }
        }

        // 4. حجم قاعدة البيانات
        const sizeResult = await client.query(`
          SELECT pg_size_pretty(pg_database_size('${dbName}')) as size
        `)
        const dbSize = sizeResult.rows[0].size

        // 5. آخر تعديل على قاعدة البيانات
        let lastModification = null
        try {
          const lastModResult = await client.query(`
            SELECT MAX(GREATEST(
              COALESCE(created_at, '1970-01-01'::timestamp),
              COALESCE(updated_at, '1970-01-01'::timestamp)
            )) as last_modification
            FROM (
              SELECT created_at, updated_at FROM users WHERE created_at IS NOT NULL OR updated_at IS NOT NULL
              UNION ALL
              SELECT created_at, updated_at FROM clients WHERE created_at IS NOT NULL OR updated_at IS NOT NULL
              UNION ALL
              SELECT created_at, updated_at FROM issues WHERE created_at IS NOT NULL OR updated_at IS NOT NULL
            ) combined
          `)
          lastModification = lastModResult.rows[0]?.last_modification
        } catch (e) {
          // قد تكون الجداول غير موجودة
        }

        results[dbName] = {
          tableCount,
          tables: tableListResult.rows,
          tableDetails,
          dbSize,
          lastModification,
          connected: true
        }

      } catch (error) {
        console.log(`❌ خطأ في الاتصال بقاعدة البيانات ${dbName}:`, error.message)
        results[dbName] = { error: error.message, connected: false }
      } finally {
        await client.end()
      }
    }

    // مقارنة النتائج وتحديد التوصية
    let recommendation = ''
    let reasons: string[] = []

    if (results.mohammi.connected && results.mohammidev.connected) {
      // مقارنة عدد الجداول
      if (results.mohammi.tableCount > results.mohammidev.tableCount) {
        reasons.push(`mohammi تحتوي على جداول أكثر (${results.mohammi.tableCount} مقابل ${results.mohammidev.tableCount})`)
        recommendation = 'mohammi'
      } else if (results.mohammidev.tableCount > results.mohammi.tableCount) {
        reasons.push(`mohammidev تحتوي على جداول أكثر (${results.mohammidev.tableCount} مقابل ${results.mohammi.tableCount})`)
        recommendation = 'mohammidev'
      }

      // مقارنة البيانات
      const importantTables = ['users', 'clients', 'issues', 'case_movements', 'companies', 'chart_of_accounts']
      let mohammiDataCount = 0
      let mohammidevDataCount = 0

      for (const table of importantTables) {
        if (results.mohammi.tableDetails[table] && !results.mohammi.tableDetails[table].error) {
          mohammiDataCount += results.mohammi.tableDetails[table].count
        }
        if (results.mohammidev.tableDetails[table] && !results.mohammidev.tableDetails[table].error) {
          mohammidevDataCount += results.mohammidev.tableDetails[table].count
        }
      }

      if (mohammiDataCount > mohammidevDataCount) {
        reasons.push(`mohammi تحتوي على بيانات أكثر (${mohammiDataCount} مقابل ${mohammidevDataCount} سجل)`)
        if (!recommendation) recommendation = 'mohammi'
      } else if (mohammidevDataCount > mohammiDataCount) {
        reasons.push(`mohammidev تحتوي على بيانات أكثر (${mohammidevDataCount} مقابل ${mohammiDataCount} سجل)`)
        if (!recommendation) recommendation = 'mohammidev'
      }

      // مقارنة آخر تعديل
      if (results.mohammi.lastModification && results.mohammidev.lastModification) {
        const mohammiDate = new Date(results.mohammi.lastModification)
        const mohammidevDate = new Date(results.mohammidev.lastModification)
        
        if (mohammiDate > mohammidevDate) {
          reasons.push(`mohammi تم تحديثها مؤخراً`)
          if (!recommendation) recommendation = 'mohammi'
        } else if (mohammidevDate > mohammiDate) {
          reasons.push(`mohammidev تم تحديثها مؤخراً`)
          if (!recommendation) recommendation = 'mohammidev'
        }
      }
    }

    return NextResponse.json({
      success: true,
      databases: results,
      comparison: {
        recommendation: recommendation || 'mohammi',
        reasons,
        summary: {
          mohammi: {
            tables: results.mohammi?.tableCount || 0,
            size: results.mohammi?.dbSize || 'غير متاح',
            connected: results.mohammi?.connected || false
          },
          mohammidev: {
            tables: results.mohammidev?.tableCount || 0,
            size: results.mohammidev?.dbSize || 'غير متاح',
            connected: results.mohammidev?.connected || false
          }
        }
      }
    })

  } catch (error) {
    console.error('❌ خطأ في مقارنة قواعد البيانات:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في مقارنة قواعد البيانات',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
