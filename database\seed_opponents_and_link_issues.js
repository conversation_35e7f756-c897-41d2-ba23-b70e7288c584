const { Client } = require('pg')

const DB_NAMES = (process.env.DB_NAMES ? process.env.DB_NAMES.split(',').map(s=>s.trim()).filter(Boolean) : ['mohammidev'])
const base = { host: 'localhost', port: 5432, user: 'postgres', password: 'yemen123' }

async function run(db){
  const client = new Client({ ...base, database: db })
  await client.connect()
  try {
    console.log(`\n=== Seeding opponents and linking issues on ${db} ===`)

    // Ensure opponents table
    await client.query(`
      CREATE TABLE IF NOT EXISTS opponents (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        phone VARCHAR(50),
        address TEXT,
        notes TEXT,
        created_at TIMESTAMP DEFAULT NOW()
      );
    `)

    // Ensure opponent_id column on issues
    await client.query(`
      ALTER TABLE issues
      ADD COLUMN IF NOT EXISTS opponent_id INTEGER REFERENCES opponents(id);
    `)

    // Seed opponents if empty
    const { rows } = await client.query('SELECT COUNT(*)::int as c FROM opponents')
    if (rows[0].c === 0) {
      console.log('🌱 Seeding sample opponents...')
      await client.query(`
        INSERT INTO opponents(name, phone, address, notes) VALUES
        ('شركة الأفق للتجارة', '777777001', 'صنعاء - شارع الخمسين', 'خصم في قضايا تجارية'),
        ('مؤسسة السلام', '777777002', 'عدن - المنصورة', 'خصم في قضية شيكات'),
        ('بنك اليمن الدولي', '777777003', 'تعز - التحرير', 'نزاع حول قرض مصرفي'),
        ('شركة سبأ للمقاولات', '777777004', 'إب - الميدان', 'منازعة عقد'),
        ('فرد: صالح محمد أحمد', '777777005', 'صنعاء - الحصبة', 'خلاف مدني')
      `)
    } else {
      console.log('ℹ️ Opponents already exist:', rows[0].c)
    }

    // Link first N issues to random opponents if opponent_id is null
    const { rows: issueRows } = await client.query(`SELECT id FROM issues WHERE opponent_id IS NULL ORDER BY id DESC LIMIT 20`)
    if (issueRows.length) {
      const { rows: oppRows } = await client.query(`SELECT id FROM opponents ORDER BY id`)
      for (let i = 0; i < issueRows.length; i++) {
        const issueId = issueRows[i].id
        const oppId = oppRows[i % oppRows.length].id
        await client.query(`UPDATE issues SET opponent_id = $1 WHERE id = $2`, [oppId, issueId])
      }
      console.log(`🔗 Linked ${issueRows.length} issues to opponents`)
    } else {
      console.log('ℹ️ No issues to link or already linked')
    }

    console.log('✅ Done seeding/linking')
  } catch (e) {
    console.error('❌ Error:', e)
  } finally {
    await client.end()
  }
}

;(async () => {
  for(const db of DB_NAMES){
    await run(db)
  }
  console.log('\nAll done.')
})()
