import { MessageSquare, Loader2 } from 'lucide-react'
import MainLayout from '@/components/layout/main-layout'

export default function WhatsAppLoading() {
  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="relative mb-4">
            <MessageSquare className="h-12 w-12 mx-auto text-green-600" />
            <Loader2 className="h-6 w-6 animate-spin absolute -bottom-1 -right-1 text-blue-600" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            جاري تحميل إعدادات WhatsApp
          </h3>
          <p className="text-gray-600">
            يرجى الانتظار...
          </p>
        </div>
      </div>
    </MainLayout>
  )
}
