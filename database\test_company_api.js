// اختبار API الشركة
const { Client } = require('pg');

async function testCompanyAPI() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'moham<PERSON>',
    user: 'postgres',
    password: 'yemen123'
  });
  
  try {
    await client.connect();
    console.log('✅ متصل بقاعدة البيانات');

    // 1. اختبار استعلام API مباشرة
    console.log('\n🧪 اختبار استعلام API مباشرة...');
    
    const apiQuery = `
      SELECT
        id,
        name,
        legal_name,
        registration_number,
        tax_number,
        address,
        city,
        country,
        phone,
        email,
        website,
        logo_url,
        logo_right_text,
        logo_left_text,
        logo_image_url,
        established_date,
        legal_form,
        capital,
        description,
        is_active,
        created_date,
        latitude,
        longitude
      FROM companies
      ORDER BY created_date DESC
    `;

    const result = await client.query(apiQuery);
    
    console.log(`📊 عدد الشركات: ${result.rows.length}`);
    
    if (result.rows.length > 0) {
      const company = result.rows[0];
      
      console.log('\n📋 بيانات الشركة الأولى:');
      console.log(`   ID: ${company.id}`);
      console.log(`   الاسم: ${company.name}`);
      console.log(`   الاسم القانوني: ${company.legal_name}`);
      console.log(`   رقم التسجيل: ${company.registration_number}`);
      console.log(`   الرقم الضريبي: ${company.tax_number}`);
      console.log(`   العنوان: ${company.address}`);
      console.log(`   المدينة: ${company.city}`);
      console.log(`   البلد: ${company.country}`);
      console.log(`   الهاتف: ${company.phone}`);
      console.log(`   البريد: ${company.email}`);
      console.log(`   الموقع: ${company.website}`);
      console.log(`   تاريخ التأسيس: ${company.established_date}`);
      console.log(`   الشكل القانوني: ${company.legal_form}`);
      console.log(`   رأس المال: ${company.capital}`);
      console.log(`   الوصف: ${company.description}`);
      console.log(`   نشط: ${company.is_active}`);
      
      // 2. محاكاة استجابة API
      console.log('\n📤 محاكاة استجابة API:');
      const apiResponse = {
        success: true,
        data: result.rows
      };
      
      console.log(JSON.stringify(apiResponse, null, 2));
      
      // 3. اختبار HTTP API
      console.log('\n🌐 اختبار HTTP API...');
      
      try {
        const fetch = require('node-fetch');
        const response = await fetch('http://localhost:7443/api/company');
        const httpResult = await response.json();
        
        console.log(`📊 حالة HTTP: ${response.status}`);
        console.log(`📊 نجح: ${httpResult.success}`);
        console.log(`📊 عدد البيانات: ${httpResult.data ? httpResult.data.length : 0}`);
        
        if (httpResult.success && httpResult.data && httpResult.data.length > 0) {
          const httpCompany = httpResult.data[0];
          console.log('\n📋 بيانات من HTTP API:');
          console.log(`   الاسم: ${httpCompany.name}`);
          console.log(`   البريد: ${httpCompany.email}`);
          console.log(`   الهاتف: ${httpCompany.phone}`);
          console.log(`   العنوان: ${httpCompany.address}`);
          
          // مقارنة البيانات
          console.log('\n🔍 مقارنة البيانات:');
          console.log(`   قاعدة البيانات: ${company.name}`);
          console.log(`   HTTP API: ${httpCompany.name}`);
          console.log(`   متطابقة: ${company.name === httpCompany.name ? 'نعم' : 'لا'}`);
          
        } else {
          console.log('❌ فشل في جلب البيانات من HTTP API');
          console.log('📋 الاستجابة:', httpResult);
        }
        
      } catch (httpError) {
        console.log('❌ خطأ في HTTP API:', httpError.message);
      }
      
    } else {
      console.log('❌ لا توجد شركات في قاعدة البيانات');
    }

    // 4. التحقق من الواجهة الأمامية
    console.log('\n🖥️ نصائح للواجهة الأمامية:');
    console.log('1. تأكد من أن useEffect يستدعي /api/company');
    console.log('2. تحقق من أن البيانات تُحفظ في state');
    console.log('3. تأكد من أن البيانات الافتراضية محدثة');
    console.log('4. افحص console في المتصفح للأخطاء');

    console.log('\n✅ اختبار API الشركة مكتمل');

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
  } finally {
    await client.end();
  }
}

testCompanyAPI();
