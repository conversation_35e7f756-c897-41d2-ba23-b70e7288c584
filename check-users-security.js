/**
 * سكريبت فحص أمان المستخدمين وتشفير كلمات المرور
 */

const { Client } = require('pg')
const bcrypt = require('bcryptjs')

const dbConfig = {
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'mohammi'
}

async function checkUsersSecurity() {
  const client = new Client(dbConfig)

  try {
    await client.connect()
    console.log('🔗 تم الاتصال بقاعدة البيانات mohammi')

    // 1. فحص جدول المستخدمين
    console.log('\n👥 فحص جدول المستخدمين...')
    const usersResult = await client.query(`
      SELECT
        id, username, password_hash, role, permissions,
        is_active, user_type, status, created_date
      FROM users
      ORDER BY id
    `)

    console.log(`📊 عدد المستخدمين: ${usersResult.rows.length}`)

    if (usersResult.rows.length === 0) {
      console.log('⚠️ لا يوجد مستخدمين في قاعدة البيانات')
      return
    }

    // 2. فحص كلمات المرور
    console.log('\n🔐 فحص تشفير كلمات المرور...')
    let unencryptedPasswords = []
    let encryptedPasswords = []

    for (const user of usersResult.rows) {
      console.log(`\n👤 المستخدم: ${user.username}`)
      console.log(`   🆔 المعرف: ${user.id}`)
      console.log(`   🎭 الدور: ${user.role}`)
      console.log(`   ✅ نشط: ${user.is_active}`)
      console.log(`   🔑 كلمة المرور: ${user.password_hash ? user.password_hash.substring(0, 20) + '...' : 'غير محددة'}`)

      // فحص إذا كانت كلمة المرور مشفرة
      if (user.password_hash) {
        if (user.password_hash.startsWith('$2a$') || user.password_hash.startsWith('$2b$')) {
          console.log(`   🔒 مشفرة: نعم (bcrypt)`)
          encryptedPasswords.push(user.username)
        } else {
          console.log(`   ⚠️ مشفرة: لا (نص واضح)`)
          unencryptedPasswords.push({
            id: user.id,
            username: user.username,
            password: user.password_hash
          })
        }
      } else {
        console.log(`   ❌ كلمة المرور: غير محددة`)
      }

      // فحص الصلاحيات
      if (user.permissions) {
        const perms = Array.isArray(user.permissions) ? user.permissions : JSON.parse(user.permissions || '[]')
        console.log(`   🛡️ الصلاحيات: ${perms.length} صلاحية`)
      } else {
        console.log(`   ⚠️ الصلاحيات: غير محددة`)
      }
    }

    // 3. تقرير الأمان
    console.log('\n📋 تقرير الأمان:')
    console.log(`   ✅ كلمات مرور مشفرة: ${encryptedPasswords.length}`)
    console.log(`   ⚠️ كلمات مرور غير مشفرة: ${unencryptedPasswords.length}`)

    if (encryptedPasswords.length > 0) {
      console.log(`   🔒 المستخدمين بكلمات مرور مشفرة: ${encryptedPasswords.join(', ')}`)
    }

    if (unencryptedPasswords.length > 0) {
      console.log(`   ⚠️ المستخدمين بكلمات مرور غير مشفرة: ${unencryptedPasswords.map(u => u.username).join(', ')}`)

      // عرض خيار التشفير
      console.log('\n🔧 هل تريد تشفير كلمات المرور غير المشفرة؟')
      console.log('   سيتم تشفير كلمات المرور التالية:')

      for (const user of unencryptedPasswords) {
        console.log(`   - ${user.username}: ${user.password}`)
      }
    }

    // 4. فحص جدول الأدوار
    console.log('\n🎭 فحص جدول الأدوار...')
    try {
      const rolesResult = await client.query(`
        SELECT role_name, display_name, permissions, is_active
        FROM user_roles
        ORDER BY role_name
      `)

      if (rolesResult.rows.length > 0) {
        console.log(`📊 عدد الأدوار: ${rolesResult.rows.length}`)
        rolesResult.rows.forEach(role => {
          const perms = Array.isArray(role.permissions) ? role.permissions : []
          console.log(`   🎭 ${role.role_name} (${role.display_name}): ${perms.length} صلاحية`)
        })
      } else {
        console.log('⚠️ لا توجد أدوار محددة')
      }
    } catch (error) {
      console.log('⚠️ جدول الأدوار غير موجود')
    }

    return {
      totalUsers: usersResult.rows.length,
      encryptedCount: encryptedPasswords.length,
      unencryptedCount: unencryptedPasswords.length,
      unencryptedUsers: unencryptedPasswords
    }

  } catch (error) {
    console.error('❌ خطأ في فحص أمان المستخدمين:', error)
    throw error
  } finally {
    await client.end()
    console.log('\n🔌 تم قطع الاتصال بقاعدة البيانات')
  }
}

async function encryptPasswords(unencryptedUsers) {
  const client = new Client(dbConfig)

  try {
    await client.connect()
    console.log('\n🔐 بدء تشفير كلمات المرور...')

    for (const user of unencryptedUsers) {
      console.log(`🔒 تشفير كلمة مرور المستخدم: ${user.username}`)

      // تشفير كلمة المرور
      const hashedPassword = await bcrypt.hash(user.password, 12)

      // تحديث قاعدة البيانات
      await client.query(`
        UPDATE users
        SET password_hash = $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
      `, [hashedPassword, user.id])

      console.log(`   ✅ تم تشفير كلمة مرور ${user.username}`)
    }

    console.log('\n✅ تم تشفير جميع كلمات المرور بنجاح!')

  } catch (error) {
    console.error('❌ خطأ في تشفير كلمات المرور:', error)
    throw error
  } finally {
    await client.end()
  }
}

// تشغيل الفحص
if (require.main === module) {
  checkUsersSecurity()
    .then((result) => {
      console.log('\n🎯 انتهى فحص الأمان')

      if (result.unencryptedCount > 0) {
        console.log('\n⚠️ تحذير: يوجد كلمات مرور غير مشفرة!')
        console.log('💡 لتشفيرها، شغل الأمر: node check-users-security.js --encrypt')
      } else {
        console.log('\n✅ جميع كلمات المرور مشفرة بأمان')
      }
    })
    .catch((error) => {
      console.error('\n💥 فشل فحص الأمان:', error.message)
      process.exit(1)
    })
}

module.exports = { checkUsersSecurity, encryptPasswords }
