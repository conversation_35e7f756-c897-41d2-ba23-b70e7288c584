/**
 * مكتبة إنشاء الحسابات الفرعية تلقائياً
 */

import { query } from '@/lib/database'

interface SubAccountResult {
  success: boolean
  message: string
  accountId?: number
  accountCode?: string
}

/**
 * إنشاء حساب فرعي تلقائياً لعنصر جديد
 */
export async function createSubAccount(
  entityType: 'clients' | 'employees' | 'suppliers',
  entityId: number,
  entityName: string
): Promise<SubAccountResult> {
  try {
    // تحديد معلومات الحساب الرئيسي حسب نوع الكيان
    const mainAccountInfo = getMainAccountInfo(entityType)
    
    // البحث عن الحساب الرئيسي أو إنشاؤه
    let mainAccount = await query(`
      SELECT id, account_code 
      FROM chart_of_accounts 
      WHERE account_code = $1
    `, [mainAccountInfo.code])
    
    let mainAccountId: number
    let mainAccountCode: string
    
    if (mainAccount.rows.length === 0) {
      // إنشاء الحساب الرئيسي
      const newMainAccount = await query(`
        INSERT INTO chart_of_accounts (
          account_code, account_name, account_type, parent_id,
          is_active, allow_transactions, current_balance, account_level
        ) VALUES ($1, $2, $3, NULL, true, false, 0, 1)
        RETURNING id, account_code
      `, [mainAccountInfo.code, mainAccountInfo.name, mainAccountInfo.type])
      
      mainAccountId = newMainAccount.rows[0].id
      mainAccountCode = newMainAccount.rows[0].account_code
    } else {
      mainAccountId = mainAccount.rows[0].id
      mainAccountCode = mainAccount.rows[0].account_code
    }
    
    // إنشاء كود الحساب الفرعي
    const subAccountCode = `${mainAccountCode}${String(entityId).padStart(3, '0')}`
    const subAccountName = `${mainAccountInfo.prefix}: ${entityName}`
    
    // التحقق من عدم وجود الحساب الفرعي مسبقاً
    const existingSubAccount = await query(`
      SELECT id FROM chart_of_accounts 
      WHERE account_code = $1 OR (parent_id = $2 AND account_name = $3)
    `, [subAccountCode, mainAccountId, subAccountName])
    
    if (existingSubAccount.rows.length > 0) {
      const existingAccountId = existingSubAccount.rows[0].id
      
      // ربط الكيان بالحساب الموجود
      await updateEntityAccount(entityType, entityId, existingAccountId, subAccountCode)
      
      return {
        success: true,
        message: 'تم ربط الحساب الموجود بنجاح',
        accountId: existingAccountId,
        accountCode: subAccountCode
      }
    }
    
    // إنشاء الحساب الفرعي الجديد
    const newSubAccount = await query(`
      INSERT INTO chart_of_accounts (
        account_code, account_name, account_type, parent_id,
        is_active, allow_transactions, current_balance, account_level
      ) VALUES ($1, $2, $3, $4, true, true, 0, 2)
      RETURNING id
    `, [subAccountCode, subAccountName, mainAccountInfo.type, mainAccountId])
    
    const subAccountId = newSubAccount.rows[0].id
    
    // ربط الكيان بالحساب الجديد
    await updateEntityAccount(entityType, entityId, subAccountId, subAccountCode)
    
    return {
      success: true,
      message: 'تم إنشاء الحساب الفرعي بنجاح',
      accountId: subAccountId,
      accountCode: subAccountCode
    }
    
  } catch (error) {
    console.error('Error creating sub account:', error)
    return {
      success: false,
      message: 'فشل في إنشاء الحساب الفرعي'
    }
  }
}

/**
 * الحصول على معلومات الحساب الرئيسي حسب نوع الكيان
 */
function getMainAccountInfo(entityType: 'clients' | 'employees' | 'suppliers') {
  switch (entityType) {
    case 'clients':
      return {
        code: '1121',
        name: 'حسابات العملاء',
        type: 'أصول',
        prefix: 'عميل'
      }
    case 'employees':
      return {
        code: '1122',
        name: 'حسابات الموظفين',
        type: 'أصول',
        prefix: 'موظف'
      }
    case 'suppliers':
      return {
        code: '2123',
        name: 'حسابات الموردين',
        type: 'خصوم',
        prefix: 'مورد'
      }
    default:
      throw new Error('نوع كيان غير مدعوم')
  }
}

/**
 * تحديث ربط الكيان بالحساب
 */
async function updateEntityAccount(
  entityType: 'clients' | 'employees' | 'suppliers',
  entityId: number,
  accountId: number,
  accountCode: string
) {
  const tableName = entityType
  
  if (entityType === 'clients') {
    await query(`
      UPDATE ${tableName} 
      SET account_id = $1, account_code = $2 
      WHERE id = $3
    `, [accountId, accountCode, entityId])
  } else {
    await query(`
      UPDATE ${tableName} 
      SET account_id = $1 
      WHERE id = $2
    `, [accountId, entityId])
  }
}

/**
 * حذف الحساب الفرعي عند حذف الكيان
 */
export async function deleteSubAccount(
  entityType: 'clients' | 'employees' | 'suppliers',
  entityId: number
): Promise<SubAccountResult> {
  try {
    // الحصول على معرف الحساب المرتبط
    const entityResult = await query(`
      SELECT account_id FROM ${entityType} WHERE id = $1
    `, [entityId])
    
    if (entityResult.rows.length === 0 || !entityResult.rows[0].account_id) {
      return {
        success: true,
        message: 'لا يوجد حساب مرتبط للحذف'
      }
    }
    
    const accountId = entityResult.rows[0].account_id
    
    // التحقق من عدم وجود معاملات على الحساب
    const transactionsCheck = await query(`
      SELECT COUNT(*) as count 
      FROM journal_entries 
      WHERE account_id = $1
    `, [accountId])
    
    const transactionCount = parseInt(transactionsCheck.rows[0].count)
    
    if (transactionCount > 0) {
      // إلغاء تفعيل الحساب بدلاً من حذفه
      await query(`
        UPDATE chart_of_accounts 
        SET is_active = false, account_name = account_name || ' (محذوف)'
        WHERE id = $1
      `, [accountId])
      
      return {
        success: true,
        message: 'تم إلغاء تفعيل الحساب (يحتوي على معاملات)'
      }
    } else {
      // حذف الحساب نهائياً
      await query(`
        DELETE FROM chart_of_accounts WHERE id = $1
      `, [accountId])
      
      return {
        success: true,
        message: 'تم حذف الحساب الفرعي بنجاح'
      }
    }
    
  } catch (error) {
    console.error('Error deleting sub account:', error)
    return {
      success: false,
      message: 'فشل في حذف الحساب الفرعي'
    }
  }
}

/**
 * تحديث اسم الحساب الفرعي عند تحديث اسم الكيان
 */
export async function updateSubAccountName(
  entityType: 'clients' | 'employees' | 'suppliers',
  entityId: number,
  newName: string
): Promise<SubAccountResult> {
  try {
    // الحصول على معرف الحساب المرتبط
    const entityResult = await query(`
      SELECT account_id FROM ${entityType} WHERE id = $1
    `, [entityId])
    
    if (entityResult.rows.length === 0 || !entityResult.rows[0].account_id) {
      return {
        success: true,
        message: 'لا يوجد حساب مرتبط للتحديث'
      }
    }
    
    const accountId = entityResult.rows[0].account_id
    const mainAccountInfo = getMainAccountInfo(entityType)
    const newAccountName = `${mainAccountInfo.prefix}: ${newName}`
    
    // تحديث اسم الحساب
    await query(`
      UPDATE chart_of_accounts 
      SET account_name = $1 
      WHERE id = $2
    `, [newAccountName, accountId])
    
    return {
      success: true,
      message: 'تم تحديث اسم الحساب بنجاح'
    }
    
  } catch (error) {
    console.error('Error updating sub account name:', error)
    return {
      success: false,
      message: 'فشل في تحديث اسم الحساب'
    }
  }
}