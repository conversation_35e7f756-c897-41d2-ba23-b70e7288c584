import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

async function ensureCaseDocuments() {
  await query(`
    CREATE TABLE IF NOT EXISTS case_documents (
      id SERIAL PRIMARY KEY,
      case_id INTEGER NOT NULL,
      follow_id INTEGER NULL,
      file_name TEXT NOT NULL,
      mime_type TEXT NOT NULL,
      size_bytes INTEGER NOT NULL,
      content BYTEA NOT NULL,
      uploaded_by INTEGER NULL,
      created_at TIMESTAMP DEFAULT NOW()
    );
  `)
}

// POST: رفع وثائق (base64) وربطها بالقضية والمتابعة
export async function POST(request: NextRequest) {
  try {
    await ensureCaseDocuments()
    const body = await request.json()
    const { case_id, follow_id, uploaded_by, files } = body as any

    if (!case_id || !Array.isArray(files) || files.length === 0) {
      return NextResponse.json({ success: false, error: 'case_id و files مطلوبة' }, { status: 400 })
    }

    for (const f of files) {
      const { file_name, mime_type, base64 } = f
      if (!file_name || !mime_type || !base64) continue
      const buf = Buffer.from(base64, 'base64')
      await query(
        `INSERT INTO case_documents (case_id, follow_id, file_name, mime_type, size_bytes, content, uploaded_by)
         VALUES ($1,$2,$3,$4,$5,$6,$7)`,
        [case_id, follow_id || null, file_name, mime_type, buf.length, buf, uploaded_by || null]
      )
    }

    return NextResponse.json({ success: true })
  } catch (e:any) {
    console.error('Error uploading case documents:', e)
    return NextResponse.json({ success: false, error: 'فشل رفع الوثائق' }, { status: 500 })
  }
}

// GET: قائمة وثائق القضية
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const caseId = searchParams.get('case_id')
    if (!caseId) return NextResponse.json({ success: false, error: 'case_id مطلوب' }, { status: 400 })

    const rows = await query(
      `SELECT id, case_id, follow_id, file_name, mime_type, size_bytes, created_at
       FROM case_documents WHERE case_id = $1 ORDER BY id DESC`,
      [caseId]
    )
    return NextResponse.json({ success: true, data: rows.rows })
  } catch (e:any) {
    console.error('Error listing case documents:', e)
    return NextResponse.json({ success: false, error: 'فشل في جلب وثائق القضية' }, { status: 500 })
  }
}
