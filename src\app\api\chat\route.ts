import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// دالة لتحديد قاعدة البيانات حسب المنفذ
function getDatabaseByPort(port: string): string {
  switch (port) {
    case '7443':
      return 'mohammi'
    case '8914':
      return 'rubaie'
    default:
      return 'mohammi' // افتراضي
  }
}

// دالة للحصول على المنفذ من الطلب
function getPortFromRequest(request: NextRequest): string {
  const host = request.headers.get('host') || ''
  const port = host.split(':')[1] || '7443'
  return port
}

// دالة للحصول على إعدادات الذكاء الاصطناعي
async function getAISettings(dbName: string) {
  try {
    const { Pool } = require('pg')
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    })

    const result = await pool.query(`
      SELECT * FROM ai_settings
      WHERE id = 1
    `)

    await pool.end()
    return result.rows[0] || null
  } catch (error) {
    console.error('خطأ في جلب إعدادات الذكاء الاصطناعي:', error)
    return null
  }
}

// دالة للحصول على بيانات الشركة
async function getCompanyData(dbName: string) {
  try {
    const { Pool } = require('pg')
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    })

    const result = await pool.query(`
      SELECT * FROM companies
      ORDER BY id LIMIT 1
    `)

    await pool.end()
    return result.rows[0] || null
  } catch (error) {
    console.error('خطأ في جلب بيانات الشركة:', error)
    return null
  }
}

// دالة للحصول على الخدمات
async function getServices(dbName: string) {
  try {
    const { Pool } = require('pg')
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    })

    const result = await pool.query(`
      SELECT title, description, slug FROM serviceslow
      WHERE is_active = true
      ORDER BY sort_order ASC
    `)

    await pool.end()
    return result.rows || []
  } catch (error) {
    console.error('خطأ في جلب الخدمات:', error)
    return []
  }
}

// دالة للحصول على المكتبة القانونية
async function getLegalLibrary(dbName: string) {
  try {
    const { Pool } = require('pg')
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: dbName,
      password: 'yemen123',
      port: 5432,
    })

    const result = await pool.query(`
      SELECT title, description, category FROM legal_library
      WHERE is_active = true
      ORDER BY created_date DESC
      LIMIT 10
    `)

    await pool.end()
    return result.rows || []
  } catch (error) {
    console.error('خطأ في جلب المكتبة القانونية:', error)
    return []
  }
}

// دالة لمعالجة الرسالة وإنشاء الرد
async function processMessage(message: string, companyData: any, aiSettings: any, services: any[], legalLibrary: any[]) {
  const lowerMessage = message.toLowerCase().trim()

  // رسائل الترحيب
  const greetings = ['مرحبا', 'السلام عليكم', 'أهلا', 'صباح الخير', 'مساء الخير', 'هلا', 'اهلين']
  if (greetings.some(greeting => lowerMessage.includes(greeting))) {
    return {
      type: 'greeting',
      message: aiSettings?.welcome_message || `مرحباً بك في ${companyData?.name || 'مكتبنا القانوني'}! كيف يمكنني مساعدتك اليوم؟`
    }
  }

  // أسئلة عن الأسعار والرسوم - يجب أن تأتي قبل الاستشارة العامة
  const priceKeywords = ['سعر', 'تكلفة', 'رسوم', 'أتعاب', 'كم يكلف', 'كم السعر', 'كم تكلفة']
  if (priceKeywords.some(keyword => lowerMessage.includes(keyword))) {
    return {
      type: 'pricing',
      message: `أسعار الخدمات القانونية تختلف حسب نوع القضية وتعقيدها.\n\nللحصول على عرض سعر دقيق، يرجى:\n• التواصل مع مكتبنا مباشرة\n• تحديد نوع الخدمة المطلوبة\n• شرح تفاصيل القضية\n\n📞 ${companyData?.phone || 'اتصل بنا'} للاستفسار عن الأسعار`
    }
  }

  // طلبات الاستشارة القانونية - رد مخصص وذكي
  const consultationKeywords = ['استشارة', 'استشارات', 'مشورة', 'رأي قانوني', 'نصيحة قانونية', 'احتاج استشارة', 'اريد استشارة']
  if (consultationKeywords.some(keyword => lowerMessage.includes(keyword))) {
    return {
      type: 'consultation',
      message: `بالطبع! يسعدني مساعدتك في الحصول على الاستشارة القانونية المناسبة.\n\nما هو نوع الاستشارة التي تحتاجها؟ يمكنك إخباري بتفاصيل أكثر عن موضوعك القانوني وسأوجهك للمختص المناسب.\n\nأو يمكنك التواصل مباشرة مع مكتبنا:\n📞 ${companyData?.phone || 'اتصل بنا'}`
    }
  }

  // أسئلة عامة حول الخدمات - رد مختصر
  const serviceKeywords = ['خدمات', 'خدمة', 'تخصص', 'مجال', 'عمل', 'قانوني', 'محاماة', 'ماذا تقدمون']
  if (serviceKeywords.some(keyword => lowerMessage.includes(keyword))) {
    const mainServices = services.slice(0, 3) // أول 3 خدمات فقط
    let response = `نحن متخصصون في:\n\n`
    mainServices.forEach((service, index) => {
      response += `• ${service.title}\n`
    })
    response += `\nهل تحتاج معلومات أكثر عن أي من هذه المجالات؟`

    return {
      type: 'services',
      message: response
    }
  }

  // أسئلة حول التواصل
  const contactKeywords = ['تواصل', 'رقم', 'هاتف', 'عنوان', 'موقع', 'مكان', 'اتصال', 'واتساب']
  if (contactKeywords.some(keyword => lowerMessage.includes(keyword))) {
    let response = `يمكنك التواصل معنا من خلال:\n\n`

    if (companyData?.phone) {
      response += `📞 **الهاتف:** ${companyData.phone}\n`
    }

    if (companyData?.email) {
      response += `📧 **البريد الإلكتروني:** ${companyData.email}\n`
    }

    if (companyData?.address) {
      response += `📍 **العنوان:** ${companyData.address}\n`
    }

    if (companyData?.working_hours) {
      response += `🕐 **ساعات العمل:** ${companyData.working_hours}\n`
    }

    response += `\nنحن في خدمتك دائماً!`

    return {
      type: 'contact',
      message: response
    }
  }

  // أسئلة حول المكتبة القانونية
  const libraryKeywords = ['قانون', 'قوانين', 'مكتبة', 'وثائق', 'مراجع', 'نصوص', 'تشريع']
  if (libraryKeywords.some(keyword => lowerMessage.includes(keyword))) {
    let response = `لدينا مكتبة قانونية شاملة تحتوي على:\n\n`

    if (legalLibrary.length > 0) {
      legalLibrary.forEach((doc, index) => {
        response += `${index + 1}. **${doc.title}** (${doc.category})\n`
      })
      response += `\nللحصول على الوثائق كاملة، يرجى التواصل معنا.`
    } else {
      response += `مجموعة شاملة من القوانين والتشريعات اليمنية والعربية.`
    }

    return {
      type: 'library',
      message: response
    }
  }

  // أسئلة حول الشركة
  const companyKeywords = ['من أنتم', 'عنكم', 'تعريف', 'شركة', 'مكتب', 'تأسيس', 'خبرة']
  if (companyKeywords.some(keyword => lowerMessage.includes(keyword))) {
    let response = `**${companyData?.name || 'مكتبنا القانوني'}**\n\n`

    if (companyData?.description) {
      response += `${companyData.description}\n\n`
    }

    if (companyData?.established_date) {
      const establishedYear = new Date(companyData.established_date).getFullYear()
      const currentYear = new Date().getFullYear()
      const experience = currentYear - establishedYear
      response += `🏛️ **تأسس عام:** ${establishedYear} (${experience} سنة من الخبرة)\n`
    }

    if (companyData?.legal_form) {
      response += `📋 **الشكل القانوني:** ${companyData.legal_form}\n`
    }

    response += `\nنحن هنا لخدمتك بأفضل الحلول القانونية.`

    return {
      type: 'company',
      message: response
    }
  }

  // أسئلة محددة حول مجالات قانونية
  const legalAreas = {
    'جنائي': ['جنائي', 'جريمة', 'اتهام', 'محكمة جنائية', 'قضية جنائية'],
    'مدني': ['مدني', 'عقد', 'دين', 'تعويض', 'نزاع مدني'],
    'تجاري': ['تجاري', 'شركة', 'استثمار', 'عقد تجاري', 'نزاع تجاري'],
    'عمل': ['عمل', 'موظف', 'راتب', 'فصل', 'حقوق العمال'],
    'أسرة': ['طلاق', 'زواج', 'حضانة', 'نفقة', 'ميراث']
  }

  for (const [area, keywords] of Object.entries(legalAreas)) {
    if (keywords.some(keyword => lowerMessage.includes(keyword))) {
      return {
        type: 'specific_area',
        message: `أفهم أنك تحتاج مساعدة في قضايا ${area === 'أسرة' ? 'الأحوال الشخصية' : 'القانون ال' + area}.\n\nيمكنني توجيهك للمختص المناسب في هذا المجال. هل يمكنك إخباري بالمزيد من التفاصيل عن حالتك؟\n\nأو تواصل مباشرة:\n📞 ${companyData?.phone || 'اتصل بنا'}`
      }
    }
  }

  // أسئلة عن المواعيد
  const appointmentKeywords = ['موعد', 'مقابلة', 'لقاء', 'زيارة', 'حجز موعد']
  if (appointmentKeywords.some(keyword => lowerMessage.includes(keyword))) {
    return {
      type: 'appointment',
      message: `لحجز موعد للاستشارة:\n\n📞 اتصل بنا على: ${companyData?.phone || 'الرقم غير متوفر'}\n📧 أو راسلنا على: ${companyData?.email || 'البريد غير متوفر'}\n\nسنكون سعداء لترتيب موعد يناسبك لمناقشة قضيتك بالتفصيل.`
    }
  }

  // رد افتراضي ذكي
  return {
    type: 'default',
    message: `شكراً لتواصلك معنا! \n\nيمكنني مساعدتك في:\n• الاستفسار عن خدماتنا القانونية\n• توجيهك للمختص المناسب\n• معلومات التواصل والمواعيد\n\nما الذي تحتاج مساعدة فيه تحديداً؟\n\n📞 ${companyData?.phone || 'اتصل بنا'}`
  }
}

// POST - معالجة رسالة المحادثة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { message, sessionId } = body

    if (!message || !message.trim()) {
      return NextResponse.json(
        { success: false, error: 'الرسالة مطلوبة' },
        { status: 400 }
      )
    }

    // تحديد قاعدة البيانات حسب المنفذ
    const port = getPortFromRequest(request)
    const dbName = getDatabaseByPort(port)

    // جلب البيانات المطلوبة
    const [aiSettings, companyData, services, legalLibrary] = await Promise.all([
      getAISettings(dbName),
      getCompanyData(dbName),
      getServices(dbName),
      getLegalLibrary(dbName)
    ])

    if (!aiSettings) {
      return NextResponse.json(
        { success: false, error: 'إعدادات الذكاء الاصطناعي غير متوفرة' },
        { status: 500 }
      )
    }

    // التحقق من تفعيل النظام
    const isEnabled = aiSettings.is_enabled !== undefined ? aiSettings.is_enabled : aiSettings.enabled;
    if (!isEnabled) {
      return NextResponse.json({
        success: true,
        data: {
          type: 'disabled',
          message: 'نعتذر، خدمة المحادثة غير متوفرة حالياً. يرجى التواصل معنا مباشرة.',
          contact: {
            phone: companyData?.phone,
            email: companyData?.email
          }
        }
      })
    }

    // معالجة الرسالة
    const response = await processMessage(message, companyData, aiSettings, services, legalLibrary)

    // حفظ المحادثة في قاعدة البيانات المناسبة
    try {
      const { Pool } = require('pg')
      const pool = new Pool({
        user: 'postgres',
        host: 'localhost',
        database: dbName,
        password: 'yemen123',
        port: 5432,
      })

      await pool.query(`
        INSERT INTO chat_logs (session_id, user_message, bot_response, response_type, created_at)
        VALUES ($1, $2, $3, $4, NOW())
      `, [sessionId || 'anonymous', message, response.message, response.type])

      await pool.end()
    } catch (logError) {
      console.error('خطأ في حفظ سجل المحادثة:', logError)
      // لا نوقف العملية إذا فشل الحفظ
    }

    return NextResponse.json({
      success: true,
      data: {
        ...response,
        timestamp: new Date().toISOString(),
        companyInfo: {
          name: companyData?.name,
          phone: companyData?.phone,
          email: companyData?.email
        }
      }
    })

  } catch (error) {
    console.error('خطأ في معالجة رسالة المحادثة:', error)
    return NextResponse.json(
      { success: false, error: 'خطأ في معالجة الرسالة' },
      { status: 500 }
    )
  }
}

// GET - جلب رسالة الترحيب
export async function GET(request: NextRequest) {
  try {
    // تحديد قاعدة البيانات حسب المنفذ
    const port = getPortFromRequest(request)
    const dbName = getDatabaseByPort(port)

    const [aiSettings, companyData] = await Promise.all([
      getAISettings(dbName),
      getCompanyData(dbName)
    ])

    const isEnabled = aiSettings?.is_enabled !== undefined ? aiSettings.is_enabled : aiSettings?.enabled;
    if (!aiSettings || !isEnabled) {
      return NextResponse.json({
        success: false,
        error: 'خدمة المحادثة غير متوفرة'
      })
    }

    return NextResponse.json({
      success: true,
      data: {
        welcomeMessage: aiSettings.welcome_message || `مرحباً بك في ${companyData?.name || 'مكتبنا القانوني'}!`,
        isEnabled: isEnabled,
        companyInfo: {
          name: companyData?.name,
          phone: companyData?.phone,
          email: companyData?.email
        }
      }
    })

  } catch (error) {
    console.error('خطأ في جلب إعدادات المحادثة:', error)
    return NextResponse.json(
      { success: false, error: 'خطأ في جلب الإعدادات' },
      { status: 500 }
    )
  }
}
