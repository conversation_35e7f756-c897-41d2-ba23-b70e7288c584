'use client'

import { useEffect, useRef } from 'react'

interface VanillaSelectProps {
  label: string
  placeholder: string
  onChange?: (value: string) => void
}

export function VanillaSelect({ label, placeholder, onChange }: VanillaSelectProps) {
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!containerRef.current) return

    const container = containerRef.current

    // إنشاء HTML مباشرة
    container.innerHTML = `
      <div style="margin-bottom: 16px;">
        <label style="display: block; font-weight: 600; margin-bottom: 8px; color: #374151;">
          ${label}
        </label>
        <select 
          id="vanilla-select" 
          style="
            width: 100%; 
            padding: 12px; 
            border: 1px solid #d1d5db; 
            border-radius: 6px; 
            background: white;
            font-size: 14px;
          "
        >
          <option value="">جاري التحميل...</option>
        </select>
        <div id="status" style="margin-top: 8px; font-size: 12px; color: #6b7280;">
          جاري تحميل البيانات...
        </div>
      </div>
    `

    const select = container.querySelector('#vanilla-select') as HTMLSelectElement
    const status = container.querySelector('#status') as HTMLDivElement

    // جلب البيانات
    const fetchData = async () => {
      try {
        
        
        // جلب العملاء
        const clientsResponse = await fetch('/api/clients')
        const clientsData = await clientsResponse.json()
        

        // جلب الموظفين
        const employeesResponse = await fetch('/api/employees')
        const employeesData = await employeesResponse.json()
        

        // مسح الخيارات الحالية
        select.innerHTML = `<option value="">${placeholder}</option>`

        let totalOptions = 0

        // إضافة العملاء
        if (clientsData.success && clientsData.clients.length > 0) {
          const clientsGroup = document.createElement('optgroup')
          clientsGroup.label = `👤 العملاء (${clientsData.clients.length})`
          
          clientsData.clients.forEach((client: any) => {
            const option = document.createElement('option')
            option.value = `client_${client.id}`
            option.textContent = `${client.name} - ${client.account_code || 'بدون كود'}`
            clientsGroup.appendChild(option)
            totalOptions++
          })
          
          select.appendChild(clientsGroup)
        }

        // إضافة الموظفين
        if (employeesData.success && employeesData.employees.length > 0) {
          const employeesGroup = document.createElement('optgroup')
          employeesGroup.label = `👨‍💼 الموظفين (${employeesData.employees.length})`
          
          employeesData.employees.forEach((employee: any) => {
            const option = document.createElement('option')
            option.value = `employee_${employee.id}`
            option.textContent = `${employee.name} - ${employee.account_code || 'بدون كود'}`
            employeesGroup.appendChild(option)
            totalOptions++
          })
          
          select.appendChild(employeesGroup)
        }

        status.textContent = `✅ تم تحميل ${totalOptions} خيار بنجاح`
        status.style.color = '#059669'
        
        

      } catch (error) {
        console.error('❌ VanillaSelect: خطأ:', error)
        select.innerHTML = `<option value="">خطأ في التحميل</option>`
        status.textContent = `❌ خطأ في تحميل البيانات: ${error}`
        status.style.color = '#dc2626'
      }
    }

    // إضافة مستمع للتغيير
    select.addEventListener('change', (e) => {
      const target = e.target as HTMLSelectElement
      
      if (onChange) {
        onChange(target.value)
      }
    })

    // بدء جلب البيانات
    fetchData()

  }, [label, placeholder, onChange])

  return <div ref={containerRef}></div>
}
