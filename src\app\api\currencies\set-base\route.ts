import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// PUT - تعيين العملة الأساسية
export async function PUT(request: NextRequest) {
  try {
    const url = new URL(request.url)
    const id = url.searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف العملة مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من وجود العملة
    const checkResult = await query(`SELECT id, currency_name FROM currencies WHERE id = $1 AND is_active = true`, [id])
    
    if (checkResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'العملة غير موجودة' },
        { status: 404 }
      )
    }

    await query('BEGIN')

    try {
      // إلغاء العملة الأساسية الحالية
      await query(`UPDATE currencies SET is_base_currency = false WHERE is_base_currency = true`)
      
      // تعيين العملة الجديدة كأساسية
      await query(`
        UPDATE currencies 
        SET 
          is_base_currency = true,
          updated_date = NOW()
        WHERE id = $1
      `, [id])

      await query('COMMIT')

      return NextResponse.json({
        success: true,
        message: `تم تعيين ${checkResult.rows[0].currency_name} كعملة أساسية بنجاح`
      })
    } catch (error) {
      await query('ROLLBACK')
      throw error
    }
  } catch (error) {
    console.error('Error setting base currency:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تعيين العملة الأساسية: ' + error.message },
      { status: 500 }
    )
  }
}
