'use client'

import { useState, useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ChevronDown, Search, Settings } from 'lucide-react'

interface Service {
  id: number
  name: string
  description: string
  category: string
}

interface ServiceSelectProps {
  value: string
  onChange: (serviceName: string) => void
  label?: string
  placeholder?: string
  required?: boolean
}

export function ServiceSelect({ value, onChange, label = "الخدمة", placeholder = "اختر الخدمة", required = false }: ServiceSelectProps) {
  const [services, setServices] = useState<Service[]>([])
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const fetchServices = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/services')

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      if (result.success) {
        const serviceData = result.services || result.data || []
        if (Array.isArray(serviceData)) {
          setServices(serviceData)
        } else {
          console.error('ServiceSelect: البيانات ليست مصفوفة:', serviceData)
          setServices([])
        }
      } else {
        console.error('ServiceSelect: فشل في جلب الخدمات:', result.error)
        setServices([])
      }
    } catch (error) {
      console.error('ServiceSelect: خطأ في جلب الخدمات:', error)
      setServices([])
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchServices()
  }, [])

  const filteredServices = Array.isArray(services) ? services.filter(service => {
    if (!service || typeof service !== 'object') return false
    return (
      (service.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (service.description || '').toLowerCase().includes(searchTerm.toLowerCase())
    )
  }) : []

  const handleSelect = (service: Service) => {
    onChange(service.name)
    setIsOpen(false)
    setSearchTerm('')
  }

  const handleClear = () => {
    onChange('')
    setIsOpen(false)
    setSearchTerm('')
  }

  return (
    <div className="relative">
      <div className="relative">
        <div
          className="w-full px-2 py-1 border border-gray-300 rounded-md cursor-pointer bg-white flex items-center justify-between text-xs h-6"
          onClick={() => setIsOpen(!isOpen)}
        >
          <div className="flex items-center">
            <Settings className="h-3 w-3 mr-1 text-gray-400" />
            <span className={value ? 'text-gray-900' : 'text-gray-500'}>
              {value || placeholder}
            </span>
          </div>
          <ChevronDown className={`h-3 w-3 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </div>

        {isOpen && (
          <div className="absolute z-[80] w-full mt-1 bg-white border border-gray-300 rounded-md shadow-xl max-h-80 overflow-hidden">
            {/* شريط البحث */}
            <div className="p-3 border-b bg-gray-50">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  type="text"
                  placeholder="البحث في الخدمات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10 text-sm h-8 border-gray-200 focus:border-blue-500"
                />
              </div>
            </div>

            {/* قائمة الخدمات */}
            <div className="max-h-60 overflow-y-auto">
              {isLoading ? (
                <div className="p-4 text-center text-gray-500 text-sm">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mx-auto mb-2"></div>
                  جاري التحميل...
                </div>
              ) : filteredServices.length > 0 ? (
                <>
                  {value && (
                    <div
                      className="p-3 hover:bg-red-50 cursor-pointer border-b text-red-600 transition-colors"
                      onClick={handleClear}
                    >
                      <div className="flex items-center">
                        <span className="text-sm font-medium">✕ إلغاء الاختيار</span>
                      </div>
                    </div>
                  )}
                  {filteredServices.map((service) => (
                    <div
                      key={service.id}
                      className="p-3 hover:bg-blue-50 cursor-pointer border-b last:border-b-0 transition-colors"
                      onClick={() => handleSelect(service)}
                    >
                      <div className="font-medium text-gray-900 text-sm">{service.name}</div>
                      {service.description && (
                        <div className="text-xs text-gray-500 mt-1">{service.description}</div>
                      )}
                    </div>
                  ))}
                </>
              ) : (
                <div className="p-4 text-center text-gray-500 text-sm">
                  <div className="text-gray-400 mb-2">🔍</div>
                  {searchTerm ? 'لا توجد نتائج للبحث' : 'لا توجد خدمات'}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
