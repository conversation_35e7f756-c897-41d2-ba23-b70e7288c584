/**
 * إضافة نظام معرفات الأجهزة المتعددة
 * - إضافة عمود device_ids لحفظ معرفات متعددة مفصولة بفاصلة
 * - تحديث نظام الجلسات للعمل مع معرفات الأجهزة
 * - منع تسجيل الدخول من أجهزة غير مصرح بها
 */

const { Client } = require('pg');

// تطبيق على قاعدتي البيانات
const databases = [
  {
    name: 'moham<PERSON>',
    config: {
      host: 'localhost',
      port: 5432,
      database: 'mohammi',
      user: 'postgres',
      password: 'yemen123'
    }
  },
  {
    name: 'rubaie',
    config: {
      host: 'localhost',
      port: 5432,
      database: 'rubaie',
      user: 'postgres',
      password: 'yemen123'
    }
  }
];

async function addDeviceIdsSystemToDatabase(dbConfig, dbName) {
  const client = new Client(dbConfig);

  try {
    console.log(`🔧 إضافة نظام معرفات الأجهزة المتعددة لقاعدة ${dbName}...`);
    await client.connect();

    // 1. إضافة عمود معرفات الأجهزة المتعددة
    console.log('📱 إضافة عمود معرفات الأجهزة...');
    await client.query(`
      ALTER TABLE users
      ADD COLUMN IF NOT EXISTS device_ids TEXT DEFAULT NULL,
      ADD COLUMN IF NOT EXISTS current_device_id VARCHAR(255) DEFAULT NULL,
      ADD COLUMN IF NOT EXISTS device_login_required BOOLEAN DEFAULT TRUE
    `);

    // 2. تحديث المستخدمين الموجودين - نقل device_id القديم إلى device_ids
    console.log('🔄 تحديث المستخدمين الموجودين...');
    await client.query(`
      UPDATE users
      SET device_ids = device_id,
          device_login_required = CASE
            WHEN username = 'admin' THEN FALSE
            ELSE TRUE
          END
      WHERE device_id IS NOT NULL AND device_ids IS NULL
    `);

    // 3. إنشاء جدول سجل تسجيل الدخول بالأجهزة
    console.log('📋 إنشاء جدول سجل تسجيل الدخول...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS device_login_log (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        username VARCHAR(150),
        device_id VARCHAR(255),
        device_info TEXT,
        ip_address INET,
        user_agent TEXT,
        login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        login_status VARCHAR(50) DEFAULT 'success', -- success, failed, blocked
        failure_reason TEXT,
        session_token VARCHAR(255),
        is_active BOOLEAN DEFAULT TRUE
      )
    `);

    // 4. إنشاء جدول الجلسات النشطة المحسن
    console.log('🔐 تحديث جدول الجلسات النشطة...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS active_sessions_enhanced (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        username VARCHAR(150),
        session_token VARCHAR(255) NOT NULL UNIQUE,
        device_id VARCHAR(255) NOT NULL,
        device_info TEXT,
        ip_address INET,
        user_agent TEXT,
        login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(user_id, device_id)
      )
    `);

    // 5. إنشاء فهارس للأداء
    console.log('⚡ إنشاء فهارس الأداء...');
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_users_device_ids ON users(device_ids);
      CREATE INDEX IF NOT EXISTS idx_users_current_device ON users(current_device_id);
      CREATE INDEX IF NOT EXISTS idx_device_login_log_user ON device_login_log(user_id);
      CREATE INDEX IF NOT EXISTS idx_device_login_log_device ON device_login_log(device_id);
      CREATE INDEX IF NOT EXISTS idx_active_sessions_user_device ON active_sessions_enhanced(user_id, device_id);
      CREATE INDEX IF NOT EXISTS idx_active_sessions_token ON active_sessions_enhanced(session_token);
    `);

    // 6. إنشاء دالة للتحقق من معرف الجهاز
    console.log('🔍 إنشاء دالة التحقق من معرف الجهاز...');
    await client.query(`
      CREATE OR REPLACE FUNCTION check_device_authorization(
        p_username VARCHAR(150),
        p_device_id VARCHAR(255)
      ) RETURNS TABLE(
        is_authorized BOOLEAN,
        user_id INTEGER,
        username VARCHAR(150),
        device_login_required BOOLEAN,
        message TEXT
      ) AS $$
      DECLARE
        v_user_record RECORD;
        v_device_ids TEXT[];
      BEGIN
        -- جلب بيانات المستخدم
        SELECT u.id, u.username, u.device_ids, u.device_login_required, u.is_active
        INTO v_user_record
        FROM users u
        WHERE u.username = p_username AND u.is_active = TRUE;

        -- التحقق من وجود المستخدم
        IF NOT FOUND THEN
          RETURN QUERY SELECT FALSE, NULL::INTEGER, NULL::VARCHAR(150), NULL::BOOLEAN, 'المستخدم غير موجود أو غير نشط'::TEXT;
          RETURN;
        END IF;

        -- إذا كان المستخدم admin، السماح بالدخول بدون تحقق من الجهاز
        IF v_user_record.username = 'admin' OR v_user_record.device_login_required = FALSE THEN
          RETURN QUERY SELECT TRUE, v_user_record.id, v_user_record.username, v_user_record.device_login_required, 'مصرح للمدير'::TEXT;
          RETURN;
        END IF;

        -- التحقق من معرف الجهاز
        IF v_user_record.device_ids IS NULL OR v_user_record.device_ids = '' THEN
          RETURN QUERY SELECT FALSE, v_user_record.id, v_user_record.username, v_user_record.device_login_required, 'لم يتم تسجيل أي جهاز لهذا المستخدم'::TEXT;
          RETURN;
        END IF;

        -- تحويل معرفات الأجهزة إلى مصفوفة
        v_device_ids := string_to_array(v_user_record.device_ids, ',');

        -- التحقق من وجود معرف الجهاز في القائمة
        IF p_device_id = ANY(v_device_ids) THEN
          RETURN QUERY SELECT TRUE, v_user_record.id, v_user_record.username, v_user_record.device_login_required, 'الجهاز مصرح به'::TEXT;
        ELSE
          RETURN QUERY SELECT FALSE, v_user_record.id, v_user_record.username, v_user_record.device_login_required, 'الجهاز غير مصرح به'::TEXT;
        END IF;

        RETURN;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // 7. إنشاء دالة إنهاء الجلسات الأخرى
    console.log('🔄 إنشاء دالة إنهاء الجلسات...');
    await client.query(`
      CREATE OR REPLACE FUNCTION terminate_other_user_sessions(
        p_user_id INTEGER,
        p_current_session_token VARCHAR(255)
      ) RETURNS INTEGER AS $$
      DECLARE
        v_terminated_count INTEGER;
      BEGIN
        -- إنهاء جميع الجلسات الأخرى للمستخدم
        UPDATE active_sessions_enhanced
        SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP
        WHERE user_id = p_user_id
          AND session_token != p_current_session_token
          AND is_active = TRUE;

        GET DIAGNOSTICS v_terminated_count = ROW_COUNT;

        -- تحديث حالة المستخدم
        UPDATE users
        SET current_session_token = p_current_session_token,
            updated_date = CURRENT_TIMESTAMP
        WHERE id = p_user_id;

        RETURN v_terminated_count;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // 8. إنشاء دالة تسجيل محاولة الدخول
    console.log('📝 إنشاء دالة تسجيل محاولات الدخول...');
    await client.query(`
      CREATE OR REPLACE FUNCTION log_device_login_attempt(
        p_user_id INTEGER,
        p_username VARCHAR(150),
        p_device_id VARCHAR(255),
        p_device_info TEXT,
        p_ip_address INET,
        p_user_agent TEXT,
        p_login_status VARCHAR(50),
        p_failure_reason TEXT DEFAULT NULL,
        p_session_token VARCHAR(255) DEFAULT NULL
      ) RETURNS INTEGER AS $$
      DECLARE
        v_log_id INTEGER;
      BEGIN
        INSERT INTO device_login_log (
          user_id, username, device_id, device_info, ip_address,
          user_agent, login_status, failure_reason, session_token
        ) VALUES (
          p_user_id, p_username, p_device_id, p_device_info, p_ip_address,
          p_user_agent, p_login_status, p_failure_reason, p_session_token
        ) RETURNING id INTO v_log_id;

        RETURN v_log_id;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // 9. عرض النتائج
    console.log('\n📊 فحص النتائج...');

    const usersResult = await client.query(`
      SELECT username, device_ids, device_login_required, is_active
      FROM users
      ORDER BY id
    `);

    console.log('👥 المستخدمين المحدثين:');
    usersResult.rows.forEach(user => {
      console.log(`   - ${user.username}: أجهزة=${user.device_ids || 'غير محدد'}, مطلوب=${user.device_login_required}`);
    });

    console.log(`\n✅ تم إضافة نظام معرفات الأجهزة المتعددة لقاعدة ${dbName} بنجاح!`);

  } catch (error) {
    console.error(`❌ خطأ في إضافة نظام معرفات الأجهزة لقاعدة ${dbName}:`, error);
    throw error;
  } finally {
    await client.end();
  }
}

// دالة رئيسية لتطبيق التحديثات على جميع قواعد البيانات
async function addDeviceIdsSystem() {
  console.log('🚀 بدء تطبيق نظام معرفات الأجهزة المتعددة...\n');

  for (const db of databases) {
    try {
      await addDeviceIdsSystemToDatabase(db.config, db.name);
    } catch (error) {
      console.error(`❌ فشل في تطبيق التحديث على قاعدة ${db.name}:`, error.message);
    }
  }

  console.log('\n🎉 انتهى تطبيق نظام معرفات الأجهزة على جميع قواعد البيانات!');
  console.log('\n📋 المميزات المضافة:');
  console.log('   ✅ عمود device_ids لحفظ معرفات متعددة');
  console.log('   ✅ استثناء المدير من التحقق من الجهاز');
  console.log('   ✅ جدول سجل تسجيل الدخول');
  console.log('   ✅ جدول الجلسات النشطة المحسن');
  console.log('   ✅ دوال التحقق والإدارة');
  console.log('   ✅ فهارس الأداء');
}

// تشغيل التحديث
addDeviceIdsSystem().catch(console.error);
