import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب جميع مسارات المكتبة القانونية
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const activeOnly = searchParams.get('active') === 'true'

    let queryStr = 'SELECT * FROM legal_library_paths'
    const queryParams = []

    if (activeOnly) {
      queryStr += ' WHERE is_active = true'
    }

    queryStr += ' ORDER BY is_default DESC, path_name ASC'

    const result = await query(queryStr, queryParams)

    return NextResponse.json({
      success: true,
      data: result.rows
    })

  } catch (error) {
    console.error('خطأ في جلب مسارات المكتبة القانونية:', error)
    return NextResponse.json(
      { success: false, error: 'خطأ في جلب المسارات' },
      { status: 500 }
    )
  }
}

// POST - إضافة مسار جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { path_name, path_value, description, is_default, scan_enabled } = body

    if (!path_name || !path_value) {
      return NextResponse.json(
        { success: false, error: 'اسم المسار والقيمة مطلوبان' },
        { status: 400 }
      )
    }

    // التحقق من عدم تكرار المسار
    const existingResult = await query(
      'SELECT id FROM legal_library_paths WHERE path_value = $1',
      [path_value]
    )

    if (existingResult.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'هذا المسار موجود بالفعل' },
        { status: 409 }
      )
    }

    // التحقق من الحد الأقصى للمسارات
    const countResult = await query('SELECT COUNT(*) as count FROM legal_library_paths')
    const currentCount = parseInt(countResult.rows[0].count)
    
    const maxPathsResult = await query(
      'SELECT setting_value FROM system_settings WHERE setting_key = $1',
      ['legal_library_max_paths']
    )
    const maxPaths = maxPathsResult.rows.length > 0 ? parseInt(maxPathsResult.rows[0].setting_value) : 10

    if (currentCount >= maxPaths) {
      return NextResponse.json(
        { success: false, error: `تم الوصول للحد الأقصى من المسارات (${maxPaths})` },
        { status: 400 }
      )
    }

    // إذا كان المسار الجديد افتراضي، إلغاء الافتراضي من المسارات الأخرى
    if (is_default) {
      await query('UPDATE legal_library_paths SET is_default = false')
    }

    // إضافة المسار الجديد
    const result = await query(`
      INSERT INTO legal_library_paths (path_name, path_value, description, is_active, is_default, scan_enabled)
      VALUES ($1, $2, $3, true, $4, $5)
      RETURNING *
    `, [path_name, path_value, description || '', is_default || false, scan_enabled !== false])

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم إضافة المسار بنجاح'
    })

  } catch (error) {
    console.error('خطأ في إضافة مسار المكتبة القانونية:', error)
    return NextResponse.json(
      { success: false, error: 'خطأ في إضافة المسار' },
      { status: 500 }
    )
  }
}

// PUT - تحديث مسار موجود
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, path_name, path_value, description, is_active, is_default, scan_enabled } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف المسار مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من وجود المسار
    const existingResult = await query(
      'SELECT * FROM legal_library_paths WHERE id = $1',
      [id]
    )

    if (existingResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'المسار غير موجود' },
        { status: 404 }
      )
    }

    // التحقق من عدم تكرار المسار (إذا تم تغييره)
    if (path_value && path_value !== existingResult.rows[0].path_value) {
      const duplicateResult = await query(
        'SELECT id FROM legal_library_paths WHERE path_value = $1 AND id != $2',
        [path_value, id]
      )

      if (duplicateResult.rows.length > 0) {
        return NextResponse.json(
          { success: false, error: 'هذا المسار موجود بالفعل' },
          { status: 409 }
        )
      }
    }

    // إذا كان المسار الجديد افتراضي، إلغاء الافتراضي من المسارات الأخرى
    if (is_default) {
      await query('UPDATE legal_library_paths SET is_default = false WHERE id != $1', [id])
    }

    // تحديث المسار
    const updateFields = []
    const updateValues = [id]
    let paramIndex = 2

    if (path_name !== undefined) {
      updateFields.push(`path_name = $${paramIndex}`)
      updateValues.push(path_name)
      paramIndex++
    }

    if (path_value !== undefined) {
      updateFields.push(`path_value = $${paramIndex}`)
      updateValues.push(path_value)
      paramIndex++
    }

    if (description !== undefined) {
      updateFields.push(`description = $${paramIndex}`)
      updateValues.push(description)
      paramIndex++
    }

    if (is_active !== undefined) {
      updateFields.push(`is_active = $${paramIndex}`)
      updateValues.push(is_active)
      paramIndex++
    }

    if (is_default !== undefined) {
      updateFields.push(`is_default = $${paramIndex}`)
      updateValues.push(is_default)
      paramIndex++
    }

    if (scan_enabled !== undefined) {
      updateFields.push(`scan_enabled = $${paramIndex}`)
      updateValues.push(scan_enabled)
      paramIndex++
    }

    if (updateFields.length === 0) {
      return NextResponse.json(
        { success: false, error: 'لا توجد بيانات للتحديث' },
        { status: 400 }
      )
    }

    const result = await query(`
      UPDATE legal_library_paths 
      SET ${updateFields.join(', ')}
      WHERE id = $1
      RETURNING *
    `, updateValues)

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم تحديث المسار بنجاح'
    })

  } catch (error) {
    console.error('خطأ في تحديث مسار المكتبة القانونية:', error)
    return NextResponse.json(
      { success: false, error: 'خطأ في تحديث المسار' },
      { status: 500 }
    )
  }
}

// DELETE - حذف مسار
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف المسار مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من وجود المسار
    const existingResult = await query(
      'SELECT * FROM legal_library_paths WHERE id = $1',
      [id]
    )

    if (existingResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'المسار غير موجود' },
        { status: 404 }
      )
    }

    // منع حذف المسار الافتراضي إذا كان الوحيد
    const countResult = await query('SELECT COUNT(*) as count FROM legal_library_paths WHERE is_active = true')
    const activeCount = parseInt(countResult.rows[0].count)

    if (activeCount <= 1) {
      return NextResponse.json(
        { success: false, error: 'لا يمكن حذف المسار الوحيد المتبقي' },
        { status: 400 }
      )
    }

    // إذا كان المسار المحذوف افتراضي، جعل أول مسار آخر افتراضي
    if (existingResult.rows[0].is_default) {
      await query(`
        UPDATE legal_library_paths 
        SET is_default = true 
        WHERE id != $1 AND is_active = true 
        ORDER BY id 
        LIMIT 1
      `, [id])
    }

    // حذف المسار
    await query('DELETE FROM legal_library_paths WHERE id = $1', [id])

    return NextResponse.json({
      success: true,
      message: 'تم حذف المسار بنجاح'
    })

  } catch (error) {
    console.error('خطأ في حذف مسار المكتبة القانونية:', error)
    return NextResponse.json(
      { success: false, error: 'خطأ في حذف المسار' },
      { status: 500 }
    )
  }
}
