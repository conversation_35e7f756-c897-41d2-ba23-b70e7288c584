// استعادة جديدة من النسخة الاحتياطية المحدثة
const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

async function freshRestoreFromBackup() {
  console.log('🔄 بدء الاستعادة الجديدة من النسخة الاحتياطية المحدثة...');
  
  try {
    // 1. التحقق من وجود ملف النسخة الاحتياطية
    const backupFile = path.join(__dirname, '..', 'mohammi.sql');
    
    if (!fs.existsSync(backupFile)) {
      console.error('❌ ملف النسخة الاحتياطية غير موجود:', backupFile);
      return;
    }
    
    console.log('✅ تم العثور على ملف النسخة الاحتياطية');
    
    // قراءة الملف
    let sqlContent = fs.readFileSync(backupFile, 'utf8');
    console.log(`📊 حجم الملف: ${(sqlContent.length / 1024 / 1024).toFixed(2)} MB`);

    // 2. الاتصال بقاعدة postgres الافتراضية لحذف وإنشاء قاعدة البيانات
    const adminClient = new Client({
      host: 'localhost',
      port: 5432,
      database: 'postgres',
      user: 'postgres',
      password: 'yemen123'
    });
    
    await adminClient.connect();
    console.log('✅ متصل بقاعدة postgres الافتراضية');
    
    // 3. إنهاء جميع الاتصالات بقاعدة mohammi
    console.log('🔌 إنهاء الاتصالات الحالية...');
    try {
      await adminClient.query(`
        SELECT pg_terminate_backend(pid)
        FROM pg_stat_activity
        WHERE datname = 'mohammi' AND pid <> pg_backend_pid()
      `);
      console.log('✅ تم إنهاء الاتصالات الحالية');
    } catch (error) {
      console.log('⚠️ لا توجد اتصالات نشطة لإنهائها');
    }
    
    // 4. حذف قاعدة البيانات القديمة
    console.log('🗑️ حذف قاعدة البيانات القديمة...');
    try {
      await adminClient.query('DROP DATABASE IF EXISTS mohammi');
      console.log('✅ تم حذف قاعدة البيانات القديمة');
    } catch (error) {
      console.log('⚠️ قاعدة البيانات غير موجودة أو تم حذفها مسبقاً');
    }
    
    // 5. إنشاء قاعدة البيانات الجديدة
    console.log('🏗️ إنشاء قاعدة البيانات الجديدة...');
    await adminClient.query(`CREATE DATABASE mohammi WITH ENCODING = 'UTF8'`);
    console.log('✅ تم إنشاء قاعدة البيانات الجديدة');
    
    await adminClient.end();

    // 6. تنظيف محتوى ملف SQL
    console.log('🧹 تنظيف محتوى ملف SQL...');
    
    // إزالة الأوامر الإدارية التي تسبب مشاكل
    sqlContent = sqlContent.replace(/^SET [^;]*;$/gm, '');
    sqlContent = sqlContent.replace(/^SELECT pg_catalog[^;]*;$/gm, '');
    sqlContent = sqlContent.replace(/^DROP DATABASE[^;]*;$/gm, '');
    sqlContent = sqlContent.replace(/^CREATE DATABASE[^;]*;$/gm, '');
    sqlContent = sqlContent.replace(/^ALTER DATABASE[^;]*;$/gm, '');
    sqlContent = sqlContent.replace(/^\\connect[^\n]*$/gm, '');
    sqlContent = sqlContent.replace(/LOCALE_PROVIDER = libc LOCALE = '[^']*'/g, '');
    
    // إزالة التعليقات والأسطر الفارغة
    const lines = sqlContent.split('\n');
    const cleanLines = lines.filter(line => {
      const trimmed = line.trim();
      return trimmed.length > 0 && 
             !trimmed.startsWith('--') && 
             !trimmed.startsWith('/*') && 
             !trimmed.includes('TOC entry') &&
             !trimmed.match(/^Name: .+; Type: .+; Schema: .+; Owner:/);
    });
    
    sqlContent = cleanLines.join('\n');
    console.log('✅ تم تنظيف محتوى الملف');

    // 7. الاتصال بقاعدة mohammi الجديدة
    const client = new Client({
      host: 'localhost',
      port: 5432,
      database: 'mohammi',
      user: 'postgres',
      password: 'yemen123'
    });
    
    await client.connect();
    console.log('✅ متصل بقاعدة mohammi الجديدة');

    // 8. تقسيم المحتوى إلى أوامر منفصلة
    console.log('📋 تحليل أوامر SQL...');
    
    const statements = [];
    let currentStatement = '';
    let inQuotes = false;
    let quoteChar = '';
    
    for (let i = 0; i < sqlContent.length; i++) {
      const char = sqlContent[i];
      const prevChar = i > 0 ? sqlContent[i - 1] : '';
      
      if ((char === "'" || char === '"') && prevChar !== '\\') {
        if (!inQuotes) {
          inQuotes = true;
          quoteChar = char;
        } else if (char === quoteChar) {
          inQuotes = false;
          quoteChar = '';
        }
      }
      
      currentStatement += char;
      
      if (char === ';' && !inQuotes) {
        const statement = currentStatement.trim();
        if (statement.length > 1) {
          statements.push(statement);
        }
        currentStatement = '';
      }
    }
    
    console.log(`📊 تم العثور على ${statements.length} أمر SQL`);

    // 9. تنفيذ الأوامر
    console.log('⚡ تنفيذ أوامر SQL...');
    
    let successCount = 0;
    let errorCount = 0;
    let createTableCount = 0;
    let insertCount = 0;
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      try {
        await client.query(statement);
        successCount++;
        
        // تصنيف نوع الأمر
        if (statement.toUpperCase().includes('CREATE TABLE')) {
          createTableCount++;
        } else if (statement.toUpperCase().includes('INSERT INTO')) {
          insertCount++;
        }
        
        // عرض التقدم كل 100 أمر
        if (i % 100 === 0) {
          console.log(`📈 تم تنفيذ ${i + 1}/${statements.length} أمر`);
        }
        
      } catch (error) {
        errorCount++;
        
        // عرض الأخطاء المهمة فقط
        if (!error.message.includes('already exists') && 
            !error.message.includes('duplicate key') &&
            !error.message.includes('does not exist') &&
            !error.message.includes('unterminated dollar-quoted string')) {
          console.log(`⚠️ خطأ في الأمر ${i + 1}: ${error.message.substring(0, 100)}`);
        }
      }
    }
    
    console.log(`✅ تم تنفيذ ${successCount} أمر بنجاح`);
    console.log(`📋 إنشاء الجداول: ${createTableCount}`);
    console.log(`📥 إدراج البيانات: ${insertCount}`);
    console.log(`⚠️ أخطاء: ${errorCount} (معظمها طبيعي)`);

    // 10. التحقق من النتائج
    console.log('\n🔍 التحقق من النتائج...');
    
    // فحص الجداول
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    console.log(`📊 عدد الجداول: ${tablesResult.rows.length}`);

    if (tablesResult.rows.length > 0) {
      console.log('📋 أول 15 جدول:');
      tablesResult.rows.slice(0, 15).forEach((row, index) => {
        console.log(`   ${index + 1}. ${row.table_name}`);
      });
      
      if (tablesResult.rows.length > 15) {
        console.log(`   ... و ${tablesResult.rows.length - 15} جدول آخر`);
      }
    }

    // فحص البيانات في الجداول المهمة
    const importantTables = ['companies', 'users', 'clients', 'issues', 'employees', 'services'];
    
    console.log('\n📊 فحص البيانات في الجداول المهمة:');
    for (const tableName of importantTables) {
      try {
        const result = await client.query(`SELECT COUNT(*) as count FROM ${tableName}`);
        console.log(`   📊 ${tableName}: ${result.rows[0].count} سجل`);
      } catch (error) {
        console.log(`   ❌ ${tableName}: غير موجود`);
      }
    }

    // فحص بيانات الشركة
    try {
      const companyResult = await client.query('SELECT name, email, phone FROM companies LIMIT 1');
      if (companyResult.rows.length > 0) {
        const company = companyResult.rows[0];
        console.log('\n🏢 بيانات الشركة:');
        console.log(`   الاسم: ${company.name}`);
        console.log(`   البريد: ${company.email}`);
        console.log(`   الهاتف: ${company.phone}`);
      } else {
        console.log('\n⚠️ لا توجد بيانات شركة - سيتم إضافة بيانات افتراضية');
      }
    } catch (error) {
      console.log('\n❌ لا يمكن الوصول لبيانات الشركة');
    }

    // فحص المستخدمين
    try {
      const usersResult = await client.query('SELECT username, email, role FROM users LIMIT 5');
      if (usersResult.rows.length > 0) {
        console.log('\n👥 المستخدمين:');
        usersResult.rows.forEach((user, index) => {
          console.log(`   ${index + 1}. ${user.username} (${user.role}) - ${user.email}`);
        });
      } else {
        console.log('\n⚠️ لا توجد مستخدمين - سيتم إضافة المستخدم admin');
      }
    } catch (error) {
      console.log('\n❌ لا يمكن الوصول لبيانات المستخدمين');
    }

    await client.end();
    
    if (tablesResult.rows.length > 0) {
      console.log('\n🎉 تمت الاستعادة الجديدة بنجاح!');
      console.log('✅ تم حذف قاعدة البيانات القديمة واستعادة النسخة الجديدة');
      console.log('🔄 يمكنك الآن إعادة تشغيل النظام');
      
      // إضافة بيانات أساسية إذا لزم الأمر
      console.log('\n📋 سيتم الآن إضافة البيانات الأساسية إذا لزم الأمر...');
    } else {
      console.log('\n❌ فشلت الاستعادة - لم يتم إنشاء أي جداول');
    }
    
  } catch (error) {
    console.error('❌ خطأ في الاستعادة:', error.message);
  }
}

freshRestoreFromBackup();
