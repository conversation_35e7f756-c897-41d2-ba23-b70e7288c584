/**
 * API تسجيل الخروج مع إنهاء الجلسة
 */

import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'
import SessionManager from '@/lib/session-manager'

// POST - تسجيل الخروج
export async function POST(request: NextRequest) {
  try {
    // جلب التوكن من الهيدر
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({
        success: true,
        message: 'تم تسجيل الخروج'
      })
    }
    
    const token = authHeader.substring(7)
    
    // التحقق من JWT واستخراج معلومات الجلسة
    const decoded = SessionManager.verifyJWT(token)
    
    if (decoded && decoded.sessionToken && decoded.userId) {
      // إنهاء الجلسة في قاعدة البيانات
      await SessionManager.terminateSession(decoded.sessionToken)
      
      // تحديث حالة المستخدم
      await query(`
        UPDATE users 
        SET is_online = false, 
            last_logout = CURRENT_TIMESTAMP,
            current_session_token = NULL
        WHERE id = $1
      `, [decoded.userId])
      
      console.log(`✅ تم تسجيل خروج المستخدم ${decoded.userId} من الجلسة ${decoded.sessionToken.substring(0, 8)}...`)
    }
    
    return NextResponse.json({
      success: true,
      message: 'تم تسجيل الخروج بنجاح'
    })
    
  } catch (error) {
    console.error('❌ خطأ في تسجيل الخروج:', error)
    
    // حتى لو حدث خطأ، نعتبر تسجيل الخروج ناجحاً
    return NextResponse.json({
      success: true,
      message: 'تم تسجيل الخروج'
    })
  }
}

// GET - التحقق من حالة الجلسة
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({
        success: false,
        error: 'غير مصرح'
      }, { status: 401 })
    }
    
    const token = authHeader.substring(7)
    const decoded = SessionManager.verifyJWT(token)
    
    if (!decoded || !decoded.sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'جلسة غير صحيحة'
      }, { status: 401 })
    }
    
    // التحقق من صحة الجلسة
    const sessionInfo = await SessionManager.validateSession(decoded.sessionToken)
    
    if (!sessionInfo) {
      return NextResponse.json({
        success: false,
        error: 'الجلسة منتهية الصلاحية أو تم إنهاؤها',
        reason: 'session_terminated'
      }, { status: 401 })
    }
    
    return NextResponse.json({
      success: true,
      session: {
        userId: sessionInfo.userId,
        loginTime: sessionInfo.loginTime,
        lastActivity: sessionInfo.lastActivity,
        deviceInfo: sessionInfo.deviceInfo,
        ipAddress: sessionInfo.ipAddress
      }
    })
    
  } catch (error) {
    console.error('❌ خطأ في التحقق من الجلسة:', error)
    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم'
    }, { status: 500 })
  }
}
