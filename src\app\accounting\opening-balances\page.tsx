'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Calculator,
  Plus,
  Search,
  Save,
  X,
  Trash2,
  AlertCircle,
  CheckCircle,
  DollarSign
} from 'lucide-react'

interface OpeningBalance {
  id: number
  account_id: number
  account_code: string
  account_name: string
  debit_balance: number
  credit_balance: number
  balance_date: string
}

interface ChartAccount {
  id: number
  account_code: string
  account_name: string
  account_type: string
  account_level: number
  // خصائص اختيارية مستخدمة في العرض
  is_linked_record?: boolean
  original_table?: string
  description?: string
  allow_transactions?: boolean
}

interface Currency {
  id: number
  currency_code: string
  currency_name: string
  symbol: string
  exchange_rate: number
  is_base_currency: boolean
}

interface CaseItem {
  id: number
  case_number: string
  title: string
}

interface BalanceRow {
  id: string
  account_id: number | null
  account_code: string
  account_name: string
  currency_id: number | null
  exchange_rate: number
  issues_id?: number | null
  debit_balance: string
  credit_balance: string
}

export default function OpeningBalancesPage() {
  const [balances, setBalances] = useState<OpeningBalance[]>([])
  const [openingEntries, setOpeningEntries] = useState<any[]>([])
  const [chartAccounts, setChartAccounts] = useState<ChartAccount[]>([])
  const [currencies, setCurrencies] = useState<Currency[]>([])
  const [cases, setCases] = useState<CaseItem[]>([])
  // دالة مساعدة للحصول على العملة الافتراضية (ريال يمني id=1 ثم الأساسية)
  const getDefaultCurrency = () => {
    const yer = currencies.find(c => c.id === 1)
    if (yer) return yer
    const base = currencies.find(c => c.is_base_currency)
    if (base) return base
    return currencies[0]
  }
  const [costCenters, setCostCenters] = useState<{ id: number; center_code: string; center_name: string }[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)

  // حالة نافذة الإضافة
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const [balanceRows, setBalanceRows] = useState<BalanceRow[]>([
    {
      id: '1',
      account_id: null,
      account_code: '',
      account_name: '',
      currency_id: null,
      exchange_rate: 1,
      debit_balance: '',
      credit_balance: ''
    }
  ])

  // حقول رأس القيد
  const [entryDate, setEntryDate] = useState<string>(new Date().toISOString().split('T')[0])
  const [costCenterId, setCostCenterId] = useState<string>('')
  const [headerDescription, setHeaderDescription] = useState<string>('قيد رصيد افتتاحي')
  // لا اختيار قضية على مستوى الرأس، سيتم اختيارها لكل سطر

  // حالة البحث في الحسابات
  const [accountSearchTerms, setAccountSearchTerms] = useState<{ [key: string]: string }>({})

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setIsLoading(true)
      // جلب دليل الحسابات مع الحسابات المرتبطة (العملاء والموظفين)
      const accountsResponse = await fetch('/api/accounting/chart-of-accounts?include_linked=true&only_transactional=true')
      const accountsData = await accountsResponse.json()

      // جلب العملات
      const currenciesRes = await fetch('/api/accounting/currencies')
      const currenciesJson = await currenciesRes.json()

      // جلب مراكز التكلفة
      const centersRes = await fetch('/api/cost-centers')
      const centersJson = await centersRes.json()

      // جلب القضايا
      const casesRes = await fetch('/api/issues')
      const casesJson = await casesRes.json()

      // جلب قيود الرصيد الافتتاحي (ST)
      try {
        const entriesRes = await fetch('/api/accounting/journal-entries?include_details=true&entry_types=opening')
        if (entriesRes.ok) {
          const entriesJson = await entriesRes.json()
          const list = (entriesJson.entries || [])
          setOpeningEntries(list)
        }
      } catch (e) {
        console.warn('تعذر جلب قيود ST:', e)
      }

      if (accountsData.success) {
        console.log('تم جلب الحسابات:', accountsData.accounts?.length || 0)
        setChartAccounts(accountsData.accounts || [])
      } else {
        console.error('خطأ في جلب الحسابات:', accountsData.error)
        setMessage({ type: 'error', text: 'فشل في جلب دليل الحسابات' })
      }

      if (currenciesJson.success) {
        const list = currenciesJson.currencies || []
        setCurrencies(list)
        // ضبط العملة الافتراضية للصفوف الحالية إذا لم تكن محددة
        const def = (list.find((c: any) => c.id === 1) || list.find((c: any) => c.is_base_currency) || list[0])
        if (def) {
          setBalanceRows(prev => prev.map(r => (
            r.currency_id ? r : { ...r, currency_id: def.id, exchange_rate: def.exchange_rate }
          )))
        }
      }

      if (centersJson.success) {
        setCostCenters((centersJson.centers || []).map((c: any) => ({ id: c.id, center_code: c.center_code, center_name: c.center_name })))
      }

      if (casesJson.success) {
        const list = (casesJson.data || casesJson.issues || []).map((c: any) => ({ id: c.id, case_number: c.case_number || '', title: c.title || '' }))
        setCases(list)
      }

    } catch (error) {
      console.error('خطأ في جلب البيانات:', error)
      setMessage({ type: 'error', text: 'حدث خطأ في جلب البيانات' })
    } finally {
      setIsLoading(false)
    }
  }

  // تصفية الأرصدة حسب البحث
  const filteredEntries = openingEntries.filter(e =>
    (e.entry_number || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (e.description || '').toLowerCase().includes(searchTerm.toLowerCase())
  )

  // إضافة صف جديد
  const addNewRow = () => {
    const newRow: BalanceRow = {
      id: Date.now().toString(),
      account_id: null,
      account_code: '',
      account_name: '',
      currency_id: (currencies.find(c => c.id === 1)?.id) || (currencies.find(c => c.is_base_currency)?.id) || null,
      exchange_rate: (currencies.find(c => c.id === 1)?.exchange_rate) || (currencies.find(c => c.is_base_currency)?.exchange_rate) || 1,
      issues_id: null,
      debit_balance: '',
      credit_balance: ''
    }
    setBalanceRows([...balanceRows, newRow])
  }

  // حذف صف
  const removeRow = (id: string) => {
    if (balanceRows.length > 1) {
      setBalanceRows(balanceRows.filter(row => row.id !== id))
      // إزالة مصطلح البحث للصف المحذوف
      const newSearchTerms = { ...accountSearchTerms }
      delete newSearchTerms[id]
      setAccountSearchTerms(newSearchTerms)
    }
  }

  // تحديث صف (باستخدام setState الدالي)
  const updateRow = (id: string, field: keyof BalanceRow, value: any) => {
    setBalanceRows(prev => prev.map(row => (row.id === id ? { ...row, [field]: value } : row)))
  }

  // تحديث عدة حقول دفعة واحدة لمنع فقدان التحديثات المتتابعة
  const updateRowPatch = (id: string, patch: Partial<BalanceRow>) => {
    setBalanceRows(prev => prev.map(row => (row.id === id ? { ...row, ...patch } : row)))
  }

  // اختيار حساب
  const selectAccount = (rowId: string, account: any) => {
    // تحديد معرف الحساب الصحيح
    let accountId = account.id
    if (account.is_linked_record) {
      // للحسابات المرتبطة، نستخدم معرف خاص
      accountId = account.original_table === 'clients'
        ? `client_${account.external_id}`
        : `employee_${account.external_id}`
    }

    updateRow(rowId, 'account_id', accountId)
    updateRow(rowId, 'account_code', account.account_code)
    updateRow(rowId, 'account_name', account.account_name)

    // افتراض العملة الأساسية عند اختيار الحساب أول مرة
    const base = currencies.find(c => c.is_base_currency)
    if (base) {
      updateRow(rowId, 'currency_id', base.id)
      updateRow(rowId, 'exchange_rate', base.exchange_rate)
    }

    // مسح مصطلح البحث
    setAccountSearchTerms(prev => ({
      ...prev,
      [rowId]: ''
    }))
  }

  // تحديث مصطلح البحث للحساب
  const updateAccountSearch = (rowId: string, searchTerm: string) => {
    setAccountSearchTerms(prev => ({
      ...prev,
      [rowId]: searchTerm
    }))
  }

  // تصفية الحسابات حسب البحث
  const getFilteredAccounts = (rowId: string) => {
    const searchTerm = accountSearchTerms[rowId] || ''
    if (!searchTerm.trim()) {
      return chartAccounts
    }

    return chartAccounts.filter(account =>
      account.account_code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      account.account_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      account.account_type.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }

  // حساب الإجماليات
  const calculateTotals = () => {
    const toYER = (amountStr: string, rate: number) => {
      const amount = parseFloat(amountStr) || 0
      return Math.round(amount * (rate || 1))
    }

    const totalDebit = balanceRows.reduce((sum, row) => sum + toYER(row.debit_balance, row.exchange_rate), 0)
    const totalCredit = balanceRows.reduce((sum, row) => sum + toYER(row.credit_balance, row.exchange_rate), 0)

    return { totalDebit, totalCredit }
  }

  const { totalDebit, totalCredit } = calculateTotals()
  const isBalanced = totalDebit === totalCredit

  // حفظ الأرصدة
  const handleSave = async () => {
    try {
      // التحقق من التوازن بالريال اليمني
      if (!isBalanced) {
        setMessage({ type: 'error', text: 'يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن (بالريال اليمني)' })
        return
      }

      // التحقق من الصفوف الصحيحة
      const validRows = balanceRows.filter(row => row.account_id && ((parseFloat(row.debit_balance)||0) > 0 || (parseFloat(row.credit_balance)||0) > 0))
      if (validRows.length === 0) {
        setMessage({ type: 'error', text: 'يجب إضافة سطر واحد على الأقل' })
        return
      }

      // إعداد البيان التلقائي
      const autoDescription = (headerDescription && headerDescription.trim().length > 0)
        ? headerDescription.trim()
        : 'قيد رصيد افتتاحي'

      // تحويل مبالغ كل صف إلى الريال اليمني
      const toYER = (amountStr: string, rate: number) => Math.round((parseFloat(amountStr) || 0) * (rate || 1))

      const payload = {
        entry_date: entryDate,
        description: autoDescription,
        status: 'approved',
        entry_type: 'opening', // سيولد رقم ST تلقائياً
        details: validRows.map((row, idx) => ({
          account_id: typeof row.account_id === 'string' ? null : row.account_id, // نتعامل مع الحسابات القياسية فقط هنا
          debit_amount: toYER(row.debit_balance, row.exchange_rate),
          credit_amount: toYER(row.credit_balance, row.exchange_rate),
          cost_center_id: costCenterId ? parseInt(costCenterId) : null,
          issues_id: row.issues_id ? Number(row.issues_id) : null,
          description: autoDescription,
          line_order: idx + 1
        }))
      }

      const response = await fetch('/api/accounting/journal-entries', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      })

      const data = await response.json()

      if (data.success) {
        setMessage({ type: 'success', text: `تم إنشاء قيد رصيد افتتاحي (${data.entry?.entry_number || 'ST'}) بنجاح` })
        setIsAddModalOpen(false)
        setBalanceRows([{
          id: '1', account_id: null, account_code: '', account_name: '', currency_id: currencies.find(c=>c.is_base_currency)?.id || null, exchange_rate: currencies.find(c=>c.is_base_currency)?.exchange_rate || 1, debit_balance: '', credit_balance: ''
        }])
        setAccountSearchTerms({})
      } else {
        // تسجيل تفاصيل إضافية للمساعدة في التشخيص
        console.error('فشل حفظ القيد:', {
          status: response.status,
          error: data?.error,
          details: data?.details,
          lastSql: data?.lastSql,
          lastParams: data?.lastParams,
        })
        setMessage({ type: 'error', text: `${data.error || 'فشل في إنشاء القيد'}${data?.details ? ' - ' + data.details : ''}` })
      }

    } catch (error) {
      console.error('خطأ في الحفظ:', error)
      setMessage({ type: 'error', text: 'حدث خطأ أثناء حفظ الأرصدة' })
    }
  }

  // حذف قيد رصيد افتتاحي (من جدول القيود)
  const handleDelete = async (id: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا الرصيد الافتتاحي؟')) return

    try {
      const response = await fetch(`/api/accounting/journal-entries/${id}`, { method: 'DELETE' })

      const data = await response.json()
      if (data.success) {
        setMessage({ type: 'success', text: 'تم حذف الرصيد الافتتاحي بنجاح' })
        await fetchData()
      } else {
        setMessage({ type: 'error', text: data.error || 'فشل في حذف الرصيد' })
      }
    } catch (error) {
      console.error('خطأ في الحذف:', error)
      setMessage({ type: 'error', text: 'حدث خطأ أثناء حذف الرصيد' })
    }
  }

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Calculator className="h-8 w-8 animate-spin mx-auto mb-2" />
            <span>جاري التحميل...</span>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان والأزرار */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 space-x-reverse">
            <Calculator className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">الأرصدة الافتتاحية</h1>
              <p className="text-gray-600">إدارة الأرصدة الافتتاحية للحسابات</p>
            </div>
          </div>
          <Button
            onClick={() => setIsAddModalOpen(true)}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Plus className="h-4 w-4 ml-2" />
            إضافة أرصدة افتتاحية
          </Button>
        </div>

        {/* رسائل التنبيه */}
        {message && (
          <Alert className={message.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className={message.type === 'success' ? 'text-green-800' : 'text-red-800'}>
              {message.text}
            </AlertDescription>
          </Alert>
        )}

        {/* إحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-gray-900">
                {filteredEntries.length}
              </div>
              <div className="text-sm text-gray-600">إجمالي الأرصدة</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-green-600">
                {Math.round(filteredEntries.reduce((sum, e:any) => sum + Number(e.total_debit||0), 0)).toLocaleString('en-US', { maximumFractionDigits: 0 })}
              </div>
              <div className="text-sm text-gray-600">إجمالي المدين</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-blue-600">
                {Math.round(filteredEntries.reduce((sum, e:any) => sum + Number(e.total_credit||0), 0)).toLocaleString('en-US', { maximumFractionDigits: 0 })}
              </div>
              <div className="text-sm text-gray-600">إجمالي الدائن</div>
            </CardContent>
          </Card>
        </div>

        {/* البحث */}
        <Card>
          <CardContent className="p-4">
            <div className="flex justify-center">
              <div className="relative w-96">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في الأرصدة الافتتاحية..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10 w-full"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* جدول الأرصدة */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <DollarSign className="h-5 w-5 ml-2" />
              الأرصدة الافتتاحية ({filteredEntries.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {filteredEntries.length === 0 ? (
              <div className="text-center text-gray-500 p-6">لا توجد أرصدة افتتاحية حتى الآن</div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="text-right p-3">رقم القيد</th>
                      <th className="text-right p-3">اسم الحساب</th>
                      <th className="text-right p-3">مدين</th>
                      <th className="text-right p-3">دائن</th>
                      <th className="text-right p-3">القضية</th>
                      <th className="text-center p-3">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredEntries.flatMap((e: any) => (e.details || []).map((d: any) => ({ entry: e, detail: d }))).map(({ entry, detail }: any, idx: number) => (
                      <tr key={`${entry.id}-${detail.id || idx}`} className="border-b hover:bg-gray-50">
                        <td className="p-3 font-mono text-blue-600">{entry.entry_number}</td>
                        <td className="p-3">{detail.account_name || ''}</td>
                        <td className="p-3 text-green-600 font-medium">{parseFloat(detail.debit_amount||0) > 0 ? Math.round(Number(detail.debit_amount||0)).toLocaleString('en-US', { maximumFractionDigits: 0 }) : '-'}</td>
                        <td className="p-3 text-blue-600 font-medium">{parseFloat(detail.credit_amount||0) > 0 ? Math.round(Number(detail.credit_amount||0)).toLocaleString('en-US', { maximumFractionDigits: 0 }) : '-'}</td>
                        <td className="p-3">{detail.issues_id ? `قضية #${detail.issues_id}` : '-'}</td>
                        <td className="p-3 text-center">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(entry.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* جدول قيود الرصيد الافتتاحي ST */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <DollarSign className="h-5 w-5 ml-2" />
              الأرصدة الافتتاحية ({filteredEntries.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {filteredEntries.length === 0 ? (
              <div className="text-center text-gray-500 p-6">لا توجد قيود رصيد افتتاحي (ST) حتى الآن</div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="text-right p-3">رقم القيد</th>
                      <th className="text-right p-3">البيان</th>
                      <th className="text-right p-3">التاريخ</th>
                      <th className="text-right p-3">إجمالي المدين</th>
                      <th className="text-right p-3">إجمالي الدائن</th>
                      <th className="text-center p-3">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredEntries.map((e: any) => (
                      <tr key={e.id} className="border-b hover:bg-gray-50">
                        <td className="p-3 font-mono text-blue-600">{e.entry_number}</td>
                        <td className="p-3">{e.description || ''}</td>
                        <td className="p-3 text-gray-600">{e.entry_date}</td>
                        <td className="p-3 text-green-600 font-medium">{Math.round(Number(e.total_debit||0)).toLocaleString('en-US', { maximumFractionDigits: 0 })}</td>
                        <td className="p-3 text-blue-600 font-medium">{Math.round(Number(e.total_credit||0)).toLocaleString('en-US', { maximumFractionDigits: 0 })}</td>
                        <td className="p-3 text-center">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(e.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* نافذة إضافة الأرصدة */}
        {isAddModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-6xl max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">إضافة أرصدة افتتاحية</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsAddModalOpen(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-4">
                {/* رأس القيد: التاريخ، مركز التكلفة، البيان */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 border rounded-lg bg-gray-50">
                  <div>
                    <Label className="mb-1 block">تاريخ القيد</Label>
                    <Input
                      type="date"
                      value={entryDate}
                      onChange={(e) => setEntryDate(e.target.value)}
                      className="h-9"
                    />
                  </div>
                  <div>
                    <Label className="mb-1 block">مركز التكلفة</Label>
                    <Select
                      value={costCenterId || 'none'}
                      onValueChange={(val) => setCostCenterId(val === 'none' ? '' : val)}
                    >
                      <SelectTrigger className="h-9">
                        <SelectValue placeholder="اختر مركز التكلفة (اختياري)" />
                      </SelectTrigger>
                      <SelectContent className="max-h-64">
                        <SelectItem value="none">بدون مركز تكلفة</SelectItem>
                        {costCenters.map((cc) => (
                          <SelectItem key={cc.id} value={cc.id.toString()}>
                            {cc.center_code} - {cc.center_name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label className="mb-1 block">القضية</Label>
                    <Select
                      value={caseId || 'none'}
                      onValueChange={(val) => setCaseId(val === 'none' ? '' : val)}
                    >
                      <SelectTrigger className="h-9">
                        <SelectValue placeholder="اختر القضية (اختياري)" />
                      </SelectTrigger>
                      <SelectContent className="max-h-64">
                        <SelectItem value="none">بدون قضية</SelectItem>
                        {cases.map((cs) => (
                          <SelectItem key={cs.id} value={cs.id.toString()}>
                            {cs.case_number ? `${cs.case_number} - ` : ''}{cs.title}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label className="mb-1 block">البيان</Label>
                    <Input
                      value={headerDescription}
                      onChange={(e) => setHeaderDescription(e.target.value)}
                      placeholder="قيد رصيد افتتاحي"
                      className="h-9"
                    />
                  </div>
                </div>

                {/* أزرار التحكم */}
                <div className="flex justify-between items-center">
                  <Button
                    onClick={addNewRow}
                    variant="outline"
                    className="border-green-600 text-green-600 hover:bg-green-50"
                  >
                    <Plus className="h-4 w-4 ml-2" />
                    إضافة صف جديد
                  </Button>

                  <div className="flex items-center space-x-4 space-x-reverse">
                    <div className="text-sm">
                      <span className="text-gray-600">إجمالي المدين (ر.ي): </span>
                      <span className="font-bold text-green-600">{totalDebit.toLocaleString('en-US', { maximumFractionDigits: 0 })}</span>
                    </div>
                    <div className="text-sm">
                      <span className="text-gray-600">إجمالي الدائن (ر.ي): </span>
                      <span className="font-bold text-blue-600">{totalCredit.toLocaleString('en-US', { maximumFractionDigits: 0 })}</span>
                    </div>
                    <div className="flex items-center">
                      {isBalanced ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : (
                        <AlertCircle className="h-5 w-5 text-red-600" />
                      )}
                      <span className={`text-sm ml-1 ${isBalanced ? 'text-green-600' : 'text-red-600'}`}>
                        {isBalanced ? 'متوازن' : 'غير متوازن'}
                      </span>
                    </div>
                  </div>
                </div>

                {/* جدول الإدخال */}
                <div className="border rounded-lg overflow-hidden">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="text-right p-3 border-b">اسم الحساب</th>
                        <th className="text-right p-3 border-b">العملة</th>
                        <th className="text-right p-3 border-b">القضية</th>
                        <th className="text-right p-3 border-b">مدين</th>
                        <th className="text-right p-3 border-b">دائن</th>
                        <th className="text-right p-3 border-b">مدين (ر.ي)</th>
                        <th className="text-right p-3 border-b">دائن (ر.ي)</th>
                        <th className="text-center p-3 border-b">الإجراءات</th>
                      </tr>
                    </thead>
                    <tbody>
                      {balanceRows.map((row, index) => (
                        <tr key={row.id} className="border-b">
                          <td className="p-3" style={{ minWidth: '320px' }}>
                            {/* اختيار الحساب بنفس آلية صفحة القيود اليومية */}
                            <select
                              value={(() => {
                                if (row.account_id !== null && row.account_id !== undefined) return String(row.account_id)
                                if (row.account_code) {
                                  const acc = chartAccounts.find(a => a.account_code === row.account_code)
                                  return acc ? acc.id.toString() : ''
                                }
                                return ''
                              })()}
                              onChange={(e) => {
                                const val = e.target.value
                                if (!val) {
                                  updateRowPatch(row.id, {
                                    account_id: null,
                                    account_code: '',
                                    account_name: ''
                                  })
                                  return
                                }
                                const acc = chartAccounts.find(a => a.id.toString() === val)
                                // تفضيل الريال اليمني (id=1) ثم العملة الأساسية
                                const base = !row.currency_id
                                  ? (currencies.find(c => c.id === 1) || currencies.find(c => c.is_base_currency))
                                  : undefined
                                updateRowPatch(row.id, {
                                  account_id: parseInt(val),
                                  account_code: acc?.account_code || '',
                                  account_name: acc?.account_name || '',
                                  ...(base ? { currency_id: base.id, exchange_rate: base.exchange_rate } : {})
                                })
                              }}
                              className="w-full h-9 text-sm p-2 border border-gray-300 rounded-md bg-white focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                              style={{ color: 'black' }}
                            >
                              <option value="">اختر الحساب...</option>
                              <optgroup label="كل الحسابات">
                                {[...chartAccounts]
                                  .sort((a, b) => a.account_code.localeCompare(b.account_code))
                                  .map(account => (
                                    <option key={account.id} value={account.id.toString()} style={{ color: 'black' }}>
                                      {account.account_name} ({account.account_code})
                                      {account.is_linked_record && account.original_table && (
                                        ` - ${account.original_table === 'clients' ? 'عميل' : account.original_table === 'employees' ? 'موظف' : account.original_table === 'suppliers' ? 'مورد' : ''}`
                                      )}
                                    </option>
                                  ))}
                              </optgroup>
                            </select>
                          </td>
                          {/* العملة */}
                          <td className="p-3" style={{ minWidth: '140px' }}>
                            <select
                              value={row.currency_id?.toString() || ''}
                              onChange={(e) => {
                                const val = e.target.value
                                const c = currencies.find(cc => cc.id.toString() === val)
                                updateRowPatch(row.id, {
                                  currency_id: val ? parseInt(val) : null,
                                  exchange_rate: c ? c.exchange_rate : 1
                                })
                              }}
                              className="h-9 text-xs p-2 border border-gray-300 rounded-md bg-white focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                              style={{ width: '30%', minWidth: '110px', color: 'black' }}
                            >
                              <option value="">اختر العملة...</option>
                              {[...currencies]
                                .sort((a,b) => a.currency_code.localeCompare(b.currency_code))
                                .map(c => (
                                  <option key={c.id} value={c.id.toString()}>
                                    {c.currency_code} — {c.currency_name}
                                  </option>
                                ))}
                            </select>
                          </td>
                          {/* القضية */}
                          <td className="p-3" style={{ minWidth: '220px' }}>
                            <select
                              value={row.issues_id ? String(row.issues_id) : ''}
                              onChange={(e) => {
                                const val = e.target.value
                                updateRowPatch(row.id, { issues_id: val ? parseInt(val) : null })
                              }}
                              className="h-9 text-xs p-2 border border-gray-300 rounded-md bg-white focus:border-blue-500 focus:ring-1 focus:ring-blue-500 w-full"
                              style={{ color: 'black' }}
                            >
                              <option value="">اختر القضية (اختياري)</option>
                              {[...cases]
                                .sort((a,b) => (a.case_number||'').localeCompare(b.case_number||''))
                                .map(cs => (
                                  <option key={cs.id} value={cs.id.toString()}>
                                    {cs.case_number ? `${cs.case_number} - ` : ''}{cs.title}
                                  </option>
                                ))}
                            </select>
                          </td>

                          {/* مدين */}
                          <td className="p-3">
                            <Input
                              type="text"
                              inputMode="numeric"
                              pattern="\\d*"
                              placeholder=""
                              value={row.debit_balance}
                              onChange={(e) => updateRow(row.id, 'debit_balance', e.target.value.replace(/\D+/g, ''))}
                              className="text-center"
                            />
                          </td>

                          {/* دائن */}
                          <td className="p-3">
                            <Input
                              type="text"
                              inputMode="numeric"
                              pattern="\\d*"
                              placeholder=""
                              value={row.credit_balance}
                              onChange={(e) => updateRow(row.id, 'credit_balance', e.target.value.replace(/\D+/g, ''))}
                              className="text-center"
                            />
                          </td>

                          {/* مدين (ر.ي) */}
                          <td className="p-3 text-green-700 font-medium text-center">
                            {(() => {
                              const amt = Math.round((parseFloat(row.debit_balance || '0') || 0) * (row.exchange_rate || 1))
                              return amt > 0 ? amt.toLocaleString('en-US', { maximumFractionDigits: 0 }) : '-'
                            })()}
                          </td>

                          {/* دائن (ر.ي) */}
                          <td className="p-3 text-blue-700 font-medium text-center">
                            {(() => {
                              const amt = Math.round((parseFloat(row.credit_balance || '0') || 0) * (row.exchange_rate || 1))
                              return amt > 0 ? amt.toLocaleString('en-US', { maximumFractionDigits: 0 }) : '-'
                            })()}
                          </td>
                          <td className="p-3 text-center">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeRow(row.id)}
                              disabled={balanceRows.length === 1}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* أزرار الحفظ */}
                <div className="flex justify-end space-x-2 space-x-reverse pt-4 border-t">
                  <Button
                    variant="outline"
                    onClick={() => setIsAddModalOpen(false)}
                  >
                    إلغاء
                  </Button>
                  <Button
                    onClick={handleSave}
                    disabled={!isBalanced}
                    className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400"
                  >
                    <Save className="h-4 w-4 ml-2" />
                    حفظ الأرصدة
                  </Button>
                </div>

                {/* تنبيه التوازن */}
                {!isBalanced && (
                  <Alert className="border-red-200 bg-red-50">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription className="text-red-800">
                      يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن لحفظ الأرصدة
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}