'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import {
  Scale,
  Users,
  FileText,
  Shield,
  Building,
  Gavel,
  BookOpen,
  Award,
  Phone,
  Mail,
  MapPin,
  Star,
  TrendingUp,
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  MessageCircle,
  LogIn,
  Download,
  Home,
  Search
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'

interface Service {
  id: number
  title: string
  slug: string
  description: string
  content: string
  icon_name: string
  icon_color: string
  image_url?: string
  meta_title?: string
  meta_description?: string
}

interface CompanyData {
  id: number
  name: string
  logo_url?: string
  logo_image_url?: string
  phone?: string
  email?: string
  address?: string
  city?: string
  country?: string
  description?: string
}

// خريطة الأيقونات
const iconMap: { [key: string]: any } = {
  Scale,
  Users,
  FileText,
  Shield,
  Building,
  Gavel,
  BookOpen,
  Award,
  Phone,
  Mail,
  MapPin,
  Star,
  TrendingUp,
  CheckCircle,
  ArrowRight,
  MessageCircle,
  LogIn,
  Download
}

export default function ServicesPage() {
  const [services, setServices] = useState<Service[]>([])
  const [companyData, setCompanyData] = useState<CompanyData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      setError(null)

      // جلب الخدمات
      const servicesResponse = await fetch('/api/serviceslow?active=true')
      const servicesResult = await servicesResponse.json()
      
      if (servicesResult.success) {
        setServices(servicesResult.data)
      } else {
        setError('فشل في جلب الخدمات')
        return
      }

      // جلب بيانات الشركة
      const companyResponse = await fetch('/api/company')
      const companyResult = await companyResponse.json()
      if (companyResult.success && companyResult.data.length > 0) {
        setCompanyData(companyResult.data[0])
      }

    } catch (error) {
      console.error('Error fetching data:', error)
      setError('حدث خطأ في تحميل البيانات')
    } finally {
      setLoading(false)
    }
  }

  // تصفية الخدمات حسب البحث
  const filteredServices = searchQuery
    ? services.filter(service =>
        service.title.includes(searchQuery) ||
        service.description.includes(searchQuery)
      )
    : services

  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <div className="animate-pulse">
          <div className="h-16 bg-gray-200"></div>
          <div className="container mx-auto px-4 py-8">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-64 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Scale className="h-12 w-12 text-red-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">خطأ في تحميل الخدمات</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <Button onClick={() => window.location.reload()} className="bg-blue-600 hover:bg-blue-700 text-white">
            إعادة المحاولة
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen" style={{ background: 'linear-gradient(135deg, #222222 0%, #**********%)' }}>
      {/* Header */}
      <header className="shadow-sm border-b sticky top-0 z-50" style={{ background: 'linear-gradient(135deg, #222222 0%, #**********%)', borderColor: 'rgba(204, 169, 103, 0.2)' }}>
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              {companyData?.logo_image_url ? (
                <Image
                  src={companyData.logo_image_url}
                  alt="شعار الشركة"
                  width={60}
                  height={60}
                  className="rounded-lg mr-3 object-contain"
                />
              ) : companyData?.logo_url ? (
                <Image
                  src={companyData.logo_url}
                  alt="شعار الشركة"
                  width={60}
                  height={60}
                  className="rounded-lg mr-3 object-contain"
                />
              ) : (
                <div className="w-12 h-12 rounded-lg flex items-center justify-center mr-3 shadow-lg" style={{ background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)' }}>
                  <Scale className="h-6 w-6 text-gray-900" />
                </div>
              )}
              <div>
                <h1 className="text-xl font-bold" style={{ color: '#cca967' }}>
                  {companyData?.name || 'مؤسسة المحاماة والاستشارات القانونية'}
                </h1>
                <p className="text-sm text-gray-300">Excellence in Legal Services</p>
              </div>
            </div>

            <nav className="hidden md:flex items-center space-x-8 space-x-reverse">
              <Link href="/home" className="text-gray-300 hover:text-yellow-400 font-medium transition-colors">الرئيسية</Link>
              <Link href="/home#services" className="text-gray-300 hover:text-yellow-400 font-medium transition-colors">خدماتنا</Link>
              <Link href="/home#about" className="text-gray-300 hover:text-yellow-400 font-medium transition-colors">من نحن</Link>
              <Link href="/home#library" className="text-gray-300 hover:text-yellow-400 font-medium transition-colors">المكتبة القانونية</Link>
            </nav>

            <Link href="/dashboard">
              <Button className="text-gray-900 hover:text-gray-800 shadow-lg" style={{ background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)' }}>
                <LogIn className="h-4 w-4 mr-2" />
                دخول النظام
              </Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Breadcrumb */}
      <div className="py-4" style={{ background: 'rgba(204, 169, 103, 0.1)' }}>
        <div className="container mx-auto px-4">
          <nav className="flex items-center space-x-2 space-x-reverse text-sm">
            <Link href="/home" className="text-yellow-400 hover:text-yellow-300 transition-colors">الرئيسية</Link>
            <ArrowLeft className="h-4 w-4 text-gray-500" />
            <span className="text-gray-300">خدماتنا القانونية</span>
          </nav>
        </div>
      </div>

      {/* Hero Section */}
      <section className="py-16" style={{ background: 'linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%)' }}>
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-5xl font-bold mb-6" style={{ color: '#cca967' }}>خدماتنا القانونية</h1>
            <p className="text-xl text-gray-300 leading-relaxed max-w-3xl mx-auto mb-8">
              نقدم مجموعة شاملة من الخدمات القانونية المتخصصة لتلبية جميع احتياجاتكم القانونية
            </p>

            {/* Search Bar */}
            <div className="max-w-md mx-auto">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5" style={{ color: '#cca967' }} />
                <Input
                  type="text"
                  placeholder="ابحث في الخدمات..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-4 pr-12 py-3 border rounded-lg text-white placeholder-gray-200 focus:ring-2"
                  style={{
                    background: 'rgba(204, 169, 103, 0.1)',
                    borderColor: 'rgba(204, 169, 103, 0.3)',
                    '--tw-ring-color': '#cca967'
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          {filteredServices.length === 0 ? (
            <div className="text-center py-12">
              <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">لا توجد خدمات</h3>
              <p className="text-gray-600">
                {searchQuery ? 'لم يتم العثور على خدمات تطابق بحثك' : 'لا توجد خدمات متاحة حالياً'}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredServices.map((service) => (
                <ServiceCard key={service.id} service={service} />
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Contact Section */}
      <section className="bg-gray-50 py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">هل تحتاج لاستشارة قانونية؟</h2>
            <p className="text-xl text-gray-600 mb-8">
              تواصل معنا الآن للحصول على استشارة مجانية من فريقنا المتخصص
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="bg-white p-6 rounded-xl shadow-lg">
                <Phone className="h-8 w-8 text-blue-600 mx-auto mb-4" />
                <h3 className="font-semibold text-gray-900 mb-2">اتصل بنا</h3>
                <p className="text-gray-600">{companyData?.phone || '+967-1-234567'}</p>
              </div>

              <div className="bg-white p-6 rounded-xl shadow-lg">
                <Mail className="h-8 w-8 text-blue-600 mx-auto mb-4" />
                <h3 className="font-semibold text-gray-900 mb-2">راسلنا</h3>
                <p className="text-gray-600">{companyData?.email || '<EMAIL>'}</p>
              </div>

              <div className="bg-white p-6 rounded-xl shadow-lg">
                <MapPin className="h-8 w-8 text-blue-600 mx-auto mb-4" />
                <h3 className="font-semibold text-gray-900 mb-2">زورنا</h3>
                <p className="text-gray-600">{companyData?.address || 'العنوان غير محدد'}</p>
              </div>
            </div>

            <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg font-semibold">
              <MessageCircle className="h-5 w-5 mr-2" />
              احجز استشارة مجانية
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="flex items-center justify-center mb-4">
              {companyData?.logo_image_url ? (
                <Image
                  src={companyData.logo_image_url}
                  alt="شعار الشركة"
                  width={40}
                  height={40}
                  className="rounded-lg mr-3 object-contain"
                />
              ) : companyData?.logo_url ? (
                <Image
                  src={companyData.logo_url}
                  alt="شعار الشركة"
                  width={40}
                  height={40}
                  className="rounded-lg mr-3 object-contain"
                />
              ) : (
                <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                  <Scale className="h-5 w-5 text-white" />
                </div>
              )}
              <h3 className="text-xl font-bold">
                {companyData?.name || 'مؤسسة المحاماة والاستشارات القانونية'}
              </h3>
            </div>
            <p className="text-gray-400 mb-4">
              &copy; {new Date().getFullYear()} {companyData?.name || 'مؤسسة المحاماة والاستشارات القانونية'}. جميع الحقوق محفوظة.
            </p>
            <Link href="/home" className="text-blue-400 hover:text-blue-300">
              العودة للرئيسية
            </Link>
          </div>
        </div>
      </footer>
    </div>
  )
}

function ServiceCard({ service }: { service: Service }) {
  const Icon = iconMap[service.icon_name] || Scale

  return (
    <Card className="group h-full hover:shadow-xl transition-all duration-300 hover:transform hover:-translate-y-2 border-0" style={{ background: 'linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%)', borderColor: 'rgba(204, 169, 103, 0.2)' }}>
      <CardContent className="p-8 text-center">
        <div
          className="w-16 h-16 mx-auto mb-6 rounded-lg flex items-center justify-center shadow-lg"
          style={{ background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)' }}
        >
          <Icon className="w-8 h-8 text-gray-900" />
        </div>

        <h3 className="text-xl font-bold leading-tight mb-4" style={{ color: '#cca967' }}>
          {service.title}
        </h3>

        <p className="leading-relaxed mb-6 text-gray-300">
          {service.description}
        </p>

        <Link href={`/serviceslow/${service.slug}`}>
          <Button
            className="w-full text-gray-900 hover:text-gray-800 transition-all duration-300 group-hover:transform group-hover:-translate-y-1 shadow-lg"
            style={{ background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)' }}
          >
            <span className="flex items-center justify-center">
              تفاصيل الخدمة
              <ArrowLeft className="w-4 h-4 mr-2 transition-transform duration-300 group-hover:translate-x-1" />
            </span>
          </Button>
        </Link>
      </CardContent>
    </Card>
  )
}
