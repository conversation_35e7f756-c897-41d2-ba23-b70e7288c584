import { NextRequest, NextResponse } from 'next/server'
import { promises as fs } from 'fs'
import path from 'path'

// مسار مجلد القوانين
const LAWS_DIR = path.join(process.cwd(), 'laws')

// GET - جلب جميع الملفات القانونية
export async function GET() {
  try {
    const files = await getAllLegalFiles(LAWS_DIR)

    return NextResponse.json({
      success: true,
      data: files
    })
  } catch (error) {
    console.error('Error fetching legal files:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الملفات القانونية' },
      { status: 500 }
    )
  }
}

async function getAllLegalFiles(dir: string): Promise<any[]> {
  const files: any[] = []

  try {
    const items = await fs.readdir(dir, { withFileTypes: true })

    for (const item of items) {
      const fullPath = path.join(dir, item.name)

      if (item.isDirectory()) {
        // إضافة الملفات من المجلدات الفرعية
        const subFiles = await getAllLegalFiles(fullPath)
        files.push(...subFiles)
      } else if (item.isFile() && (item.name.endsWith('.txt') || item.name.endsWith('.pdf') || item.name.endsWith('.doc') || item.name.endsWith('.docx'))) {
        // تنظيف اسم الملف
        let cleanName = item.name
          .replace(/^agoyemen\.net_\d+_/, '') // إزالة البادئة
          .replace(/\.(txt|pdf|doc|docx)$/i, '') // إزالة الامتداد
          .replace(/-/g, ' ') // استبدال الشرطات بمسافات
          .replace(/_/g, ' ') // استبدال الشرطات السفلية بمسافات

        // تحديد الفئة بناءً على محتوى اسم الملف
        let category = 'عام'
        const fileName = cleanName.toLowerCase()

        if (fileName.includes('دستور')) {
          category = 'الدستور'
        } else if (fileName.includes('جمارك') || fileName.includes('ضرائب') || fileName.includes('زكاة')) {
          category = 'مالي وضريبي'
        } else if (fileName.includes('عمل') || fileName.includes('نقابات')) {
          category = 'عمل ونقابات'
        } else if (fileName.includes('تجاري') || fileName.includes('شركات') || fileName.includes('غرف تجارية')) {
          category = 'تجاري'
        } else if (fileName.includes('جرائم') || fileName.includes('عقوبات') || fileName.includes('عسكرية')) {
          category = 'جنائي'
        } else if (fileName.includes('طفل') || fileName.includes('حقوق')) {
          category = 'حقوق الإنسان'
        } else if (fileName.includes('ملكية') || fileName.includes('فكري')) {
          category = 'ملكية فكرية'
        } else if (fileName.includes('بنوك') || fileName.includes('مالي')) {
          category = 'مصرفي ومالي'
        } else if (fileName.includes('صحافة') || fileName.includes('مطبوعات')) {
          category = 'إعلام وصحافة'
        } else if (fileName.includes('مياه') || fileName.includes('بيئة')) {
          category = 'بيئة وموارد'
        } else if (fileName.includes('تحكيم')) {
          category = 'تحكيم ومنازعات'
        } else if (fileName.includes('لائحة') || fileName.includes('تنفيذية')) {
          category = 'لوائح تنفيذية'
        }

        const stats = await fs.stat(fullPath)
        const ext = path.extname(item.name).toLowerCase()

        files.push({
          id: Buffer.from(fullPath).toString('base64'),
          name: cleanName,
          originalName: item.name,
          fileName: item.name,
          path: fullPath,
          category,
          type: ext.substring(1).toUpperCase(),
          fileType: ext.substring(1).toUpperCase(),
          size: stats.size,
          sizeFormatted: formatFileSize(stats.size),
          lastModified: stats.mtime.toISOString(),
          downloadUrl: `/api/legal-library/download?file=${encodeURIComponent(item.name)}`
        })
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error)
  }

  return files.sort((a, b) => a.name.localeCompare(b.name, 'ar'))
}

// POST - إضافة ملف قانوني جديد
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    const category = formData.get('category') as string || 'عام'

    if (!file) {
      return NextResponse.json(
        { success: false, error: 'لم يتم تحديد ملف' },
        { status: 400 }
      )
    }

    // التحقق من نوع الملف
    if (!file.name.endsWith('.txt') && !file.name.endsWith('.pdf')) {
      return NextResponse.json(
        { success: false, error: 'نوع الملف غير مدعوم. يجب أن يكون txt أو pdf' },
        { status: 400 }
      )
    }

    // تحديد المجلد المناسب
    let targetDir = LAWS_DIR
    if (category !== 'عام') {
      targetDir = path.join(LAWS_DIR, category)

      // إنشاء المجلد إذا لم يكن موجوداً
      try {
        await fs.access(targetDir)
      } catch {
        await fs.mkdir(targetDir, { recursive: true })
      }
    }

    // حفظ الملف
    const buffer = Buffer.from(await file.arrayBuffer())
    const filePath = path.join(targetDir, file.name)
    await fs.writeFile(filePath, buffer)

    return NextResponse.json({
      success: true,
      message: 'تم رفع الملف بنجاح',
      data: {
        name: file.name,
        path: filePath,
        category,
        size: buffer.length
      }
    })
  } catch (error) {
    console.error('Error uploading file:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في رفع الملف' },
      { status: 500 }
    )
  }
}

// دالة لتنسيق حجم الملف
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}