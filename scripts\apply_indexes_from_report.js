#!/usr/bin/env node
/**
 * Apply indexes based on latest DB analysis report in logs/db-analysis-*.txt
 * - Parses lines like: "- عمود شائع ... | schema.table.column"
 * - Generates CREATE INDEX CONCURRENTLY IF NOT EXISTS on detected columns
 * - Heuristics:
 *   - *_id, status => normal btree
 *   - created_at/updated_at/date/issue_date/entry_date => btree DESC
 * - Environment overrides: DB_HOST, DB_PORT, DB_USER, DB_PASSWORD, DB_NAME
 * - DRY_RUN=true will only print the SQL without executing
 */
const fs = require('fs');
const path = require('path');
const { Client } = require('pg');

function findLatestReport() {
  const dir = path.join(process.cwd(), 'logs');
  if (!fs.existsSync(dir)) throw new Error('logs directory not found');
  const files = fs.readdirSync(dir)
    .filter(f => /^db-analysis-\d+\.txt$/.test(f))
    .map(f => ({ f, mtime: fs.statSync(path.join(dir, f)).mtimeMs }))
    .sort((a,b) => b.mtime - a.mtime);
  if (files.length === 0) throw new Error('no db-analysis-*.txt found');
  return path.join(dir, files[0].f);
}

function parseTargets(reportText) {
  const targets = new Set();
  const lines = reportText.split(/\r?\n/);
  for (const line of lines) {
    const m = line.match(/\|\s*([a-zA-Z0-9_]+)\.([a-zA-Z0-9_]+)\.([a-zA-Z0-9_]+)/);
    if (m) {
      const schema = m[1];
      const table = m[2];
      const column = m[3];
      targets.add(`${schema}.${table}.${column}`);
    }
  }
  return Array.from(targets);
}

function buildConn() {
  const routing = JSON.parse(fs.readFileSync(path.join(process.cwd(), 'routing.config.json'), 'utf8'));
  const dc = routing.default_config || {};
  return {
    host: process.env.DB_HOST || dc.db_host || 'localhost',
    port: Number(process.env.DB_PORT || dc.db_port || 5432),
    user: process.env.DB_USER || dc.db_user || 'postgres',
    password: process.env.DB_PASSWORD || dc.db_password || 'postgres',
    database: process.env.DB_NAME || 'mohammi',
  };
}

function buildIndexSQL(schema, table, column) {
  const idxName = `idx_${table}_${column}`.toLowerCase();
  const fqTable = `${schema}.${table}`;
  const colLower = column.toLowerCase();
  if (colLower.endsWith('_id') || colLower === 'status') {
    return `CREATE INDEX CONCURRENTLY IF NOT EXISTS ${idxName} ON ${fqTable}(${column});`;
  }
  if (/(created_at|updated_at|date|issue_date|entry_date)$/i.test(column)) {
    return `CREATE INDEX CONCURRENTLY IF NOT EXISTS ${idxName} ON ${fqTable}(${column} DESC);`;
  }
  // default
  return `CREATE INDEX CONCURRENTLY IF NOT EXISTS ${idxName} ON ${fqTable}(${column});`;
}

(async () => {
  try {
    const reportPath = findLatestReport();
    const report = fs.readFileSync(reportPath, 'utf8');
    const targets = parseTargets(report);
    if (targets.length === 0) {
      console.log('لا توجد أهداف فهرسة في التقرير');
      process.exit(0);
    }

    const sqls = targets.map(t => {
      const [schema, table, column] = t.split('.');
      return buildIndexSQL(schema, table, column);
    });

    const uniqueSqls = Array.from(new Set(sqls));
    const dry = /^true$/i.test(process.env.DRY_RUN || 'false');

    console.log('سيتم تطبيق الفهارس التالية:\n');
    uniqueSqls.forEach(s => console.log(s));

    if (dry) {
      console.log('\nDRY_RUN=true — لن يتم التنفيذ.');
      return;
    }

    const conn = buildConn();
    const client = new Client(conn);
    await client.connect();

    for (const sql of uniqueSqls) {
      try {
        console.log(`\nتنفيذ: ${sql}`);
        await client.query(sql);
        console.log('تم');
      } catch (e) {
        console.warn('تحذير أثناء إنشاء الفهرس:', e.message);
      }
    }

    await client.end();
    console.log('\nتم تطبيق الفهارس بنجاح.');
  } catch (e) {
    console.error('فشل تطبيق الفهارس:', e.message);
    process.exitCode = 1;
  }
})();
