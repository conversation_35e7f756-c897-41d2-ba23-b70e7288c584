import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب جميع الأدوار
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'رمز المصادقة مطلوب' },
        { status: 401 }
      )
    }

    // جلب جميع الأدوار
    const result = await query(`
      SELECT 
        role_name, display_name, description, permissions
      FROM user_roles
      ORDER BY 
        CASE role_name 
          WHEN 'super_admin' THEN 1
          WHEN 'admin' THEN 2
          WHEN 'manager' THEN 3
          WHEN 'lawyer' THEN 4
          WHEN 'secretary' THEN 5
          WHEN 'accountant' THEN 6
          ELSE 7
        END
    `)

    return NextResponse.json({
      success: true,
      data: result.rows
    })

  } catch (error) {
    console.error('خطأ في جلب الأدوار:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في جلب الأدوار' },
      { status: 500 }
    )
  }
}