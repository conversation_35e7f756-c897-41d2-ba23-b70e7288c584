# 📋 ملخص نظام تتبع حركات القضايا

## 🎯 **آلية العمل العامة:**

### **1. تدفق البيانات:**
```
القضية (issues) → الحركات (case_movements) → الجلسات (court_sessions) → الإشعارات (case_notifications)
```

### **2. دورة حياة القضية:**
1. **إنشاء القضية** في جدول `issues` (موجود مسبقاً)
2. **تسجيل الحركات** في جدول `case_movements` (جديد)
3. **جدولة الجلسات** في جدول `court_sessions` (جديد)
4. **تتبع المراحل** في جدول `case_stages` (جديد)
5. **إرسال الإشعارات** عبر جدول `case_notifications` (جديد)

---

## 🗄️ **الجداول المضافة:**

### **1. جدول `case_movements` - حركات القضايا:**
```sql
CREATE TABLE case_movements (
    id SERIAL PRIMARY KEY,
    case_id INTEGER REFERENCES issues(id),
    movement_type VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    movement_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    employee_id INTEGER REFERENCES employees(id),
    priority_level VARCHAR(20) DEFAULT 'normal',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**الغرض:** تسجيل جميع الأنشطة والحركات التي تحدث على القضية

### **2. جدول `court_sessions` - جلسات المحكمة:**
```sql
CREATE TABLE court_sessions (
    id SERIAL PRIMARY KEY,
    case_id INTEGER REFERENCES issues(id),
    session_date TIMESTAMP NOT NULL,
    court_name VARCHAR(255),
    session_type VARCHAR(100) DEFAULT 'hearing',
    status VARCHAR(50) DEFAULT 'scheduled',
    reminder_days INTEGER DEFAULT 3,
    client_reminder_sent BOOLEAN DEFAULT false,
    employee_reminder_sent BOOLEAN DEFAULT false,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**الغرض:** إدارة مواعيد الجلسات والتذكيرات

### **3. جدول `case_stages` - مراحل القضايا:**
```sql
CREATE TABLE case_stages (
    id SERIAL PRIMARY KEY,
    case_id INTEGER REFERENCES issues(id),
    stage_name VARCHAR(255) NOT NULL,
    stage_type VARCHAR(100),
    status VARCHAR(50) DEFAULT 'pending',
    start_date DATE,
    end_date DATE,
    responsible_employee_id INTEGER REFERENCES employees(id),
    order_index INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**الغرض:** تتبع مراحل تقدم القضية

### **4. جدول `case_notifications` - الإشعارات:**
```sql
CREATE TABLE case_notifications (
    id SERIAL PRIMARY KEY,
    case_id INTEGER REFERENCES issues(id),
    recipient_type VARCHAR(50) NOT NULL,
    recipient_id INTEGER NOT NULL,
    notification_type VARCHAR(100) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    scheduled_date TIMESTAMP NOT NULL,
    sent_date TIMESTAMP,
    status VARCHAR(50) DEFAULT 'pending',
    delivery_method VARCHAR(50) DEFAULT 'email',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**الغرض:** إدارة الإشعارات والتذكيرات

### **5. جدول `tracking_settings` - إعدادات التتبع:**
```sql
CREATE TABLE tracking_settings (
    id SERIAL PRIMARY KEY,
    setting_name VARCHAR(255) UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    setting_type VARCHAR(50) DEFAULT 'string',
    description TEXT,
    category VARCHAR(100) DEFAULT 'general',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**الغرض:** تخزين إعدادات النظام القابلة للتخصيص

---

## 🔗 **ربط الجداول:**

### **العلاقات الأساسية:**
```
issues (1) ←→ (N) case_movements
issues (1) ←→ (N) court_sessions  
issues (1) ←→ (N) case_stages
issues (1) ←→ (N) case_notifications

employees (1) ←→ (N) case_movements
employees (1) ←→ (N) case_stages
clients (1) ←→ (N) case_notifications (عند recipient_type = 'client')
```

### **الفهارس المطلوبة:**
```sql
CREATE INDEX idx_case_movements_case_id ON case_movements(case_id);
CREATE INDEX idx_court_sessions_case_id ON court_sessions(case_id);
CREATE INDEX idx_case_stages_case_id ON case_stages(case_id);
CREATE INDEX idx_case_notifications_case_id ON case_notifications(case_id);
CREATE INDEX idx_case_notifications_recipient ON case_notifications(recipient_type, recipient_id);
```

---

## ⚙️ **الإعدادات المطلوبة:**

### **إعدادات أساسية في `tracking_settings`:**
```sql
INSERT INTO tracking_settings (setting_name, setting_value, setting_type, description, category) VALUES
('default_reminder_days', '3', 'number', 'عدد الأيام الافتراضي للتذكير قبل الجلسات', 'notifications'),
('auto_client_notifications', 'true', 'boolean', 'تفعيل الإشعارات التلقائية للعملاء', 'notifications'),
('auto_employee_notifications', 'true', 'boolean', 'تفعيل الإشعارات التلقائية للموظفين', 'notifications'),
('case_inactivity_threshold', '30', 'number', 'عدد الأيام لاعتبار القضية متوقفة', 'tracking'),
('priority_case_threshold', '7', 'number', 'عدد الأيام لاعتبار القضية عالية الأولوية', 'tracking'),
('working_hours_start', '08:00', 'time', 'بداية ساعات العمل', 'schedule'),
('working_hours_end', '17:00', 'time', 'نهاية ساعات العمل', 'schedule'),
('weekend_days', '["friday","saturday"]', 'json', 'أيام نهاية الأسبوع', 'schedule'),
('notification_methods', '["email","sms"]', 'json', 'طرق الإشعار المفعلة', 'notifications');
```

---

## 🔄 **آلية التتبع التلقائي:**

### **1. تسجيل الحركات:**
- عند إنشاء قضية جديدة → حركة "case_created"
- عند تحديث حالة القضية → حركة "status_changed"
- عند إضافة وثيقة → حركة "document_added"
- عند جدولة جلسة → حركة "session_scheduled"

### **2. إدارة الجلسات:**
- جدولة الجلسات مع تحديد أيام التذكير
- إرسال تذكيرات تلقائية للعملاء والموظفين
- تتبع حالة الجلسات (مجدولة، مكتملة، مؤجلة)

### **3. تتبع المراحل:**
- تحديد مراحل القضية حسب النوع
- تتبع تقدم كل مرحلة
- تحديد المسؤول عن كل مرحلة

### **4. نظام الإشعارات:**
- تذكيرات الجلسات (قبل 1-7 أيام)
- تنبيهات القضايا المتوقفة (بعد 30+ يوم)
- تحذيرات المواعيد النهائية
- تقارير دورية للإدارة

---

## 📊 **APIs المطورة:**

### **1. `/api/case-movements`**
- GET: جلب حركات القضايا مع الإحصائيات
- POST: إضافة حركة جديدة
- PUT: تحديث حركة موجودة

### **2. `/api/court-sessions`**
- GET: جلب الجلسات مع الفلترة
- POST: جدولة جلسة جديدة
- PUT: تحديث جلسة موجودة

### **3. `/api/case-analytics`**
- GET: إحصائيات وتحليلات شاملة
- POST: إنشاء تقارير مخصصة

### **4. `/api/auto-notifications`**
- GET: تشغيل نظام التنبيهات التلقائية
- POST: تخصيص إعدادات التنبيهات

### **5. `/api/tracking-settings`**
- GET: جلب الإعدادات
- POST: إضافة إعداد جديد
- PUT: تحديث إعداد موجود

---

## 🎯 **الصفحات المطورة:**

### **1. `/movements` - حركة القضايا:**
- عرض جميع القضايا مع آخر الحركات
- الخط الزمني لمراحل كل قضية
- إحصائيات سريعة وتنبيهات عاجلة
- فلترة وبحث متقدم

### **2. `/analytics` - التحليلات:**
- إحصائيات شاملة للقضايا والجلسات
- أداء الموظفين ومقارنات
- رسوم بيانية وتوزيعات
- تقارير قابلة للتصدير

### **3. `/settings/tracking` - إعدادات التتبع:**
- تخصيص أيام التذكير
- إعدادات الإشعارات
- ساعات العمل وأيام العطل
- طرق التواصل المفعلة

---

## 🔧 **متطلبات التشغيل:**

### **1. قاعدة البيانات:**
- PostgreSQL مع الجداول المطلوبة
- الفهارس المحسنة للأداء
- البيانات التجريبية للاختبار

### **2. الخادم:**
- Node.js مع Next.js
- اتصال بقاعدة البيانات
- APIs مفعلة ومختبرة

### **3. الواجهة:**
- مكونات UI محدثة
- تكامل مع النظام الموجود
- دعم اللغة العربية والـ RTL

---

## 🎉 **الفوائد المحققة:**

✅ **تتبع شامل** لجميع أنشطة القضايا  
✅ **إشعارات ذكية** تمنع فوات المواعيد  
✅ **تحليلات متقدمة** لتحسين الأداء  
✅ **أتمتة العمليات** وتقليل الأخطاء  
✅ **واجهة سهلة** ومتجاوبة  
✅ **تقارير مفصلة** لاتخاذ القرارات  

النظام الآن مكتمل ومتكامل مع البنية الموجودة! 🚀
