{"routes": {"7443": {"database": "moham<PERSON>", "company_name": "نظام إدارة المحاماة - محمد", "theme_color": "#cca967", "logo_text": "نظام إدارة المحاماة - محمد", "description": "نظام إدارة المحاماة - محمد", "welcome_message": "مرحباً بكم في نظام إدارة المحاماة - محمد", "notification_prefix": "محمد: ", "enabled": true, "homepage": "/home", "internal_next_port": 3000}, "8914": {"database": "rubaie", "company_name": "نظام إدارة المحاماة - الربعي", "theme_color": "#2563eb", "logo_text": "نظام إدارة المحاماة - الربعي", "description": "نظام إدارة المحاماة - الربعي", "welcome_message": "مرحباً بكم في نظام إدارة المحاماة - الربعي", "notification_prefix": "الربعي: ", "enabled": true, "homepage": "/home", "internal_next_port": 3001}}, "default_config": {"db_host": "localhost", "db_port": 5432, "db_user": "postgres", "db_password": "yemen123", "jwt_secret": "your-secret-key-here", "next_port": 3001, "default_theme": "#1e40af", "default_welcome": "مرحباً بكم في نظام إدارة المحاماة", "default_notification_prefix": "إشعار: ", "ssl": {"enabled": true, "key_path": "D:\\server\\correct_private_key.key", "cert_path": "D:\\server\\mohammi_com.crt", "ca_path": "D:\\server\\SectigoPublicServerAuthenticationCADVR36.crt"}}, "system_settings": {"auto_create_missing_databases": true, "validate_database_connection": true, "log_all_requests": true, "show_detailed_errors": true, "auto_restart_on_error": true, "connection_timeout": 5000, "max_request_size": "50mb"}}