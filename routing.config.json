{"routes": {"3300": {"database": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "company_name": "نظام إدارة المحاماة - محم<PERSON> (التطوير)", "theme_color": "#ff6b35", "logo_text": "نظام إدارة المحاماة - محم<PERSON> (التطوير)", "description": "نظام إدارة المحاماة - محمد (نسخة التطوير)", "welcome_message": "مرحباً بكم في نظام إدارة المحاماة - محمد (نسخة التطوير)", "notification_prefix": "محمد (تطوير): ", "enabled": true, "homepage": "/home", "internal_next_port": 3300}}, "system_settings": {"auto_create_missing_databases": true, "validate_database_connection": true, "log_all_requests": true, "show_detailed_errors": true, "auto_restart_on_error": true, "connection_timeout": 5000, "max_request_size": "50mb"}}