import { NextResponse } from 'next/server'
import { query } from '@/lib/database'

export async function POST() {
  try {
    // حذف البيانات الموجودة
    await query('DELETE FROM serviceslow')
    
    // إدراج البيانات الافتراضية
    const defaultServices = [
      {
        title: 'التقاضي والمرافعات',
        slug: 'litigation-advocacy',
        description: 'تمثيل قضائي احترافي أمام جميع المحاكم والدرجات القضائية مع ضمان أفضل النتائج',
        content: `
          <h2>خدمات التقاضي والمرافعات</h2>
          <p>نقدم خدمات تمثيل قضائي شاملة أمام جميع المحاكم والدرجات القضائية، مع فريق من أمهر المحامين المتخصصين.</p>
          
          <h3>خدماتنا تشمل:</h3>
          <ul>
            <li>التمثيل أمام المحاكم الابتدائية</li>
            <li>الاستئناف أمام محاكم الاستئناف</li>
            <li>الطعن أمام المحكمة العليا</li>
            <li>إعداد المذكرات القانونية</li>
            <li>جمع الأدلة والشهادات</li>
          </ul>
        `,
        icon_name: 'Gavel',
        icon_color: '#2563eb',
        is_active: true,
        sort_order: 1,
        meta_title: 'خدمات التقاضي والمرافعات - مكتب المحاماة',
        meta_description: 'تمثيل قضائي احترافي أمام جميع المحاكم مع ضمان أفضل النتائج القانونية'
      },
      {
        title: 'صياغة العقود والاتفاقيات',
        slug: 'contracts-agreements',
        description: 'إعداد ومراجعة العقود القانونية بأعلى معايير الدقة والاحترافية لحماية مصالحك',
        content: `
          <h2>خدمات صياغة العقود والاتفاقيات</h2>
          <p>نتخصص في إعداد ومراجعة جميع أنواع العقود والاتفاقيات القانونية بأعلى معايير الدقة.</p>
          
          <h3>أنواع العقود التي نصيغها:</h3>
          <ul>
            <li>عقود البيع والشراء</li>
            <li>عقود الإيجار والتأجير</li>
            <li>عقود العمل والتوظيف</li>
            <li>عقود الشراكة التجارية</li>
            <li>اتفاقيات السرية</li>
            <li>عقود الخدمات المهنية</li>
          </ul>
        `,
        icon_name: 'FileText',
        icon_color: '#059669',
        is_active: true,
        sort_order: 2,
        meta_title: 'صياغة العقود والاتفاقيات القانونية',
        meta_description: 'إعداد ومراجعة العقود القانونية بأعلى معايير الدقة والاحترافية'
      },
      {
        title: 'قانون الشركات والاستثمار',
        slug: 'corporate-investment-law',
        description: 'استشارات قانونية متخصصة في تأسيس الشركات والاستثمار والامتثال التنظيمي',
        content: `
          <h2>خدمات قانون الشركات والاستثمار</h2>
          <p>نقدم استشارات قانونية شاملة للشركات والمستثمرين في جميع مراحل النمو والتطوير.</p>
          
          <h3>خدماتنا تشمل:</h3>
          <ul>
            <li>تأسيس الشركات بجميع أنواعها</li>
            <li>هيكلة الاستثمارات</li>
            <li>الامتثال التنظيمي</li>
            <li>عمليات الاندماج والاستحواذ</li>
            <li>حوكمة الشركات</li>
            <li>قانون الأوراق المالية</li>
          </ul>
        `,
        icon_name: 'Building',
        icon_color: '#7c3aed',
        is_active: true,
        sort_order: 3,
        meta_title: 'قانون الشركات والاستثمار - استشارات قانونية',
        meta_description: 'استشارات قانونية متخصصة في تأسيس الشركات والاستثمار والامتثال التنظيمي'
      },
      {
        title: 'القانون الجنائي والدفاع',
        slug: 'criminal-law-defense',
        description: 'دفاع قوي ومتخصص في القضايا الجنائية مع فريق من أمهر المحامين الجنائيين',
        content: `
          <h2>خدمات القانون الجنائي والدفاع</h2>
          <p>نوفر دفاعاً قوياً ومتخصصاً في جميع أنواع القضايا الجنائية مع ضمان حماية حقوقك.</p>
          
          <h3>مجالات تخصصنا:</h3>
          <ul>
            <li>الجرائم المالية والاقتصادية</li>
            <li>جرائم الإنترنت والتكنولوجيا</li>
            <li>القضايا الجنائية العامة</li>
            <li>جرائم الشركات</li>
            <li>التحقيقات الجنائية</li>
            <li>الطعون الجنائية</li>
          </ul>
        `,
        icon_name: 'Shield',
        icon_color: '#dc2626',
        is_active: true,
        sort_order: 4,
        meta_title: 'القانون الجنائي والدفاع - محامي جنائي',
        meta_description: 'دفاع قوي ومتخصص في القضايا الجنائية مع فريق من أمهر المحامين'
      },
      {
        title: 'قانون العمل والعلاقات الصناعية',
        slug: 'labor-industrial-relations',
        description: 'حلول قانونية متكاملة لقضايا العمل وحقوق الموظفين وأصحاب العمل',
        content: `
          <h2>خدمات قانون العمل والعلاقات الصناعية</h2>
          <p>نقدم حلولاً قانونية شاملة لجميع قضايا العمل والعلاقات الصناعية.</p>
          
          <h3>خدماتنا تشمل:</h3>
          <ul>
            <li>عقود العمل والتوظيف</li>
            <li>فصل الموظفين والتعويضات</li>
            <li>النزاعات العمالية</li>
            <li>حقوق الموظفين</li>
            <li>سياسات الشركات</li>
            <li>التأمينات الاجتماعية</li>
          </ul>
        `,
        icon_name: 'Briefcase',
        icon_color: '#ea580c',
        is_active: true,
        sort_order: 5,
        meta_title: 'قانون العمل والعلاقات الصناعية',
        meta_description: 'حلول قانونية متكاملة لقضايا العمل وحقوق الموظفين وأصحاب العمل'
      },
      {
        title: 'القانون المدني والأحوال الشخصية',
        slug: 'civil-personal-status-law',
        description: 'خدمات قانونية شاملة في القضايا المدنية وقضايا الأسرة والميراث',
        content: `
          <h2>خدمات القانون المدني والأحوال الشخصية</h2>
          <p>نتخصص في جميع القضايا المدنية وقضايا الأحوال الشخصية والأسرة.</p>
          
          <h3>مجالات خدماتنا:</h3>
          <ul>
            <li>قضايا الطلاق والخلع</li>
            <li>النفقة وحضانة الأطفال</li>
            <li>قسمة التركات والميراث</li>
            <li>القضايا المدنية العامة</li>
            <li>التعويضات والأضرار</li>
            <li>عقود الزواج</li>
          </ul>
        `,
        icon_name: 'UserCheck',
        icon_color: '#0891b2',
        is_active: true,
        sort_order: 6,
        meta_title: 'القانون المدني والأحوال الشخصية',
        meta_description: 'خدمات قانونية شاملة في القضايا المدنية وقضايا الأسرة والميراث'
      }
    ];

    for (const service of defaultServices) {
      await query(`
        INSERT INTO serviceslow (
          title, slug, description, content, icon_name, icon_color,
          is_active, sort_order, meta_title, meta_description
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      `, [
        service.title,
        service.slug,
        service.description,
        service.content,
        service.icon_name,
        service.icon_color,
        service.is_active,
        service.sort_order,
        service.meta_title,
        service.meta_description
      ]);
    }

    return NextResponse.json({
      success: true,
      message: `تم إدراج ${defaultServices.length} خدمة بنجاح`,
      count: defaultServices.length
    });

  } catch (error) {
    console.error('خطأ في إدراج البيانات الافتراضية:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في إدراج البيانات الافتراضية' },
      { status: 500 }
    );
  }
}
