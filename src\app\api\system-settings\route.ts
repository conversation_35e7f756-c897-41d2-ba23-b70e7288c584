import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب جميع إعدادات النظام أو إعداد محدد
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const key = searchParams.get('key')

    let result
    if (key) {
      // جلب إعداد محدد
      result = await query(
        'SELECT * FROM system_settings WHERE setting_key = $1',
        [key]
      )
    } else {
      // جلب جميع الإعدادات
      result = await query(
        'SELECT * FROM system_settings ORDER BY setting_key'
      )
    }

    return NextResponse.json({
      success: true,
      data: key ? (result.rows[0] || null) : result.rows
    })

  } catch (error) {
    console.error('خطأ في جلب إعدادات النظام:', error)
    return NextResponse.json(
      { success: false, error: 'خطأ في جلب الإعدادات' },
      { status: 500 }
    )
  }
}

// POST - إضافة إعداد جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { setting_key, setting_value, setting_type, description, is_editable } = body

    if (!setting_key || setting_value === undefined) {
      return NextResponse.json(
        { success: false, error: 'مفتاح الإعداد والقيمة مطلوبان' },
        { status: 400 }
      )
    }

    const result = await query(`
      INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_editable)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `, [setting_key, setting_value, setting_type || 'string', description || '', is_editable !== false])

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم إضافة الإعداد بنجاح'
    })

  } catch (error) {
    console.error('خطأ في إضافة إعداد النظام:', error)
    
    if (error.code === '23505') { // unique violation
      return NextResponse.json(
        { success: false, error: 'مفتاح الإعداد موجود بالفعل' },
        { status: 409 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'خطأ في إضافة الإعداد' },
      { status: 500 }
    )
  }
}

// PUT - تحديث إعداد موجود
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { setting_key, setting_value, setting_type, description } = body

    if (!setting_key || setting_value === undefined) {
      return NextResponse.json(
        { success: false, error: 'مفتاح الإعداد والقيمة مطلوبان' },
        { status: 400 }
      )
    }

    // التحقق من وجود الإعداد وإمكانية تعديله
    const existingResult = await query(
      'SELECT * FROM system_settings WHERE setting_key = $1',
      [setting_key]
    )

    if (existingResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الإعداد غير موجود' },
        { status: 404 }
      )
    }

    if (!existingResult.rows[0].is_editable) {
      return NextResponse.json(
        { success: false, error: 'هذا الإعداد غير قابل للتعديل' },
        { status: 403 }
      )
    }

    // تحديث الإعداد
    const updateFields = ['setting_value = $2']
    const updateValues = [setting_key, setting_value]
    let paramIndex = 3

    if (setting_type) {
      updateFields.push(`setting_type = $${paramIndex}`)
      updateValues.push(setting_type)
      paramIndex++
    }

    if (description !== undefined) {
      updateFields.push(`description = $${paramIndex}`)
      updateValues.push(description)
      paramIndex++
    }

    const result = await query(`
      UPDATE system_settings 
      SET ${updateFields.join(', ')}
      WHERE setting_key = $1
      RETURNING *
    `, updateValues)

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم تحديث الإعداد بنجاح'
    })

  } catch (error) {
    console.error('خطأ في تحديث إعداد النظام:', error)
    return NextResponse.json(
      { success: false, error: 'خطأ في تحديث الإعداد' },
      { status: 500 }
    )
  }
}

// DELETE - حذف إعداد
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const key = searchParams.get('key')

    if (!key) {
      return NextResponse.json(
        { success: false, error: 'مفتاح الإعداد مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من وجود الإعداد وإمكانية حذفه
    const existingResult = await query(
      'SELECT * FROM system_settings WHERE setting_key = $1',
      [key]
    )

    if (existingResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الإعداد غير موجود' },
        { status: 404 }
      )
    }

    if (!existingResult.rows[0].is_editable) {
      return NextResponse.json(
        { success: false, error: 'هذا الإعداد غير قابل للحذف' },
        { status: 403 }
      )
    }

    await query('DELETE FROM system_settings WHERE setting_key = $1', [key])

    return NextResponse.json({
      success: true,
      message: 'تم حذف الإعداد بنجاح'
    })

  } catch (error) {
    console.error('خطأ في حذف إعداد النظام:', error)
    return NextResponse.json(
      { success: false, error: 'خطأ في حذف الإعداد' },
      { status: 500 }
    )
  }
}
