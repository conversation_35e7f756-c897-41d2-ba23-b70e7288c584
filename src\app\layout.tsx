import type { Metadata } from "next";
import "./globals.css";
import "../styles/professional-theme.css";
import "../styles/tafahum-theme.css";
import "../styles/latin-numbers.css";
import "../styles/search-layout.css";

export const metadata: Metadata = {
  title: "نظام الإدارة القانونية",
  description: "نظام شامل لإدارة المكاتب القانونية والقضايا",
  other: {
    'http-equiv': 'Content-Type',
    content: 'text/html; charset=UTF-8'
  }
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl">
      <head>
        <meta charSet="UTF-8" />
        <meta httpEquiv="Content-Type" content="text/html; charset=UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        {/* Inline SVG favicon to avoid /favicon.ico network request */}
        <link
          rel="icon"
          href={`data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 64 64'><rect width='64' height='64' rx='12' ry='12' fill='%231e40af'/><text x='50%' y='58%' dominant-baseline='middle' text-anchor='middle' font-family='Arial' font-size='36' fill='white'>ل</text></svg>`}
        />
      </head>
      <body>
        {children}
      </body>
    </html>
  );
}
