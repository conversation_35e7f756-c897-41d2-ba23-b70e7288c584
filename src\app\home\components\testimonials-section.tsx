"use client";

import { useState, useRef, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Star } from 'lucide-react';


type Testimonial = {
  id: number;
  name: string;
  role: string;
  company: string;
  avatar: string;
  content: string;
  rating: number;
  date: string;
};

export function TestimonialsSection() {
  const [activeIndex, setActiveIndex] = useState(0);

  const sliderRef = useRef<HTMLDivElement>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const testimonials: Testimonial[] = [
    {
      id: 1,
      name: 'أحمد السيد',
      role: 'مدير عام',
      company: 'شركة التقنية المتطورة',
      avatar: '',
      content: 'أشكر فريق العمل على الاحترافية العالية في التعامل مع قضيتنا. لقد قدموا لنا استشارات قانونية متميزة ساعدتنا في تجنب العديد من المشاكل القانونية.',
      rating: 5,
      date: '15 مارس 2023'
    },
    {
      id: 2,
      name: 'سارة محمد',
      role: 'رئيسة قسم الشؤون القانونية',
      company: 'مجموعة الأعمال المتحدة',
      avatar: '',
      content: 'تعاملنا مع المكتب في عدة قضايا معقدة وكان أداؤهم ممتازاً. ننصح بهم بشدة لكل من يبحث عن استشارات قانونية احترافية.',
      rating: 5,
      date: '2 أبريل 2023'
    },
    {
      id: 3,
      name: 'خالد عبدالله',
      role: 'مالك',
      company: 'مطاعم الذواقة',
      avatar: '',
      content: 'المحامي محمد من أفضل من تعاملت معهم في مجال المحاماة. يمتلك خبرة واسعة وأسلوباً مهنياً راقياً في التعامل مع القضايا.',
      rating: 4,
      date: '22 أبريل 2023'
    },
    {
      id: 4,
      name: 'نورة سليمان',
      role: 'مديرة الموارد البشرية',
      company: 'شركة المستقبل',
      avatar: '',
      content: 'فريق عمل محترف ويتمتع بأعلى معايير النزاهة والكفاءة. ساعدونا في حل نزاع عمل معقد بكل كفاءة واحترافية.',
      rating: 5,
      date: '5 مايو 2023'
    },
    {
      id: 5,
      name: 'عمر أحمد',
      role: 'مدير مالي',
      company: 'شركة الاستثمارات العقارية',
      avatar: '',
      content: 'نحن نتعامل مع المكتب منذ أكثر من 3 سنوات في جميع استشاراتنا القانونية. نثق بهم تماماً وننصح الجميع بالتعامل معهم.',
      rating: 5,
      date: '18 مايو 2023'
    }
  ];

  const visibleTestimonials = testimonials.slice(0, 3); // Show 3 testimonials at a time

  const goToPrev = () => {
    setActiveIndex((prev) => (prev === 0 ? testimonials.length - 1 : prev - 1));
    resetAutoPlay();
  };

  const goToNext = () => {
    setActiveIndex((prev) => (prev === testimonials.length - 1 ? 0 : prev + 1));
    resetAutoPlay();
  };

  const goToSlide = (index: number) => {
    setActiveIndex(index);
    resetAutoPlay();
  };

  const resetAutoPlay = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    intervalRef.current = setInterval(goToNext, 8000);
  };

  useEffect(() => {
    intervalRef.current = setInterval(goToNext, 8000);
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [activeIndex]);

  const renderStars = (rating: number) => {
    return Array(5)
      .fill(0)
      .map((_, i) => (
        <Star
          key={i}
          className={`w-5 h-5 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
        />
      ));
  };

  return (
    <section className="py-20" style={{ background: 'linear-gradient(135deg, #333333 0%, #171717 100%)' }}>
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <span className="inline-block text-sm font-semibold px-6 py-3 rounded-full mb-6 border" style={{
            background: 'linear-gradient(to right, rgba(204, 169, 103, 0.2), rgba(204, 169, 103, 0.1))',
            color: '#cca967',
            borderColor: 'rgba(204, 169, 103, 0.3)'
          }}>
            آراء العملاء
          </span>
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">ماذا يقول عملاؤنا</h2>
          <div className="w-24 h-1 rounded-full mx-auto my-8" style={{ background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)' }}></div>
          <p className="text-gray-300 max-w-2xl mx-auto">
            آراء وتقييمات عملائنا الكرام الذين استفادوا من خدماتنا القانونية المتميزة
          </p>
        </div>

        <div className="relative">
          {/* Navigation Arrows - بتصميم تفاهم */}
          <div className="flex justify-between items-center absolute left-0 right-0 top-1/2 -translate-y-1/2 z-10 px-4">
            <button
              className="rounded-full w-14 h-14 text-gray-900 shadow-xl hover:shadow-2xl transition-all duration-300 flex items-center justify-center"
              style={{
                background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)',
              }}
              onClick={goToPrev}
            >
              <ChevronRight className="w-6 h-6" />
              <span className="sr-only">السابق</span>
            </button>
            <button
              className="rounded-full w-14 h-14 text-gray-900 shadow-xl hover:shadow-2xl transition-all duration-300 flex items-center justify-center"
              style={{
                background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)',
              }}
              onClick={goToNext}
            >
              <ChevronLeft className="w-6 h-6" />
              <span className="sr-only">التالي</span>
            </button>
          </div>

          {/* Testimonials Slider */}
          <div
            ref={sliderRef}
            className="relative overflow-hidden"
          >
            <div
              className="flex transition-transform duration-500 ease-in-out"
              style={{
                transform: `translateX(-${activeIndex * (100 / 3)}%)`,
                width: `${testimonials.length * (100 / 3)}%`
              }}
            >
              {testimonials.map((testimonial, index) => (
                <div
                  key={testimonial.id}
                  className={`w-full md:w-1/3 px-4 transition-all duration-300 ${activeIndex === index ? 'scale-105' : 'scale-95 opacity-70'}`}
                >
                  <div className="h-full p-8 rounded-xl border relative" style={{
                    background: 'rgba(34, 34, 34, 0.3)',
                    borderColor: 'rgba(204, 169, 103, 0.1)'
                  }}>
                    {/* علامة الاقتباس */}
                    <div className="absolute top-4 right-6 text-6xl font-serif" style={{ color: '#cca967' }}>"</div>

                    <div className="text-lg text-white font-medium leading-relaxed mb-6 pr-8 italic">
                      {testimonial.content}
                    </div>

                    <div className="flex items-center gap-4 pr-8">
                      <div className="w-12 h-12 rounded-full flex items-center justify-center text-gray-900 font-bold" style={{
                        background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)'
                      }}>
                        {testimonial.name.charAt(0)}
                      </div>
                      <div>
                        <h4 className="text-white font-semibold">{testimonial.name}</h4>
                        <p className="text-gray-300 text-sm">{testimonial.role} - {testimonial.company}</p>
                      </div>
                    </div>

                    <div className="flex items-center justify-between mt-6 pr-8">
                      <div className="flex">
                        {renderStars(testimonial.rating)}
                      </div>
                      <div className="text-sm text-gray-400">{testimonial.date}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Dots Indicator */}
          <div className="flex justify-center mt-8 space-x-2 space-x-reverse">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-colors ${
                  index === activeIndex ? 'bg-blue-600 w-8' : 'bg-gray-300'
                }`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </div>


      </div>
    </section>
  );
}
