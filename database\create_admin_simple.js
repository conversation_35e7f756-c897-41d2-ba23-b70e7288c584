// إنشاء مستخدم admin بكلمة مرور بسيطة
const { Client } = require('pg');

async function createAdminUser() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'moham<PERSON>',
    user: 'postgres',
    password: 'yemen123'
  });
  
  try {
    await client.connect();
    console.log('✅ متصل بقاعدة البيانات');

    // حذف المستخدم admin إذا كان موجود
    await client.query("DELETE FROM users WHERE username = 'admin'");
    console.log('🗑️ تم حذف المستخدم admin السابق');

    // إنشاء مستخدم admin جديد
    const result = await client.query(`
      INSERT INTO users (username, password_hash, email, role)
      VALUES ('admin', 'admin123', '<EMAIL>', 'admin')
      RETURNING id, username, email, role
    `);

    const admin = result.rows[0];
    
    console.log('\n✅ تم إنشاء مستخدم admin بنجاح:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log(`📋 ID: ${admin.id}`);
    console.log(`👤 اسم المستخدم: ${admin.username}`);
    console.log(`🔑 كلمة المرور: admin123`);
    console.log(`📧 البريد الإلكتروني: ${admin.email}`);
    console.log(`🎭 الدور: ${admin.role}`);
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

    // التحقق من المستخدم
    const verification = await client.query(`
      SELECT username, password_hash, email, role 
      FROM users 
      WHERE username = 'admin'
    `);

    if (verification.rows.length > 0) {
      const user = verification.rows[0];
      console.log('\n🔍 التحقق من البيانات:');
      console.log(`   اسم المستخدم: ${user.username}`);
      console.log(`   كلمة المرور المحفوظة: ${user.password_hash}`);
      console.log(`   البريد: ${user.email}`);
      console.log(`   الدور: ${user.role}`);
    }

    console.log('\n🌐 يمكنك الآن تسجيل الدخول باستخدام:');
    console.log('   اسم المستخدم: admin');
    console.log('   كلمة المرور: admin123');
    console.log('   الرابط: http://localhost:7443/login');

  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await client.end();
  }
}

createAdminUser();
