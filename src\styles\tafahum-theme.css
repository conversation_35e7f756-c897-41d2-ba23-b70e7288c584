/* تصميم مستوحى من موقع تفاهم */

:root {
  /* الألوان الأساسية */
  --tafahum-gold: #cca967;
  --tafahum-dark: #222222;
  --tafahum-darker: #171717;
  --tafahum-light-gold: rgba(204, 169, 103, 0.1);
  --tafahum-medium-gold: rgba(204, 169, 103, 0.3);
  --tafahum-text-light: #9f9f9f;
  --tafahum-text-white: #ffffff;
  --tafahum-bg-card: rgba(34, 34, 34, 0.3);
  --tafahum-bg-dark: #333333;
  
  /* الظلال */
  --tafahum-shadow: 0px 10px 30px rgba(0, 0, 0, 0.3);
  --tafahum-shadow-light: 0px 5px 15px rgba(0, 0, 0, 0.2);
  
  /* الحواف المدورة */
  --tafahum-radius: 10px;
  --tafahum-radius-large: 20px;
  --tafahum-radius-small: 5px;
}

/* الخلفية العامة */
body {
  background: linear-gradient(135deg, var(--tafahum-bg-dark) 0%, var(--tafahum-darker) 100%);
  color: var(--tafahum-text-light);
  font-family: 'Cairo', sans-serif;
}

/* البطاقات */
.tafahum-card {
  background: var(--tafahum-bg-card);
  border-radius: var(--tafahum-radius);
  padding: 40px;
  box-shadow: var(--tafahum-shadow);
  transition: all 0.3s ease;
  border: 1px solid rgba(204, 169, 103, 0.1);
}

.tafahum-card:hover {
  transform: translateY(-5px);
  box-shadow: 0px 15px 40px rgba(0, 0, 0, 0.4);
  border-color: var(--tafahum-gold);
}

/* الأزرار */
.tafahum-btn {
  background: var(--tafahum-gold);
  color: var(--tafahum-dark);
  font-weight: 700;
  padding: 15px 40px;
  border-radius: 100px;
  border: none;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 10px;
}

.tafahum-btn:hover {
  background: var(--tafahum-text-white);
  color: var(--tafahum-dark);
  transform: translateY(-2px);
  box-shadow: var(--tafahum-shadow);
}

.tafahum-btn-outline {
  background: transparent;
  color: var(--tafahum-gold);
  border: 2px solid var(--tafahum-gold);
}

.tafahum-btn-outline:hover {
  background: var(--tafahum-gold);
  color: var(--tafahum-dark);
}

/* العناوين */
.tafahum-title {
  color: var(--tafahum-text-white);
  font-weight: 700;
  margin-bottom: 20px;
}

.tafahum-title-gold {
  color: var(--tafahum-gold);
}

.tafahum-subtitle {
  color: var(--tafahum-text-light);
  font-size: 18px;
  line-height: 1.6;
}

/* الخط الذهبي */
.tafahum-divider {
  background: var(--tafahum-gold);
  width: 100px;
  height: 2px;
  margin: 45px auto;
  border-radius: 2px;
}

/* الأيقونات */
.tafahum-icon {
  background: var(--tafahum-gold);
  color: var(--tafahum-dark);
  width: 60px;
  height: 60px;
  border-radius: var(--tafahum-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-bottom: 20px;
}

.tafahum-icon-circle {
  border-radius: 50%;
  width: 50px;
  height: 50px;
  font-size: 20px;
}

/* الإحصائيات */
.tafahum-stat {
  text-align: center;
  padding: 30px;
  background: var(--tafahum-bg-card);
  border-radius: var(--tafahum-radius);
  border: 1px solid rgba(204, 169, 103, 0.1);
  transition: all 0.3s ease;
}

.tafahum-stat:hover {
  border-color: var(--tafahum-gold);
  transform: translateY(-3px);
}

.tafahum-stat-number {
  font-size: 48px;
  font-weight: 700;
  color: var(--tafahum-gold);
  margin-bottom: 10px;
}

.tafahum-stat-label {
  color: var(--tafahum-text-light);
  font-size: 16px;
}

/* الخدمات */
.tafahum-service {
  background: var(--tafahum-text-white);
  color: var(--tafahum-dark);
  padding: 50px 40px;
  border-radius: var(--tafahum-radius);
  text-align: center;
  transition: all 0.3s ease;
  height: 100%;
  box-shadow: var(--tafahum-shadow-light);
}

.tafahum-service:hover {
  transform: translateY(-10px);
  box-shadow: var(--tafahum-shadow);
}

.tafahum-service h3 {
  color: var(--tafahum-dark);
  font-size: 24px;
  margin-bottom: 15px;
}

.tafahum-service p {
  color: #666;
  line-height: 1.6;
}

/* الشهادات */
.tafahum-testimonial {
  background: var(--tafahum-bg-card);
  padding: 40px;
  border-radius: var(--tafahum-radius-large);
  border: 1px solid rgba(204, 169, 103, 0.1);
  position: relative;
}

.tafahum-testimonial::before {
  content: '"';
  font-size: 80px;
  color: var(--tafahum-gold);
  position: absolute;
  top: 10px;
  left: 20px;
  font-family: serif;
}

.tafahum-testimonial-text {
  font-size: 18px;
  color: var(--tafahum-text-white);
  font-style: italic;
  line-height: 1.6;
  margin-bottom: 20px;
  padding-left: 40px;
}

.tafahum-testimonial-author {
  display: flex;
  align-items: center;
  gap: 15px;
  padding-left: 40px;
}

.tafahum-testimonial-avatar {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--tafahum-gold), #d4b876);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--tafahum-dark);
  font-weight: 700;
}

.tafahum-testimonial-info h4 {
  color: var(--tafahum-text-white);
  margin: 0;
  font-size: 16px;
}

.tafahum-testimonial-info p {
  color: var(--tafahum-text-light);
  margin: 0;
  font-size: 14px;
}

/* الفوتر */
.tafahum-footer {
  background: linear-gradient(135deg, var(--tafahum-dark) 0%, var(--tafahum-darker) 100%);
  padding: 80px 0 40px;
  border-top: 1px solid rgba(204, 169, 103, 0.1);
}

.tafahum-footer-widget h4 {
  color: var(--tafahum-gold);
  font-size: 20px;
  margin-bottom: 20px;
}

.tafahum-footer-widget a {
  color: var(--tafahum-text-light);
  text-decoration: none;
  transition: color 0.3s ease;
}

.tafahum-footer-widget a:hover {
  color: var(--tafahum-gold);
}

/* الهيدر */
.tafahum-header {
  background: rgba(34, 34, 34, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(204, 169, 103, 0.1);
}

.tafahum-nav-link {
  color: var(--tafahum-text-light);
  text-decoration: none;
  padding: 10px 20px;
  border-radius: 25px;
  transition: all 0.3s ease;
  font-weight: 600;
}

.tafahum-nav-link:hover,
.tafahum-nav-link.active {
  color: var(--tafahum-gold);
  background: var(--tafahum-light-gold);
}

/* تأثيرات خاصة */
.tafahum-gradient-text {
  background: linear-gradient(135deg, var(--tafahum-gold), #d4b876);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.tafahum-glow {
  box-shadow: 0 0 20px rgba(204, 169, 103, 0.3);
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
  .tafahum-card {
    padding: 30px 20px;
  }
  
  .tafahum-service {
    padding: 40px 30px;
  }
  
  .tafahum-testimonial {
    padding: 30px 20px;
  }
  
  .tafahum-testimonial-text {
    padding-left: 20px;
  }
  
  .tafahum-testimonial-author {
    padding-left: 20px;
  }
  
  .tafahum-stat-number {
    font-size: 36px;
  }
}
