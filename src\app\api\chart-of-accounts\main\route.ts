import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب الحسابات الرئيسية فقط (للاستخدام في نماذج إضافة الحسابات الفرعية)
export async function GET() {
  try {
    const result = await query(`
      SELECT
        id,
        account_code,
        account_name,
        account_type,
        account_level,
        account_nature,
        (
          SELECT COUNT(*)
          FROM chart_of_accounts child
          WHERE child.parent_id = c.id AND child.is_active = true
        ) as children_count
      FROM chart_of_accounts c
      WHERE c.is_main_account = true AND c.is_active = true
      ORDER BY c.account_code
    `)

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching main accounts:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الحسابات الرئيسية' },
      { status: 500 }
    )
  }
}
