{"case_movements": [{"id": 1, "case_id": 1, "case_number": "2024/001", "case_title": "قضية تجارية - شركة الأمل", "movement_type": "case_created", "description": "تم إنشاء القضية في النظام", "created_by_name": "النظام", "priority": "normal", "client_name": "شركة الأمل للتجارة", "case_status": "نشطة", "created_at": "2025-09-12T20:22:20.688Z", "movement_date": "2025-09-12"}, {"id": 2, "case_id": 1, "case_number": "2024/001", "case_title": "قضية تجارية - شركة الأمل", "movement_type": "case_assigned", "description": "تم توزيع القضية على المحامي أحمد محمد", "created_by_name": "المدير العام", "priority": "high", "client_name": "شركة الأمل للتجارة", "case_status": "موزعة", "created_at": "2025-09-11T20:22:20.688Z", "movement_date": "2025-09-11"}, {"id": 3, "case_id": 2, "case_number": "2024/002", "case_title": "قضية عمالية - <PERSON><PERSON>م<PERSON> علي", "movement_type": "case_created", "description": "تم إنشاء القضية في النظام", "created_by_name": "النظام", "priority": "normal", "client_name": "محم<PERSON> ع<PERSON>ي أحمد", "case_status": "نشطة", "created_at": "2025-09-10T20:22:20.688Z", "movement_date": "2025-09-10"}, {"id": 4, "case_id": 2, "case_number": "2024/002", "case_title": "قضية عمالية - <PERSON><PERSON>م<PERSON> علي", "movement_type": "hearing_scheduled", "description": "تم تحديد جلسة للقضية في المحكمة العمالية", "created_by_name": "<PERSON><PERSON><PERSON><PERSON> محمد", "priority": "high", "client_name": "محم<PERSON> ع<PERSON>ي أحمد", "case_status": "جلسة مجدولة", "created_at": "2025-09-11T20:22:20.688Z", "movement_date": "2025-09-11"}, {"id": 5, "case_id": 3, "case_number": "2024/003", "case_title": "قضية مدنية - فاطمة سالم", "movement_type": "case_created", "description": "تم إنشاء القضية في النظام", "created_by_name": "النظام", "priority": "normal", "client_name": "فاطمة سالم محمد", "case_status": "نشطة", "created_at": "2025-09-09T20:22:20.688Z", "movement_date": "2025-09-09"}, {"id": 6, "case_id": 3, "case_number": "2024/003", "case_title": "قضية مدنية - فاطمة سالم", "movement_type": "document_uploaded", "description": "تم رفع وثيقة \"عقد الإيجار الأصلي\" للقضية", "created_by_name": "سارة أحمد", "priority": "normal", "client_name": "فاطمة سالم محمد", "case_status": "قيد المراجعة", "created_at": "2025-09-10T20:22:20.688Z", "movement_date": "2025-09-10"}, {"id": 7, "case_id": 4, "case_number": "2024/004", "case_title": "قضية جنائية - <PERSON><PERSON><PERSON><PERSON> يوسف", "movement_type": "case_created", "description": "تم إنشاء القضية في النظام", "created_by_name": "النظام", "priority": "urgent", "client_name": "<PERSON><PERSON><PERSON><PERSON> يو<PERSON><PERSON> علي", "case_status": "نشطة", "created_at": "2025-09-08T20:22:20.688Z", "movement_date": "2025-09-08"}, {"id": 8, "case_id": 4, "case_number": "2024/004", "case_title": "قضية جنائية - <PERSON><PERSON><PERSON><PERSON> يوسف", "movement_type": "follow_added", "description": "تم إضافة متابعة: تم تقديم الاستئناف للمحكمة العليا", "created_by_name": "م<PERSON>م<PERSON> الحا<PERSON>دي", "priority": "urgent", "client_name": "<PERSON><PERSON><PERSON><PERSON> يو<PERSON><PERSON> علي", "case_status": "استئناف", "created_at": "2025-09-11T20:22:20.688Z", "movement_date": "2025-09-11"}, {"id": 9, "case_id": 5, "case_number": "2024/005", "case_title": "قضية إدارية - شركة النور", "movement_type": "case_created", "description": "تم إنشاء القضية في النظام", "created_by_name": "النظام", "priority": "normal", "client_name": "شركة النور للمقاولات", "case_status": "نشطة", "created_at": "2025-09-07T20:22:20.688Z", "movement_date": "2025-09-07"}, {"id": 10, "case_id": 5, "case_number": "2024/005", "case_title": "قضية إدارية - شركة النور", "movement_type": "case_status_changed", "description": "تم تغيير حالة القضية من \"نشطة\" إلى \"قيد المراجعة\"", "created_by_name": "<PERSON><PERSON><PERSON><PERSON> محمد", "priority": "normal", "client_name": "شركة النور للمقاولات", "case_status": "قيد المراجعة", "created_at": "2025-09-09T20:22:20.688Z", "movement_date": "2025-09-09"}], "metadata": {"total_movements": 10, "total_cases": 5, "last_updated": "2025-09-12T20:22:20.688Z", "database_type": "JSON_FALLBACK"}}