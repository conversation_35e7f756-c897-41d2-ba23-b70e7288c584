const { Client } = require('pg');

async function createDocumentsTable() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    user: 'postgres',
    password: 'yemen123',
    database: 'moham<PERSON>'
  });

  try {
    await client.connect();
    console.log('🔗 متصل بقاعدة البيانات');

    // إنشاء جدول الوثائق
    await client.query(`
      CREATE TABLE IF NOT EXISTS documents (
        id SERIAL PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        file_name VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size BIGINT,
        file_type VARCHAR(100),
        mime_type VARCHAR(100),
        
        -- ربط بالجداول الأخرى
        case_id INTEGER,
        client_id INTEGER,
        employee_id INTEGER,
        
        -- تصنيف الوثائق
        category VARCHAR(100) DEFAULT 'general',
        subcategory VARCHAR(100),
        tags TEXT[],
        
        -- البحث في المحتوى
        content_text TEXT,
        keywords TEXT[],
        
        -- الأمان والصلاحيات
        access_level VARCHAR(50) DEFAULT 'private',
        is_confidential BOOLEAN DEFAULT false,
        
        -- معلومات الإنشاء والتعديل
        uploaded_by INTEGER,
        version_number INTEGER DEFAULT 1,
        is_active BOOLEAN DEFAULT true,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    console.log('✅ تم إنشاء جدول documents بنجاح');

    // إنشاء الفهارس
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_documents_case_id ON documents(case_id);
      CREATE INDEX IF NOT EXISTS idx_documents_client_id ON documents(client_id);
      CREATE INDEX IF NOT EXISTS idx_documents_category ON documents(category);
      CREATE INDEX IF NOT EXISTS idx_documents_is_active ON documents(is_active);
    `);

    console.log('✅ تم إنشاء الفهارس بنجاح');

    // إدراج بيانات تجريبية
    await client.query(`
      INSERT INTO documents (title, description, file_name, file_path, category, tags, content_text, uploaded_by)
      VALUES 
      ('دليل النظام', 'دليل استخدام النظام القانوني', 'system_guide.pdf', '/uploads/documents/system_guide.pdf', 'guide', ARRAY['دليل', 'نظام'], 'دليل شامل لاستخدام النظام القانوني', 1),
      ('نموذج عقد', 'نموذج عقد قانوني', 'contract_template.pdf', '/uploads/documents/contract_template.pdf', 'template', ARRAY['نموذج', 'عقد'], 'نموذج عقد قانوني للاستخدام', 1)
      ON CONFLICT DO NOTHING
    `);

    console.log('✅ تم إدراج البيانات التجريبية بنجاح');

  } catch (error) {
    console.error('❌ خطأ:', error);
  } finally {
    await client.end();
  }
}

createDocumentsTable();
