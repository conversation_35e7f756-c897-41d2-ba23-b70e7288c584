/**
 * Middleware للتحقق من الجلسات النشطة
 * يطبق نظام الجلسة الواحدة - جلسة واحدة فقط لكل مستخدم
 */

import { NextRequest, NextResponse } from 'next/server'
import jwt from 'jsonwebtoken'

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-here'

// المسارات التي تحتاج للتحقق من الجلسة
const PROTECTED_PATHS = [
  '/dashboard',
  '/home',
  '/cases',
  '/clients',
  '/employees',
  '/settings',
  '/reports',
  '/calendar',
  '/documents',
  '/billing',
  '/tasks',
  '/api/cases',
  '/api/clients',
  '/api/employees',
  '/api/settings',
  '/api/reports',
  '/api/documents',
  '/api/billing',
  '/api/tasks'
]

// المسارات المستثناة من التحقق
const EXCLUDED_PATHS = [
  '/login',
  '/api/auth',
  '/api/public',
  '/_next',
  '/favicon.ico',
  '/static'
]

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // تخطي طلبات prefetch وطلبات HEAD/OPTIONS لتفادي 404 في الكونسول عند التوجيه المسبق
  const isPrefetch = request.headers.get('next-router-prefetch') === '1' || request.headers.get('purpose') === 'prefetch'
  const method = request.method
  if (isPrefetch || method === 'HEAD' || method === 'OPTIONS') {
    return NextResponse.next()
  }

  // تخطي المسارات المستثناة
  if (EXCLUDED_PATHS.some(path => pathname.startsWith(path))) {
    return NextResponse.next()
  }
  
  // التحقق من المسارات المحمية
  const isProtectedPath = PROTECTED_PATHS.some(path => pathname.startsWith(path))
  
  if (isProtectedPath) {
    return await validateSession(request)
  }
  
  return NextResponse.next()
}

async function validateSession(request: NextRequest): Promise<NextResponse> {
  try {
    // جلب التوكن من الهيدر أو الكوكيز
    const authHeader = request.headers.get('authorization')
    const cookieToken = request.cookies.get('auth-token')?.value
    
    let token = null
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7)
    } else if (cookieToken) {
      token = cookieToken
    }
    
    if (!token) {
      return redirectToLogin(request)
    }
    
    // التحقق من JWT
    let decoded: any
    try {
      decoded = jwt.verify(token, JWT_SECRET)
    } catch (jwtError) {
      console.log('❌ JWT غير صحيح:', jwtError.message)
      return redirectToLogin(request)
    }
    
    // التحقق من وجود معلومات الجلسة
    if (!decoded.sessionToken || !decoded.userId) {
      console.log('❌ معلومات الجلسة مفقودة في JWT')
      return redirectToLogin(request)
    }
    
    // التحقق من صحة الجلسة في قاعدة البيانات
    const isValidSession = await checkSessionInDatabase(decoded.sessionToken, decoded.userId)
    
    if (!isValidSession) {
      console.log(`❌ الجلسة غير صحيحة للمستخدم ${decoded.userId}`)
      return redirectToLogin(request, 'تم تسجيل دخولك من جهاز آخر')
    }
    
    // تحديث آخر نشاط للجلسة
    await updateSessionActivity(decoded.sessionToken)
    
    // إضافة معلومات المستخدم للهيدر
    const response = NextResponse.next()
    response.headers.set('x-user-id', decoded.userId.toString())
    response.headers.set('x-session-token', decoded.sessionToken)
    
    return response
    
  } catch (error) {
    console.error('❌ خطأ في التحقق من الجلسة:', error)
    return redirectToLogin(request)
  }
}

async function checkSessionInDatabase(sessionToken: string, userId: number): Promise<boolean> {
  try {
    // استيراد ديناميكي لتجنب مشاكل الاستيراد في middleware
    const { query } = await import('./lib/db')
    
    const result = await query(`
      SELECT 1 FROM active_sessions 
      WHERE session_token = $1 
        AND user_id = $2
        AND is_active = true
        AND last_activity > NOW() - INTERVAL '24 hours'
    `, [sessionToken, userId])
    
    return result.rows.length > 0
    
  } catch (error) {
    console.error('❌ خطأ في التحقق من الجلسة في قاعدة البيانات:', error)
    return false
  }
}

async function updateSessionActivity(sessionToken: string): Promise<void> {
  try {
    const { query } = await import('./lib/db')
    
    await query(`
      UPDATE active_sessions 
      SET last_activity = CURRENT_TIMESTAMP,
          updated_at = CURRENT_TIMESTAMP
      WHERE session_token = $1 AND is_active = true
    `, [sessionToken])
    
  } catch (error) {
    console.error('❌ خطأ في تحديث نشاط الجلسة:', error)
  }
}

function redirectToLogin(request: NextRequest, message?: string): NextResponse {
  const loginUrl = new URL('/login', request.url)
  
  if (message) {
    loginUrl.searchParams.set('message', message)
  }
  
  // إضافة الصفحة المطلوبة للعودة إليها بعد تسجيل الدخول
  loginUrl.searchParams.set('redirect', request.nextUrl.pathname)
  
  const response = NextResponse.redirect(loginUrl)
  
  // حذف الكوكيز
  response.cookies.delete('auth-token')
  response.cookies.delete('user-data')
  
  return response
}

// تحديد المسارات التي يطبق عليها الـ middleware
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (authentication routes)
     * - api/public (public API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api/auth|api/public|_next/static|_next/image|favicon.ico).*)',
  ],
}
