'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { useState, useEffect } from 'react'
import {
  Home,
  Users,
  FileText,
  Building,
  Building2,
  Database,
  MapPin,
  Gavel,
  UserCheck,
  DollarSign,
  BarChart3,
  Settings,
  Calendar,
  Briefcase,
  Scale,
  Percent,
  BookOpen,
  Receipt,
  CreditCard,
  Target,
  TrendingUp,
  TrendingDown,
  PieChart,
  ArrowRightLeft,
  Share2,
  Link2,
  Shield,
  Menu,
  X,
  ChevronDown,
  ChevronUp,
  Calculator,
  ExternalLink,
  Megaphone,
  Search,
  Clock,
  FolderOpen,
  Upload,
  Archive,
  Bot,
  Globe,
  MessageSquare,
  Smartphone,
  QrCode,
  Bell,
  BarChart2
} from 'lucide-react'

const menuItems = [
  {
    title: 'لوحة التحكم',
    href: '/',
    icon: BarChart3,
    color: 'bg-gradient-to-r from-blue-500 to-blue-600',
    textColor: 'text-white'
  },
  {
    title: 'إدارة البيانات الأساسية',
    color: 'bg-gradient-to-r from-emerald-500 to-emerald-600',
    textColor: 'text-white',
    items: [
      {
        title: 'بيانات الشركة',
        href: '/company',
        icon: Building,
        color: 'bg-emerald-100',
        textColor: 'text-emerald-700'
      },
      {
        title: 'الموكلين',
        href: '/clients',
        icon: UserCheck,
        color: 'bg-emerald-100',
        textColor: 'text-emerald-700'
      },
      {
        title: 'الموظفين',
        href: '/employees',
        icon: Users,
        color: 'bg-emerald-100',
        textColor: 'text-emerald-700'
      },
      {
        title: 'المستخدمين',
        href: '/users',
        icon: Users,
        color: 'bg-emerald-100',
        textColor: 'text-emerald-700'
      }
    ]
  },
  {
    title: 'إدارة القضايا',
    color: 'bg-gradient-to-r from-purple-500 to-purple-600',
    textColor: 'text-white',
    items: [
      {
        title: 'القضايا',
        href: '/issues',
        icon: FileText,
        color: 'bg-purple-100',
        textColor: 'text-purple-700'
      },
      {
        title: 'توزيع القضايا',
        href: '/case-distribution',
        icon: Share2,
        color: 'bg-purple-100',
        textColor: 'text-purple-700'
      },
      {
        title: 'أنواع القضايا',
        href: '/issue-types',
        icon: Scale,
        color: 'bg-purple-100',
        textColor: 'text-purple-700'
      },
      {
        title: 'المتابعات',
        href: '/follows',
        icon: Calendar,
        color: 'bg-purple-100',
        textColor: 'text-purple-700'
      },
      {
        title: 'حركة القضايا',
        href: '/movements',
        icon: Briefcase,
        color: 'bg-purple-100',
        textColor: 'text-purple-700'
      }
    ]
  },
  {
    title: 'النظام المحاسبي',
    color: 'bg-gradient-to-r from-green-500 to-green-600',
    textColor: 'text-white',
    items: [
      {
        title: 'لوحة المحاسبة',
        href: '/accounting',
        icon: Calculator,
        color: 'bg-blue-100',
        textColor: 'text-blue-700'
      },
      {
        title: 'دليل الحسابات',
        href: '/accounting/chart-of-accounts',
        icon: BookOpen,
        color: 'bg-blue-100',
        textColor: 'text-blue-700'
      },
      {
        title: 'ربط الحسابات',
        href: '/accounting/account-links',
        icon: Link2,
        color: 'bg-purple-100',
        textColor: 'text-purple-700'
      },
      {
        title: 'سندات الصرف',
        href: '/accounting/payment-vouchers',
        icon: Receipt,
        color: 'bg-red-100',
        textColor: 'text-red-700'
      },
      {
        title: 'سندات القبض',
        href: '/accounting/receipt-vouchers',
        icon: FileText,
        color: 'bg-green-100',
        textColor: 'text-green-700'
      },
      {
        title: 'القيود اليومية',
        href: '/accounting/journal-entries',
        icon: Calculator,
        color: 'bg-purple-100',
        textColor: 'text-purple-700'
      },
      {
        title: 'الأرصدة الافتتاحية',
        href: '/accounting/opening-balances',
        icon: DollarSign,
        color: 'bg-yellow-100',
        textColor: 'text-yellow-700'
      },
      {
        title: 'ربط الحسابات الجديدة',
        href: '/accounting/link-accounts',
        icon: Link2,
        color: 'bg-emerald-100',
        textColor: 'text-emerald-700'
      },
      {
        title: 'التقارير المحاسبية',
        href: '/accounting/reports',
        icon: BarChart3,
        color: 'bg-orange-100',
        textColor: 'text-orange-700'
      },
      {
        title: 'كشف حساب',
        href: '/accounting/reports/account-statement',
        icon: FileText,
        color: 'bg-cyan-100',
        textColor: 'text-cyan-700'
      },
      {
        title: 'تتبع الوقت والفوترة',
        href: '/time-tracking',
        icon: Clock,
        color: 'bg-indigo-100',
        textColor: 'text-indigo-700'
      },
      {
        title: 'إدارة الوثائق',
        href: '/documents',
        icon: FolderOpen,
        color: 'bg-teal-100',
        textColor: 'text-teal-700'
      },
      {
        title: 'الفواتير',
        href: '/invoices',
        icon: Receipt,
        color: 'bg-pink-100',
        textColor: 'text-pink-700'
      }
    ]
  },
  {
    title: 'إدارة الوثائق',
    color: 'bg-gradient-to-r from-teal-500 to-teal-600',
    textColor: 'text-white',
    items: [
      {
        title: 'المكتبة القانونية',
        href: '/legal-library',
        icon: BookOpen,
        color: 'bg-teal-100',
        textColor: 'text-teal-700'
      },
      {
        title: 'مكتبة الوثائق',
        href: '/documents',
        icon: FolderOpen,
        color: 'bg-teal-100',
        textColor: 'text-teal-700'
      },
      {
        title: 'رفع الوثائق',
        href: '/documents/upload',
        icon: Upload,
        color: 'bg-teal-100',
        textColor: 'text-teal-700'
      },
      {
        title: 'الأرشيف',
        href: '/documents/archive',
        icon: Archive,
        color: 'bg-teal-100',
        textColor: 'text-teal-700'
      }
    ]
  },
  {
    title: 'بوابة العملاء',
    color: 'bg-gradient-to-r from-indigo-500 to-indigo-600',
    textColor: 'text-white',
    items: [
      {
        title: 'بوابة العملاء',
        href: '/client-portal',
        icon: Users,
        color: 'bg-indigo-100',
        textColor: 'text-indigo-700'
      },
      {
        title: 'حسابات العملاء',
        href: '/client-accounts',
        icon: UserCheck,
        color: 'bg-indigo-100',
        textColor: 'text-indigo-700'
      }
    ]
  },
  {
    title: 'التقارير والإحصائيات',
    color: 'bg-gradient-to-r from-rose-500 to-rose-600',
    textColor: 'text-white',
    items: [
      {
        title: 'التقارير المتقدمة',
        href: '/reports',
        icon: BarChart3,
        color: 'bg-rose-100',
        textColor: 'text-rose-700'
      },
      {
        title: 'تقارير القضايا',
        href: '/case-reports',
        icon: FileText,
        color: 'bg-rose-100',
        textColor: 'text-rose-700'
      },
      {
        title: 'تقارير المالية',
        href: '/financial-reports',
        icon: DollarSign,
        color: 'bg-rose-100',
        textColor: 'text-rose-700'
      },
      {
        title: 'تقارير الموظفين',
        href: '/employee-reports',
        icon: Users,
        color: 'bg-rose-100',
        textColor: 'text-rose-700'
      }
    ]
  },
  {
    title: 'الإعدادات',
    color: 'bg-gradient-to-r from-gray-500 to-gray-600',
    textColor: 'text-white',
    items: [
      {
        title: 'المحافظات',
        href: '/governorates',
        icon: MapPin,
        color: 'bg-gray-100',
        textColor: 'text-gray-700'
      },
      {
        title: 'الفروع',
        href: '/branches',
        icon: Building,
        color: 'bg-gray-100',
        textColor: 'text-gray-700'
      },
      {
        title: 'المحاكم',
        href: '/courts',
        icon: Gavel,
        color: 'bg-gray-100',
        textColor: 'text-gray-700'
      },
      {
        title: 'النسب',
        href: '/percentages',
        icon: Percent,
        color: 'bg-gray-100',
        textColor: 'text-gray-700'
      },
      {
        title: 'إدارة الأقسام',
        href: '/settings/departments',
        icon: Building2,
        color: 'bg-gray-100',
        textColor: 'text-gray-700'
      },
      {
        title: 'الخدمات',
        href: '/services',
        icon: Briefcase,
        color: 'bg-gray-100',
        textColor: 'text-gray-700'
      },
      {
        title: 'مراكز التكلفة',
        href: '/settings/cost-centers',
        icon: Building2,
        color: 'bg-gray-100',
        textColor: 'text-gray-700'
      },
      {
        title: 'الإعلانات',
        href: '/settings/announcements',
        icon: Megaphone,
        color: 'bg-gray-100',
        textColor: 'text-gray-700'
      },
      {
        title: 'صفحات التنقل',
        href: '/settings/navigation-pages',
        icon: Search,
        color: 'bg-gray-100',
        textColor: 'text-gray-700'
      },
      {
        title: 'الترحيل',
        href: '/transfers',
        icon: ArrowRightLeft,
        color: 'bg-gray-100',
        textColor: 'text-gray-700'
      },
      {
        title: 'الأمان',
        href: '/security',
        icon: Settings,
        color: 'bg-gray-100',
        textColor: 'text-gray-700'
      },
      {
        title: 'الذكاء الاصطناعي',
        href: '/admin/ai-settings',
        icon: Bot,
        color: 'bg-blue-100',
        textColor: 'text-blue-700'
      },
      {
        title: 'إدارة الموقع الرئيسي',
        href: '/website-admin',
        icon: Globe,
        color: 'bg-purple-100',
        textColor: 'text-purple-700'
      },
      {
        title: 'إعدادات WhatsApp',
        href: '/settings/whatsapp',
        icon: MessageSquare,
        color: 'bg-green-100',
        textColor: 'text-green-700'
      },
      {
        title: 'إدارة الأجهزة',
        href: '/settings/device-management',
        icon: Smartphone,
        color: 'bg-blue-100',
        textColor: 'text-blue-700'
      }
    ]
  }
]

interface SidebarProps {
  isCollapsed: boolean
  onToggle: () => void
}

export function Sidebar({ isCollapsed, onToggle }: SidebarProps) {
  const pathname = usePathname()
  const [openMenus, setOpenMenus] = useState<number[]>([])

  const toggleMenu = (index: number) => {
    setOpenMenus(prev =>
      prev.includes(index)
        ? prev.filter(i => i !== index)
        : [index] // فتح قائمة واحدة فقط في كل مرة
    )
  }

  // فتح القائمة التي تحتوي على الصفحة الحالية تلقائياً
  useEffect(() => {
    menuItems.forEach((item, index) => {
      if (item.items) {
        const hasActiveItem = item.items.some(subItem => subItem.href === pathname)
        if (hasActiveItem && !openMenus.includes(index)) {
          setOpenMenus([index])
        }
      }
    })
  }, [pathname])

  const getGroupColor = (index: number) => {
    const colors = [
      'text-blue-400 border-blue-400',
      'text-green-400 border-green-400',
      'text-purple-400 border-purple-400',
      'text-orange-400 border-orange-400',
      'text-cyan-400 border-cyan-400',
      'text-pink-400 border-pink-400',
      'text-yellow-400 border-yellow-400',
      'text-red-400 border-red-400',
      'text-indigo-400 border-indigo-400',
      'text-emerald-400 border-emerald-400'
    ]
    return colors[index % colors.length]
  }

  return (
    <div className={cn(
      "flex h-full flex-col bg-white text-gray-900 transition-all duration-300 ease-in-out border-r border-gray-200",
      isCollapsed ? "w-16" : "w-64"
    )} style={{ backgroundColor: '#ffffff' }}>
      {/* العنوان الرئيسي */}
      {!isCollapsed && (
        <div className="px-4 py-3 border-b border-gray-200">
          <h1 className="text-lg font-bold text-gray-900 whitespace-nowrap">
            نظام إدارة القضايا
          </h1>
        </div>
      )}

      <div className="flex h-16 items-center justify-between border-b border-gray-200 px-4" style={{ backgroundColor: '#ffffff' }}>
        <button
          onClick={onToggle}
          className="flex items-center justify-center w-10 h-10 rounded-lg bg-red-600 hover:bg-red-700 transition-colors duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
        >
          {isCollapsed ? (
            <Menu className="h-5 w-5 text-white" />
          ) : (
            <Menu className="h-5 w-5 text-white" />
          )}
        </button>

      </div>

      <nav className="flex-1 space-y-3 p-3 overflow-y-auto">
        {menuItems.map((item, index) => (
          <div key={index}>
            {item.href ? (
              <Link
                href={item.href}
                className={cn(
                  'flex items-center rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105',
                  isCollapsed ? 'justify-center px-2 py-3' : 'space-x-2 space-x-reverse px-4 py-3',
                  'text-lg font-bold',
                  pathname === item.href
                    ? `${item.color} ${item.textColor} shadow-xl scale-105`
                    : `${item.color} ${item.textColor} hover:shadow-xl hover:scale-105 opacity-90 hover:opacity-100`
                )}
                title={isCollapsed ? item.title : undefined}
              >
                <div className="p-2 rounded-lg bg-white bg-opacity-20">
                  <item.icon className="h-6 w-6" />
                </div>
                {!isCollapsed && (
                  <span className="font-bold">{item.title}</span>
                )}
              </Link>
            ) : (
              <div className="mb-2">
                {!isCollapsed && (
                  <button
                    onClick={() => toggleMenu(index)}
                    className={cn(
                      "w-full flex items-center justify-between mb-2 px-4 py-3 text-xl font-bold uppercase tracking-wider rounded-lg shadow-lg transform transition-all duration-200 hover:scale-105",
                      item.color,
                      item.textColor,
                      openMenus.includes(index) ? "ring-2 ring-white ring-opacity-50" : ""
                    )}
                  >
                    <span>{item.title}</span>
                    {openMenus.includes(index) ? (
                      <ChevronUp className="h-5 w-5" />
                    ) : (
                      <ChevronDown className="h-5 w-5" />
                    )}
                  </button>
                )}

                {/* القائمة الفرعية - تظهر فقط عند فتح القائمة الرئيسية */}
                <div className={cn(
                  "overflow-hidden transition-all duration-500 ease-in-out",
                  isCollapsed ? "space-y-1" : "mr-2",
                  openMenus.includes(index)
                    ? "max-h-[1000px] opacity-100 transform translate-y-0"
                    : "max-h-0 opacity-0 transform -translate-y-2"
                )}>
                  <div className="space-y-2 pt-2 pb-2">
                    {item.items?.map((subItem, subIndex) => (
                      <Link
                        key={subIndex}
                        href={subItem.href}
                        className={cn(
                          'flex items-center rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105',
                          isCollapsed
                            ? 'justify-center px-2 py-2'
                            : 'space-x-2 space-x-reverse px-3 py-2 mr-4',
                          'text-sm font-medium',
                          pathname === subItem.href
                            ? `${subItem.color} ${subItem.textColor} shadow-lg scale-105 border-r-4 border-white`
                            : `${subItem.color} ${subItem.textColor} hover:shadow-lg hover:scale-105 opacity-90 hover:opacity-100`
                        )}
                        title={isCollapsed ? subItem.title : undefined}
                      >
                        <div className="p-1.5 rounded-md bg-white bg-opacity-30">
                          <subItem.icon className="h-4 w-4" />
                        </div>
                        {!isCollapsed && (
                          <span className="font-medium">{subItem.title}</span>
                        )}
                      </Link>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
      </nav>
    </div>
  )
}
