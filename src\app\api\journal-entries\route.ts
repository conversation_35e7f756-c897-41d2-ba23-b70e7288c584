import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع القيود اليومية من قاعدة البيانات
export async function GET() {
  try {
    const result = await query('SELECT * FROM journal_entries ORDER BY id')

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching القيود اليومية:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'فشل في جلب بيانات القيود اليومية',
        message: 'تأكد من وجود الجدول في قاعدة البيانات'
      },
      { status: 500 }
    )
  }
}

// POST - إضافة القيود اليومية جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { voucher_number, voucher_date, description, entries } = body

    // تتبع العملات المستلمة
    console.log('💰 API - العملات المستلمة:', entries?.map(e => ({
      currency_id: e.currency_id,
      exchange_rate: e.exchange_rate
    })))

    // إدراج السند الرئيسي
    const journalEntryResult = await query(
      `INSERT INTO journal_entries (voucher_number, voucher_date, description, created_at)
       VALUES ($1, $2, $3, NOW()) RETURNING id`,
      [voucher_number, voucher_date, description]
    )

    const journalEntryId = journalEntryResult.rows[0].id

    // إدراج تفاصيل السند مع بيانات العملة
    for (const entry of entries) {
      console.log(`💾 حفظ قيد - العملة: ${entry.currency_id}, سعر الصرف: ${entry.exchange_rate}`)

      await query(
        `INSERT INTO journal_entry_details
         (journal_entry_id, debit_account_id, credit_account_id, debit_amount, credit_amount,
          currency_id, exchange_rate, debit_amount_yr, credit_amount_yr, description)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`,
        [
          journalEntryId,
          entry.debit_account_id,
          entry.credit_account_id,
          entry.amount, // debit_amount
          entry.amount, // credit_amount (نفس المبلغ في النظام المبسط)
          entry.currency_id,
          entry.exchange_rate,
          entry.amount_yer, // debit_amount_yr
          entry.amount_yer, // credit_amount_yr
          entry.description
        ]
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم إضافة القيد اليومي بنجاح',
      data: { id: journalEntryId }
    })
  } catch (error) {
    console.error('Error creating journal entry:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة القيد اليومي: ' + error.message },
      { status: 500 }
    )
  }
}

// PUT - تحديث القيود اليومية
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()

    // هنا يجب إضافة منطق التحديث حسب كل جدول
    // سيتم تحديثه لاحقاً حسب هيكل كل جدول

    return NextResponse.json({
      success: true,
      message: 'تم تحديث القيود اليومية بنجاح'
    })
  } catch (error) {
    console.error('Error updating القيود اليومية:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث القيود اليومية' },
      { status: 500 }
    )
  }
}

// DELETE - حذف القيود اليومية
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف القيود اليومية مطلوب' },
        { status: 400 }
      )
    }

    await query('DELETE FROM journal_entries WHERE id = $1', [id])

    return NextResponse.json({
      success: true,
      message: 'تم حذف القيود اليومية بنجاح'
    })
  } catch (error) {
    console.error('Error deleting القيود اليومية:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف القيود اليومية' },
      { status: 500 }
    )
  }
}