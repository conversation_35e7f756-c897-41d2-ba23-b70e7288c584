'use client'

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertCircle, LogIn, RefreshCw } from 'lucide-react'

export default function SessionExpiredPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [countdown, setCountdown] = useState(10)
  
  const message = searchParams.get('message') || 'انتهت صلاحية الجلسة'
  const redirectPath = searchParams.get('redirect') || '/dashboard'
  
  useEffect(() => {
    // تنظيف البيانات المحلية
    localStorage.removeItem('userToken')
    localStorage.removeItem('clientToken')
    localStorage.removeItem('userData')
    localStorage.removeItem('sessionToken')
    
    // عداد تنازلي للتوجيه التلقائي
    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          clearInterval(timer)
          router.push('/login')
          return 0
        }
        return prev - 1
      })
    }, 1000)
    
    return () => clearInterval(timer)
  }, [router])
  
  const handleLoginRedirect = () => {
    router.push(`/login?redirect=${encodeURIComponent(redirectPath)}`)
  }
  
  const handleRefresh = () => {
    window.location.reload()
  }
  
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4" dir="rtl">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
            <AlertCircle className="h-8 w-8 text-red-600" />
          </div>
          <CardTitle className="text-xl font-bold text-gray-900">
            انتهت صلاحية الجلسة
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <div className="text-center">
            <p className="text-gray-600 mb-4">
              {message}
            </p>
            
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
              <h3 className="font-semibold text-blue-800 mb-2">
                نظام الجلسة الواحدة
              </h3>
              <p className="text-blue-700 text-sm">
                يسمح النظام بجلسة واحدة فقط لكل مستخدم. عند تسجيل الدخول من جهاز جديد، 
                يتم إنهاء الجلسات الأخرى تلقائياً لضمان الأمان.
              </p>
            </div>
            
            <div className="bg-gray-100 rounded-lg p-3 mb-4">
              <p className="text-sm text-gray-600">
                سيتم توجيهك لصفحة تسجيل الدخول خلال:
              </p>
              <p className="text-2xl font-bold text-gray-900">
                {countdown} ثانية
              </p>
            </div>
          </div>
          
          <div className="space-y-3">
            <Button 
              onClick={handleLoginRedirect}
              className="w-full"
            >
              <LogIn className="h-4 w-4 ml-2" />
              تسجيل الدخول الآن
            </Button>
            
            <Button 
              onClick={handleRefresh}
              variant="outline"
              className="w-full"
            >
              <RefreshCw className="h-4 w-4 ml-2" />
              تحديث الصفحة
            </Button>
          </div>
          
          <div className="text-center">
            <p className="text-xs text-gray-500">
              إذا كنت تواجه مشاكل متكررة، تواصل مع الدعم التقني
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
