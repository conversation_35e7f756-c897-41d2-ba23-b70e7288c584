@echo off
chcp 65001 >nul
echo ========================================
echo   تشغيل سريع للنظام
echo ========================================
echo.

echo 1. فحص PostgreSQL...
echo.

REM فحص PostgreSQL
node -e "
const { Client } = require('pg');
const client = new Client({
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'mohammidev',
  connectTimeoutMillis: 5000
});

client.connect()
  .then(() => {
    console.log('✅ PostgreSQL متصل');
    return client.query('SELECT COUNT(*) FROM case_movements');
  })
  .then(result => {
    console.log('📊 عدد حركات القضايا:', result.rows[0].count);
    client.end();
  })
  .catch(error => {
    console.log('❌ خطأ PostgreSQL:', error.message);
    client.end();
  });
"

echo.
echo 2. تشغيل الخادم...
echo.

REM تشغيل الخادم
start cmd /k "npm run dev"

echo.
echo 3. انتظار تشغيل الخادم...
timeout /t 10 /nobreak >nul

echo.
echo 4. فتح المتصفح...
start http://localhost:3300/movements

echo.
echo ========================================
echo   تم تشغيل النظام
echo ========================================
echo.
echo الروابط المتاحة:
echo - صفحة حركة القضايا: http://localhost:3300/movements
echo - إعداد قاعدة البيانات: http://localhost:3300/setup-mohammidev
echo - الصفحة الرئيسية: http://localhost:3300
echo.
pause
