import { NextRequest, NextResponse } from 'next/server'
import { Client } from 'pg'

const dbConfig = {
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'mohammidev'
}

// GET - جلب الجلسات
export async function GET(request: NextRequest) {
  const client = new Client(dbConfig)
  
  try {
    await client.connect()
    
    const { searchParams } = new URL(request.url)
    const case_id = searchParams.get('case_id')
    const status = searchParams.get('status')
    const date_from = searchParams.get('date_from')
    const date_to = searchParams.get('date_to')
    const upcoming = searchParams.get('upcoming') === 'true'
    
    let whereConditions = ['1=1']
    let queryParams: any[] = []
    let paramIndex = 1
    
    if (case_id) {
      whereConditions.push(`cs.case_id = $${paramIndex}`)
      queryParams.push(parseInt(case_id))
      paramIndex++
    }
    
    if (status) {
      whereConditions.push(`cs.status = $${paramIndex}`)
      queryParams.push(status)
      paramIndex++
    }
    
    if (date_from) {
      whereConditions.push(`cs.session_date >= $${paramIndex}`)
      queryParams.push(date_from)
      paramIndex++
    }
    
    if (date_to) {
      whereConditions.push(`cs.session_date <= $${paramIndex}`)
      queryParams.push(date_to)
      paramIndex++
    }
    
    if (upcoming) {
      whereConditions.push(`cs.session_date > NOW()`)
    }
    
    const whereClause = whereConditions.join(' AND ')
    
    const sessionsQuery = `
      SELECT 
        cs.*,
        c.case_number,
        c.title as case_title,
        c.priority as case_priority,
        cl.name as client_name,
        cl.phone as client_phone,
        cl.email as client_email,
        e.name as lawyer_name,
        e.phone as lawyer_phone,
        e.email as lawyer_email,
        CASE 
          WHEN cs.session_date BETWEEN NOW() AND NOW() + INTERVAL '1 day' THEN 'urgent'
          WHEN cs.session_date BETWEEN NOW() AND NOW() + INTERVAL '3 days' THEN 'soon'
          WHEN cs.session_date > NOW() THEN 'upcoming'
          ELSE 'past'
        END as urgency_level
      FROM court_sessions cs
      JOIN cases c ON cs.case_id = c.id
      LEFT JOIN clients cl ON c.client_id = cl.id
      LEFT JOIN employees e ON c.assigned_lawyer_id = e.id
      WHERE ${whereClause}
      ORDER BY cs.session_date ASC
    `
    
    const sessionsResult = await client.query(sessionsQuery, queryParams)
    
    // إحصائيات الجلسات
    const statsQuery = `
      SELECT 
        COUNT(*) as total_sessions,
        COUNT(CASE WHEN session_date::date = CURRENT_DATE THEN 1 END) as today_sessions,
        COUNT(CASE WHEN session_date BETWEEN NOW() AND NOW() + INTERVAL '7 days' THEN 1 END) as week_sessions,
        COUNT(CASE WHEN status = 'scheduled' THEN 1 END) as scheduled_sessions,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_sessions,
        COUNT(CASE WHEN status = 'postponed' THEN 1 END) as postponed_sessions,
        COUNT(CASE WHEN client_reminder_sent = false AND session_date > NOW() THEN 1 END) as pending_client_reminders,
        COUNT(CASE WHEN employee_reminder_sent = false AND session_date > NOW() THEN 1 END) as pending_employee_reminders
      FROM court_sessions
    `
    
    const statsResult = await client.query(statsQuery)
    
    return NextResponse.json({
      success: true,
      data: {
        sessions: sessionsResult.rows,
        stats: statsResult.rows[0]
      }
    })
    
  } catch (error) {
    console.error('خطأ في جلب الجلسات:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب الجلسات'
    }, { status: 500 })
  } finally {
    await client.end()
  }
}

// POST - إضافة جلسة جديدة
export async function POST(request: NextRequest) {
  const client = new Client(dbConfig)
  
  try {
    await client.connect()
    
    const body = await request.json()
    const {
      case_id,
      session_date,
      court_name,
      session_type = 'hearing',
      reminder_days = 3,
      notes,
      location,
      judge_name
    } = body
    
    // التحقق من صحة البيانات
    if (!case_id || !session_date) {
      return NextResponse.json({
        success: false,
        error: 'معرف القضية وتاريخ الجلسة مطلوبان'
      }, { status: 400 })
    }
    
    // إدراج الجلسة الجديدة
    const insertQuery = `
      INSERT INTO court_sessions (
        case_id, session_date, court_name, session_type, 
        reminder_days, notes, location, judge_name
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `
    
    const result = await client.query(insertQuery, [
      case_id, session_date, court_name, session_type,
      reminder_days, notes, location, judge_name
    ])
    
    // إضافة حركة للقضية
    await client.query(`
      INSERT INTO case_movements (
        case_id, movement_type, description, movement_date
      ) VALUES ($1, $2, $3, $4)
    `, [
      case_id,
      'session_scheduled',
      `تم جدولة جلسة ${session_type} في ${court_name} بتاريخ ${session_date}`,
      new Date()
    ])
    
    // تحديث تاريخ آخر تحديث للقضية
    await client.query(
      'UPDATE cases SET updated_at = CURRENT_TIMESTAMP WHERE id = $1',
      [case_id]
    )
    
    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم إضافة الجلسة بنجاح'
    })
    
  } catch (error) {
    console.error('خطأ في إضافة الجلسة:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في إضافة الجلسة'
    }, { status: 500 })
  } finally {
    await client.end()
  }
}

// PUT - تحديث جلسة
export async function PUT(request: NextRequest) {
  const client = new Client(dbConfig)
  
  try {
    await client.connect()
    
    const body = await request.json()
    const {
      id,
      session_date,
      court_name,
      session_type,
      status,
      session_result,
      next_session_date,
      notes,
      location,
      judge_name
    } = body
    
    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف الجلسة مطلوب'
      }, { status: 400 })
    }
    
    // بناء استعلام التحديث
    const updateFields = []
    const queryParams = []
    let paramIndex = 1
    
    if (session_date) {
      updateFields.push(`session_date = $${paramIndex}`)
      queryParams.push(session_date)
      paramIndex++
    }
    
    if (court_name) {
      updateFields.push(`court_name = $${paramIndex}`)
      queryParams.push(court_name)
      paramIndex++
    }
    
    if (session_type) {
      updateFields.push(`session_type = $${paramIndex}`)
      queryParams.push(session_type)
      paramIndex++
    }
    
    if (status) {
      updateFields.push(`status = $${paramIndex}`)
      queryParams.push(status)
      paramIndex++
    }
    
    if (session_result) {
      updateFields.push(`session_result = $${paramIndex}`)
      queryParams.push(session_result)
      paramIndex++
    }
    
    if (next_session_date) {
      updateFields.push(`next_session_date = $${paramIndex}`)
      queryParams.push(next_session_date)
      paramIndex++
    }
    
    if (notes) {
      updateFields.push(`notes = $${paramIndex}`)
      queryParams.push(notes)
      paramIndex++
    }
    
    if (location) {
      updateFields.push(`location = $${paramIndex}`)
      queryParams.push(location)
      paramIndex++
    }
    
    if (judge_name) {
      updateFields.push(`judge_name = $${paramIndex}`)
      queryParams.push(judge_name)
      paramIndex++
    }
    
    if (updateFields.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'لا توجد بيانات للتحديث'
      }, { status: 400 })
    }
    
    updateFields.push(`updated_at = CURRENT_TIMESTAMP`)
    queryParams.push(id)
    
    const updateQuery = `
      UPDATE court_sessions 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `
    
    const result = await client.query(updateQuery, queryParams)
    
    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم تحديث الجلسة بنجاح'
    })
    
  } catch (error) {
    console.error('خطأ في تحديث الجلسة:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث الجلسة'
    }, { status: 500 })
  } finally {
    await client.end()
  }
}
