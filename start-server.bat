@echo off
chcp 65001 >nul
echo ========================================
echo   تشغيل خادم Next.js على المنفذ 3300
echo ========================================
echo.

REM فحص المنفذ 3300
echo فحص المنفذ 3300...
netstat -an | findstr :3300 >nul
if %errorlevel% equ 0 (
    echo ⚠️ المنفذ 3300 مستخدم بالفعل
    echo جاري إيقاف العمليات...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3300') do taskkill /f /pid %%a >nul 2>&1
    timeout /t 2 /nobreak >nul
)

REM تحقق من وجود node_modules
if not exist "node_modules" (
    echo 📦 تثبيت المكتبات المطلوبة...
    npm install
    echo.
)

REM تحقق من PostgreSQL
echo 🔍 فحص اتصال PostgreSQL...
node -e "
const { Client } = require('pg');
const client = new Client({
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'mohammidev',
  connectTimeoutMillis: 3000
});

client.connect()
  .then(() => {
    console.log('✅ PostgreSQL متصل بقاعدة mohammidev');
    client.end();
  })
  .catch(error => {
    console.log('❌ خطأ PostgreSQL:', error.message);
    console.log('💡 تأكد من تشغيل PostgreSQL وصحة كلمة المرور');
  });
" 2>nul

echo.
echo 🚀 بدء تشغيل الخادم على المنفذ 3300...
echo.
echo الروابط المتاحة بعد التشغيل:
echo - صفحة حركة القضايا: http://localhost:3300/movements
echo - إعداد قاعدة البيانات: http://localhost:3300/setup-mohammidev
echo - الصفحة الرئيسية: http://localhost:3300
echo.

npm run dev
