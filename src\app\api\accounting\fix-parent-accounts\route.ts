import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {

    const results = []

    // 1. البحث عن الحسابات الأب

    // حساب العملاء الأب
    const clientsParentAccount = await query(`
      SELECT id, account_name, account_code 
      FROM chart_of_accounts 
      WHERE account_name = 'حسابات العملاء' AND linked_table = 'clients'
      LIMIT 1
    `)

    // حساب الموظفين الأب
    const employeesParentAccount = await query(`
      SELECT id, account_name, account_code 
      FROM chart_of_accounts 
      WHERE account_name = 'حسابات الموظفين' AND linked_table = 'employees'
      LIMIT 1
    `)

    // حساب الموردين الأب
    const suppliersParentAccount = await query(`
      SELECT id, account_name, account_code 
      FROM chart_of_accounts 
      WHERE account_name = 'حسابات الموردين' AND linked_table = 'suppliers'
      LIMIT 1
    `)

    let clientsParentId = null
    let employeesParentId = null
    let suppliersParentId = null

    if (clientsParentAccount.rows.length > 0) {
      clientsParentId = clientsParentAccount.rows[0].id
      results.push(`✅ تم العثور على حساب العملاء الأب: ${clientsParentAccount.rows[0].account_name} (${clientsParentAccount.rows[0].account_code})`)
    } else {
      results.push('❌ لم يتم العثور على حساب العملاء الأب')
      return NextResponse.json({
        success: false,
        message: 'لم يتم العثور على حساب العملاء الأب',
        data: { results }
      }, { status: 400 })
    }

    if (employeesParentAccount.rows.length > 0) {
      employeesParentId = employeesParentAccount.rows[0].id
      results.push(`✅ تم العثور على حساب الموظفين الأب: ${employeesParentAccount.rows[0].account_name} (${employeesParentAccount.rows[0].account_code})`)
    } else {
      results.push('❌ لم يتم العثور على حساب الموظفين الأب')
      return NextResponse.json({
        success: false,
        message: 'لم يتم العثور على حساب الموظفين الأب',
        data: { results }
      }, { status: 400 })
    }

    if (suppliersParentAccount.rows.length > 0) {
      suppliersParentId = suppliersParentAccount.rows[0].id
      results.push(`✅ تم العثور على حساب الموردين الأب: ${suppliersParentAccount.rows[0].account_name} (${suppliersParentAccount.rows[0].account_code})`)
    } else {
      results.push('❌ لم يتم العثور على حساب الموردين الأب')
      return NextResponse.json({
        success: false,
        message: 'لم يتم العثور على حساب الموردين الأب',
        data: { results }
      }, { status: 400 })
    }

    // 2. تحديث جدول العملاء

    const updateClients = await query(`
      UPDATE clients 
      SET account_id = $1
      WHERE account_id != $1 OR account_id IS NULL
    `, [clientsParentId])

    results.push(`✅ تم تحديث ${updateClients.rowCount} عميل ليشير للحساب الأب`)

    // 3. تحديث جدول الموظفين

    const updateEmployees = await query(`
      UPDATE employees 
      SET account_id = $1
      WHERE account_id != $1 OR account_id IS NULL
    `, [employeesParentId])

    results.push(`✅ تم تحديث ${updateEmployees.rowCount} موظف ليشير للحساب الأب`)

    // 4. تحديث جدول الموردين

    const updateSuppliers = await query(`
      UPDATE suppliers 
      SET account_id = $1
      WHERE account_id != $1 OR account_id IS NULL
    `, [suppliersParentId])

    results.push(`✅ تم تحديث ${updateSuppliers.rowCount} مورد ليشير للحساب الأب`)

    // 5. اختبار النتائج

    // اختبار العملاء
    const clientsTest = await query(`
      SELECT 
        c.name as client_name,
        c.account_id,
        coa.account_name as parent_account_name,
        coa.account_code as parent_account_code
      FROM clients c
      LEFT JOIN chart_of_accounts coa ON c.account_id = coa.id
      LIMIT 3
    `)

    results.push(`✅ اختبار العملاء: ${clientsTest.rows.length} عميل مربوط بالحساب الأب`)

    // اختبار الموظفين
    const employeesTest = await query(`
      SELECT 
        e.name as employee_name,
        e.account_id,
        coa.account_name as parent_account_name,
        coa.account_code as parent_account_code
      FROM employees e
      LEFT JOIN chart_of_accounts coa ON e.account_id = coa.id
      LIMIT 3
    `)

    results.push(`✅ اختبار الموظفين: ${employeesTest.rows.length} موظف مربوط بالحساب الأب`)

    // اختبار الموردين
    const suppliersTest = await query(`
      SELECT 
        s.name as supplier_name,
        s.account_id,
        coa.account_name as parent_account_name,
        coa.account_code as parent_account_code
      FROM suppliers s
      LEFT JOIN chart_of_accounts coa ON s.account_id = coa.id
      LIMIT 3
    `)

    results.push(`✅ اختبار الموردين: ${suppliersTest.rows.length} مورد مربوط بالحساب الأب`)

    // 6. إحصائيات نهائية
    const stats = {
      clientsParentId,
      employeesParentId,
      suppliersParentId,
      clientsUpdated: updateClients.rowCount,
      employeesUpdated: updateEmployees.rowCount,
      suppliersUpdated: updateSuppliers.rowCount,
      clientsTest: clientsTest.rows,
      employeesTest: employeesTest.rows,
      suppliersTest: suppliersTest.rows
    }

    return NextResponse.json({
      success: true,
      message: 'تم تحديث account_id ليشير للحساب الأب بنجاح',
      data: {
        results,
        stats,
        parentAccounts: {
          clients: clientsParentAccount.rows[0],
          employees: employeesParentAccount.rows[0],
          suppliers: suppliersParentAccount.rows[0]
        }
      }
    })

  } catch (error) {
    console.error('❌ خطأ في تحديث الحسابات الأب:', error)
    return NextResponse.json({
      success: false,
      message: 'فشل في تحديث الحسابات الأب',
      error: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// GET - عرض الحالة الحالية
export async function GET(request: NextRequest) {
  try {
    const results = []

    // فحص العملاء
    const clientsCheck = await query(`
      SELECT 
        c.name,
        c.account_id,
        coa.account_name,
        coa.account_code
      FROM clients c
      LEFT JOIN chart_of_accounts coa ON c.account_id = coa.id
      LIMIT 5
    `)

    // فحص الموظفين
    const employeesCheck = await query(`
      SELECT 
        e.name,
        e.account_id,
        coa.account_name,
        coa.account_code
      FROM employees e
      LEFT JOIN chart_of_accounts coa ON e.account_id = coa.id
      LIMIT 5
    `)

    // فحص الموردين
    const suppliersCheck = await query(`
      SELECT 
        s.name,
        s.account_id,
        coa.account_name,
        coa.account_code
      FROM suppliers s
      LEFT JOIN chart_of_accounts coa ON s.account_id = coa.id
      LIMIT 5
    `)

    return NextResponse.json({
      success: true,
      data: {
        clients: clientsCheck.rows,
        employees: employeesCheck.rows,
        suppliers: suppliersCheck.rows
      },
      message: 'تم جلب الحالة الحالية بنجاح'
    })

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
