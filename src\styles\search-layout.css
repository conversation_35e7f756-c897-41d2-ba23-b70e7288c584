/**
 * تصميم موحد لمربعات البحث في جميع أنحاء النظام
 * يضمن توسيط مربعات البحث وتصميم موحد
 */

/* تصميم مربع البحث المركزي */
.centered-search-container {
  display: flex;
  justify-content: center;
  padding: 1rem;
  background: #ffffff;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
}

.centered-search-box {
  position: relative;
  width: 100%;
  max-width: 24rem; /* w-96 equivalent */
}

.centered-search-input {
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  transition: all 0.2s ease-in-out;
  background-color: #ffffff;
}

.centered-search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.centered-search-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  width: 1rem;
  height: 1rem;
}

/* تصميم العنوان الرئيسي */
.main-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 250px;
}

/* تصميم الهيدر المحسن */
.enhanced-header {
  background: #ffffff;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid #e5e7eb;
  padding: 1rem 1.5rem;
}

.header-layout {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
}

.header-title-section {
  flex-shrink: 0;
  min-width: 200px;
}

.header-search-section {
  flex: 1;
  display: flex;
  justify-content: center;
  padding: 0 2rem;
}

.header-actions-section {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* تصميم مربع البحث في الهيدر */
.header-search-container {
  position: relative;
  width: 100%;
  max-width: 20rem; /* w-80 equivalent */
}

.header-search-input {
  width: 100%;
  padding: 0.5rem 2.5rem 0.5rem 2rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  background-color: #ffffff;
  transition: all 0.2s ease-in-out;
}

.header-search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* تصميم مربعات البحث في الصفحات */
.page-search-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.page-search-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}

.page-search-container {
  position: relative;
  width: 100%;
  max-width: 24rem;
}

.page-search-input {
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  background-color: #ffffff;
  transition: all 0.2s ease-in-out;
}

.page-search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.page-search-input::placeholder {
  color: #9ca3af;
  font-style: italic;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
  .header-layout {
    flex-direction: column;
    gap: 1rem;
  }
  
  .header-search-section {
    padding: 0;
    width: 100%;
  }
  
  .header-search-container {
    max-width: 100%;
  }
  
  .main-title {
    font-size: 1.125rem;
    max-width: none;
  }
  
  .page-search-container {
    max-width: 100%;
  }
}

@media (max-width: 640px) {
  .enhanced-header {
    padding: 0.75rem 1rem;
  }
  
  .header-actions-section {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .main-title {
    font-size: 1rem;
  }
}

/* تحسينات إضافية للبحث */
.search-results-container {
  position: absolute;
  top: 100%;
  right: 0;
  left: 0;
  margin-top: 0.5rem;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  z-index: 50;
  max-height: 24rem;
  overflow-y: auto;
}

.search-result-item {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
}

.search-result-item:hover {
  background-color: #f9fafb;
}

.search-result-item:last-child {
  border-bottom: none;
}

/* تصميم أيقونات البحث */
.search-icon {
  color: #6b7280;
  width: 1rem;
  height: 1rem;
}

.clear-search-icon {
  color: #9ca3af;
  width: 1rem;
  height: 1rem;
  cursor: pointer;
  transition: color 0.2s ease-in-out;
}

.clear-search-icon:hover {
  color: #6b7280;
}

/* تصميم خاص للصفحات المختلفة */
.accounting-search {
  border-color: #10b981;
}

.accounting-search:focus {
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.legal-search {
  border-color: #8b5cf6;
}

.legal-search:focus {
  border-color: #8b5cf6;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.documents-search {
  border-color: #f59e0b;
}

.documents-search:focus {
  border-color: #f59e0b;
  box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
}

/* تحسينات الأداء */
.search-input-optimized {
  will-change: border-color, box-shadow;
}

/* تصميم للطباعة */
@media print {
  .search-container,
  .search-card,
  .search-input {
    display: none !important;
  }
}
