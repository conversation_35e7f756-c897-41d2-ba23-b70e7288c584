// فحص بنية الجداول الأساسية
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function checkTableStructure() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔄 جاري الاتصال بقاعدة البيانات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    const tables = ['users', 'employees', 'clients', 'issues'];

    for (const tableName of tables) {
      console.log(`\n🔍 فحص بنية جدول ${tableName}:`);
      
      try {
        const columns = await client.query(`
          SELECT column_name, data_type, is_nullable, column_default
          FROM information_schema.columns 
          WHERE table_name = $1 AND table_schema = 'public'
          ORDER BY ordinal_position
        `, [tableName]);
        
        if (columns.rows.length > 0) {
          console.log('📋 الأعمدة الموجودة:');
          columns.rows.forEach(col => {
            console.log(`   - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? '(مطلوب)' : '(اختياري)'}`);
          });
        } else {
          console.log('❌ الجدول غير موجود');
        }
      } catch (error) {
        console.log(`❌ خطأ في فحص الجدول: ${error.message}`);
      }
    }

    console.log('\n🔄 جاري إضافة بيانات تجريبية بسيطة...');

    // إضافة مستخدم واحد بالأعمدة الموجودة فقط
    try {
      const userResult = await client.query(`
        INSERT INTO users (username, password_hash)
        SELECT 'admin', '$2b$10$example'
        WHERE NOT EXISTS (SELECT 1 FROM users WHERE username = 'admin')
        RETURNING id, username
      `);
      if (userResult.rows.length > 0) {
        console.log(`✅ تم إدراج مستخدم: ${userResult.rows[0].username}`);
      }
    } catch (error) {
      console.log(`⚠️ خطأ في إدراج المستخدم: ${error.message}`);
    }

    // إضافة موظف واحد بالأعمدة الموجودة فقط
    try {
      const empResult = await client.query(`
        INSERT INTO employees (name, position, phone)
        SELECT 'أحمد محمد', 'محامي', '777123456'
        WHERE NOT EXISTS (SELECT 1 FROM employees WHERE phone = '777123456')
        RETURNING id, name
      `);
      if (empResult.rows.length > 0) {
        console.log(`✅ تم إدراج موظف: ${empResult.rows[0].name}`);
      }
    } catch (error) {
      console.log(`⚠️ خطأ في إدراج الموظف: ${error.message}`);
    }

    // إضافة موكل واحد بالأعمدة الموجودة فقط
    try {
      const clientResult = await client.query(`
        INSERT INTO clients (name, phone, email)
        SELECT 'شركة الأمل', '777111222', '<EMAIL>'
        WHERE NOT EXISTS (SELECT 1 FROM clients WHERE phone = '777111222')
        RETURNING id, name
      `);
      if (clientResult.rows.length > 0) {
        console.log(`✅ تم إدراج موكل: ${clientResult.rows[0].name}`);
      }
    } catch (error) {
      console.log(`⚠️ خطأ في إدراج الموكل: ${error.message}`);
    }

    // إضافة قضية واحدة بالأعمدة الموجودة فقط
    try {
      const issueResult = await client.query(`
        INSERT INTO issues (title, description, status)
        SELECT 'قضية تجريبية', 'وصف القضية التجريبية', 'active'
        WHERE NOT EXISTS (SELECT 1 FROM issues WHERE title = 'قضية تجريبية')
        RETURNING id, title
      `);
      if (issueResult.rows.length > 0) {
        console.log(`✅ تم إدراج قضية: ${issueResult.rows[0].title}`);
      }
    } catch (error) {
      console.log(`⚠️ خطأ في إدراج القضية: ${error.message}`);
    }

    // التحقق من النتائج النهائية
    console.log('\n📊 ملخص البيانات النهائي:');
    const results = await Promise.all([
      client.query('SELECT COUNT(*) FROM users'),
      client.query('SELECT COUNT(*) FROM employees'),
      client.query('SELECT COUNT(*) FROM clients'),
      client.query('SELECT COUNT(*) FROM issues')
    ]);

    console.log(`   - المستخدمين: ${results[0].rows[0].count} سجل`);
    console.log(`   - الموظفين: ${results[1].rows[0].count} سجل`);
    console.log(`   - الموكلين: ${results[2].rows[0].count} سجل`);
    console.log(`   - القضايا: ${results[3].rows[0].count} سجل`);

    console.log('✅ تم فحص وإضافة البيانات بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في فحص الجداول:', error.message);
  } finally {
    await client.end();
    console.log('🔄 تم قطع الاتصال بقاعدة البيانات');
  }
}

checkTableStructure();
