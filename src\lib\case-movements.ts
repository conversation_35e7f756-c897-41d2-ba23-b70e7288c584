import { query } from '@/lib/db'

export interface CaseMovementData {
  case_id: number
  movement_type: string
  description: string
  details?: any
  hearing_date?: string
  hearing_time?: string
  court_name?: string
  created_by?: number
  created_by_name?: string
  priority?: 'low' | 'normal' | 'high' | 'urgent'
  reference_id?: number
  reference_type?: string
  notes?: string
}

// أنواع الحركات المختلفة
export const MOVEMENT_TYPES = {
  CASE_CREATED: 'case_created',
  CASE_ASSIGNED: 'case_assigned',
  CASE_STATUS_CHANGED: 'case_status_changed',
  HEARING_SCHEDULED: 'hearing_scheduled',
  HEARING_POSTPONED: 'hearing_postponed',
  HEARING_COMPLETED: 'hearing_completed',
  FOLLOW_ADDED: 'follow_added',
  DOCUMENT_UPLOADED: 'document_uploaded',
  DOCUMENT_UPDATED: 'document_updated',
  PAYMENT_RECEIVED: 'payment_received',
  CASE_CLOSED: 'case_closed',
  CASE_REOPENED: 'case_reopened',
  CLIENT_CONTACTED: 'client_contacted',
  COURT_DECISION: 'court_decision',
  APPEAL_FILED: 'appeal_filed',
  SETTLEMENT_REACHED: 'settlement_reached'
} as const

// دالة لإضافة حركة جديدة
export async function addCaseMovement(data: CaseMovementData): Promise<number | null> {
  try {
    const result = await query(`
      SELECT add_case_movement(
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12
      ) as movement_id
    `, [
      data.case_id,
      data.movement_type,
      data.description,
      data.details ? JSON.stringify(data.details) : null,
      data.hearing_date || null,
      data.hearing_time || null,
      data.court_name || null,
      data.created_by || null,
      data.created_by_name || 'النظام',
      data.priority || 'normal',
      data.reference_id || null,
      data.reference_type || null
    ])

    const movementId = result.rows[0]?.movement_id

    // إضافة الملاحظات إذا وجدت
    if (data.notes && movementId) {
      await query(`
        UPDATE case_movements 
        SET notes = $1 
        WHERE id = $2
      `, [data.notes, movementId])
    }

    return movementId
  } catch (error) {
    console.error('خطأ في إضافة حركة القضية:', error)
    return null
  }
}

// دالة لإضافة حركة إنشاء قضية
export async function addCaseCreatedMovement(caseId: number, createdBy?: number, createdByName?: string) {
  return addCaseMovement({
    case_id: caseId,
    movement_type: MOVEMENT_TYPES.CASE_CREATED,
    description: 'تم إنشاء القضية في النظام',
    created_by: createdBy,
    created_by_name: createdByName || 'النظام',
    priority: 'normal'
  })
}

// دالة لإضافة حركة توزيع قضية
export async function addCaseAssignedMovement(
  caseId: number, 
  assignedTo: string, 
  createdBy?: number, 
  createdByName?: string
) {
  return addCaseMovement({
    case_id: caseId,
    movement_type: MOVEMENT_TYPES.CASE_ASSIGNED,
    description: `تم توزيع القضية على: ${assignedTo}`,
    details: { assigned_to: assignedTo },
    created_by: createdBy,
    created_by_name: createdByName || 'النظام',
    priority: 'normal'
  })
}

// دالة لإضافة حركة جدولة جلسة
export async function addHearingScheduledMovement(
  caseId: number,
  hearingDate: string,
  hearingTime: string,
  courtName: string,
  createdBy?: number,
  createdByName?: string
) {
  return addCaseMovement({
    case_id: caseId,
    movement_type: MOVEMENT_TYPES.HEARING_SCHEDULED,
    description: `تم تحديد جلسة للقضية في ${courtName}`,
    hearing_date: hearingDate,
    hearing_time: hearingTime,
    court_name: courtName,
    created_by: createdBy,
    created_by_name: createdByName || 'النظام',
    priority: 'high'
  })
}

// دالة لإضافة حركة إضافة متابعة
export async function addFollowAddedMovement(
  caseId: number,
  followDescription: string,
  createdBy?: number,
  createdByName?: string,
  referenceId?: number
) {
  return addCaseMovement({
    case_id: caseId,
    movement_type: MOVEMENT_TYPES.FOLLOW_ADDED,
    description: `تم إضافة متابعة جديدة: ${followDescription.substring(0, 100)}...`,
    details: { follow_description: followDescription },
    created_by: createdBy,
    created_by_name: createdByName || 'النظام',
    priority: 'normal',
    reference_id: referenceId,
    reference_type: 'follow'
  })
}

// دالة لإضافة حركة رفع وثيقة
export async function addDocumentUploadedMovement(
  caseId: number,
  documentTitle: string,
  createdBy?: number,
  createdByName?: string,
  referenceId?: number
) {
  return addCaseMovement({
    case_id: caseId,
    movement_type: MOVEMENT_TYPES.DOCUMENT_UPLOADED,
    description: `تم رفع وثيقة جديدة: ${documentTitle}`,
    details: { document_title: documentTitle },
    created_by: createdBy,
    created_by_name: createdByName || 'النظام',
    priority: 'normal',
    reference_id: referenceId,
    reference_type: 'document'
  })
}

// دالة لإضافة حركة تغيير حالة القضية
export async function addCaseStatusChangedMovement(
  caseId: number,
  oldStatus: string,
  newStatus: string,
  createdBy?: number,
  createdByName?: string
) {
  return addCaseMovement({
    case_id: caseId,
    movement_type: MOVEMENT_TYPES.CASE_STATUS_CHANGED,
    description: `تم تغيير حالة القضية من "${oldStatus}" إلى "${newStatus}"`,
    details: { old_status: oldStatus, new_status: newStatus },
    created_by: createdBy,
    created_by_name: createdByName || 'النظام',
    priority: 'normal'
  })
}

// دالة للحصول على الحركات القادمة (الجلسات)
export async function getUpcomingHearings(limit: number = 10) {
  try {
    const result = await query(`
      SELECT 
        cm.*,
        i.client_name,
        i.case_number,
        i.title as case_title,
        EXTRACT(DAYS FROM (cm.hearing_date - CURRENT_DATE)) as days_until_hearing
      FROM case_movements cm
      LEFT JOIN issues i ON cm.case_id = i.id
      WHERE cm.hearing_date >= CURRENT_DATE 
        AND cm.movement_type = $1
        AND cm.status = 'active'
      ORDER BY cm.hearing_date ASC, cm.hearing_time ASC
      LIMIT $2
    `, [MOVEMENT_TYPES.HEARING_SCHEDULED, limit])

    return result.rows
  } catch (error) {
    console.error('خطأ في جلب الجلسات القادمة:', error)
    return []
  }
}

// دالة للحصول على القضايا التي لم تحدث بها حركة لفترة معينة
export async function getInactiveCases(daysSinceLastMovement: number = 30) {
  try {
    const result = await query(`
      SELECT 
        i.id,
        i.case_number,
        i.title,
        i.client_name,
        i.status,
        MAX(cm.created_at) as last_movement_date,
        EXTRACT(DAYS FROM (CURRENT_DATE - MAX(cm.created_at)::date)) as days_since_last_movement
      FROM issues i
      LEFT JOIN case_movements cm ON i.id = cm.case_id
      WHERE i.status NOT IN ('closed', 'completed', 'cancelled')
      GROUP BY i.id, i.case_number, i.title, i.client_name, i.status
      HAVING MAX(cm.created_at) < CURRENT_DATE - INTERVAL '$1 days'
        OR MAX(cm.created_at) IS NULL
      ORDER BY days_since_last_movement DESC NULLS FIRST
    `, [daysSinceLastMovement])

    return result.rows
  } catch (error) {
    console.error('خطأ في جلب القضايا غير النشطة:', error)
    return []
  }
}
