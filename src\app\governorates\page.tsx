'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  MapPin,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  X,
  Save
} from 'lucide-react'

interface Governorate {
  id: number
  name: string
  code: string
  is_active: boolean
  created_date: string
}

export default function GovernoratesPage() {
  const [governorates, setGovernorates] = useState<Governorate[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalType, setModalType] = useState<'add' | 'edit' | 'view'>('add')
  const [editingGovernorate, setEditingGovernorate] = useState<Governorate | null>(null)
  const [dbError, setDbError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    is_active: true
  })

  const fetchGovernorates = async () => {
    setIsLoading(true)
    setDbError(null)

    try {
      const response = await fetch('/api/governorates')
      const result = await response.json()

      if (result.success) {
        setGovernorates(result.data)
      } else {
        setDbError(result.error || 'فشل في جلب بيانات المحافظات')
        setGovernorates([])
      }
    } catch (error) {
      console.error('Network error:', error)
      setDbError('فشل في الاتصال بقاعدة البيانات')
      setGovernorates([])
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchGovernorates()
  }, [])

  const filteredGovernorates = governorates.filter(governorate =>
    (governorate.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (governorate.code || '').toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleDelete = async (id: number) => {
    if (confirm('هل أنت متأكد من حذف هذه المحافظة؟')) {
      try {
        const response = await fetch(`/api/governorates?id=${id}`, {
          method: 'DELETE'
        })

        const result = await response.json()

        if (result.success) {
          alert('تم حذف المحافظة بنجاح')
          fetchGovernorates()
        } else {
          alert(result.error || 'فشل في حذف المحافظة')
        }
      } catch (error) {
        console.error('Error deleting governorate:', error)
        alert('حدث خطأ في الاتصال')
      }
    }
  }

  const handleEdit = (governorate: Governorate) => {
    setEditingGovernorate(governorate)
    setFormData({
      name: governorate.name,
      code: governorate.code,
      is_active: governorate.is_active
    })
    setModalType('edit')
    setIsModalOpen(true)
  }

  const handleView = (governorate: Governorate) => {
    setEditingGovernorate(governorate)
    setModalType('view')
    setIsModalOpen(true)
  }

  const handleAddNew = () => {
    setEditingGovernorate(null)
    setFormData({
      name: '',
      code: '',
      is_active: true
    })
    setModalType('add')
    setIsModalOpen(true)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      if (modalType === 'add') {
        const response = await fetch('/api/governorates', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(formData)
        })

        const result = await response.json()

        if (result.success) {
          alert('تم إضافة المحافظة بنجاح')
          fetchGovernorates()
        } else {
          alert(result.error || 'فشل في إضافة المحافظة')
          return
        }
      } else if (modalType === 'edit' && editingGovernorate) {
        const response = await fetch('/api/governorates', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ ...formData, id: editingGovernorate.id })
        })

        const result = await response.json()

        if (result.success) {
          alert('تم تحديث بيانات المحافظة بنجاح')
          fetchGovernorates()
        } else {
          alert(result.error || 'فشل في تحديث بيانات المحافظة')
          return
        }
      }

      setIsModalOpen(false)
      setEditingGovernorate(null)
    } catch (error) {
      console.error('Error submitting form:', error)
      alert('حدث خطأ في الاتصال')
    }
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <MapPin className="h-8 w-8 mr-3 text-blue-600" />
              إدارة المحافظات
            </h1>
            <p className="text-gray-600 mt-1">إدارة ومتابعة جميع المحافظات</p>
          </div>

          <Button onClick={handleAddNew} className="bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            إضافة محافظة جديدة
          </Button>
        </div>

        <Card>
          <CardContent className="p-4">
            <div className="flex justify-center">
              <div className="relative w-96">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في المحافظات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10 w-full"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* عرض رسالة الخطأ */}
        {dbError && (
          <Card>
            <CardContent className="p-4">
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="mr-3">
                    <h3 className="text-sm font-medium text-red-800">خطأ في الاتصال بقاعدة البيانات</h3>
                    <div className="mt-2 text-sm text-red-700">
                      <p>{dbError}</p>
                    </div>
                  </div>
                </div>
                <div className="mt-4">
                  <Button onClick={fetchGovernorates} variant="outline" size="sm" className="bg-white hover:bg-gray-50">
                    إعادة المحاولة
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <MapPin className="h-5 w-5 mr-2" />
              قائمة المحافظات ({filteredGovernorates.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {!dbError && !isLoading && (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="text-right p-3 font-semibold">اسم المحافظة</th>
                      <th className="text-right p-3 font-semibold">الرمز</th>
                      <th className="text-center p-3 font-semibold">الحالة</th>
                      <th className="text-center p-3 font-semibold">تاريخ الإنشاء</th>
                      <th className="text-center p-3 font-semibold">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredGovernorates.map((governorate) => (
                      <tr key={governorate.id} className="border-b hover:bg-gray-50">
                        <td className="p-3 font-medium">{governorate.name}</td>
                        <td className="p-3">{governorate.code}</td>
                        <td className="text-center p-3">
                          <Badge className={governorate.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                            {governorate.is_active ? 'نشط' : 'غير نشط'}
                          </Badge>
                        </td>
                        <td className="text-center p-3">{governorate.created_date}</td>
                        <td className="text-center p-3">
                          <div className="flex justify-center space-x-2 space-x-reverse">
                            <Button size="sm" variant="outline" onClick={() => handleView(governorate)}>
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline" onClick={() => handleEdit(governorate)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDelete(governorate.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}

            {!dbError && !isLoading && governorates.length === 0 && (
              <div className="text-center py-8">
                <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد محافظات</h3>
                <p className="text-gray-600">لم يتم العثور على أي محافظات في قاعدة البيانات</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Modal للإضافة/التعديل/المشاهدة */}
        {isModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">
                  {modalType === 'add' && 'إضافة محافظة جديدة'}
                  {modalType === 'edit' && 'تعديل بيانات المحافظة'}
                  {modalType === 'view' && 'عرض بيانات المحافظة'}
                </h3>
                <Button variant="ghost" size="sm" onClick={() => setIsModalOpen(false)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>

              {modalType === 'view' && editingGovernorate ? (
                <div className="space-y-4">
                  <div>
                    <Label>اسم المحافظة</Label>
                    <p className="mt-1 p-2 bg-gray-50 rounded">{editingGovernorate.name}</p>
                  </div>
                  <div>
                    <Label>الرمز</Label>
                    <p className="mt-1 p-2 bg-gray-50 rounded">{editingGovernorate.code}</p>
                  </div>
                  <div>
                    <Label>الحالة</Label>
                    <p className="mt-1 p-2 bg-gray-50 rounded">{editingGovernorate.is_active ? 'نشط' : 'غير نشط'}</p>
                  </div>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <Label htmlFor="name">اسم المحافظة *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({...formData, name: e.target.value})}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="code">الرمز</Label>
                    <Input
                      id="code"
                      value={formData.code}
                      onChange={(e) => setFormData({...formData, code: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="is_active">الحالة</Label>
                    <select
                      id="is_active"
                      value={formData.is_active.toString()}
                      onChange={(e) => setFormData({...formData, is_active: e.target.value === 'true'})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    >
                      <option value="true">نشط</option>
                      <option value="false">غير نشط</option>
                    </select>
                  </div>

                  <div className="flex space-x-3 space-x-reverse">
                    <Button type="submit" className="flex-1">
                      <Save className="h-4 w-4 mr-2" />
                      {modalType === 'add' ? 'إضافة' : 'تحديث'}
                    </Button>
                    <Button type="button" variant="outline" onClick={() => setIsModalOpen(false)} className="flex-1">
                      إلغاء
                    </Button>
                  </div>
                </form>
              )}
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}
