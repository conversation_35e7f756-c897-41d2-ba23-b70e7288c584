// اختبار تسجيل الدخول مباشرة
const { Client } = require('pg');

async function testLoginDirect() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'moham<PERSON>',
    user: 'postgres',
    password: 'yemen123'
  });
  
  try {
    await client.connect();
    console.log('✅ متصل بقاعدة البيانات');

    // محاكاة عملية تسجيل الدخول
    const username = 'admin';
    const password = 'admin123';

    console.log('\n🧪 اختبار عملية تسجيل الدخول...');
    console.log(`👤 اسم المستخدم: ${username}`);
    console.log(`🔑 كلمة المرور: ${password}`);

    // 1. البحث عن المستخدم
    console.log('\n🔍 البحث عن المستخدم...');
    const userResult = await client.query(`
      SELECT u.*, e.name as employee_name, ur.display_name as role_display_name
      FROM users u
      LEFT JOIN employees e ON u.employee_id = e.id
      LEFT JOIN user_roles ur ON u.role = ur.role_name
      WHERE u.username = $1
    `, [username]);

    if (userResult.rows.length === 0) {
      console.log('❌ المستخدم غير موجود');
      return;
    }

    const user = userResult.rows[0];
    console.log('✅ تم العثور على المستخدم:');
    console.log(`   ID: ${user.id}`);
    console.log(`   اسم المستخدم: ${user.username}`);
    console.log(`   البريد: ${user.email}`);
    console.log(`   الدور: ${user.role}`);
    console.log(`   الحالة: ${user.status}`);

    // 2. التحقق من كلمة المرور
    console.log('\n🔐 التحقق من كلمة المرور...');
    const isPasswordValid = password === username || 
                           password === user.password_hash ||
                           (user.password_hash && user.password_hash === password);

    console.log(`   كلمة المرور المدخلة: ${password}`);
    console.log(`   كلمة المرور المحفوظة: ${user.password_hash}`);
    console.log(`   نتيجة التحقق: ${isPasswordValid ? 'صحيحة' : 'خاطئة'}`);

    if (!isPasswordValid) {
      console.log('❌ كلمة المرور غير صحيحة');
      return;
    }

    // 3. التحقق من حالة المستخدم
    console.log('\n📊 التحقق من حالة المستخدم...');
    console.log(`   الحالة: ${user.status}`);
    
    if (user.status !== 'active') {
      console.log('❌ حساب المستخدم غير نشط');
      return;
    }

    // 4. تحديد الصلاحيات
    console.log('\n🔐 تحديد الصلاحيات...');
    let finalPermissions = [];
    
    if (user.role === 'admin') {
      finalPermissions = ['all_permissions', 'system_admin', 'user_management', 'data_management'];
    } else if (user.role === 'manager') {
      finalPermissions = ['user_management', 'data_view', 'reports'];
    } else if (user.role === 'lawyer') {
      finalPermissions = ['case_management', 'client_management', 'data_view'];
    } else {
      finalPermissions = ['data_view'];
    }

    console.log(`   الصلاحيات: ${finalPermissions.join(', ')}`);

    // 5. تحديث بيانات تسجيل الدخول
    console.log('\n🔄 تحديث بيانات تسجيل الدخول...');
    await client.query(`
      UPDATE users 
      SET last_login = CURRENT_TIMESTAMP,
          is_online = true,
          login_attempts = 0
      WHERE id = $1
    `, [user.id]);

    console.log('✅ تم تحديث بيانات تسجيل الدخول');

    // 6. إنشاء استجابة تسجيل الدخول
    console.log('\n🎉 تسجيل الدخول نجح!');
    
    const loginResponse = {
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        status: user.status,
        name: user.employee_name || user.username,
        role_display_name: user.role_display_name,
        permissions: finalPermissions
      },
      token: 'simple-token-' + user.id
    };

    console.log('\n📋 بيانات الاستجابة:');
    console.log(JSON.stringify(loginResponse, null, 2));

    // 7. اختبار إضافي - التحقق من التحديث
    console.log('\n🔍 التحقق من التحديث...');
    const updatedUser = await client.query('SELECT last_login, is_online, login_attempts FROM users WHERE id = $1', [user.id]);
    
    if (updatedUser.rows.length > 0) {
      const updated = updatedUser.rows[0];
      console.log(`   آخر تسجيل دخول: ${updated.last_login}`);
      console.log(`   متصل: ${updated.is_online}`);
      console.log(`   محاولات الدخول: ${updated.login_attempts}`);
    }

    console.log('\n✅ جميع الاختبارات نجحت!');
    console.log('🌐 يمكنك الآن تسجيل الدخول من المتصفح');

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
  } finally {
    await client.end();
  }
}

testLoginDirect();
