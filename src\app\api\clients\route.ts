import { NextRequest, NextResponse } from 'next/server'
import { query, getDatabaseName } from '@/lib/database-router'

// GET - جلب جميع الموكلين من قاعدة البيانات
export async function GET() {
  try {

    const result = await query('SELECT * FROM clients ORDER BY id')

    return NextResponse.json({
      success: true,
      // المفتاح المتوقع من واجهة الصفحة
      clients: result.rows,
      // الإبقاء على المفتاح القديم للتوافق
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching الموكلين:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'فشل في جلب بيانات الموكلين',
        message: 'تأكد من وجود الجدول في قاعدة البيانات'
      },
      { status: 500 }
    )
  }
}

// POST - إضافة الموكلين جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    const { name, phone, email, address, id_number } = body;

    if (!name) {
      return NextResponse.json(
        { success: false, error: 'اسم العميل مطلوب' },
        { status: 400 }
      );
    }

    const result = await query(`
      INSERT INTO clients (name, phone, email, address, id_number, status, created_date)
      VALUES ($1, $2, $3, $4, $5, 'active', CURRENT_DATE)
      RETURNING *
    `, [name, phone, email, address, id_number]);

    return NextResponse.json({
      success: true,
      message: 'تم إضافة العميل بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('خطأ في إضافة العميل:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة الموكلين' },
      { status: 500 }
    )
  }
}

// PUT - تحديث الموكلين
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()

    // هنا يجب إضافة منطق التحديث حسب كل جدول
    // سيتم تحديثه لاحقاً حسب هيكل كل جدول

    return NextResponse.json({
      success: true,
      message: 'تم تحديث الموكلين بنجاح'
    })
  } catch (error) {
    console.error('Error updating الموكلين:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الموكلين' },
      { status: 500 }
    )
  }
}

// DELETE - حذف الموكلين
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الموكلين مطلوب' },
        { status: 400 }
      )
    }

    await query('DELETE FROM clients WHERE id = $1', [id])

    return NextResponse.json({
      success: true,
      message: 'تم حذف الموكلين بنجاح'
    })
  } catch (error) {
    console.error('Error deleting الموكلين:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الموكلين' },
      { status: 500 }
    )
  }
}