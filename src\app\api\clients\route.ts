import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب جميع الموكلين من قاعدة البيانات
export async function GET() {
  try {
    const result = await query(`
      SELECT
        c.*,
        asl.sub_account_code,
        coa.account_code AS parent_account_code,
        coa.account_name AS parent_account_name
      FROM clients c
      LEFT JOIN account_sub_links asl
        ON asl.linked_table = 'clients' AND asl.linked_record_id = c.id
      LEFT JOIN chart_of_accounts coa
        ON coa.id = asl.main_account_id
      ORDER BY c.id
    `)

    return NextResponse.json({
      success: true,
      // المفتاح المتوقع من واجهة الصفحة
      clients: result.rows,
      // الإبقاء على المفتاح القديم للتوافق
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching الموكلين:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'فشل في جلب بيانات الموكلين',
        message: 'تأكد من وجود الجدول في قاعدة البيانات'
      },
      { status: 500 }
    )
  }
}

// POST - إضافة الموكلين جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    const { name, phone, email, address, id_number } = body;

    if (!name) {
      return NextResponse.json(
        { success: false, error: 'اسم العميل مطلوب' },
        { status: 400 }
      );
    }

    // تحقق من رقم الهوية: 11 رقم
    if (!id_number || !/^\d{11}$/.test(String(id_number))) {
      return NextResponse.json(
        { success: false, error: 'رقم الهوية يجب أن يتكون من 11 رقمًا' },
        { status: 400 }
      )
    }

    // الحصول على الحساب الأب للعملاء من دليل الحسابات
    const parentAccountResult = await query(`
      SELECT id FROM chart_of_accounts
      WHERE account_code = '1121' OR account_name LIKE '%عملاء%'
      LIMIT 1
    `)

    const parentAccountId = parentAccountResult.rows.length > 0 ? parentAccountResult.rows[0].id : null

    const result = await query(`
      INSERT INTO clients (name, phone, email, address, id_number, account_id, status, created_date)
      VALUES ($1, $2, $3, $4, $5, $6, 'active', CURRENT_DATE)
      RETURNING *
    `, [name, phone, email, address, id_number, parentAccountId]);

    return NextResponse.json({
      success: true,
      message: 'تم إضافة العميل بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('خطأ في إضافة العميل:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة الموكلين' },
      { status: 500 }
    )
  }
}

// PUT - تحديث الموكلين
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, name, phone, email, address, id_number, status } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف العميل مطلوب' },
        { status: 400 }
      )
    }

    if (!name) {
      return NextResponse.json(
        { success: false, error: 'اسم العميل مطلوب' },
        { status: 400 }
      )
    }

    // تحقق من رقم الهوية: 11 رقم
    if (id_number && !/^\d{11}$/.test(String(id_number))) {
      return NextResponse.json(
        { success: false, error: 'رقم الهوية يجب أن يتكون من 11 رقمًا' },
        { status: 400 }
      )
    }

    // الحصول على الحساب الأب للعملاء من دليل الحسابات
    const parentAccountResult = await query(`
      SELECT id FROM chart_of_accounts
      WHERE account_code = '1121' OR account_name LIKE '%عملاء%'
      LIMIT 1
    `)

    const parentAccountId = parentAccountResult.rows.length > 0 ? parentAccountResult.rows[0].id : null

    const result = await query(`
      UPDATE clients SET
        name = $2,
        phone = $3,
        email = $4,
        address = $5,
        id_number = $6,
        account_id = $7,
        status = $8,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `, [
      Number(id),
      name,
      phone || null,
      email || null,
      address || null,
      id_number || null,
      parentAccountId,
      status || 'active'
    ])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'العميل غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم تحديث العميل بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error updating العميل:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث العميل' },
      { status: 500 }
    )
  }
}

// DELETE - حذف الموكلين
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الموكلين مطلوب' },
        { status: 400 }
      )
    }

    await query('DELETE FROM clients WHERE id = $1', [id])

    return NextResponse.json({
      success: true,
      message: 'تم حذف الموكلين بنجاح'
    })
  } catch (error) {
    console.error('Error deleting الموكلين:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الموكلين' },
      { status: 500 }
    )
  }
}