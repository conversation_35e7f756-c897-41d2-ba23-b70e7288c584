import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database-router'

// جلب سند قبض محدد
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = parseInt(params.id, 10)
    if (Number.isNaN(id)) {
      return NextResponse.json({ success: false, error: 'معرّف غير صالح' }, { status: 400 })
    }

    // جلب القيد الرئيسي
    const entryRes = await query(
      `SELECT * FROM journal_entries WHERE id = $1 AND entry_type = 'receipt'`,
      [id]
    )
    if (entryRes.rowCount === 0) {
      return NextResponse.json({ success: false, error: 'السند غير موجود' }, { status: 404 })
    }

    // جلب التفاصيل
    const detailsRes = await query(
      `SELECT * FROM journal_entry_details WHERE journal_entry_id = $1 ORDER BY line_order`,
      [id]
    )

    // تحديد السطور المدين/الدائن
    const debitDetail = detailsRes.rows.find((d: any) => parseFloat(d.debit_amount) > 0)
    const creditDetail = detailsRes.rows.find((d: any) => parseFloat(d.credit_amount) > 0)

    const row = entryRes.rows[0]
    const voucher = {
      id: row.id,
      entry_number: row.entry_number,
      entry_date: row.entry_date,
      payer_name: row.party_name,
      payer_type: row.party_type,
      amount: parseFloat(row.total_debit || 0),
      description: row.description,
      reference_number: row.reference_number,
      status: row.status,
      debit_account_id: debitDetail?.account_id,
      credit_account_id: creditDetail?.account_id,
      debit_account_name: debitDetail?.account_name,
      credit_account_name: creditDetail?.account_name,
      // الحقول الإضافية لواجهة التعديل والطباعة
      currency_id: row.currency_id || debitDetail?.currency_id || creditDetail?.currency_id || null,
      payment_method_id: debitDetail?.payment_method_id || creditDetail?.payment_method_id || null,
      cost_center_id: row.cost_center_id || debitDetail?.cost_center_id || creditDetail?.cost_center_id || null,
      case_id: debitDetail?.case_id || creditDetail?.case_id || null,
      case_number: debitDetail?.case_number || creditDetail?.case_number || null,
      service_id: row.service_id || null,
      details: detailsRes.rows,
    }

    return NextResponse.json({ success: true, voucher })
  } catch (error) {
    console.error('❌ GET /receipt-vouchers/[id] error:', error)
    return NextResponse.json({ success: false, error: 'فشل في جلب السند' }, { status: 500 })
  }
}

// تحديث سند قبض
export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = parseInt(params.id, 10)
    if (Number.isNaN(id)) {
      return NextResponse.json({ success: false, error: 'معرّف غير صالح' }, { status: 400 })
    }

    const body = await request.json()
    const {
      entry_date,
      beneficiary_name,
      beneficiary_type,
      payer_name,
      payer_type,
      debit_account_id,
      credit_account_id,
      amount,
      description,
      reference_number,
    } = body

    const finalPayerName = payer_name || beneficiary_name
    const finalPayerType = beneficiary_type || payer_type || 'external'

    if (!entry_date || !finalPayerName || !debit_account_id || !credit_account_id || !amount) {
      return NextResponse.json(
        {
          success: false,
          error: 'الحقول المطلوبة مفقودة',
          details: 'entry_date, payer_name, debit_account_id, credit_account_id, amount'
        },
        { status: 400 }
      )
    }

    // تحقق من وجود السند
    const exists = await query(
      `SELECT id FROM journal_entries WHERE id = $1 AND entry_type = 'receipt'`,
      [id]
    )
    if (exists.rowCount === 0) {
      return NextResponse.json({ success: false, error: 'السند غير موجود' }, { status: 404 })
    }

    // تحديث السجل الرئيسي
    await query(
      `UPDATE journal_entries
       SET entry_date = $1,
           description = $2,
           party_name = $3,
           party_type = $4,
           reference_number = $5,
           total_debit = $6,
           total_credit = $6,
           updated_at = NOW()
       WHERE id = $7`,
      [
        entry_date,
        description,
        finalPayerName,
        finalPayerType,
        reference_number,
        amount,
        id,
      ]
    )

    // الحصول على أسماء الحسابات المحدثة
    const debitAcc = await query(`SELECT account_name FROM chart_of_accounts WHERE id = $1`, [debit_account_id])
    const creditAcc = await query(`SELECT account_name FROM chart_of_accounts WHERE id = $1`, [credit_account_id])

    // جلب التفاصيل الحالية
    const detailsRes = await query(
      `SELECT id, debit_amount, credit_amount, line_order FROM journal_entry_details WHERE journal_entry_id = $1 ORDER BY line_order`,
      [id]
    )

    const debitRow = detailsRes.rows.find((d: any) => parseFloat(d.debit_amount) > 0)
    const creditRow = detailsRes.rows.find((d: any) => parseFloat(d.credit_amount) > 0)

    // تحديث أو إنشاء السطر المدين
    if (debitRow) {
      await query(
        `UPDATE journal_entry_details
         SET account_id = $1,
             account_name = $2,
             debit_amount = $3,
             credit_amount = 0,
             description = $4
         WHERE id = $5`,
        [debit_account_id, debitAcc.rows[0]?.account_name || '', amount, `استلام من ${finalPayerName}`, debitRow.id]
      )
    } else {
      await query(
        `INSERT INTO journal_entry_details (journal_entry_id, account_id, account_name, debit_amount, credit_amount, description, line_order)
         VALUES ($1, $2, $3, $4, 0, $5, 1)`,
        [id, debit_account_id, debitAcc.rows[0]?.account_name || '', amount, `استلام من ${finalPayerName}`]
      )
    }

    // تحديث أو إنشاء السطر الدائن
    if (creditRow) {
      await query(
        `UPDATE journal_entry_details
         SET account_id = $1,
             account_name = $2,
             debit_amount = 0,
             credit_amount = $3,
             description = $4
         WHERE id = $5`,
        [credit_account_id, creditAcc.rows[0]?.account_name || '', amount, `إيراد من ${finalPayerName}`, creditRow.id]
      )
    } else {
      await query(
        `INSERT INTO journal_entry_details (journal_entry_id, account_id, account_name, debit_amount, credit_amount, description, line_order)
         VALUES ($1, $2, $3, 0, $4, $5, 2)`,
        [id, credit_account_id, creditAcc.rows[0]?.account_name || '', amount, `إيراد من ${finalPayerName}`]
      )
    }

    return NextResponse.json({ success: true, message: 'تم تحديث السند بنجاح' })
  } catch (error) {
    console.error('❌ PUT /receipt-vouchers/[id] error:', error)
    return NextResponse.json({ success: false, error: 'فشل في تحديث السند' }, { status: 500 })
  }
}

// حذف سند قبض
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = parseInt(params.id, 10)
    if (Number.isNaN(id)) {
      return NextResponse.json({ success: false, error: 'معرّف غير صالح' }, { status: 400 })
    }

    const res = await query(`DELETE FROM journal_entries WHERE id = $1 AND entry_type = 'receipt'`, [id])
    if (res.rowCount === 0) {
      return NextResponse.json({ success: false, error: 'السند غير موجود' }, { status: 404 })
    }

    return NextResponse.json({ success: true, message: 'تم حذف السند' })
  } catch (error) {
    console.error('❌ DELETE /receipt-vouchers/[id] error:', error)
    return NextResponse.json({ success: false, error: 'فشل في حذف السند' }, { status: 500 })
  }
}
