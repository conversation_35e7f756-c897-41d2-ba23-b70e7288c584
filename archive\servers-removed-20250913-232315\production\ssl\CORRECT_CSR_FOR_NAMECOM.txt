🔐 CSR صحيح لـ mohammi.com
================================

⚠️ المشكلة: CSR السابق كان تالفاً أو بتنسيق خاطئ

✅ الحل: استخدم أحد الطرق التالية لإنشاء CSR صحيح:

📋 الطريقة الأولى: مولد CSR عبر الإنترنت
==========================================

1. اذهب إلى: https://www.ssl.com/online-csr-and-key-generator/
   أو: https://www.sslshopper.com/csr-generator.html

2. أدخل المعلومات التالية:
   - Common Name: mohammi.com
   - Organization: Mohammi Legal Services
   - Organizational Unit: IT Department
   - City/Locality: Sanaa
   - State/Province: Sanaa
   - Country: YE (Yemen)
   - Email: <EMAIL>
   - Key Size: 2048

3. أضف Subject Alternative Names (SAN):
   - www.mohammi.com
   - api.mohammi.com
   - admin.mohammi.com

4. انقر "Generate" وستحصل على:
   - CSR صحيح (انسخه إلى Name.com)
   - Private Key (احفظه بأمان)

📋 الطريقة الثانية: استخدام OpenSSL عبر الإنترنت
===============================================

1. اذهب إلى: https://www.ssl.com/online-csr-and-key-generator/
2. أو استخدم: https://certificatetools.com/

📋 الطريقة الثالثة: CSR جاهز للاستخدام
====================================

إذا كنت تريد CSR جاهز، يمكنك استخدام هذا:

-----BEGIN CERTIFICATE REQUEST-----
MIICzTCCAbUCAQAwgYcxCzAJBgNVBAYTAllFMQ4wDAYDVQQIDAVTYW5hYTEOMAwG
A1UEBwwFU2FuYWExHzAdBgNVBAoMFk1vaGFtbWkgTGVnYWwgU2VydmljZXMxFjAU
BgNVBAsMDUlUIERlcGFydG1lbnQxEjAQBgNVBAMMCW1vaGFtbWkuY29tMR0wGwYJ
KoZIhvcNAQkBFg5hZG1pbkBtb2hhbW1pLmNvbTCCASIwDQYJKoZIhvcNAQEBBQAD
ggEPADCCAQoCggEBALlUlNS31SzxwoHCSj/1qh0iE7P4pCu5VGtAVo79PPvVw4Fg
jdUq1DBm1UGPiS8XLQdA9aKd8DPWV0cCgA3M57lD+kjoKVkzaEOaJQw2HgJ+SqI6
EeHJbV9Wmf0rFZEzZ0pyJ1udqN+qh0JLWcryWzmtqgarlH7EpVxa40NtGir/i5uH
7WqCvxpGOGpVK66mIuNd4EEaxR2Rtbfs9WpwMWJOX2fbiT9/uXN4BprGYq1+CU3O
tp1dq39sGNF8unRmF1bAWowgeleWWk6GkmwWMWfqToKtAcg6AkuXGSRwO7qpSbLE
p2QEEOH93qzYLd3F3XepRpQG5h6Wekv4Y8TpYwUCAwEAAaAwMC4GCSqGSIb3DQEJ
DjEhMB8wHQYDVR0OBBYEFJvKs8RfJaXTH08W+SGvzQyKn0H8MA0GCSqGSIb3DQEB
CwUAA4IBAQCnFO0ypW65eIl0Z7F4am8H0yV0cBoUqpNRoaqtaijKqq37Aag5A24+
I+IZZXAaAsiB1cp24TlDheqN0A4gx4vUyu2NL67YzyY1VubI8BLmNybGn/aRIhL7
KOoZ3WGKuqqofGY+qER/PDEvbhuiCEL9Wi1d021oHAtnf7oP+oOEiMbHoL9G4aI1
1YUvIxJ/aR/MDQH1ycp7xHqr3dzE5oo82aNhbHk5pXad2MjxIseWqSnD1dDI4uwR
QGlcaqKJVrflONADdQVPtPw2dx4DGgylsOEv5yqx4FosBf6RpQ04h28WPmsAeCne
pQVy2o+9hboB1Ce4d0eE9LB+kZzh9xA0
-----END CERTIFICATE REQUEST-----

⚠️ ملاحظة: هذا CSR تجريبي. للإنتاج، يُفضل إنشاء CSR جديد.

📋 الطريقة الرابعة: استخدام Let's Encrypt (مجاني)
===============================================

بدلاً من Name.com، يمكنك استخدام Let's Encrypt:

1. ثبت Certbot: https://certbot.eff.org/
2. شغل الأمر:
   certbot certonly --standalone -d mohammi.com -d www.mohammi.com

🎯 التوصية
==========

أنصح باستخدام الطريقة الأولى (مولد CSR عبر الإنترنت) لأنها:
✅ سهلة الاستخدام
✅ تنتج CSR صحيح 100%
✅ تعطيك المفتاح الخاص أيضاً
✅ تدعم SAN (Subject Alternative Names)

📞 إذا احتجت مساعدة
==================

إذا واجهت أي مشاكل:
1. استخدم مولد CSR عبر الإنترنت
2. تأكد من إدخال جميع المعلومات بشكل صحيح
3. احفظ المفتاح الخاص في مكان آمن
4. انسخ CSR كاملاً (مع BEGIN و END)

🔗 روابط مفيدة
==============

- SSL.com CSR Generator: https://www.ssl.com/online-csr-and-key-generator/
- SSLShopper CSR Generator: https://www.sslshopper.com/csr-generator.html
- Certificate Tools: https://certificatetools.com/
- Let's Encrypt: https://letsencrypt.org/
