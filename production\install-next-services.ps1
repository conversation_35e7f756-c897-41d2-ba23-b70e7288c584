param(
  [string]$WorkingDir = "D:\\mohammi",
  [string]$Service3000 = "LegalSystemApp3000",
  [string]$Service3001 = "LegalSystemApp3001",
  [string]$Service3300 = "LegalSystemDev3300",
  [string]$Hostname = "127.0.0.1",
  [string]$DevDatabaseUrl = "postgresql://postgres:yemen123@localhost:5432/mohammidev"
)

Write-Host "Installing Next services at $WorkingDir ..."

if (!(Test-Path $WorkingDir)) { throw "WorkingDir not found: $WorkingDir" }

# Helper to register task
function New-NextTask {
  param(
    [string]$Name,
    [int]$Port,
    [string]$EnvDatabaseUrl
  )
  # نبني أمر PowerShell آمن الاقتباس لتمريره إلى -Command
  if ($EnvDatabaseUrl) {
    $psCmd = "& { $env:DATABASE_URL='" + $EnvDatabaseUrl.Replace("'","''") + "'; npx next start -p $Port --hostname $Hostname }"
  } else {
    $psCmd = "& { npx next start -p $Port --hostname $Hostname }"
  }
  $commandArg = "-NoProfile -WindowStyle Hidden -Command '" + $psCmd.Replace("'","''") + "'"
  $action = New-ScheduledTaskAction -Execute "powershell.exe" -Argument $commandArg -WorkingDirectory $WorkingDir
  $trg1 = New-ScheduledTaskTrigger -AtStartup
  $trg2 = New-ScheduledTaskTrigger -AtLogOn
  $principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount -RunLevel Highest
  $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable -ExecutionTimeLimit (New-TimeSpan -Hours 0)
  Register-ScheduledTask -TaskName $Name -Action $action -Trigger $trg1,$trg2 -Principal $principal -Settings $settings -Force | Out-Null
  Start-ScheduledTask -TaskName $Name -ErrorAction SilentlyContinue
}

# Production apps (headers from proxy will select DBs)
New-NextTask -Name $Service3000 -Port 3000 -EnvDatabaseUrl $null
New-NextTask -Name $Service3001 -Port 3001 -EnvDatabaseUrl $null

# Development app bound to mohammidev
New-NextTask -Name $Service3300 -Port 3300 -EnvDatabaseUrl $DevDatabaseUrl

Start-Sleep -Seconds 3
Write-Host "Installed and started: $Service3000, $Service3001, $Service3300"
