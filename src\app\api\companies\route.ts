import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع بيانات الشركة من قاعدة البيانات
export async function GET() {
  try {
    const result = await query('SELECT * FROM companies ORDER BY id')
    
    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching بيانات الشركة:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'فشل في جلب بيانات بيانات الشركة',
        message: 'تأكد من وجود الجدول في قاعدة البيانات'
      },
      { status: 500 }
    )
  }
}

// POST - إضافة بيانات الشركة جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // هنا يجب إضافة منطق الإدراج حسب كل جدول
    // سيتم تحديثه لاحقاً حسب هيكل كل جدول
    
    return NextResponse.json({
      success: true,
      message: 'تم إضافة بيانات الشركة بنجاح'
    })
  } catch (error) {
    console.error('Error creating بيانات الشركة:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة بيانات الشركة' },
      { status: 500 }
    )
  }
}

// PUT - تحديث بيانات الشركة
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    
    // هنا يجب إضافة منطق التحديث حسب كل جدول
    // سيتم تحديثه لاحقاً حسب هيكل كل جدول
    
    return NextResponse.json({
      success: true,
      message: 'تم تحديث بيانات الشركة بنجاح'
    })
  } catch (error) {
    console.error('Error updating بيانات الشركة:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث بيانات الشركة' },
      { status: 500 }
    )
  }
}

// DELETE - حذف بيانات الشركة
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف بيانات الشركة مطلوب' },
        { status: 400 }
      )
    }

    await query('DELETE FROM companies WHERE id = $1', [id])

    return NextResponse.json({
      success: true,
      message: 'تم حذف بيانات الشركة بنجاح'
    })
  } catch (error) {
    console.error('Error deleting بيانات الشركة:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف بيانات الشركة' },
      { status: 500 }
    )
  }
}