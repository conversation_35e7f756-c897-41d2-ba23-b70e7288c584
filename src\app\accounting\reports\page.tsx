'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import {
  BarChart3,
  FileText,
  Download,
  Calendar,
  TrendingUp,
  DollarSign,
  BookOpen,
  Calculator,
  PieChart,
  Activity
} from 'lucide-react'

interface ReportData {
  accounts: any[]
  transactions: any[]
  summary: {
    totalAssets: number
    totalLiabilities: number
    totalEquity: number
    totalRevenue: number
    totalExpenses: number
    netIncome: number
    // optional fields for vouchers summary and ledger stats
    totalPayments?: number
    paymentCount?: number
    totalReceipts?: number
    receiptCount?: number
    netAmount?: number
    totalTransactions?: number
    totalAccounts?: number
  }
}

export default function AccountingReportsPage() {
  const [reportData, setReportData] = useState<ReportData>({
    accounts: [],
    transactions: [],
    summary: {
      totalAssets: 0,
      totalLiabilities: 0,
      totalEquity: 0,
      totalRevenue: 0,
      totalExpenses: 0,
      netIncome: 0
    }
  })
  const [loading, setLoading] = useState(false)
  const [selectedReport, setSelectedReport] = useState('')
  // فلتر نوع السند لملخص السندات
  const [voucherType, setVoucherType] = useState<'all' | 'payment' | 'receipt' | 'journal'>('all')
  const [dateFrom, setDateFrom] = useState(new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0])
  const [dateTo, setDateTo] = useState(new Date().toISOString().split('T')[0])

  const reports = [
    {
      id: 'account-statement',
      title: 'كشف حساب',
      description: 'عرض تفصيلي لحركة حساب محدد مع الرصيد الجاري',
      icon: FileText,
      color: 'bg-cyan-500',
      isSpecial: true // تقرير خاص يحتاج صفحة منفصلة
    },
    {
      id: 'trial-balance',
      title: 'ميزان المراجعة',
      description: 'عرض أرصدة جميع الحسابات في تاريخ محدد',
      icon: Calculator,
      color: 'bg-blue-500'
    },
    {
      id: 'general-ledger',
      title: 'دفتر الأستاذ العام',
      description: 'تفاصيل حركة الحسابات خلال فترة محددة',
      icon: BookOpen,
      color: 'bg-green-500'
    },
    {
      id: 'income-statement',
      title: 'قائمة الدخل',
      description: 'الإيرادات والمصروفات وصافي الدخل',
      icon: TrendingUp,
      color: 'bg-purple-500'
    },
    {
      id: 'balance-sheet',
      title: 'الميزانية العمومية',
      description: 'الأصول والخصوم وحقوق الملكية',
      icon: PieChart,
      color: 'bg-orange-500'
    },
    {
      id: 'cash-flow',
      title: 'قائمة التدفقات النقدية',
      description: 'حركة النقدية من الأنشطة المختلفة',
      icon: Activity,
      color: 'bg-red-500'
    },
    {
      id: 'vouchers-summary',
      title: 'ملخص السندات',
      description: 'إجماليات سندات الصرف والقبض',
      icon: FileText,
      color: 'bg-indigo-500'
    }
  ]

  const generateReport = async () => {
    if (!selectedReport) {
      alert('يرجى اختيار نوع التقرير')
      return
    }

    try {
      setLoading(true)
      const params = new URLSearchParams({ date_from: dateFrom, date_to: dateTo })
      if (selectedReport === 'vouchers-summary' && voucherType && voucherType !== 'all') {
        params.append('voucher_type', voucherType)
      }
      const response = await fetch(`/api/accounting/reports/${selectedReport}?${params.toString()}`)

      if (response.ok) {
        const data = await response.json()
        setReportData(data)
      } else {
        alert('فشل في إنشاء التقرير')
      }
    } catch (error) {
      console.error('خطأ في إنشاء التقرير:', error)
      alert('حدث خطأ أثناء إنشاء التقرير')
    } finally {
      setLoading(false)
    }
  }

  const exportReport = async (format: 'pdf' | 'excel') => {
    if (!selectedReport || !reportData.accounts.length) {
      alert('يرجى إنشاء التقرير أولاً')
      return
    }

    try {
      const response = await fetch(`/api/accounting/reports/${selectedReport}/export`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          format,
          date_from: dateFrom,
          date_to: dateTo,
          voucher_type: selectedReport === 'vouchers-summary' && voucherType !== 'all' ? voucherType : undefined,
          data: reportData
        }),
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${selectedReport}-${dateFrom}-${dateTo}.${format === 'pdf' ? 'pdf' : 'xlsx'}`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        alert('فشل في تصدير التقرير')
      }
    } catch (error) {
      console.error('خطأ في تصدير التقرير:', error)
      alert('حدث خطأ أثناء تصدير التقرير')
    }
  }

  const renderTrialBalance = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-bold">ميزان المراجعة</h3>
      <div className="overflow-x-auto">
        <table className="w-full border-collapse border border-gray-300">
          <thead>
            <tr className="bg-gray-50">
              <th className="border border-gray-300 p-2 text-right">رمز الحساب</th>
              <th className="border border-gray-300 p-2 text-right">اسم الحساب</th>
              <th className="border border-gray-300 p-2 text-right">مدين</th>
              <th className="border border-gray-300 p-2 text-right">دائن</th>
            </tr>
          </thead>
          <tbody>
            {reportData.accounts.map((account, index) => (
              <tr key={index} className="hover:bg-gray-50">
                <td className="border border-gray-300 p-2 font-mono">{account.account_code}</td>
                <td className="border border-gray-300 p-2">{account.account_name}</td>
                <td className="border border-gray-300 p-2 text-right">
                  {account.debit_balance > 0 ? account.debit_balance.toLocaleString() : '-'}
                </td>
                <td className="border border-gray-300 p-2 text-right">
                  {account.credit_balance > 0 ? account.credit_balance.toLocaleString() : '-'}
                </td>
              </tr>
            ))}
          </tbody>
          <tfoot>
            <tr className="bg-gray-100 font-bold">
              <td colSpan={2} className="border border-gray-300 p-2 text-right">الإجماليات</td>
              <td className="border border-gray-300 p-2 text-right">
                {reportData.accounts.reduce((sum, acc) => sum + (acc.debit_balance || 0), 0).toLocaleString()}
              </td>
              <td className="border border-gray-300 p-2 text-right">
                {reportData.accounts.reduce((sum, acc) => sum + (acc.credit_balance || 0), 0).toLocaleString()}
              </td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>
  )

  const renderIncomeStatement = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-bold">قائمة الدخل</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-green-600">الإيرادات</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {reportData.accounts.filter(acc => acc.account_type === 'إيرادات').map((account, index) => (
                <div key={index} className="flex justify-between">
                  <span>{account.account_name}</span>
                  <span className="font-medium">{account.credit_balance?.toLocaleString() || 0}</span>
                </div>
              ))}
              <div className="border-t pt-2 font-bold flex justify-between">
                <span>إجمالي الإيرادات</span>
                <span className="text-green-600">{reportData.summary.totalRevenue.toLocaleString()}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-red-600">المصروفات</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {reportData.accounts.filter(acc => acc.account_type === 'مصروفات').map((account, index) => (
                <div key={index} className="flex justify-between">
                  <span>{account.account_name}</span>
                  <span className="font-medium">{account.debit_balance?.toLocaleString() || 0}</span>
                </div>
              ))}
              <div className="border-t pt-2 font-bold flex justify-between">
                <span>إجمالي المصروفات</span>
                <span className="text-red-600">{reportData.summary.totalExpenses.toLocaleString()}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="bg-blue-50">
        <CardContent className="p-6">
          <div className="flex justify-between items-center">
            <span className="text-lg font-bold">صافي الدخل</span>
            <span className={`text-2xl font-bold ${reportData.summary.netIncome >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {reportData.summary.netIncome.toLocaleString()} ر.ي
            </span>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  const renderBalanceSheet = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-bold">الميزانية العمومية</h3>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-blue-600">الأصول</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {reportData.accounts.filter(acc => acc.account_type === 'أصول').map((account, index) => (
                  <div key={index} className="flex justify-between">
                    <span>{account.account_name}</span>
                    <span className="font-medium">{account.debit_balance?.toLocaleString() || 0}</span>
                  </div>
                ))}
                <div className="border-t pt-2 font-bold flex justify-between">
                  <span>إجمالي الأصول</span>
                  <span className="text-blue-600">{reportData.summary.totalAssets.toLocaleString()}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-red-600">الخصوم</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {reportData.accounts.filter(acc => acc.account_type === 'خصوم').map((account, index) => (
                  <div key={index} className="flex justify-between">
                    <span>{account.account_name}</span>
                    <span className="font-medium">{account.credit_balance?.toLocaleString() || 0}</span>
                  </div>
                ))}
                <div className="border-t pt-2 font-bold flex justify-between">
                  <span>إجمالي الخصوم</span>
                  <span className="text-red-600">{reportData.summary.totalLiabilities.toLocaleString()}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-green-600">حقوق الملكية</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {reportData.accounts.filter(acc => acc.account_type === 'حقوق ملكية').map((account, index) => (
                  <div key={index} className="flex justify-between">
                    <span>{account.account_name}</span>
                    <span className="font-medium">{account.credit_balance?.toLocaleString() || 0}</span>
                  </div>
                ))}
                <div className="border-t pt-2 font-bold flex justify-between">
                  <span>إجمالي حقوق الملكية</span>
                  <span className="text-green-600">{reportData.summary.totalEquity.toLocaleString()}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )

  const renderReportContent = () => {
    if (!selectedReport || reportData.accounts.length === 0) {
      return (
        <div className="text-center py-12 text-gray-500">
          <BarChart3 className="h-16 w-16 mx-auto mb-4 text-gray-300" />
          <p>اختر نوع التقرير واضغط "إنشاء التقرير" لعرض البيانات</p>
        </div>
      )
    }

    switch (selectedReport) {
      case 'trial-balance':
        return renderTrialBalance()
      case 'income-statement':
        return renderIncomeStatement()
      case 'balance-sheet':
        return renderBalanceSheet()
      case 'vouchers-summary':
        return renderVouchersSummary()
      case 'general-ledger':
        return renderGeneralLedger()
      default:
        return (
          <div className="text-center py-12 text-gray-500">
            <p>هذا التقرير قيد التطوير</p>
          </div>
        )
    }
  }

  const renderVouchersSummary = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-bold">ملخص السندات</h3>

      {/* الإحصائيات */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-red-50 p-4 rounded-lg">
          <h4 className="font-medium text-red-900">سندات الصرف</h4>
          <p className="text-2xl font-bold text-red-700">
            {reportData.summary?.totalPayments?.toLocaleString() || 0} ر.ي
          </p>
          <p className="text-sm text-red-600">
            عدد السندات: {reportData.summary?.paymentCount || 0}
          </p>
        </div>

        <div className="bg-green-50 p-4 rounded-lg">
          <h4 className="font-medium text-green-900">سندات القبض</h4>
          <p className="text-2xl font-bold text-green-700">
            {reportData.summary?.totalReceipts?.toLocaleString() || 0} ر.ي
          </p>
          <p className="text-sm text-green-600">
            عدد السندات: {reportData.summary?.receiptCount || 0}
          </p>
        </div>

        <div className="bg-blue-50 p-4 rounded-lg">
          <h4 className="font-medium text-blue-900">صافي الحركة</h4>
          <p className={`text-2xl font-bold ${(reportData.summary?.netAmount || 0) >= 0 ? 'text-green-700' : 'text-red-700'}`}>
            {reportData.summary?.netAmount?.toLocaleString() || 0} ر.ي
          </p>
          <p className="text-sm text-blue-600">
            القبض - الصرف
          </p>
        </div>
      </div>

      {/* تفاصيل السندات */}
      {reportData.transactions && reportData.transactions.length > 0 && (
        <div className="overflow-x-auto">
          <h4 className="font-medium mb-3">آخر السندات (أحدث 50 سند)</h4>
          <table className="w-full border-collapse border border-gray-300 text-sm">
            <thead>
              <tr className="bg-gray-50">
                <th className="border border-gray-300 px-2 py-2 text-right w-20">رقم السند</th>
                <th className="border border-gray-300 px-2 py-2 text-right w-24">التاريخ</th>
                <th className="border border-gray-300 px-2 py-2 text-right w-16">النوع</th>
                <th className="border border-gray-300 px-2 py-2 text-right w-32">الحساب المدين</th>
                <th className="border border-gray-300 px-2 py-2 text-right w-32">الحساب الدائن</th>
                <th className="border border-gray-300 px-2 py-2 text-right w-24">المبلغ</th>
                <th className="border border-gray-300 px-2 py-2 text-right w-20">طريقة الدفع</th>
                <th className="border border-gray-300 px-2 py-2 text-right">البيان</th>
              </tr>
            </thead>
            <tbody>
              {reportData.transactions.map((transaction, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="border border-gray-300 px-2 py-2 font-mono text-xs">
                    {transaction.entry_number}
                  </td>
                  <td className="border border-gray-300 px-2 py-2 text-xs">
                    {new Date(transaction.entry_date).toLocaleDateString('ar-EG')}
                  </td>
                  <td className="border border-gray-300 px-2 py-2">
                    <span className={`px-1 py-0.5 rounded text-xs ${
                      transaction.entry_type === 'payment' ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'
                    }`}>
                      {transaction.entry_type === 'payment' ? 'صرف' : 'قبض'}
                    </span>
                  </td>
                  <td className="border border-gray-300 px-2 py-2 text-xs">
                    <div className="truncate" title={`${transaction.debit_account_code} - ${transaction.debit_account_name}`}>
                      <span className="font-mono text-blue-600">{transaction.debit_account_code}</span>
                      <br />
                      <span className="text-gray-600">{transaction.debit_account_name}</span>
                    </div>
                  </td>
                  <td className="border border-gray-300 px-2 py-2 text-xs">
                    <div className="truncate" title={`${transaction.credit_account_code} - ${transaction.credit_account_name}`}>
                      <span className="font-mono text-blue-600">{transaction.credit_account_code}</span>
                      <br />
                      <span className="text-gray-600">{transaction.credit_account_name}</span>
                    </div>
                  </td>
                  <td className="border border-gray-300 px-2 py-2 text-right font-medium text-xs">
                    <div>
                      {(transaction.total_debit || transaction.total_credit || 0)?.toLocaleString()}
                      <br />
                      <span className="text-gray-500">{(transaction.currency_symbol || 'ر.ي')}</span>
                    </div>
                  </td>
                  <td className="border border-gray-300 px-2 py-2 text-xs">
                    {transaction.payment_method || '—'}
                  </td>
                  <td className="border border-gray-300 px-2 py-2 text-xs">
                    <div className="max-w-xs truncate" title={transaction.description}>
                      {transaction.description}
                      {transaction.party_name && (
                        <div className="text-gray-500 mt-1">
                          {transaction.entry_type === 'payment' ? 'إلى: ' : 'من: '}
                          {transaction.party_name}
                        </div>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  )

  const renderGeneralLedger = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-bold">دفتر الأستاذ العام</h3>

      {reportData.summary && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-900">إجمالي المعاملات</h4>
            <p className="text-2xl font-bold text-blue-700">
              {reportData.summary.totalTransactions || 0}
            </p>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <h4 className="font-medium text-green-900">عدد الحسابات</h4>
            <p className="text-2xl font-bold text-green-700">
              {reportData.summary.totalAccounts || 0}
            </p>
          </div>
        </div>
      )}

      {reportData.accounts && reportData.accounts.length > 0 ? (
        <div className="space-y-6">
          {reportData.accounts.map((account, accountIndex) => (
            <div key={accountIndex} className="border rounded-lg p-4">
              <div className="flex justify-between items-center mb-3">
                <h4 className="font-medium text-lg">
                  {account.account_code} - {account.account_name}
                </h4>
                <div className="text-sm text-gray-600">
                  مدين: {account.total_debit?.toLocaleString()} |
                  دائن: {account.total_credit?.toLocaleString()} |
                  الرصيد: {account.balance?.toLocaleString()}
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 p-2 text-right">التاريخ</th>
                      <th className="border border-gray-300 p-2 text-right">المرجع</th>
                      <th className="border border-gray-300 p-2 text-right">النوع</th>
                      <th className="border border-gray-300 p-2 text-right">البيان</th>
                      <th className="border border-gray-300 p-2 text-right">مدين</th>
                      <th className="border border-gray-300 p-2 text-right">دائن</th>
                    </tr>
                  </thead>
                  <tbody>
                    {account.transactions.map((transaction, transIndex) => (
                      <tr key={transIndex} className="hover:bg-gray-50">
                        <td className="border border-gray-300 p-2">{new Date(transaction.transaction_date).toLocaleDateString('ar-EG')}</td>
                        <td className="border border-gray-300 p-2 font-mono">{transaction.reference}</td>
                        <td className="border border-gray-300 p-2">{transaction.transaction_type}</td>
                        <td className="border border-gray-300 p-2">{transaction.description}</td>
                        <td className="border border-gray-300 p-2 text-right">
                          {transaction.debit_amount > 0 ? transaction.debit_amount.toLocaleString() : '-'}
                        </td>
                        <td className="border border-gray-300 p-2 text-right">
                          {transaction.credit_amount > 0 ? transaction.credit_amount.toLocaleString() : '-'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          <p>لا توجد معاملات في الفترة المحددة</p>
        </div>
      )}
    </div>
  )

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان والأدوات */}
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-3 space-x-reverse">
            <BarChart3 className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">التقارير المحاسبية</h1>
              <p className="text-gray-600">تقارير مالية شاملة وتحليلات متقدمة</p>
            </div>
          </div>
        </div>

        {/* أنواع التقارير */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {reports.map((report) => (
            <Card
              key={report.id}
              className={`cursor-pointer transition-all hover:shadow-lg ${
                selectedReport === report.id ? 'ring-2 ring-blue-500 bg-blue-50' : ''
              }`}
              onClick={() => {
                if (report.isSpecial && report.id === 'account-statement') {
                  // الانتقال لصفحة كشف الحساب المنفصلة
                  window.location.href = '/accounting/reports/account-statement'
                } else {
                  setSelectedReport(report.id)
                }
              }}
            >
              <CardContent className="p-4">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <div className={`p-2 rounded-lg ${report.color} text-white`}>
                    <report.icon className="h-5 w-5" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium">{report.title}</h3>
                    <p className="text-sm text-gray-600">{report.description}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* إعدادات التقرير */}
        <Card>
          <CardHeader>
            <CardTitle>إعدادات التقرير</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
              <div>
                <Label htmlFor="date_from">من تاريخ</Label>
                <Input
                  id="date_from"
                  type="date"
                  value={dateFrom}
                  onChange={(e) => setDateFrom(e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="date_to">إلى تاريخ</Label>
                <Input
                  id="date_to"
                  type="date"
                  value={dateTo}
                  onChange={(e) => setDateTo(e.target.value)}
                />
              </div>

              <Button
                onClick={generateReport}
                disabled={!selectedReport || loading}
                className="w-full"
              >
                {loading ? 'جاري الإنشاء...' : 'إنشاء التقرير'}
              </Button>

              {/* فلتر نوع السند يظهر فقط في ملخص السندات */}
              {selectedReport === 'vouchers-summary' && (
                <div>
                  <Label>نوع السند</Label>
                  <Select value={voucherType} onValueChange={(v) => setVoucherType(v as any)}>
                    <SelectTrigger>
                      <SelectValue placeholder="كل الأنواع" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">الكل</SelectItem>
                      <SelectItem value="payment">سند صرف</SelectItem>
                      <SelectItem value="receipt">سند قبض</SelectItem>
                      <SelectItem value="journal">قيد يومي</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}

              <div className="flex space-x-2 space-x-reverse">
                <Button
                  variant="outline"
                  onClick={() => exportReport('pdf')}
                  disabled={!reportData.accounts.length}
                  className="flex-1"
                >
                  <Download className="h-4 w-4 ml-2" />
                  PDF
                </Button>
                <Button
                  variant="outline"
                  onClick={() => exportReport('excel')}
                  disabled={!reportData.accounts.length}
                  className="flex-1"
                >
                  <Download className="h-4 w-4 ml-2" />
                  Excel
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* محتوى التقرير */}
        <Card>
          <CardHeader>
            <CardTitle>
              {selectedReport ? reports.find(r => r.id === selectedReport)?.title : 'التقرير'}
              {selectedReport && (
                <Badge variant="outline" className="mr-2">
                  {dateFrom} إلى {dateTo}
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {renderReportContent()}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
