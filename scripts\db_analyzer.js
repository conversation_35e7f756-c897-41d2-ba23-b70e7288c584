#!/usr/bin/env node
/**
 * Database analyzer for PostgreSQL (mohammi)
 * - Reads connection from routing.config.json default_config
 * - Checks: tables without PK, FK columns without index, columns named *_id without FK,
 *   missing indexes on common filters (status, created_at), big tables without pagination index,
 *   row counts and index bloat quick estimate (index/table size), slow stats if available
 * - Outputs recommendations
 */
const fs = require('fs');
const path = require('path');
const { Client } = require('pg');

function loadRouting() {
  const rcPath = path.join(process.cwd(), 'routing.config.json');
  const raw = fs.readFileSync(rcPath, 'utf8');
  return JSON.parse(raw);
}

function buildConn(dbname, rc) {
  const dc = rc.default_config || {};
  return {
    host: process.env.DB_HOST || dc.db_host || 'localhost',
    port: Number(process.env.DB_PORT || dc.db_port || 5432),
    user: process.env.DB_USER || dc.db_user || 'postgres',
    password: process.env.DB_PASSWORD || dc.db_password || 'postgres',
    database: process.env.DB_NAME || dbname || 'mohammi',
  };
}

function pad(n, w) { const s = String(n); return s.length >= w ? s : ' '.repeat(w - s.length) + s; }

(async () => {
  const routing = loadRouting();
  const conn = buildConn('mohammi', routing);
  const client = new Client(conn);
  const findings = [];
  const recs = [];

  try {
    await client.connect();

    // tables & row counts
    const tablesRes = await client.query(`
      SELECT n.nspname AS schema, c.relname AS table,
             pg_total_relation_size(c.oid) AS total_bytes,
             pg_relation_size(c.oid) AS table_bytes,
             COALESCE((SELECT reltuples::bigint FROM pg_class WHERE oid=c.oid),0) AS est_rows
      FROM pg_class c
      JOIN pg_namespace n ON n.oid = c.relnamespace
      WHERE c.relkind='r' AND n.nspname NOT IN ('pg_catalog','information_schema')
      ORDER BY total_bytes DESC
    `);

    // primary keys
    const pkRes = await client.query(`
      SELECT tc.table_schema, tc.table_name
      FROM information_schema.tables t
      JOIN information_schema.table_constraints tc
        ON tc.table_schema=t.table_schema AND tc.table_name=t.table_name
      WHERE t.table_type='BASE TABLE' AND t.table_schema NOT IN ('pg_catalog','information_schema')
        AND tc.constraint_type='PRIMARY KEY'
    `);
    const pkSet = new Set(pkRes.rows.map(r => `${r.table_schema}.${r.table_name}`));

    // foreign keys
    const fkRes = await client.query(`
      SELECT tc.table_schema, tc.table_name, kcu.column_name
      FROM information_schema.table_constraints AS tc
      JOIN information_schema.key_column_usage AS kcu
        ON tc.constraint_name = kcu.constraint_name AND tc.table_schema = kcu.table_schema
      WHERE tc.constraint_type = 'FOREIGN KEY' AND tc.table_schema NOT IN ('pg_catalog','information_schema')
    `);

    // indexes per column
    const idxRes = await client.query(`
      SELECT n.nspname AS schema, t.relname AS table, a.attname AS column
      FROM pg_index i
      JOIN pg_class t ON t.oid = i.indrelid
      JOIN pg_namespace n ON n.oid = t.relnamespace
      JOIN unnest(i.indkey) WITH ORDINALITY AS k(attnum, ord) ON true
      JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = k.attnum
      WHERE n.nspname NOT IN ('pg_catalog','information_schema') AND i.indisvalid = true
    `);
    const idxSet = new Set(idxRes.rows.map(r => `${r.schema}.${r.table}.${r.column}`));

    // columns list
    const colsRes = await client.query(`
      SELECT table_schema, table_name, column_name, data_type
      FROM information_schema.columns
      WHERE table_schema NOT IN ('pg_catalog','information_schema')
    `);

    // Analyze: tables without PK
    for (const t of tablesRes.rows) {
      const fq = `${t.schema}.${t.table}`;
      if (!pkSet.has(fq)) {
        findings.push({ type: 'no_pk', table: fq, est_rows: Number(t.est_rows) });
        recs.push({
          table: fq,
          recommendation: 'جدول بدون مفتاح أساسي — يُنصح بإضافة عمود PK (serial/bigserial أو uuid) لتحسين الأداء والعلاقات',
        });
      }
    }

    // Analyze: *_id columns lacking FK or index
    const fkMap = new Map();
    fkRes.rows.forEach(r => {
      const key = `${r.table_schema}.${r.table_name}.${r.column_name}`;
      fkMap.set(key, true);
    });

    for (const c of colsRes.rows) {
      const fqCol = `${c.table_schema}.${c.table_name}.${c.column_name}`;
      if (c.column_name.toLowerCase().endsWith('_id')) {
        const hasFk = fkMap.has(fqCol);
        const hasIdx = idxSet.has(fqCol);
        if (!hasFk) {
          findings.push({ type: 'id_no_fk', column: fqCol });
          recs.push({
            column: fqCol,
            recommendation: 'عمود *_id بدون قيد علاقة FK — إن كان يمثل علاقة، أضف FOREIGN KEY للتحقق المرجعي وتحسين الربط',
          });
        }
        if (!hasIdx) {
          findings.push({ type: 'fk_no_index', column: fqCol });
          recs.push({
            column: fqCol,
            recommendation: 'عمود علاقة بدون فهرس — إضافة INDEX على أعمدة *_id يُسرّع الاستعلامات والربط',
          });
        }
      }
    }

    // Analyze: common filter columns (status, created_at, updated_at, date)
    const commonCols = colsRes.rows.filter(c => /^(status|created_at|updated_at|date|issue_date|entry_date)$/i.test(c.column_name));
    for (const c of commonCols) {
      const fqCol = `${c.table_schema}.${c.table_name}.${c.column_name}`;
      const hasIdx = idxSet.has(fqCol);
      if (!hasIdx) {
        findings.push({ type: 'common_no_index', column: fqCol });
        recs.push({
          column: fqCol,
          recommendation: 'عمود شائع للاستخدام في الفرز/التصفية بدون فهرس — يُنصح بإضافة INDEX',
        });
      }
    }

    // Output
    const lines = [];
    lines.push('تحليل قاعدة البيانات: mohammi');
    lines.push(`الاستضافة: ${conn.host}:${conn.port}`);
    lines.push('');
    lines.push('نتائج ملحوظة:');
    if (findings.length === 0) lines.push('- لا توجد مشاكل واضحة وفق القواعد البسيطة');
    for (const f of findings.slice(0, 200)) {
      if (f.type === 'no_pk') lines.push(`- جدول بدون PK: ${f.table} (rows≈${f.est_rows})`);
      if (f.type === 'id_no_fk') lines.push(`- عمود *_id بدون FK: ${f.column}`);
      if (f.type === 'fk_no_index') lines.push(`- عمود علاقة بدون فهرس: ${f.column}`);
      if (f.type === 'common_no_index') lines.push(`- عمود شائع بدون فهرس: ${f.column}`);
    }
    lines.push('');
    lines.push('توصيات:');
    if (recs.length === 0) lines.push('- لا توجد توصيات فورية — تبدو البنية الأساسية سليمة');
    for (const r of recs.slice(0, 200)) {
      lines.push(`- ${r.recommendation}${r.table ? ' | ' + r.table : ''}${r.column ? ' | ' + r.column : ''}`);
    }

    const outPath = path.join(process.cwd(), 'logs', `db-analysis-${Date.now()}.txt`);
    try { fs.mkdirSync(path.dirname(outPath), { recursive: true }); } catch {}
    fs.writeFileSync(outPath, lines.join('\n'), 'utf8');

    console.log(lines.join('\n'));
    console.log(`\nتم حفظ التقرير: ${outPath}`);
  } catch (e) {
    console.error('فشل التحليل:', e.message);
    process.exitCode = 1;
  } finally {
    await client.end().catch(()=>{});
  }
})();
