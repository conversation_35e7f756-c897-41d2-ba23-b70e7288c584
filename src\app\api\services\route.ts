import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database-router'

// GET - جلب جميع الخدمات
export async function GET() {
  try {
    
    const result = await query(`
      SELECT
        s.*,
        l.name as lineage_name
      FROM services s
      LEFT JOIN lineages l ON s.lineage_id = l.id
      ORDER BY s.created_date DESC
    `)

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching services:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات الخدمات' },
      { status: 500 }
    )
  }
}

// POST - إضافة خدمة جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, description, price, lineage_id, default_percentage } = body

    if (!name) {
      return NextResponse.json(
        { success: false, error: 'اسم الخدمة مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(`
      INSERT INTO services (name, description, price, lineage_id, default_percentage)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `, [name, description || '', price || 0, lineage_id || null, default_percentage || 0])

    return NextResponse.json({
      success: true,
      message: 'تم إضافة الخدمة بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error creating service:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة الخدمة' },
      { status: 500 }
    )
  }
}

// PUT - تحديث خدمة
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, name, description, price, lineage_id, default_percentage, is_active } = body

    if (!id || !name) {
      return NextResponse.json(
        { success: false, error: 'المعرف واسم الخدمة مطلوبان' },
        { status: 400 }
      )
    }

    const result = await query(`
      UPDATE services
      SET name = $1, description = $2, price = $3, lineage_id = $4,
          default_percentage = $5, is_active = $6, updated_at = CURRENT_TIMESTAMP
      WHERE id = $7
      RETURNING *
    `, [name, description || '', price || 0, lineage_id || null, default_percentage || 0, is_active !== false, id])

    return NextResponse.json({
      success: true,
      message: 'تم تحديث الخدمة بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error updating service:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الخدمة' },
      { status: 500 }
    )
  }
}

// DELETE - حذف خدمة
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الخدمة مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(
      'DELETE FROM services WHERE id = $1 RETURNING *',
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الخدمة غير موجودة' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف الخدمة بنجاح'
    })
  } catch (error) {
    console.error('Error deleting service:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الخدمة' },
      { status: 500 }
    )
  }
}