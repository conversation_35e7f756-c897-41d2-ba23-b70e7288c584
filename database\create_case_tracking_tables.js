/**
 * إنشا<PERSON> جداول نظام تتبع القضايا المتقدم
 */

const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'mohammidev'
};

async function createCaseTrackingTables() {
  const client = new Client(dbConfig);
  
  try {
    await client.connect();
    console.log('🔗 متصل بقاعدة البيانات');

    // 1. جدول مراحل القضايا
    await client.query(`
      CREATE TABLE IF NOT EXISTS case_stages (
        id SERIAL PRIMARY KEY,
        case_id INTEGER REFERENCES cases(id) ON DELETE CASCADE,
        stage_name VARCHAR(255) NOT NULL,
        stage_type VARCHAR(50) DEFAULT 'general',
        status VARCHAR(50) DEFAULT 'pending',
        start_date DATE,
        end_date DATE,
        expected_duration INTEGER DEFAULT 0,
        responsible_employee_id INTEGER REFERENCES employees(id),
        notes TEXT,
        order_index INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('✅ تم إنشاء جدول case_stages');

    // 2. جدول حركات القضايا
    await client.query(`
      CREATE TABLE IF NOT EXISTS case_movements (
        id SERIAL PRIMARY KEY,
        case_id INTEGER REFERENCES cases(id) ON DELETE CASCADE,
        movement_type VARCHAR(100) NOT NULL,
        movement_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        description TEXT NOT NULL,
        employee_id INTEGER REFERENCES employees(id),
        client_notified BOOLEAN DEFAULT false,
        documents_attached JSONB DEFAULT '[]',
        next_action_required TEXT,
        priority_level VARCHAR(20) DEFAULT 'medium',
        status VARCHAR(50) DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('✅ تم إنشاء جدول case_movements');

    // 3. جدول مواعيد الجلسات
    await client.query(`
      CREATE TABLE IF NOT EXISTS court_sessions (
        id SERIAL PRIMARY KEY,
        case_id INTEGER REFERENCES cases(id) ON DELETE CASCADE,
        session_date TIMESTAMP NOT NULL,
        court_name VARCHAR(255),
        session_type VARCHAR(100) DEFAULT 'hearing',
        status VARCHAR(50) DEFAULT 'scheduled',
        reminder_days INTEGER DEFAULT 3,
        client_reminder_sent BOOLEAN DEFAULT false,
        employee_reminder_sent BOOLEAN DEFAULT false,
        session_result TEXT,
        next_session_date TIMESTAMP,
        notes TEXT,
        location VARCHAR(255),
        judge_name VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('✅ تم إنشاء جدول court_sessions');

    // 4. جدول إعدادات التتبع
    await client.query(`
      CREATE TABLE IF NOT EXISTS tracking_settings (
        id SERIAL PRIMARY KEY,
        setting_name VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT NOT NULL,
        setting_type VARCHAR(50) DEFAULT 'string',
        description TEXT,
        category VARCHAR(100) DEFAULT 'general',
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('✅ تم إنشاء جدول tracking_settings');

    // 5. جدول الإشعارات
    await client.query(`
      CREATE TABLE IF NOT EXISTS case_notifications (
        id SERIAL PRIMARY KEY,
        case_id INTEGER REFERENCES cases(id) ON DELETE CASCADE,
        recipient_type VARCHAR(20) NOT NULL,
        recipient_id INTEGER NOT NULL,
        notification_type VARCHAR(100) NOT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        scheduled_date TIMESTAMP NOT NULL,
        sent_date TIMESTAMP,
        status VARCHAR(50) DEFAULT 'pending',
        delivery_method VARCHAR(50) DEFAULT 'email',
        metadata JSONB DEFAULT '{}',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('✅ تم إنشاء جدول case_notifications');

    // إنشاء الفهارس لتحسين الأداء
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_case_stages_case_id ON case_stages(case_id);
      CREATE INDEX IF NOT EXISTS idx_case_movements_case_id ON case_movements(case_id);
      CREATE INDEX IF NOT EXISTS idx_court_sessions_case_id ON court_sessions(case_id);
      CREATE INDEX IF NOT EXISTS idx_court_sessions_date ON court_sessions(session_date);
      CREATE INDEX IF NOT EXISTS idx_notifications_scheduled ON case_notifications(scheduled_date);
      CREATE INDEX IF NOT EXISTS idx_notifications_status ON case_notifications(status);
    `);
    console.log('✅ تم إنشاء الفهارس');

    // إدراج الإعدادات الافتراضية
    await client.query(`
      INSERT INTO tracking_settings (setting_name, setting_value, setting_type, description, category) VALUES
      ('default_reminder_days', '3', 'number', 'عدد الأيام الافتراضي للتذكير قبل الجلسات', 'notifications'),
      ('auto_client_notifications', 'true', 'boolean', 'تفعيل الإشعارات التلقائية للعملاء', 'notifications'),
      ('auto_employee_notifications', 'true', 'boolean', 'تفعيل الإشعارات التلقائية للموظفين', 'notifications'),
      ('case_inactivity_threshold', '30', 'number', 'عدد الأيام لاعتبار القضية متوقفة', 'tracking'),
      ('priority_case_threshold', '7', 'number', 'عدد الأيام لاعتبار القضية عالية الأولوية', 'tracking'),
      ('notification_methods', '["email", "sms", "whatsapp"]', 'json', 'طرق الإشعار المتاحة', 'notifications')
      ON CONFLICT (setting_name) DO NOTHING;
    `);
    console.log('✅ تم إدراج الإعدادات الافتراضية');

    console.log('\n🎉 تم إنشاء جميع جداول نظام تتبع القضايا بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في إنشاء الجداول:', error.message);
    throw error;
  } finally {
    await client.end();
  }
}

// تشغيل الدالة
if (require.main === module) {
  createCaseTrackingTables()
    .then(() => {
      console.log('✅ اكتمل إنشاء جداول نظام تتبع القضايا');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ فشل في إنشاء الجداول:', error);
      process.exit(1);
    });
}

module.exports = { createCaseTrackingTables };
