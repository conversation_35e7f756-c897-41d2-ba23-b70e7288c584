'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { BookOpen, Plus, Edit, Trash2, Search, Calculator, AlertCircle, Eye, Printer, Save, X } from 'lucide-react'

interface JournalEntry {
  id: number
  entry_number: string
  entry_date: string
  description: string
  total_debit: number
  total_credit: number
  currency_id: number
  currency_code?: string
  cost_center_id?: number
  cost_center_name?: string
  case_id?: number
  case_number?: string
  status: string
  created_by_name?: string
  created_date: string
  details?: JournalEntryDetail[]
}

interface JournalEntryDetail {
  id?: number
  line_number: number
  account_id: number | string
  account_name?: string
  account_code?: string
  debit_amount: number | ''
  credit_amount: number | ''
  currency_id: number
  exchange_rate: number
  cost_center_id?: number
  case_id?: number
  service_id?: number
  payment_method_id?: number
  description: string
  reference_number?: string
}

interface Account {
  id: number
  account_code: string
  account_name: string
  account_type: string
  linked_table?: string
  allow_transactions?: boolean
  is_linked_record?: boolean
  original_table?: string
}

interface Currency {
  id: number
  currency_code: string
  currency_name: string
  symbol: string
}

interface PaymentMethod {
  id: number
  method_code: string
  method_name: string
}

interface CostCenter {
  id: number
  center_code: string
  center_name: string
}

interface Case {
  id: number
  case_number: string
  title: string
  client_name: string
  status: string
}

interface Service {
  id: number
  name: string
  lineage_id: number
  lineage_name: string
}

// حالة النموذج للقيود اليومية
interface FormDataState {
  entry_date: string
  description: string
  currency_id: string
  reference_number: string
  cost_center_id?: string
  case_id?: string
  service_id?: string
  case_number?: string
}

export default function JournalEntriesPage() {
  const [entries, setEntries] = useState<JournalEntry[]>([])
  const [accounts, setAccounts] = useState<Account[]>([])
  const [currencies, setCurrencies] = useState<Currency[]>([])
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([])
  const [costCenters, setCostCenters] = useState<CostCenter[]>([])
  const [cases, setCases] = useState<Case[]>([])
  const [services, setServices] = useState<Service[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [editingEntry, setEditingEntry] = useState<JournalEntry | null>(null)
  const [searchTerm, setSearchTerm] = useState('')

  // بيانات النموذج
  const [formData, setFormData] = useState<FormDataState>({
    entry_date: new Date().toISOString().split('T')[0],
    description: '',
    currency_id: '1',
    reference_number: '',
    cost_center_id: '',
    case_id: '',
    service_id: '',
    case_number: ''
  })

  // تفاصيل القيد
  const [entryDetails, setEntryDetails] = useState<JournalEntryDetail[]>([
    {
      line_number: 1,
      account_id: '',
      debit_amount: '',
      credit_amount: '',
      currency_id: 1,
      exchange_rate: 1,
      description: ''
    },
    {
      line_number: 2,
      account_id: '',
      debit_amount: '',
      credit_amount: '',
      currency_id: 1,
      exchange_rate: 1,
      description: ''
    }
  ])

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)

      // جلب القيود مع التفاصيل (فقط نوع JE)
      const entriesResponse = await fetch('/api/accounting/journal-entries?include_details=true&entry_types=journal')
      if (entriesResponse.ok) {
        const entriesData = await entriesResponse.json()
        setEntries(entriesData.entries || [])
      }

      // جلب الحسابات (المستوى 4 فقط + العملاء والموظفين)
      const accountsResponse = await fetch('/api/accounting/chart-of-accounts?only_transactional=true&include_linked=true')
      if (accountsResponse.ok) {
        const accountsData = await accountsResponse.json()

        // ترتيب الحسابات: الحسابات العادية أولاً، ثم العملاء، ثم الموظفين
        const sortedAccounts = (accountsData.accounts || []).sort((a: any, b: any) => {
          if (a.is_linked_record && !b.is_linked_record) return 1
          if (!a.is_linked_record && b.is_linked_record) return -1
          if (a.is_linked_record && b.is_linked_record) {
            if (a.original_table === 'clients' && b.original_table === 'employees') return -1
            if (a.original_table === 'employees' && b.original_table === 'clients') return 1
          }
          return a.account_name.localeCompare(b.account_name, 'ar')
        })

        setAccounts(sortedAccounts)
      } else {
        console.error('Failed to fetch accounts:', accountsResponse.status)
      }

      // جلب العملات
      const currenciesResponse = await fetch('/api/accounting/currencies')
      if (currenciesResponse.ok) {
        const currenciesData = await currenciesResponse.json()
        setCurrencies(currenciesData.currencies || [])
      }

      // جلب طرق الدفع
      const paymentMethodsResponse = await fetch('/api/accounting/payment-methods')
      if (paymentMethodsResponse.ok) {
        const paymentMethodsData = await paymentMethodsResponse.json()
        setPaymentMethods(paymentMethodsData.methods || [])
      }

      // جلب مراكز التكلفة
      const costCentersResponse = await fetch('/api/cost-centers')
      if (costCentersResponse.ok) {
        const costCentersData = await costCentersResponse.json()
        setCostCenters(costCentersData.centers || [])
      }

      // جلب القضايا
      const casesResponse = await fetch('/api/issues')
      if (casesResponse.ok) {
        const casesData = await casesResponse.json()
        setCases(casesData.data || casesData.issues || [])
      }

      // جلب الخدمات
      const servicesResponse = await fetch('/api/services')
      if (servicesResponse.ok) {
        const servicesData = await servicesResponse.json()
        setServices(servicesData.data || [])
      }

    } catch (error) {
      console.error('خطأ في جلب البيانات:', error)
    } finally {
      setLoading(false)
    }
  }

  // حساب إجماليات القيد
  const calculateTotals = () => {
    const totalDebit = entryDetails.reduce((sum, detail) => sum + (detail.debit_amount || 0), 0)
    const totalCredit = entryDetails.reduce((sum, detail) => sum + (detail.credit_amount || 0), 0)
    return { totalDebit, totalCredit }
  }

  // التحقق من توازن القيد
  const isBalanced = () => {
    const { totalDebit, totalCredit } = calculateTotals()
    return Math.abs(totalDebit - totalCredit) < 0.01 // للتعامل مع أخطاء الفاصلة العشرية
  }

  // إضافة سطر جديد
  const addDetailLine = () => {
    const newLine: JournalEntryDetail = {
      line_number: entryDetails.length + 1,
      account_id: '',
      debit_amount: 0,
      credit_amount: 0,
      currency_id: 1,
      exchange_rate: 1,
      description: ''
    }
    setEntryDetails([...entryDetails, newLine])
  }

  // حذف سطر
  const removeDetailLine = (index: number) => {
    if (entryDetails.length > 2) { // الحد الأدنى سطرين
      const newDetails = entryDetails.filter((_, i) => i !== index)
      // إعادة ترقيم الأسطر
      const reNumbered = newDetails.map((detail, i) => ({
        ...detail,
        line_number: i + 1
      }))
      setEntryDetails(reNumbered)
    }
  }

  // تحديث تفاصيل السطر
  const updateDetailLine = (index: number, field: keyof JournalEntryDetail, value: any) => {
    const newDetails = [...entryDetails]

    // معالجة خاصة لـ account_id
    if (field === 'account_id') {
      // إذا كانت القيمة فارغة فلا نحدث شيء
      if (value === "" || value === "0") {
        newDetails[index] = { ...newDetails[index], [field]: '' }
      } else {
        // تحويل القيمة إلى رقم إذا كانت رقمية
        const numericValue = parseInt(value)
        newDetails[index] = { ...newDetails[index], [field]: numericValue }
      }
    } else if (field === 'debit_amount' || field === 'credit_amount') {
      // إدخال أعداد صحيحة فقط، والسماح بقيمة فارغة
      if (value === '' || value === null || value === undefined) {
        newDetails[index] = { ...newDetails[index], [field]: '' as any }
      } else {
        const onlyDigits = String(value).replace(/\D+/g, '')
        const intVal = onlyDigits ? parseInt(onlyDigits, 10) : ''
        newDetails[index] = { ...newDetails[index], [field]: intVal as any }
      }
    } else {
      newDetails[index] = { ...newDetails[index], [field]: value }
    }

    // إذا تم إدخال مبلغ مدين، مسح المبلغ الدائن والعكس
    const valNum = Number(String(value).replace(/\D+/g, ''))
    if (field === 'debit_amount' && valNum > 0) {
      newDetails[index].credit_amount = ''
    } else if (field === 'credit_amount' && valNum > 0) {
      newDetails[index].debit_amount = ''
    }

    setEntryDetails(newDetails)
  }

  // دالة لجلب اسم الحساب من قائمة الحسابات
  const getAccountName = (accountId: number | string) => {
    if (!accountId) return 'غير محدد'
    const account = accounts.find(acc => acc.id.toString() === accountId.toString())
    return account ? `${account.account_name} (${account.account_code})` : `حساب رقم ${accountId}`
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // التحقق من توازن القيد
    if (!isBalanced()) {
      alert('القيد غير متوازن! يجب أن تكون إجماليات المدين والدائن متساوية')
      return
    }

    // التحقق من وجود حسابات وأرقام صحيحة
    const validDetails = entryDetails.filter(detail =>
      (typeof detail.account_id === 'number' ? detail.account_id > 0 : detail.account_id !== '') &&
      (Number(detail.debit_amount || 0) > 0 || Number(detail.credit_amount || 0) > 0)
    )

    if (validDetails.length < 2) {
      alert('يجب أن يحتوي القيد على سطرين على الأقل بحسابات وأرقام صحيحة')
      return
    }

    try {
      const { totalDebit, totalCredit } = calculateTotals()

      const url = editingEntry
        ? `/api/accounting/journal-entries/${editingEntry.id}`
        : '/api/accounting/journal-entries'

      const method = editingEntry ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          total_debit: totalDebit,
          total_credit: totalCredit,
          currency_id: parseInt(formData.currency_id),
          cost_center_id: formData.cost_center_id ? parseInt(formData.cost_center_id) : null,
          case_id: formData.case_id ? parseInt(formData.case_id) : null,
          service_id: formData.service_id ? parseInt(formData.service_id) : null,
          details: validDetails.map(detail => ({
            ...detail,
            account_id: parseInt(detail.account_id.toString()),
            currency_id: parseInt(detail.currency_id.toString()),
            debit_amount: Number(detail.debit_amount || 0),
            credit_amount: Number(detail.credit_amount || 0),
            cost_center_id: detail.cost_center_id ? parseInt((detail.cost_center_id as any).toString()) : null,
            payment_method_id: detail.payment_method_id ? parseInt((detail.payment_method_id as any).toString()) : null
          }))
        }),
      })

      if (response.ok) {
        await fetchData()
        setShowAddDialog(false)
        setEditingEntry(null)
        resetForm()
      } else {
        const errorData = await response.json()
        alert(`خطأ: ${errorData.error}`)
      }
    } catch (error) {
      console.error('خطأ في حفظ القيد:', error)
      alert('حدث خطأ أثناء حفظ القيد')
    }
  }

  const resetForm = () => {
    setFormData({
      entry_date: new Date().toISOString().split('T')[0],
      description: '',
      currency_id: '1',
      cost_center_id: '',
      case_id: '',
      service_id: '',
      reference_number: ''
    })
    setEntryDetails([
      {
        line_number: 1,
        account_id: '',
        debit_amount: '',
        credit_amount: '',
        currency_id: 1,
        exchange_rate: 1,
        cost_center_id: 0,
        case_id: 0,
        service_id: 0,
        description: ''
      },
      {
        line_number: 2,
        account_id: '',
        debit_amount: 0,
        credit_amount: 0,
        currency_id: 1,
        exchange_rate: 1,
        cost_center_id: 0,
        case_id: 0,
        service_id: 0,
        description: ''
      }
    ])
  }

  const handleEdit = async (entry: JournalEntry) => {
    try {
      // جلب تفاصيل القيد
      const response = await fetch(`/api/accounting/journal-entries/${entry.id}?include_details=true`)
      if (response.ok) {
        const data = await response.json()
        const entryWithDetails = data.entry

        setEditingEntry(entryWithDetails)
        setFormData({
          entry_date: entryWithDetails.entry_date,
          description: entryWithDetails.description,
          currency_id: entryWithDetails.currency_id?.toString() || '1',
          cost_center_id: entryWithDetails.cost_center_id?.toString() || '',
          case_id: entryWithDetails.case_id?.toString() || '',
          case_number: entryWithDetails.case_number || '',
          reference_number: entryWithDetails.reference_number || ''
        })

        if (entryWithDetails.details && entryWithDetails.details.length > 0) {
          setEntryDetails(entryWithDetails.details.map((detail: any) => ({
            line_number: detail.line_number,
            account_id: detail.account_id,
            debit_amount: detail.debit_amount || 0,
            credit_amount: detail.credit_amount || 0,
            currency_id: detail.currency_id || 1,
            exchange_rate: detail.exchange_rate || 1,
            cost_center_id: detail.cost_center_id,
            case_id: detail.case_id, // إضافة case_id للتفصيل
            payment_method_id: detail.payment_method_id,
            description: detail.description || '',
            reference_number: detail.reference_number || ''
          })))
        }

        setShowAddDialog(true)
      }
    } catch (error) {
      console.error('خطأ في جلب تفاصيل القيد:', error)
      alert('حدث خطأ أثناء جلب تفاصيل القيد')
    }
  }

  const handleView = async (entry: JournalEntry) => {
    try {
      const response = await fetch(`/api/accounting/journal-entries/${entry.id}?include_details=true`)
      if (!response.ok) throw new Error('فشل في جلب تفاصيل القيد')
      const data = await response.json()
      const entryWithDetails = data.entry

      const companyRes = await fetch('/api/company')
      const companyJson = companyRes.ok ? await companyRes.json() : { data: [] }
      const company = (companyJson.data && companyJson.data[0]) || {}

      const amountToWords = (num: number): string => {
        const units = ['','واحد','اثنان','ثلاثة','أربعة','خمسة','ستة','سبعة','ثمانية','تسعة']
        const tens = ['','عشرة','عشرون','ثلاثون','أربعون','خمسون','ستون','سبعون','ثمانون','تسعون']
        const teens = ['','أحد عشر','اثنا عشر','ثلاثة عشر','أربعة عشر','خمسة عشر','ستة عشر','سبعة عشر','ثمانية عشر','تسعة عشر']
        const hundreds = ['','مائة','مائتان','ثلاثمائة','أربعمائة','خمسمائة','ستمائة','سبعمائة','ثمانمائة','تسعمائة']
        const scales = [ {v:1_000_000_000, s:'مليار'}, {v:1_000_000, s:'مليون'}, {v:1_000, s:'ألف'} ]
        const toWordsUnder100 = (n:number) => {
          if (n<10) return units[n]
          if (n===10) return 'عشرة'
          if (n>10 && n<20) return teens[n-10]
          const t=Math.floor(n/10), u=n%10
          return u? `${units[u]} و ${tens[t]}`: tens[t]
        }
        const toWordsUnder1000 = (n:number) => {
          const h=Math.floor(n/100), r=n%100
          const hPart = h? hundreds[h]: ''
          const rPart = r? toWordsUnder100(r): ''
          return [hPart, rPart].filter(Boolean).join(' و ')
        }
        if (!Number.isFinite(num) || num<0) return `${num} ريال`
        if (num===0) return 'صفر ريال'
        let n=Math.floor(num)
        const parts:string[]=[]
        for (const sc of scales){
          if (n>=sc.v){
            const q=Math.floor(n/sc.v)
            parts.push(`${toWordsUnder1000(q)} ${sc.s}`)
            n%=sc.v
          }
        }
        if (n>0) parts.push(toWordsUnder1000(n))
        return parts.filter(Boolean).join(' و ') + ' ريال'
      }

      const totalCredit = Number(entry.total_credit || 0)
      const totalCreditWords = amountToWords(totalCredit)

      const printWindow = window.open('', '_blank', 'width=900,height=900')
      if (printWindow) {
        const logoUrl = company.logo_image_url || company.logo_url || ''
        const companyName = company.company_name || company.name || company.legal_name || 'المؤسسة'
        const todayStr = new Date(entry.entry_date).toLocaleDateString('en-GB')

        printWindow.document.write(`
          <html dir="rtl">
            <head>
              <title>قيد يومي رقم ${entry.entry_number}</title>
              <style>
                @font-face { font-family: 'Khalid-Art-bold'; src: url('/fonts/Khalid-Art-bold.ttf') format('truetype'); font-weight: bold; font-style: normal; }
                body { font-family: 'Khalid-Art-bold', Arial, sans-serif; margin: 24px; color:#000; }
                .header { display:grid; grid-template-columns: 1fr auto 1fr; align-items:center; border-bottom:2px solid #000; padding-bottom:12px; margin-bottom:16px; }
                .header .right { text-align:right; line-height:1.4; }
                .header .center { text-align:center; }
                .header .left { text-align:left; line-height:1.4; }
                .header .logo img { max-height:64px; }
                .boxes { display:flex; gap:12px; margin:16px 0; }
                .box { border:1px solid #444; padding:10px 14px; border-radius:8px; font-weight:bold; background: #f9fafb; color:#111; }
                .box.center { flex:1; text-align:center; background: linear-gradient(180deg, #f1f5f9, #e2e8f0); border-color:#222; }
                .row { display:flex; gap:12px; align-items:center; margin:10px 0; }
                .label { font-weight:bold; min-width:140px; }
                .details-table { width:100%; border-collapse:collapse; margin-top:16px; }
                .details-table th, .details-table td { border:1px solid #000; padding:8px 10px; text-align:right; }
                .details-table th { background:#eee; }
                .credit { background:#e8f3ff; }
                .debit { background:#eaf7ea; }
                .separator { height:3px; background:#000; margin:18px 0; }
                .signatures { display:flex; justify-content:space-between; margin-top:28px; }
                .signatures .col { width:30%; text-align:center; }
                .signatures .title { font-weight:bold; border-top:1px solid #000; padding-top:8px; }
                @media print { body { margin:0; } }
              </style>
            </head>
            <body>
              <div class="header">
                <div class="right">
                  <div style="font-size:18px; font-weight:bold;">${companyName}</div>
                  <div>${company.legal_form || company.company_type || ''}</div>
                </div>
                <div class="center logo">${logoUrl ? `<img src="${logoUrl}" />` : ''}</div>
                <div class="left">
                  ${company.phone ? `<div>رقم التواصل: ${company.phone}</div>` : ''}
                  ${company.email ? `<div>الايميل: ${company.email}</div>` : ''}
                </div>
              </div>

              <div class="boxes">
                <div class="box" style="min-width:220px; text-align:right;">رقم القيد: ${entry.entry_number}</div>
                <div class="box center">قيد يومية</div>
                <div class="box" style="min-width:180px; text-align:left;">التاريخ: ${todayStr}</div>
              </div>

              <div class="row" style="margin-top:12px;">
                <div class="label" style="min-width:auto;">إجمالي الدائن:</div>
                <div style="font-weight:bold; margin-inline-end:16px;">${Math.round(Number(totalCredit||0)).toLocaleString('en-US', { maximumFractionDigits: 0 })} ر.ي</div>
                <div style="font-weight:bold;">${totalCreditWords}</div>
              </div>

              <div class="row">
                <div class="label">البيان:</div>
                <div style="flex:1; border-bottom:1px dotted #000; padding-bottom:4px;">${entry.description || ''}</div>
              </div>

              <table class="details-table">
                <thead>
                  <tr>
                    <th style="width:70px;">م</th>
                    <th>اسم الحساب</th>
                    <th style="width:140px;">مدين</th>
                    <th style="width:140px;">دائن</th>
                    <th>البيان</th>
                  </tr>
                </thead>
                <tbody>
                  ${entryWithDetails.details?.map((detail: any) => {
                    const isDebit = parseFloat(detail.debit_amount||0) > 0
                    const isCredit = parseFloat(detail.credit_amount||0) > 0
                    const cls = isDebit ? 'debit' : (isCredit ? 'credit' : '')
                    return `
                      <tr class="${cls}">
                        <td>${detail.id || detail.line_number || ''}</td>
                        <td>${detail.account_name || ''}</td>
                        <td>${isDebit ? Math.round(Number(detail.debit_amount||0)).toLocaleString('en-US', { maximumFractionDigits: 0 }) : '-'}</td>
                        <td>${isCredit ? Math.round(Number(detail.credit_amount||0)).toLocaleString('en-US', { maximumFractionDigits: 0 }) : '-'}</td>
                        <td>${detail.description || ''}</td>
                      </tr>`
                  }).join('') || ''}
                </tbody>
              </table>

              <div class="separator"></div>

              <div class="signatures">
                <div class="col"><div class="title">أمين الصندوق</div></div>
                <div class="col"><div class="title">المحاسب</div></div>
                <div class="col"><div class="title">المدير</div></div>
              </div>

              <script>
                window.onload = function() { window.focus(); };
              </script>
            </body>
          </html>
        `)
        printWindow.document.close()
      }
    } catch (error) {
      console.error('خطأ في عرض القيد:', error)
      alert('حدث خطأ أثناء عرض القيد')
    }
  }

  const handleDelete = async (entryId: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا القيد؟ سيتم حذف جميع تفاصيل القيد أيضاً.')) {
      return
    }

    try {
      const response = await fetch(`/api/accounting/journal-entries/${entryId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        await fetchData()
        alert('تم حذف القيد بنجاح')
      } else {
        const errorData = await response.json()
        alert(`خطأ في حذف القيد: ${errorData.error}`)
      }
    } catch (error) {
      console.error('خطأ في حذف القيد:', error)
      alert('حدث خطأ أثناء حذف القيد')
    }
  }

  const handlePrint = async (entry: JournalEntry) => {
    try {
      // جلب تفاصيل القيد للطباعة
      const response = await fetch(`/api/accounting/journal-entries/${entry.id}?include_details=true`)
      if (!response.ok) throw new Error('فشل في جلب تفاصيل القيد')
      const data = await response.json()
      const entryWithDetails = data.entry

      // جلب بيانات الشركة
      const companyRes = await fetch('/api/company')
      const companyJson = companyRes.ok ? await companyRes.json() : { data: [] }
      const company = (companyJson.data && companyJson.data[0]) || {}

      const amountToWords = (num: number): string => {
        const units = ['','واحد','اثنان','ثلاثة','أربعة','خمسة','ستة','سبعة','ثمانية','تسعة']
        const tens = ['','عشرة','عشرون','ثلاثون','أربعون','خمسون','ستون','سبعون','ثمانون','تسعون']
        const teens = ['','أحد عشر','اثنا عشر','ثلاثة عشر','أربعة عشر','خمسة عشر','ستة عشر','سبعة عشر','ثمانية عشر','تسعة عشر']
        const hundreds = ['','مائة','مائتان','ثلاثمائة','أربعمائة','خمسمائة','ستمائة','سبعمائة','ثمانمائة','تسعمائة']
        const scales = [ {v:1_000_000_000, s:'مليار'}, {v:1_000_000, s:'مليون'}, {v:1_000, s:'ألف'} ]
        const toWordsUnder100 = (n:number) => {
          if (n<10) return units[n]
          if (n===10) return 'عشرة'
          if (n>10 && n<20) return teens[n-10]
          const t=Math.floor(n/10), u=n%10
          return u? `${units[u]} و ${tens[t]}`: tens[t]
        }
        const toWordsUnder1000 = (n:number) => {
          const h=Math.floor(n/100), r=n%100
          const hPart = h? hundreds[h]: ''
          const rPart = r? toWordsUnder100(r): ''
          return [hPart, rPart].filter(Boolean).join(' و ')
        }
        if (!Number.isFinite(num) || num<0) return `${num} ريال`
        if (num===0) return 'صفر ريال'
        let n=Math.floor(num)
        const parts:string[]=[]
        for (const sc of scales){
          if (n>=sc.v){
            const q=Math.floor(n/sc.v)
            parts.push(`${toWordsUnder1000(q)} ${sc.s}`)
            n%=sc.v
          }
        }
        if (n>0) parts.push(toWordsUnder1000(n))
        return parts.filter(Boolean).join(' و ') + ' ريال'
      }

      const totalCredit = Number(entry.total_credit || 0)
      const totalCreditWords = amountToWords(totalCredit)

      const printWindow = window.open('', '_blank', 'width=900,height=900')
      if (printWindow) {
        const logoUrl = company.logo_image_url || company.logo_url || ''
        const companyName = company.company_name || company.name || company.legal_name || 'المؤسسة'
        const todayStr = new Date(entry.entry_date).toLocaleDateString('en-GB')

        printWindow.document.write(`
          <html dir="rtl">
            <head>
              <title>قيد يومي رقم ${entry.entry_number}</title>
              <style>
                @font-face { font-family: 'Khalid-Art-bold'; src: url('/fonts/Khalid-Art-bold.ttf') format('truetype'); font-weight: bold; font-style: normal; }
                body { font-family: 'Khalid-Art-bold', Arial, sans-serif; margin: 24px; color:#000; }
                .header { display:grid; grid-template-columns: 1fr auto 1fr; align-items:center; border-bottom:2px solid #000; padding-bottom:12px; margin-bottom:16px; }
                .header .right { text-align:right; line-height:1.4; }
                .header .center { text-align:center; }
                .header .left { text-align:left; line-height:1.4; }
                .header .logo img { max-height:64px; }
                .boxes { display:flex; gap:12px; margin:16px 0; }
                .box { border:1px solid #444; padding:10px 14px; border-radius:8px; font-weight:bold; background: #f9fafb; color:#111; }
                .box.center { flex:1; text-align:center; background: linear-gradient(180deg, #f1f5f9, #e2e8f0); border-color:#222; }
                .row { display:flex; gap:12px; align-items:center; margin:10px 0; }
                .label { font-weight:bold; min-width:140px; }
                .details-table { width:100%; border-collapse:collapse; margin-top:16px; }
                .details-table th, .details-table td { border:1px solid #000; padding:8px 10px; text-align:right; }
                .details-table th { background:#eee; }
                .credit { background:#e8f3ff; }
                .debit { background:#eaf7ea; }
                .separator { height:3px; background:#000; margin:18px 0; }
                .signatures { display:flex; justify-content:space-between; margin-top:28px; }
                .signatures .col { width:30%; text-align:center; }
                .signatures .title { font-weight:bold; border-top:1px solid #000; padding-top:8px; }
                @media print { body { margin:0; } }
              </style>
            </head>
            <body>
              <div class="header">
                <div class="right">
                  <div style="font-size:18px; font-weight:bold;">${companyName}</div>
                  <div>${company.legal_form || company.company_type || ''}</div>
                </div>
                <div class="center logo">${logoUrl ? `<img src="${logoUrl}" />` : ''}</div>
                <div class="left">
                  ${company.phone ? `<div>رقم التواصل: ${company.phone}</div>` : ''}
                  ${company.email ? `<div>الايميل: ${company.email}</div>` : ''}
                </div>
              </div>

              <div class="boxes">
                <div class="box" style="min-width:220px; text-align:right;">رقم القيد: ${entry.entry_number}</div>
                <div class="box center">قيد يومية</div>
                <div class="box" style="min-width:180px; text-align:left;">التاريخ: ${todayStr}</div>
              </div>

              <div class="row" style="margin-top:12px;">
                <div class="label" style="min-width:auto;">إجمالي الدائن:</div>
                <div style="font-weight:bold; margin-inline-end:16px;">${Math.round(Number(totalCredit||0)).toLocaleString('en-US', { maximumFractionDigits: 0 })} ر.ي</div>
                <div style="font-weight:bold;">${totalCreditWords}</div>
              </div>

              <div class="row">
                <div class="label">البيان:</div>
                <div style="flex:1; border-bottom:1px dotted #000; padding-bottom:4px;">${entry.description || ''}</div>
              </div>

              <table class="details-table">
                <thead>
                  <tr>
                    <th style="width:70px;">م</th>
                    <th>اسم الحساب</th>
                    <th style="width:140px;">مدين</th>
                    <th style="width:140px;">دائن</th>
                    <th>البيان</th>
                  </tr>
                </thead>
                <tbody>
                  ${entryWithDetails.details?.map((detail: any) => {
                    const isDebit = parseFloat(detail.debit_amount||0) > 0
                    const isCredit = parseFloat(detail.credit_amount||0) > 0
                    const cls = isDebit ? 'debit' : (isCredit ? 'credit' : '')
                    return `
                      <tr class="${cls}">
                        <td>${detail.id || detail.line_number || ''}</td>
                        <td>${detail.account_name || ''}</td>
                        <td>${isDebit ? Math.round(Number(detail.debit_amount||0)).toLocaleString('en-US', { maximumFractionDigits: 0 }) : '-'}</td>
                        <td>${isCredit ? Math.round(Number(detail.credit_amount||0)).toLocaleString('en-US', { maximumFractionDigits: 0 }) : '-'}</td>
                        <td>${detail.description || ''}</td>
                      </tr>`
                  }).join('') || ''}
                </tbody>
              </table>

              <div class="separator"></div>

              <div class="signatures">
                <div class="col"><div class="title">أمين الصندوق</div></div>
                <div class="col"><div class="title">المحاسب</div></div>
                <div class="col"><div class="title">المدير</div></div>
              </div>

              <script>
                window.onload = function() { window.print(); window.onafterprint = function(){ window.close(); } }
              </script>
            </body>
          </html>
        `)
        printWindow.document.close()
      }
    } catch (error) {
      console.error('خطأ في طباعة القيد:', error)
      alert('حدث خطأ أثناء طباعة القيد')
    }
  }

  const filteredEntries = entries.filter(entry =>
    entry.entry_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
    entry.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'draft': { label: 'مسودة', variant: 'secondary' as const },
      'approved': { label: 'معتمد', variant: 'default' as const },
      'cancelled': { label: 'ملغي', variant: 'destructive' as const }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  const { totalDebit, totalCredit } = calculateTotals()
  const balanced = isBalanced()

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50">
        <div className="space-y-6 p-6 bg-white min-h-screen">
        {/* العنوان والأدوات */}
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-3 space-x-reverse">
            <BookOpen className="h-8 w-8 text-purple-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">القيود اليومية</h1>
              <p className="text-gray-600">إدارة القيود المحاسبية اليومية</p>
            </div>
          </div>

          <Button onClick={() => setShowAddDialog(true)}>
            <Plus className="h-4 w-4 ml-2" />
            قيد يومي جديد
          </Button>
        </div>

        {/* جدول القيود */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
            <CardTitle>القيود اليومية</CardTitle>
            <div className="relative w-80">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="البحث في القيود اليومية..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">جاري تحميل القيود...</p>
              </div>
            ) : filteredEntries.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <BookOpen className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>لا توجد قيود يومية</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 border-b">
                    <tr>
                      <th className="text-right p-3 font-semibold text-black">رقم القيد</th>
                      <th className="text-right p-3 font-semibold text-black">التاريخ</th>
                      <th className="text-right p-3 font-semibold text-black">الحساب الدائن</th>
                      <th className="text-right p-3 font-semibold text-black">الحساب المدين</th>
                      <th className="text-right p-3 font-semibold text-black">مدين</th>
                      <th className="text-right p-3 font-semibold text-black">دائن</th>
                      <th className="text-right p-3 font-semibold text-black">البيان</th>
                      <th className="text-center p-3 font-semibold text-black">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredEntries.map((entry, entryIndex) => {
                      // إذا كان القيد يحتوي على تفاصيل، عرض كل تفصيل في سطر منفصل
                      if (entry.details && entry.details.length > 0) {
                        return entry.details.map((detail: any, detailIndex: number) => (
                          <tr key={`${entry.id}-${detail.id || detailIndex}`} className={`border-b hover:bg-gray-50 ${entryIndex % 2 === 0 ? 'bg-white' : 'bg-gray-25'}`}>
                            <td className="p-3">
                              <div className="flex items-center space-x-2 space-x-reverse">
                                <span className="font-mono text-black font-medium">
                                  {entry.entry_number}
                                </span>
                                {detailIndex === 0 && getStatusBadge(entry.status)}
                              </div>
                            </td>
                            <td className="p-3 text-black">
                              {new Date(entry.entry_date).toLocaleDateString('en-GB')}
                            </td>
                            <td className="p-3 text-black">
                              {detail.credit_amount > 0 ? (
                                <div>
                                  <div className="font-medium">{detail.account_name}</div>
                                </div>
                              ) : '-'}
                            </td>
                            <td className="p-3 text-black">
                              {detail.debit_amount > 0 ? (
                                <div>
                                  <div className="font-medium">{detail.account_name}</div>
                                </div>
                              ) : '-'}
                            </td>
                            <td className="p-3 text-right">
                              <span className="font-bold text-black">
                                {detail.debit_amount > 0 ? parseFloat(detail.debit_amount).toLocaleString() + ' ر.ي' : '-'}
                              </span>
                            </td>
                            <td className="p-3 text-right">
                              <span className="font-bold text-black">
                                {detail.credit_amount > 0 ? parseFloat(detail.credit_amount).toLocaleString() + ' ر.ي' : '-'}
                              </span>
                            </td>
                            <td className="p-3 text-black max-w-xs truncate" title={detail.description || entry.description}>
                              {detail.description || entry.description}
                            </td>
                            <td className="p-3">
                              {detailIndex === 0 && (
                                <div className="flex justify-center space-x-1 space-x-reverse">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleView(entry)}
                                    className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                                    title="مشاهدة"
                                  >
                                    <Eye className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleEdit(entry)}
                                    className="text-orange-600 hover:text-orange-700 hover:bg-orange-50"
                                    title="تعديل"
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleDelete(entry.id)}
                                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                    title="حذف"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handlePrint(entry)}
                                    className="text-gray-600 hover:text-gray-700 hover:bg-gray-50"
                                    title="طباعة"
                                  >
                                    <Printer className="h-4 w-4" />
                                  </Button>
                                </div>
                              )}
                            </td>
                          </tr>
                        ))
                      } else {
                        // إذا لم يكن هناك تفاصيل، عرض القيد كما هو
                        return (
                          <tr key={entry.id} className={`border-b hover:bg-gray-50 ${entryIndex % 2 === 0 ? 'bg-white' : 'bg-gray-25'}`}>
                            <td className="p-3">
                              <div className="flex items-center space-x-2 space-x-reverse">
                                <span className="font-mono text-blue-600 font-medium">
                                  {entry.entry_number}
                                </span>
                                {getStatusBadge(entry.status)}
                              </div>
                            </td>
                            <td className="p-3 text-gray-600">
                              {new Date(entry.entry_date).toLocaleDateString('en-GB')}
                            </td>
                            <td className="p-3">
                              <span className="font-bold text-blue-600">
                                {entry.total_debit.toLocaleString()} ر.ي
                              </span>
                            </td>
                            <td className="p-3 text-gray-600">-</td>
                            <td className="p-3 text-gray-600">-</td>
                            <td className="p-3 text-gray-600 max-w-xs truncate" title={entry.description}>
                              {entry.description}
                            </td>
                            <td className="p-3">
                              <div className="flex justify-center space-x-1 space-x-reverse">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleView(entry)}
                                  className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                                  title="مشاهدة"
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleEdit(entry)}
                                  className="text-orange-600 hover:text-orange-700 hover:bg-orange-50"
                                  title="تعديل"
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDelete(entry.id)}
                                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                  title="حذف"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handlePrint(entry)}
                                  className="text-gray-600 hover:text-gray-700 hover:bg-gray-50"
                                  title="طباعة"
                                >
                                  <Printer className="h-4 w-4" />
                                </Button>
                              </div>
                            </td>
                          </tr>
                        )
                      }
                    })}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* نموذج إضافة/تعديل القيد */}
        <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
          <DialogContent className="max-w-7xl max-h-[95vh] overflow-y-auto p-4">
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* الصف الأول: التاريخ والعملة والبيان والقوائم */}
              <div className="grid grid-cols-8 gap-3">
                <div className="col-span-1">
                  <Label htmlFor="entry_date" className="text-xs font-medium text-black mb-1 block">
                    📅 التاريخ
                  </Label>
                  <Input
                    id="entry_date"
                    type="date"
                    value={formData.entry_date}
                    onChange={(e) => setFormData({...formData, entry_date: e.target.value})}
                    required
                    className="h-8 text-sm"
                  />
                </div>

                <div className="col-span-1">
                  <Label htmlFor="currency_id" className="text-xs font-medium text-yellow-700 mb-1 block">
                    💱 العملة
                  </Label>
                  <Select value={formData.currency_id} onValueChange={(value) => setFormData({...formData, currency_id: value})}>
                    <SelectTrigger className="h-8 text-sm">
                      <SelectValue placeholder="اختر العملة" />
                    </SelectTrigger>
                    <SelectContent>
                      {currencies.map((currency) => (
                        <SelectItem key={currency.id} value={currency.id.toString()}>
                          {currency.currency_name} ({currency.symbol})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-2">
                  <Label htmlFor="description" className="text-xs font-medium text-black mb-1 block">
                    📝 البيان
                  </Label>
                  <Input
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    required
                    placeholder="وصف مختصر للقيد..."
                    className="h-8 text-sm"
                  />
                </div>

                <div className="col-span-1">
                  <Label htmlFor="cost_center_id" className="text-xs font-medium text-black mb-1 block">
                    🏢 م.التكلفة
                  </Label>
                  <Select value={formData.cost_center_id} onValueChange={(value) => setFormData({...formData, cost_center_id: value})}>
                    <SelectTrigger className="h-8 text-sm">
                      <SelectValue placeholder="اختر مركز التكلفة" />
                    </SelectTrigger>
                    <SelectContent className="max-h-60">
                      <SelectItem value="0">بدون مركز تكلفة</SelectItem>
                      {costCenters.map((center) => (
                        <SelectItem key={center.id} value={center.id.toString()}>
                          <span className="font-medium text-black">{center.center_name}</span>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-1">
                  <Label htmlFor="service_id" className="text-xs font-medium text-teal-700 mb-1 block">
                    🔧 الخدمة
                  </Label>
                  <Select value={formData.service_id} onValueChange={(value) => setFormData({...formData, service_id: value})}>
                    <SelectTrigger className="h-8 text-sm">
                      <SelectValue placeholder="اختر الخدمة" />
                    </SelectTrigger>
                    <SelectContent className="max-h-60">
                      <SelectItem value="0">بدون خدمة</SelectItem>
                      {services.map((service) => (
                        <SelectItem key={service.id} value={service.id.toString()}>
                          <span className="font-medium text-black">{service.name}</span>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-1">
                  <Label htmlFor="reference_number" className="text-xs font-medium text-blue-700 mb-1 block">
                    📄 المرجع
                  </Label>
                  <Input
                    id="reference_number"
                    value={formData.reference_number}
                    onChange={(e) => setFormData({...formData, reference_number: e.target.value})}
                    placeholder="رقم المرجع"
                    className="h-8 text-sm"
                  />
                </div>
              </div>

              {/* تفاصيل القيد */}
              <Card className="shadow-md">
                <CardHeader className="bg-gradient-to-r from-green-50 to-green-100 border-b">
                  <CardTitle className="flex items-center text-black">
                    📊 تفاصيل القيد
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  {/* جدول تفاصيل القيد */}
                  <div className="space-y-4">
                    {/* عناوين الجدول */}
                    <div className="grid grid-cols-12 gap-2 bg-gray-50 p-3 rounded-lg font-semibold text-sm text-black">
                      <div className="col-span-3">الحساب</div>
                      <div className="col-span-2 text-center">مدين</div>
                      <div className="col-span-2 text-center">دائن</div>
                      <div className="col-span-2">القضية</div>
                      <div className="col-span-2">البيان</div>
                      <div className="col-span-1 text-center">إجراءات</div>
                    </div>

                    {/* صفوف تفاصيل القيد */}
                    {entryDetails.map((detail, index) => (
                      <div key={index} className="grid grid-cols-12 gap-2 items-center p-2 border rounded-lg">
                        {/* الحساب */}
                        <div className="col-span-3">
                          <select
                            value={detail.account_id?.toString() || ''}
                            onChange={(e) => updateDetailLine(index, 'account_id', e.target.value)}
                            className="w-full h-8 text-sm p-2 border border-gray-300 rounded-md bg-white focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                            style={{ color: 'black' }}
                          >
                            <option value="" style={{ color: 'black' }}>اختر الحساب...</option>
                            {/* الأصول */}
                            <optgroup label="💰 الأصول">
                              {accounts
                                .filter(account => account.account_type === 'أصول' && account.allow_transactions)
                                .map(account => (
                                  <option key={account.id} value={account.id.toString()} style={{ color: 'black' }}>
                                    {account.account_name} ({account.account_code})
                                    {account.is_linked_record && account.original_table && (
                                      ` - ${account.original_table === 'clients' ? 'عميل' :
                                           account.original_table === 'employees' ? 'موظف' :
                                           account.original_table === 'suppliers' ? 'مورد' : ''}`
                                    )}
                                  </option>
                                ))}
                            </optgroup>

                            {/* الخصوم */}
                            <optgroup label="📊 الخصوم">
                              {accounts
                                .filter(account => account.account_type === 'خصوم' && account.allow_transactions)
                                .map(account => (
                                  <option key={account.id} value={account.id.toString()} style={{ color: 'black' }}>
                                    {account.account_name} ({account.account_code})
                                    {account.is_linked_record && account.original_table && (
                                      ` - ${account.original_table === 'clients' ? 'عميل' :
                                           account.original_table === 'employees' ? 'موظف' :
                                           account.original_table === 'suppliers' ? 'مورد' : ''}`
                                    )}
                                  </option>
                                ))}
                            </optgroup>

                            {/* حقوق الملكية */}
                            <optgroup label="💼 حقوق الملكية">
                              {accounts
                                .filter(account => account.account_type === 'حقوق الملكية' && account.allow_transactions)
                                .map(account => (
                                  <option key={account.id} value={account.id.toString()} style={{ color: 'black' }}>
                                    {account.account_name} ({account.account_code})
                                    {account.is_linked_record && account.original_table && (
                                      ` - ${account.original_table === 'clients' ? 'عميل' :
                                           account.original_table === 'employees' ? 'موظف' :
                                           account.original_table === 'suppliers' ? 'مورد' : ''}`
                                    )}
                                  </option>
                                ))}
                            </optgroup>

                            {/* الإيرادات */}
                            <optgroup label="📈 الإيرادات">
                              {accounts
                                .filter(account => account.account_type === 'إيرادات' && account.allow_transactions)
                                .map(account => (
                                  <option key={account.id} value={account.id.toString()} style={{ color: 'black' }}>
                                    {account.account_name} ({account.account_code})
                                    {account.is_linked_record && account.original_table && (
                                      ` - ${account.original_table === 'clients' ? 'عميل' :
                                           account.original_table === 'employees' ? 'موظف' :
                                           account.original_table === 'suppliers' ? 'مورد' : ''}`
                                    )}
                                  </option>
                                ))}
                            </optgroup>

                            {/* المصروفات */}
                            <optgroup label="📉 المصروفات">
                              {accounts
                                .filter(account => account.account_type === 'مصروفات' && account.allow_transactions)
                                .map(account => (
                                  <option key={account.id} value={account.id.toString()} style={{ color: 'black' }}>
                                    {account.account_name} ({account.account_code})
                                    {account.is_linked_record && account.original_table && (
                                      ` - ${account.original_table === 'clients' ? 'عميل' :
                                           account.original_table === 'employees' ? 'موظف' :
                                           account.original_table === 'suppliers' ? 'مورد' : ''}`
                                    )}
                                  </option>
                                ))}
                            </optgroup>
                          </select>

                        </div>

                        {/* مدين */}
                        <div className="col-span-2">
                          <Input
                            type="text"
                            inputMode="numeric"
                            pattern="\\d*"
                            value={detail.debit_amount === '' ? '' : String(detail.debit_amount)}
                            onChange={(e) => updateDetailLine(index, 'debit_amount', e.target.value)}
                            placeholder=""
                            className="h-8 text-sm text-center"
                          />
                        </div>

                        {/* دائن */}
                        <div className="col-span-2">
                          <Input
                            type="text"
                            inputMode="numeric"
                            pattern="\\d*"
                            value={detail.credit_amount === '' ? '' : String(detail.credit_amount)}
                            onChange={(e) => updateDetailLine(index, 'credit_amount', e.target.value)}
                            placeholder=""
                            className="h-8 text-sm text-center"
                          />
                        </div>

                        {/* القضية */}
                        <div className="col-span-2">
                          <Select
                            value={detail.case_id?.toString() || '0'}
                            onValueChange={(value) => updateDetailLine(index, 'case_id', parseInt(value))}
                          >
                            <SelectTrigger className="h-8 text-sm">
                              <SelectValue placeholder="اختر القضية" />
                            </SelectTrigger>
                            <SelectContent className="max-h-60">
                              <SelectItem value="0">بدون قضية</SelectItem>
                              {cases.map((caseItem) => (
                                <SelectItem key={caseItem.id} value={caseItem.id.toString()}>
                                  <span className="font-medium text-black">{caseItem.title}</span>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        {/* البيان */}
                        <div className="col-span-2">
                          <Input
                            value={detail.description || ''}
                            onChange={(e) => updateDetailLine(index, 'description', e.target.value)}
                            placeholder="بيان الحساب..."
                            className="h-8 text-sm"
                          />
                        </div>

                        {/* إجراءات */}
                        <div className="col-span-1 text-center">
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeDetailLine(index)}
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}

                    {/* زر إضافة صف جديد */}
                    <Button
                      type="button"
                      variant="outline"
                      onClick={addDetailLine}
                      className="w-full border-dashed border-2 border-gray-300 hover:border-blue-400 hover:bg-blue-50"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      إضافة صف جديد
                    </Button>

                    {/* ملخص المبالغ */}
                    <div className="grid grid-cols-3 gap-4 mt-6 p-4 bg-gray-50 rounded-lg">
                      <div className="text-center">
                        <div className="text-sm text-black">إجمالي المدين</div>
                        <div className="text-lg font-bold text-black">
                          {totalDebit.toLocaleString()} ر.ي
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm text-black">إجمالي الدائن</div>
                        <div className="text-lg font-bold text-black">
                          {totalCredit.toLocaleString()} ر.ي
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm text-black">الفرق</div>
                        <div className={`text-lg font-bold ${balanced ? 'text-black' : 'text-red-600'}`}>
                          {Math.abs(totalDebit - totalCredit).toLocaleString()} ر.ي
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* أزرار الحفظ */}
              <div className="flex justify-between items-center pt-6 border-t">
                <Button type="button" variant="outline" onClick={() => setShowAddDialog(false)}>
                  إلغاء
                </Button>

                <div className="flex space-x-2 space-x-reverse">
                  <Button
                    type="submit"
                    disabled={!balanced}
                    className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400"
                  >
                    {editingEntry ? 'تحديث القيد' : 'حفظ القيد'}
                  </Button>
                </div>
              </div>

              {/* تحذير عدم التوازن */}
              {!balanced && (
                <Card className="border-red-200 bg-red-50">
                  <CardContent className="p-4">
                    <div className="flex items-center text-red-700">
                      <AlertCircle className="h-5 w-5 mr-2" />
                      <span className="font-medium">⚠️ يجب أن يكون مجموع المدين مساوياً لمجموع الدائن لحفظ القيد</span>
                    </div>
                  </CardContent>
                </Card>
              )}
            </form>
          </DialogContent>
        </Dialog>
        </div>
      </div>
    </MainLayout>
  )
}
