'use client'

import { useEffect, useMemo, useState } from 'react'
import Link from 'next/link'
import { useAuth } from '@/hooks/useAuth'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Plus, Edit, Trash2, Building2, ArrowLeft } from 'lucide-react'

interface Department {
  id: number
  dept_code: string
  dept_name: string
  description?: string
  is_active: boolean
  created_date?: string
  updated_date?: string
}

export default function DepartmentsPage() {
  const { hasPermission, isAdmin, user } = useAuth()

  // صلاحيات الصفحة
  const canView = useMemo(
    () => isAdmin() || hasPermission('departments:view'),
    [hasPermission, isAdmin, user?.id, user?.role, (user?.permissions || []).join(',')]
  )
  const canCreate = useMemo(
    () => isAdmin() || hasPermission('departments:create'),
    [hasPermission, isAdmin, user?.id, user?.role, (user?.permissions || []).join(',')]
  )
  const canUpdate = useMemo(
    () => isAdmin() || hasPermission('departments:update'),
    [hasPermission, isAdmin, user?.id, user?.role, (user?.permissions || []).join(',')]
  )
  const canDelete = useMemo(
    () => isAdmin() || hasPermission('departments:delete'),
    [hasPermission, isAdmin, user?.id, user?.role, (user?.permissions || []).join(',')]
  )

  const [departments, setDepartments] = useState<Department[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [showDialog, setShowDialog] = useState(false)
  const [editingDept, setEditingDept] = useState<Department | null>(null)

  const [formData, setFormData] = useState({
    dept_code: '',
    dept_name: '',
    description: '',
    is_active: true,
  })

  // جلب الأقسام
  const fetchDepartments = async () => {
    console.log('🔍 فحص صلاحيات الأقسام:', {
      canView,
      isAdmin: isAdmin(),
      hasPermission: hasPermission('departments:view'),
      userType: user?.user_type,
      userRole: user?.role,
      userPermissions: user?.permissions?.slice(0, 5) // أول 5 صلاحيات للمراجعة
    })

    if (!canView) {
      console.warn('⚠️ لا توجد صلاحية لعرض الأقسام')
      setLoading(false);
      return
    }

    try {
      console.log('📡 جلب الأقسام من API...')
      const res = await fetch('/api/departments')
      if (res.ok) {
        const data = await res.json()
        console.log('✅ تم جلب الأقسام:', data)
        setDepartments(data.departments || data.items || [])
      } else {
        console.error('❌ فشل جلب الأقسام - Status:', res.status)
        const errorText = await res.text()
        console.error('❌ تفاصيل الخطأ:', errorText)
      }
    } catch (e) {
      console.error('❌ خطأ في جلب الأقسام:', e)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => { fetchDepartments() }, [canView])

  const resetForm = () => {
    setFormData({ dept_code: '', dept_name: '', description: '', is_active: true })
    setEditingDept(null)
  }

  const handleAdd = () => {
    resetForm()
    setShowDialog(true)
  }

  const handleEdit = (dept: Department) => {
    setFormData({
      dept_code: dept.dept_code,
      dept_name: dept.dept_name,
      description: dept.description || '',
      is_active: dept.is_active,
    })
    setEditingDept(dept)
    setShowDialog(true)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const method = editingDept ? 'PUT' : 'POST'
      const body = editingDept ? { ...formData, id: editingDept.id } : formData
      const res = await fetch('/api/departments', {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body),
      })
      const result = await res.json()
      if (res.ok && (result.success ?? true)) {
        alert(result.message || 'تم الحفظ بنجاح')
        setShowDialog(false)
        resetForm()
        fetchDepartments()
      } else {
        alert(result.error || 'تعذر حفظ القسم')
      }
    } catch (e) {
      console.error('خطأ في الحفظ:', e)
      alert('حدث خطأ أثناء حفظ القسم')
    }
  }

  const handleDelete = async (id: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا القسم؟')) return
    try {
      const res = await fetch(`/api/departments?id=${id}`, { method: 'DELETE' })
      const result = await res.json().catch(() => ({}))
      if (res.ok && (result.success ?? true)) {
        alert(result.message || 'تم الحذف بنجاح')
        fetchDepartments()
      } else {
        alert(result.error || 'تعذر حذف القسم')
      }
    } catch (e) {
      console.error('خطأ في الحذف:', e)
      alert('حدث خطأ أثناء حذف القسم')
    }
  }

  const filtered = departments.filter(d =>
    d.dept_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    d.dept_code?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (!canView) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <Card>
            <CardContent className="p-8 text-center text-red-600">
              ليس لديك صلاحية لعرض الأقسام
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto text-center py-12 text-gray-600">جاري تحميل الأقسام...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <Link href="/settings" className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            العودة إلى الإعدادات
          </Link>
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <Building2 className="h-8 w-8 mr-3 text-blue-600" />
                إدارة الأقسام
              </h1>
              <p className="text-gray-600 mt-2">إضافة وتعديل وحذف الأقسام داخل النظام</p>
            </div>
            {canCreate && (
              <Button onClick={handleAdd} className="bg-blue-600 hover:bg-blue-700">
                <Plus className="h-4 w-4 mr-2" />
                إضافة قسم
              </Button>
            )}
          </div>
        </div>

        {/* البحث */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex gap-4">
              <div className="flex-1">
                <Input
                  placeholder="بحث بالاسم أو الرمز..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* الجدول */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة الأقسام ({filtered.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {!canView ? (
              <div className="text-center py-12">
                <Building2 className="h-12 w-12 text-red-400 mx-auto mb-4" />
                <p className="text-red-500 font-semibold">ليس لديك صلاحية لعرض الأقسام</p>
                <p className="text-gray-500 text-sm mt-2">
                  المستخدم: {user?.username} | النوع: {user?.user_type} | الدور: {user?.role}
                </p>
                <p className="text-gray-500 text-xs mt-1">
                  isAdmin: {isAdmin() ? 'نعم' : 'لا'} | hasPermission: {hasPermission('departments:view') ? 'نعم' : 'لا'}
                </p>
              </div>
            ) : filtered.length === 0 ? (
              <div className="text-center py-12">
                <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">لا توجد أقسام</p>
                <p className="text-gray-400 text-sm mt-2">
                  تم جلب {departments.length} قسم من قاعدة البيانات
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-right p-3 font-semibold">الرمز</th>
                      <th className="text-right p-3 font-semibold">اسم القسم</th>
                      <th className="text-right p-3 font-semibold">الوصف</th>
                      <th className="text-center p-3 font-semibold">الحالة</th>
                      <th className="text-center p-3 font-semibold">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filtered.map((d) => (
                      <tr key={d.id} className="border-b hover:bg-gray-50">
                        <td className="p-3 font-mono text-blue-600">{d.dept_code}</td>
                        <td className="p-3 font-medium">{d.dept_name}</td>
                        <td className="p-3 text-gray-600">{d.description || '-'}</td>
                        <td className="p-3 text-center">
                          <Badge className={d.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                            {d.is_active ? 'نشط' : 'غير نشط'}
                          </Badge>
                        </td>
                        <td className="p-3 text-center">
                          <div className="flex justify-center space-x-2 space-x-reverse">
                            {canUpdate && (
                              <Button variant="ghost" size="sm" onClick={() => handleEdit(d)} className="text-blue-600 hover:text-blue-700 hover:bg-blue-50">
                                <Edit className="h-4 w-4" />
                              </Button>
                            )}
                            {canDelete && (
                              <Button variant="ghost" size="sm" onClick={() => handleDelete(d.id)} className="text-red-600 hover:text-red-700 hover:bg-red-50">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* نموذج */}
        <Dialog open={showDialog} onOpenChange={setShowDialog}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>{editingDept ? 'تعديل قسم' : 'إضافة قسم جديد'}</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="dept_code">رمز القسم *</Label>
                <Input id="dept_code" value={formData.dept_code} onChange={(e) => setFormData({ ...formData, dept_code: e.target.value })} placeholder="مثال: D001" required />
              </div>
              <div>
                <Label htmlFor="dept_name">اسم القسم *</Label>
                <Input id="dept_name" value={formData.dept_name} onChange={(e) => setFormData({ ...formData, dept_name: e.target.value })} placeholder="أدخل اسم القسم" required />
              </div>
              <div>
                <Label htmlFor="description">الوصف</Label>
                <Textarea id="description" value={formData.description} onChange={(e) => setFormData({ ...formData, description: e.target.value })} rows={3} placeholder="وصف مختصر للقسم" />
              </div>
              <div className="flex justify-end space-x-2 space-x-reverse pt-4">
                <Button type="button" variant="outline" onClick={() => setShowDialog(false)}>إلغاء</Button>
                <Button type="submit" className="bg-blue-600 hover:bg-blue-700">{editingDept ? 'تحديث' : 'إضافة'}</Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  )
}
