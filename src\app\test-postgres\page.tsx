'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Loader2, Database, Server, CheckCircle, XCircle, AlertCircle } from 'lucide-react'

interface TestResult {
  config: string
  password: string
  status: 'success' | 'failed'
  error: string
  databases: Array<{ name: string; size: string }>
  version: string
  targetDatabases: any[]
}

interface ConnectionTest {
  success: boolean
  message: string
  problemAnalysis?: string
  detailedError?: string
  testResults: TestResult[]
  recommendedConnection: any
  solutions: string[]
}

export default function TestPostgresPage() {
  const [testing, setTesting] = useState(false)
  const [results, setResults] = useState<ConnectionTest | null>(null)

  const runTest = async () => {
    setTesting(true)
    setResults(null)
    
    try {
      const response = await fetch('/api/test-postgres')
      const data = await response.json()
      setResults(data)
    } catch (error) {
      console.error('خطأ في اختبار الاتصال:', error)
      setResults({
        success: false,
        message: 'فشل في تشغيل اختبار الاتصال',
        testResults: [],
        recommendedConnection: null,
        solutions: ['تحقق من تشغيل الخادم', 'تحقق من الاتصال بالإنترنت']
      })
    } finally {
      setTesting(false)
    }
  }

  const updateConfig = async () => {
    if (!results?.recommendedConnection) return
    
    try {
      const response = await fetch('/api/update-config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          connection: results.recommendedConnection
        })
      })
      
      if (response.ok) {
        alert('تم تحديث الإعدادات بنجاح! سيتم إعادة تشغيل الخادم...')
        window.location.reload()
      } else {
        alert('فشل في تحديث الإعدادات')
      }
    } catch (error) {
      console.error('خطأ في تحديث الإعدادات:', error)
      alert('خطأ في تحديث الإعدادات')
    }
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl" dir="rtl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">اختبار اتصال PostgreSQL</h1>
        <p className="text-gray-600">
          فحص شامل للعثور على إعدادات PostgreSQL الصحيحة وقواعد البيانات المتاحة
        </p>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Server className="h-5 w-5" />
            اختبار الاتصال
          </CardTitle>
          <CardDescription>
            سيتم اختبار عدة إعدادات مختلفة للعثور على الاتصال الصحيح
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Button
              onClick={runTest}
              disabled={testing}
              className="w-full"
            >
              {testing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  جاري الاختبار...
                </>
              ) : (
                <>
                  <Database className="mr-2 h-4 w-4" />
                  بدء اختبار الاتصال
                </>
              )}
            </Button>

            {testing && (
              <div className="text-center text-sm text-gray-600">
                <p>جاري اختبار عدة إعدادات مختلفة...</p>
                <p>قد يستغرق هذا بضع ثوان</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {results && (
        <div className="space-y-6">
          {/* نتيجة الاختبار الرئيسية */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {results.success ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-500" />
                )}
                نتيجة الاختبار
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className={`p-4 rounded-lg ${results.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                <p className={`font-medium mb-2 ${results.success ? 'text-green-800' : 'text-red-800'}`}>
                  {results.message}
                </p>

                {!results.success && results.problemAnalysis && (
                  <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded">
                    <p className="text-yellow-800 font-medium">تحليل المشكلة:</p>
                    <p className="text-yellow-700 text-sm mt-1">{results.problemAnalysis}</p>
                  </div>
                )}

                {!results.success && results.detailedError && (
                  <details className="mt-3">
                    <summary className="cursor-pointer text-sm text-gray-600 hover:text-gray-800">
                      عرض تفاصيل الخطأ التقنية
                    </summary>
                    <div className="mt-2 p-2 bg-gray-100 rounded text-xs font-mono text-gray-700">
                      {results.detailedError}
                    </div>
                  </details>
                )}
              </div>
            </CardContent>
          </Card>

          {/* الاتصال الموصى به */}
          {results.success && results.recommendedConnection && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  الإعدادات الصحيحة
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">المضيف</label>
                    <p className="font-mono">{results.recommendedConnection.host}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">المنفذ</label>
                    <p className="font-mono">{results.recommendedConnection.port}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">المستخدم</label>
                    <p className="font-mono">{results.recommendedConnection.user}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">كلمة المرور</label>
                    <p className="font-mono">{results.recommendedConnection.password || 'فارغة'}</p>
                  </div>
                </div>

                {results.recommendedConnection.version && (
                  <div className="mb-4">
                    <label className="text-sm font-medium text-gray-500">إصدار PostgreSQL</label>
                    <p>{results.recommendedConnection.version}</p>
                  </div>
                )}

                {results.recommendedConnection.databases && results.recommendedConnection.databases.length > 0 && (
                  <div className="mb-4">
                    <label className="text-sm font-medium text-gray-500 block mb-2">قواعد البيانات المتاحة</label>
                    <div className="flex flex-wrap gap-2">
                      {results.recommendedConnection.databases.map((db: any, index: number) => (
                        <Badge key={index} variant="outline">
                          {db.name} ({db.size})
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {results.recommendedConnection.targetDatabases && results.recommendedConnection.targetDatabases.length > 0 && (
                  <div className="mb-4">
                    <label className="text-sm font-medium text-gray-500 block mb-2">قواعد البيانات المطلوبة الموجودة</label>
                    <div className="space-y-2">
                      {results.recommendedConnection.targetDatabases.map((db: any, index: number) => (
                        <div key={index} className="p-3 bg-green-50 border border-green-200 rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <span className="font-medium text-green-800">{db.name || db}</span>
                            {typeof db === 'object' && db.tables && (
                              <Badge variant="secondary">{db.tables} جدول</Badge>
                            )}
                          </div>
                          {typeof db === 'object' && db.importantTables && db.importantTables.length > 0 && (
                            <div className="text-sm text-green-700">
                              الجداول المهمة: {db.importantTables.map((t: any) => `${t.name} (${t.count})`).join(', ')}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <Button onClick={updateConfig} className="w-full">
                  تحديث إعدادات المشروع
                </Button>
              </CardContent>
            </Card>
          )}

          {/* تفاصيل جميع الاختبارات */}
          <Card>
            <CardHeader>
              <CardTitle>تفاصيل الاختبارات</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {results.testResults.map((test, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-mono text-sm">{test.config}</span>
                      <Badge variant={test.status === 'success' ? 'default' : 'destructive'}>
                        {test.status === 'success' ? 'نجح' : 'فشل'}
                      </Badge>
                    </div>
                    
                    <div className="text-sm text-gray-600 mb-2">
                      كلمة المرور: {test.password}
                    </div>

                    {test.status === 'success' ? (
                      <div className="space-y-2">
                        {test.version && (
                          <div className="text-sm">
                            <span className="font-medium">الإصدار:</span> {test.version}
                          </div>
                        )}
                        {test.databases.length > 0 && (
                          <div className="text-sm">
                            <span className="font-medium">قواعد البيانات:</span> {test.databases.length}
                          </div>
                        )}
                        {test.targetDatabases.length > 0 && (
                          <div className="text-sm text-green-600">
                            <span className="font-medium">قواعد البيانات المطلوبة:</span> {test.targetDatabases.length}
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="text-sm text-red-600">
                        {test.error}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* الحلول المقترحة */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5 text-blue-500" />
                {results.success ? 'الخطوات التالية' : 'الحلول المقترحة'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {results.solutions.map((solution, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="text-blue-500 mt-1">•</span>
                    <span>{solution}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
