/**
 * خادم فوري - يعمل مباشرة بدون انتظار
 */

const http = require('http');
const { spawn } = require('child_process');

console.log('⚡ خادم فوري - يعمل في ثوانٍ!');
console.log('='.repeat(40));

// إعدادات
const CONFIG = {
  7443: { name: 'محمد', db: 'mohammi', color: '#cca967' },
  8914: { name: 'الربعي', db: 'rubaie', color: '#2563eb' }
};

let nextDev = null;

// بدء Next.js في وضع التطوير (أسرع)
function startNextDev() {
  console.log('🔄 بدء Next.js (وضع التطوير)...');
  
  nextDev = spawn('npx', ['next', 'dev', '-p', '3000'], {
    cwd: __dirname,
    stdio: 'pipe'
  });

  nextDev.stdout.on('data', (data) => {
    const output = data.toString();
    if (output.includes('Ready')) {
      console.log('✅ Next.js جاهز على المنفذ 3000');
    }
  });

  nextDev.on('error', (error) => {
    console.error('❌ خطأ في Next.js:', error.message);
  });
}

// إنشاء خادم بسيط
function createSimpleServer(port, config) {
  const server = http.createServer((req, res) => {
    // إعداد CORS
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', '*');
    res.setHeader('Access-Control-Allow-Headers', '*');
    
    if (req.method === 'OPTIONS') {
      res.writeHead(200);
      res.end();
      return;
    }

    // محاولة التوجيه إلى Next.js
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: req.url,
      method: req.method,
      headers: {
        ...req.headers,
        'x-forwarded-port': port,
        'x-database': config.db,
        'x-company': config.name
      }
    };

    const proxyReq = http.request(options, (proxyRes) => {
      res.writeHead(proxyRes.statusCode, proxyRes.headers);
      proxyRes.pipe(res);
    });

    proxyReq.on('error', () => {
      // إذا فشل التوجيه، اعرض صفحة بسيطة
      res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
      res.end(`
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
          <meta charset="utf-8">
          <title>نظام ${config.name}</title>
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { 
              font-family: 'Segoe UI', Tahoma, Arial, sans-serif; 
              background: linear-gradient(135deg, ${config.color}20, ${config.color}10);
              min-height: 100vh; 
              display: flex; 
              align-items: center; 
              justify-content: center;
            }
            .container { 
              background: white; 
              padding: 40px; 
              border-radius: 15px; 
              box-shadow: 0 10px 30px rgba(0,0,0,0.1);
              text-align: center;
              max-width: 500px;
              width: 90%;
            }
            h1 { 
              color: ${config.color}; 
              margin-bottom: 20px; 
              font-size: 2.5em;
            }
            .status { 
              background: #e8f5e8; 
              color: #2d5a2d; 
              padding: 15px; 
              border-radius: 8px; 
              margin: 20px 0;
              border-left: 4px solid #4caf50;
            }
            .info { 
              background: #f0f8ff; 
              padding: 20px; 
              border-radius: 8px; 
              margin: 20px 0;
              border-left: 4px solid ${config.color};
            }
            .refresh { 
              background: ${config.color}; 
              color: white; 
              padding: 12px 24px; 
              border: none; 
              border-radius: 6px; 
              cursor: pointer; 
              font-size: 16px;
              margin-top: 20px;
            }
            .refresh:hover { opacity: 0.9; }
            .time { color: #666; font-size: 14px; margin-top: 20px; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>🏢 نظام ${config.name}</h1>
            
            <div class="status">
              ✅ الخادم يعمل بنجاح
            </div>
            
            <div class="info">
              <strong>معلومات النظام:</strong><br>
              📊 قاعدة البيانات: ${config.db}<br>
              🌐 المنفذ: ${port}<br>
              🔗 النوع: ${port == 7443 || port == 8914 ? 'إنتاج (خارجي)' : 'تطوير (محلي)'}
            </div>
            
            <button class="refresh" onclick="location.reload()">
              🔄 تحديث الصفحة
            </button>
            
            <div class="time">
              آخر تحديث: ${new Date().toLocaleString('ar-SA')}
            </div>
          </div>
          
          <script>
            // تحديث تلقائي كل 30 ثانية
            setTimeout(() => location.reload(), 30000);
          </script>
        </body>
        </html>
      `);
    });

    req.pipe(proxyReq);
  });

  server.listen(port, '0.0.0.0', () => {
    console.log(`✅ ${config.name}: http://localhost:${port} (قاعدة بيانات: ${config.db})`);
  });

  return server;
}

// بدء الخوادم
console.log('🚀 بدء الخوادم...');

// بدء Next.js
startNextDev();

// بدء الخوادم الرئيسية
Object.entries(CONFIG).forEach(([port, config]) => {
  createSimpleServer(parseInt(port), config);
});

console.log('\n🎉 جميع الخوادم تعمل!');
console.log('\n📋 الروابط:');
console.log('   🏠 محلي: http://localhost:3000');
console.log('   🌐 محمد: http://localhost:7443 (https://mohammi.com:7443)');
console.log('   🌐 الربعي: http://localhost:8914 (https://mohammi.com:8914)');
console.log('\n💡 نصيحة: استخدم الروابط الخارجية للوصول من خارج السيرفر');

// معالجة الإيقاف
process.on('SIGINT', () => {
  console.log('\n🛑 إيقاف الخوادم...');
  if (nextDev) nextDev.kill();
  process.exit(0);
});
