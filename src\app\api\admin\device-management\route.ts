import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database-router'

// GET - جلب قائمة المستخدمين ومعرفات أجهزتهم
export async function GET(request: NextRequest) {
  try {
    console.log('📋 جلب قائمة المستخدمين ومعرفات الأجهزة...')

    const usersResult = await query(`
      SELECT 
        id, username, device_ids, device_login_required, 
        is_active, current_device_id, last_login,
        created_date, updated_date
      FROM users 
      ORDER BY 
        CASE WHEN username = 'admin' THEN 0 ELSE 1 END,
        username
    `)

    console.log(`✅ تم جلب ${usersResult.rows.length} مستخدم`)

    return NextResponse.json({
      success: true,
      users: usersResult.rows
    })

  } catch (error) {
    console.error('❌ خطأ في جلب بيانات المستخدمين:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب بيانات المستخدمين'
    }, { status: 500 })
  }
}

// POST - تحديث معرفات الأجهزة أو إعدادات المستخدم
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, userId, deviceIds, required } = body

    console.log(`🔧 تنفيذ العملية: ${action} للمستخدم ${userId}`)

    if (action === 'update_devices') {
      // تحديث معرفات الأجهزة
      if (!userId) {
        return NextResponse.json({
          success: false,
          error: 'معرف المستخدم مطلوب'
        }, { status: 400 })
      }

      // التحقق من وجود المستخدم
      const userCheck = await query(`
        SELECT username FROM users WHERE id = $1
      `, [userId])

      if (userCheck.rows.length === 0) {
        return NextResponse.json({
          success: false,
          error: 'المستخدم غير موجود'
        }, { status: 404 })
      }

      const username = userCheck.rows[0].username

      // تنظيف وتنسيق معرفات الأجهزة
      let cleanDeviceIds = null
      if (deviceIds && deviceIds.trim()) {
        // إزالة المسافات الزائدة وتنظيف الفواصل
        cleanDeviceIds = deviceIds
          .split(',')
          .map((id: string) => id.trim())
          .filter((id: string) => id.length > 0)
          .join(',')
      }

      // تحديث معرفات الأجهزة
      await query(`
        UPDATE users 
        SET device_ids = $1, updated_date = CURRENT_TIMESTAMP
        WHERE id = $2
      `, [cleanDeviceIds, userId])

      console.log(`✅ تم تحديث معرفات الأجهزة للمستخدم ${username}`)

      // تسجيل العملية
      await query(`
        INSERT INTO device_login_log (
          user_id, username, device_id, device_info, 
          login_status, failure_reason
        ) VALUES ($1, $2, $3, $4, $5, $6)
      `, [
        userId, username, 'ADMIN_UPDATE', 
        `تحديث معرفات الأجهزة: ${cleanDeviceIds || 'إزالة جميع الأجهزة'}`,
        'admin_action', 'تحديث من لوحة الإدارة'
      ])

      return NextResponse.json({
        success: true,
        message: 'تم تحديث معرفات الأجهزة بنجاح'
      })

    } else if (action === 'toggle_requirement') {
      // تبديل حالة تطلب معرف الجهاز
      if (!userId || required === undefined) {
        return NextResponse.json({
          success: false,
          error: 'بيانات غير مكتملة'
        }, { status: 400 })
      }

      // التحقق من أن المستخدم ليس admin
      const userCheck = await query(`
        SELECT username FROM users WHERE id = $1
      `, [userId])

      if (userCheck.rows.length === 0) {
        return NextResponse.json({
          success: false,
          error: 'المستخدم غير موجود'
        }, { status: 404 })
      }

      const username = userCheck.rows[0].username

      if (username === 'admin') {
        return NextResponse.json({
          success: false,
          error: 'لا يمكن تغيير إعدادات المدير'
        }, { status: 403 })
      }

      // تحديث حالة تطلب معرف الجهاز
      await query(`
        UPDATE users 
        SET device_login_required = $1, updated_date = CURRENT_TIMESTAMP
        WHERE id = $2
      `, [required, userId])

      console.log(`✅ تم ${required ? 'تفعيل' : 'إلغاء'} تطلب معرف الجهاز للمستخدم ${username}`)

      // تسجيل العملية
      await query(`
        INSERT INTO device_login_log (
          user_id, username, device_id, device_info, 
          login_status, failure_reason
        ) VALUES ($1, $2, $3, $4, $5, $6)
      `, [
        userId, username, 'ADMIN_SETTING', 
        `${required ? 'تفعيل' : 'إلغاء'} تطلب معرف الجهاز`,
        'admin_action', 'تحديث من لوحة الإدارة'
      ])

      return NextResponse.json({
        success: true,
        message: `تم ${required ? 'تفعيل' : 'إلغاء'} تطلب معرف الجهاز بنجاح`
      })

    } else {
      return NextResponse.json({
        success: false,
        error: 'عملية غير صحيحة'
      }, { status: 400 })
    }

  } catch (error) {
    console.error('❌ خطأ في تحديث بيانات الأجهزة:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث البيانات'
    }, { status: 500 })
  }
}

// DELETE - حذف جميع معرفات الأجهزة لمستخدم
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'معرف المستخدم مطلوب'
      }, { status: 400 })
    }

    // التحقق من وجود المستخدم
    const userCheck = await query(`
      SELECT username FROM users WHERE id = $1
    `, [userId])

    if (userCheck.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'المستخدم غير موجود'
      }, { status: 404 })
    }

    const username = userCheck.rows[0].username

    if (username === 'admin') {
      return NextResponse.json({
        success: false,
        error: 'لا يمكن حذف أجهزة المدير'
      }, { status: 403 })
    }

    // حذف جميع معرفات الأجهزة
    await query(`
      UPDATE users 
      SET device_ids = NULL, 
          current_device_id = NULL,
          updated_date = CURRENT_TIMESTAMP
      WHERE id = $1
    `, [userId])

    // إنهاء جميع الجلسات النشطة للمستخدم
    await query(`
      UPDATE active_sessions_enhanced 
      SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP
      WHERE user_id = $1
    `, [userId])

    console.log(`✅ تم حذف جميع أجهزة المستخدم ${username}`)

    // تسجيل العملية
    await query(`
      INSERT INTO device_login_log (
        user_id, username, device_id, device_info, 
        login_status, failure_reason
      ) VALUES ($1, $2, $3, $4, $5, $6)
    `, [
      userId, username, 'ADMIN_DELETE', 
      'حذف جميع معرفات الأجهزة وإنهاء الجلسات',
      'admin_action', 'حذف من لوحة الإدارة'
    ])

    return NextResponse.json({
      success: true,
      message: 'تم حذف جميع معرفات الأجهزة وإنهاء الجلسات بنجاح'
    })

  } catch (error) {
    console.error('❌ خطأ في حذف معرفات الأجهزة:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في حذف معرفات الأجهزة'
    }, { status: 500 })
  }
}
