'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Clock,
  Play,
  Pause,
  Square,
  Plus,
  Calendar,
  DollarSign,
  User,
  FileText,
  Timer,
  TrendingUp,
  Edit,
  Trash2
} from 'lucide-react'

interface TimeEntry {
  id: number
  case_id: number
  client_id: number
  employee_id: number
  start_time: string
  end_time: string
  duration_minutes: number
  task_description: string
  task_category: string
  hourly_rate: number
  billable_amount: number
  is_billable: boolean
  is_billed: boolean
  status: string
  notes: string
  employee_name: string
  client_name: string
  case_title: string
  case_number: string
  created_date: string
}

interface TimeStats {
  total_entries: number
  total_minutes: number
  billable_minutes: number
  total_billable_amount: number
  billed_amount: number
}

export default function TimeTrackingPage() {
  const [timeEntries, setTimeEntries] = useState<TimeEntry[]>([])
  const [stats, setStats] = useState<TimeStats>({
    total_entries: 0,
    total_minutes: 0,
    billable_minutes: 0,
    total_billable_amount: 0,
    billed_amount: 0
  })
  const [loading, setLoading] = useState(true)
  const [activeTimer, setActiveTimer] = useState<number | null>(null)
  const [currentTime, setCurrentTime] = useState(new Date())
  const [showAddModal, setShowAddModal] = useState(false)
  const [selectedEmployee, setSelectedEmployee] = useState('')
  const [selectedCase, setSelectedCase] = useState('')
  const [dateRange, setDateRange] = useState({
    start: new Date().toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  })

  // تحديث الوقت كل ثانية
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)
    return () => clearInterval(timer)
  }, [])

  // جلب تسجيلات الوقت
  const fetchTimeEntries = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        start_date: dateRange.start,
        end_date: dateRange.end
      })

      if (selectedEmployee) params.append('employee_id', selectedEmployee)
      if (selectedCase) params.append('case_id', selectedCase)

      const response = await fetch(`/api/time-tracking?${params}`)
      const data = await response.json()

      if (data.success) {
        setTimeEntries(data.data.timeEntries)
        setStats(data.data.statistics)
      }
    } catch (error) {
      console.error('خطأ في جلب تسجيلات الوقت:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchTimeEntries()
  }, [dateRange, selectedEmployee, selectedCase])

  // تنسيق الوقت
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    return `${hours}س ${mins}د`
  }

  // تنسيق المبلغ
  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  // تنسيق التاريخ والوقت
  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // بدء تسجيل الوقت
  const startTimer = async (entryData: any) => {
    try {
      const response = await fetch('/api/time-tracking', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...entryData,
          startTime: new Date().toISOString(),
          status: 'active'
        })
      })

      const data = await response.json()
      if (data.success) {
        setActiveTimer(data.data.id)
        fetchTimeEntries()
      }
    } catch (error) {
      console.error('خطأ في بدء التسجيل:', error)
    }
  }

  // إيقاف تسجيل الوقت
  const stopTimer = async (entryId: number) => {
    try {
      const response = await fetch('/api/time-tracking', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: entryId,
          endTime: new Date().toISOString(),
          status: 'completed'
        })
      })

      const data = await response.json()
      if (data.success) {
        setActiveTimer(null)
        fetchTimeEntries()
      }
    } catch (error) {
      console.error('خطأ في إيقاف التسجيل:', error)
    }
  }

  // الحصول على لون حالة المهمة
  const getStatusColor = (status: string) => {
    const colors: { [key: string]: string } = {
      'active': 'bg-green-100 text-green-800',
      'paused': 'bg-yellow-100 text-yellow-800',
      'completed': 'bg-blue-100 text-blue-800'
    }
    return colors[status] || 'bg-gray-100 text-gray-800'
  }

  // الحصول على لون فئة المهمة
  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      'research': 'bg-purple-100 text-purple-800',
      'meeting': 'bg-blue-100 text-blue-800',
      'court': 'bg-red-100 text-red-800',
      'documentation': 'bg-green-100 text-green-800',
      'general': 'bg-gray-100 text-gray-800'
    }
    return colors[category] || 'bg-gray-100 text-gray-800'
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان والأدوات */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">تتبع الوقت والفوترة</h1>
            <p className="text-gray-600 mt-1">
              إدارة الوقت المستغرق في المهام والقضايا
            </p>
          </div>
          <div className="flex gap-3">
            <Button 
              onClick={() => setShowAddModal(true)}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Plus className="h-4 w-4 mr-2" />
              تسجيل وقت جديد
            </Button>
            <Button 
              variant="outline"
              className="bg-green-600 text-white hover:bg-green-700"
            >
              <Play className="h-4 w-4 mr-2" />
              بدء مؤقت سريع
            </Button>
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-blue-600" />
                <div className="mr-4">
                  <div className="text-2xl font-bold">{formatDuration(stats.total_minutes)}</div>
                  <div className="text-sm text-gray-600">إجمالي الوقت</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <Timer className="h-8 w-8 text-green-600" />
                <div className="mr-4">
                  <div className="text-2xl font-bold">{formatDuration(stats.billable_minutes)}</div>
                  <div className="text-sm text-gray-600">الوقت القابل للفوترة</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <DollarSign className="h-8 w-8 text-purple-600" />
                <div className="mr-4">
                  <div className="text-2xl font-bold">{formatAmount(stats.total_billable_amount)}</div>
                  <div className="text-sm text-gray-600">إجمالي المبلغ</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-orange-600" />
                <div className="mr-4">
                  <div className="text-2xl font-bold">{formatAmount(stats.billed_amount)}</div>
                  <div className="text-sm text-gray-600">المبلغ المفوتر</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-red-600" />
                <div className="mr-4">
                  <div className="text-2xl font-bold">{stats.total_entries}</div>
                  <div className="text-sm text-gray-600">عدد التسجيلات</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* أدوات التصفية */}
        <Card>
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">من تاريخ</label>
                <Input
                  type="date"
                  value={dateRange.start}
                  onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">إلى تاريخ</label>
                <Input
                  type="date"
                  value={dateRange.end}
                  onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">الموظف</label>
                <select
                  value={selectedEmployee}
                  onChange={(e) => setSelectedEmployee(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">جميع الموظفين</option>
                  {/* سيتم جلب قائمة الموظفين من API */}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">القضية</label>
                <select
                  value={selectedCase}
                  onChange={(e) => setSelectedCase(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">جميع القضايا</option>
                  {/* سيتم جلب قائمة القضايا من API */}
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* قائمة تسجيلات الوقت */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="h-5 w-5 mr-2" />
              تسجيلات الوقت
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">جاري التحميل...</p>
              </div>
            ) : timeEntries.length === 0 ? (
              <div className="text-center py-8">
                <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">لا توجد تسجيلات وقت</p>
              </div>
            ) : (
              <div className="space-y-4">
                {timeEntries.map((entry) => (
                  <div
                    key={entry.id}
                    className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <div className="flex items-center gap-2">
                            {entry.status === 'active' && activeTimer === entry.id ? (
                              <Button
                                size="sm"
                                onClick={() => stopTimer(entry.id)}
                                className="bg-red-600 hover:bg-red-700"
                              >
                                <Square className="h-4 w-4" />
                              </Button>
                            ) : (
                              <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                                <Clock className="h-4 w-4 text-gray-600" />
                              </div>
                            )}
                          </div>
                          <div>
                            <h3 className="font-semibold text-lg">{entry.task_description}</h3>
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <User className="h-4 w-4" />
                              {entry.employee_name}
                              {entry.case_number && (
                                <>
                                  <span>•</span>
                                  <FileText className="h-4 w-4" />
                                  {entry.case_number} - {entry.case_title}
                                </>
                              )}
                            </div>
                          </div>
                        </div>

                        <div className="flex flex-wrap gap-2 mb-3">
                          <Badge className={getStatusColor(entry.status)}>
                            {entry.status === 'active' ? 'نشط' : 
                             entry.status === 'paused' ? 'متوقف' : 'مكتمل'}
                          </Badge>
                          <Badge className={getCategoryColor(entry.task_category)}>
                            {entry.task_category}
                          </Badge>
                          {entry.is_billable && (
                            <Badge className="bg-green-100 text-green-800">
                              قابل للفوترة
                            </Badge>
                          )}
                          {entry.is_billed && (
                            <Badge className="bg-blue-100 text-blue-800">
                              تم الفوترة
                            </Badge>
                          )}
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">المدة:</span>
                            <span className="font-semibold mr-2">
                              {formatDuration(entry.duration_minutes)}
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-600">السعر/ساعة:</span>
                            <span className="font-semibold mr-2">
                              {formatAmount(entry.hourly_rate)}
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-600">المبلغ:</span>
                            <span className="font-semibold mr-2">
                              {formatAmount(entry.billable_amount)}
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-600">التاريخ:</span>
                            <span className="font-semibold mr-2">
                              {formatDateTime(entry.start_time)}
                            </span>
                          </div>
                        </div>

                        {entry.notes && (
                          <div className="mt-3 p-3 bg-gray-50 rounded-md">
                            <p className="text-sm text-gray-700">{entry.notes}</p>
                          </div>
                        )}
                      </div>

                      <div className="flex gap-2">
                        <Button size="sm" variant="outline">
                          <Edit className="h-4 w-4" />
                        </Button>
                        {!entry.is_billed && (
                          <Button size="sm" variant="outline" className="text-red-600 hover:text-red-700">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* نافذة إضافة تسجيل وقت */}
        {showAddModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <h3 className="text-lg font-semibold mb-4">تسجيل وقت جديد</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">وصف المهمة</label>
                  <Input placeholder="أدخل وصف المهمة" />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">فئة المهمة</label>
                  <select className="w-full p-2 border border-gray-300 rounded-md">
                    <option value="">اختر الفئة</option>
                    <option value="research">بحث</option>
                    <option value="meeting">اجتماع</option>
                    <option value="court">محكمة</option>
                    <option value="documentation">توثيق</option>
                    <option value="general">عام</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">القضية</label>
                  <select className="w-full p-2 border border-gray-300 rounded-md">
                    <option value="">اختر القضية</option>
                    {/* سيتم جلب القضايا من API */}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">السعر بالساعة</label>
                  <Input type="number" placeholder="0.00" />
                </div>
                
                <div className="flex items-center gap-2">
                  <input type="checkbox" id="billable" />
                  <label htmlFor="billable" className="text-sm">قابل للفوترة</label>
                </div>
              </div>
              
              <div className="flex gap-3 mt-6">
                <Button className="flex-1 bg-blue-600 hover:bg-blue-700">
                  <Plus className="h-4 w-4 mr-2" />
                  إضافة التسجيل
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => setShowAddModal(false)}
                  className="flex-1"
                >
                  إلغاء
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}