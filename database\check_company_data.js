// فحص بيانات الشركة في قاعدة البيانات
const { Client } = require('pg');

async function checkCompanyData() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'moham<PERSON>',
    user: 'postgres',
    password: 'yemen123'
  });
  
  try {
    await client.connect();
    console.log('✅ متصل بقاعدة البيانات');

    // 1. فحص بنية جدول companies
    console.log('\n🔍 فحص بنية جدول companies...');
    const columns = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'companies' AND table_schema = 'public'
      ORDER BY ordinal_position
    `);
    
    console.log('📋 الأعمدة الموجودة:');
    columns.rows.forEach(col => {
      console.log(`   - ${col.column_name}: ${col.data_type}`);
    });

    // 2. فحص البيانات الموجودة
    console.log('\n📊 البيانات الموجودة في جدول companies:');
    const companies = await client.query('SELECT * FROM companies ORDER BY id');
    
    if (companies.rows.length > 0) {
      companies.rows.forEach((company, index) => {
        console.log(`\n   ${index + 1}. شركة ID: ${company.id}`);
        console.log(`      الاسم: ${company.name || 'غير محدد'}`);
        console.log(`      الاسم القانوني: ${company.legal_name || 'غير محدد'}`);
        console.log(`      رقم التسجيل: ${company.registration_number || 'غير محدد'}`);
        console.log(`      الرقم الضريبي: ${company.tax_number || 'غير محدد'}`);
        console.log(`      العنوان: ${company.address || 'غير محدد'}`);
        console.log(`      المدينة: ${company.city || 'غير محدد'}`);
        console.log(`      الهاتف: ${company.phone || 'غير محدد'}`);
        console.log(`      البريد الإلكتروني: ${company.email || 'غير محدد'}`);
        console.log(`      الموقع الإلكتروني: ${company.website || 'غير محدد'}`);
        console.log(`      تاريخ التأسيس: ${company.established_date || 'غير محدد'}`);
        console.log(`      رأس المال: ${company.capital || 'غير محدد'}`);
        console.log(`      الوصف: ${company.description || 'غير محدد'}`);
        console.log(`      نشط: ${company.is_active}`);
      });
    } else {
      console.log('   لا توجد بيانات في جدول companies');
      
      // إنشاء بيانات الشركة الافتراضية
      console.log('\n🔧 إنشاء بيانات الشركة الافتراضية...');
      
      const defaultCompany = {
        name: 'مكتب المحامي محمد هاشم للاستشارات القانونية',
        legal_name: 'مكتب المحامي محمد هاشم للاستشارات القانونية',
        registration_number: 'REG-2024-001',
        tax_number: 'TAX-*********',
        address: 'صنعاء - شارع الزبيري - مجمع الأعمال التجاري',
        city: 'صنعاء',
        country: 'اليمن',
        phone: '+967-1-234567',
        email: '<EMAIL>',
        website: 'www.legal-office.com',
        established_date: '2020-01-01',
        legal_form: 'مكتب فردي',
        capital: 1000000,
        description: 'مكتب متخصص في تقديم الاستشارات القانونية وتمثيل الموكلين أمام المحاكم',
        logo_url: '/images/logo.png',
        logo_right_text: 'مكتب المحامي',
        logo_left_text: 'محمد هاشم',
        logo_image_url: '/images/logo-main.png',
        is_active: true
      };

      await client.query(`
        INSERT INTO companies (
          name, legal_name, registration_number, tax_number, address, city, country,
          phone, email, website, established_date, legal_form, capital, description,
          logo_url, logo_right_text, logo_left_text, logo_image_url, is_active
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19
        )
      `, [
        defaultCompany.name, defaultCompany.legal_name, defaultCompany.registration_number,
        defaultCompany.tax_number, defaultCompany.address, defaultCompany.city, defaultCompany.country,
        defaultCompany.phone, defaultCompany.email, defaultCompany.website, defaultCompany.established_date,
        defaultCompany.legal_form, defaultCompany.capital, defaultCompany.description,
        defaultCompany.logo_url, defaultCompany.logo_right_text, defaultCompany.logo_left_text,
        defaultCompany.logo_image_url, defaultCompany.is_active
      ]);

      console.log('✅ تم إنشاء بيانات الشركة الافتراضية');
    }

    // 3. فحص البيانات بعد التحديث
    console.log('\n📊 البيانات النهائية:');
    const finalCompanies = await client.query('SELECT * FROM companies ORDER BY id');
    
    finalCompanies.rows.forEach((company, index) => {
      console.log(`\n   ${index + 1}. ${company.name}`);
      console.log(`      📧 ${company.email}`);
      console.log(`      📞 ${company.phone}`);
      console.log(`      📍 ${company.address}`);
      console.log(`      🌐 ${company.website}`);
    });

    // 4. اختبار API الشركة
    console.log('\n🧪 اختبار API الشركة...');
    
    try {
      const apiTest = await client.query(`
        SELECT
          id, name, legal_name, registration_number, tax_number, address, city, country,
          phone, email, website, logo_url, logo_right_text, logo_left_text, logo_image_url,
          established_date, legal_form, capital, description, is_active, created_date,
          latitude, longitude
        FROM companies
        ORDER BY created_date DESC
      `);

      console.log('✅ استعلام API نجح');
      console.log(`📊 عدد الشركات: ${apiTest.rows.length}`);
      
      if (apiTest.rows.length > 0) {
        const company = apiTest.rows[0];
        console.log('\n📋 بيانات الشركة الأولى:');
        console.log(`   الاسم: ${company.name}`);
        console.log(`   البريد: ${company.email}`);
        console.log(`   الهاتف: ${company.phone}`);
        console.log(`   العنوان: ${company.address}`);
        console.log(`   الموقع: ${company.website}`);
      }

    } catch (apiError) {
      console.log('❌ خطأ في استعلام API:', apiError.message);
    }

    console.log('\n✅ تم فحص بيانات الشركة بنجاح');

  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await client.end();
  }
}

checkCompanyData();
