'use client'

import { useState, useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { ChevronDown, Search, BookOpen } from 'lucide-react'

interface Account {
  id: string | number
  account_code: string
  account_name: string
  account_type: string
  account_nature: string
  is_linked_record?: boolean
  allow_transactions: boolean
}

interface FinancialAccountSelectProps {
  value: string
  onChange: (accountId: string) => void
  label?: string
  placeholder?: string
  required?: boolean
  className?: string
  accountTypes?: string[] // فلترة حسب نوع الحساب
}

export function FinancialAccountSelect({ 
  value, 
  onChange, 
  label = "الحساب", 
  placeholder = "اختر الحساب المالي أو ابحث...", 
  required = false,
  className = "",
  accountTypes = [] // إذا كان فارغ، يعرض جميع الحسابات المالية
}: FinancialAccountSelectProps) {
  const [accounts, setAccounts] = useState<Account[]>([])
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedAccount, setSelectedAccount] = useState<Account | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  const fetchAccounts = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/accounting/chart-of-accounts?only_transactional=true&include_linked=false')
      if (response.ok) {
        const data = await response.json()
        let filteredAccounts = data.accounts || []
        
        // فلترة الحسابات المرتبطة (العملاء/الموظفين/الموردين)
        filteredAccounts = filteredAccounts.filter((account: Account) => !account.is_linked_record)
        
        // فلترة حسب نوع الحساب إذا تم تحديده
        if (accountTypes.length > 0) {
          filteredAccounts = filteredAccounts.filter((account: Account) => 
            accountTypes.includes(account.account_type)
          )
        }
        
        setAccounts(filteredAccounts)
      } else {
        console.error('فشل في جلب الحسابات المالية:', response.status)
        setAccounts([])
      }
    } catch (error) {
      console.error('خطأ في جلب الحسابات المالية:', error)
      setAccounts([])
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchAccounts()
  }, [accountTypes])

  useEffect(() => {
    if (value && accounts.length > 0) {
      const account = accounts.find(a => a.id.toString() === value)
      setSelectedAccount(account || null)
    } else {
      setSelectedAccount(null)
    }
  }, [value, accounts])

  const filteredAccounts = accounts.filter(account =>
    account.account_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    account.account_code.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleSelect = (account: Account) => {
    setSelectedAccount(account)
    onChange(account.id.toString())
    setIsOpen(false)
    setSearchTerm('')
  }

  const handleClear = () => {
    setSelectedAccount(null)
    onChange('')
    setSearchTerm('')
  }



  const getAccountTypeColor = (accountType: string) => {
    switch (accountType) {
      case 'أصول': return 'bg-green-100 text-green-800'
      case 'خصوم': return 'bg-red-100 text-red-800'
      case 'حقوق ملكية': return 'bg-blue-100 text-blue-800'
      case 'إيرادات': return 'bg-purple-100 text-purple-800'
      case 'مصروفات': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className={`relative ${className}`}>
      <Label className="block text-sm font-medium text-gray-700 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </Label>
      
      <div className="relative">
        <div
          className="w-full px-3 py-2 border border-gray-300 rounded-md cursor-pointer bg-white flex items-center justify-between hover:border-gray-400 focus-within:border-blue-500 transition-colors"
          onClick={() => setIsOpen(!isOpen)}
        >
          <div className="flex items-center flex-1 min-w-0">
            <BookOpen className="h-4 w-4 mr-2 text-gray-400" />
            <span className={`${selectedAccount ? 'text-gray-900' : 'text-gray-500'} mr-2 truncate`}>
              {selectedAccount ? selectedAccount.account_name : placeholder}
            </span>
            {selectedAccount && (
              <Badge className={`${getAccountTypeColor(selectedAccount.account_type)} ml-2`} size="sm">
                {selectedAccount.account_type}
              </Badge>
            )}
          </div>
          <ChevronDown className={`h-4 w-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </div>

        {isOpen && (
          <div className="absolute z-[100] w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-[500px] overflow-hidden">
            {/* شريط البحث */}
            <div className="p-3 border-b bg-gray-50">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="ابحث بالاسم أو الرمز..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                  autoFocus
                />
              </div>
            </div>

            {/* قائمة الحسابات */}
            <div className="max-h-[400px] overflow-y-auto">
              {isLoading ? (
                <div className="p-4 text-center text-gray-500">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto mb-2"></div>
                  جاري التحميل...
                </div>
              ) : (
                <>
                  {selectedAccount && (
                    <div
                      className="p-3 hover:bg-red-50 cursor-pointer border-b text-red-600 bg-red-25"
                      onClick={handleClear}
                    >
                      <div className="flex items-center">
                        <span className="text-sm font-medium">✕ إلغاء الاختيار</span>
                      </div>
                    </div>
                  )}

                  {filteredAccounts.length > 0 ? (
                    filteredAccounts.map((account) => (
                      <div
                        key={account.id}
                        className="p-3 hover:bg-gray-50 cursor-pointer border-b last:border-b-0"
                        onClick={() => handleSelect(account)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center">
                              <BookOpen className="h-4 w-4 mr-2 text-purple-500" />
                              <span className="font-medium text-gray-900 mr-2 truncate">{account.account_name}</span>
                              <Badge className={getAccountTypeColor(account.account_type)} size="sm">
                                {account.account_type}
                              </Badge>
                            </div>
                            <div className="text-sm text-gray-500 mt-1">
                              <span className="font-mono">{account.account_code}</span>
                              <span className="mx-2">•</span>
                              <span>{account.account_nature}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="p-4 text-center text-gray-500">
                      {searchTerm ? 'لا توجد نتائج للبحث' : 'لا توجد حسابات مالية'}
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        )}
      </div>

      {/* عرض تفاصيل الحساب المختار */}
      {selectedAccount && (
        <div className="mt-2 p-3 bg-gray-50 border border-gray-200 rounded-md">
          <div className="text-sm text-gray-700">
            <div className="grid grid-cols-2 gap-2">
              <div><strong>الرمز:</strong> {selectedAccount.account_code}</div>
              <div><strong>النوع:</strong> {selectedAccount.account_type}</div>
              <div><strong>الطبيعة:</strong> {selectedAccount.account_nature}</div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
