/**
 * سكريبت لإعداد جدول إعدادات الربط التلقائي للحسابات
 * وإنشاء الإعدادات الافتراضية للعملاء والموظفين
 */

const { Client } = require('pg');

// إعدادات قاعدة البيانات mohammidev
const dbConfig = {
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'mohammidev'
};

async function setupAccountLinkingSettings() {
  const client = new Client(dbConfig);

  try {
    await client.connect();
    console.log('✅ متصل بقاعدة البيانات mohammidev');

    // 1. إنشاء جدول إعدادات الربط التلقائي
    console.log('\n🔧 إنشاء جدول إعدادات الربط التلقائي...');
    
    await client.query(`
      CREATE TABLE IF NOT EXISTS account_linking_settings (
        id SERIAL PRIMARY KEY,
        table_name VARCHAR(100) NOT NULL UNIQUE,
        table_display_name VARCHAR(200) NOT NULL,
        description TEXT,
        default_main_account_id INTEGER,
        name_field VARCHAR(100) DEFAULT 'name',
        id_field VARCHAR(100) DEFAULT 'id',
        prefix VARCHAR(10),
        is_enabled BOOLEAN DEFAULT true,
        auto_create_on_insert BOOLEAN DEFAULT true,
        auto_create_on_update BOOLEAN DEFAULT false,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (default_main_account_id) REFERENCES chart_of_accounts(id)
      )
    `);
    
    console.log('✅ تم إنشاء جدول account_linking_settings');

    // 2. إنشاء جدول الروابط الفرعية
    console.log('\n🔧 إنشاء جدول الروابط الفرعية...');
    
    await client.query(`
      CREATE TABLE IF NOT EXISTS account_sub_links (
        id SERIAL PRIMARY KEY,
        main_account_id INTEGER NOT NULL,
        linked_table VARCHAR(100) NOT NULL,
        linked_record_id INTEGER NOT NULL,
        sub_account_code VARCHAR(100) NOT NULL,
        sub_account_name VARCHAR(300) NOT NULL,
        notes TEXT,
        is_active BOOLEAN DEFAULT true,
        created_by VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (main_account_id) REFERENCES chart_of_accounts(id),
        UNIQUE(main_account_id, linked_table, linked_record_id)
      )
    `);
    
    console.log('✅ تم إنشاء جدول account_sub_links');

    // 3. البحث عن الحسابات الأب
    console.log('\n🔍 البحث عن الحسابات الأب...');
    
    // حساب العملاء
    const clientsAccount = await client.query(`
      SELECT id, account_code, account_name FROM chart_of_accounts 
      WHERE account_code = '1121' OR account_name LIKE '%عملاء%' OR account_name LIKE '%العملاء%'
      ORDER BY account_level ASC
      LIMIT 1
    `);
    
    // حساب الموظفين
    const employeesAccount = await client.query(`
      SELECT id, account_code, account_name FROM chart_of_accounts 
      WHERE account_name_en = 'Employees' OR account_code = '1151' OR account_name LIKE '%موظف%'
      ORDER BY account_level ASC
      LIMIT 1
    `);

    console.log(`📊 حساب العملاء: ${clientsAccount.rows.length > 0 ? clientsAccount.rows[0].account_name : 'غير موجود'}`);
    console.log(`📊 حساب الموظفين: ${employeesAccount.rows.length > 0 ? employeesAccount.rows[0].account_name : 'غير موجود'}`);

    // 4. إنشاء الإعدادات الافتراضية
    console.log('\n⚙️ إنشاء الإعدادات الافتراضية...');
    
    const defaultSettings = [
      {
        table_name: 'clients',
        table_display_name: 'العملاء والموكلين',
        description: 'ربط حسابات العملاء تلقائياً عند إضافة عميل جديد',
        prefix: 'CLI',
        name_field: 'name',
        account_id: clientsAccount.rows.length > 0 ? clientsAccount.rows[0].id : null
      },
      {
        table_name: 'employees',
        table_display_name: 'الموظفين',
        description: 'ربط حسابات الموظفين تلقائياً عند إضافة موظف جديد',
        prefix: 'EMP',
        name_field: 'name',
        account_id: employeesAccount.rows.length > 0 ? employeesAccount.rows[0].id : null
      }
    ];

    for (const setting of defaultSettings) {
      if (setting.account_id) {
        await client.query(`
          INSERT INTO account_linking_settings 
          (table_name, table_display_name, description, default_main_account_id, name_field, prefix, is_enabled, auto_create_on_insert)
          VALUES ($1, $2, $3, $4, $5, $6, true, true)
          ON CONFLICT (table_name) DO UPDATE SET
            table_display_name = EXCLUDED.table_display_name,
            description = EXCLUDED.description,
            default_main_account_id = EXCLUDED.default_main_account_id,
            name_field = EXCLUDED.name_field,
            prefix = EXCLUDED.prefix,
            is_enabled = true,
            auto_create_on_insert = true,
            updated_at = CURRENT_TIMESTAMP
        `, [
          setting.table_name,
          setting.table_display_name,
          setting.description,
          setting.account_id,
          setting.name_field,
          setting.prefix
        ]);
        
        console.log(`✅ تم إعداد ربط ${setting.table_display_name}`);
      } else {
        console.log(`⚠️ لم يتم العثور على حساب أب لـ ${setting.table_display_name}`);
      }
    }

    // 5. تحديث الحسابات الأب لتفعيل الربط التلقائي
    console.log('\n🔄 تحديث الحسابات الأب...');
    
    if (clientsAccount.rows.length > 0) {
      await client.query(`
        UPDATE chart_of_accounts 
        SET linked_table = 'clients', auto_create_sub_accounts = true
        WHERE id = $1
      `, [clientsAccount.rows[0].id]);
      console.log(`✅ تم تحديث حساب العملاء لتفعيل الربط التلقائي`);
    }

    if (employeesAccount.rows.length > 0) {
      await client.query(`
        UPDATE chart_of_accounts 
        SET linked_table = 'employees', auto_create_sub_accounts = true
        WHERE id = $1
      `, [employeesAccount.rows[0].id]);
      console.log(`✅ تم تحديث حساب الموظفين لتفعيل الربط التلقائي`);
    }

    // 6. إنشاء دوال قاعدة البيانات للربط التلقائي
    console.log('\n🔧 إنشاء دوال قاعدة البيانات...');
    
    // دالة عامة للربط التلقائي
    await client.query(`
      CREATE OR REPLACE FUNCTION auto_create_account_link()
      RETURNS TRIGGER AS $$
      DECLARE
        settings_record RECORD;
        sub_code VARCHAR(50);
        sub_name VARCHAR(255);
        record_name VARCHAR(255);
      BEGIN
        -- البحث عن إعدادات الربط للجدول
        SELECT * INTO settings_record
        FROM account_linking_settings
        WHERE table_name = TG_TABLE_NAME 
        AND is_enabled = true 
        AND auto_create_on_insert = true
        LIMIT 1;
        
        -- إذا لم توجد إعدادات، لا نفعل شيئاً
        IF NOT FOUND THEN
          RETURN NEW;
        END IF;
        
        -- الحصول على اسم السجل
        EXECUTE format('SELECT %I FROM %I WHERE %I = $1', 
                      settings_record.name_field, 
                      TG_TABLE_NAME, 
                      settings_record.id_field) 
        INTO record_name 
        USING NEW.id;
        
        -- إنشاء كود الحساب الفرعي
        sub_code := settings_record.prefix || '-' || LPAD(NEW.id::text, 4, '0');
        
        -- إنشاء اسم الحساب الفرعي
        sub_name := settings_record.table_display_name || ': ' || COALESCE(record_name, 'سجل رقم ' || NEW.id);
        
        -- إنشاء الرابط الفرعي
        INSERT INTO account_sub_links 
        (main_account_id, linked_table, linked_record_id, sub_account_code, sub_account_name, created_by)
        VALUES (settings_record.default_main_account_id, TG_TABLE_NAME, NEW.id, sub_code, sub_name, 'النظام')
        ON CONFLICT (main_account_id, linked_table, linked_record_id) DO NOTHING;
        
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);
    
    console.log('✅ تم إنشاء دالة الربط التلقائي');

    // 7. إنشاء المحفزات للجداول
    console.log('\n🔧 إنشاء المحفزات...');
    
    const tables = ['clients', 'employees'];
    
    for (const table of tables) {
      // التحقق من وجود الجدول
      const tableExists = await client.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = $1
        )
      `, [table]);

      if (tableExists.rows[0].exists) {
        // حذف المحفز القديم إن وجد
        await client.query(`DROP TRIGGER IF EXISTS trigger_auto_create_account_link ON ${table}`);
        
        // إنشاء المحفز الجديد
        await client.query(`
          CREATE TRIGGER trigger_auto_create_account_link
            AFTER INSERT ON ${table}
            FOR EACH ROW
            EXECUTE FUNCTION auto_create_account_link()
        `);
        
        console.log(`✅ تم إنشاء محفز الربط التلقائي لجدول ${table}`);
      } else {
        console.log(`⚠️ الجدول ${table} غير موجود`);
      }
    }

    // 8. التحقق النهائي
    console.log('\n🔍 التحقق النهائي...');
    
    const finalCheck = await client.query(`
      SELECT 
        als.table_name,
        als.table_display_name,
        als.is_enabled,
        als.auto_create_on_insert,
        coa.account_name,
        coa.account_code
      FROM account_linking_settings als
      LEFT JOIN chart_of_accounts coa ON als.default_main_account_id = coa.id
      ORDER BY als.table_name
    `);

    console.log('\n📋 إعدادات الربط التلقائي:');
    finalCheck.rows.forEach(setting => {
      const status = setting.is_enabled ? '✅ مفعل' : '❌ معطل';
      console.log(`   - ${setting.table_display_name}: ${status} (${setting.account_code}: ${setting.account_name})`);
    });

    // 9. إحصائيات
    const stats = await client.query(`
      SELECT 
        COUNT(*) as total_settings,
        COUNT(CASE WHEN is_enabled = true THEN 1 END) as enabled_settings
      FROM account_linking_settings
    `);

    const stat = stats.rows[0];
    console.log('\n📊 إحصائيات:');
    console.log(`   - إجمالي الإعدادات: ${stat.total_settings}`);
    console.log(`   - الإعدادات المفعلة: ${stat.enabled_settings}`);

    console.log('\n🎉 تم إعداد نظام الربط التلقائي بنجاح!');
    console.log('\n💡 الآن لن تظهر رسالة التحذير في صفحة العملاء');
    console.log('🔄 سيتم ربط العملاء والموظفين الجدد تلقائياً بدليل الحسابات');

  } catch (error) {
    console.error('❌ خطأ في إعداد نظام الربط التلقائي:', error.message);
    throw error;
  } finally {
    await client.end();
  }
}

// تشغيل السكريبت
if (require.main === module) {
  setupAccountLinkingSettings().catch(console.error);
}

module.exports = { setupAccountLinkingSettings };
