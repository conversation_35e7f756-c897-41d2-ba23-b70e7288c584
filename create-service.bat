@echo off
echo Setting up Advanced Unified Server as Windows Service...
echo.

REM Check for admin privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

REM Service configuration
set SERVICE_NAME=AdvancedUnifiedServer
set WORKING_DIR=D:\mohammi
set SERVER_SCRIPT=service-wrapper.js
set NODE_PATH=

REM Find Node.js path
echo Looking for Node.js...
if exist "C:\Program Files\nodejs\node.exe" (
    set NODE_PATH=C:\Program Files\nodejs\node.exe
    echo Found Node.js at: C:\Program Files\nodejs\node.exe
    goto :found_node
)

where node >nul 2>&1
if %errorLevel% equ 0 (
    for /f "tokens=*" %%i in ('where node') do (
        set NODE_PATH=%%i
        echo Found Node.js in PATH: %%i
        goto :found_node
    )
)

echo ERROR: Node.js not found. Please install Node.js first.
pause
exit /b 1

:found_node

REM Check if working directory exists
if not exist "%WORKING_DIR%" (
    echo ERROR: Working directory not found: %WORKING_DIR%
    pause
    exit /b 1
)

REM Check if server script exists
if not exist "%WORKING_DIR%\%SERVER_SCRIPT%" (
    echo ERROR: Server script not found: %WORKING_DIR%\%SERVER_SCRIPT%
    pause
    exit /b 1
)

echo All files found successfully.
echo.

REM Stop and delete existing service if it exists
echo Checking for existing service...
sc query "%SERVICE_NAME%" >nul 2>&1
if %errorLevel% equ 0 (
    echo Stopping existing service...
    sc stop "%SERVICE_NAME%" >nul 2>&1
    timeout /t 3 /nobreak >nul

    echo Deleting existing service...
    sc delete "%SERVICE_NAME%" >nul 2>&1
    timeout /t 2 /nobreak >nul
)

REM Create new service
echo Creating new service...
sc create "%SERVICE_NAME%" binPath= "\"%NODE_PATH%\" \"%WORKING_DIR%\%SERVER_SCRIPT%\"" start= auto DisplayName= "Advanced Unified Server"

if %errorLevel% equ 0 (
    echo Service created successfully.
) else (
    echo ERROR: Failed to create service.
    pause
    exit /b 1
)

REM Set service description
echo Setting service description...
sc description "%SERVICE_NAME%" "Advanced Law Management System Server - Manages multiple ports and automatic routing"

REM Set recovery options
echo Setting recovery options...
sc failure "%SERVICE_NAME%" reset= 86400 actions= restart/5000/restart/10000/restart/30000

REM Start the service
echo Starting service...
sc start "%SERVICE_NAME%"

REM Wait and check service status
timeout /t 5 /nobreak >nul
echo Checking service status...
sc query "%SERVICE_NAME%" | find "RUNNING" >nul
if %errorLevel% equ 0 (
    echo.
    echo SUCCESS: Service setup completed!
    echo.
    echo Service Information:
    echo - Service Name: %SERVICE_NAME%
    echo - Node.js Path: %NODE_PATH%
    echo - Working Directory: %WORKING_DIR%
    echo - Server Script: %SERVER_SCRIPT%
    echo - Status: RUNNING
    echo.
    echo Available Ports:
    echo - Port 7443: Mohamed System (Production)
    echo - Port 8914: Rabei System
    echo - Port 3300: Mohamed System (Development)
    echo.
    echo Service Management Commands:
    echo - Stop: sc stop %SERVICE_NAME%
    echo - Start: sc start %SERVICE_NAME%
    echo - Delete: sc delete %SERVICE_NAME%
    echo.
    echo The service will start automatically on server reboot.
) else (
    echo ERROR: Service failed to start.
    echo Service status:
    sc query "%SERVICE_NAME%"
)

echo.
echo Important Notes:
echo - Make sure ports 7443, 8914, and 3300 are not used by other programs
echo - You can monitor the service through Services.msc
echo - Check Event Viewer for troubleshooting if needed
echo.

pause
