'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Bell, Search, User, LogOut } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { SmartSearch } from './smart-search'
import { useAuth } from '@/hooks/useAuth'

export function Header() {
  const router = useRouter()
  const { user, logout } = useAuth()

  const handleLogout = () => {
    logout()
  }
  return (
    <header className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        {/* مربع البحث في الوسط */}
        <div className="flex-1 flex justify-center">
          <SmartSearch />
        </div>

        <div className="flex items-center space-x-4 space-x-reverse">
          <Button
            variant="outline"
            onClick={() => window.open('/home', '_blank')}
            className="text-sm"
          >
            الموقع الرئيسي
          </Button>

          <Button
            variant="outline"
            onClick={() => router.push('/client-login')}
            className="text-sm"
          >
            بوابة العملاء
          </Button>

          <Button variant="ghost" size="icon">
            <Bell className="h-5 w-5" />
          </Button>

          <div className="flex items-center space-x-3 space-x-reverse">
            <div className="text-right">
              <div className="text-sm font-medium text-gray-900">
                {user?.name || 'المستخدم'}
              </div>
              <div className="text-xs text-gray-500">
                {user?.role_display_name || user?.username || 'غير محدد'}
              </div>
            </div>
            <Button variant="ghost" size="icon">
              <User className="h-5 w-5" />
            </Button>

            {user && (
              <Button
                variant="ghost"
                size="icon"
                onClick={handleLogout}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
                title="تسجيل الخروج"
              >
                <LogOut className="h-5 w-5" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}
