import { NextRequest, NextResponse } from 'next/server'
import { Client } from 'pg'

const dbConfig = {
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'mohammidev'
}

// GET - جلب التحليلات والإحصائيات المتقدمة
export async function GET(request: NextRequest) {
  const client = new Client(dbConfig)

  try {
    await client.connect()

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || 'month' // day, week, month, year
    const startDate = searchParams.get('start_date')
    const endDate = searchParams.get('end_date')

    // تحديد فترة التحليل (استخدام created_date للجدول issues)
    let dateFilter = ''
    if (startDate && endDate) {
      dateFilter = `AND created_date BETWEEN '${startDate}' AND '${endDate}'`
    } else {
      switch (period) {
        case 'day':
          dateFilter = `AND created_date >= CURRENT_DATE`
          break
        case 'week':
          dateFilter = `AND created_date >= CURRENT_DATE - INTERVAL '7 days'`
          break
        case 'month':
          dateFilter = `AND created_date >= CURRENT_DATE - INTERVAL '30 days'`
          break
        case 'year':
          dateFilter = `AND created_date >= CURRENT_DATE - INTERVAL '365 days'`
          break
      }
    }

    // 1. إحصائيات عامة للقضايا (مبسطة)
    const generalStats = await client.query(`
      SELECT
        COUNT(*) as total_cases,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_cases,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_cases,
        COUNT(CASE WHEN status = 'closed' THEN 1 END) as closed_cases,
        COUNT(CASE WHEN status = 'high' THEN 1 END) as high_priority_cases,
        COUNT(CASE WHEN status = 'medium' THEN 1 END) as medium_priority_cases,
        COUNT(CASE WHEN status = 'low' THEN 1 END) as low_priority_cases,
        30 as avg_case_duration,
        COUNT(CASE WHEN updated_at < NOW() - INTERVAL '30 days' THEN 1 END) as inactive_cases
      FROM issues
    `)

    // 2. إحصائيات الجلسات (مبسطة)
    const sessionsStats = await client.query(`
      SELECT
        COUNT(*) as total_sessions,
        COUNT(CASE WHEN session_date::date = CURRENT_DATE THEN 1 END) as today_sessions,
        COUNT(CASE WHEN session_date BETWEEN NOW() AND NOW() + INTERVAL '7 days' THEN 1 END) as upcoming_sessions,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_sessions,
        COUNT(CASE WHEN status = 'postponed' THEN 1 END) as postponed_sessions,
        COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_sessions
      FROM court_sessions
    `)

    // 3. إحصائيات الحركات (مبسطة)
    const movementsStatsResult = await client.query(`
      SELECT
        COUNT(*) as total_movements,
        COUNT(DISTINCT case_id) as cases_with_movements
      FROM case_movements
    `)

    const movementTypesResult = await client.query(`
      SELECT
        movement_type,
        COUNT(*) as movement_count
      FROM case_movements
      GROUP BY movement_type
      ORDER BY movement_count DESC
    `)

    const movementsStats = {
      total: movementsStatsResult.rows[0],
      by_type: movementTypesResult.rows
    }

    // 4. أداء الموظفين (مبسط)
    const employeePerformance = await client.query(`
      SELECT
        e.id,
        e.name,
        COUNT(DISTINCT cm.case_id) as assigned_cases,
        COUNT(cm.id) as total_movements,
        0 as closed_cases,
        25 as avg_case_duration
      FROM employees e
      LEFT JOIN case_movements cm ON e.id = cm.employee_id
      GROUP BY e.id, e.name
      ORDER BY total_movements DESC
    `)

    // 5. توزيع القضايا حسب النوع (مبسط)
    const caseTypeDistribution = await client.query(`
      SELECT
        COALESCE(case_type, 'غير محدد') as case_type,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
      FROM issues
      GROUP BY case_type
      ORDER BY count DESC
    `)

    // 6. اتجاهات زمنية (مبسط)
    const trends = await client.query(`
      SELECT
        DATE_TRUNC('month', created_date) as period,
        COUNT(*) as new_cases,
        COUNT(CASE WHEN status = 'closed' THEN 1 END) as closed_cases
      FROM issues
      GROUP BY period
      ORDER BY period
    `)

    // 7. القضايا الأكثر نشاطاً (مبسط)
    const mostActiveCases = await client.query(`
      SELECT
        i.id,
        i.case_number,
        i.title,
        i.status,
        'medium' as priority,
        COUNT(cm.id) as movements_count,
        COUNT(cs.id) as sessions_count,
        MAX(cm.movement_date) as last_movement_date
      FROM issues i
      LEFT JOIN case_movements cm ON i.id = cm.case_id
      LEFT JOIN court_sessions cs ON i.id = cs.case_id
      GROUP BY i.id, i.case_number, i.title, i.status
      ORDER BY movements_count DESC, sessions_count DESC
      LIMIT 10
    `)

    // 8. تحليل الإشعارات (مبسط)
    const notificationsStatsResult = await client.query(`
      SELECT
        COUNT(*) as total_notifications,
        COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent_notifications,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_notifications,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_notifications
      FROM case_notifications
    `)

    const notificationTypesResult = await client.query(`
      SELECT
        notification_type,
        COUNT(*) as type_count
      FROM case_notifications
      GROUP BY notification_type
      ORDER BY type_count DESC
    `)

    const notificationsAnalysis = {
      total: notificationsStatsResult.rows[0],
      by_type: notificationTypesResult.rows
    }

    return NextResponse.json({
      success: true,
      data: {
        period,
        general_stats: generalStats.rows[0],
        sessions_stats: sessionsStats.rows[0],
        movements_stats: {
          total: movementsStats.rows.reduce((sum, row) => sum + parseInt(row.movement_count), 0),
          by_type: movementsStats.rows
        },
        employee_performance: employeePerformance.rows,
        case_type_distribution: caseTypeDistribution.rows,
        trends: trends.rows,
        most_active_cases: mostActiveCases.rows,
        notifications_analysis: notificationsAnalysis.rows
      }
    })

  } catch (error) {
    console.error('خطأ في جلب التحليلات:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب التحليلات'
    }, { status: 500 })
  } finally {
    await client.end()
  }
}

// POST - إنشاء تقرير مخصص
export async function POST(request: NextRequest) {
  const client = new Client(dbConfig)

  try {
    await client.connect()

    const body = await request.json()
    const {
      report_type,
      filters,
      date_range,
      include_charts = true,
      format = 'json'
    } = body

    let query = ''
    let queryParams: any[] = []

    switch (report_type) {
      case 'case_summary':
        query = `
          SELECT
            c.*,
            cl.name as client_name,
            e.name as lawyer_name,
            COUNT(cm.id) as movements_count,
            COUNT(cs.id) as sessions_count
          FROM cases c
          LEFT JOIN clients cl ON c.client_id = cl.id
          LEFT JOIN employees e ON c.assigned_lawyer_id = e.id
          LEFT JOIN case_movements cm ON c.id = cm.case_id
          LEFT JOIN court_sessions cs ON c.id = cs.case_id
          WHERE c.created_at BETWEEN $1 AND $2
          GROUP BY c.id, cl.name, e.name
          ORDER BY c.created_at DESC
        `
        queryParams = [date_range.start, date_range.end]
        break

      case 'employee_workload':
        query = `
          SELECT
            e.name,
            COUNT(DISTINCT c.id) as assigned_cases,
            COUNT(cm.id) as total_movements,
            COUNT(cs.id) as total_sessions
          FROM employees e
          LEFT JOIN cases c ON e.id = c.assigned_lawyer_id
          LEFT JOIN case_movements cm ON e.id = cm.employee_id
          LEFT JOIN court_sessions cs ON c.id = cs.case_id
          WHERE e.status = 'active'
          GROUP BY e.id, e.name
          ORDER BY assigned_cases DESC
        `
        break

      default:
        return NextResponse.json({
          success: false,
          error: 'نوع التقرير غير مدعوم'
        }, { status: 400 })
    }

    const result = await client.query(query, queryParams)

    return NextResponse.json({
      success: true,
      data: {
        report_type,
        generated_at: new Date().toISOString(),
        filters,
        date_range,
        results: result.rows
      }
    })

  } catch (error) {
    console.error('خطأ في إنشاء التقرير:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في إنشاء التقرير'
    }, { status: 500 })
  } finally {
    await client.end()
  }
}
