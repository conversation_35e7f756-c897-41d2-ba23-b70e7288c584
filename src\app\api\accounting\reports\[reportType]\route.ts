import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database-router'

// GET - إنشاء التقارير المحاسبية
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ reportType: string }> }
) {
  try {
    const { searchParams } = new URL(request.url)
    const dateFrom = searchParams.get('date_from')
    const dateTo = searchParams.get('date_to')
    const { reportType } = await params

    let reportData: any = {
      accounts: [],
      transactions: [],
      summary: {
        totalAssets: 0,
        totalLiabilities: 0,
        totalEquity: 0,
        totalRevenue: 0,
        totalExpenses: 0,
        netIncome: 0
      }
    }

    switch (reportType) {
      case 'trial-balance':
        reportData = await generateTrialBalance(dateFrom, dateTo)
        break

      case 'general-ledger':
        reportData = await generateGeneralLedger(dateFrom, dateTo)
        break

      case 'income-statement':
        reportData = await generateIncomeStatement(dateFrom, dateTo)
        break

      case 'balance-sheet':
        reportData = await generateBalanceSheet(dateFrom, dateTo)
        break

      case 'cash-flow':
        reportData = await generateCashFlow(dateFrom, dateTo)
        break

      case 'vouchers-summary':
        reportData = await generateVouchersSummary(dateFrom, dateTo)
        break

      default:
        return NextResponse.json({
          success: false,
          error: 'نوع التقرير غير مدعوم'
        }, { status: 400 })
    }

    return NextResponse.json({
      success: true,
      ...reportData,
      reportType,
      dateFrom,
      dateTo,
      generatedAt: new Date().toISOString(),
      message: 'تم إنشاء التقرير بنجاح'
    })

  } catch (error) {
    console.error('خطأ في إنشاء التقرير:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في إنشاء التقرير',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// ميزان المراجعة
async function generateTrialBalance(dateFrom?: string | null, dateTo?: string | null) {
  const startDate = dateFrom || new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0]
  const endDate = dateTo || new Date().toISOString().split('T')[0]

  // ميزان المراجعة مع: رصيد افتتاحي قبل startDate + حركة الفترة + رصيد نهائي
  const accountsResult = await query(`
    WITH base_accounts AS (
      SELECT id, account_code, account_name, account_type, account_nature, opening_balance
      FROM chart_of_accounts
      WHERE allow_transactions = true AND is_active = true
    ),
    opening AS (
      SELECT
        ba.id,
        COALESCE(SUM(CASE WHEN ba.account_nature = 'مدين' THEN jed.debit_amount - jed.credit_amount ELSE jed.credit_amount - jed.debit_amount END), 0) AS movement_before
      FROM base_accounts ba
      LEFT JOIN journal_entry_details jed ON ba.id = jed.account_id
      LEFT JOIN journal_entries je ON jed.journal_entry_id = je.id
        AND je.entry_date < $1
        AND je.status IN ('draft', 'approved')
      GROUP BY ba.id
    ),
    period AS (
      SELECT
        ba.id,
        COALESCE(SUM(jed.debit_amount), 0) AS period_debit,
        COALESCE(SUM(jed.credit_amount), 0) AS period_credit
      FROM base_accounts ba
      LEFT JOIN journal_entry_details jed ON ba.id = jed.account_id
      LEFT JOIN journal_entries je ON jed.journal_entry_id = je.id
        AND je.entry_date BETWEEN $1 AND $2
        AND je.status IN ('draft', 'approved')
      GROUP BY ba.id
    )
    SELECT
      ba.id,
      ba.account_code,
      ba.account_name,
      ba.account_type,
      ba.account_nature,
      ba.opening_balance,
      -- توزيع الرصيد الافتتاحي حسب الطبيعة
      CASE WHEN ba.account_nature = 'مدين' THEN ba.opening_balance ELSE 0 END AS opening_debit,
      CASE WHEN ba.account_nature = 'دائن' THEN ba.opening_balance ELSE 0 END AS opening_credit,
      -- الرصيد قبل الفترة وفق الطبيعة
      (ba.opening_balance + COALESCE(o.movement_before, 0)) AS opening_balance_before_period,
      -- حركة الفترة
      COALESCE(p.period_debit, 0) AS period_debit,
      COALESCE(p.period_credit, 0) AS period_credit,
      -- الرصيد النهائي وفق الطبيعة
      CASE 
        WHEN ba.account_nature = 'مدين' THEN (ba.opening_balance + COALESCE(o.movement_before, 0) + COALESCE(p.period_debit, 0) - COALESCE(p.period_credit, 0))
        ELSE (ba.opening_balance + COALESCE(o.movement_before, 0) - COALESCE(p.period_debit, 0) + COALESCE(p.period_credit, 0))
      END AS final_balance,
      -- الميزان المدين/الدائن النهائي
      CASE 
        WHEN ba.account_nature = 'مدين' THEN GREATEST((ba.opening_balance + COALESCE(o.movement_before, 0) + COALESCE(p.period_debit, 0) - COALESCE(p.period_credit, 0)), 0)
        ELSE GREATEST((ba.opening_balance + COALESCE(o.movement_before, 0) - COALESCE(p.period_debit, 0) + COALESCE(p.period_credit, 0)), 0)
      END AS debit_balance,
      CASE 
        WHEN ba.account_nature = 'مدين' THEN GREATEST(-(ba.opening_balance + COALESCE(o.movement_before, 0) + COALESCE(p.period_debit, 0) - COALESCE(p.period_credit, 0)), 0)
        ELSE GREATEST(-(ba.opening_balance + COALESCE(o.movement_before, 0) - COALESCE(p.period_debit, 0) + COALESCE(p.period_credit, 0)), 0)
      END AS credit_balance
    FROM base_accounts ba
    LEFT JOIN opening o ON ba.id = o.id
    LEFT JOIN period p ON ba.id = p.id
    -- إظهار الحسابات ذات أي حركة أو أرصدة
    WHERE (
      COALESCE(p.period_debit, 0) != 0 OR 
      COALESCE(p.period_credit, 0) != 0 OR 
      ba.opening_balance != 0 OR 
      COALESCE(o.movement_before, 0) != 0
    )
    ORDER BY ba.account_code
  `, [startDate, endDate])

  const rows = accountsResult.rows
  return {
    accounts: rows,
    summary: {
      totalDebit: rows.reduce((sum, r) => sum + (parseFloat(r.debit_balance || 0)), 0),
      totalCredit: rows.reduce((sum, r) => sum + (parseFloat(r.credit_balance || 0)), 0)
    }
  }
}

// قائمة الدخل
async function generateIncomeStatement(dateFrom?: string | null, dateTo?: string | null) {
  const startDate = dateFrom || new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0]
  const endDate = dateTo || new Date().toISOString().split('T')[0]

  // جلب الإيرادات والمصروفات
  const accountsResult = await query(`
    WITH income_accounts AS (
      SELECT
        ca.id,
        ca.account_code,
        ca.account_name,
        ca.account_type,
        ca.account_nature,

        -- من القيود اليومية (تشمل السندات والقيود اليدوية)
        COALESCE(SUM(
          CASE
            WHEN ca.account_type IN ('إيرادات', 'revenue')
            THEN jed.credit_amount - jed.debit_amount
            WHEN ca.account_type IN ('مصروفات', 'expenses')
            THEN jed.debit_amount - jed.credit_amount
            ELSE 0
          END
        ), 0) as journal_amount

      FROM chart_of_accounts ca
      LEFT JOIN journal_entry_details jed ON ca.id = jed.account_id
      LEFT JOIN journal_entries je ON jed.journal_entry_id = je.id
        AND je.entry_date BETWEEN $1 AND $2
        AND je.status = 'approved'
      WHERE ca.account_type IN ('إيرادات', 'revenue', 'مصروفات', 'expenses')
        AND ca.is_active = true
      GROUP BY ca.id, ca.account_code, ca.account_name, ca.account_type, ca.account_nature
    )
    SELECT
      *,
      CASE
        WHEN account_type IN ('إيرادات', 'revenue')
        THEN journal_amount
        ELSE 0
      END as total_revenue,
      CASE
        WHEN account_type IN ('مصروفات', 'expenses')
        THEN journal_amount
        ELSE 0
      END as total_expenses
    FROM income_accounts
    WHERE journal_amount != 0
    ORDER BY account_code
  `, [startDate, endDate])

  const accounts = accountsResult.rows
  const totalRevenue = accounts.reduce((sum, acc) => sum + (acc.total_revenue || 0), 0)
  const totalExpenses = accounts.reduce((sum, acc) => sum + (acc.total_expenses || 0), 0)
  const netIncome = totalRevenue - totalExpenses

  return {
    accounts,
    summary: {
      totalRevenue,
      totalExpenses,
      netIncome
    }
  }
}

// الميزانية العمومية
async function generateBalanceSheet(dateFrom?: string | null, dateTo?: string | null) {
  const endDate = dateTo || new Date().toISOString().split('T')[0]

  const accountsResult = await query(`
    WITH balance_sheet_accounts AS (
      SELECT
        ca.id,
        ca.account_code,
        ca.account_name,
        ca.account_type,
        ca.account_nature,
        ca.opening_balance,

        -- حساب الرصيد النهائي
        ca.opening_balance +
        COALESCE(SUM(
          CASE
            WHEN ca.account_nature = 'مدين'
            THEN COALESCE(jed.debit_amount, 0) - COALESCE(jed.credit_amount, 0)
            ELSE COALESCE(jed.credit_amount, 0) - COALESCE(jed.debit_amount, 0)
          END
        ), 0) +
        0 as final_balance

      FROM chart_of_accounts ca
      LEFT JOIN journal_entry_details jed ON ca.id = jed.account_id
      LEFT JOIN journal_entries je ON jed.journal_entry_id = je.id
        AND je.entry_date <= $1
        AND je.status = 'approved'
      WHERE ca.account_type IN ('أصول', 'assets', 'خصوم', 'liabilities', 'حقوق ملكية', 'equity')
        AND ca.is_active = true
      GROUP BY ca.id, ca.account_code, ca.account_name, ca.account_type, ca.account_nature, ca.opening_balance
    )
    SELECT
      *,
      CASE
        WHEN account_type IN ('أصول', 'assets') AND final_balance > 0
        THEN final_balance
        ELSE 0
      END as asset_balance,
      CASE
        WHEN account_type IN ('خصوم', 'liabilities') AND final_balance > 0
        THEN final_balance
        ELSE 0
      END as liability_balance,
      CASE
        WHEN account_type IN ('حقوق ملكية', 'equity') AND final_balance > 0
        THEN final_balance
        ELSE 0
      END as equity_balance
    FROM balance_sheet_accounts
    WHERE final_balance != 0
    ORDER BY account_code
  `, [endDate])

  const accounts = accountsResult.rows
  const totalAssets = accounts.reduce((sum, acc) => sum + (acc.asset_balance || 0), 0)
  const totalLiabilities = accounts.reduce((sum, acc) => sum + (acc.liability_balance || 0), 0)
  const totalEquity = accounts.reduce((sum, acc) => sum + (acc.equity_balance || 0), 0)

  return {
    accounts,
    summary: {
      totalAssets,
      totalLiabilities,
      totalEquity
    }
  }
}

// دفتر الأستاذ العام
async function generateGeneralLedger(dateFrom?: string | null, dateTo?: string | null) {
  const startDate = dateFrom || new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0]
  const endDate = dateTo || new Date().toISOString().split('T')[0]

  const transactionsResult = await query(`
    SELECT
      CASE
        WHEN je.entry_type = 'payment' THEN 'سند صرف'
        WHEN je.entry_type = 'receipt' THEN 'سند قبض'
        WHEN je.entry_type = 'journal' THEN 'قيد يومي'
        ELSE 'معاملة'
      END as transaction_type,
      je.entry_number as reference,
      je.entry_date as transaction_date,
      COALESCE(jed.description, je.description) as description,
      ca.account_code,
      ca.account_name,
      jed.debit_amount,
      jed.credit_amount,
      je.party_name as party_name,
      je.entry_type
    FROM journal_entry_details jed
    JOIN journal_entries je ON jed.journal_entry_id = je.id
    JOIN chart_of_accounts ca ON jed.account_id = ca.id
    WHERE je.entry_date BETWEEN $1 AND $2
      AND je.status IN ('draft', 'approved')
      AND ca.is_active = true
    ORDER BY ca.account_code, je.entry_date, je.entry_number, jed.line_order
  `, [startDate, endDate])

  // تجميع المعاملات حسب الحساب
  const accountsMap = new Map()

  transactionsResult.rows.forEach(row => {
    const accountKey = `${row.account_code}-${row.account_name}`
    if (!accountsMap.has(accountKey)) {
      accountsMap.set(accountKey, {
        account_code: row.account_code,
        account_name: row.account_name,
        transactions: [],
        total_debit: 0,
        total_credit: 0,
        balance: 0
      })
    }

    const account = accountsMap.get(accountKey)
    account.transactions.push(row)
    account.total_debit += parseFloat(row.debit_amount || 0)
    account.total_credit += parseFloat(row.credit_amount || 0)
    account.balance = account.total_debit - account.total_credit
  })

  return {
    transactions: transactionsResult.rows,
    accounts: Array.from(accountsMap.values()),
    summary: {
      totalTransactions: transactionsResult.rows.length,
      totalAccounts: accountsMap.size
    }
  }
}

// ملخص السندات
async function generateVouchersSummary(dateFrom?: string | null, dateTo?: string | null) {
  const startDate = dateFrom || new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0]
  const endDate = dateTo || new Date().toISOString().split('T')[0]

  const summaryResult = await query(`
    SELECT
      entry_type,
      COUNT(*) as total_count,
      SUM(total_debit) as sum_debit,
      SUM(total_credit) as sum_credit,
      AVG(COALESCE(NULLIF(total_debit, 0), total_credit)) as average_amount,
      MIN(COALESCE(NULLIF(total_debit, 0), total_credit)) as min_amount,
      MAX(COALESCE(NULLIF(total_debit, 0), total_credit)) as max_amount
    FROM journal_entries
    WHERE entry_date BETWEEN $1 AND $2
      AND entry_type IN ('payment', 'receipt')
      AND status IN ('draft', 'approved')
    GROUP BY entry_type
    ORDER BY entry_type
  `, [startDate, endDate])

  // جلب تفاصيل السندات مع الحسابات المدينة والدائنة
  const detailsResult = await query(`
    SELECT
      je.entry_type,
      je.entry_number,
      je.entry_date,
      je.total_debit,
      je.total_credit,
      je.description,
      je.party_name as party_name,
      -- الحساب المدين
      debit_acc.account_code as debit_account_code,
      debit_acc.account_name as debit_account_name,
      -- الحساب الدائن
      credit_acc.account_code as credit_account_code,
      credit_acc.account_name as credit_account_name
    FROM journal_entries je
    -- ربط الحساب المدين
    LEFT JOIN journal_entry_details jed_debit ON je.id = jed_debit.journal_entry_id
      AND jed_debit.debit_amount > 0 AND jed_debit.line_order = 1
    LEFT JOIN chart_of_accounts debit_acc ON jed_debit.account_id = debit_acc.id
    -- ربط الحساب الدائن
    LEFT JOIN journal_entry_details jed_credit ON je.id = jed_credit.journal_entry_id
      AND jed_credit.credit_amount > 0 AND jed_credit.line_order = 2
    LEFT JOIN chart_of_accounts credit_acc ON jed_credit.account_id = credit_acc.id
    WHERE je.entry_date BETWEEN $1 AND $2
      AND je.entry_type IN ('payment', 'receipt')
      AND je.status IN ('draft', 'approved')
    ORDER BY je.entry_date DESC, je.entry_number DESC
    LIMIT 50
  `, [startDate, endDate])

  const paymentSummary = summaryResult.rows.find(r => r.entry_type === 'payment')
  const receiptSummary = summaryResult.rows.find(r => r.entry_type === 'receipt')

  return {
    accounts: summaryResult.rows,
    transactions: detailsResult.rows,
    summary: {
      totalPayments: parseFloat(paymentSummary?.sum_credit || 0),
      totalReceipts: parseFloat(receiptSummary?.sum_debit || 0),
      paymentCount: parseInt(paymentSummary?.total_count || 0),
      receiptCount: parseInt(receiptSummary?.total_count || 0),
      netAmount: parseFloat(receiptSummary?.sum_debit || 0) - parseFloat(paymentSummary?.sum_credit || 0)
    }
  }
}

// التدفقات النقدية
async function generateCashFlow(dateFrom?: string | null, dateTo?: string | null) {

  return {
    accounts: [],
    summary: {}
  }
}
