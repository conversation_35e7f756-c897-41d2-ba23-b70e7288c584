// SQLite fallback database connection
import Database from 'better-sqlite3'
import path from 'path'
import fs from 'fs'

// Create database directory if it doesn't exist
const dbDir = path.join(process.cwd(), 'database')
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true })
}

const dbPath = path.join(dbDir, 'case_movements.db')
const db = new Database(dbPath)

// Enable foreign keys
db.pragma('foreign_keys = ON')

// Create tables if they don't exist
const createTables = () => {
  // Create case_movements table
  db.exec(`
    CREATE TABLE IF NOT EXISTS case_movements (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      case_id INTEGER NOT NULL,
      case_number TEXT,
      case_title TEXT,
      movement_type TEXT NOT NULL,
      description TEXT NOT NULL,
      details TEXT,
      hearing_date DATE,
      hearing_time TIME,
      court_name TEXT,
      created_by INTEGER,
      created_by_name TEXT,
      user_role TEXT,
      movement_date DATE DEFAULT (date('now')),
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
      status TEXT DEFAULT 'active',
      notes TEXT,
      reference_id INTEGER,
      reference_type TEXT,
      client_name TEXT,
      case_status TEXT,
      case_next_hearing DATE,
      court_full_name TEXT,
      employee_name TEXT
    )
  `)

  // Create indexes
  db.exec(`
    CREATE INDEX IF NOT EXISTS idx_case_movements_case_id ON case_movements(case_id);
    CREATE INDEX IF NOT EXISTS idx_case_movements_type ON case_movements(movement_type);
    CREATE INDEX IF NOT EXISTS idx_case_movements_date ON case_movements(movement_date);
    CREATE INDEX IF NOT EXISTS idx_case_movements_priority ON case_movements(priority);
    CREATE INDEX IF NOT EXISTS idx_case_movements_created_at ON case_movements(created_at);
  `)

  // Insert sample data if table is empty
  const count = db.prepare('SELECT COUNT(*) as count FROM case_movements').get() as { count: number }
  
  if (count.count === 0) {
    const insert = db.prepare(`
      INSERT INTO case_movements (
        case_id, case_number, case_title, movement_type, description, 
        created_by_name, priority, client_name, case_status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `)

    const movements = [
      [1, '2024/001', 'قضية تجارية - شركة الأمل', 'case_created', 'تم إنشاء القضية في النظام', 'النظام', 'normal', 'شركة الأمل للتجارة', 'نشطة'],
      [1, '2024/001', 'قضية تجارية - شركة الأمل', 'case_assigned', 'تم توزيع القضية على المحامي أحمد محمد', 'المدير العام', 'high', 'شركة الأمل للتجارة', 'موزعة'],
      [2, '2024/002', 'قضية عمالية - محمد علي', 'case_created', 'تم إنشاء القضية في النظام', 'النظام', 'normal', 'محمد علي أحمد', 'نشطة'],
      [2, '2024/002', 'قضية عمالية - محمد علي', 'hearing_scheduled', 'تم تحديد جلسة للقضية في المحكمة العمالية', 'أحمد محمد', 'high', 'محمد علي أحمد', 'جلسة مجدولة'],
      [3, '2024/003', 'قضية مدنية - فاطمة سالم', 'case_created', 'تم إنشاء القضية في النظام', 'النظام', 'normal', 'فاطمة سالم محمد', 'نشطة'],
      [3, '2024/003', 'قضية مدنية - فاطمة سالم', 'document_uploaded', 'تم رفع وثيقة "عقد الإيجار الأصلي" للقضية', 'سارة أحمد', 'normal', 'فاطمة سالم محمد', 'قيد المراجعة'],
      [4, '2024/004', 'قضية جنائية - خالد يوسف', 'case_created', 'تم إنشاء القضية في النظام', 'النظام', 'urgent', 'خالد يوسف علي', 'نشطة'],
      [4, '2024/004', 'قضية جنائية - خالد يوسف', 'follow_added', 'تم إضافة متابعة: تم تقديم الاستئناف للمحكمة العليا', 'محمد الحاشدي', 'urgent', 'خالد يوسف علي', 'استئناف'],
      [5, '2024/005', 'قضية إدارية - شركة النور', 'case_created', 'تم إنشاء القضية في النظام', 'النظام', 'normal', 'شركة النور للمقاولات', 'نشطة'],
      [5, '2024/005', 'قضية إدارية - شركة النور', 'case_status_changed', 'تم تغيير حالة القضية من "نشطة" إلى "قيد المراجعة"', 'أحمد محمد', 'normal', 'شركة النور للمقاولات', 'قيد المراجعة'],
      [6, '2024/006', 'قضية تجارية - مؤسسة الرياض', 'case_created', 'تم إنشاء القضية في النظام', 'النظام', 'normal', 'مؤسسة الرياض التجارية', 'نشطة'],
      [6, '2024/006', 'قضية تجارية - مؤسسة الرياض', 'hearing_scheduled', 'تم تحديد جلسة للقضية في المحكمة التجارية', 'سارة أحمد', 'high', 'مؤسسة الرياض التجارية', 'جلسة مجدولة'],
      [7, '2024/007', 'قضية أسرة - عبدالله حسن', 'case_created', 'تم إنشاء القضية في النظام', 'النظام', 'normal', 'عبدالله حسن محمد', 'نشطة'],
      [7, '2024/007', 'قضية أسرة - عبدالله حسن', 'document_uploaded', 'تم رفع وثيقة "شهادة الزواج" للقضية', 'محمد الحاشدي', 'normal', 'عبدالله حسن محمد', 'قيد المراجعة'],
      [8, '2024/008', 'قضية عقارية - منى سعد', 'case_created', 'تم إنشاء القضية في النظام', 'النظام', 'normal', 'منى سعد أحمد', 'نشطة'],
      [8, '2024/008', 'قضية عقارية - منى سعد', 'follow_added', 'تم إضافة متابعة: تم الحصول على تقرير الخبير العقاري', 'أحمد محمد', 'normal', 'منى سعد أحمد', 'قيد المراجعة'],
      [9, '2024/009', 'قضية تأمين - شركة الحياة', 'case_created', 'تم إنشاء القضية في النظام', 'النظام', 'normal', 'شركة الحياة للتأمين', 'نشطة'],
      [9, '2024/009', 'قضية تأمين - شركة الحياة', 'case_assigned', 'تم توزيع القضية على المحامي سارة أحمد', 'المدير العام', 'normal', 'شركة الحياة للتأمين', 'موزعة'],
      [10, '2024/010', 'قضية بنكية - بنك اليمن', 'case_created', 'تم إنشاء القضية في النظام', 'النظام', 'high', 'بنك اليمن الدولي', 'نشطة'],
      [10, '2024/010', 'قضية بنكية - بنك اليمن', 'hearing_scheduled', 'تم تحديد جلسة للقضية في المحكمة التجارية', 'محمد الحاشدي', 'high', 'بنك اليمن الدولي', 'جلسة مجدولة']
    ]

    const insertMany = db.transaction((movements) => {
      for (const movement of movements) {
        insert.run(movement)
      }
    })

    insertMany(movements)
    console.log('✅ تم إدراج البيانات التجريبية في SQLite')
  }
}

// Initialize tables
createTables()

// Query function compatible with PostgreSQL interface
export async function query(text: string, params?: any[]) {
  try {
    // Convert PostgreSQL syntax to SQLite
    let sqliteQuery = text
      .replace(/\$(\d+)/g, '?') // Replace $1, $2, etc. with ?
      .replace(/SERIAL PRIMARY KEY/gi, 'INTEGER PRIMARY KEY AUTOINCREMENT')
      .replace(/CURRENT_TIMESTAMP/gi, "datetime('now')")
      .replace(/CURRENT_DATE/gi, "date('now')")
      .replace(/INTERVAL '\d+ days?'/gi, "'-1 day'")
      .replace(/EXTRACT\(DAYS FROM \([^)]+\)\)/gi, "julianday('now') - julianday(created_at)")

    // Handle different query types
    if (sqliteQuery.trim().toUpperCase().startsWith('SELECT')) {
      const stmt = db.prepare(sqliteQuery)
      const rows = params ? stmt.all(...params) : stmt.all()
      return {
        rows,
        rowCount: rows.length
      }
    } else if (sqliteQuery.trim().toUpperCase().startsWith('INSERT') || 
               sqliteQuery.trim().toUpperCase().startsWith('UPDATE') || 
               sqliteQuery.trim().toUpperCase().startsWith('DELETE')) {
      const stmt = db.prepare(sqliteQuery)
      const result = params ? stmt.run(...params) : stmt.run()
      return {
        rows: [],
        rowCount: result.changes
      }
    } else {
      // DDL statements (CREATE, DROP, etc.)
      db.exec(sqliteQuery)
      return {
        rows: [],
        rowCount: 0
      }
    }
  } catch (error) {
    console.error('SQLite Query Error:', error)
    console.error('Query:', text)
    console.error('Params:', params)
    throw error
  }
}

export { db as pool }

console.log(`📁 SQLite database initialized: ${dbPath}`)
