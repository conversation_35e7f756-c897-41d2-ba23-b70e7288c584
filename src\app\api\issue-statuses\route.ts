import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب حالات القضايا
export async function GET() {
  try {

    // جلب حالات القضايا من قاعدة البيانات
    const result = await query(`
      SELECT 
        status_code as value,
        status_name as label,
        description,
        color,
        is_active
      FROM issue_statuses 
      WHERE is_active = true
      ORDER BY sort_order, status_name
    `)

    // إذا لم توجد بيانات في قاعدة البيانات، إرجاع البيانات الافتراضية
    if (result.rows.length === 0) {
      const defaultStatuses = [
        { value: 'new', label: 'جديدة', description: 'قضية جديدة', color: '#10b981', is_active: true },
        { value: 'pending', label: 'معلقة', description: 'قضية معلقة', color: '#f59e0b', is_active: true },
        { value: 'in_progress', label: 'قيد المعالجة', description: 'قضية قيد المعالجة', color: '#3b82f6', is_active: true },
        { value: 'completed', label: 'مكتملة', description: 'قضية مكتملة', color: '#059669', is_active: true },
        { value: 'cancelled', label: 'ملغية', description: 'قضية ملغية', color: '#dc2626', is_active: true }
      ]

      return NextResponse.json({
        success: true,
        data: defaultStatuses,
        message: 'تم جلب حالات القضايا الافتراضية'
      })
    }

    return NextResponse.json({
      success: true,
      data: result.rows,
      message: 'تم جلب حالات القضايا بنجاح'
    })
  } catch (error) {
    console.error('GET Issue Statuses API: Error:', error)

    // في حالة الخطأ، إرجاع البيانات الافتراضية
    const defaultStatuses = [
      { value: 'new', label: 'جديدة', description: 'قضية جديدة', color: '#10b981', is_active: true },
      { value: 'pending', label: 'معلقة', description: 'قضية معلقة', color: '#f59e0b', is_active: true },
      { value: 'in_progress', label: 'قيد المعالجة', description: 'قضية قيد المعالجة', color: '#3b82f6', is_active: true },
      { value: 'completed', label: 'مكتملة', description: 'قضية مكتملة', color: '#059669', is_active: true },
      { value: 'cancelled', label: 'ملغية', description: 'قضية ملغية', color: '#dc2626', is_active: true }
    ]

    return NextResponse.json({
      success: true,
      data: defaultStatuses,
      message: 'تم جلب حالات القضايا الافتراضية (خطأ في قاعدة البيانات)'
    })
  }
}

// POST - إضافة حالة قضية جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { status_code, status_name, description, color, sort_order } = body

    // التحقق من البيانات المطلوبة
    if (!status_code || !status_name) {
      return NextResponse.json(
        { success: false, error: 'رمز الحالة واسم الحالة مطلوبان' },
        { status: 400 }
      )
    }

    // التحقق من عدم تكرار رمز الحالة
    const duplicateCheck = await query(
      'SELECT id FROM issue_statuses WHERE status_code = $1',
      [status_code]
    )

    if (duplicateCheck.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'رمز الحالة موجود مسبقاً' },
        { status: 400 }
      )
    }

    // إدراج حالة القضية الجديدة
    const result = await query(`
      INSERT INTO issue_statuses (status_code, status_name, description, color, sort_order, is_active, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING *
    `, [status_code, status_name, description || '', color || '#3b82f6', sort_order || 0])

    return NextResponse.json({
      success: true,
      message: 'تم إضافة حالة القضية بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('POST Issue Statuses API: Error creating status:', error)
    return NextResponse.json(
      { success: false, error: `فشل في إضافة حالة القضية: ${error.message}` },
      { status: 500 }
    )
  }
}
