'use client'

import { useState, useEffect } from 'react'
import MainLayout from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { SearchableAccountSelect } from '@/components/ui/searchable-account-select'

import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { FileText, Plus, Edit, Search, User, DollarSign, Eye, Printer, Trash2 } from 'lucide-react'

interface ReceiptVoucher {
  id: number
  entry_number: string
  entry_date: string
  payer_name: string
  payer_type: string
  party_name?: string
  party_type?: string
  payer_id?: number
  debit_account_id: number
  credit_account_id: number
  amount: number
  total_debit?: number
  currency_id: number
  currency_code?: string
  payment_method_id: number
  payment_method_name?: string
  cost_center_id?: number
  cost_center_name?: string
  description: string
  reference_number?: string
  case_id?: number
  case_number?: string
  service_id?: number
  service_name?: string
  // أسماء الحسابات
  debit_account_name?: string
  credit_account_name?: string
  account_name?: string
  status: string
  created_by_name?: string
  created_at: string
  voucher_type: string
}

interface Account {
  id: number
  account_code: string
  account_name: string
  account_type: string
  allow_transactions?: boolean
  is_linked_record?: boolean
  original_table?: string
  external_id?: number
}

interface Currency {
  id: number
  currency_code: string
  currency_name: string
  symbol: string
}

interface PaymentMethod {
  id: number
  method_code: string
  method_name: string
}

interface CostCenter {
  id: number
  center_code: string
  center_name: string
}

interface Case {
  id: number
  case_number: string
  title: string
  client_name: string
}

interface Service {
  id: number
  name: string
  lineage_name: string
}

export default function ReceiptVouchersPage() {
  const [vouchers, setVouchers] = useState<ReceiptVoucher[]>([])
  const [accounts, setAccounts] = useState<Account[]>([])
  const [currencies, setCurrencies] = useState<Currency[]>([])
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([])
  const [costCenters, setCostCenters] = useState<CostCenter[]>([])
  const [cases, setCases] = useState<Case[]>([])
  const [services, setServices] = useState<Service[]>([])
  // حالات تحميل منفصلة لتسريع التجربة
  const [loadingRefs, setLoadingRefs] = useState(true)
  const [loadingVouchers, setLoadingVouchers] = useState(true)
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [editingVoucher, setEditingVoucher] = useState<ReceiptVoucher | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')

  const [formData, setFormData] = useState({
    entry_date: new Date().toISOString().split('T')[0],
    beneficiary_id: '', // معرف المستفيد (client_1, employee_2, supplier_3)
    beneficiary_name: '', // اسم المستفيد
    beneficiary_type: '', // نوع المستفيد (client, employee, supplier)
    debit_account_id: '',
    credit_account_id: '',
    amount: '',
    currency_id: '1',
    payment_method_id: '1',
    cost_center_id: '',
    description: '',
    reference_number: '',
    case_id: '',
    service_id: ''
  })

  useEffect(() => {
    // تحميل المرجعيات مرة واحدة، والسندات بشكل منفصل
    fetchReferenceData()
    fetchVouchers()
  }, [])

  // دالة لاستخراج بيانات الدافع من الحساب المختار
  const extractPayerInfoFromAccount = (accountId: string) => {
    if (!accountId) return

    const account = accounts.find(acc => acc.id.toString() === accountId)
    if (!account) return

    let payerName = ''
    let payerType = 'external'

    // إذا كان الحساب مرتبط بجدول خارجي
    if (account.is_linked_record && account.original_table) {
      if (account.original_table === 'clients') {
        payerType = 'client'
        // استخراج اسم العميل من اسم الحساب
        payerName = account.account_name.replace('حساب العميل: ', '').replace('حساب العميل:', '').trim()
      } else if (account.original_table === 'employees') {
        payerType = 'employee'
        // استخراج اسم الموظف من اسم الحساب
        payerName = account.account_name.replace('حساب الموظف: ', '').replace('حساب الموظف:', '').trim()
      } else if (account.original_table === 'suppliers') {
        payerType = 'supplier'
        // استخراج اسم المورد من اسم الحساب
        payerName = account.account_name.replace('حساب المورد: ', '').replace('حساب المورد:', '').trim()
      }
    } else {
      // إذا كان حساب عادي، استخدم اسم الحساب مباشرة
      payerName = account.account_name

      // تحديد النوع بناءً على اسم الحساب أو نوعه
      if (account.account_name.includes('عميل') || account.account_name.includes('العملاء')) {
        payerType = 'client'
      } else if (account.account_name.includes('موظف') || account.account_name.includes('الموظفين')) {
        payerType = 'employee'
      } else if (account.account_name.includes('مورد') || account.account_name.includes('الموردين')) {
        payerType = 'supplier'
      }
    }

    // تحديث بيانات النموذج
    setFormData(prev => ({
      ...prev,
      beneficiary_name: payerName,
      beneficiary_type: payerType
    }))
  }

  // تحميل السندات فقط
  const fetchVouchers = async () => {
    try {
      setLoadingVouchers(true)
      const res = await fetch('/api/accounting/receipt-vouchers')
      if (res.ok) {
        const data = await res.json()
        setVouchers(data.vouchers || [])
      }
    } catch (error) {
      console.error('خطأ في جلب السندات:', error)
    } finally {
      setLoadingVouchers(false)
    }
  }

  // تحميل المرجعيات مرة واحدة وبشكل متوازٍ
  const fetchReferenceData = async () => {
    try {
      setLoadingRefs(true)
      const [accountsRes, currenciesRes, methodsRes, centersRes, casesRes, servicesRes] = await Promise.all([
        fetch('/api/accounting/chart-of-accounts?only_transactional=true&include_linked=true'),
        fetch('/api/accounting/currencies'),
        fetch('/api/accounting/payment-methods'),
        fetch('/api/cost-centers'),
        fetch('/api/issues'),
        fetch('/api/services')
      ])

      if (accountsRes.ok) {
        const accountsData = await accountsRes.json()
        setAccounts(accountsData.accounts || [])
      }
      if (currenciesRes.ok) {
        const currenciesData = await currenciesRes.json()
        setCurrencies(currenciesData.currencies || [])
      }
      if (methodsRes.ok) {
        const methodsData = await methodsRes.json()
        setPaymentMethods(methodsData.methods || [])
      }
      if (centersRes.ok) {
        const centersData = await centersRes.json()
        setCostCenters(centersData.centers || [])
      }
      if (casesRes.ok) {
        const casesData = await casesRes.json()
        setCases(casesData.data || casesData.issues || [])
      }
      if (servicesRes.ok) {
        const servicesData = await servicesRes.json()
        setServices(servicesData.data || servicesData.services || [])
      }
    } catch (error) {
      console.error('خطأ في جلب بيانات المرجعيات:', error)
    } finally {
      setLoadingRefs(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (isSubmitting) return
    setIsSubmitting(true)

    try {

      const url = editingVoucher
        ? `/api/accounting/receipt-vouchers/${editingVoucher.id}`
        : '/api/accounting/receipt-vouchers'

      const method = editingVoucher ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          amount: formData.amount ? parseFloat(formData.amount) : null,
          debit_account_id: formData.debit_account_id ? parseInt(formData.debit_account_id) : null,
          credit_account_id: formData.credit_account_id ? parseInt(formData.credit_account_id) : null,
          currency_id: formData.currency_id ? parseInt(formData.currency_id) : null,
          payment_method_id: formData.payment_method_id ? parseInt(formData.payment_method_id) : null,
          cost_center_id: formData.cost_center_id && formData.cost_center_id !== '0' ? parseInt(formData.cost_center_id) : null,
          issues_id: formData.case_id && formData.case_id !== '0' ? parseInt(formData.case_id) : null,
          service_id: formData.service_id && formData.service_id !== '0' ? parseInt(formData.service_id) : null
        }),
      })

      if (response.ok) {
        // أغلق الحوار وافرغ النموذج فوراً لسرعة التجربة
        setShowAddDialog(false)
        setEditingVoucher(null)
        resetForm()
        // حدث السندات فقط بدون إعادة تحميل المرجعيات الثقيلة
        await fetchVouchers()
      } else {
        const errorData = await response.json()
        console.error('❌ خطأ من الخادم:', errorData)
        alert(`خطأ: ${errorData.error}\nالتفاصيل: ${errorData.details || 'غير متوفرة'}`)
      }
    } catch (error) {
      console.error('خطأ في حفظ السند:', error)
      alert('حدث خطأ أثناء حفظ السند')
    } finally {
      setIsSubmitting(false)
    }
  }

  const resetForm = () => {
    setFormData({
      entry_date: new Date().toISOString().split('T')[0],
      beneficiary_id: '',
      beneficiary_name: '',
      beneficiary_type: '',
      debit_account_id: '',
      credit_account_id: '',
      amount: '',
      currency_id: '1',
      payment_method_id: '1',
      cost_center_id: '',
      description: '',
      reference_number: '',
      case_id: '',
      service_id: ''
    })
  }

  const handleEdit = (voucher: ReceiptVoucher) => {
    setEditingVoucher(voucher)
    setFormData({
      entry_date: (() => {
        const d = voucher.entry_date
        if (!d) return new Date().toISOString().split('T')[0]
        // إذا كانت بصيغة ISO كاملة، قص الجزء قبل T
        if (d.includes('T')) return d.split('T')[0]
        // إذا كانت بصيغة YYYY/MM/DD حولها إلى YYYY-MM-DD
        if (d.includes('/')) {
          const [y, m, day] = d.split('/')
          if (y && m && day) return `${y}-${m.padStart(2,'0')}-${day.padStart(2,'0')}`
        }
        return d
      })(),
      beneficiary_id: '',
      beneficiary_name: voucher.payer_name || voucher.party_name || '',
      beneficiary_type: voucher.payer_type || voucher.party_type || 'client',
      debit_account_id: voucher.debit_account_id?.toString() || '',
      credit_account_id: voucher.credit_account_id?.toString() || '',
      amount: (voucher.amount || voucher.total_debit || 0).toString(),
      currency_id: voucher.currency_id?.toString() || '1',
      payment_method_id: voucher.payment_method_id?.toString() || '1',
      cost_center_id: voucher.cost_center_id?.toString() || '',
      description: voucher.description || '',
      reference_number: voucher.reference_number || '',
      case_id: voucher.case_id?.toString() || '',
      service_id: voucher.service_id?.toString() || ''
    })
    setShowAddDialog(true)
  }

  // فتح سند جديد فارغ دومًا
  const handleAddNew = () => {
    setEditingVoucher(null)
    resetForm()
    setShowAddDialog(true)
  }

  const handleView = (voucher: ReceiptVoucher) => {
    const url = `/accounting/receipt-vouchers/${voucher.id}/print?v=${Date.now()}`
    window.open(url, 'viewVoucher', 'width=900,height=700,menubar=no,toolbar=no,location=no,status=no')
  }

  const handlePrint = (voucher: ReceiptVoucher) => {
    const url = `/accounting/receipt-vouchers/${voucher.id}/print?v=${Date.now()}`
    window.open(url, 'printWindow', 'width=900,height=700,menubar=no,toolbar=no,location=no,status=no')
  }

  const handleDelete = async (voucherId: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا السند؟')) {
      return
    }

    try {
      const response = await fetch(`/api/accounting/receipt-vouchers/${voucherId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        await fetchVouchers()
        alert('تم حذف السند بنجاح')
      } else {
        const errorData = await response.json()
        alert(`خطأ في حذف السند: ${errorData.error}`)
      }
    } catch (error) {
      console.error('خطأ في حذف السند:', error)
      alert('حدث خطأ أثناء حذف السند')
    }
  }

  const filteredVouchers = vouchers.filter(voucher =>
    voucher.entry_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
    voucher.payer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    voucher.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'draft': { label: 'مسودة', variant: 'secondary' as const },
      'approved': { label: 'معتمد', variant: 'default' as const },
      'cancelled': { label: 'ملغي', variant: 'destructive' as const }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50">
        <div className="space-y-6 p-6 bg-white min-h-screen">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-3 space-x-reverse">
            <FileText className="h-8 w-8 text-green-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">سندات القبض</h1>
              <p className="text-gray-600">إدارة سندات القبض والمقبوضات</p>
            </div>
          </div>

          <Button onClick={handleAddNew}>
            <Plus className="h-4 w-4 ml-2" />
            سند قبض جديد
          </Button>
        </div>

        {/* البحث */}
        <Card>
          <CardContent className="p-6">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="البحث في سندات القبض..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* جدول السندات */}
        <Card>
          <CardHeader>
            <CardTitle>سندات القبض ({filteredVouchers.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {loadingVouchers ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">جاري تحميل السندات...</p>
              </div>
            ) : filteredVouchers.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>لا توجد سندات قبض</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 border-b">
                    <tr>
                      <th className="text-right p-3 font-semibold text-black">رقم السند</th>
                      <th className="text-right p-3 font-semibold text-black">التاريخ</th>
                      <th className="text-right p-3 font-semibold text-black">المبلغ</th>
                      <th className="text-right p-3 font-semibold text-black">الدافع</th>
                      <th className="text-right p-3 font-semibold text-black">المستلم</th>
                      <th className="text-right p-3 font-semibold text-black">البيان</th>
                      <th className="text-center p-3 font-semibold text-black">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredVouchers.map((voucher, index) => (
                      <tr key={`voucher-${voucher.id}-${index}`} className={`border-b hover:bg-gray-50 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-25'}`}>
                        <td className="p-3">
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <span className="font-mono text-black font-medium">
                              {voucher.entry_number}
                            </span>
                            {getStatusBadge(voucher.status)}
                          </div>
                        </td>
                        <td className="p-3 text-black">
                          {new Date(voucher.entry_date).toLocaleDateString('en-GB')}
                        </td>
                        <td className="p-3">
                          <span className="font-bold text-black">
                            {(voucher.amount || voucher.total_debit || 0).toLocaleString()} {voucher.currency_code || 'ر.ي'}
                          </span>
                        </td>
                        <td className="p-3 text-black">
                          <span className="font-medium">
                            {voucher.payer_name || 'غير محدد'}
                          </span>
                          {voucher.payer_type && (
                            <span className="text-xs text-black block">
                              ({voucher.payer_type === 'client' ? 'عميل' :
                                voucher.payer_type === 'employee' ? 'موظف' :
                                voucher.payer_type === 'supplier' ? 'مورد' : 'خارجي'})
                            </span>
                          )}
                        </td>
                        <td className="p-3 text-black">
                          <span className="font-medium">
                            {voucher.debit_account_name || 'غير محدد'}
                          </span>
                        </td>
                        <td className="p-3 text-black max-w-xs truncate" title={voucher.description}>
                          {voucher.description}
                        </td>
                        <td className="p-3">
                          <div className="flex justify-center space-x-1 space-x-reverse">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleView(voucher)}
                              className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                              title="مشاهدة"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEdit(voucher)}
                              className="text-orange-600 hover:text-orange-700 hover:bg-orange-50"
                              title="تعديل"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDelete(voucher.id)}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                              title="حذف"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handlePrint(voucher)}
                              className="text-gray-600 hover:text-gray-700 hover:bg-gray-50"
                              title="طباعة"
                            >
                              <Printer className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* نموذج إضافة/تعديل السند */}
        <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
          <DialogContent className="max-w-[95vw] max-h-[95vh] w-[95vw] overflow-y-auto">
            <DialogHeader className="bg-gradient-to-r from-green-50 to-green-100 p-4 border-b">
              <DialogTitle className="text-2xl font-bold text-green-800 flex items-center">
                <FileText className="h-7 w-7 mr-3" />
                {editingVoucher ? '✏️ تعديل سند القبض' : '💰 سند قبض جديد'}
              </DialogTitle>
              <DialogDescription className="text-green-600 mt-2">
                {editingVoucher ? 'تعديل بيانات سند القبض المحدد' : 'إنشاء سند قبض جديد مع تفاصيل المبلغ والحساب'}
              </DialogDescription>
            </DialogHeader>

            <div className="flex-1 overflow-y-auto p-4">

              <form onSubmit={handleSubmit} className="space-y-4">
              {/* الصف الأول: التاريخ والمبلغ والعملة وطريقة الدفع */}
              <div className="grid grid-cols-8 gap-3">
                <div className="col-span-2">
                  <Label htmlFor="entry_date" className="text-xs font-medium text-black mb-1 block">
                    📅 تاريخ السند
                  </Label>
                  <Input
                    id="entry_date"
                    type="date"
                    value={formData.entry_date}
                    onChange={(e) => setFormData({...formData, entry_date: e.target.value})}
                    required
                    className="h-8 text-sm"
                  />
                </div>

                <div className="col-span-2">
                  <Label htmlFor="amount" className="text-xs font-medium text-black mb-1 block">
                    💰 المبلغ
                  </Label>
                  <Input
                    id="amount"
                    type="number"
                    step="0.01"
                    value={formData.amount}
                    onChange={(e) => setFormData({...formData, amount: e.target.value})}
                    placeholder="0.00"
                    required
                    className="h-8 text-sm"
                  />
                </div>

                <div className="col-span-2">
                  <Label htmlFor="currency_id" className="text-xs font-medium text-yellow-700 mb-1 block">
                    💱 العملة
                  </Label>
                  <Select value={formData.currency_id} onValueChange={(value) => setFormData({...formData, currency_id: value})}>
                    <SelectTrigger className="h-8 text-sm">
                      <SelectValue placeholder="اختر العملة" />
                    </SelectTrigger>
                    <SelectContent>
                      {currencies.map((currency) => (
                        <SelectItem key={currency.id} value={currency.id.toString()}>
                          {currency.currency_code} - {currency.currency_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-2">
                  <Label htmlFor="payment_method_id" className="text-xs font-medium text-indigo-700 mb-1 block">
                    💳 طريقة الدفع
                  </Label>
                  <Select value={formData.payment_method_id} onValueChange={(value) => setFormData({...formData, payment_method_id: value})}>
                    <SelectTrigger className="h-8 text-sm">
                      <SelectValue placeholder="اختر طريقة الدفع" />
                    </SelectTrigger>
                    <SelectContent>
                      {paymentMethods.map((method) => (
                        <SelectItem key={method.id} value={method.id.toString()}>
                          {method.method_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* الصف الثاني: القضية ومركز التكلفة والخدمة ورقم المرجع */}
              <div className="grid grid-cols-8 gap-3">
                <div className="col-span-2">
                  <Label htmlFor="case_id" className="text-xs font-medium text-black mb-1 block">
                    ⚖️ القضية
                  </Label>
                  <Select value={formData.case_id} onValueChange={(value) => setFormData({...formData, case_id: value})}>
                    <SelectTrigger className="h-8 text-sm">
                      <SelectValue placeholder="اختر القضية" />
                    </SelectTrigger>
                    <SelectContent className="max-h-60">
                      <SelectItem value="0">بدون قضية</SelectItem>
                      {cases.map((caseItem) => (
                        <SelectItem key={caseItem.id} value={caseItem.id.toString()}>
                          <span className="font-medium text-black">{caseItem.title}</span>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-2">
                  <Label htmlFor="cost_center_id" className="text-xs font-medium text-black mb-1 block">
                    🏢 مركز التكلفة
                  </Label>
                  <Select value={formData.cost_center_id} onValueChange={(value) => setFormData({...formData, cost_center_id: value})}>
                    <SelectTrigger className="h-8 text-sm">
                      <SelectValue placeholder="اختر مركز التكلفة" />
                    </SelectTrigger>
                    <SelectContent className="max-h-60">
                      <SelectItem value="0">بدون مركز تكلفة</SelectItem>
                      {costCenters.map((center) => (
                        <SelectItem key={center.id} value={center.id.toString()}>
                          <span className="font-medium text-black">{center.center_name}</span>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-2">
                  <Label htmlFor="service_id" className="text-xs font-medium text-teal-700 mb-1 block">
                    🔧 الخدمة
                  </Label>
                  <Select value={formData.service_id} onValueChange={(value) => setFormData({...formData, service_id: value})}>
                    <SelectTrigger className="h-8 text-sm">
                      <SelectValue placeholder="اختر الخدمة" />
                    </SelectTrigger>
                    <SelectContent className="max-h-60">
                      <SelectItem value="0">بدون خدمة</SelectItem>
                      {services.map((service) => (
                        <SelectItem key={service.id} value={service.id.toString()}>
                          <span className="font-medium text-black">{service.name}</span>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-2">
                  <Label htmlFor="reference_number" className="text-xs font-medium text-blue-700 mb-1 block">
                    📄 رقم المرجع
                  </Label>
                  <Input
                    id="reference_number"
                    value={formData.reference_number}
                    onChange={(e) => setFormData({...formData, reference_number: e.target.value})}
                    placeholder="رقم المرجع (اختياري)"
                    className="h-8 text-sm"
                  />
                </div>
              </div>

              {/* تمت إزالة سطر معلومات الدافع والاكتفاء بالحقل */}

              {/* الصف الثالث: الحسابات - تم الإصلاح 2024 */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-black">
                    🏦 الحساب المدين (المقبوض إليه)
                  </label>
                  <select
                    value={formData.debit_account_id}
                    onChange={(e) => setFormData({...formData, debit_account_id: e.target.value})}
                    className="w-full p-3 border border-gray-300 rounded-md bg-white focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                    required
                  >
                    <option value="">اختر الحساب المدين...</option>
                    {accounts
                      .filter(account => account.account_type === 'أصول' && account.allow_transactions)
                      .map(account => (
                        <option key={account.id} value={account.id.toString()} style={{ color: 'black' }}>
                          {account.account_name} ({account.account_code})
                        </option>
                      ))}
                  </select>
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium text-black">
                    📊 الحساب الدائن (الدافع)
                  </label>
                  <select
                    value={formData.credit_account_id}
                    onChange={(e) => {
                      setFormData({ ...formData, credit_account_id: e.target.value })
                      extractPayerInfoFromAccount(e.target.value)
                    }}
                    className="w-full p-3 border border-gray-300 rounded-md bg-white focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                    required
                  >
                    <option value="">اختر الحساب الدائن (الدافع)...</option>
                    {accounts
                      .filter(account => account.allow_transactions)
                      .map(account => (
                        <option key={account.id} value={account.id.toString()} style={{ color: 'black' }}>
                          {account.account_name} ({account.account_code})
                          {account.is_linked_record && account.original_table && (
                            ` - ${account.original_table === 'clients' ? 'عميل' :
                                 account.original_table === 'employees' ? 'موظف' :
                                 account.original_table === 'suppliers' ? 'مورد' : ''}`
                          )}
                        </option>
                      ))}
                  </select>
                </div>
              </div>

              {/* البيان */}
              <div>
                <Label htmlFor="description" className="text-sm font-semibold text-black bg-gray-50 px-2 py-1 rounded-md inline-block mb-2">
                  📝 البيان
                </Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  placeholder="وصف تفصيلي للسند..."
                  required
                  rows={3}
                  className="h-[80px] text-sm bg-gray-50 border-gray-300 focus:border-gray-500 focus:bg-white transition-colors"
                />
              </div>

              <DialogFooter className="flex justify-between pt-6 border-t">
                <Button type="button" variant="outline" onClick={() => setShowAddDialog(false)}>
                  إلغاء
                </Button>
                <Button type="submit" className="bg-green-600 hover:bg-green-700 disabled:opacity-60" disabled={isSubmitting}>
                  {isSubmitting ? 'جارٍ الحفظ...' : (editingVoucher ? 'تحديث السند' : 'حفظ السند')}
                </Button>
              </DialogFooter>
              </form>
            </div>
          </DialogContent>
        </Dialog>
        </div>
      </div>
    </MainLayout>
  )
}
