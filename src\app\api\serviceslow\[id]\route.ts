import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب خدمة واحدة بالمعرف
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    const result = await query(
      'SELECT * FROM serviceslow WHERE id = $1',
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الخدمة غير موجودة' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0]
    })
  } catch (error) {
    console.error('خطأ في جلب الخدمة:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الخدمة' },
      { status: 500 }
    )
  }
}

// PUT - تحديث خدمة واحدة
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const {
      title,
      slug,
      description,
      content,
      icon_name,
      icon_color,
      image_url,
      is_active,
      sort_order,
      meta_title,
      meta_description
    } = body

    if (!title || !slug) {
      return NextResponse.json(
        { success: false, error: 'العنوان والرابط مطلوبان' },
        { status: 400 }
      )
    }

    // التحقق من وجود الخدمة
    const existingService = await query(
      'SELECT id FROM serviceslow WHERE id = $1',
      [id]
    )

    if (existingService.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الخدمة غير موجودة' },
        { status: 404 }
      )
    }

    // التحقق من عدم تكرار الرابط (باستثناء الخدمة الحالية)
    const duplicateSlug = await query(
      'SELECT id FROM serviceslow WHERE slug = $1 AND id != $2',
      [slug, id]
    )

    if (duplicateSlug.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'الرابط موجود مسبقاً' },
        { status: 400 }
      )
    }

    const result = await query(`
      UPDATE serviceslow SET
        title = $1,
        slug = $2,
        description = $3,
        content = $4,
        icon_name = $5,
        icon_color = $6,
        image_url = $7,
        is_active = $8,
        sort_order = $9,
        meta_title = $10,
        meta_description = $11,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $12
      RETURNING *
    `, [
      title, slug, description, content, icon_name, icon_color,
      image_url, is_active, sort_order, meta_title, meta_description, id
    ])

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم تحديث الخدمة بنجاح'
    })
  } catch (error) {
    console.error('خطأ في تحديث الخدمة:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الخدمة' },
      { status: 500 }
    )
  }
}

// DELETE - حذف خدمة
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // التحقق من وجود الخدمة
    const existingService = await query(
      'SELECT id FROM serviceslow WHERE id = $1',
      [id]
    )

    if (existingService.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الخدمة غير موجودة' },
        { status: 404 }
      )
    }

    await query('DELETE FROM serviceslow WHERE id = $1', [id])

    return NextResponse.json({
      success: true,
      message: 'تم حذف الخدمة بنجاح'
    })
  } catch (error) {
    console.error('خطأ في حذف الخدمة:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الخدمة' },
      { status: 500 }
    )
  }
}
