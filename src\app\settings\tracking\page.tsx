'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { 
  Settings, 
  Bell, 
  Clock, 
  AlertTriangle, 
  Save,
  RefreshCw,
  CheckCircle,
  Mail,
  MessageSquare,
  Phone
} from 'lucide-react'

interface TrackingSettings {
  default_reminder_days: number
  auto_client_notifications: boolean
  auto_employee_notifications: boolean
  case_inactivity_threshold: number
  priority_case_threshold: number
  notification_methods: string[]
  working_hours_start: string
  working_hours_end: string
  weekend_days: string[]
}

export default function TrackingSettingsPage() {
  const [settings, setSettings] = useState<TrackingSettings>({
    default_reminder_days: 3,
    auto_client_notifications: true,
    auto_employee_notifications: true,
    case_inactivity_threshold: 30,
    priority_case_threshold: 7,
    notification_methods: ['email', 'sms'],
    working_hours_start: '08:00',
    working_hours_end: '17:00',
    weekend_days: ['friday', 'saturday']
  })
  
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [saved, setSaved] = useState(false)

  // جلب الإعدادات الحالية
  const fetchSettings = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/tracking-settings')
      const data = await response.json()
      
      if (data.success) {
        // تحويل الإعدادات من قاعدة البيانات إلى الكائن
        const settingsObj: any = {}
        data.data.settings.forEach((setting: any) => {
          let value = setting.setting_value
          
          // تحويل القيم حسب النوع
          if (setting.setting_type === 'number') {
            value = parseInt(value)
          } else if (setting.setting_type === 'boolean') {
            value = value === 'true'
          } else if (setting.setting_type === 'json') {
            value = JSON.parse(value)
          }
          
          settingsObj[setting.setting_name] = value
        })
        
        setSettings(prev => ({ ...prev, ...settingsObj }))
      }
    } catch (error) {
      console.error('خطأ في جلب الإعدادات:', error)
    } finally {
      setLoading(false)
    }
  }

  // حفظ الإعدادات
  const saveSettings = async () => {
    try {
      setSaving(true)
      
      // تحويل الإعدادات إلى مصفوفة للإرسال
      const settingsArray = Object.entries(settings).map(([key, value]) => ({
        setting_name: key,
        setting_value: typeof value === 'object' ? JSON.stringify(value) : value.toString(),
        setting_type: typeof value === 'boolean' ? 'boolean' : 
                     typeof value === 'number' ? 'number' :
                     typeof value === 'object' ? 'json' : 'string'
      }))
      
      // حفظ كل إعداد
      for (const setting of settingsArray) {
        const response = await fetch('/api/tracking-settings', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(setting)
        })
        
        if (!response.ok) {
          // إذا فشل الإنشاء، جرب التحديث
          await fetch('/api/tracking-settings', {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(setting)
          })
        }
      }
      
      setSaved(true)
      setTimeout(() => setSaved(false), 3000)
      
    } catch (error) {
      console.error('خطأ في حفظ الإعدادات:', error)
    } finally {
      setSaving(false)
    }
  }

  // تشغيل التنبيهات التلقائية
  const runAutoNotifications = async () => {
    try {
      const response = await fetch('/api/auto-notifications')
      const data = await response.json()
      
      if (data.success) {
        alert(`تم إنشاء ${data.data.notifications_created} إشعار تلقائي`)
      }
    } catch (error) {
      console.error('خطأ في تشغيل التنبيهات:', error)
    }
  }

  useEffect(() => {
    fetchSettings()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل الإعدادات...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6" dir="rtl">
      {/* العنوان */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <Settings className="h-8 w-8 mr-3 text-blue-600" />
            إعدادات تتبع القضايا
          </h1>
          <p className="text-gray-600 mt-1">تخصيص إعدادات التتبع والإشعارات</p>
        </div>

        <div className="flex space-x-2 space-x-reverse">
          <Button 
            onClick={runAutoNotifications}
            variant="outline"
            className="border-green-600 text-green-600 hover:bg-green-50"
          >
            <Bell className="h-4 w-4 mr-2" />
            تشغيل التنبيهات
          </Button>
          
          <Button 
            onClick={saveSettings}
            disabled={saving}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {saving ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : saved ? (
              <CheckCircle className="h-4 w-4 mr-2" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            {saving ? 'جاري الحفظ...' : saved ? 'تم الحفظ' : 'حفظ الإعدادات'}
          </Button>
        </div>
      </div>

      {/* إعدادات الإشعارات */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Bell className="h-5 w-5 mr-2" />
            إعدادات الإشعارات
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="reminder_days">عدد أيام التذكير قبل الجلسات</Label>
              <Input
                id="reminder_days"
                type="number"
                value={settings.default_reminder_days}
                onChange={(e) => setSettings(prev => ({
                  ...prev,
                  default_reminder_days: parseInt(e.target.value) || 3
                }))}
                className="w-full"
              />
              <p className="text-sm text-gray-500">سيتم إرسال تذكير للعملاء والموظفين قبل هذا العدد من الأيام</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="inactivity_threshold">عتبة القضايا المتوقفة (بالأيام)</Label>
              <Input
                id="inactivity_threshold"
                type="number"
                value={settings.case_inactivity_threshold}
                onChange={(e) => setSettings(prev => ({
                  ...prev,
                  case_inactivity_threshold: parseInt(e.target.value) || 30
                }))}
                className="w-full"
              />
              <p className="text-sm text-gray-500">القضايا التي لم يتم تحديثها خلال هذه المدة تعتبر متوقفة</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">إشعارات العملاء التلقائية</h4>
                <p className="text-sm text-gray-500">إرسال إشعارات تلقائية للعملاء</p>
              </div>
              <Switch
                checked={settings.auto_client_notifications}
                onCheckedChange={(checked) => setSettings(prev => ({
                  ...prev,
                  auto_client_notifications: checked
                }))}
              />
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">إشعارات الموظفين التلقائية</h4>
                <p className="text-sm text-gray-500">إرسال إشعارات تلقائية للموظفين</p>
              </div>
              <Switch
                checked={settings.auto_employee_notifications}
                onCheckedChange={(checked) => setSettings(prev => ({
                  ...prev,
                  auto_employee_notifications: checked
                }))}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* طرق الإشعار */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <MessageSquare className="h-5 w-5 mr-2" />
            طرق الإشعار
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center">
                <Mail className="h-5 w-5 mr-2 text-blue-600" />
                <span>البريد الإلكتروني</span>
              </div>
              <Switch
                checked={settings.notification_methods.includes('email')}
                onCheckedChange={(checked) => {
                  const methods = checked 
                    ? [...settings.notification_methods, 'email']
                    : settings.notification_methods.filter(m => m !== 'email')
                  setSettings(prev => ({ ...prev, notification_methods: methods }))
                }}
              />
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center">
                <Phone className="h-5 w-5 mr-2 text-green-600" />
                <span>الرسائل النصية</span>
              </div>
              <Switch
                checked={settings.notification_methods.includes('sms')}
                onCheckedChange={(checked) => {
                  const methods = checked 
                    ? [...settings.notification_methods, 'sms']
                    : settings.notification_methods.filter(m => m !== 'sms')
                  setSettings(prev => ({ ...prev, notification_methods: methods }))
                }}
              />
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center">
                <MessageSquare className="h-5 w-5 mr-2 text-green-500" />
                <span>واتساب</span>
              </div>
              <Switch
                checked={settings.notification_methods.includes('whatsapp')}
                onCheckedChange={(checked) => {
                  const methods = checked 
                    ? [...settings.notification_methods, 'whatsapp']
                    : settings.notification_methods.filter(m => m !== 'whatsapp')
                  setSettings(prev => ({ ...prev, notification_methods: methods }))
                }}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* ساعات العمل */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="h-5 w-5 mr-2" />
            ساعات العمل
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="start_time">بداية ساعات العمل</Label>
              <Input
                id="start_time"
                type="time"
                value={settings.working_hours_start}
                onChange={(e) => setSettings(prev => ({
                  ...prev,
                  working_hours_start: e.target.value
                }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="end_time">نهاية ساعات العمل</Label>
              <Input
                id="end_time"
                type="time"
                value={settings.working_hours_end}
                onChange={(e) => setSettings(prev => ({
                  ...prev,
                  working_hours_end: e.target.value
                }))}
              />
            </div>
          </div>

          <div className="mt-4">
            <Label>أيام نهاية الأسبوع</Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'].map(day => (
                <Badge
                  key={day}
                  variant={settings.weekend_days.includes(day) ? "default" : "outline"}
                  className="cursor-pointer"
                  onClick={() => {
                    const days = settings.weekend_days.includes(day)
                      ? settings.weekend_days.filter(d => d !== day)
                      : [...settings.weekend_days, day]
                    setSettings(prev => ({ ...prev, weekend_days: days }))
                  }}
                >
                  {day === 'sunday' ? 'الأحد' :
                   day === 'monday' ? 'الاثنين' :
                   day === 'tuesday' ? 'الثلاثاء' :
                   day === 'wednesday' ? 'الأربعاء' :
                   day === 'thursday' ? 'الخميس' :
                   day === 'friday' ? 'الجمعة' : 'السبت'}
                </Badge>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* معاينة الإعدادات */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertTriangle className="h-5 w-5 mr-2" />
            ملخص الإعدادات الحالية
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <p><strong>تذكير الجلسات:</strong> {settings.default_reminder_days} أيام مسبقاً</p>
              <p><strong>عتبة التوقف:</strong> {settings.case_inactivity_threshold} يوم</p>
              <p><strong>ساعات العمل:</strong> {settings.working_hours_start} - {settings.working_hours_end}</p>
            </div>
            <div>
              <p><strong>إشعارات العملاء:</strong> {settings.auto_client_notifications ? 'مفعلة' : 'معطلة'}</p>
              <p><strong>إشعارات الموظفين:</strong> {settings.auto_employee_notifications ? 'مفعلة' : 'معطلة'}</p>
              <p><strong>طرق الإشعار:</strong> {settings.notification_methods.join(', ')}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
