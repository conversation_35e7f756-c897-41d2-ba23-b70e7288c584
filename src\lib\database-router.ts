/**
 * وحدة توجيه قواعد البيانات - تدعم الاتصال بقواعد بيانات متعددة
 * 
 * تستخدم هذه الوحدة لتوجيه الاتصالات إلى قاعدة البيانات المناسبة بناءً على رؤوس الطلب
 */

import { Pool, PoolClient } from 'pg';

// تخزين مؤقت لمجمعات الاتصال
const poolCache: Record<string, Pool> = {};

/**
 * الحصول على اسم قاعدة البيانات من متغيرات البيئة أو رؤوس الطلب
 * @returns اسم قاعدة البيانات
 */
export function getDatabaseName(): string {
  try {
    // قراءة اسم القاعدة من متغيرات البيئة (الوضع المدعوم حالياً)
    if (process.env.X_DATABASE) return process.env.X_DATABASE;
    if (process.env.DB_NAME) return process.env.DB_NAME;

    // وأخيراً، القاعدة الافتراضية
    return 'mohammi';
  } catch (error) {
    console.error('خطأ في الحصول على اسم قاعدة البيانات:', error);
    return 'mohammi';
  }
}

/**
 * الحصول على اسم الشركة من متغيرات البيئة أو رؤوس الطلب
 * @returns اسم الشركة
 */
export function getCompanyName(): string {
  try {
    if (process.env.X_COMPANY) {
      return decodeURIComponent(process.env.X_COMPANY);
    }

    return 'نظام إدارة المحاماة';
  } catch (error) {
    console.error('خطأ في الحصول على اسم الشركة:', error);
    return 'نظام إدارة المحاماة';
  }
}

/**
 * الحصول على لون السمة من متغيرات البيئة أو رؤوس الطلب
 * @returns لون السمة
 */
export function getThemeColor(): string {
  try {
    if (process.env.X_THEME_COLOR) {
      return process.env.X_THEME_COLOR;
    }

    return '#1e40af';
  } catch (error) {
    console.error('خطأ في الحصول على لون السمة:', error);
    return '#1e40af';
  }
}

/**
 * الحصول على رسالة الترحيب من متغيرات البيئة أو رؤوس الطلب
 * @returns رسالة الترحيب
 */
export function getWelcomeMessage(): string {
  try {
    if (process.env.X_WELCOME_MESSAGE) {
      return decodeURIComponent(process.env.X_WELCOME_MESSAGE);
    }

    return 'مرحباً بكم في نظام إدارة المحاماة';
  } catch (error) {
    console.error('خطأ في الحصول على رسالة الترحيب:', error);
    return 'مرحباً بكم في نظام إدارة المحاماة';
  }
}

/**
 * الحصول على بادئة الإشعارات من متغيرات البيئة أو رؤوس الطلب
 * @returns بادئة الإشعارات
 */
export function getNotificationPrefix(): string {
  try {
    if (process.env.X_NOTIFICATION_PREFIX) {
      return decodeURIComponent(process.env.X_NOTIFICATION_PREFIX);
    }

    return 'إشعار: ';
  } catch (error) {
    console.error('خطأ في الحصول على بادئة الإشعارات:', error);
    return 'إشعار: ';
  }
}

/**
 * الحصول على مجمع اتصال لقاعدة بيانات محددة
 * @param dbName اسم قاعدة البيانات
 * @returns مجمع اتصال
 */
export function getConnectionPool(dbName: string): Pool {
  // مفتاح فريد لكل مجمع اتصال
  const poolKey = `${dbName}_${process.env.PORT || 'default'}`;

  // التحقق من وجود مجمع اتصال في التخزين المؤقت
  if (poolCache[poolKey]) {
    return poolCache[poolKey];
  }

  // قراءة إعدادات قاعدة البيانات من متغيرات البيئة
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    database: dbName,
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'yemen123', // كلمة مرور افتراضية
    ssl: false,
    connectionTimeoutMillis: 5000,
    idleTimeoutMillis: 30000,
    max: 20
  };

  // إنشاء مجمع اتصال جديد
  const pool = new Pool(dbConfig);

  // معالجة الأخطاء
  pool.on('error', (err) => {
    console.error(`❌ خطأ في مجمع الاتصال لقاعدة البيانات ${dbName}:`, err.message);
  });

  pool.on('connect', (client) => {

  });

  // تخزين مجمع الاتصال في التخزين المؤقت
  poolCache[poolKey] = pool;

  return pool;
}

/**
 * الحصول على مجمع اتصال لقاعدة البيانات الحالية
 * @returns مجمع اتصال
 */
export function getCurrentConnectionPool(): Pool {
  const dbName = getDatabaseName();
  return getConnectionPool(dbName);
}

/**
 * اختبار الاتصال بقاعدة البيانات
 * @param dbName اسم قاعدة البيانات (اختياري)
 * @returns نجاح الاتصال
 */
export async function testConnection(dbName?: string): Promise<boolean> {
  const db = dbName || getDatabaseName();
  const pool = getConnectionPool(db);

  try {
    const client = await pool.connect();
    const result = await client.query('SELECT NOW()');
    client.release();

    return true;
  } catch (error) {
    console.error(`❌ فشل الاتصال بقاعدة البيانات ${db}:`, error);
    return false;
  }
}

/**
 * تنفيذ استعلام على قاعدة البيانات الحالية
 * @param text نص الاستعلام
 * @param params معلمات الاستعلام
 * @returns نتيجة الاستعلام
 */
export async function query(text: string, params?: any[]) {
  const pool = getCurrentConnectionPool();
  let client: PoolClient | undefined;

  try {
    client = await pool.connect();
    const result = await client.query(text, params);
    return result;
  } catch (error) {
    console.error('خطأ في استعلام قاعدة البيانات:', error);
    console.error('الاستعلام:', text);
    console.error('المعلمات:', params);
    throw error;
  } finally {
    if (client) {
      client.release();
    }
  }
}

/**
 * فتح اتصال بقاعدة البيانات الحالية
 * @returns واجهة اتصال قاعدة البيانات
 */
export async function openDb() {
  const pool = getCurrentConnectionPool();
  const client = await pool.connect();

  return {
    async exec(sql: string) {
      try {
        // تقسيم الاستعلامات المتعددة وتنفيذها واحدة تلو الأخرى
        const statements = sql.split(';').filter(stmt => stmt.trim());
        for (const statement of statements) {
          if (statement.trim()) {
            await client.query(statement.trim());
          }
        }
      } catch (error) {
        console.error('خطأ في تنفيذ استعلام:', error);
        throw error;
      }
    },

    async get(sql: string, params: any[] = []) {
      try {
        const result = await client.query(sql, params);
        return result.rows[0] || null;
      } catch (error) {
        console.error('خطأ في الحصول على بيانات:', error);
        throw error;
      }
    },

    async all(sql: string, params: any[] = []) {
      try {
        const result = await client.query(sql, params);
        return result.rows;
      } catch (error) {
        console.error('خطأ في الحصول على جميع البيانات:', error);
        throw error;
      }
    },

    async run(sql: string, params: any[] = []) {
      try {
        // بالنسبة لاستعلامات INSERT، أضف RETURNING id للحصول على المعرف المدرج
        if (sql.trim().toUpperCase().startsWith('INSERT')) {
          const modifiedSql = sql.includes('RETURNING') ? sql : sql + ' RETURNING id';
          const result = await client.query(modifiedSql, params);
          return {
            lastID: result.rows[0]?.id || null,
            changes: result.rowCount || 0
          };
        } else {
          const result = await client.query(sql, params);
          return {
            lastID: null,
            changes: result.rowCount || 0
          };
        }
      } catch (error) {
        console.error('خطأ في تشغيل استعلام:', error);
        throw error;
      }
    },

    close() {
      client.release();
    }
  };
}

/**
 * الحصول على بيانات الشركة من قاعدة البيانات
 * @returns بيانات الشركة
 */
export async function getCompanyDataFromDb(): Promise<any> {
  try {
    const result = await query('SELECT name, phone, email, logo_url, description FROM companies WHERE is_active = true LIMIT 1');
    if (result.rows.length > 0) {
      return result.rows[0];
    }
    return null;
  } catch (error) {
    console.error('خطأ في الحصول على بيانات الشركة من قاعدة البيانات:', error);
    return null;
  }
}

/**
 * الحصول على اسم الشركة الحقيقي من قاعدة البيانات مع الاحتياطي من متغيرات البيئة
 * @returns اسم الشركة
 */
export async function getRealCompanyName(): Promise<string> {
  try {
    // أولاً، جرب الحصول على اسم الشركة من قاعدة البيانات
    const companyData = await getCompanyDataFromDb();
    if (companyData && companyData.name) {
      return companyData.name;
    }
    
    // إذا فشل، استخدم متغيرات البيئة
    return getCompanyName();
  } catch (error) {
    console.error('خطأ في الحصول على اسم الشركة الحقيقي:', error);
    return getCompanyName();
  }
}

/**
 * الحصول على معلومات قاعدة البيانات الحالية
 * @returns معلومات قاعدة البيانات
 */
export function getDatabaseInfo() {
  return {
    name: getDatabaseName(),
    company: getCompanyName(),
    themeColor: getThemeColor(),
    welcomeMessage: getWelcomeMessage(),
    notificationPrefix: getNotificationPrefix()
  };
}

/**
 * الحصول على معلومات قاعدة البيانات والشركة من قاعدة البيانات
 * @returns معلومات كاملة
 */
export async function getFullDatabaseInfo() {
  try {
    const companyData = await getCompanyDataFromDb();
    const dbName = getDatabaseName();
    
    return {
      database: dbName,
      company: companyData?.name || getCompanyName(),
      phone: companyData?.phone || 'غير محدد',
      email: companyData?.email || 'غير محدد',
      themeColor: getThemeColor(),
      welcomeMessage: getWelcomeMessage(),
      notificationPrefix: getNotificationPrefix(),
      port: process.env.PORT || 'غير محدد',
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('خطأ في الحصول على المعلومات الكاملة:', error);
    return {
      database: getDatabaseName(),
      company: getCompanyName(),
      themeColor: getThemeColor(),
      welcomeMessage: getWelcomeMessage(),
      notificationPrefix: getNotificationPrefix(),
      port: process.env.PORT || 'غير محدد',
      timestamp: new Date().toISOString()
    };
  }
}