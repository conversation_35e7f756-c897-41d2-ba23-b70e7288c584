import { NextRequest, NextResponse } from 'next/server'
import { Client } from 'pg'

const dbConfig = {
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'mohammidev'
}

// GET - جلب حركات القضايا مع الإحصائيات
export async function GET(request: NextRequest) {
  const client = new Client(dbConfig)

  try {
    await client.connect()

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const status = searchParams.get('status')
    const priority = searchParams.get('priority')
    const search = searchParams.get('search')

    const offset = (page - 1) * limit

    // بناء الاستعلام مع الفلاتر
    let whereConditions = ['1=1']
    let queryParams: any[] = []
    let paramIndex = 1

    if (status) {
      whereConditions.push(`c.status = $${paramIndex}`)
      queryParams.push(status)
      paramIndex++
    }

    if (priority) {
      whereConditions.push(`c.priority = $${paramIndex}`)
      queryParams.push(priority)
      paramIndex++
    }

    if (search) {
      whereConditions.push(`(c.title ILIKE $${paramIndex} OR c.case_number ILIKE $${paramIndex} OR cl.name ILIKE $${paramIndex})`)
      queryParams.push(`%${search}%`)
      paramIndex++
    }

    const whereClause = whereConditions.join(' AND ')

    // جلب القضايا مع آخر حركة وجلسة قادمة (استخدام جدول issues)
    const casesQuery = `
      SELECT
        i.id,
        i.case_number,
        i.title,
        i.description,
        i.status,
        'medium' as priority,
        i.client_id,
        i.updated_at,
        i.created_date,
        cl.name as client_name,
        e.name as lawyer_name,
        (
          SELECT json_build_object(
            'id', cm.id,
            'movement_type', cm.movement_type,
            'description', cm.description,
            'movement_date', cm.movement_date,
            'employee_name', emp.name
          )
          FROM case_movements cm
          LEFT JOIN employees emp ON cm.employee_id = emp.id
          WHERE cm.case_id = i.id
          ORDER BY cm.movement_date DESC
          LIMIT 1
        ) as last_movement,
        (
          SELECT json_build_object(
            'id', cs.id,
            'session_date', cs.session_date,
            'court_name', cs.court_name,
            'session_type', cs.session_type,
            'status', cs.status
          )
          FROM court_sessions cs
          WHERE cs.case_id = i.id AND cs.session_date > NOW()
          ORDER BY cs.session_date ASC
          LIMIT 1
        ) as next_session,
        (
          SELECT COUNT(*)
          FROM case_movements cm2
          WHERE cm2.case_id = i.id
        ) as movements_count,
        CASE
          WHEN i.updated_at < NOW() - INTERVAL '30 days' THEN 'inactive'
          WHEN EXISTS (
            SELECT 1 FROM court_sessions cs
            WHERE cs.case_id = i.id AND cs.session_date BETWEEN NOW() AND NOW() + INTERVAL '7 days'
          ) THEN 'urgent'
          ELSE 'active'
        END as activity_status
      FROM issues i
      LEFT JOIN clients cl ON i.client_id = cl.id
      LEFT JOIN employees e ON e.id = 1
      WHERE ${whereClause}
      ORDER BY i.updated_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `

    queryParams.push(limit, offset)

    const casesResult = await client.query(casesQuery, queryParams)

    // جلب الإحصائيات (استخدام جدول issues)
    const statsQuery = `
      SELECT
        COUNT(*) as total_cases,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_cases,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_cases,
        COUNT(CASE WHEN status = 'closed' THEN 1 END) as closed_cases,
        COUNT(CASE WHEN status = 'high' THEN 1 END) as high_priority_cases,
        COUNT(CASE WHEN updated_at < NOW() - INTERVAL '30 days' THEN 1 END) as inactive_cases,
        (
          SELECT COUNT(*)
          FROM court_sessions
          WHERE session_date BETWEEN NOW() AND NOW() + INTERVAL '7 days'
        ) as upcoming_sessions,
        (
          SELECT COUNT(*)
          FROM court_sessions
          WHERE session_date::date = CURRENT_DATE
        ) as today_sessions
      FROM issues
    `

    const statsResult = await client.query(statsQuery)

    // جلب الجلسات القادمة (استخدام جدول issues)
    const upcomingSessionsQuery = `
      SELECT
        cs.*,
        i.case_number,
        i.title as case_title,
        cl.name as client_name,
        e.name as lawyer_name,
        CASE
          WHEN cs.session_date BETWEEN NOW() AND NOW() + INTERVAL '1 day' THEN 'urgent'
          WHEN cs.session_date BETWEEN NOW() AND NOW() + INTERVAL '3 days' THEN 'soon'
          ELSE 'upcoming'
        END as urgency_level
      FROM court_sessions cs
      JOIN issues i ON cs.case_id = i.id
      LEFT JOIN clients cl ON i.client_id = cl.id
      LEFT JOIN employees e ON e.id = 1
      WHERE cs.session_date BETWEEN NOW() AND NOW() + INTERVAL '7 days'
      ORDER BY cs.session_date ASC
      LIMIT 10
    `

    const upcomingSessionsResult = await client.query(upcomingSessionsQuery)

    return NextResponse.json({
      success: true,
      data: {
        cases: casesResult.rows,
        stats: statsResult.rows[0],
        upcoming_sessions: upcomingSessionsResult.rows,
        pagination: {
          page,
          limit,
          total: parseInt(statsResult.rows[0].total_cases)
        }
      }
    })

  } catch (error) {
    console.error('خطأ في جلب حركات القضايا:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب البيانات'
    }, { status: 500 })
  } finally {
    await client.end()
  }
}

// POST - إضافة حركة جديدة للقضية
export async function POST(request: NextRequest) {
  const client = new Client(dbConfig)

  try {
    await client.connect()

    const body = await request.json()
    const {
      case_id,
      movement_type,
      description,
      employee_id,
      next_action_required,
      priority_level = 'medium',
      client_notified = false
    } = body

    // التحقق من صحة البيانات
    if (!case_id || !movement_type || !description) {
      return NextResponse.json({
        success: false,
        error: 'البيانات المطلوبة مفقودة'
      }, { status: 400 })
    }

    // إدراج الحركة الجديدة
    const insertQuery = `
      INSERT INTO case_movements (
        case_id, movement_type, description, employee_id,
        next_action_required, priority_level, client_notified
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `

    const result = await client.query(insertQuery, [
      case_id, movement_type, description, employee_id,
      next_action_required, priority_level, client_notified
    ])

    // تحديث تاريخ آخر تحديث للقضية
    await client.query(
      'UPDATE cases SET updated_at = CURRENT_TIMESTAMP WHERE id = $1',
      [case_id]
    )

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم إضافة الحركة بنجاح'
    })

  } catch (error) {
    console.error('خطأ في إضافة حركة القضية:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في إضافة الحركة'
    }, { status: 500 })
  } finally {
    await client.end()
  }
}
