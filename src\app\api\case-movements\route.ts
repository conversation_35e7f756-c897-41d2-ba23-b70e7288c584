import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب حركة القضايا
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const caseId = searchParams.get('case_id')
    const movementType = searchParams.get('movement_type')
    const priority = searchParams.get('priority')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    let whereConditions = ['cm.status = $1']
    let queryParams: any[] = ['active']
    let paramIndex = 2

    if (caseId) {
      whereConditions.push(`cm.case_id = $${paramIndex}`)
      queryParams.push(parseInt(caseId))
      paramIndex++
    }

    if (movementType) {
      whereConditions.push(`cm.movement_type = $${paramIndex}`)
      queryParams.push(movementType)
      paramIndex++
    }

    if (priority) {
      whereConditions.push(`cm.priority = $${paramIndex}`)
      queryParams.push(priority)
      paramIndex++
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : ''

    const movementsQuery = `
      SELECT 
        cm.*,
        i.client_name,
        i.status as case_status,
        i.next_hearing as case_next_hearing,
        c.name as court_full_name,
        e.name as employee_name
      FROM case_movements cm
      LEFT JOIN issues i ON cm.case_id = i.id
      LEFT JOIN courts c ON i.court_id = c.id
      LEFT JOIN employees e ON cm.created_by = e.id
      ${whereClause}
      ORDER BY cm.created_at DESC, cm.priority DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `

    queryParams.push(limit, offset)
    const result = await query(movementsQuery, queryParams)

    // جلب إحصائيات
    const statsQuery = `
      SELECT 
        COUNT(*) as total_count,
        COUNT(CASE WHEN cm.movement_type = 'hearing_scheduled' THEN 1 END) as hearings_count,
        COUNT(CASE WHEN cm.priority = 'urgent' THEN 1 END) as urgent_count,
        COUNT(CASE WHEN cm.hearing_date >= CURRENT_DATE THEN 1 END) as upcoming_hearings
      FROM case_movements cm
      ${whereClause.replace(/LIMIT.*$/, '')}
    `

    const statsResult = await query(statsQuery, queryParams.slice(0, -2))

    // جلب الحركات القادمة (الجلسات)
    const upcomingQuery = `
      SELECT 
        cm.*,
        i.client_name,
        i.case_number,
        i.title as case_title
      FROM case_movements cm
      LEFT JOIN issues i ON cm.case_id = i.id
      WHERE cm.hearing_date >= CURRENT_DATE 
        AND cm.movement_type = 'hearing_scheduled'
        AND cm.status = 'active'
      ORDER BY cm.hearing_date ASC, cm.hearing_time ASC
      LIMIT 10
    `

    const upcomingResult = await query(upcomingQuery)

    return NextResponse.json({
      success: true,
      data: {
        movements: result.rows,
        stats: statsResult.rows[0],
        upcoming_hearings: upcomingResult.rows
      }
    })

  } catch (error) {
    console.error('خطأ في جلب حركة القضايا:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب حركة القضايا' },
      { status: 500 }
    )
  }
}

// POST - إضافة حركة جديدة (للاستخدام الداخلي فقط)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      case_id,
      movement_type,
      description,
      details,
      hearing_date,
      hearing_time,
      court_name,
      created_by,
      created_by_name,
      priority = 'normal',
      reference_id,
      reference_type,
      notes
    } = body

    if (!case_id || !movement_type || !description) {
      return NextResponse.json(
        { success: false, error: 'البيانات المطلوبة مفقودة' },
        { status: 400 }
      )
    }

    // استخدام الدالة المخصصة لإضافة الحركة
    const result = await query(`
      SELECT add_case_movement(
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12
      ) as movement_id
    `, [
      case_id,
      movement_type,
      description,
      details ? JSON.stringify(details) : null,
      hearing_date,
      hearing_time,
      court_name,
      created_by,
      created_by_name,
      priority,
      reference_id,
      reference_type
    ])

    // إضافة الملاحظات إذا وجدت
    if (notes && result.rows[0].movement_id) {
      await query(`
        UPDATE case_movements 
        SET notes = $1 
        WHERE id = $2
      `, [notes, result.rows[0].movement_id])
    }

    return NextResponse.json({
      success: true,
      data: { movement_id: result.rows[0].movement_id },
      message: 'تم إضافة الحركة بنجاح'
    })

  } catch (error) {
    console.error('خطأ في إضافة حركة القضية:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة حركة القضية' },
      { status: 500 }
    )
  }
}

// PUT - تحديث حركة (للملاحظات والتفاصيل فقط)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, notes, priority } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الحركة مطلوب' },
        { status: 400 }
      )
    }

    const updateFields = []
    const queryParams = []
    let paramIndex = 1

    if (notes !== undefined) {
      updateFields.push(`notes = $${paramIndex}`)
      queryParams.push(notes)
      paramIndex++
    }

    if (priority) {
      updateFields.push(`priority = $${paramIndex}`)
      queryParams.push(priority)
      paramIndex++
    }

    if (updateFields.length === 0) {
      return NextResponse.json(
        { success: false, error: 'لا توجد بيانات للتحديث' },
        { status: 400 }
      )
    }

    queryParams.push(id)
    
    await query(`
      UPDATE case_movements 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex}
    `, queryParams)

    return NextResponse.json({
      success: true,
      message: 'تم تحديث الحركة بنجاح'
    })

  } catch (error) {
    console.error('خطأ في تحديث حركة القضية:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث حركة القضية' },
      { status: 500 }
    )
  }
}
