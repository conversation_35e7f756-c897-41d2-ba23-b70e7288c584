'use client';

import { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Save, X, Eye, EyeOff } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardHeader, CardContent } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';

type Service = {
  id: number;
  title: string;
  slug: string;
  description: string;
  content?: string;
  icon_name: string;
  icon_color: string;
  image_url?: string;
  is_active: boolean;
  sort_order: number;
  meta_title?: string;
  meta_description?: string;
  created_date: string;
  updated_date: string;
};

const iconOptions = [
  'Gavel', 'FileText', 'Building', 'Shield', 'Briefcase', 
  'UserCheck', 'BookOpen', 'TrendingUp', 'CheckCircle', 'Search'
];

export default function ServicesLowManagement() {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editingService, setEditingService] = useState<Service | null>(null);
  const [isAddingNew, setIsAddingNew] = useState(false);

  // جلب الخدمات
  const fetchServices = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/serviceslow');
      const data = await response.json();
      
      if (data.success) {
        setServices(data.data || []);
      } else {
        setError('فشل في جلب الخدمات');
      }
    } catch (err) {
      console.error('خطأ في جلب الخدمات:', err);
      setError('حدث خطأ في جلب الخدمات');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchServices();
  }, []);

  // إضافة خدمة جديدة
  const handleAddService = async (serviceData: Partial<Service>) => {
    try {
      const response = await fetch('/api/serviceslow', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(serviceData),
      });

      const data = await response.json();
      
      if (data.success) {
        setServices([...services, data.data]);
        setIsAddingNew(false);
      } else {
        setError(data.error || 'فشل في إضافة الخدمة');
      }
    } catch (err) {
      console.error('خطأ في إضافة الخدمة:', err);
      setError('حدث خطأ في إضافة الخدمة');
    }
  };

  // تحديث خدمة
  const handleUpdateService = async (id: number, serviceData: Partial<Service>) => {
    try {
      const response = await fetch(`/api/serviceslow/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(serviceData),
      });

      const data = await response.json();
      
      if (data.success) {
        setServices(services.map(s => s.id === id ? data.data : s));
        setEditingService(null);
      } else {
        setError(data.error || 'فشل في تحديث الخدمة');
      }
    } catch (err) {
      console.error('خطأ في تحديث الخدمة:', err);
      setError('حدث خطأ في تحديث الخدمة');
    }
  };

  // حذف خدمة
  const handleDeleteService = async (id: number) => {
    if (!confirm('هل أنت متأكد من حذف هذه الخدمة؟')) return;

    try {
      const response = await fetch(`/api/serviceslow/${id}`, {
        method: 'DELETE',
      });

      const data = await response.json();
      
      if (data.success) {
        setServices(services.filter(s => s.id !== id));
      } else {
        setError(data.error || 'فشل في حذف الخدمة');
      }
    } catch (err) {
      console.error('خطأ في حذف الخدمة:', err);
      setError('حدث خطأ في حذف الخدمة');
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="mr-3">جاري تحميل الخدمات...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">إدارة خدمات الموقع الرئيسي</h1>
        <Button
          onClick={() => setIsAddingNew(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          <Plus className="w-4 h-4 ml-2" />
          إضافة خدمة جديدة
        </Button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
          {error}
          <button 
            onClick={() => setError(null)}
            className="float-left text-red-500 hover:text-red-700"
          >
            ×
          </button>
        </div>
      )}

      {/* نموذج إضافة خدمة جديدة */}
      {isAddingNew && (
        <ServiceForm
          onSave={handleAddService}
          onCancel={() => setIsAddingNew(false)}
          iconOptions={iconOptions}
        />
      )}

      {/* قائمة الخدمات */}
      <div className="grid gap-6">
        {services.map((service) => (
          <Card key={service.id} className="border border-gray-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="flex items-center space-x-4 space-x-reverse">
                <div 
                  className="w-10 h-10 rounded-lg flex items-center justify-center text-white text-sm font-bold"
                  style={{ backgroundColor: service.icon_color }}
                >
                  {service.icon_name.slice(0, 2)}
                </div>
                <div>
                  <h3 className="text-lg font-semibold">{service.title}</h3>
                  <p className="text-sm text-gray-500">/{service.slug}</p>
                </div>
              </div>
              <div className="flex items-center space-x-2 space-x-reverse">
                <div className="flex items-center space-x-2 space-x-reverse">
                  {service.is_active ? (
                    <Eye className="w-4 h-4 text-green-600" />
                  ) : (
                    <EyeOff className="w-4 h-4 text-gray-400" />
                  )}
                  <span className={`text-sm ${service.is_active ? 'text-green-600' : 'text-gray-400'}`}>
                    {service.is_active ? 'نشط' : 'غير نشط'}
                  </span>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setEditingService(service)}
                >
                  <Edit className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDeleteService(service.id)}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-2">{service.description}</p>
              <div className="flex items-center justify-between text-sm text-gray-500">
                <span>ترتيب: {service.sort_order}</span>
                <span>تم التحديث: {new Date(service.updated_date).toLocaleDateString('ar-SA')}</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* نموذج تحرير الخدمة */}
      {editingService && (
        <ServiceForm
          service={editingService}
          onSave={(data) => handleUpdateService(editingService.id, data)}
          onCancel={() => setEditingService(null)}
          iconOptions={iconOptions}
        />
      )}
    </div>
  );
}

// مكون النموذج
function ServiceForm({
  service,
  onSave,
  onCancel,
  iconOptions
}: {
  service?: Service;
  onSave: (data: Partial<Service>) => void;
  onCancel: () => void;
  iconOptions: string[];
}) {
  const [formData, setFormData] = useState({
    title: service?.title || '',
    slug: service?.slug || '',
    description: service?.description || '',
    content: service?.content || '',
    icon_name: service?.icon_name || 'Briefcase',
    icon_color: service?.icon_color || '#2563eb',
    image_url: service?.image_url || '',
    is_active: service?.is_active ?? true,
    sort_order: service?.sort_order || 0,
    meta_title: service?.meta_title || '',
    meta_description: service?.meta_description || ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title || !formData.slug) {
      alert('العنوان والرابط مطلوبان');
      return;
    }

    onSave(formData);
  };

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[أ-ي]/g, (match) => {
        const arabicToEnglish: { [key: string]: string } = {
          'أ': 'a', 'ب': 'b', 'ت': 't', 'ث': 'th', 'ج': 'j', 'ح': 'h', 'خ': 'kh',
          'د': 'd', 'ذ': 'dh', 'ر': 'r', 'ز': 'z', 'س': 's', 'ش': 'sh', 'ص': 's',
          'ض': 'd', 'ط': 't', 'ظ': 'z', 'ع': 'a', 'غ': 'gh', 'ف': 'f', 'ق': 'q',
          'ك': 'k', 'ل': 'l', 'م': 'm', 'ن': 'n', 'ه': 'h', 'و': 'w', 'ي': 'y'
        };
        return arabicToEnglish[match] || match;
      })
      .replace(/\s+/g, '-')
      .replace(/[^\w\-]+/g, '')
      .replace(/\-\-+/g, '-')
      .replace(/^-+/, '')
      .replace(/-+$/, '');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">
            {service ? 'تحرير الخدمة' : 'إضافة خدمة جديدة'}
          </h2>
          <Button variant="outline" size="sm" onClick={onCancel}>
            <X className="w-4 h-4" />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">العنوان *</label>
              <Input
                value={formData.title}
                onChange={(e) => {
                  const title = e.target.value;
                  setFormData({
                    ...formData,
                    title,
                    slug: formData.slug || generateSlug(title)
                  });
                }}
                placeholder="عنوان الخدمة"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">الرابط *</label>
              <Input
                value={formData.slug}
                onChange={(e) => setFormData({...formData, slug: e.target.value})}
                placeholder="service-slug"
                required
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">الوصف</label>
            <Textarea
              value={formData.description}
              onChange={(e) => setFormData({...formData, description: e.target.value})}
              placeholder="وصف مختصر للخدمة"
              rows={3}
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">المحتوى التفصيلي</label>
            <Textarea
              value={formData.content}
              onChange={(e) => setFormData({...formData, content: e.target.value})}
              placeholder="محتوى تفصيلي للخدمة"
              rows={5}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">الأيقونة</label>
              <select
                value={formData.icon_name}
                onChange={(e) => setFormData({...formData, icon_name: e.target.value})}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                {iconOptions.map(icon => (
                  <option key={icon} value={icon}>{icon}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">لون الأيقونة</label>
              <Input
                type="color"
                value={formData.icon_color}
                onChange={(e) => setFormData({...formData, icon_color: e.target.value})}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">ترتيب العرض</label>
              <Input
                type="number"
                value={formData.sort_order}
                onChange={(e) => setFormData({...formData, sort_order: parseInt(e.target.value) || 0})}
                min="0"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">رابط الصورة</label>
            <Input
              value={formData.image_url}
              onChange={(e) => setFormData({...formData, image_url: e.target.value})}
              placeholder="https://example.com/image.jpg"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">عنوان SEO</label>
              <Input
                value={formData.meta_title}
                onChange={(e) => setFormData({...formData, meta_title: e.target.value})}
                placeholder="عنوان محركات البحث"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">وصف SEO</label>
              <Input
                value={formData.meta_description}
                onChange={(e) => setFormData({...formData, meta_description: e.target.value})}
                placeholder="وصف محركات البحث"
              />
            </div>
          </div>

          <div className="flex items-center space-x-2 space-x-reverse">
            <Switch
              checked={formData.is_active}
              onCheckedChange={(checked) => setFormData({...formData, is_active: checked})}
            />
            <label className="text-sm font-medium">نشط</label>
          </div>

          <div className="flex justify-end space-x-2 space-x-reverse pt-4">
            <Button type="button" variant="outline" onClick={onCancel}>
              إلغاء
            </Button>
            <Button type="submit" className="bg-blue-600 hover:bg-blue-700 text-white">
              <Save className="w-4 h-4 ml-2" />
              حفظ
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
