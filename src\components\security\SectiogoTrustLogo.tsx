'use client';

import { useEffect } from 'react';
import Image from 'next/image';

interface SectiogoTrustLogoProps {
  size?: 'small' | 'medium' | 'large';
  className?: string;
  showTooltip?: boolean;
  position?: 'footer' | 'header' | 'checkout' | 'sidebar';
}

const SectiogoTrustLogo = ({ 
  size = 'medium', 
  className = '', 
  showTooltip = true,
  position = 'footer' 
}: SectiogoTrustLogoProps) => {
  // أحجام الشعارات المختلفة
  const logoSizes = {
    small: { width: 82, height: 32, image: 'sectigo-ssl-82x32.png' },
    medium: { width: 106, height: 42, image: 'sectigo-ssl-106x42.png' },
    large: { width: 140, height: 54, image: 'sectigo-ssl-140x54.png' }
  };

  const currentSize = logoSizes[size] || logoSizes.medium;

  // رابط التحقق من Sectigo
  const verificationUrl = `https://secure.trust-provider.com/ttb_searcher/trustlogo?v_querytype=W&v_shortname=SV&v_search=https://mohammi.com&x=6&y=5`;

  // أنماط مختلفة حسب الموضع
  const positionStyles = {
    footer: 'inline-block mx-2 my-1',
    header: 'inline-block mx-1',
    checkout: 'block mx-auto my-4 text-center',
    sidebar: 'block mb-4'
  };

  const containerClass = `${positionStyles[position]} ${className} transition-all duration-300 hover:opacity-80 hover:scale-105`;

  // تفعيل TrustLogo عند تحميل المكون
  useEffect(() => {
    // التأكد من تحميل سكريپت TrustLogo
    if (typeof window !== 'undefined') {
      const checkTrustLogo = () => {
        if (typeof (window as any).TrustLogo !== 'undefined') {
          try {
            (window as any).TrustLogo(
              `https://mohammi.com:7443/images/${currentSize.image}`, 
              "CL1", 
              "none"
            );
          } catch (error) {

          }
        } else {
          // إعادة المحاولة بعد 500ms
          setTimeout(checkTrustLogo, 500);
        }
      };

      checkTrustLogo();
    }
  }, [currentSize.image]);

  return (
    <div className="relative">
      <a
        href={verificationUrl}
        target="_blank"
        rel="noopener noreferrer"
        className={containerClass}
        title="تحقق من صحة شهادة SSL"
        aria-label="شهادة SSL من Sectigo - انقر للتحقق"
      >
        <Image
          src={`/images/${currentSize.image}`}
          alt="شهادة SSL من Sectigo"
          width={currentSize.width}
          height={currentSize.height}
          className="border-none"
          priority={position === 'checkout'}
          onError={(e) => {
            // في حالة عدم وجود الصورة، عرض نص بديل
            const target = e.target as HTMLImageElement;
            target.style.display = 'none';
            const parent = target.parentElement;
            if (parent) {
              parent.innerHTML = `
                <div style="
                  width: ${currentSize.width}px; 
                  height: ${currentSize.height}px; 
                  background: #0066cc; 
                  color: white; 
                  display: flex; 
                  align-items: center; 
                  justify-content: center; 
                  font-size: 10px; 
                  border-radius: 4px;
                  font-family: Arial, sans-serif;
                ">
                  🔐 SSL Secured
                </div>
              `;
            }
          }}
        />
      </a>

      {/* Tooltip عند التمرير */}
      {showTooltip && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg shadow-lg z-10 whitespace-nowrap opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none">
          <div className="text-center">
            <div className="font-semibold">🔐 موقع آمن</div>
            <div className="text-xs">شهادة SSL من Sectigo</div>
          </div>
          {/* سهم الـ tooltip */}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
        </div>
      )}
    </div>
  );
};

// مكون خاص لصفحة الدفع
export const CheckoutTrustBadge = () => {
  return (
    <div className="bg-green-50 border border-green-200 rounded-lg p-4 my-4">
      <div className="flex items-center justify-center space-x-3 rtl:space-x-reverse">
        <div className="text-green-600">
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
          </svg>
        </div>
        <div className="text-center">
          <div className="text-green-800 font-semibold">معاملتك محمية بـ SSL</div>
          <div className="text-green-600 text-sm">جميع البيانات مشفرة وآمنة</div>
        </div>
        <SectiogoTrustLogo size="medium" showTooltip={false} position="checkout" />
      </div>
    </div>
  );
};

// مكون للفوتر
export const FooterTrustLogos = () => {
  return (
    <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse py-4">
      <div className="text-gray-600 text-sm">موقع آمن ومحمي:</div>
      <SectiogoTrustLogo size="small" position="footer" />
      <div className="text-gray-500 text-xs">SSL 256-bit</div>
    </div>
  );
};

// مكون لشريط الأمان العلوي
export const SecurityBar = () => {
  return (
    <div className="bg-green-600 text-white py-2 px-4">
      <div className="container mx-auto flex items-center justify-center space-x-3 rtl:space-x-reverse">
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          <span className="text-sm font-medium">اتصال آمن ومشفر</span>
        </div>
        <SectiogoTrustLogo size="small" showTooltip={false} />
      </div>
    </div>
  );
};

export default SectiogoTrustLogo;
