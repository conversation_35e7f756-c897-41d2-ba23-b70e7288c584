'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Link2,
  Users,
  UserCheck,
  Search,
  Save,
  X,
  Trash2,
  AlertCircle,
  CheckCircle,
  Plus,
  Building,
  UserPlus,
  Settings
} from 'lucide-react'

interface ChartAccount {
  id: number
  account_code: string
  account_name: string
  account_type: string
  account_level: number
  linked_table?: string
  auto_create_sub_accounts?: boolean
}

interface Client {
  id: number
  name: string
  phone: string
  email: string
  status: string
}

interface Employee {
  id: number
  name: string
  position: string
  phone: string
  status: string
}

interface LinkingRule {
  id: string
  control_account_id: number
  control_account_name: string
  linked_table: 'clients' | 'employees'
  auto_create: boolean
  account_prefix: string
}

export default function LinkAccountsPage() {
  const [controlAccounts, setControlAccounts] = useState<ChartAccount[]>([])
  const [clients, setClients] = useState<Client[]>([])
  const [employees, setEmployees] = useState<Employee[]>([])
  const [linkingRules, setLinkingRules] = useState<LinkingRule[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)

  // حالة إضافة قاعدة ربط جديدة
  const [isAddingRule, setIsAddingRule] = useState(false)
  const [newRule, setNewRule] = useState<Partial<LinkingRule>>({
    control_account_id: 0,
    linked_table: 'clients',
    auto_create: true,
    account_prefix: 'C'
  })

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setIsLoading(true)
      
      // جلب حسابات التحكم (المستوى 3 فقط)
      const accountsResponse = await fetch('/api/accounting/chart-of-accounts?level=3')
      const accountsData = await accountsResponse.json()
      
      // جلب العملاء
      const clientsResponse = await fetch('/api/clients')
      const clientsData = await clientsResponse.json()
      
      // جلب الموظفين
      const employeesResponse = await fetch('/api/employees')
      const employeesData = await employeesResponse.json()

      if (accountsData.success) {
        setControlAccounts(accountsData.accounts || [])
      }
      
      if (clientsData.success) {
        setClients(clientsData.data || [])
      }
      
      if (employeesData.success) {
        setEmployees(employeesData.data || [])
      }

      // جلب قواعد الربط الحالية
      await fetchLinkingRules()

    } catch (error) {
      console.error('خطأ في جلب البيانات:', error)
      setMessage({ type: 'error', text: 'حدث خطأ في جلب البيانات' })
    } finally {
      setIsLoading(false)
    }
  }

  const fetchLinkingRules = async () => {
    try {
      const response = await fetch('/api/accounting/chart-of-accounts')
      const data = await response.json()
      
      if (data.success) {
        const rules = data.accounts
          .filter((account: ChartAccount) => account.linked_table && account.auto_create_sub_accounts)
          .map((account: ChartAccount) => ({
            id: account.id.toString(),
            control_account_id: account.id,
            control_account_name: account.account_name,
            linked_table: account.linked_table as 'clients' | 'employees',
            auto_create: account.auto_create_sub_accounts || false,
            account_prefix: account.linked_table === 'clients' ? 'C' : 'E'
          }))
        
        setLinkingRules(rules)
      }
    } catch (error) {
      console.error('خطأ في جلب قواعد الربط:', error)
    }
  }

  const handleAddRule = async () => {
    try {
      if (!newRule.control_account_id || !newRule.linked_table) {
        setMessage({ type: 'error', text: 'يرجى ملء جميع الحقول المطلوبة' })
        return
      }

      const response = await fetch(`/api/accounting/chart-of-accounts`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: newRule.control_account_id,
          linked_table: newRule.linked_table,
          auto_create_sub_accounts: newRule.auto_create
        }),
      })

      const data = await response.json()

      if (data.success) {
        setMessage({ type: 'success', text: 'تم إضافة قاعدة الربط بنجاح' })
        setIsAddingRule(false)
        setNewRule({
          control_account_id: 0,
          linked_table: 'clients',
          auto_create: true,
          account_prefix: 'C'
        })
        await fetchLinkingRules()
      } else {
        setMessage({ type: 'error', text: data.error || 'فشل في إضافة قاعدة الربط' })
      }

    } catch (error) {
      console.error('خطأ في إضافة قاعدة الربط:', error)
      setMessage({ type: 'error', text: 'حدث خطأ أثناء إضافة قاعدة الربط' })
    }
  }

  const handleRemoveRule = async (ruleId: string) => {
    if (!confirm('هل أنت متأكد من حذف قاعدة الربط هذه؟')) return

    try {
      const rule = linkingRules.find(r => r.id === ruleId)
      if (!rule) return

      const response = await fetch(`/api/accounting/chart-of-accounts`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: rule.control_account_id,
          linked_table: null,
          auto_create_sub_accounts: false
        }),
      })

      const data = await response.json()

      if (data.success) {
        setMessage({ type: 'success', text: 'تم حذف قاعدة الربط بنجاح' })
        await fetchLinkingRules()
      } else {
        setMessage({ type: 'error', text: data.error || 'فشل في حذف قاعدة الربط' })
      }

    } catch (error) {
      console.error('خطأ في حذف قاعدة الربط:', error)
      setMessage({ type: 'error', text: 'حدث خطأ أثناء حذف قاعدة الربط' })
    }
  }

  const generateAccountCode = (type: 'clients' | 'employees', id: number) => {
    const prefix = type === 'clients' ? 'C' : 'E'
    return prefix + String(id).padStart(6, '0')
  }

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Link2 className="h-8 w-8 animate-spin mx-auto mb-2" />
            <span>جاري التحميل...</span>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان والوصف */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 space-x-reverse">
            <Settings className="h-8 w-8 text-emerald-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">الحسابات الافتراضية</h1>
              <p className="text-gray-600">تحديد الحسابات الافتراضية للصندوق والعملاء والموظفين والمصروفات والإيرادات مع الربط التلقائي</p>
            </div>
          </div>
          <Button
            onClick={() => setIsAddingRule(true)}
            className="bg-emerald-600 hover:bg-emerald-700"
          >
            <Plus className="h-4 w-4 ml-2" />
            إضافة قاعدة ربط
          </Button>
        </div>

        {/* رسائل التنبيه */}
        {message && (
          <Alert className={message.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className={message.type === 'success' ? 'text-green-800' : 'text-red-800'}>
              {message.text}
            </AlertDescription>
          </Alert>
        )}

        {/* إحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-blue-600">
                {controlAccounts.length}
              </div>
              <div className="text-sm text-gray-600">حسابات التحكم</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-green-600">
                {clients.length}
              </div>
              <div className="text-sm text-gray-600">العملاء النشطين</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-purple-600">
                {employees.length}
              </div>
              <div className="text-sm text-gray-600">الموظفين النشطين</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-emerald-600">
                {linkingRules.length}
              </div>
              <div className="text-sm text-gray-600">قواعد الربط النشطة</div>
            </CardContent>
          </Card>
        </div>

        {/* التبويبات */}
        <Tabs defaultValue="rules" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="rules">قواعد الربط</TabsTrigger>
            <TabsTrigger value="clients">حسابات العملاء</TabsTrigger>
            <TabsTrigger value="employees">حسابات الموظفين</TabsTrigger>
          </TabsList>

          {/* تبويب قواعد الربط */}
          <TabsContent value="rules">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Settings className="h-5 w-5 ml-2" />
                  قواعد الربط النشطة ({linkingRules.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {linkingRules.length === 0 ? (
                  <div className="text-center py-8">
                    <Link2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">لا توجد قواعد ربط نشطة</p>
                    <p className="text-sm text-gray-400">انقر على "إضافة قاعدة ربط" لبدء الإعداد</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {linkingRules.map((rule) => (
                      <div key={rule.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center space-x-4 space-x-reverse">
                          <div className={`p-2 rounded-lg ${rule.linked_table === 'clients' ? 'bg-green-100' : 'bg-purple-100'}`}>
                            {rule.linked_table === 'clients' ? (
                              <Users className={`h-5 w-5 ${rule.linked_table === 'clients' ? 'text-green-600' : 'text-purple-600'}`} />
                            ) : (
                              <UserCheck className={`h-5 w-5 ${rule.linked_table === 'clients' ? 'text-green-600' : 'text-purple-600'}`} />
                            )}
                          </div>
                          <div>
                            <div className="font-medium">{rule.control_account_name}</div>
                            <div className="text-sm text-gray-500">
                              مرتبط بـ {rule.linked_table === 'clients' ? 'العملاء' : 'الموظفين'} 
                              - بادئة الحساب: {rule.account_prefix}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <Badge variant={rule.auto_create ? 'default' : 'secondary'}>
                            {rule.auto_create ? 'إنشاء تلقائي' : 'يدوي'}
                          </Badge>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveRule(rule.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* تبويب حسابات العملاء */}
          <TabsContent value="clients">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="h-5 w-5 ml-2" />
                  حسابات العملاء ({clients.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {clients.length === 0 ? (
                  <div className="text-center py-8">
                    <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">لا يوجد عملاء نشطين</p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-right p-3">رقم الحساب</th>
                          <th className="text-right p-3">اسم العميل</th>
                          <th className="text-right p-3">الهاتف</th>
                          <th className="text-right p-3">البريد الإلكتروني</th>
                          <th className="text-center p-3">الحالة</th>
                        </tr>
                      </thead>
                      <tbody>
                        {clients.map((client) => (
                          <tr key={client.id} className="border-b hover:bg-gray-50">
                            <td className="p-3 font-mono text-green-600 font-medium">
                              {generateAccountCode('clients', client.id)}
                            </td>
                            <td className="p-3 font-medium">{client.name}</td>
                            <td className="p-3 text-gray-600">{client.phone}</td>
                            <td className="p-3 text-gray-600">{client.email || '-'}</td>
                            <td className="p-3 text-center">
                              <Badge variant={client.status === 'active' ? 'default' : 'secondary'}>
                                {client.status === 'active' ? 'نشط' : 'غير نشط'}
                              </Badge>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* تبويب حسابات الموظفين */}
          <TabsContent value="employees">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <UserCheck className="h-5 w-5 ml-2" />
                  حسابات الموظفين ({employees.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {employees.length === 0 ? (
                  <div className="text-center py-8">
                    <UserCheck className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">لا يوجد موظفين نشطين</p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-right p-3">رقم الحساب</th>
                          <th className="text-right p-3">اسم الموظف</th>
                          <th className="text-right p-3">المنصب</th>
                          <th className="text-right p-3">الهاتف</th>
                          <th className="text-center p-3">الحالة</th>
                        </tr>
                      </thead>
                      <tbody>
                        {employees.map((employee) => (
                          <tr key={employee.id} className="border-b hover:bg-gray-50">
                            <td className="p-3 font-mono text-purple-600 font-medium">
                              {generateAccountCode('employees', employee.id)}
                            </td>
                            <td className="p-3 font-medium">{employee.name}</td>
                            <td className="p-3 text-gray-600">{employee.position}</td>
                            <td className="p-3 text-gray-600">{employee.phone}</td>
                            <td className="p-3 text-center">
                              <Badge variant={employee.status === 'نشط' ? 'default' : 'secondary'}>
                                {employee.status}
                              </Badge>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* نافذة إضافة قاعدة ربط */}
        {isAddingRule && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">إضافة قاعدة ربط جديدة</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsAddingRule(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="control-account">حساب التحكم</Label>
                  <Select
                    value={newRule.control_account_id?.toString() || ''}
                    onValueChange={(value) => setNewRule({ ...newRule, control_account_id: parseInt(value) })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر حساب التحكم" />
                    </SelectTrigger>
                    <SelectContent>
                      {controlAccounts.map((account) => (
                        <SelectItem key={account.id} value={account.id.toString()}>
                          {account.account_code} - {account.account_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="linked-table">نوع الربط</Label>
                  <Select
                    value={newRule.linked_table || 'clients'}
                    onValueChange={(value: 'clients' | 'employees') => {
                      setNewRule({ 
                        ...newRule, 
                        linked_table: value,
                        account_prefix: value === 'clients' ? 'C' : 'E'
                      })
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="clients">العملاء</SelectItem>
                      <SelectItem value="employees">الموظفين</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="account-prefix">بادئة رقم الحساب</Label>
                  <Input
                    id="account-prefix"
                    value={newRule.account_prefix || ''}
                    onChange={(e) => setNewRule({ ...newRule, account_prefix: e.target.value })}
                    placeholder="مثل: C للعملاء، E للموظفين"
                    maxLength={2}
                  />
                </div>

                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="checkbox"
                    id="auto-create"
                    checked={newRule.auto_create || false}
                    onChange={(e) => setNewRule({ ...newRule, auto_create: e.target.checked })}
                    className="rounded"
                  />
                  <Label htmlFor="auto-create">إنشاء الحسابات تلقائياً</Label>
                </div>

                <div className="flex justify-end space-x-2 space-x-reverse pt-4 border-t">
                  <Button
                    variant="outline"
                    onClick={() => setIsAddingRule(false)}
                  >
                    إلغاء
                  </Button>
                  <Button
                    onClick={handleAddRule}
                    className="bg-emerald-600 hover:bg-emerald-700"
                  >
                    <Save className="h-4 w-4 ml-2" />
                    حفظ القاعدة
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}