/**
 * API للحصول على حالة WhatsApp (ديناميكي حسب المنفذ)
 */

export const runtime = 'nodejs'
export const dynamic = 'force-dynamic'

import { NextRequest, NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'

let whatsappService: any = null

async function getWhatsAppService() {
  if (!whatsappService) {
    try {
      // إعادة استخدام نفس الخدمة المستخدمة في /api/whatsapp/control مع دعم fallback
      whatsappService = require('../../../../../whatsapp-real-service')
    } catch (error: any) {
      try {
        const servicePath = path.join(process.cwd(), 'whatsapp-real-service.js')
        whatsappService = require(servicePath)
      } catch (altError: any) {
        throw new Error(`فشل تحميل خدمة WhatsApp: ${error?.message || ''} | ${altError?.message || ''}`)
      }
    }
  }
  return whatsappService
}

function loadRoutingConfig(): any | null {
  try {
    const cwd = process.cwd()
    const mainPath = path.join(cwd, 'routing.config.json')
    const prodPath = path.join(cwd, 'production', 'routing.config.json')
    const configPath = fs.existsSync(mainPath) ? mainPath : (fs.existsSync(prodPath) ? prodPath : '')
    if (!configPath) return null
    return JSON.parse(fs.readFileSync(configPath, 'utf-8'))
  } catch (e) {
    return null
  }
}

// تحميل إعدادات WhatsApp الخاصة بكل منفذ (إن وجدت)
function loadWhatsAppConfig(): any | null {
  try {
    const cwd = process.cwd()
    const mainPath = path.join(cwd, 'whatsapp.config.json')
    const prodPath = path.join(cwd, 'production', 'whatsapp.config.json')
    const configPath = fs.existsSync(mainPath) ? mainPath : (fs.existsSync(prodPath) ? prodPath : '')
    if (!configPath) return null
    return JSON.parse(fs.readFileSync(configPath, 'utf-8'))
  } catch (e) {
    return null
  }
}

function getRouteForRequest(request: NextRequest) {
  // دعم المنافذ عبر البروكسي: X-Forwarded-Host / X-Forwarded-Port
  const xfHost = request.headers.get('x-forwarded-host') || ''
  const xfPortHeader = request.headers.get('x-forwarded-port') || ''

  const host = xfHost || request.headers.get('host') || ''
  const portStr = xfPortHeader || (host.includes(':') ? host.split(':').pop() : '')
  const port = portStr ? parseInt(portStr, 10) : NaN

  const cfg = loadRoutingConfig()
  if (!cfg) return { key: 'default', config: {} }
  const routes = cfg.routes || {}
  const entries = Object.entries(routes) as Array<[string, any]>

  // 1) مطابقة المنفذ الداخلي (Next.js الداخلي)
  let matched = entries.find(([, v]) => Number(v?.internal_next_port) === port)
  if (matched) return { key: matched[0], config: matched[1], raw: cfg }

  // 2) مطابقة المنفذ الخارجي (مفتاح route مثل 7443/8914)
  if (portStr && entries.some(([k]) => k === portStr)) {
    matched = entries.find(([k]) => k === portStr) as [string, any] | undefined
    if (matched) return { key: matched[0], config: matched[1], raw: cfg }
  }

  return { key: 'default', config: cfg.default_config || {}, raw: cfg }
}

export async function GET(_request: NextRequest) {
  return NextResponse.json({
    success: false,
    error: 'تم تعطيل مسار حالة WhatsApp المحلي. النظام يستخدم الإرسال عبر Cloud API فقط.'
  }, { status: 410 })
}
