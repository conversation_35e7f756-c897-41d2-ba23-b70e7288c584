// إعداد البيانات الأساسية بعد الاستعادة
const { Client } = require('pg');

async function setupBasicData() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'mohammi',
    user: 'postgres',
    password: 'yemen123'
  });
  
  try {
    await client.connect();
    console.log('✅ متصل بقاعدة البيانات');

    // 1. إنشاء بيانات الشركة
    console.log('\n🏢 إنشاء بيانات الشركة...');
    
    await client.query(`
      INSERT INTO companies (
        name, legal_name, registration_number, tax_number, address, city, country,
        phone, email, website, established_date, legal_form, capital, description,
        logo_url, logo_right_text, logo_left_text, logo_image_url, is_active
      ) VALUES (
        'مؤسسة الجرافي للمحاماة والاستشارات القانونية',
        'مؤسسة الجرافي للمحاماة والاستشارات القانونية المحدودة',
        'CR-2024-001',
        'TAX-*********',
        'صنعاء- شارع مجاهد- عمارة الحاشدي',
        'صنعاء',
        'اليمن',
        '+967-1-123456',
        '<EMAIL>',
        'www.legalfirm.ye',
        '2020-01-14',
        'شركة محدودة المسؤولية',
        1000000.00,
        'مكتب متخصص في تقديم الخدمات القانونية والاستشارات القانونية في جميع المجالات',
        '/images/company-logo.png',
        'مؤسسة الجرافي للمحاماة والاستشارات القانونية',
        'صنعاء - شارع تعز - امام بريد شميلة',
        '/images/logo.png',
        true
      ) ON CONFLICT DO NOTHING
    `);
    
    console.log('✅ تم إنشاء بيانات الشركة');

    // 2. إنشاء أدوار المستخدمين
    console.log('\n👥 إنشاء أدوار المستخدمين...');
    
    await client.query(`
      INSERT INTO user_roles (role_name, display_name, description)
      VALUES 
        ('admin', 'مدير النظام', 'صلاحيات كاملة للنظام'),
        ('manager', 'مدير', 'صلاحيات إدارية'),
        ('lawyer', 'محامي', 'صلاحيات المحامي'),
        ('secretary', 'سكرتير', 'صلاحيات السكرتارية'),
        ('user', 'مستخدم', 'صلاحيات أساسية')
      ON CONFLICT (role_name) DO NOTHING
    `);
    
    console.log('✅ تم إنشاء أدوار المستخدمين');

    // 3. إنشاء المستخدم admin
    console.log('\n🔑 إنشاء المستخدم admin...');
    
    await client.query(`
      INSERT INTO users (
        username, password_hash, email, role, status, is_online, login_attempts,
        created_date, user_type
      ) VALUES (
        'admin',
        'admin123',
        '<EMAIL>',
        'admin',
        'active',
        false,
        0,
        CURRENT_DATE,
        'admin'
      ) ON CONFLICT (username) DO UPDATE SET
        password_hash = 'admin123',
        email = '<EMAIL>',
        role = 'admin',
        status = 'active'
    `);
    
    console.log('✅ تم إنشاء المستخدم admin');

    // 4. إنشاء بعض الموظفين
    console.log('\n👨‍💼 إنشاء الموظفين...');
    
    await client.query(`
      INSERT INTO employees (name, position, phone, email, hire_date, is_active)
      VALUES 
        ('أحمد محمد علي', 'محامي أول', '+967-777-123456', '<EMAIL>', CURRENT_DATE, true),
        ('فاطمة سالم', 'محامية', '+967-777-234567', '<EMAIL>', CURRENT_DATE, true),
        ('محمد حسن', 'سكرتير', '+967-777-345678', '<EMAIL>', CURRENT_DATE, true)
      ON CONFLICT DO NOTHING
    `);
    
    console.log('✅ تم إنشاء الموظفين');

    // 5. إنشاء بعض الخدمات
    console.log('\n⚖️ إنشاء الخدمات القانونية...');
    
    await client.query(`
      INSERT INTO services (name, description, price, is_active, created_date)
      VALUES 
        ('استشارة قانونية', 'تقديم استشارة قانونية شاملة', 100.00, true, CURRENT_DATE),
        ('صياغة عقود', 'صياغة ومراجعة العقود القانونية', 500.00, true, CURRENT_DATE),
        ('تمثيل قضائي', 'تمثيل الموكل أمام المحاكم', 1000.00, true, CURRENT_DATE),
        ('تأسيس شركات', 'خدمات تأسيس الشركات والمؤسسات', 2000.00, true, CURRENT_DATE)
      ON CONFLICT DO NOTHING
    `);
    
    console.log('✅ تم إنشاء الخدمات القانونية');

    // 6. إنشاء بعض المحاكم
    console.log('\n🏛️ إنشاء المحاكم...');
    
    await client.query(`
      INSERT INTO courts (name, type, address, phone, is_active)
      VALUES 
        ('المحكمة الابتدائية بصنعاء', 'ابتدائية', 'صنعاء - شارع الستين', '+967-1-111111', true),
        ('محكمة الاستئناف بصنعاء', 'استئناف', 'صنعاء - شارع الزبيري', '+967-1-222222', true),
        ('المحكمة التجارية', 'تجارية', 'صنعاء - شارع الحصبة', '+967-1-333333', true)
      ON CONFLICT DO NOTHING
    `);
    
    console.log('✅ تم إنشاء المحاكم');

    // 7. إنشاء أنواع القضايا
    console.log('\n📋 إنشاء أنواع القضايا...');
    
    await client.query(`
      INSERT INTO issue_types (name, description, is_active)
      VALUES 
        ('مدني', 'قضايا مدنية وتجارية', true),
        ('جنائي', 'قضايا جنائية', true),
        ('أحوال شخصية', 'قضايا الأحوال الشخصية', true),
        ('إداري', 'قضايا إدارية', true),
        ('عمالي', 'قضايا عمالية', true)
      ON CONFLICT DO NOTHING
    `);
    
    console.log('✅ تم إنشاء أنواع القضايا');

    // 8. إنشاء النسب المالية
    console.log('\n💰 إنشاء النسب المالية...');
    
    await client.query(`
      INSERT INTO lineages (service_name, percentage, description, is_active)
      VALUES 
        ('استشارة قانونية', 10.00, 'نسبة المكتب من الاستشارات', true),
        ('صياغة عقود', 15.00, 'نسبة المكتب من صياغة العقود', true),
        ('تمثيل قضائي', 20.00, 'نسبة المكتب من التمثيل القضائي', true),
        ('تأسيس شركات', 25.00, 'نسبة المكتب من تأسيس الشركات', true)
      ON CONFLICT DO NOTHING
    `);
    
    console.log('✅ تم إنشاء النسب المالية');

    // 9. إنشاء روابط التذييل
    console.log('\n🔗 إنشاء روابط التذييل...');
    
    await client.query(`
      INSERT INTO footer_links (title, url, category, is_active, sort_order)
      VALUES 
        ('الرئيسية', '/', 'main', true, 1),
        ('من نحن', '/about', 'main', true, 2),
        ('خدماتنا', '/services', 'main', true, 3),
        ('اتصل بنا', '/contact', 'main', true, 4),
        ('سياسة الخصوصية', '/privacy', 'legal', true, 5),
        ('شروط الاستخدام', '/terms', 'legal', true, 6)
      ON CONFLICT DO NOTHING
    `);
    
    console.log('✅ تم إنشاء روابط التذييل');

    // 10. التحقق من البيانات
    console.log('\n🔍 التحقق من البيانات المُدرجة...');
    
    const checks = [
      { table: 'companies', name: 'الشركات' },
      { table: 'users', name: 'المستخدمين' },
      { table: 'employees', name: 'الموظفين' },
      { table: 'services', name: 'الخدمات' },
      { table: 'courts', name: 'المحاكم' },
      { table: 'issue_types', name: 'أنواع القضايا' },
      { table: 'lineages', name: 'النسب المالية' },
      { table: 'footer_links', name: 'روابط التذييل' }
    ];
    
    for (const check of checks) {
      try {
        const result = await client.query(`SELECT COUNT(*) as count FROM ${check.table}`);
        console.log(`   ✅ ${check.name}: ${result.rows[0].count} سجل`);
      } catch (error) {
        console.log(`   ❌ ${check.name}: خطأ - ${error.message}`);
      }
    }

    // 11. فحص المستخدم admin
    console.log('\n👤 فحص المستخدم admin...');
    
    const adminResult = await client.query(`
      SELECT username, email, role, status 
      FROM users 
      WHERE username = 'admin'
    `);
    
    if (adminResult.rows.length > 0) {
      const admin = adminResult.rows[0];
      console.log('✅ المستخدم admin موجود:');
      console.log(`   اسم المستخدم: ${admin.username}`);
      console.log(`   البريد: ${admin.email}`);
      console.log(`   الدور: ${admin.role}`);
      console.log(`   الحالة: ${admin.status}`);
    } else {
      console.log('❌ المستخدم admin غير موجود');
    }

    await client.end();
    
    console.log('\n🎉 تم إعداد البيانات الأساسية بنجاح!');
    console.log('✅ قاعدة البيانات جاهزة للاستخدام');
    console.log('🔑 بيانات تسجيل الدخول: admin / admin123');
    console.log('🌐 يمكنك الآن تشغيل النظام على: http://localhost:7443');
    
  } catch (error) {
    console.error('❌ خطأ في إعداد البيانات:', error.message);
  }
}

setupBasicData();
