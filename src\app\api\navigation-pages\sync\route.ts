import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'
import fs from 'fs'
import path from 'path'

// Helpers
function titleize(segment: string): string {
  if (!segment) return ''
  // Replace dashes/underscores with spaces and uppercase first letter of words (keep as-is for Arabic)
  const s = segment.replace(/[-_]+/g, ' ').trim()
  return s
    .split(' ')
    .map(w => (/[A-Za-z]/.test(w.charAt(0)) ? w.charAt(0).toUpperCase() + w.slice(1) : w))
    .join(' ')
}

function computeUrls(baseDir: string): string[] {
  const urls: string[] = []
  const ignoreDirs = new Set(['api', '_next', 'node_modules'])

  function walk(dir: string, relative: string) {
    const entries = fs.readdirSync(dir, { withFileTypes: true })
    for (const entry of entries) {
      // Skip hidden directories or special routing groups ()
      if (entry.isDirectory()) {
        if (ignoreDirs.has(entry.name) || entry.name.startsWith('.')) continue
        if (entry.name.startsWith('(') && entry.name.endsWith(')')) continue
        walk(path.join(dir, entry.name), path.join(relative, entry.name))
      } else {
        if (entry.name === 'page.tsx' || entry.name === 'page.ts') {
          // Build URL
          const full = path.join(dir, entry.name)
          let url = '/' + relative.replace(/\\/g, '/').replace(/\/+/g, '/').replace(/^\/+/, '')
          // If it's an index page under src/app (e.g., src/app/page.tsx) => '/'
          if (relative === '') url = '/'
          // If dynamic segment, keep it as is (e.g., [id])
          // Store URL
          urls.push(url)
        }
      }
    }
  }

  walk(baseDir, '')
  // Deduplicate and filter out api
  return Array.from(new Set(urls)).filter(u => !u.startsWith('/api'))
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json().catch(() => ({} as any))
    const mode = body?.mode || 'upsert_missing' // 'upsert_missing' | 'force_update'

    const baseDir = path.join(process.cwd(), 'src', 'app')
    if (!fs.existsSync(baseDir)) {
      return NextResponse.json({ success: false, error: 'app directory not found' }, { status: 500 })
    }

    // تأكيد وجود جدول navigation_pages
    await query(`
      CREATE TABLE IF NOT EXISTS navigation_pages (
        id SERIAL PRIMARY KEY,
        page_title VARCHAR(255) NOT NULL,
        page_url VARCHAR(512) UNIQUE NOT NULL,
        page_description TEXT DEFAULT '',
        category VARCHAR(100) DEFAULT '',
        keywords VARCHAR(255) DEFAULT '',
        is_active BOOLEAN DEFAULT TRUE,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    const urls = computeUrls(baseDir)

    const added: any[] = []
    const updated: any[] = []
    const skipped: any[] = []

    for (const url of urls) {
      // Derive title and category
      const segs = url.split('/').filter(Boolean)
      const category = segs[0] || 'misc'
      const last = segs.length === 0 ? 'home' : segs[segs.length - 1]
      const page_title = segs.length === 0 ? 'الصفحة الرئيسية' : titleize(last)

      // Check existence
      const existing = await query('SELECT id, page_title, category FROM navigation_pages WHERE page_url = $1', [url])
      if (existing.rows.length === 0) {
        const ins = await query(
          `INSERT INTO navigation_pages (page_title, page_url, page_description, category, keywords, is_active)
           VALUES ($1, $2, $3, $4, $5, true)
           RETURNING *`,
          [page_title, url, '', category, '']
        )
        added.push(ins.rows[0])
      } else {
        // Update strategy:
        // - force_update: always update title/category to derived values
        // - upsert_missing: update only if empty
        const row = existing.rows[0]
        if (mode === 'force_update') {
          const upd = await query(
            `UPDATE navigation_pages SET page_title = $1, category = $2, updated_at = CURRENT_TIMESTAMP WHERE id = $3 RETURNING *`,
            [page_title, category, row.id]
          )
          updated.push(upd.rows[0])
        } else {
          if ((!row.page_title || row.page_title.trim() === '') || (!row.category || row.category.trim() === '')) {
            const upd = await query(
              `UPDATE navigation_pages SET page_title = COALESCE(NULLIF($1, ''), page_title), category = COALESCE(NULLIF($2, ''), category), updated_at = CURRENT_TIMESTAMP WHERE id = $3 RETURNING *`,
              [row.page_title && row.page_title.trim() !== '' ? row.page_title : page_title, row.category && row.category.trim() !== '' ? row.category : category, row.id]
            )
            updated.push(upd.rows[0])
          } else {
            skipped.push(row)
          }
        }
      }
    }

    return NextResponse.json({ success: true, added_count: added.length, updated_count: updated.length, total_urls: urls.length, mode, added, updated, skipped })
  } catch (error: any) {
    console.error('Sync navigation pages error:', error)
    return NextResponse.json({ success: false, error: error?.message || 'Unknown error' }, { status: 500 })
  }
}

export async function GET() {
  // For convenience, allow GET to trigger a dry-run listing only
  try {
    const baseDir = path.join(process.cwd(), 'src', 'app')
    if (!fs.existsSync(baseDir)) {
      return NextResponse.json({ success: false, error: 'app directory not found' }, { status: 500 })
    }
    const urls = computeUrls(baseDir)
    return NextResponse.json({ success: true, urls, count: urls.length })
  } catch (error: any) {
    console.error('List navigation pages error:', error)
    return NextResponse.json({ success: false, error: error?.message || 'Unknown error' }, { status: 500 })
  }
}
