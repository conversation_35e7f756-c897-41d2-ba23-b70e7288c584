// اختبار API تسجيل الدخول
const fetch = require('node-fetch');

async function testLoginAPI() {
  try {
    console.log('🧪 اختبار API تسجيل الدخول...');
    console.log('🌐 الرابط: http://localhost:7443/api/auth/users');
    
    const response = await fetch('http://localhost:7443/api/auth/users', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123'
      })
    });

    console.log(`📊 حالة الاستجابة: ${response.status} ${response.statusText}`);
    
    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ تسجيل الدخول نجح!');
      console.log('📋 بيانات الاستجابة:');
      console.log(`   الرسالة: ${result.message}`);
      console.log(`   اسم المستخدم: ${result.user?.username}`);
      console.log(`   البريد: ${result.user?.email}`);
      console.log(`   الدور: ${result.user?.role}`);
      console.log(`   الرمز المميز: ${result.token}`);
    } else {
      console.log('❌ تسجيل الدخول فشل!');
      console.log(`📋 رسالة الخطأ: ${result.error}`);
      console.log('📋 تفاصيل الاستجابة:', result);
    }

  } catch (error) {
    console.error('❌ خطأ في الاتصال:', error.message);
    
    // اختبار إضافي للتأكد من أن الخادم يعمل
    try {
      console.log('\n🔍 اختبار الاتصال بالخادم...');
      const healthCheck = await fetch('http://localhost:7443/');
      console.log(`📊 حالة الخادم: ${healthCheck.status} ${healthCheck.statusText}`);
    } catch (serverError) {
      console.error('❌ الخادم لا يعمل:', serverError.message);
    }
  }
}

testLoginAPI();
