// فحص بنية الجداول
const { Client } = require('pg');

async function examineTableStructure() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'moham<PERSON>',
    user: 'postgres',
    password: 'yemen123'
  });
  
  try {
    await client.connect();
    console.log('✅ متصل بقاعدة البيانات');

    // الجداول المهمة للفحص
    const importantTables = [
      'companies', 'users', 'employees', 'services', 
      'courts', 'issue_types', 'lineages', 'footer_links'
    ];

    for (const tableName of importantTables) {
      console.log(`\n📋 بنية جدول ${tableName}:`);
      console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
      
      try {
        const result = await client.query(`
          SELECT column_name, data_type, is_nullable, column_default
          FROM information_schema.columns 
          WHERE table_name = $1 AND table_schema = 'public'
          ORDER BY ordinal_position
        `, [tableName]);
        
        if (result.rows.length > 0) {
          result.rows.forEach((col, index) => {
            const nullable = col.is_nullable === 'YES' ? 'NULL' : 'NOT NULL';
            const defaultVal = col.column_default ? ` DEFAULT ${col.column_default}` : '';
            console.log(`   ${index + 1}. ${col.column_name}: ${col.data_type} ${nullable}${defaultVal}`);
          });
        } else {
          console.log('   ❌ الجدول غير موجود');
        }
      } catch (error) {
        console.log(`   ❌ خطأ في فحص الجدول: ${error.message}`);
      }
    }

    // فحص الجداول الموجودة
    console.log('\n📊 جميع الجداول الموجودة:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    
    const allTablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    allTablesResult.rows.forEach((row, index) => {
      console.log(`   ${index + 1}. ${row.table_name}`);
    });

    await client.end();
    
  } catch (error) {
    console.error('❌ خطأ في فحص بنية الجداول:', error.message);
  }
}

examineTableStructure();
