// فحص تطابق الشهادة والمفتاح باستخدام Node.js فقط
const fs = require('fs');
const crypto = require('crypto');

console.log('🔍 فحص تطابق الشهادة والمفتاح (Node.js)');
console.log('='.repeat(50));

// مسارات الملفات
const files = {
  cert: 'ssl/mohammi_com.crt',
  key: 'ssl/mohammi.key'
};

// التحقق من وجود الملفات
console.log('📁 فحص وجود الملفات:');
for (const [name, path] of Object.entries(files)) {
  if (fs.existsSync(path)) {
    const stats = fs.statSync(path);
    console.log(`✅ ${name}: ${path} (${stats.size} bytes)`);
  } else {
    console.log(`❌ ${name}: ${path} غير موجود`);
    process.exit(1);
  }
}

// قراءة الملفات
console.log('\n📖 قراءة ملفات SSL...');
let certContent, keyContent;

try {
  certContent = fs.readFileSync(files.cert, 'utf8');
  keyContent = fs.readFileSync(files.key, 'utf8');
  console.log('✅ تم قراءة الملفات بنجاح');
} catch (error) {
  console.log('❌ خطأ في قراءة الملفات:', error.message);
  process.exit(1);
}

// الاختبار الأول: فحص تنسيق الملفات
console.log('\n🔍 الاختبار الأول: فحص تنسيق الملفات');
console.log('-'.repeat(40));

// فحص تنسيق الشهادة
if (certContent.includes('-----BEGIN CERTIFICATE-----') && certContent.includes('-----END CERTIFICATE-----')) {
  console.log('✅ تنسيق الشهادة صحيح (PEM)');
} else {
  console.log('❌ تنسيق الشهادة خاطئ');
}

// فحص تنسيق المفتاح
if (keyContent.includes('-----BEGIN PRIVATE KEY-----')) {
  console.log('✅ تنسيق المفتاح: PKCS#8');
} else if (keyContent.includes('-----BEGIN RSA PRIVATE KEY-----')) {
  console.log('✅ تنسيق المفتاح: PKCS#1');
} else if (keyContent.includes('-----BEGIN ENCRYPTED PRIVATE KEY-----')) {
  console.log('⚠️ المفتاح مشفر - يحتاج كلمة مرور');
} else {
  console.log('❌ تنسيق المفتاح غير معروف');
}

// الاختبار الثاني: محاولة قراءة المفتاح والشهادة
console.log('\n🔐 الاختبار الثاني: قراءة المفتاح والشهادة');
console.log('-'.repeat(40));

let privateKey, publicKeyFromCert, publicKeyFromPrivate;

try {
  // قراءة المفتاح الخاص
  privateKey = crypto.createPrivateKey(keyContent);
  console.log('✅ تم قراءة المفتاح الخاص بنجاح');
  console.log(`   📊 نوع المفتاح: ${privateKey.asymmetricKeyType}`);
  console.log(`   📏 حجم المفتاح: ${privateKey.asymmetricKeySize * 8} bits`);
  
  // استخراج المفتاح العام من المفتاح الخاص
  publicKeyFromPrivate = crypto.createPublicKey(privateKey);
  console.log('✅ تم استخراج المفتاح العام من المفتاح الخاص');
  
} catch (error) {
  console.log('❌ خطأ في قراءة المفتاح الخاص:', error.message);
  if (error.message.includes('bad decrypt')) {
    console.log('💡 المفتاح قد يكون مشفراً ويحتاج كلمة مرور');
  }
}

try {
  // قراءة الشهادة واستخراج المفتاح العام
  publicKeyFromCert = crypto.createPublicKey({
    key: certContent,
    format: 'pem'
  });
  console.log('✅ تم استخراج المفتاح العام من الشهادة');
  
} catch (error) {
  console.log('❌ خطأ في قراءة الشهادة:', error.message);
}

// الاختبار الثالث: مقارنة المفاتيح العامة
console.log('\n🔢 الاختبار الثالث: مقارنة المفاتيح العامة');
console.log('-'.repeat(40));

if (publicKeyFromPrivate && publicKeyFromCert) {
  try {
    // تصدير المفاتيح العامة للمقارنة
    const privateKeyPem = publicKeyFromPrivate.export({ format: 'pem', type: 'spki' });
    const certKeyPem = publicKeyFromCert.export({ format: 'pem', type: 'spki' });
    
    console.log('📊 المفتاح العام من المفتاح الخاص:');
    console.log('   ' + privateKeyPem.split('\n')[1].substring(0, 50) + '...');
    
    console.log('📊 المفتاح العام من الشهادة:');
    console.log('   ' + certKeyPem.split('\n')[1].substring(0, 50) + '...');
    
    if (privateKeyPem === certKeyPem) {
      console.log('✅ المفاتيح العامة متطابقة! الشهادة والمفتاح متوافقان');
    } else {
      console.log('❌ المفاتيح العامة غير متطابقة! الشهادة والمفتاح غير متوافقين');
      console.log('💡 هذا يؤكد أن المفتاح الحالي خاطئ');
    }
    
  } catch (error) {
    console.log('❌ خطأ في مقارنة المفاتيح:', error.message);
  }
} else {
  console.log('❌ لا يمكن مقارنة المفاتيح - فشل في قراءة أحد الملفات');
}

// الاختبار الرابع: اختبار التشفير وفك التشفير
console.log('\n🔒 الاختبار الرابع: اختبار التشفير وفك التشفير');
console.log('-'.repeat(40));

if (publicKeyFromCert && privateKey) {
  try {
    const testMessage = 'message test';
    console.log('📤 الرسالة الأصلية:', testMessage);
    
    // تشفير بالمفتاح العام
    const encrypted = crypto.publicEncrypt(publicKeyFromCert, Buffer.from(testMessage));
    console.log('🔐 تم التشفير بالمفتاح العام');
    
    // فك التشفير بالمفتاح الخاص
    const decrypted = crypto.privateDecrypt(privateKey, encrypted);
    const decryptedMessage = decrypted.toString();
    console.log('📥 الرسالة المفكوكة:', decryptedMessage);
    
    if (testMessage === decryptedMessage) {
      console.log('✅ اختبار التشفير/فك التشفير نجح!');
    } else {
      console.log('❌ اختبار التشفير/فك التشفير فشل!');
    }
    
  } catch (error) {
    console.log('❌ خطأ في اختبار التشفير:', error.message);
    console.log('💡 هذا يؤكد أن المفتاح والشهادة غير متوافقين');
  }
} else {
  console.log('❌ لا يمكن إجراء اختبار التشفير - فشل في قراءة الملفات');
}

// الاختبار الخامس: اختبار التوقيع الرقمي
console.log('\n✍️ الاختبار الخامس: اختبار التوقيع الرقمي');
console.log('-'.repeat(40));

if (publicKeyFromCert && privateKey) {
  try {
    const testData = 'message test';
    console.log('📝 البيانات للتوقيع:', testData);
    
    // إنشاء التوقيع
    const sign = crypto.createSign('SHA256');
    sign.update(testData);
    const signature = sign.sign(privateKey);
    console.log('✍️ تم إنشاء التوقيع الرقمي');
    
    // التحقق من التوقيع
    const verify = crypto.createVerify('SHA256');
    verify.update(testData);
    const isValid = verify.verify(publicKeyFromCert, signature);
    
    if (isValid) {
      console.log('✅ التوقيع الرقمي صحيح!');
    } else {
      console.log('❌ التوقيع الرقمي غير صحيح!');
    }
    
  } catch (error) {
    console.log('❌ خطأ في اختبار التوقيع:', error.message);
  }
} else {
  console.log('❌ لا يمكن إجراء اختبار التوقيع - فشل في قراءة الملفات');
}

// اختبار إنشاء خيارات SSL
console.log('\n⚙️ الاختبار السادس: اختبار إنشاء خيارات SSL');
console.log('-'.repeat(40));

try {
  const sslOptions = {
    key: keyContent,
    cert: certContent
  };
  
  // محاولة إنشاء سياق SSL
  const https = require('https');
  const server = https.createServer(sslOptions);
  
  console.log('✅ تم إنشاء خيارات SSL بنجاح');
  console.log('✅ يمكن استخدام الملفات لإنشاء خادم HTTPS');
  
} catch (error) {
  console.log('❌ خطأ في إنشاء خيارات SSL:', error.message);
  console.log('💡 الملفات غير متوافقة لإنشاء خادم HTTPS');
}

// النتيجة النهائية
console.log('\n📊 النتيجة النهائية:');
console.log('='.repeat(50));

console.log('📋 ملخص الاختبارات:');
console.log('1. تنسيق الملفات: تم فحصه');
console.log('2. قراءة المفتاح والشهادة: تم فحصه');
console.log('3. مقارنة المفاتيح العامة: تم فحصه');
console.log('4. اختبار التشفير/فك التشفير: تم فحصه');
console.log('5. اختبار التوقيع الرقمي: تم فحصه');
console.log('6. اختبار خيارات SSL: تم فحصه');

console.log('\n💡 التوصيات:');
console.log('• إذا فشلت الاختبارات: المفتاح غير صحيح');
console.log('• اتصل بـ Name.com لطلب المفتاح الصحيح');
console.log('• أو أنشئ مفتاح جديد واطلب إعادة إصدار الشهادة');
console.log('• إذا نجحت الاختبارات: يمكن تشغيل SSL');

console.log('\n🚀 للمتابعة (إذا نجحت الاختبارات):');
console.log('node ssl/start_ssl_correct.js');
