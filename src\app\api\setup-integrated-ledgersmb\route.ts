import { NextResponse } from 'next/server'
import { query } from '@/lib/db'

export async function POST() {
  try {

    // اختبار الاتصال أولاً
    await query('SELECT 1')

    await query('BEGIN')

    try {
      // 1. إنشاء جداول دليل الحسابات (Chart of Accounts)

      try {
        await query(`
          CREATE TABLE IF NOT EXISTS chart_of_accounts (
            id SERIAL PRIMARY KEY,
            accno VARCHAR(20) UNIQUE NOT NULL,
            description TEXT NOT NULL,
            charttype CHAR(1) DEFAULT 'A' CHECK (charttype IN ('A', 'H')),
            category CHAR(1) CHECK (category IN ('A', 'L', 'Q', 'I', 'E')),
            contra BOOLEAN DEFAULT FALSE,
            tax BOOLEAN DEFAULT FALSE,
            link TEXT,
            heading INTEGER,
            gifi_accno VARCHAR(20),
            obsolete BOOLEAN DEFAULT FALSE,
            is_temp BOOLEAN DEFAULT FALSE,
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
          )
        `)

      } catch (error) {
        console.error('❌ خطأ في إنشاء جدول دليل الحسابات:', error)
        throw error
      }

      // 2. إنشاء جدول الشركات/العملاء

      await query(`
        CREATE TABLE IF NOT EXISTS company (
          id SERIAL PRIMARY KEY,
          entity_id INTEGER,
          legal_name TEXT NOT NULL,
          tax_id VARCHAR(20),
          sales_tax_id VARCHAR(20),
          license_number VARCHAR(50),
          created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          control_code VARCHAR(20) UNIQUE,
          country_id INTEGER,
          sic_code VARCHAR(20)
        )
      `)

      // 3. إنشاء جدول الكيانات (Entities)
      await query(`
        CREATE TABLE IF NOT EXISTS entity (
          id SERIAL PRIMARY KEY,
          name TEXT NOT NULL,
          entity_class INTEGER NOT NULL,
          created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          control_code VARCHAR(20) UNIQUE,
          country_id INTEGER
        )
      `)

      // 4. إنشاء جدول فئات الكيانات
      await query(`
        CREATE TABLE IF NOT EXISTS entity_class (
          id SERIAL PRIMARY KEY,
          class VARCHAR(20) UNIQUE NOT NULL,
          in_use BOOLEAN DEFAULT TRUE,
          created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `)

      // 5. إنشاء جدول المعاملات (Transactions)

      await query(`
        CREATE TABLE IF NOT EXISTS gl (
          id SERIAL PRIMARY KEY,
          reference TEXT,
          description TEXT,
          transdate DATE NOT NULL,
          person_id INTEGER,
          notes TEXT,
          approved BOOLEAN DEFAULT FALSE,
          approved_by INTEGER,
          approved_at TIMESTAMP,
          created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          workflow_id INTEGER
        )
      `)

      // 6. إنشاء جدول تفاصيل المعاملات
      await query(`
        CREATE TABLE IF NOT EXISTS acc_trans (
          trans_id INTEGER NOT NULL REFERENCES gl(id),
          chart_id INTEGER NOT NULL REFERENCES chart_of_accounts(id),
          amount DECIMAL(20,8) NOT NULL,
          transdate DATE NOT NULL,
          source TEXT,
          cleared BOOLEAN DEFAULT FALSE,
          fx_transaction BOOLEAN DEFAULT FALSE,
          memo TEXT,
          invoice_id INTEGER,
          approved BOOLEAN DEFAULT FALSE,
          entry_id SERIAL PRIMARY KEY,
          voucher_id INTEGER
        )
      `)

      // 7. إنشاء جدول الفواتير

      await query(`
        CREATE TABLE IF NOT EXISTS ar (
          id SERIAL PRIMARY KEY,
          invnumber TEXT UNIQUE,
          transdate DATE NOT NULL,
          entity_id INTEGER,
          taxincluded BOOLEAN DEFAULT FALSE,
          amount DECIMAL(20,8) NOT NULL,
          netamount DECIMAL(20,8) NOT NULL,
          paid DECIMAL(20,8) DEFAULT 0,
          datepaid DATE,
          duedate DATE,
          invoice BOOLEAN DEFAULT TRUE,
          shippingpoint TEXT,
          terms TEXT,
          notes TEXT,
          curr VARCHAR(3) DEFAULT 'SAR',
          ordnumber TEXT,
          employee_id INTEGER,
          till VARCHAR(20),
          quonumber TEXT,
          intnotes TEXT,
          department_id INTEGER,
          shipvia TEXT,
          language_code VARCHAR(6),
          ponumber TEXT,
          on_hold BOOLEAN DEFAULT FALSE,
          reverse BOOLEAN DEFAULT FALSE,
          approved BOOLEAN DEFAULT FALSE,
          created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          workflow_id INTEGER,
          crdate DATE DEFAULT CURRENT_DATE
        )
      `)

      // 8. إنشاء جدول عناصر الفواتير
      await query(`
        CREATE TABLE IF NOT EXISTS invoice (
          id SERIAL PRIMARY KEY,
          trans_id INTEGER REFERENCES ar(id),
          parts_id INTEGER,
          description TEXT,
          qty DECIMAL(20,8),
          allocated DECIMAL(20,8) DEFAULT 0,
          sellprice DECIMAL(20,8),
          fxsellprice DECIMAL(20,8),
          discount DECIMAL(4,4) DEFAULT 0,
          assemblyitem BOOLEAN DEFAULT FALSE,
          unit VARCHAR(20),
          deliverydate DATE,
          serialnumber TEXT,
          notes TEXT
        )
      `)

      // 9. إنشاء جدول المدفوعات

      await query(`
        CREATE TABLE IF NOT EXISTS payment (
          id SERIAL PRIMARY KEY,
          reference TEXT,
          payment_class INTEGER NOT NULL,
          payment_date DATE NOT NULL,
          closed BOOLEAN DEFAULT FALSE,
          entity_id INTEGER,
          employee_id INTEGER,
          currency VARCHAR(3) DEFAULT 'SAR',
          notes TEXT,
          department_id INTEGER,
          gl_id INTEGER REFERENCES gl(id),
          approved BOOLEAN DEFAULT FALSE,
          workflow_id INTEGER,
          created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `)

      // 10. إنشاء جدول تفاصيل المدفوعات
      await query(`
        CREATE TABLE IF NOT EXISTS payment_links (
          payment_id INTEGER REFERENCES payment(id),
          entry_id INTEGER REFERENCES acc_trans(entry_id),
          type INTEGER NOT NULL,
          PRIMARY KEY (payment_id, entry_id)
        )
      `)

      // 11. إنشاء جدول المشاريع

      await query(`
        CREATE TABLE IF NOT EXISTS project (
          id SERIAL PRIMARY KEY,
          projectnumber TEXT UNIQUE,
          description TEXT,
          startdate DATE,
          enddate DATE,
          parts_id INTEGER,
          production DECIMAL(20,8) DEFAULT 0,
          completed DECIMAL(20,8) DEFAULT 0,
          customer_id INTEGER,
          created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `)

      // 12. إنشاء جدول إدخالات الوقت
      await query(`
        CREATE TABLE IF NOT EXISTS timecard (
          id SERIAL PRIMARY KEY,
          employee_id INTEGER,
          project_id INTEGER REFERENCES project(id),
          business_unit_id INTEGER,
          transdate DATE NOT NULL,
          description TEXT,
          qty DECIMAL(20,8) NOT NULL,
          sellprice DECIMAL(20,8),
          fxsellprice DECIMAL(20,8),
          curr VARCHAR(3) DEFAULT 'SAR',
          allocated DECIMAL(20,8) DEFAULT 0,
          notes TEXT,
          jctype INTEGER,
          total DECIMAL(20,8),
          non_billable BOOLEAN DEFAULT FALSE,
          created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `)

      // 13. إدراج فئات الكيانات الأساسية

      await query(`
        INSERT INTO entity_class (id, class) VALUES
        (1, 'Vendor'),
        (2, 'Customer'),
        (3, 'Employee'),
        (4, 'Contact'),
        (5, 'Lead')
        ON CONFLICT (id) DO NOTHING
      `)

      // 14. إدراج دليل الحسابات الأساسي
      const accounts = [
        // الأصول
        { accno: '1000', description: 'الأصول المتداولة', charttype: 'H', category: 'A' },
        { accno: '1100', description: 'النقدية والبنوك', charttype: 'H', category: 'A' },
        { accno: '1110', description: 'النقدية في الصندوق', charttype: 'A', category: 'A', link: 'AR_paid:AP_paid:IC_sale:IC_cogs:IC_income' },
        { accno: '1120', description: 'حساب جاري - البنك الأهلي', charttype: 'A', category: 'A', link: 'AR_paid:AP_paid' },
        { accno: '1121', description: 'حساب جاري - بنك الراجحي', charttype: 'A', category: 'A', link: 'AR_paid:AP_paid' },
        { accno: '1200', description: 'المدينون', charttype: 'H', category: 'A' },
        { accno: '1210', description: 'حسابات العملاء', charttype: 'A', category: 'A', link: 'AR' },
        { accno: '1220', description: 'أوراق القبض', charttype: 'A', category: 'A' },

        // الخصوم
        { accno: '2000', description: 'الخصوم المتداولة', charttype: 'H', category: 'L' },
        { accno: '2100', description: 'الدائنون', charttype: 'H', category: 'L' },
        { accno: '2110', description: 'حسابات الموردين', charttype: 'A', category: 'L', link: 'AP' },
        { accno: '2120', description: 'أوراق الدفع', charttype: 'A', category: 'L' },
        { accno: '2200', description: 'المستحقات', charttype: 'H', category: 'L' },
        { accno: '2210', description: 'رواتب مستحقة', charttype: 'A', category: 'L' },
        { accno: '2220', description: 'ضرائب مستحقة', charttype: 'A', category: 'L', tax: true },

        // حقوق الملكية
        { accno: '3000', description: 'حقوق الملكية', charttype: 'H', category: 'Q' },
        { accno: '3100', description: 'رأس المال', charttype: 'A', category: 'Q' },
        { accno: '3200', description: 'الأرباح المحتجزة', charttype: 'A', category: 'Q' },

        // الإيرادات
        { accno: '4000', description: 'الإيرادات', charttype: 'H', category: 'I' },
        { accno: '4100', description: 'إيرادات الخدمات القانونية', charttype: 'H', category: 'I' },
        { accno: '4110', description: 'أتعاب استشارات قانونية', charttype: 'A', category: 'I', link: 'AR_amount:IC_sale:IC_income' },
        { accno: '4120', description: 'أتعاب ترافع', charttype: 'A', category: 'I', link: 'AR_amount:IC_sale:IC_income' },
        { accno: '4130', description: 'أتعاب صياغة عقود', charttype: 'A', category: 'I', link: 'AR_amount:IC_sale:IC_income' },
        { accno: '4140', description: 'أتعاب تحكيم', charttype: 'A', category: 'I', link: 'AR_amount:IC_sale:IC_income' },

        // المصروفات
        { accno: '5000', description: 'المصروفات', charttype: 'H', category: 'E' },
        { accno: '5100', description: 'مصروفات التشغيل', charttype: 'H', category: 'E' },
        { accno: '5110', description: 'رواتب وأجور', charttype: 'A', category: 'E', link: 'AP_amount' },
        { accno: '5120', description: 'إيجار المكتب', charttype: 'A', category: 'E', link: 'AP_amount' },
        { accno: '5130', description: 'كهرباء وماء', charttype: 'A', category: 'E', link: 'AP_amount' },
        { accno: '5140', description: 'اتصالات وإنترنت', charttype: 'A', category: 'E', link: 'AP_amount' },
        { accno: '5150', description: 'مصروفات مكتبية', charttype: 'A', category: 'E', link: 'AP_amount' },
        { accno: '5160', description: 'مصروفات سفر وانتقال', charttype: 'A', category: 'E', link: 'AP_amount' },
        { accno: '5200', description: 'مصروفات إدارية', charttype: 'H', category: 'E' },
        { accno: '5210', description: 'رسوم مهنية', charttype: 'A', category: 'E', link: 'AP_amount' },
        { accno: '5220', description: 'رسوم حكومية', charttype: 'A', category: 'E', link: 'AP_amount' }
      ]

      for (const account of accounts) {
        await query(`
          INSERT INTO chart_of_accounts (accno, description, charttype, category, contra, tax, link)
          VALUES ($1, $2, $3, $4, $5, $6, $7)
          ON CONFLICT (accno) DO NOTHING
        `, [
          account.accno,
          account.description,
          account.charttype,
          account.category,
          account.contra || false,
          account.tax || false,
          account.link || null
        ])
      }

      // 15. إضافة عمود project_id للقضايا

      await query(`
        ALTER TABLE cases
        ADD COLUMN IF NOT EXISTS project_id INTEGER REFERENCES project(id)
      `)

      // 16. إنشاء الفهارس

      const indexes = [
        'CREATE INDEX IF NOT EXISTS idx_acc_trans_chart_id ON acc_trans(chart_id)',
        'CREATE INDEX IF NOT EXISTS idx_acc_trans_trans_id ON acc_trans(trans_id)',
        'CREATE INDEX IF NOT EXISTS idx_acc_trans_transdate ON acc_trans(transdate)',
        'CREATE INDEX IF NOT EXISTS idx_ar_entity_id ON ar(entity_id)',
        'CREATE INDEX IF NOT EXISTS idx_ar_transdate ON ar(transdate)',
        'CREATE INDEX IF NOT EXISTS idx_gl_transdate ON gl(transdate)',
        'CREATE INDEX IF NOT EXISTS idx_payment_entity_id ON payment(entity_id)',
        'CREATE INDEX IF NOT EXISTS idx_project_customer_id ON project(customer_id)',
        'CREATE INDEX IF NOT EXISTS idx_timecard_project_id ON timecard(project_id)',
        'CREATE INDEX IF NOT EXISTS idx_timecard_employee_id ON timecard(employee_id)'
      ]

      for (const indexQuery of indexes) {
        await query(indexQuery)
      }

      await query('COMMIT')

      // 16. إحصائيات النهائية
      const stats = await query(`
        SELECT
          (SELECT COUNT(*) FROM chart_of_accounts) as accounts_count,
          (SELECT COUNT(*) FROM entity_class) as entity_classes_count,
          (SELECT COUNT(*) FROM clients) as clients_count,
          (SELECT COUNT(*) FROM cases) as cases_count,
          (SELECT COUNT(*) FROM employees) as employees_count
      `)

      return NextResponse.json({
        success: true,
        message: 'تم إعداد LedgerSMB المدمج بنجاح',
        data: {
          accounts_count: parseInt(stats.rows[0].accounts_count),
          entity_classes_count: parseInt(stats.rows[0].entity_classes_count),
          clients_count: parseInt(stats.rows[0].clients_count),
          cases_count: parseInt(stats.rows[0].cases_count),
          employees_count: parseInt(stats.rows[0].employees_count)
        }
      })

    } catch (error) {
      await query('ROLLBACK')
      throw error
    }

  } catch (error) {
    console.error('خطأ في إعداد LedgerSMB المدمج:', error)

    // تفاصيل أكثر عن الخطأ
    let errorMessage = 'فشل في إعداد LedgerSMB المدمج'
    let errorDetails = 'خطأ غير معروف'

    if (error instanceof Error) {
      errorDetails = error.message
      console.error('تفاصيل الخطأ:', error.stack)

      // أخطاء قاعدة البيانات الشائعة
      if (error.message.includes('connect')) {
        errorMessage = 'فشل في الاتصال بقاعدة البيانات'
        errorDetails = 'تأكد من تشغيل PostgreSQL وصحة إعدادات الاتصال'
      } else if (error.message.includes('permission')) {
        errorMessage = 'خطأ في صلاحيات قاعدة البيانات'
        errorDetails = 'تأكد من صلاحيات المستخدم لإنشاء الجداول'
      } else if (error.message.includes('syntax')) {
        errorMessage = 'خطأ في صيغة SQL'
        errorDetails = error.message
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
        details: errorDetails,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}
