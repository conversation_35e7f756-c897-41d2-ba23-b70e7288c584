'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Loader2, Shield, CheckCircle, XCircle, Users } from 'lucide-react'
import { useAuth } from '@/hooks/useAuth'

interface Permission {
  key: string
  name: string
  description?: string
}

interface PermissionsByCategory {
  [category: string]: Permission[]
}

interface UserPermissionsModalProps {
  isOpen: boolean
  onClose: () => void
  userId: number
  userName: string
  onSuccess?: () => void
}

export function UserPermissionsModal({ 
  isOpen, 
  onClose, 
  userId, 
  userName, 
  onSuccess 
}: UserPermissionsModalProps) {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [permissionsByCategory, setPermissionsByCategory] = useState<PermissionsByCategory>({})
  const [userPermissions, setUserPermissions] = useState<string[]>([])
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([])
  const [message, setMessage] = useState('')

  useEffect(() => {
    if (isOpen) {
      fetchData()
    }
  }, [isOpen, userId])

  const fetchData = async () => {
    setLoading(true)
    try {
      // جلب جميع الصلاحيات المتاحة
      const permissionsResponse = await fetch('/api/permissions')
      const permissionsResult = await permissionsResponse.json()

      if (permissionsResult.success) {
        setPermissionsByCategory(permissionsResult.data.permissionsByCategory)
      }

      // جلب صلاحيات المستخدم الحالية
      const userPermissionsResponse = await fetch(`/api/user-permissions?userId=${userId}`)
      const userPermissionsResult = await userPermissionsResponse.json()

      if (userPermissionsResult.success) {
        const currentPermissions = userPermissionsResult.data.map((p: any) => p.permission_key)
        setUserPermissions(currentPermissions)
        setSelectedPermissions([...currentPermissions])
      }

    } catch (error) {
      console.error('خطأ في جلب البيانات:', error)
      setMessage('فشل في جلب البيانات')
    } finally {
      setLoading(false)
    }
  }

  const handlePermissionChange = (permissionKey: string, checked: boolean) => {
    if (checked) {
      setSelectedPermissions(prev => [...prev, permissionKey])
    } else {
      setSelectedPermissions(prev => prev.filter(p => p !== permissionKey))
    }
  }

  const handleSelectAll = () => {
    const allPermissions = Object.values(permissionsByCategory)
      .flat()
      .map(p => p.key)
    setSelectedPermissions(allPermissions)
  }

  const handleDeselectAll = () => {
    setSelectedPermissions([])
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      const response = await fetch('/api/user-permissions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId,
          permissions: selectedPermissions,
          grantedBy: user?.id
        })
      })

      const result = await response.json()

      if (result.success) {
        setMessage('تم تحديث الصلاحيات بنجاح')
        setUserPermissions([...selectedPermissions])
        onSuccess?.()
        setTimeout(() => {
          onClose()
          setMessage('')
        }, 1500)
      } else {
        setMessage('فشل في تحديث الصلاحيات: ' + result.error)
      }

    } catch (error) {
      console.error('خطأ في حفظ الصلاحيات:', error)
      setMessage('حدث خطأ أثناء حفظ الصلاحيات')
    } finally {
      setSaving(false)
    }
  }

  const getTotalPermissions = () => {
    return Object.values(permissionsByCategory).flat().length
  }

  const getSelectedCount = () => {
    return selectedPermissions.length
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center text-xl">
            <Shield className="h-6 w-6 mr-3 text-blue-600" />
            إدارة صلاحيات المستخدم: {userName}
          </DialogTitle>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            <span className="mr-3">جاري تحميل الصلاحيات...</span>
          </div>
        ) : (
          <div className="space-y-6">
            {/* إحصائيات */}
            <div className="flex items-center justify-between bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center space-x-4 space-x-reverse">
                <Badge variant="outline" className="text-blue-700">
                  <Users className="h-4 w-4 mr-1" />
                  {getSelectedCount()} من {getTotalPermissions()} صلاحية
                </Badge>
              </div>
              
              <div className="flex space-x-2 space-x-reverse">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAll}
                  className="text-green-700 border-green-300 hover:bg-green-50"
                >
                  <CheckCircle className="h-4 w-4 mr-1" />
                  تحديد الكل
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleDeselectAll}
                  className="text-red-700 border-red-300 hover:bg-red-50"
                >
                  <XCircle className="h-4 w-4 mr-1" />
                  إلغاء الكل
                </Button>
              </div>
            </div>

            {/* الصلاحيات حسب الفئة */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(permissionsByCategory).map(([category, permissions]) => (
                <Card key={category} className="border-2">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg text-gray-800">
                      {category}
                    </CardTitle>
                    <div className="text-sm text-gray-600">
                      {permissions.filter(p => selectedPermissions.includes(p.key)).length} من {permissions.length} محددة
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {permissions.map((permission) => (
                      <div key={permission.key} className="flex items-start space-x-3 space-x-reverse">
                        <Checkbox
                          id={permission.key}
                          checked={selectedPermissions.includes(permission.key)}
                          onCheckedChange={(checked) => 
                            handlePermissionChange(permission.key, checked as boolean)
                          }
                          className="mt-1"
                        />
                        <div className="flex-1">
                          <Label 
                            htmlFor={permission.key}
                            className="text-sm font-medium cursor-pointer"
                          >
                            {permission.name}
                          </Label>
                          {permission.description && (
                            <p className="text-xs text-gray-500 mt-1">
                              {permission.description}
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* رسالة النتيجة */}
            {message && (
              <div className={`p-3 rounded-lg text-center ${
                message.includes('نجاح') 
                  ? 'bg-green-50 text-green-800 border border-green-200' 
                  : 'bg-red-50 text-red-800 border border-red-200'
              }`}>
                {message}
              </div>
            )}

            {/* أزرار الحفظ */}
            <div className="flex justify-end space-x-3 space-x-reverse pt-4 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={saving}
              >
                إلغاء
              </Button>
              <Button
                type="button"
                onClick={handleSave}
                disabled={saving}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {saving ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    جاري الحفظ...
                  </>
                ) : (
                  <>
                    <Shield className="h-4 w-4 mr-2" />
                    حفظ الصلاحيات
                  </>
                )}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
