'use client'

import { useState, useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { ChevronDown, Search, User, Users, Building, BookOpen } from 'lucide-react'

interface Account {
  id: string | number
  account_code: string
  account_name: string
  account_type: string
  account_nature: string
  is_linked_record?: boolean
  original_table?: string
  linked_record_id?: number
  allow_transactions: boolean
}

interface SearchableAccountSelectProps {
  value: string
  onChange: (accountId: string) => void
  label?: string
  placeholder?: string
  required?: boolean
  className?: string
}

export function SearchableAccountSelect({ 
  value, 
  onChange, 
  label = "الحساب", 
  placeholder = "اختر الحساب أو ابحث...", 
  required = false,
  className = ""
}: SearchableAccountSelectProps) {
  const [accounts, setAccounts] = useState<Account[]>([])
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedAccount, setSelectedAccount] = useState<Account | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  const fetchAccounts = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/accounting/chart-of-accounts?only_transactional=true&include_linked=true')
      if (response.ok) {
        const data = await response.json()
        // فلترة العملاء والموظفين والموردين فقط
        const linkedAccounts = (data.accounts || []).filter((account: Account) => account.is_linked_record)
        setAccounts(linkedAccounts)
      } else {
        console.error('فشل في جلب الحسابات:', response.status)
        setAccounts([])
      }
    } catch (error) {
      console.error('خطأ في جلب الحسابات:', error)
      setAccounts([])
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchAccounts()
  }, [])

  useEffect(() => {
    if (value && accounts.length > 0) {
      const account = accounts.find(a => a.id.toString() === value)
      setSelectedAccount(account || null)
    } else {
      setSelectedAccount(null)
    }
  }, [value, accounts])

  const filteredAccounts = accounts.filter(account =>
    account.account_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    account.account_code.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleSelect = (account: Account) => {
    setSelectedAccount(account)
    onChange(account.id.toString())
    setIsOpen(false)
    setSearchTerm('')
  }

  const handleClear = () => {
    setSelectedAccount(null)
    onChange('')
    setSearchTerm('')
  }



  const getAccountIcon = (account: Account) => {
    if (account.is_linked_record) {
      switch (account.original_table) {
        case 'clients': return <User className="h-4 w-4 text-blue-500" />
        case 'employees': return <Users className="h-4 w-4 text-green-500" />
        case 'suppliers': return <Building className="h-4 w-4 text-orange-500" />
        default: return <BookOpen className="h-4 w-4 text-purple-500" />
      }
    }
    return <BookOpen className="h-4 w-4 text-purple-500" />
  }

  const getAccountTypeText = (account: Account) => {
    if (account.is_linked_record) {
      switch (account.original_table) {
        case 'clients': return 'عميل'
        case 'employees': return 'موظف'
        case 'suppliers': return 'مورد'
        default: return 'حساب'
      }
    }
    return 'حساب'
  }

  const getAccountTypeColor = (account: Account) => {
    if (account.is_linked_record) {
      switch (account.original_table) {
        case 'clients': return 'bg-blue-100 text-blue-800'
        case 'employees': return 'bg-green-100 text-green-800'
        case 'suppliers': return 'bg-orange-100 text-orange-800'
        default: return 'bg-purple-100 text-purple-800'
      }
    }
    return 'bg-purple-100 text-purple-800'
  }

  // تجميع الحسابات حسب النوع (العملاء/الموظفين/الموردين فقط)
  const groupedAccounts = {
    clients: filteredAccounts.filter(account => account.original_table === 'clients'),
    employees: filteredAccounts.filter(account => account.original_table === 'employees'),
    suppliers: filteredAccounts.filter(account => account.original_table === 'suppliers')
  }

  return (
    <div className={`relative ${className}`}>
      <Label className="block text-sm font-medium text-gray-700 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </Label>
      
      <div className="relative">
        <div
          className="w-full px-3 py-2 border border-gray-300 rounded-md cursor-pointer bg-white flex items-center justify-between hover:border-gray-400 focus-within:border-blue-500 transition-colors"
          onClick={() => setIsOpen(!isOpen)}
        >
          <div className="flex items-center flex-1 min-w-0">
            {selectedAccount ? getAccountIcon(selectedAccount) : <BookOpen className="h-4 w-4 mr-2 text-gray-400" />}
            <span className={`${selectedAccount ? 'text-gray-900' : 'text-gray-500'} mr-2 truncate`}>
              {selectedAccount ? selectedAccount.account_name : placeholder}
            </span>
            {selectedAccount && (
              <Badge className={`${getAccountTypeColor(selectedAccount)} ml-2`} size="sm">
                {getAccountTypeText(selectedAccount)}
              </Badge>
            )}
          </div>
          <ChevronDown className={`h-4 w-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </div>

        {isOpen && (
          <div className="absolute z-[100] w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-[500px] overflow-hidden">
            {/* شريط البحث */}
            <div className="p-3 border-b bg-gray-50">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="ابحث بالاسم أو الرمز..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                  autoFocus
                />
              </div>
            </div>

            {/* قائمة الحسابات */}
            <div className="max-h-[400px] overflow-y-auto">
              {isLoading ? (
                <div className="p-4 text-center text-gray-500">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto mb-2"></div>
                  جاري التحميل...
                </div>
              ) : (
                <>
                  {selectedAccount && (
                    <div
                      className="p-3 hover:bg-red-50 cursor-pointer border-b text-red-600 bg-red-25"
                      onClick={handleClear}
                    >
                      <div className="flex items-center">
                        <span className="text-sm font-medium">✕ إلغاء الاختيار</span>
                      </div>
                    </div>
                  )}

                  {/* العملاء */}
                  {groupedAccounts.clients.length > 0 && (
                    <>
                      <div className="px-3 py-2 bg-blue-50 text-blue-800 text-sm font-medium border-b">
                        👤 العملاء
                      </div>
                      {groupedAccounts.clients.map((account) => (
                        <div
                          key={account.id}
                          className="p-3 hover:bg-blue-50 cursor-pointer border-b last:border-b-0"
                          onClick={() => handleSelect(account)}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center">
                                {getAccountIcon(account)}
                                <span className="font-medium text-gray-900 mr-2 truncate">{account.account_name}</span>
                                <Badge className={getAccountTypeColor(account)} size="sm">
                                  {getAccountTypeText(account)}
                                </Badge>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </>
                  )}

                  {/* الموظفين */}
                  {groupedAccounts.employees.length > 0 && (
                    <>
                      <div className="px-3 py-2 bg-green-50 text-green-800 text-sm font-medium border-b">
                        👨‍💼 الموظفين
                      </div>
                      {groupedAccounts.employees.map((account) => (
                        <div
                          key={account.id}
                          className="p-3 hover:bg-green-50 cursor-pointer border-b last:border-b-0"
                          onClick={() => handleSelect(account)}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center">
                                {getAccountIcon(account)}
                                <span className="font-medium text-gray-900 mr-2 truncate">{account.account_name}</span>
                                <Badge className={getAccountTypeColor(account)} size="sm">
                                  {getAccountTypeText(account)}
                                </Badge>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </>
                  )}

                  {/* الموردين */}
                  {groupedAccounts.suppliers.length > 0 && (
                    <>
                      <div className="px-3 py-2 bg-orange-50 text-orange-800 text-sm font-medium border-b">
                        🏢 الموردين
                      </div>
                      {groupedAccounts.suppliers.map((account) => (
                        <div
                          key={account.id}
                          className="p-3 hover:bg-orange-50 cursor-pointer border-b last:border-b-0"
                          onClick={() => handleSelect(account)}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center">
                                {getAccountIcon(account)}
                                <span className="font-medium text-gray-900 mr-2 truncate">{account.account_name}</span>
                                <Badge className={getAccountTypeColor(account)} size="sm">
                                  {getAccountTypeText(account)}
                                </Badge>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </>
                  )}

                  {filteredAccounts.length === 0 && !isLoading && (
                    <div className="p-4 text-center text-gray-500">
                      {searchTerm ? 'لا توجد نتائج للبحث' : 'لا توجد حسابات'}
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        )}
      </div>

      {/* عرض تفاصيل الحساب المختار */}
      {selectedAccount && (
        <div className="mt-2 p-3 bg-gray-50 border border-gray-200 rounded-md">
          <div className="text-sm text-gray-700">
            <div className="grid grid-cols-2 gap-2">
              <div><strong>النوع:</strong> {selectedAccount.account_type}</div>
              <div><strong>الطبيعة:</strong> {selectedAccount.account_nature}</div>
              {selectedAccount.is_linked_record && (
                <div><strong>مرتبط بـ:</strong> {getAccountTypeText(selectedAccount)}</div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
