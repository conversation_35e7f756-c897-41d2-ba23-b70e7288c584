import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب خدمة بالرابط (slug)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params

    const result = await query(
      'SELECT * FROM serviceslow WHERE slug = $1 AND is_active = $2',
      [slug, true]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الخدمة غير موجودة أو غير نشطة' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0]
    })
  } catch (error) {
    console.error('خطأ في جلب الخدمة:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الخدمة' },
      { status: 500 }
    )
  }
}
