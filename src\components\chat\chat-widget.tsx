'use client'

import { useState, useEffect, useRef, memo, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  MessageCircle,
  Send,
  X,
  Paperclip,
  Image,
  Smile,
  MoreVertical,
  Reply,
  Users,
  Circle,
  Bot,
  Zap
} from 'lucide-react'

interface Message {
  id: number
  conversation_id: number
  sender_type: 'user' | 'client' | 'ai'
  sender_id: number
  message_text: string
  message_type: 'text' | 'image' | 'file' | 'reply'
  file_url?: string
  file_name?: string
  reply_to_message_id?: number
  is_read: boolean
  created_at: string
  sender_name?: string
}

interface Conversation {
  id: number
  client_id: number
  user_id: number
  title: string
  status: string
  last_message_at: string
  client_name?: string
  user_name?: string
  unread_count?: number
}

interface ChatWidgetProps {
  userType: 'user' | 'client'
  userId: number
  userName: string
}

const ChatWidget = memo(function ChatWidget({ userType, userId, userName }: ChatWidgetProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [conversations, setConversations] = useState<Conversation[]>([])
  const [activeConversation, setActiveConversation] = useState<Conversation | null>(null)
  const [messages, setMessages] = useState<Message[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [unreadCount, setUnreadCount] = useState(0)
  const [isLoading, setIsLoading] = useState(false)
  const [onlineUsers, setOnlineUsers] = useState<string[]>([])
  const [userScrolledUp, setUserScrolledUp] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const lastFetchRef = useRef<number>(0)

  // جلب المحادثات
  const fetchConversations = async () => {
    try {
      const response = await fetch(`/api/chat/conversations?userType=${userType}&userId=${userId}`)
      const result = await response.json()

      if (result.success) {
        setConversations(result.data)

        // حساب الرسائل غير المقروءة
        const totalUnread = result.data.reduce((sum: number, conv: Conversation) =>
          sum + (conv.unread_count || 0), 0
        )
        setUnreadCount(totalUnread)
      }
    } catch (error) {
      console.error('Error fetching conversations:', error)
    }
  }

  // جلب الرسائل للمحادثة النشطة (محسن مع debouncing)
  const fetchMessages = useCallback(async (conversationId: number, forceUpdate = false) => {
    const now = Date.now()

    // تجنب الطلبات المتكررة (debouncing)
    if (!forceUpdate && now - lastFetchRef.current < 2000) {
      return
    }

    lastFetchRef.current = now
    try {
      setIsLoading(true)
      const response = await fetch(`/api/chat/messages?conversationId=${conversationId}`)
      const result = await response.json()

      if (result.success) {
        const newMessages = result.data

        // فحص إذا كانت الرسائل تغيرت فعلاً
        const messagesChanged =
          forceUpdate ||
          messages.length !== newMessages.length ||
          (newMessages.length > 0 && messages.length > 0 &&
           newMessages[newMessages.length - 1].id !== messages[messages.length - 1]?.id)

        if (messagesChanged) {
          setMessages(newMessages)

          // التمرير فقط إذا كانت هناك رسائل جديدة
          if (newMessages.length > messages.length) {
            setTimeout(() => scrollToBottom(), 100)
          }

          // تحديث حالة القراءة
          await markMessagesAsRead(conversationId)
        }
      }
    } catch (error) {
      console.error('Error fetching messages:', error)
    } finally {
      setIsLoading(false)
    }
  }, [messages])

  // تحديد الرسائل كمقروءة
  const markMessagesAsRead = async (conversationId: number) => {
    try {
      await fetch('/api/chat/messages/read', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          conversationId,
          readerType: userType,
          readerId: userId
        })
      })
    } catch (error) {
      console.error('Error marking messages as read:', error)
    }
  }

  // إرسال رسالة جديدة
  const sendMessage = async () => {
    if (!newMessage.trim() || !activeConversation) return

    try {
      const response = await fetch('/api/chat/messages', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          conversationId: activeConversation.id,
          senderType: userType,
          senderId: userId,
          messageText: newMessage,
          messageType: 'text'
        })
      })

      const result = await response.json()

      if (result.success) {
        setNewMessage('')
        setUserScrolledUp(false) // تأكيد أن المستخدم في الأسفل
        await fetchMessages(activeConversation.id, true) // فرض التحديث بعد إرسال رسالة
        await fetchConversations()
        
        // تمرير فوري للأسفل بعد إرسال الرسالة
        setTimeout(() => {
          scrollToBottom()
        }, 300)
      }
    } catch (error) {
      console.error('Error sending message:', error)
    }
  }

  // التمرير إلى أسفل الرسائل - مضمون للأسفل
  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      // التمرير للأسفل مباشرة
      messagesEndRef.current.scrollIntoView({ 
        behavior: 'smooth',
        block: 'end',
        inline: 'nearest'
      })
    }
    
    // تأكيد إضافي للتمرير للأسفل
    setTimeout(() => {
      const messagesContainer = document.querySelector('.messages-container')
      if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight
      }
    }, 200)
  }

  // فتح محادثة
  const openConversation = (conversation: Conversation) => {
    setActiveConversation(conversation)
    fetchMessages(conversation.id, true) // فرض التحديث عند فتح محادثة جديدة
  }

  // تحديث البيانات عند فتح الويدجت
  useEffect(() => {
    if (isOpen) {
      fetchConversations()
    }
  }, [isOpen])

  // تحديث دوري للمحادثات (أقل تكراراً وأكثر ذكاءً)
  useEffect(() => {
    if (!isOpen) return

    const interval = setInterval(() => {
      // تحديث قائمة المحادثات فقط (بدون إعادة تحميل الرسائل)
      fetchConversations()

      // تحديث الرسائل فقط إذا كان هناك محادثة نشطة ولم يكن المستخدم يتمرر
      // وفقط إذا مر أكثر من 30 ثانية على آخر تحديث
      if (activeConversation && !userScrolledUp) {
        const lastUpdate = localStorage.getItem(`lastUpdate_${activeConversation.id}`)
        const now = Date.now()

        if (!lastUpdate || now - parseInt(lastUpdate) > 30000) {
          fetchMessages(activeConversation.id)
          localStorage.setItem(`lastUpdate_${activeConversation.id}`, now.toString())
        }
      }
    }, 30000) // كل 30 ثانية بدلاً من 10

    return () => clearInterval(interval)
  }, [isOpen, activeConversation?.id, userScrolledUp])

  // مراقبة التمرير اليدوي - مبسط
  useEffect(() => {
    const messagesContainer = document.querySelector('.messages-container')
    if (!messagesContainer) return

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = messagesContainer
      const isAtBottom = scrollHeight - scrollTop - clientHeight < 100
      setUserScrolledUp(!isAtBottom)
    }

    messagesContainer.addEventListener('scroll', handleScroll)
    return () => messagesContainer.removeEventListener('scroll', handleScroll)
  }, [activeConversation])

  // التمرير التلقائي عند إرسال أو استقبال رسالة جديدة (محسن)
  useEffect(() => {
    if (messages.length === 0) return

    const lastMessage = messages[messages.length - 1]
    const now = new Date().getTime()
    const messageTime = new Date(lastMessage.created_at).getTime()

    // التمرير فقط للرسائل الحديثة جداً (أقل من 10 ثوان) أو إذا كان المستخدم في الأسفل
    const isRecentMessage = now - messageTime < 10000
    const shouldScroll = isRecentMessage || !userScrolledUp

    if (shouldScroll) {
      // تأخير قصير لتجنب التومض
      const timeoutId = setTimeout(() => {
        scrollToBottom()
      }, 200)

      return () => clearTimeout(timeoutId)
    }
  }, [messages.length, userScrolledUp])

  // التمرير عند فتح محادثة جديدة (دائماً)
  useEffect(() => {
    if (activeConversation && messages.length > 0) {
      // عند فتح محادثة جديدة، نعيد تعيين حالة التمرير ونتمرر للأسفل
      setUserScrolledUp(false)
      setTimeout(() => {
        scrollToBottom()
      }, 300)
    }
  }, [activeConversation])

  // تنسيق الوقت
  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <>
      {/* زر المحادثات العائم */}
      <div className="fixed bottom-6 left-6 z-50">
        <Button
          onClick={() => setIsOpen(!isOpen)}
          className={`h-16 w-16 rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 relative transform hover:scale-105 ${
            isOpen
              ? 'bg-blue-700 rotate-180'
              : 'bg-blue-600 hover:bg-blue-700'
          }`}
          title="المحادثات"
        >
          <MessageCircle className="h-7 w-7 text-white" />
          {unreadCount > 0 && (
            <Badge className="absolute -top-2 -right-2 h-6 w-6 rounded-full bg-red-500 text-white text-xs flex items-center justify-center animate-pulse">
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </div>

      {/* نافذة المحادثات */}
      {isOpen && (
        <div className="fixed bottom-24 left-6 w-96 h-[500px] bg-white rounded-lg shadow-2xl border z-50 flex flex-col">
          {/* رأس النافذة */}
          <div className="flex items-center justify-between p-4 border-b bg-blue-600 text-white rounded-t-lg">
            <div className="flex items-center">
              <MessageCircle className="h-5 w-5 mr-2" />
              <h3 className="font-semibold">
                {activeConversation ? activeConversation.title : 'المحادثات'}
              </h3>
            </div>
            <div className="flex items-center space-x-2 space-x-reverse">
              {activeConversation && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setActiveConversation(null)}
                  className="text-white hover:bg-blue-700"
                >
                  ←
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
                className="text-white hover:bg-blue-700"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* المحتوى */}
          <div className="flex-1 overflow-hidden">
            {!activeConversation ? (
              // قائمة المحادثات
              <div className="h-full overflow-y-auto">
                {conversations.length === 0 ? (
                  <div className="flex items-center justify-center h-full text-gray-500">
                    <div className="text-center">
                      <MessageCircle className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                      <p>لا توجد محادثات</p>
                    </div>
                  </div>
                ) : (
                  conversations.map((conversation) => (
                    <div
                      key={conversation.id}
                      onClick={() => openConversation(conversation)}
                      className="p-3 border-b hover:bg-gray-50 cursor-pointer"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center">
                            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                              <Users className="h-5 w-5 text-blue-600" />
                            </div>
                            <div className="flex-1">
                              <h4 className="font-medium text-sm">{conversation.title}</h4>
                              <p className="text-xs text-gray-500">
                                {userType === 'user' ? conversation.client_name : conversation.user_name}
                              </p>
                            </div>
                          </div>
                        </div>
                        <div className="text-left">
                          <p className="text-xs text-gray-400">
                            {formatTime(conversation.last_message_at)}
                          </p>
                          {conversation.unread_count && conversation.unread_count > 0 && (
                            <Badge className="bg-red-500 text-white text-xs mt-1">
                              {conversation.unread_count}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            ) : (
              // نافذة المحادثة
              <div className="h-full flex flex-col">
                {/* الرسائل */}
                <div className="flex-1 overflow-y-auto p-4 space-y-3 messages-container relative">
                  {isLoading ? (
                    <div className="flex items-center justify-center h-full">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    </div>
                  ) : (
                    messages.map((message) => (
                      <div
                        key={`msg-${message.id}-${message.created_at}`}
                        className={`flex ${
                          (message.sender_type === userType && message.sender_id === userId)
                            ? 'justify-end'
                            : 'justify-start'
                        }`}
                      >
                        <div
                          className={`max-w-[80%] p-3 rounded-lg relative ${
                            message.sender_type === 'ai'
                              ? 'bg-gradient-to-r from-purple-100 to-blue-100 text-gray-900 border border-purple-200'
                              : (message.sender_type === userType && message.sender_id === userId)
                              ? 'bg-blue-600 text-white'
                              : 'bg-gray-100 text-gray-900'
                          }`}
                        >
                          {message.sender_type === 'ai' && (
                            <div className="flex items-center mb-2">
                              <Bot className="h-4 w-4 text-purple-600 mr-1" />
                              <span className="text-xs font-medium text-purple-600">المساعد الذكي</span>
                              <Zap className="h-3 w-3 text-yellow-500 mr-1" />
                            </div>
                          )}
                          <p className="text-sm whitespace-pre-wrap">{message.message_text}</p>
                          <p className={`text-xs mt-1 ${
                            message.sender_type === 'ai'
                              ? 'text-purple-500'
                              : (message.sender_type === userType && message.sender_id === userId)
                              ? 'text-blue-100'
                              : 'text-gray-500'
                          }`}>
                            {formatTime(message.created_at)}
                            {message.sender_type === 'ai' && (
                              <span className="mr-2">🤖</span>
                            )}
                          </p>
                        </div>
                      </div>
                    ))
                  )}
                  <div ref={messagesEndRef} />
                  
                  {/* زر العودة للأسفل - مبسط */}
                  {userScrolledUp && (
                    <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 z-10">
                      <button
                        onClick={() => {
                          setUserScrolledUp(false)
                          setTimeout(() => {
                            messagesEndRef.current?.scrollIntoView({ 
                              behavior: 'smooth',
                              block: 'end'
                            })
                          }, 100)
                        }}
                        className="bg-blue-600 hover:bg-blue-700 text-white rounded-full px-3 py-2 shadow-lg text-sm font-medium"
                      >
                        ↓ رسائل جديدة
                      </button>
                    </div>
                  )}
                </div>

                {/* مربع الإرسال */}
                <div className="p-4 border-t">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <Input
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      placeholder="اكتب رسالة..."
                      className="flex-1"
                      onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                    />
                    <Button
                      onClick={sendMessage}
                      disabled={!newMessage.trim()}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </>
  )
})

export { ChatWidget }
