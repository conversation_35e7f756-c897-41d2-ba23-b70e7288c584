const { exec } = require('child_process');
const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

async function diagnosePostgreSQL() {
  console.log('🔍 تشخيص شامل لمشكلة PostgreSQL...\n');

  // 1. فحص الخدمات
  console.log('1️⃣ فحص خدمات PostgreSQL...');
  try {
    await new Promise((resolve, reject) => {
      exec('sc query | findstr /i postgres', (error, stdout, stderr) => {
        if (stdout && stdout.trim()) {
          console.log('✅ تم العثور على خدمات PostgreSQL:');
          console.log(stdout);
        } else {
          console.log('❌ لم يتم العثور على خدمات PostgreSQL مشغلة');
        }
        resolve();
      });
    });
  } catch (error) {
    console.log('⚠️ لا يمكن فحص الخدمات');
  }

  // 2. فحص المنافذ
  console.log('\n2️⃣ فحص المنافذ...');
  const ports = [5432, 5433, 5434, 3306]; // إضافة MySQL للتحقق
  
  for (const port of ports) {
    try {
      await new Promise((resolve) => {
        exec(`netstat -an | findstr :${port}`, (error, stdout, stderr) => {
          if (stdout && stdout.trim()) {
            console.log(`✅ المنفذ ${port} مستخدم:`);
            console.log(stdout.trim());
          } else {
            console.log(`❌ المنفذ ${port} غير مستخدم`);
          }
          resolve();
        });
      });
    } catch (error) {
      console.log(`⚠️ لا يمكن فحص المنفذ ${port}`);
    }
  }

  // 3. فحص العمليات
  console.log('\n3️⃣ فحص العمليات...');
  try {
    await new Promise((resolve) => {
      exec('tasklist | findstr /i postgres', (error, stdout, stderr) => {
        if (stdout && stdout.trim()) {
          console.log('✅ عمليات PostgreSQL مشغلة:');
          console.log(stdout);
        } else {
          console.log('❌ لا توجد عمليات PostgreSQL مشغلة');
        }
        resolve();
      });
    });
  } catch (error) {
    console.log('⚠️ لا يمكن فحص العمليات');
  }

  // 4. فحص مجلدات التثبيت
  console.log('\n4️⃣ فحص مجلدات التثبيت...');
  const installPaths = [
    'C:\\Program Files\\PostgreSQL',
    'C:\\Program Files (x86)\\PostgreSQL',
    'C:\\PostgreSQL',
    'C:\\xampp\\postgresql',
    'C:\\wamp64\\bin\\postgresql',
    'D:\\xampp\\postgresql',
    'D:\\wamp64\\bin\\postgresql'
  ];

  let foundInstallation = false;
  for (const installPath of installPaths) {
    if (fs.existsSync(installPath)) {
      console.log(`✅ تم العثور على PostgreSQL في: ${installPath}`);
      try {
        const versions = fs.readdirSync(installPath);
        console.log(`   الإصدارات المتاحة: ${versions.join(', ')}`);
        foundInstallation = true;
      } catch (error) {
        console.log(`   ⚠️ لا يمكن قراءة محتويات المجلد`);
      }
    }
  }

  if (!foundInstallation) {
    console.log('❌ لم يتم العثور على PostgreSQL في المجلدات الشائعة');
  }

  // 5. اختبار الاتصال المباشر
  console.log('\n5️⃣ اختبار الاتصال المباشر...');
  
  const configs = [
    { host: 'localhost', port: 5432, user: 'postgres', password: 'yemen123' },
    { host: 'localhost', port: 5432, user: 'postgres', password: 'postgres' },
    { host: 'localhost', port: 5432, user: 'postgres', password: '' },
    { host: 'localhost', port: 5432, user: 'postgres', password: 'admin' },
    { host: 'localhost', port: 5433, user: 'postgres', password: 'yemen123' },
    { host: '127.0.0.1', port: 5432, user: 'postgres', password: 'yemen123' },
  ];

  let workingConfig = null;

  for (let i = 0; i < configs.length; i++) {
    const config = configs[i];
    console.log(`\n   ${i + 1}. اختبار: ${config.user}@${config.host}:${config.port} (${config.password || 'فارغة'})`);
    
    const client = new Client({
      ...config,
      database: 'postgres',
      connectTimeoutMillis: 3000
    });

    try {
      await client.connect();
      console.log('      ✅ نجح الاتصال!');
      
      // جلب قائمة قواعد البيانات
      const result = await client.query(`
        SELECT datname FROM pg_database 
        WHERE datistemplate = false 
        ORDER BY datname
      `);
      
      console.log(`      📊 قواعد البيانات (${result.rows.length}):`);
      result.rows.forEach(row => {
        console.log(`         - ${row.datname}`);
      });

      // البحث عن قواعد البيانات المطلوبة
      const targetDbs = ['mohammi', 'mohammidev'];
      const foundDbs = [];
      
      for (const targetDb of targetDbs) {
        const found = result.rows.find(row => row.datname === targetDb);
        if (found) {
          foundDbs.push(targetDb);
          console.log(`      🎯 تم العثور على: ${targetDb}`);
        }
      }

      workingConfig = {
        ...config,
        databases: result.rows.map(row => row.datname),
        targetDatabases: foundDbs
      };

      await client.end();
      break;
      
    } catch (error) {
      console.log(`      ❌ فشل: ${error.message}`);
      
      if (error.message.includes('ECONNREFUSED')) {
        console.log('         السبب: الخادم غير مشغل أو المنفذ مغلق');
      } else if (error.message.includes('authentication failed')) {
        console.log('         السبب: كلمة المرور خاطئة');
      } else if (error.message.includes('timeout')) {
        console.log('         السبب: انتهت مهلة الاتصال');
      }
    }
  }

  // 6. النتائج والتوصيات
  console.log('\n' + '='.repeat(60));
  console.log('📋 ملخص التشخيص:');
  console.log('='.repeat(60));

  if (workingConfig) {
    console.log('✅ تم العثور على اتصال صحيح!');
    console.log(`   المضيف: ${workingConfig.host}`);
    console.log(`   المنفذ: ${workingConfig.port}`);
    console.log(`   المستخدم: ${workingConfig.user}`);
    console.log(`   كلمة المرور: ${workingConfig.password || 'فارغة'}`);
    console.log(`   قواعد البيانات: ${workingConfig.databases.length}`);
    
    if (workingConfig.targetDatabases.length > 0) {
      console.log(`   قواعد البيانات المطلوبة: ${workingConfig.targetDatabases.join(', ')}`);
    } else {
      console.log('   ⚠️ لم يتم العثور على قواعد البيانات mohammi أو mohammidev');
    }

    console.log('\n🔧 الخطوات التالية:');
    console.log('1. سأقوم بتحديث ملف routing.config.json');
    console.log('2. سأقوم بإنشاء قاعدة البيانات المطلوبة إذا لم تكن موجودة');
    console.log('3. سأقوم بإنشاء جدول case_movements');

    return workingConfig;

  } else {
    console.log('❌ لم يتم العثور على أي اتصال صحيح');
    console.log('\n🛠️ الحلول المقترحة:');
    
    if (!foundInstallation) {
      console.log('1. PostgreSQL غير مثبت - قم بتثبيته من:');
      console.log('   https://www.postgresql.org/download/windows/');
      console.log('2. أو استخدم XAMPP مع PostgreSQL');
      console.log('3. أو استخدم Docker: docker run -p 5432:5432 -e POSTGRES_PASSWORD=yemen123 postgres');
    } else {
      console.log('1. PostgreSQL مثبت ولكن غير مشغل:');
      console.log('   - افتح Services (services.msc)');
      console.log('   - ابحث عن postgresql وشغلها');
      console.log('   - أو استخدم: net start postgresql-x64-XX');
      console.log('2. تحقق من إعدادات الجدار الناري');
      console.log('3. تحقق من ملف postgresql.conf');
      console.log('4. تحقق من ملف pg_hba.conf');
    }

    return null;
  }
}

// تشغيل التشخيص
diagnosePostgreSQL()
  .then(result => {
    if (result) {
      console.log('\n🎉 التشخيص مكتمل - تم العثور على حل!');
      
      // حفظ الإعدادات في ملف
      const configData = {
        connection: result,
        timestamp: new Date().toISOString(),
        status: 'success'
      };
      
      fs.writeFileSync('postgres-diagnosis.json', JSON.stringify(configData, null, 2));
      console.log('💾 تم حفظ الإعدادات في postgres-diagnosis.json');
      
    } else {
      console.log('\n💥 التشخيص مكتمل - لم يتم العثور على حل');
      console.log('📞 يرجى اتباع الحلول المقترحة أعلاه');
      
      const configData = {
        connection: null,
        timestamp: new Date().toISOString(),
        status: 'failed',
        recommendations: [
          'تثبيت PostgreSQL',
          'تشغيل خدمة PostgreSQL',
          'فحص الجدار الناري',
          'فحص إعدادات PostgreSQL'
        ]
      };
      
      fs.writeFileSync('postgres-diagnosis.json', JSON.stringify(configData, null, 2));
    }
  })
  .catch(error => {
    console.error('\n💥 خطأ في التشخيص:', error.message);
  });
