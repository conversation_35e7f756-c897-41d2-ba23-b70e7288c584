// إصلاح المشاكل النهائية وإضافة البيانات
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function fixFinalIssues() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔄 جاري الاتصال بقاعدة البيانات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 1. إنشاء جدول account_linking_settings المفقود
    console.log('🔧 إنشاء جدول account_linking_settings...');
    try {
      await client.query(`
        CREATE TABLE IF NOT EXISTS account_linking_settings (
          id SERIAL PRIMARY KEY,
          table_name VARCHAR(100) NOT NULL,
          account_field VARCHAR(100) NOT NULL,
          default_account_id INTEGER,
          is_active BOOLEAN DEFAULT true,
          created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
      console.log('✅ تم إنشاء جدول account_linking_settings');
    } catch (error) {
      console.log(`⚠️ خطأ في إنشاء الجدول: ${error.message}`);
    }

    // 2. إضافة بيانات أساسية لجدول account_linking_settings
    console.log('🔄 إضافة بيانات أساسية لجدول account_linking_settings...');
    const linkingData = [
      { table_name: 'employees', account_field: 'account_id', default_account_id: 1 },
      { table_name: 'clients', account_field: 'account_id', default_account_id: 2 },
      { table_name: 'issues', account_field: 'account_id', default_account_id: 3 }
    ];

    for (const link of linkingData) {
      try {
        await client.query(`
          INSERT INTO account_linking_settings (table_name, account_field, default_account_id)
          SELECT $1, $2, $3
          WHERE NOT EXISTS (SELECT 1 FROM account_linking_settings WHERE table_name = $1)
        `, [link.table_name, link.account_field, link.default_account_id]);
        console.log(`   ✅ تم إدراج إعداد: ${link.table_name}`);
      } catch (error) {
        console.log(`   ⚠️ خطأ في إدراج ${link.table_name}: ${error.message}`);
      }
    }

    // 3. إضافة بيانات الموظفين
    console.log('🔄 إضافة بيانات الموظفين...');
    const employeesData = [
      { name: 'أحمد محمد الصالح', position: 'محامي أول', phone: '*********', email: '<EMAIL>', salary: 150000 },
      { name: 'سارة علي الحسن', position: 'محامية', phone: '*********', email: '<EMAIL>', salary: 120000 },
      { name: 'محمد عبدالله القاسم', position: 'مساعد قانوني', phone: '*********', email: '<EMAIL>', salary: 80000 }
    ];

    for (const employee of employeesData) {
      try {
        await client.query(`
          INSERT INTO employees (name, position, phone, email, salary, account_id)
          SELECT $1, $2, $3, $4, $5, 1
          WHERE NOT EXISTS (SELECT 1 FROM employees WHERE phone = $3)
        `, [employee.name, employee.position, employee.phone, employee.email, employee.salary]);
        console.log(`   ✅ تم إدراج الموظف: ${employee.name}`);
      } catch (error) {
        console.log(`   ⚠️ خطأ في إدراج ${employee.name}: ${error.message}`);
      }
    }

    // 4. إضافة بيانات الموكلين
    console.log('🔄 إضافة بيانات الموكلين...');
    const clientsData = [
      { name: 'شركة الأمل التجارية', phone: '*********', email: '<EMAIL>', address: 'صنعاء - شارع الزبيري', id_number: '***********', client_type: 'company' },
      { name: 'عبدالرحمن محمد الشامي', phone: '*********', email: '<EMAIL>', address: 'عدن - كريتر', id_number: '***********', client_type: 'individual' },
      { name: 'مؤسسة النور للتجارة', phone: '*********', email: '<EMAIL>', address: 'تعز - شارع جمال', id_number: '***********', client_type: 'company' }
    ];

    for (const client of clientsData) {
      try {
        await client.query(`
          INSERT INTO clients (name, phone, email, address, id_number, client_type, account_id)
          SELECT $1, $2, $3, $4, $5, $6, 2
          WHERE NOT EXISTS (SELECT 1 FROM clients WHERE id_number = $5)
        `, [client.name, client.phone, client.email, client.address, client.id_number, client.client_type]);
        console.log(`   ✅ تم إدراج الموكل: ${client.name}`);
      } catch (error) {
        console.log(`   ⚠️ خطأ في إدراج ${client.name}: ${error.message}`);
      }
    }

    // 5. إضافة بيانات القضايا مع case_number
    console.log('🔄 إضافة بيانات القضايا...');
    const issuesData = [
      { case_number: 'CASE-2024-001', title: 'قضية تجارية - نزاع عقد', description: 'نزاع حول تنفيذ عقد توريد بضائع', status: 'active', client_id: 1 },
      { case_number: 'CASE-2024-002', title: 'قضية عمالية - فصل تعسفي', description: 'دعوى فصل تعسفي من العمل', status: 'active', client_id: 2 },
      { case_number: 'CASE-2024-003', title: 'قضية مدنية - تعويض أضرار', description: 'دعوى تعويض عن أضرار مادية ومعنوية', status: 'pending', client_id: 3 }
    ];

    for (const issue of issuesData) {
      try {
        await client.query(`
          INSERT INTO issues (case_number, title, description, status, client_id)
          SELECT $1, $2, $3, $4, $5
          WHERE NOT EXISTS (SELECT 1 FROM issues WHERE case_number = $1)
        `, [issue.case_number, issue.title, issue.description, issue.status, issue.client_id]);
        console.log(`   ✅ تم إدراج القضية: ${issue.title}`);
      } catch (error) {
        console.log(`   ⚠️ خطأ في إدراج ${issue.title}: ${error.message}`);
      }
    }

    // 6. التحقق من النتائج النهائية
    console.log('🔄 جاري التحقق من النتائج النهائية...');
    
    const results = await Promise.all([
      client.query('SELECT COUNT(*) FROM users'),
      client.query('SELECT COUNT(*) FROM employees'),
      client.query('SELECT COUNT(*) FROM clients'),
      client.query('SELECT COUNT(*) FROM issues'),
      client.query('SELECT COUNT(*) FROM lineages'),
      client.query('SELECT COUNT(*) FROM services'),
      client.query('SELECT COUNT(*) FROM account_linking_settings')
    ]);

    console.log('📊 ملخص البيانات النهائي:');
    console.log(`   - المستخدمين: ${results[0].rows[0].count} سجل`);
    console.log(`   - الموظفين: ${results[1].rows[0].count} سجل`);
    console.log(`   - الموكلين: ${results[2].rows[0].count} سجل`);
    console.log(`   - القضايا: ${results[3].rows[0].count} سجل`);
    console.log(`   - النسب المالية: ${results[4].rows[0].count} سجل`);
    console.log(`   - الخدمات: ${results[5].rows[0].count} سجل`);
    console.log(`   - إعدادات ربط الحسابات: ${results[6].rows[0].count} سجل`);

    console.log('✅ تم إصلاح جميع المشاكل وإضافة البيانات بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في إصلاح المشاكل:', error.message);
  } finally {
    await client.end();
    console.log('🔄 تم قطع الاتصال بقاعدة البيانات');
  }
}

fixFinalIssues();
