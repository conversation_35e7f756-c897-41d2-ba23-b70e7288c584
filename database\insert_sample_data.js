const { Pool } = require('pg');

// Database configuration
const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123',
  ssl: false
});

async function insertSampleData() {
  const client = await pool.connect();
  
  try {
    console.log('🚀 بدء إدراج البيانات التجريبية...');

    // التأكد من وجود البيانات الأساسية أولاً
    await client.query(`
      INSERT INTO clients (name, phone, email, address, id_number) 
      VALUES ('عميل تجريبي', '0501234567', '<EMAIL>', 'الرياض', '1234567890')
      ON CONFLICT (id_number) DO NOTHING
    `);

    await client.query(`
      INSERT INTO employees (name, position, phone, email, id_number, salary) 
      VALUES ('محامي تجريبي', 'محامي', '0509876543', '<EMAIL>', '0987654321', 10000.00)
      ON CONFLICT (id_number) DO NOTHING
    `);

    await client.query(`
      INSERT INTO issue_types (name, description, color) 
      VALUES ('قضية تجريبية', 'نوع قضية للاختبار', '#3B82F6')
      ON CONFLICT DO NOTHING
    `);

    await client.query(`
      INSERT INTO issues (case_number, title, description, client_id, issue_type_id, status, amount) 
      VALUES ('CASE-001', 'قضية تجريبية', 'قضية للاختبار', 1, 1, 'active', 50000.00)
      ON CONFLICT (case_number) DO NOTHING
    `);

    // إدراج بيانات تجريبية للوثائق
    await client.query(`
      INSERT INTO documents (
        title, description, file_name, file_path, file_size, file_type, mime_type,
        case_id, client_id, category, subcategory, tags, access_level, is_confidential
      ) VALUES 
      ('عقد الاستشارة القانونية', 'عقد الاستشارة القانونية مع العميل الأول', 'contract_001.pdf', '/uploads/documents/contract_001.pdf', 1024000, 'pdf', 'application/pdf', 1, 1, 'contract', 'consultation', ARRAY['عقد', 'استشارة', 'قانونية'], 'private', false),
      ('شهادة الشهود', 'شهادات الشهود في القضية الأولى', 'witnesses_001.pdf', '/uploads/documents/witnesses_001.pdf', 512000, 'pdf', 'application/pdf', 1, 1, 'evidence', 'witness', ARRAY['شهادة', 'شهود', 'أدلة'], 'restricted', true),
      ('مراسلة مع المحكمة', 'مراسلة رسمية مع المحكمة', 'court_letter_001.pdf', '/uploads/documents/court_letter_001.pdf', 256000, 'pdf', 'application/pdf', 1, 1, 'correspondence', 'court', ARRAY['مراسلة', 'محكمة', 'رسمي'], 'private', false),
      ('الوثائق المالية', 'الوثائق المالية المتعلقة بالقضية', 'financial_docs_001.pdf', '/uploads/documents/financial_docs_001.pdf', 2048000, 'pdf', 'application/pdf', 1, 1, 'financial', 'statements', ARRAY['مالية', 'وثائق', 'حسابات'], 'restricted', true)
      ON CONFLICT DO NOTHING
    `);

    // إدراج بيانات تجريبية لتسجيلات الوقت
    await client.query(`
      INSERT INTO time_entries (
        case_id, client_id, employee_id, start_time, end_time, duration_minutes,
        task_description, task_category, hourly_rate, billable_amount, is_billable, status
      ) VALUES 
      (1, 1, 1, CURRENT_TIMESTAMP - INTERVAL '3 hours', CURRENT_TIMESTAMP - INTERVAL '2 hours', 60, 'مراجعة ملف القضية وإعداد المرافعة الأولية', 'documentation', 200.00, 200.00, true, 'completed'),
      (1, 1, 1, CURRENT_TIMESTAMP - INTERVAL '2 days', CURRENT_TIMESTAMP - INTERVAL '2 days' + INTERVAL '90 minutes', 90, 'اجتماع مع الموكل لمناقشة تفاصيل القضية', 'meeting', 200.00, 300.00, true, 'completed'),
      (1, 1, 1, CURRENT_TIMESTAMP - INTERVAL '1 day', CURRENT_TIMESTAMP - INTERVAL '1 day' + INTERVAL '45 minutes', 45, 'بحث قانوني حول سوابق قضائية مشابهة', 'research', 200.00, 150.00, true, 'completed'),
      (1, 1, 1, CURRENT_TIMESTAMP - INTERVAL '4 hours', CURRENT_TIMESTAMP - INTERVAL '3 hours', 60, 'إعداد وثائق المحكمة', 'documentation', 200.00, 200.00, true, 'completed'),
      (1, 1, 1, CURRENT_TIMESTAMP - INTERVAL '6 hours', CURRENT_TIMESTAMP - INTERVAL '5 hours', 60, 'مراسلة مع الطرف الآخر', 'correspondence', 200.00, 200.00, true, 'completed')
      ON CONFLICT DO NOTHING
    `);

    // إدراج فاتورة تجريبية
    await client.query(`
      INSERT INTO invoices (
        invoice_number, client_id, client_name, client_address,
        invoice_date, due_date, subtotal, tax_rate, tax_amount,
        discount_amount, total_amount, status, payment_status, created_by
      ) VALUES 
      ('INV000001', 1, 'عميل تجريبي', 'الرياض، المملكة العربية السعودية',
       CURRENT_DATE - INTERVAL '5 days', CURRENT_DATE + INTERVAL '25 days', 1050.00, 15.00, 157.50,
       0.00, 1207.50, 'sent', 'unpaid', 1)
      ON CONFLICT (invoice_number) DO NOTHING
    `);

    // إدراج عناصر الفاتورة
    await client.query(`
      INSERT INTO invoice_items (
        invoice_id, description, quantity, unit_price, total_price, time_entry_id, item_type
      ) VALUES 
      (1, 'مراجعة ملف القضية وإعداد المرافعة الأولية', 1.00, 200.00, 200.00, 1, 'service'),
      (1, 'اجتماع مع الموكل لمناقشة تفاصيل القضية', 1.50, 200.00, 300.00, 2, 'service'),
      (1, 'بحث قانوني حول سوابق قضائية مشابهة', 0.75, 200.00, 150.00, 3, 'service'),
      (1, 'إعداد وثائق المحكمة', 1.00, 200.00, 200.00, 4, 'service'),
      (1, 'مراسلة مع الطرف الآخر', 1.00, 200.00, 200.00, 5, 'service')
      ON CONFLICT DO NOTHING
    `);

    // تحديث تسجيلات الوقت كمفوترة
    await client.query(`
      UPDATE time_entries 
      SET is_billed = true 
      WHERE id IN (1, 2, 3, 4, 5)
    `);

    // إدراج حساب عميل تجريبي
    await client.query(`
      INSERT INTO client_portal_accounts (
        client_id, username, email, password_hash, is_active, is_verified,
        language, timezone, notification_preferences
      ) VALUES 
      (1, 'client_demo', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Txjyvq', true, true,
       'ar', 'Asia/Riyadh', '{"email_notifications": true, "case_updates": true, "document_uploads": true}')
      ON CONFLICT (client_id) DO NOTHING
    `);

    // إدراج إشعارات تجريبية للعميل
    await client.query(`
      INSERT INTO client_notifications (
        client_id, title, message, type, case_id, is_read
      ) VALUES 
      (1, 'مرحباً بك في بوابة العملاء', 'تم إنشاء حسابك بنجاح في بوابة العملاء. يمكنك الآن متابعة قضاياك والوصول للوثائق.', 'success', NULL, false),
      (1, 'تحديث في القضية', 'تم إضافة وثيقة جديدة لقضيتك رقم CASE-001', 'info', 1, false),
      (1, 'فاتورة جديدة', 'تم إنشاء فاتورة جديدة رقم INV000001 بمبلغ 1207.50 ريال', 'info', 1, true),
      (1, 'موعد قادم', 'لديك موعد مع المحامي غداً في تمام الساعة 10:00 صباحاً', 'warning', 1, false)
      ON CONFLICT DO NOTHING
    `);

    // إدراج طلبات تجريبية من العملاء
    await client.query(`
      INSERT INTO client_requests (
        client_id, case_id, request_type, title, description, priority, status
      ) VALUES 
      (1, 1, 'document_request', 'طلب نسخة من العقد', 'أحتاج نسخة من عقد الاستشارة القانونية', 'medium', 'pending'),
      (1, 1, 'meeting_request', 'طلب موعد', 'أرغب في حجز موعد لمناقشة تطورات القضية', 'high', 'in_progress'),
      (1, 1, 'status_inquiry', 'استفسار عن حالة القضية', 'ما هي آخر التطورات في قضيتي؟', 'low', 'completed')
      ON CONFLICT DO NOTHING
    `);

    // إدراج جلسة تجريبية للعميل
    await client.query(`
      INSERT INTO client_sessions (
        client_id, session_token, ip_address, user_agent, expires_at
      ) VALUES 
      (1, 'demo_session_token_123456789', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', CURRENT_TIMESTAMP + INTERVAL '24 hours')
      ON CONFLICT DO NOTHING
    `);

    console.log('✅ تم إدراج جميع البيانات التجريبية بنجاح!');
    console.log('📋 البيانات المُدرجة:');
    console.log('   - 4 وثائق تجريبية');
    console.log('   - 5 تسجيلات وقت');
    console.log('   - فاتورة واحدة مع 5 عناصر');
    console.log('   - حساب عميل واحد');
    console.log('   - 4 إشعارات للعميل');
    console.log('   - 3 طلبات من العميل');
    console.log('   - جلسة واحدة للعميل');

  } catch (error) {
    console.error('❌ خطأ في إدراج البيانات التجريبية:', error);
    throw error;
  } finally {
    client.release();
  }
}

// تشغيل الدالة
insertSampleData()
  .then(() => {
    console.log('🎉 تم الانتهاء من إدراج البيانات التجريبية!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 فشل في إدراج البيانات التجريبية:', error);
    process.exit(1);
  });