import { NextRequest, NextResponse } from 'next/server'
import { Client } from 'pg'

const dbConfig = {
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'mohammidev'
}

// GET - جلب إعدادات التتبع
export async function GET(request: NextRequest) {
  const client = new Client(dbConfig)
  
  try {
    await client.connect()
    
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    
    let whereClause = 'WHERE is_active = true'
    let queryParams: any[] = []
    
    if (category) {
      whereClause += ' AND category = $1'
      queryParams.push(category)
    }
    
    const settingsQuery = `
      SELECT 
        id,
        setting_name,
        setting_value,
        setting_type,
        description,
        category,
        is_active,
        created_at,
        updated_at
      FROM tracking_settings
      ${whereClause}
      ORDER BY category, setting_name
    `
    
    const result = await client.query(settingsQuery, queryParams)
    
    // تجميع الإعدادات حسب الفئة
    const settingsByCategory = result.rows.reduce((acc, setting) => {
      const category = setting.category || 'general'
      if (!acc[category]) {
        acc[category] = []
      }
      acc[category].push(setting)
      return acc
    }, {})
    
    return NextResponse.json({
      success: true,
      data: {
        settings: result.rows,
        settingsByCategory
      }
    })
    
  } catch (error) {
    console.error('خطأ في جلب إعدادات التتبع:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب إعدادات التتبع'
    }, { status: 500 })
  } finally {
    await client.end()
  }
}

// POST - إضافة إعداد جديد
export async function POST(request: NextRequest) {
  const client = new Client(dbConfig)
  
  try {
    await client.connect()
    
    const body = await request.json()
    const {
      setting_name,
      setting_value,
      setting_type = 'string',
      description,
      category = 'general'
    } = body
    
    // التحقق من صحة البيانات
    if (!setting_name || !setting_value) {
      return NextResponse.json({
        success: false,
        error: 'اسم الإعداد والقيمة مطلوبان'
      }, { status: 400 })
    }
    
    // التحقق من عدم وجود إعداد بنفس الاسم
    const existingCheck = await client.query(
      'SELECT id FROM tracking_settings WHERE setting_name = $1',
      [setting_name]
    )
    
    if (existingCheck.rows.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'يوجد إعداد بنفس الاسم مسبقاً'
      }, { status: 400 })
    }
    
    // إدراج الإعداد الجديد
    const insertQuery = `
      INSERT INTO tracking_settings (
        setting_name, setting_value, setting_type, description, category
      ) VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `
    
    const result = await client.query(insertQuery, [
      setting_name, setting_value, setting_type, description, category
    ])
    
    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم إضافة الإعداد بنجاح'
    })
    
  } catch (error) {
    console.error('خطأ في إضافة إعداد التتبع:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في إضافة الإعداد'
    }, { status: 500 })
  } finally {
    await client.end()
  }
}

// PUT - تحديث إعداد
export async function PUT(request: NextRequest) {
  const client = new Client(dbConfig)
  
  try {
    await client.connect()
    
    const body = await request.json()
    const {
      id,
      setting_value,
      description,
      is_active = true
    } = body
    
    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف الإعداد مطلوب'
      }, { status: 400 })
    }
    
    // بناء استعلام التحديث
    const updateFields = []
    const queryParams = []
    let paramIndex = 1
    
    if (setting_value !== undefined) {
      updateFields.push(`setting_value = $${paramIndex}`)
      queryParams.push(setting_value)
      paramIndex++
    }
    
    if (description !== undefined) {
      updateFields.push(`description = $${paramIndex}`)
      queryParams.push(description)
      paramIndex++
    }
    
    if (is_active !== undefined) {
      updateFields.push(`is_active = $${paramIndex}`)
      queryParams.push(is_active)
      paramIndex++
    }
    
    if (updateFields.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'لا توجد بيانات للتحديث'
      }, { status: 400 })
    }
    
    updateFields.push(`updated_at = CURRENT_TIMESTAMP`)
    queryParams.push(id)
    
    const updateQuery = `
      UPDATE tracking_settings 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `
    
    const result = await client.query(updateQuery, queryParams)
    
    if (result.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الإعداد غير موجود'
      }, { status: 404 })
    }
    
    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم تحديث الإعداد بنجاح'
    })
    
  } catch (error) {
    console.error('خطأ في تحديث إعداد التتبع:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث الإعداد'
    }, { status: 500 })
  } finally {
    await client.end()
  }
}

// DELETE - حذف إعداد (تعطيل فقط)
export async function DELETE(request: NextRequest) {
  const client = new Client(dbConfig)
  
  try {
    await client.connect()
    
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    
    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف الإعداد مطلوب'
      }, { status: 400 })
    }
    
    // تعطيل الإعداد بدلاً من حذفه
    const result = await client.query(
      'UPDATE tracking_settings SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1 RETURNING *',
      [id]
    )
    
    if (result.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الإعداد غير موجود'
      }, { status: 404 })
    }
    
    return NextResponse.json({
      success: true,
      message: 'تم تعطيل الإعداد بنجاح'
    })
    
  } catch (error) {
    console.error('خطأ في حذف إعداد التتبع:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في حذف الإعداد'
    }, { status: 500 })
  } finally {
    await client.end()
  }
}
