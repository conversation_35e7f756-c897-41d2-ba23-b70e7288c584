'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import {
  Building2,
  Edit,
  Save,
  X,
  Phone,
  Mail,
  MapPin,
  Globe,
  FileText,
  Calendar,
  Image as ImageIcon,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react'

interface Company {
  id: number
  name: string
  legal_name: string
  registration_number: string
  tax_number: string
  address: string
  city: string
  country: string
  phone: string
  email: string
  website: string
  logo_url: string
  logo_right_text: string
  logo_left_text: string
  logo_image_url: string
  established_date: string
  legal_form: string
  capital: number
  description: string
  is_active: boolean
  created_date: string
  working_hours?: string
}

export default function CompanyPage() {
  const [company, setCompany] = useState<Company | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [logoPreview, setLogoPreview] = useState<string | null>(null)
  const [isUploadingLogo, setIsUploadingLogo] = useState(false)

  const [formData, setFormData] = useState({
    name: '',
    legal_name: '',
    registration_number: '',
    tax_number: '',
    address: '',
    city: '',
    country: '',
    phone: '',
    email: '',
    website: '',
    logo_url: '',
    logo_right_text: '',
    logo_left_text: '',
    logo_image_url: '',
    established_date: '',
    legal_form: '',
    capital: '',
    description: '',
    working_hours: '',
    is_active: true
  })

  // جلب بيانات الشركة
  const fetchCompany = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch('/api/company')
      const result = await response.json()

      if (result.success && result.data && result.data.length > 0) {
        const companyData = result.data[0]
        setCompany(companyData)
      } else {
        setError('لم يتم العثور على بيانات الشركة')
      }
    } catch (err) {
      console.error('خطأ في جلب بيانات الشركة:', err)
      setError('فشل في جلب بيانات الشركة')
    } finally {
      setIsLoading(false)
    }
  }

  // بدء التعديل
  const handleEdit = () => {
    if (company) {
      setFormData({
        name: company.name || '',
        legal_name: company.legal_name || '',
        registration_number: company.registration_number || '',
        tax_number: company.tax_number || '',
        address: company.address || '',
        city: company.city || '',
        country: company.country || '',
        phone: company.phone || '',
        email: company.email || '',
        website: company.website || '',
        logo_url: company.logo_url || '',
        logo_right_text: company.logo_right_text || '',
        logo_left_text: company.logo_left_text || '',
        logo_image_url: company.logo_image_url || '',
        established_date: company.established_date || '',
        legal_form: company.legal_form || '',
        capital: company.capital ? company.capital.toString() : '',
        description: company.description || '',
        working_hours: company.working_hours || '',
        is_active: company.is_active
      })
      setIsEditing(true)
      setError(null)
      setSuccess(null)
    }
  }

  // حفظ التغييرات
  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!company) return

    try {
      setIsSaving(true)
      setError(null)
      setSuccess(null)

      let logoUrl = formData.logo_image_url

      // رفع الشعار الجديد إذا تم اختياره
      if (logoFile) {
        const uploadedLogoUrl = await uploadLogoFile(logoFile)
        if (uploadedLogoUrl) {
          logoUrl = uploadedLogoUrl
        } else {
          // إذا فشل رفع الشعار، لا نكمل الحفظ
          return
        }
      }

      const updateData = {
        id: company.id,
        ...formData,
        logo_image_url: logoUrl,
        capital: formData.capital ? Number(formData.capital) : 0
      }

      const response = await fetch('/api/company', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData)
      })

      const result = await response.json()

      if (result.success) {
        setCompany(result.data)
        setIsEditing(false)
        setLogoFile(null)
        setLogoPreview(null)
        setSuccess('تم تحديث بيانات الشركة بنجاح')
      } else {
        setError(result.error || 'فشل في تحديث البيانات')
      }
    } catch (err) {
      console.error('خطأ في حفظ البيانات:', err)
      setError('فشل في حفظ التغييرات')
    } finally {
      setIsSaving(false)
    }
  }

  // إلغاء التعديل
  const handleCancel = () => {
    setIsEditing(false)
    setError(null)
    setSuccess(null)
    setLogoFile(null)
    setLogoPreview(null)
  }

  // معالجة اختيار ملف الشعار
  const handleLogoFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      // التحقق من نوع الملف
      if (!file.type.startsWith('image/')) {
        setError('يرجى اختيار ملف صورة صالح')
        return
      }

      // التحقق من حجم الملف (أقل من 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setError('حجم الملف يجب أن يكون أقل من 5 ميجابايت')
        return
      }

      setLogoFile(file)
      setError(null)

      // إنشاء معاينة للصورة
      const reader = new FileReader()
      reader.onload = (e) => {
        setLogoPreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  // رفع ملف الشعار
  const uploadLogoFile = async (file: File): Promise<string | null> => {
    try {
      setIsUploadingLogo(true)

      const formData = new FormData()
      formData.append('logo', file)

      const response = await fetch('/api/upload/logo', {
        method: 'POST',
        body: formData
      })

      const result = await response.json()

      if (result.success) {
        return result.logoUrl
      } else {
        setError(result.error || 'فشل في رفع الشعار')
        return null
      }
    } catch (err) {
      console.error('خطأ في رفع الشعار:', err)
      setError('فشل في رفع الشعار')
      return null
    } finally {
      setIsUploadingLogo(false)
    }
  }

  useEffect(() => {
    fetchCompany()
  }, [])

  // إخفاء رسائل النجاح بعد 5 ثوان
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => setSuccess(null), 5000)
      return () => clearTimeout(timer)
    }
  }, [success])

  if (isLoading) {
    return (
      <MainLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <Loader2 className="h-12 w-12 animate-spin text-blue-600 mx-auto mb-4" />
            <p className="text-gray-600">جاري تحميل بيانات الشركة...</p>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* رأس الصفحة مع الشعار */}
          <div className="mb-8">
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-6 space-x-reverse">
                  {/* شعار الشركة */}
                  {company && company.logo_image_url && (
                    <div className="flex-shrink-0">
                      <div className="relative">
                        <img
                          src={company.logo_image_url}
                          alt="شعار الشركة"
                          className="h-20 w-20 object-contain border-2 border-blue-100 rounded-2xl bg-white p-3 shadow-lg"
                          onError={(e) => {
                            e.currentTarget.style.display = 'none'
                          }}
                        />
                        <div className="absolute -bottom-2 -right-2 bg-green-500 w-6 h-6 rounded-full border-2 border-white flex items-center justify-center">
                          <CheckCircle className="w-3 h-3 text-white" />
                        </div>
                      </div>
                    </div>
                  )}

                  <div>
                    <div className="flex items-center mb-2">
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-4">
                        <Building2 className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h1 className="text-3xl font-bold text-gray-900">بيانات الشركة</h1>
                        <p className="text-gray-600">إدارة ومتابعة المعلومات الأساسية للشركة</p>
                      </div>
                    </div>
                    {company && (
                      <div className="flex items-center mt-3">
                        <div className="bg-blue-50 text-blue-700 px-4 py-2 rounded-lg text-sm font-medium">
                          {company.name}
                        </div>
                        <Badge
                          variant={company.is_active ? "default" : "destructive"}
                          className="mr-3"
                        >
                          {company.is_active ? 'نشطة' : 'غير نشطة'}
                        </Badge>
                      </div>
                    )}
                  </div>
                </div>

                {!isEditing && company && (
                  <Button onClick={handleEdit} className="bg-blue-600 hover:bg-blue-700">
                    <Edit className="h-4 w-4 mr-2" />
                    تعديل البيانات
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* رسائل النجاح والخطأ */}
          {success && (
            <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                <p className="text-green-800">{success}</p>
              </div>
            </div>
          )}

          {error && (
            <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
                <p className="text-red-800">{error}</p>
              </div>
            </div>
          )}

          {!company && !error && (
            <div className="text-center py-12">
              <Building2 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد بيانات شركة</h3>
              <p className="text-gray-600">لم يتم العثور على بيانات الشركة في النظام</p>
            </div>
          )}

          {company && (
            <div className="space-y-8">
              {/* قسم الشعار ومعلومات الشركة الأساسية */}
              <Card className="overflow-hidden">
                <CardHeader className="bg-gradient-to-r from-blue-600 to-blue-700 text-white">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-xl font-bold">معلومات الشركة الأساسية</CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="p-8">
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* قسم الشعار */}
                    <div className="lg:col-span-1">
                      <div className="text-center">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">شعار الشركة</h3>
                        <div className="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-8 mb-4">
                          {logoPreview ? (
                            <div className="relative">
                              <img
                                src={logoPreview}
                                alt="معاينة الشعار"
                                className="max-w-full max-h-32 mx-auto object-contain"
                              />
                              <div className="absolute top-2 right-2 bg-blue-500 text-white px-2 py-1 rounded text-xs">
                                جديد
                              </div>
                            </div>
                          ) : company.logo_image_url ? (
                            <img
                              src={company.logo_image_url}
                              alt="شعار الشركة"
                              className="max-w-full max-h-32 mx-auto object-contain"
                            />
                          ) : (
                            <div className="text-center">
                              <ImageIcon className="h-16 w-16 text-gray-400 mx-auto mb-2" />
                              <p className="text-gray-500">لا يوجد شعار</p>
                            </div>
                          )}
                        </div>
                        {isEditing && (
                          <div className="space-y-4">
                            {/* رفع ملف جديد */}
                            <div>
                              <Label htmlFor="logo_file" className="text-sm font-medium text-gray-700 mb-2 block">
                                رفع شعار جديد
                              </Label>
                              <div className="flex items-center justify-center w-full">
                                <label
                                  htmlFor="logo_file"
                                  className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors"
                                >
                                  <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                    <svg className="w-8 h-8 mb-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                                      <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
                                    </svg>
                                    <p className="mb-2 text-sm text-gray-500">
                                      <span className="font-semibold">اضغط لرفع ملف</span> أو اسحب وأفلت
                                    </p>
                                    <p className="text-xs text-gray-500">PNG, JPG, GIF (أقل من 5MB)</p>
                                  </div>
                                  <input
                                    id="logo_file"
                                    type="file"
                                    className="hidden"
                                    accept="image/*"
                                    onChange={handleLogoFileChange}
                                  />
                                </label>
                              </div>
                              {logoFile && (
                                <div className="mt-2 text-sm text-green-600 flex items-center">
                                  <CheckCircle className="h-4 w-4 mr-1" />
                                  تم اختيار: {logoFile.name}
                                </div>
                              )}
                            </div>

                            {/* أو إدخال رابط */}
                            <div className="relative">
                              <div className="absolute inset-0 flex items-center">
                                <div className="w-full border-t border-gray-300" />
                              </div>
                              <div className="relative flex justify-center text-sm">
                                <span className="px-2 bg-white text-gray-500">أو</span>
                              </div>
                            </div>

                            <div>
                              <Label htmlFor="logo_image_url" className="text-sm font-medium text-gray-700">
                                رابط الشعار
                              </Label>
                              <Input
                                id="logo_image_url"
                                value={formData.logo_image_url}
                                onChange={(e) => setFormData({...formData, logo_image_url: e.target.value})}
                                placeholder="/images/logo.png"
                                className="mt-1"
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* معلومات الشركة الأساسية */}
                    <div className="lg:col-span-2">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* اسم الشركة */}
                        <div className="md:col-span-2">
                          <Label className="text-sm font-medium text-gray-700">اسم الشركة</Label>
                          {isEditing ? (
                            <Input
                              value={formData.name}
                              onChange={(e) => setFormData({...formData, name: e.target.value})}
                              className="mt-1"
                              placeholder="اسم الشركة"
                            />
                          ) : (
                            <p className="mt-1 text-lg font-semibold text-gray-900">{company.name}</p>
                          )}
                        </div>

                        {/* الاسم القانوني */}
                        <div className="md:col-span-2">
                          <Label className="text-sm font-medium text-gray-700">الاسم القانوني</Label>
                          {isEditing ? (
                            <Input
                              value={formData.legal_name}
                              onChange={(e) => setFormData({...formData, legal_name: e.target.value})}
                              className="mt-1"
                              placeholder="الاسم القانوني للشركة"
                            />
                          ) : (
                            <p className="mt-1 text-gray-900">{company.legal_name}</p>
                          )}
                        </div>

                        {/* الوصف */}
                        <div className="md:col-span-2">
                          <Label className="text-sm font-medium text-gray-700">وصف الشركة</Label>
                          {isEditing ? (
                            <Textarea
                              value={formData.description}
                              onChange={(e) => setFormData({...formData, description: e.target.value})}
                              className="mt-1"
                              rows={3}
                              placeholder="وصف مختصر عن الشركة وأنشطتها"
                            />
                          ) : (
                            <p className="mt-1 text-gray-900">{company.description}</p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* قسم معلومات الاتصال */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Phone className="h-5 w-5 mr-2 text-blue-600" />
                    معلومات الاتصال
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* رقم الهاتف */}
                    <div>
                      <Label className="text-sm font-medium text-gray-700">رقم الهاتف</Label>
                      {isEditing ? (
                        <Input
                          value={formData.phone}
                          onChange={(e) => setFormData({...formData, phone: e.target.value})}
                          className="mt-1"
                          placeholder="+967 1 234 567"
                        />
                      ) : (
                        <div className="mt-1 flex items-center">
                          <Phone className="h-4 w-4 text-gray-400 mr-2" />
                          <span className="text-gray-900">{company.phone}</span>
                        </div>
                      )}
                    </div>

                    {/* البريد الإلكتروني */}
                    <div>
                      <Label className="text-sm font-medium text-gray-700">البريد الإلكتروني</Label>
                      {isEditing ? (
                        <Input
                          type="email"
                          value={formData.email}
                          onChange={(e) => setFormData({...formData, email: e.target.value})}
                          className="mt-1"
                          placeholder="<EMAIL>"
                        />
                      ) : (
                        <div className="mt-1 flex items-center">
                          <Mail className="h-4 w-4 text-gray-400 mr-2" />
                          <span className="text-gray-900">{company.email}</span>
                        </div>
                      )}
                    </div>

                    {/* الموقع الإلكتروني */}
                    <div>
                      <Label className="text-sm font-medium text-gray-700">الموقع الإلكتروني</Label>
                      {isEditing ? (
                        <Input
                          value={formData.website}
                          onChange={(e) => setFormData({...formData, website: e.target.value})}
                          className="mt-1"
                          placeholder="www.company.com"
                        />
                      ) : (
                        <div className="mt-1 flex items-center">
                          <Globe className="h-4 w-4 text-gray-400 mr-2" />
                          <span className="text-gray-900">{company.website}</span>
                        </div>
                      )}
                    </div>

                    {/* ساعات العمل */}
                    <div>
                      <Label className="text-sm font-medium text-gray-700">ساعات العمل</Label>
                      {isEditing ? (
                        <Input
                          value={formData.working_hours}
                          onChange={(e) => setFormData({...formData, working_hours: e.target.value})}
                          className="mt-1"
                          placeholder="الأحد - الخميس: 8 صباحاً - 6 مساءً"
                        />
                      ) : (
                        <div className="mt-1 flex items-center">
                          <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                          <span className="text-gray-900">{company.working_hours || 'غير محدد'}</span>
                        </div>
                      )}
                    </div>

                    {/* العنوان */}
                    <div className="md:col-span-2">
                      <Label className="text-sm font-medium text-gray-700">العنوان</Label>
                      {isEditing ? (
                        <Textarea
                          value={formData.address}
                          onChange={(e) => setFormData({...formData, address: e.target.value})}
                          className="mt-1"
                          rows={2}
                          placeholder="العنوان الكامل للشركة"
                        />
                      ) : (
                        <div className="mt-1 flex items-start">
                          <MapPin className="h-4 w-4 text-gray-400 mr-2 mt-1" />
                          <span className="text-gray-900">{company.address}</span>
                        </div>
                      )}
                    </div>

                    {/* المدينة والدولة */}
                    <div>
                      <Label className="text-sm font-medium text-gray-700">المدينة</Label>
                      {isEditing ? (
                        <Input
                          value={formData.city}
                          onChange={(e) => setFormData({...formData, city: e.target.value})}
                          className="mt-1"
                          placeholder="المدينة"
                        />
                      ) : (
                        <p className="mt-1 text-gray-900">{company.city}</p>
                      )}
                    </div>

                    <div>
                      <Label className="text-sm font-medium text-gray-700">الدولة</Label>
                      {isEditing ? (
                        <Input
                          value={formData.country}
                          onChange={(e) => setFormData({...formData, country: e.target.value})}
                          className="mt-1"
                          placeholder="الدولة"
                        />
                      ) : (
                        <p className="mt-1 text-gray-900">{company.country}</p>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* قسم المعلومات القانونية والمالية */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <FileText className="h-5 w-5 mr-2 text-blue-600" />
                    المعلومات القانونية والمالية
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* رقم التسجيل */}
                    <div>
                      <Label className="text-sm font-medium text-gray-700">رقم التسجيل</Label>
                      {isEditing ? (
                        <Input
                          value={formData.registration_number}
                          onChange={(e) => setFormData({...formData, registration_number: e.target.value})}
                          className="mt-1"
                          placeholder="رقم التسجيل التجاري"
                        />
                      ) : (
                        <p className="mt-1 text-gray-900 font-mono">{company.registration_number}</p>
                      )}
                    </div>

                    {/* الرقم الضريبي */}
                    <div>
                      <Label className="text-sm font-medium text-gray-700">الرقم الضريبي</Label>
                      {isEditing ? (
                        <Input
                          value={formData.tax_number}
                          onChange={(e) => setFormData({...formData, tax_number: e.target.value})}
                          className="mt-1"
                          placeholder="الرقم الضريبي"
                        />
                      ) : (
                        <p className="mt-1 text-gray-900 font-mono">{company.tax_number}</p>
                      )}
                    </div>

                    {/* الشكل القانوني */}
                    <div>
                      <Label className="text-sm font-medium text-gray-700">الشكل القانوني</Label>
                      {isEditing ? (
                        <Input
                          value={formData.legal_form}
                          onChange={(e) => setFormData({...formData, legal_form: e.target.value})}
                          className="mt-1"
                          placeholder="شركة محدودة المسؤولية"
                        />
                      ) : (
                        <p className="mt-1 text-gray-900">{company.legal_form}</p>
                      )}
                    </div>

                    {/* رأس المال */}
                    <div>
                      <Label className="text-sm font-medium text-gray-700">رأس المال</Label>
                      {isEditing ? (
                        <Input
                          type="number"
                          value={formData.capital}
                          onChange={(e) => setFormData({...formData, capital: e.target.value})}
                          className="mt-1"
                          placeholder="1000000"
                        />
                      ) : (
                        <p className="mt-1 text-gray-900">
                          {company.capital ? company.capital.toLocaleString() : '0'} ريال
                        </p>
                      )}
                    </div>

                    {/* تاريخ التأسيس */}
                    <div>
                      <Label className="text-sm font-medium text-gray-700">تاريخ التأسيس</Label>
                      {isEditing ? (
                        <Input
                          type="date"
                          value={formData.established_date}
                          onChange={(e) => setFormData({...formData, established_date: e.target.value})}
                          className="mt-1"
                        />
                      ) : (
                        <div className="mt-1 flex items-center">
                          <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                          <span className="text-gray-900">
                            {company.established_date ? new Date(company.established_date).toLocaleDateString('ar-SA') : 'غير محدد'}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* تاريخ الإنشاء في النظام */}
                    <div>
                      <Label className="text-sm font-medium text-gray-700">تاريخ الإنشاء في النظام</Label>
                      <div className="mt-1 flex items-center">
                        <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-gray-900">
                          {company.created_date ? new Date(company.created_date).toLocaleDateString('ar-SA') : 'غير محدد'}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* أزرار الحفظ والإلغاء */}
              {isEditing && (
                <Card>
                  <CardContent className="p-6">
                    <form onSubmit={handleSave}>
                      <div className="flex items-center justify-end space-x-4 space-x-reverse">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={handleCancel}
                          disabled={isSaving}
                        >
                          <X className="h-4 w-4 mr-2" />
                          إلغاء
                        </Button>
                        <Button
                          type="submit"
                          disabled={isSaving || isUploadingLogo}
                          className="bg-blue-600 hover:bg-blue-700"
                        >
                          {isUploadingLogo ? (
                            <>
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              جاري رفع الشعار...
                            </>
                          ) : isSaving ? (
                            <>
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              جاري الحفظ...
                            </>
                          ) : (
                            <>
                              <Save className="h-4 w-4 mr-2" />
                              حفظ التغييرات
                            </>
                          )}
                        </Button>
                      </div>
                    </form>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  )
}