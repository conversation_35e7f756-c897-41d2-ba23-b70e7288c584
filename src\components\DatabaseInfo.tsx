'use client';

import React, { useEffect, useState } from 'react';
import { getDatabaseName, getCompanyName, getThemeColor, getWelcomeMessage, getNotificationPrefix } from '@/lib/database-router';

interface DatabaseInfo {
  database: string;
  company: string;
  themeColor: string;
  welcomeMessage: string;
  notificationPrefix: string;
  port?: string;
  timestamp?: string;
}

export default function DatabaseInfo({ showDetails = false }: { showDetails?: boolean }) {
  const [dbInfo, setDbInfo] = useState<DatabaseInfo | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDatabaseInfo = async () => {
      try {
        // جرب الحصول على المعلومات من API أولاً
        const response = await fetch('/api/database-info');
        if (response.ok) {
          const apiData = await response.json();
          if (apiData.success) {
            setDbInfo(apiData);
            setLoading(false);
            return;
          }
        }
      } catch (apiError) {

      }

      // إذا فشل API، استخدم database-router مباشرة
      try {
        const info: DatabaseInfo = {
          database: getDatabaseName(),
          company: getCompanyName(),
          themeColor: getThemeColor(),
          welcomeMessage: getWelcomeMessage(),
          notificationPrefix: getNotificationPrefix(),
          port: typeof window !== 'undefined' ? window.location.port : 'غير محدد',
          timestamp: new Date().toISOString()
        };
        
        setDbInfo(info);
      } catch (error) {
        console.error('خطأ في الحصول على معلومات قاعدة البيانات:', error);
        setDbInfo({
          database: 'غير محدد',
          company: 'نظام إدارة المحاماة',
          themeColor: '#1e40af',
          welcomeMessage: 'مرحباً بكم',
          notificationPrefix: 'إشعار: '
        });
      }
      
      setLoading(false);
    };

    fetchDatabaseInfo();
  }, []);

  if (loading) {
    return (
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          <span className="text-blue-700 text-sm">جاري تحميل معلومات النظام...</span>
        </div>
      </div>
    );
  }

  if (!dbInfo) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-3">
        <span className="text-red-700 text-sm">❌ خطأ في تحميل معلومات النظام</span>
      </div>
    );
  }

  const cardStyle = {
    borderColor: dbInfo.themeColor,
    backgroundColor: `${dbInfo.themeColor}10`
  };

  return (
    <div className="rounded-lg p-4 border-2" style={cardStyle}>
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-lg font-semibold" style={{ color: dbInfo.themeColor }}>
          🏛️ {dbInfo.company}
        </h3>
        <div className="text-xs opacity-75">
          📊 {dbInfo.database}
        </div>
      </div>
      
      {showDetails && (
        <div className="space-y-2 text-sm">
          <div className="grid grid-cols-2 gap-2">
            <div>
              <span className="font-medium">🗄️ قاعدة البيانات:</span>
              <span className="ml-2 px-2 py-1 bg-white rounded text-xs font-mono">
                {dbInfo.database}
              </span>
            </div>
            
            {dbInfo.port && (
              <div>
                <span className="font-medium">🌐 المنفذ:</span>
                <span className="ml-2 px-2 py-1 bg-white rounded text-xs font-mono">
                  {dbInfo.port}
                </span>
              </div>
            )}
          </div>
          
          <div>
            <span className="font-medium">🎨 لون السمة:</span>
            <span className="ml-2 px-2 py-1 bg-white rounded text-xs font-mono">
              {dbInfo.themeColor}
            </span>
            <div 
              className="inline-block ml-2 w-4 h-4 rounded border border-gray-300"
              style={{ backgroundColor: dbInfo.themeColor }}
            ></div>
          </div>
          
          <div>
            <span className="font-medium">💬 رسالة الترحيب:</span>
            <div className="mt-1 p-2 bg-white rounded text-xs">
              {dbInfo.welcomeMessage}
            </div>
          </div>
          
          <div>
            <span className="font-medium">🔔 بادئة الإشعارات:</span>
            <span className="ml-2 px-2 py-1 bg-white rounded text-xs">
              {dbInfo.notificationPrefix}
            </span>
          </div>
          
          {dbInfo.timestamp && (
            <div className="text-xs opacity-60 border-t pt-2">
              آخر تحديث: {new Date(dbInfo.timestamp).toLocaleString('ar-EG')}
            </div>
          )}
        </div>
      )}
    </div>
  );
}