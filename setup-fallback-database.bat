@echo off
chcp 65001 >nul
echo ========================================
echo   Setup Fallback Database System
echo ========================================
echo.

echo 1. Creating local JSON database...
node create-local-database.js
echo.

echo 2. Testing the fallback system...
node -e "
const fallback = require('./src/lib/db-fallback.ts');

async function test() {
  try {
    console.log('Testing JSON database...');
    
    const result = await fallback.query('SELECT * FROM case_movements ORDER BY created_at DESC LIMIT 5');
    console.log('✅ Query successful!');
    console.log('📊 Found', result.rowCount, 'movements');
    
    if (result.rows.length > 0) {
      console.log('📋 Sample data:');
      result.rows.forEach((row, index) => {
        console.log(`   ${index + 1}. ${row.case_number} - ${row.movement_type} (${row.priority})`);
      });
    }
    
    console.log('');
    console.log('🎉 Fallback database is working!');
    console.log('Now you can:');
    console.log('1. Start the server: npm run dev');
    console.log('2. Open: http://localhost:3000/movements');
    console.log('3. See the case movements page working');
    
  } catch (error) {
    console.log('❌ Test failed:', error.message);
  }
}

test();
"

echo.
echo ========================================
echo   Fallback Database Ready!
echo ========================================
echo.
echo The system will now work without PostgreSQL.
echo You can start the server and access:
echo - http://localhost:3300/movements
echo.
echo When PostgreSQL is fixed, the system will
echo automatically switch back to it.
echo.
pause
