@echo off
chcp 65001 >nul
echo.
echo 🚀 إعداد خدمة الخادم المتقدم للعمل التلقائي
echo ================================================
echo.

REM التحقق من صلاحيات المدير
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ يجب تشغيل هذا الملف كمدير ^(Run as Administrator^)
    echo انقر بزر الماوس الأيمن على الملف واختر "Run as administrator"
    pause
    exit /b 1
)

REM متغيرات الإعداد
set SERVICE_NAME=AdvancedUnifiedServer
set SERVICE_DISPLAY_NAME=Advanced Unified Server - نظام إدارة المحاماة
set WORKING_DIR=D:\mohammi
set SERVER_SCRIPT=advanced-unified-server.js
set NODE_PATH=

echo 🔍 البحث عن مسار Node.js...

REM البحث عن Node.js في المسارات المعتادة
if exist "C:\Program Files\nodejs\node.exe" (
    set NODE_PATH=C:\Program Files\nodejs\node.exe
    echo ✅ تم العثور على Node.js في: C:\Program Files\nodejs\node.exe
    goto :found_node
)

if exist "C:\Program Files ^(x86^)\nodejs\node.exe" (
    set NODE_PATH=C:\Program Files ^(x86^)\nodejs\node.exe
    echo ✅ تم العثور على Node.js في: C:\Program Files ^(x86^)\nodejs\node.exe
    goto :found_node
)

REM جرب البحث في PATH
where node >nul 2>&1
if %errorLevel% equ 0 (
    for /f "tokens=*" %%i in ('where node') do (
        set NODE_PATH=%%i
        echo ✅ تم العثور على Node.js في PATH: %%i
        goto :found_node
    )
)

echo ❌ لم يتم العثور على Node.js. يرجى تثبيت Node.js أولاً.
pause
exit /b 1

:found_node

REM التحقق من وجود مجلد العمل
if not exist "%WORKING_DIR%" (
    echo ❌ مجلد العمل غير موجود: %WORKING_DIR%
    pause
    exit /b 1
)

REM التحقق من وجود ملف الخادم
if not exist "%WORKING_DIR%\%SERVER_SCRIPT%" (
    echo ❌ ملف الخادم غير موجود: %WORKING_DIR%\%SERVER_SCRIPT%
    pause
    exit /b 1
)

echo ✅ جميع الملفات موجودة
echo.

REM التحقق من الخدمة الموجودة
echo 🔄 التحقق من الخدمة الموجودة...
sc query "%SERVICE_NAME%" >nul 2>&1
if %errorLevel% equ 0 (
    echo ⚠️ الخدمة موجودة بالفعل. سيتم إيقافها وحذفها...
    
    REM إيقاف الخدمة
    echo ⏹️ إيقاف الخدمة...
    sc stop "%SERVICE_NAME%" >nul 2>&1
    timeout /t 3 /nobreak >nul
    
    REM حذف الخدمة القديمة
    echo 🗑️ حذف الخدمة القديمة...
    sc delete "%SERVICE_NAME%" >nul 2>&1
    timeout /t 2 /nobreak >nul
)

REM إنشاء الخدمة الجديدة
echo 🔧 إنشاء الخدمة الجديدة...
sc create "%SERVICE_NAME%" binPath= "\"%NODE_PATH%\" \"%WORKING_DIR%\%SERVER_SCRIPT%\"" start= auto DisplayName= "%SERVICE_DISPLAY_NAME%"

if %errorLevel% equ 0 (
    echo ✅ تم إنشاء الخدمة بنجاح
) else (
    echo ❌ فشل في إنشاء الخدمة
    pause
    exit /b 1
)

REM تعيين وصف الخدمة
echo 📝 تعيين وصف الخدمة...
sc description "%SERVICE_NAME%" "خادم نظام إدارة المحاماة المتقدم - يدير المنافذ المتعددة والتوجيه التلقائي"

REM تعيين إعدادات الاسترداد
echo 🔄 تعيين إعدادات الاسترداد...
sc failure "%SERVICE_NAME%" reset= 86400 actions= restart/5000/restart/10000/restart/30000

REM بدء الخدمة
echo ▶️ بدء الخدمة...
sc start "%SERVICE_NAME%"

REM انتظار قليل للتأكد من بدء الخدمة
timeout /t 5 /nobreak >nul

REM التحقق من حالة الخدمة
echo 🔍 التحقق من حالة الخدمة...
sc query "%SERVICE_NAME%" | find "RUNNING" >nul
if %errorLevel% equ 0 (
    echo.
    echo 🎉 تم إعداد وتشغيل الخدمة بنجاح!
    echo.
    echo 📊 معلومات الخدمة:
    echo    - اسم الخدمة: %SERVICE_NAME%
    echo    - الاسم المعروض: %SERVICE_DISPLAY_NAME%
    echo    - مسار Node.js: %NODE_PATH%
    echo    - مجلد العمل: %WORKING_DIR%
    echo    - ملف الخادم: %SERVER_SCRIPT%
    echo    - الحالة: تعمل ✅
    echo.
    echo 🌐 المنافذ المتاحة:
    echo    - المنفذ 7443: نظام محمد ^(الإنتاج^)
    echo    - المنفذ 8914: نظام الربعي
    echo    - المنفذ 3300: نظام محمد ^(التطوير^)
    echo.
    echo 🔧 أوامر إدارة الخدمة:
    echo    - إيقاف: sc stop %SERVICE_NAME%
    echo    - تشغيل: sc start %SERVICE_NAME%
    echo    - حذف: sc delete %SERVICE_NAME%
    echo.
    echo ✅ الخدمة ستعمل تلقائياً عند إعادة تشغيل السيرفر
) else (
    echo ❌ فشل في تشغيل الخدمة
    echo 🔍 فحص حالة الخدمة:
    sc query "%SERVICE_NAME%"
)

echo.
echo 📝 ملاحظات مهمة:
echo    - تأكد من أن منافذ 7443 و 8914 و 3300 غير مستخدمة من برامج أخرى
echo    - يمكن مراقبة الخدمة من خلال Services.msc
echo    - في حالة وجود مشاكل، تحقق من Event Viewer
echo.

pause
