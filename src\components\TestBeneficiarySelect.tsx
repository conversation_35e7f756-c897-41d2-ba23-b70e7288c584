'use client'

import { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'

interface TestBeneficiarySelectProps {
  label?: string
}

export default function TestBeneficiarySelect({ label }: TestBeneficiarySelectProps) {
  const [clients, setClients] = useState<any[]>([])
  const [employees, setEmployees] = useState<any[]>([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)

        // جلب العملاء
        const clientsResponse = await fetch('/api/clients')
        const clientsData = await clientsResponse.json()

        setClients(clientsData.success ? clientsData.clients : [])

        // جلب الموظفين
        const employeesResponse = await fetch('/api/employees')
        const employeesData = await employeesResponse.json()

        setEmployees(employeesData.success ? employeesData.employees : [])

      } catch (error) {
        console.error('❌ TestBeneficiarySelect: خطأ في جلب البيانات:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  return (
    <div className="space-y-2">
      {label && (
        <Label className="text-sm font-medium text-gray-700">
          {label}
        </Label>
      )}

      <div className="p-4 border border-gray-300 rounded-md bg-gray-50">
        {loading ? (
          <p className="text-gray-500">جاري التحميل...</p>
        ) : (
          <div className="space-y-2">
            <p className="font-medium text-blue-600">
              العملاء ({clients.length}):
            </p>
            {clients.length > 0 ? (
              <ul className="list-disc list-inside space-y-1 text-sm">
                {clients.slice(0, 3).map((client) => (
                  <li key={client.id} className="text-gray-700">
                    {client.name} - {client.account_code}
                  </li>
                ))}
                {clients.length > 3 && (
                  <li className="text-gray-500">... و {clients.length - 3} عميل آخر</li>
                )}
              </ul>
            ) : (
              <p className="text-gray-500 text-sm">لا توجد عملاء</p>
            )}

            <p className="font-medium text-green-600 mt-4">
              الموظفين ({employees.length}):
            </p>
            {employees.length > 0 ? (
              <ul className="list-disc list-inside space-y-1 text-sm">
                {employees.slice(0, 3).map((employee) => (
                  <li key={employee.id} className="text-gray-700">
                    {employee.name} - {employee.account_code}
                  </li>
                ))}
                {employees.length > 3 && (
                  <li className="text-gray-500">... و {employees.length - 3} موظف آخر</li>
                )}
              </ul>
            ) : (
              <p className="text-gray-500 text-sm">لا توجد موظفين</p>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
