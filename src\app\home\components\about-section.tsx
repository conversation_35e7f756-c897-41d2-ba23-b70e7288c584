'use client';

import Image from 'next/image';
import { Briefcase, Users, Award, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';

type Feature = {
  icon: any;
  title: string;
  description: string;
};

type TeamMember = {
  id: number;
  name: string;
  role: string;
  imageUrl: string;
  bio: string;
};

export function AboutSection() {
  const features: Feature[] = [
    {
      icon: Briefcase,
      title: 'خبرة متراكمة',
      description: 'أكثر من 15 عاماً من الخبرة المتراكمة في جميع المجالات القانونية مع سجل حافل من النجاحات'
    },
    {
      icon: Users,
      title: 'فريق نخبة',
      description: 'فريق من أمهر المحامين والمستشارين القانونيين المتخصصين والحاصلين على أعلى الشهادات'
    },
    {
      icon: Award,
      title: 'ثقة وسمعة',
      description: 'سمعة طيبة وثقة عالية من العملاء مع علاقات قوية مع الجهات الحكومية والقضائية'
    },
    {
      icon: CheckCircle,
      title: 'نتائج مضمونة',
      description: 'نضمن تحقيق أفضل النتائج القانونية الممكنة بحلول مبتكرة ومدروسة بعناية'
    }
  ];

  const team: TeamMember[] = [
    {
      id: 1,
      name: 'د. أحمد محمد',
      role: 'المدير التنفيذي',
      imageUrl: '/images/team/team-1.jpg',
      bio: 'خبرة تزيد عن 20 عاماً في المحاماة والاستشارات القانونية'
    },
    {
      id: 2,
      name: 'أ. سارة أحمد',
      role: 'رئيسة قسم العقود',
      imageUrl: '/images/team/team-2.jpg',
      bio: 'متخصصة في صياغة العقود والاتفاقيات التجارية'
    },
    {
      id: 3,
      name: 'د. خالد عبدالله',
      role: 'مستشار قانوني',
      imageUrl: '/images/team/team-3.jpg',
      bio: 'خبير في المنازعات التجارية والقانون التجاري الدولي'
    },
    {
      id: 4,
      name: 'أ. نورة سليمان',
      role: 'محامية متخصصة',
      imageUrl: '/images/team/team-4.jpg',
      bio: 'متخصصة في قضايا الملكية الفكرية وحقوق النشر'
    }
  ];

  return (
    <section id="about" className="py-20 bg-gradient-to-br from-white to-gray-50">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Content */}
          <div className="order-2 lg:order-1">
            <div className="inline-flex items-center bg-blue-50 text-blue-600 text-sm font-semibold px-6 py-3 rounded-full mb-6">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              من نحن
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              <span className="block">مكتب المحاماة</span>
              <span className="block bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent">والاستشارات القانونية</span>
            </h2>
            <p className="text-xl text-gray-600 mb-6 leading-relaxed">
              نحن مكتب قانوني رائد متخصص في تقديم حلول قانونية متكاملة واستشارات متخصصة بأعلى معايير الجودة والاحترافية.
            </p>
            <p className="text-lg text-gray-600 mb-8 leading-relaxed">
              نتميز بفريق من أمهر المحامين والمستشارين القانونيين ذوي الخبرة الواسعة، ونلتزم بتحقيق أفضل النتائج لعملائنا مع الحفاظ على أعلى معايير المهنية والسرية.
            </p>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-6 mb-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-1">15+</div>
                <div className="text-sm text-gray-600">سنة خبرة</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-1">1200+</div>
                <div className="text-sm text-gray-600">عميل راضٍ</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-1">98%</div>
                <div className="text-sm text-gray-600">نسبة النجاح</div>
              </div>
            </div>
          </div>

          {/* Visual */}
          <div className="order-1 lg:order-2">
            <div className="relative">
              {/* Main Image Placeholder */}
              <div className="relative bg-gradient-to-br from-blue-100 to-blue-50 rounded-3xl p-8 shadow-2xl">
                <div className="w-full h-96 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center">
                  <svg className="w-32 h-32 text-white/80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </div>

                {/* Floating Elements */}
                <div className="absolute -top-4 -right-4 bg-white rounded-2xl p-4 shadow-xl">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mr-3">
                      <CheckCircle className="w-6 h-6 text-green-600" />
                    </div>
                    <div>
                      <div className="text-lg font-bold text-gray-900">معتمد</div>
                      <div className="text-sm text-gray-600">ومرخص</div>
                    </div>
                  </div>
                </div>

                <div className="absolute -bottom-4 -left-4 bg-white rounded-2xl p-4 shadow-xl">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-amber-100 rounded-xl flex items-center justify-center mr-3">
                      <Award className="w-6 h-6 text-amber-600" />
                    </div>
                    <div>
                      <div className="text-lg font-bold text-gray-900">جوائز</div>
                      <div className="text-sm text-gray-600">وتقديرات</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <div key={index} className="text-center group">
                <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center text-white mb-6 mx-auto group-hover:scale-110 transition-transform duration-300">
                  <Icon className="w-10 h-10" />
                </div>
                <h4 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h4>
                <p className="text-gray-600 leading-relaxed">{feature.description}</p>
              </div>
            );
          })}
        </div>
        
        {/* Team Section */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center bg-blue-50 text-blue-600 text-sm font-semibold px-6 py-3 rounded-full mb-6">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            فريق الخبراء
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            <span className="block">نخبة من</span>
            <span className="block bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent">المحامين المتخصصين</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            فريق من أمهر المحامين والمستشارين القانونيين المتخصصين في مختلف المجالات القانونية
          </p>
          <div className="flex justify-center mt-8">
            <div className="w-24 h-1 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-full"></div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {team.map((member) => (
            <div key={member.id} className="group">
              <div className="bg-white rounded-3xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
                <div className="relative h-64 bg-gradient-to-br from-blue-100 to-blue-50">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                      <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                  </div>
                </div>
                <div className="p-6 text-center">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{member.name}</h3>
                  <p className="text-blue-600 font-semibold mb-3">{member.role}</p>
                  <p className="text-gray-600 text-sm leading-relaxed">{member.bio}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-16">
          <Button
            variant="outline"
            className="px-8 py-4 text-lg font-medium border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white rounded-xl transition-all duration-300"
          >
            تعرف على فريقنا كاملاً
          </Button>
        </div>
      </div>
    </section>
  );
}
