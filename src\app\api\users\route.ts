import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database-router'

// ضمان وجود جدول المستخدمين بالأعمدة المستخدمة في النظام
async function ensureUsersTable() {
  await query(`
    CREATE TABLE IF NOT EXISTS users (
      id SERIAL PRIMARY KEY,
      username VARCHAR(150) UNIQUE NOT NULL,
      employee_id INTEGER,
      role VARCHAR(100) DEFAULT 'user',
      user_type VARCHAR(50) DEFAULT 'user',
      permissions TEXT[] DEFAULT '{}',
      is_active BOOLEAN DEFAULT TRUE,
      status VARCHAR(50) DEFAULT 'active',
      password_hash TEXT,
      name VARCHAR(200),
      created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_date TIMESTAMP,
      last_login TIMESTAMP,
      last_logout TIMESTAMP,
      is_online BOOLEAN DEFAULT FALSE,
      login_attempts INTEGER DEFAULT 0
    )
  `)
}

// GET - جلب جميع المستخدمين من قاعدة البيانات
export async function GET() {
  try {
    await ensureUsersTable()
    const result = await query('SELECT * FROM users ORDER BY id')

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching المستخدمين:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'فشل في جلب بيانات المستخدمين',
        message: 'تأكد من وجود الجدول في قاعدة البيانات'
      },
      { status: 500 }
    )
  }
}

// POST - إضافة المستخدمين جديد
export async function POST(request: NextRequest) {
  try {
    await ensureUsersTable()
    const body = await request.json()
    console.log('📝 بيانات المستخدم الواردة:', body)

    const {
      username,
      password,
      employee_id,
      role = 'user',
      user_type = 'user',
      permissions = [],
      is_active = true,
      status = 'active',
      name,
    } = body || {}

    // التحقق من البيانات الأساسية
    if (!username || !role || !user_type) {
      return NextResponse.json(
        { success: false, error: 'الحقول (username, role, user_type) مطلوبة' },
        { status: 400 }
      )
    }

    // إنشاء hash لكلمة المرور إذا تم توفيرها
    let password_hash = null
    if (password) {
      try {
        // استخدام bcryptjs لتشفير كلمة المرور
        const bcrypt = require('bcryptjs')
        password_hash = await bcrypt.hash(password, 10)
        console.log('✅ تم تشفير كلمة المرور بنجاح')
      } catch (error) {
        console.error('❌ خطأ في تشفير كلمة المرور:', error)
        // في حالة فشل التشفير، استخدم كلمة المرور كما هي (مؤقتاً)
        password_hash = password
      }
    }

    // إذا كان المستخدم admin، منحه جميع الصلاحيات
    let finalPermissions = permissions
    if (user_type === 'admin' || role === 'admin') {
      finalPermissions = [
        // إدارة المستخدمين والأدوار
        'users:view', 'users:create', 'users:update', 'users:delete',
        'user_roles:view', 'user_roles:create', 'user_roles:update', 'user_roles:delete',
        'user_permissions:view', 'user_permissions:create', 'user_permissions:update', 'user_permissions:delete',

        // إدارة الهيكل التنظيمي
        'departments:view', 'departments:create', 'departments:update', 'departments:delete',
        'cost_centers:view', 'cost_centers:create', 'cost_centers:update', 'cost_centers:delete',
        'employees:view', 'employees:create', 'employees:update', 'employees:delete',

        // إدارة العملاء والقضايا
        'clients:view', 'clients:create', 'clients:update', 'clients:delete',
        'cases:view', 'cases:create', 'cases:update', 'cases:delete',
        'follows:view', 'follows:create', 'follows:update', 'follows:delete',
        'issues:view', 'issues:create', 'issues:update', 'issues:delete',

        // المحاسبة والمالية
        'accounting:view', 'accounting:create', 'accounting:update', 'accounting:delete',
        'chart_of_accounts:view', 'chart_of_accounts:create', 'chart_of_accounts:update', 'chart_of_accounts:delete',
        'vouchers:view', 'vouchers:create', 'vouchers:update', 'vouchers:delete',
        'receipts:view', 'receipts:create', 'receipts:update', 'receipts:delete',
        'payments:view', 'payments:create', 'payments:update', 'payments:delete',
        'invoices:view', 'invoices:create', 'invoices:update', 'invoices:delete',

        // التقارير والإحصائيات
        'reports:view', 'reports:create', 'reports:update', 'reports:delete',
        'statistics:view', 'statistics:create', 'statistics:update', 'statistics:delete',
        'analytics:view', 'analytics:create', 'analytics:update', 'analytics:delete',

        // إدارة النظام والإعدادات
        'settings:view', 'settings:create', 'settings:update', 'settings:delete',
        'system_settings:view', 'system_settings:create', 'system_settings:update', 'system_settings:delete',
        'server_management:view', 'server_management:create', 'server_management:update', 'server_management:delete',

        // إدارة المحتوى والواجهة
        'footer_links:view', 'footer_links:create', 'footer_links:update', 'footer_links:delete',
        'navigation_pages:view', 'navigation_pages:create', 'navigation_pages:update', 'navigation_pages:delete',
        'announcements:view', 'announcements:create', 'announcements:update', 'announcements:delete',
        'public_announcements:view', 'public_announcements:create', 'public_announcements:update', 'public_announcements:delete',

        // المكتبة القانونية والوثائق
        'legal_library:view', 'legal_library:create', 'legal_library:update', 'legal_library:delete',
        'documents:view', 'documents:create', 'documents:update', 'documents:delete',
        'templates:view', 'templates:create', 'templates:update', 'templates:delete',

        // الذكاء الاصطناعي والأتمتة
        'ai_settings:view', 'ai_settings:create', 'ai_settings:update', 'ai_settings:delete',
        'auto_reply:view', 'auto_reply:create', 'auto_reply:update', 'auto_reply:delete',

        // النسخ الاحتياطي والأمان
        'backup:view', 'backup:create', 'backup:update', 'backup:delete',
        'security:view', 'security:create', 'security:update', 'security:delete',
        'audit_logs:view', 'audit_logs:create', 'audit_logs:update', 'audit_logs:delete',

        // صلاحيات خاصة للمدير
        'admin:full_access',
        'admin:manage_all',
        'admin:system_control',
        'admin:user_management',
        'admin:financial_control',
        'admin:reports_access',
        'admin:settings_control'
      ]

      console.log(`✅ تم منح المستخدم ${username} جميع صلاحيات المدير (${finalPermissions.length} صلاحية)`)
    }

    // الإدراج في جدول المستخدمين (يدعم المخطط الموسّع إن كان موجودًا)
    const insertColumns = [
      'username',
      'employee_id',
      'role',
      'user_type',
      'permissions',
      'is_active',
      'status',
      'password_hash',
      'name',
    ]
    const insertValues = [
      username,
      employee_id ?? null,
      role,
      user_type,
      finalPermissions,
      is_active,
      status,
      password_hash,
      name ?? null,
    ]

    // صياغة استعلام الإدراج بشكل آمن مع RETURNING
    const placeholders = insertValues.map((_, i) => `$${i + 1}`).join(', ')
    const result = await query(
      `INSERT INTO users (${insertColumns.join(', ')})
       VALUES (${placeholders})
       RETURNING id, username, employee_id, role, user_type, permissions, is_active, status, created_date`,
      insertValues
    )

    return NextResponse.json({
      success: true,
      message: 'تم إضافة المستخدم بنجاح',
      data: result.rows[0]
    })
  } catch (error: any) {
    console.error('❌ Error creating المستخدم:', error)
    console.error('❌ Error details:', {
      message: error?.message,
      code: error?.code,
      detail: error?.detail,
      stack: error?.stack
    })

    const msg = error?.message || ''

    // معالجة أخطاء مختلفة
    if (msg.includes('duplicate key value violates unique constraint')) {
      return NextResponse.json(
        { success: false, error: 'اسم المستخدم موجود بالفعل، يرجى اختيار اسم آخر' },
        { status: 400 }
      )
    }

    if (msg.includes('column') && msg.includes('does not exist')) {
      return NextResponse.json(
        { success: false, error: 'مخطط جدول المستخدمين لا يحتوي كل الأعمدة المتوقعة. يرجى تحديث المهاجرات أو إرسال حقول مطابقة.' },
        { status: 500 }
      )
    }

    if (msg.includes('connection') || msg.includes('database')) {
      return NextResponse.json(
        { success: false, error: 'خطأ في الاتصال بقاعدة البيانات' },
        { status: 500 }
      )
    }

    return NextResponse.json(
      {
        success: false,
        error: 'فشل في إضافة المستخدم',
        details: process.env.NODE_ENV === 'development' ? error?.message : undefined
      },
      { status: 500 }
    )
  }
}

// PUT - تحديث المستخدمين
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()

    // هنا يجب إضافة منطق التحديث حسب كل جدول
    // سيتم تحديثه لاحقاً حسب هيكل كل جدول

    return NextResponse.json({
      success: true,
      message: 'تم تحديث المستخدمين بنجاح'
    })
  } catch (error) {
    console.error('Error updating المستخدمين:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث المستخدمين' },
      { status: 500 }
    )
  }
}

// DELETE - حذف المستخدمين
export async function DELETE(request: NextRequest) {
  try {
    await ensureUsersTable()
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف المستخدمين مطلوب' },
        { status: 400 }
      )
    }

    await query('DELETE FROM users WHERE id = $1', [id])

    return NextResponse.json({
      success: true,
      message: 'تم حذف المستخدمين بنجاح'
    })
  } catch (error) {
    console.error('Error deleting المستخدمين:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف المستخدمين' },
      { status: 500 }
    )
  }
}