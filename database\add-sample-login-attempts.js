/**
 * إضافة بيانات تجريبية لمحاولات تسجيل الدخول
 * لاختبار صفحة إدارة الأجهزة
 */

const { Client } = require('pg');

// تطبيق على قاعدتي البيانات
const databases = [
  {
    name: 'moham<PERSON>',
    config: {
      host: 'localhost',
      port: 5432,
      database: 'moham<PERSON>',
      user: 'postgres',
      password: 'yemen123'
    }
  },
  {
    name: 'rubaie',
    config: {
      host: 'localhost',
      port: 5432,
      database: 'rubaie',
      user: 'postgres',
      password: 'yemen123'
    }
  }
];

async function addSampleLoginAttempts(dbConfig, dbName) {
  const client = new Client(dbConfig);
  
  try {
    console.log(`📝 إضافة بيانات تجريبية لمحاولات الدخول في قاعدة ${dbName}...`);
    await client.connect();

    // جلب المستخدمين الموجودين
    const usersResult = await client.query(`
      SELECT id, username FROM users WHERE username != 'admin' LIMIT 5
    `);

    if (usersResult.rows.length === 0) {
      console.log('⚠️ لا يوجد مستخدمين لإضافة محاولات دخول لهم');
      return;
    }

    // بيانات تجريبية لمحاولات الدخول
    const sampleAttempts = [
      {
        username: 'testuser1',
        device_id: 'DEV_UNAUTHORIZED_123',
        device_info: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/120.0.0.0',
        ip_address: '***********00',
        user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/120.0.0.0',
        login_status: 'blocked',
        failure_reason: 'الجهاز غير مصرح به'
      },
      {
        username: 'testuser2',
        device_id: 'DEV_UNKNOWN_456',
        device_info: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) Safari/537.36',
        ip_address: '***********01',
        user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) Safari/537.36',
        login_status: 'blocked',
        failure_reason: 'لم يتم تسجيل أي جهاز لهذا المستخدم'
      },
      {
        username: 'employee1',
        device_id: 'DEV_SUSPICIOUS_789',
        device_info: 'Mozilla/5.0 (X11; Linux x86_64) Firefox/121.0',
        ip_address: '*********',
        user_agent: 'Mozilla/5.0 (X11; Linux x86_64) Firefox/121.0',
        login_status: 'blocked',
        failure_reason: 'الجهاز غير مصرح به'
      },
      {
        username: 'secretary1',
        device_id: 'DEV_MOBILE_ABC',
        device_info: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) Safari/604.1',
        ip_address: '***********02',
        user_agent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) Safari/604.1',
        login_status: 'blocked',
        failure_reason: 'الجهاز غير مصرح به'
      },
      {
        username: 'accountant1',
        device_id: 'DEV_TABLET_XYZ',
        device_info: 'Mozilla/5.0 (iPad; CPU OS 17_0 like Mac OS X) Safari/604.1',
        ip_address: '***********03',
        user_agent: 'Mozilla/5.0 (iPad; CPU OS 17_0 like Mac OS X) Safari/604.1',
        login_status: 'blocked',
        failure_reason: 'الجهاز غير مصرح به'
      }
    ];

    // إضافة محاولات الدخول التجريبية
    for (const attempt of sampleAttempts) {
      // البحث عن المستخدم أو استخدام مستخدم موجود
      let userId = null;
      const userCheck = await client.query(`
        SELECT id FROM users WHERE username = $1
      `, [attempt.username]);

      if (userCheck.rows.length > 0) {
        userId = userCheck.rows[0].id;
      } else {
        // استخدام أول مستخدم متاح
        userId = usersResult.rows[0].id;
        attempt.username = usersResult.rows[0].username;
      }

      // إضافة محاولة الدخول
      await client.query(`
        INSERT INTO device_login_log (
          user_id, username, device_id, device_info, ip_address, 
          user_agent, login_status, failure_reason, login_time
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      `, [
        userId,
        attempt.username,
        attempt.device_id,
        attempt.device_info,
        attempt.ip_address,
        attempt.user_agent,
        attempt.login_status,
        attempt.failure_reason,
        new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000) // خلال آخر 7 أيام
      ]);
    }

    // إضافة بعض المحاولات الناجحة أيضاً
    const successfulAttempts = [
      {
        username: 'admin',
        device_id: 'DEV_ADMIN_MAIN',
        device_info: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/120.0.0.0',
        ip_address: '***********',
        user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/120.0.0.0',
        login_status: 'success',
        failure_reason: null
      }
    ];

    for (const attempt of successfulAttempts) {
      const userCheck = await client.query(`
        SELECT id FROM users WHERE username = $1
      `, [attempt.username]);

      if (userCheck.rows.length > 0) {
        await client.query(`
          INSERT INTO device_login_log (
            user_id, username, device_id, device_info, ip_address, 
            user_agent, login_status, failure_reason, login_time
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        `, [
          userCheck.rows[0].id,
          attempt.username,
          attempt.device_id,
          attempt.device_info,
          attempt.ip_address,
          attempt.user_agent,
          attempt.login_status,
          attempt.failure_reason,
          new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000) // خلال آخر 24 ساعة
        ]);
      }
    }

    // عرض الإحصائيات
    const statsResult = await client.query(`
      SELECT 
        login_status,
        COUNT(*) as count
      FROM device_login_log 
      WHERE login_time >= NOW() - INTERVAL '7 days'
      GROUP BY login_status
      ORDER BY login_status
    `);

    console.log(`✅ تم إضافة بيانات تجريبية لقاعدة ${dbName}`);
    console.log('📊 إحصائيات محاولات الدخول:');
    statsResult.rows.forEach(row => {
      console.log(`   - ${row.login_status}: ${row.count} محاولة`);
    });

  } catch (error) {
    console.error(`❌ خطأ في إضافة البيانات التجريبية لقاعدة ${dbName}:`, error);
    throw error;
  } finally {
    await client.end();
  }
}

// دالة رئيسية لتطبيق البيانات على جميع قواعد البيانات
async function addSampleData() {
  console.log('🚀 بدء إضافة بيانات تجريبية لمحاولات الدخول...\n');
  
  for (const db of databases) {
    try {
      await addSampleLoginAttempts(db.config, db.name);
    } catch (error) {
      console.error(`❌ فشل في إضافة البيانات لقاعدة ${db.name}:`, error.message);
    }
  }
  
  console.log('\n🎉 انتهى إضافة البيانات التجريبية!');
  console.log('\n💡 يمكنك الآن اختبار صفحة إدارة الأجهزة لرؤية محاولات الدخول المحظورة');
}

// تشغيل السكريبت
addSampleData().catch(console.error);
