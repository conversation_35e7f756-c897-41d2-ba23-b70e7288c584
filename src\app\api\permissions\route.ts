import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب جميع الصلاحيات
export async function GET() {
  try {
    console.log('🔄 API Permissions - بداية جلب الصلاحيات')

    // صلاحيات شاملة لجميع أجزاء النظام
    const allPermissions = [
      // إدارة المستخدمين
      { key: 'users:view', name: 'عرض المستخدمين', category: 'إدارة المستخدمين' },
      { key: 'users:create', name: 'إضافة مستخدم', category: 'إدارة المستخدمين' },
      { key: 'users:update', name: 'تعديل مستخدم', category: 'إدارة المستخدمين' },
      { key: 'users:delete', name: 'حذف مستخدم', category: 'إدارة المستخدمين' },
      { key: 'users:permissions', name: 'إدارة صلاحيات المستخدمين', category: 'إدارة المستخدمين' },

      // إدارة القضايا - صلاحيات تفصيلية
      { key: 'cases:view', name: 'عرض القضايا', category: 'إدارة القضايا' },
      { key: 'cases:create', name: 'إضافة قضية', category: 'إدارة القضايا' },
      { key: 'cases:update', name: 'تعديل قضية', category: 'إدارة القضايا' },
      { key: 'cases:delete', name: 'حذف قضية', category: 'إدارة القضايا' },
      { key: 'cases:print', name: 'طباعة تفاصيل القضية', category: 'إدارة القضايا' },
      { key: 'cases:assign', name: 'تعيين القضايا', category: 'إدارة القضايا' },
      { key: 'cases:status', name: 'تغيير حالة القضية', category: 'إدارة القضايا' },
      { key: 'cases:documents', name: 'إدارة مستندات القضية', category: 'إدارة القضايا' },
      { key: 'cases:sessions', name: 'إدارة جلسات القضية', category: 'إدارة القضايا' },
      { key: 'cases:fees', name: 'إدارة أتعاب القضية', category: 'إدارة القضايا' },

      // توزيع القضايا
      { key: 'case_distribution:view', name: 'عرض توزيع القضايا', category: 'توزيع القضايا' },
      { key: 'case_distribution:create', name: 'إنشاء توزيع قضايا', category: 'توزيع القضايا' },
      { key: 'case_distribution:update', name: 'تعديل توزيع القضايا', category: 'توزيع القضايا' },
      { key: 'case_distribution:delete', name: 'حذف توزيع القضايا', category: 'توزيع القضايا' },
      { key: 'case_distribution:print', name: 'طباعة توزيع القضايا', category: 'توزيع القضايا' },
      { key: 'case_distribution:assign', name: 'تعيين المحامين للقضايا', category: 'توزيع القضايا' },

      // المتابعات
      { key: 'followups:view', name: 'عرض المتابعات', category: 'المتابعات' },
      { key: 'followups:create', name: 'إضافة متابعة', category: 'المتابعات' },
      { key: 'followups:update', name: 'تعديل متابعة', category: 'المتابعات' },
      { key: 'followups:delete', name: 'حذف متابعة', category: 'المتابعات' },
      { key: 'followups:print', name: 'طباعة المتابعات', category: 'المتابعات' },
      { key: 'followups:assign', name: 'تعيين المتابعات', category: 'المتابعات' },
      { key: 'followups:status', name: 'تغيير حالة المتابعة', category: 'المتابعات' },

      // إدارة العملاء - صلاحيات تفصيلية
      { key: 'clients:view', name: 'عرض العملاء', category: 'إدارة العملاء' },
      { key: 'clients:create', name: 'إضافة عميل', category: 'إدارة العملاء' },
      { key: 'clients:update', name: 'تعديل عميل', category: 'إدارة العملاء' },
      { key: 'clients:delete', name: 'حذف عميل', category: 'إدارة العملاء' },
      { key: 'clients:print', name: 'طباعة بيانات العملاء', category: 'إدارة العملاء' },
      { key: 'clients:contact', name: 'التواصل مع العملاء', category: 'إدارة العملاء' },
      { key: 'clients:documents', name: 'إدارة مستندات العملاء', category: 'إدارة العملاء' },
      { key: 'clients:history', name: 'عرض تاريخ العميل', category: 'إدارة العملاء' },

      // إدارة الموظفين
      { key: 'employees:view', name: 'عرض الموظفين', category: 'إدارة الموظفين' },
      { key: 'employees:create', name: 'إضافة موظف', category: 'إدارة الموظفين' },
      { key: 'employees:update', name: 'تعديل موظف', category: 'إدارة الموظفين' },
      { key: 'employees:delete', name: 'حذف موظف', category: 'إدارة الموظفين' },
      { key: 'employees:print', name: 'طباعة بيانات الموظفين', category: 'إدارة الموظفين' },
      { key: 'employees:salary', name: 'إدارة رواتب الموظفين', category: 'إدارة الموظفين' },
      { key: 'employees:attendance', name: 'إدارة حضور الموظفين', category: 'إدارة الموظفين' },

      // المحاسبة - صلاحيات تفصيلية
      { key: 'accounting:view', name: 'عرض المحاسبة', category: 'المحاسبة' },
      { key: 'accounting:create', name: 'إضافة قيد محاسبي', category: 'المحاسبة' },
      { key: 'accounting:update', name: 'تعديل قيد محاسبي', category: 'المحاسبة' },
      { key: 'accounting:delete', name: 'حذف قيد محاسبي', category: 'المحاسبة' },
      { key: 'accounting:print', name: 'طباعة القيود المحاسبية', category: 'المحاسبة' },
      { key: 'accounting:approve', name: 'اعتماد القيود المحاسبية', category: 'المحاسبة' },
      { key: 'accounting:reports', name: 'تقارير محاسبية', category: 'المحاسبة' },

      // سندات القبض
      { key: 'receipts:view', name: 'عرض سندات القبض', category: 'سندات القبض' },
      { key: 'receipts:create', name: 'إضافة سند قبض', category: 'سندات القبض' },
      { key: 'receipts:update', name: 'تعديل سند قبض', category: 'سندات القبض' },
      { key: 'receipts:delete', name: 'حذف سند قبض', category: 'سندات القبض' },
      { key: 'receipts:print', name: 'طباعة سندات القبض', category: 'سندات القبض' },
      { key: 'receipts:approve', name: 'اعتماد سندات القبض', category: 'سندات القبض' },

      // سندات الصرف
      { key: 'payments:view', name: 'عرض سندات الصرف', category: 'سندات الصرف' },
      { key: 'payments:create', name: 'إضافة سند صرف', category: 'سندات الصرف' },
      { key: 'payments:update', name: 'تعديل سند صرف', category: 'سندات الصرف' },
      { key: 'payments:delete', name: 'حذف سند صرف', category: 'سندات الصرف' },
      { key: 'payments:print', name: 'طباعة سندات الصرف', category: 'سندات الصرف' },
      { key: 'payments:approve', name: 'اعتماد سندات الصرف', category: 'سندات الصرف' },

      // القيود المحاسبية
      { key: 'entries:view', name: 'عرض القيود المحاسبية', category: 'القيود المحاسبية' },
      { key: 'entries:create', name: 'إضافة قيد محاسبي', category: 'القيود المحاسبية' },
      { key: 'entries:update', name: 'تعديل قيد محاسبي', category: 'القيود المحاسبية' },
      { key: 'entries:delete', name: 'حذف قيد محاسبي', category: 'القيود المحاسبية' },
      { key: 'entries:print', name: 'طباعة القيود المحاسبية', category: 'القيود المحاسبية' },
      { key: 'entries:approve', name: 'اعتماد القيود المحاسبية', category: 'القيود المحاسبية' },

      // WhatsApp - صلاحيات تفصيلية
      { key: 'whatsapp:view', name: 'عرض إعدادات WhatsApp', category: 'WhatsApp' },
      { key: 'whatsapp:manage', name: 'إدارة اتصال WhatsApp', category: 'WhatsApp' },
      { key: 'whatsapp:send', name: 'إرسال رسائل WhatsApp', category: 'WhatsApp' },
      { key: 'whatsapp:templates', name: 'إدارة قوالب الرسائل', category: 'WhatsApp' },
      { key: 'whatsapp:monitor', name: 'مراقبة حالة WhatsApp', category: 'WhatsApp' },
      { key: 'whatsapp:settings', name: 'تعديل إعدادات WhatsApp', category: 'WhatsApp' },
      { key: 'whatsapp:contacts', name: 'إدارة جهات اتصال WhatsApp', category: 'WhatsApp' },
      { key: 'whatsapp:reminders', name: 'إدارة تذكيرات WhatsApp', category: 'WhatsApp' },
      { key: 'whatsapp:notifications', name: 'إدارة إشعارات WhatsApp', category: 'WhatsApp' },
      { key: 'whatsapp:logs', name: 'عرض سجلات WhatsApp', category: 'WhatsApp' },
      { key: 'whatsapp:statistics', name: 'عرض إحصائيات WhatsApp', category: 'WhatsApp' },

      // الإعدادات العامة
      { key: 'settings:view', name: 'عرض الإعدادات', category: 'الإعدادات' },
      { key: 'settings:update', name: 'تعديل الإعدادات', category: 'الإعدادات' },
      { key: 'settings:company', name: 'إعدادات الشركة', category: 'الإعدادات' },
      { key: 'settings:system', name: 'إعدادات النظام', category: 'الإعدادات' },
      { key: 'settings:security', name: 'إعدادات الأمان', category: 'الإعدادات' },

      // التقارير - صلاحيات تفصيلية
      { key: 'reports:view', name: 'عرض التقارير', category: 'التقارير' },
      { key: 'reports:create', name: 'إنشاء تقارير مخصصة', category: 'التقارير' },
      { key: 'reports:update', name: 'تعديل التقارير', category: 'التقارير' },
      { key: 'reports:delete', name: 'حذف التقارير', category: 'التقارير' },
      { key: 'reports:print', name: 'طباعة التقارير', category: 'التقارير' },
      { key: 'reports:export', name: 'تصدير التقارير', category: 'التقارير' },
      { key: 'reports:schedule', name: 'جدولة التقارير', category: 'التقارير' },
      { key: 'reports:financial', name: 'التقارير المالية', category: 'التقارير' },
      { key: 'reports:cases', name: 'تقارير القضايا', category: 'التقارير' },
      { key: 'reports:clients', name: 'تقارير العملاء', category: 'التقارير' },

      // الأقسام
      { key: 'departments:view', name: 'عرض الأقسام', category: 'إدارة الأقسام' },
      { key: 'departments:create', name: 'إضافة قسم', category: 'إدارة الأقسام' },
      { key: 'departments:update', name: 'تعديل قسم', category: 'إدارة الأقسام' },
      { key: 'departments:delete', name: 'حذف قسم', category: 'إدارة الأقسام' },

      // المكتبة القانونية - صلاحيات تفصيلية
      { key: 'library:view', name: 'عرض المكتبة القانونية', category: 'المكتبة القانونية' },
      { key: 'library:create', name: 'إضافة مستند قانوني', category: 'المكتبة القانونية' },
      { key: 'library:update', name: 'تعديل مستند قانوني', category: 'المكتبة القانونية' },
      { key: 'library:delete', name: 'حذف مستند قانوني', category: 'المكتبة القانونية' },
      { key: 'library:print', name: 'طباعة المستندات القانونية', category: 'المكتبة القانونية' },
      { key: 'library:download', name: 'تحميل المستندات', category: 'المكتبة القانونية' },
      { key: 'library:upload', name: 'رفع المستندات', category: 'المكتبة القانونية' },
      { key: 'library:categories', name: 'إدارة تصنيفات المكتبة', category: 'المكتبة القانونية' },

      // المواعيد والتقويم - صلاحيات تفصيلية
      { key: 'calendar:view', name: 'عرض التقويم', category: 'المواعيد والتقويم' },
      { key: 'calendar:create', name: 'إضافة موعد', category: 'المواعيد والتقويم' },
      { key: 'calendar:update', name: 'تعديل موعد', category: 'المواعيد والتقويم' },
      { key: 'calendar:delete', name: 'حذف موعد', category: 'المواعيد والتقويم' },
      { key: 'calendar:print', name: 'طباعة جدول المواعيد', category: 'المواعيد والتقويم' },
      { key: 'calendar:reminders', name: 'إدارة تذكيرات المواعيد', category: 'المواعيد والتقويم' },
      { key: 'calendar:share', name: 'مشاركة المواعيد', category: 'المواعيد والتقويم' },

      // المهام - صلاحيات تفصيلية
      { key: 'tasks:view', name: 'عرض المهام', category: 'إدارة المهام' },
      { key: 'tasks:create', name: 'إضافة مهمة', category: 'إدارة المهام' },
      { key: 'tasks:update', name: 'تعديل مهمة', category: 'إدارة المهام' },
      { key: 'tasks:delete', name: 'حذف مهمة', category: 'إدارة المهام' },
      { key: 'tasks:print', name: 'طباعة المهام', category: 'إدارة المهام' },
      { key: 'tasks:assign', name: 'تعيين المهام', category: 'إدارة المهام' },
      { key: 'tasks:status', name: 'تغيير حالة المهمة', category: 'إدارة المهام' },
      { key: 'tasks:priority', name: 'تحديد أولوية المهام', category: 'إدارة المهام' },

      // الإشعارات - صلاحيات تفصيلية
      { key: 'notifications:view', name: 'عرض الإشعارات', category: 'الإشعارات' },
      { key: 'notifications:create', name: 'إرسال إشعار', category: 'الإشعارات' },
      { key: 'notifications:update', name: 'تعديل الإشعارات', category: 'الإشعارات' },
      { key: 'notifications:delete', name: 'حذف الإشعارات', category: 'الإشعارات' },
      { key: 'notifications:manage', name: 'إدارة الإشعارات', category: 'الإشعارات' },
      { key: 'notifications:broadcast', name: 'إرسال إشعارات جماعية', category: 'الإشعارات' },

      // النسخ الاحتياطي - صلاحيات تفصيلية
      { key: 'backup:view', name: 'عرض النسخ الاحتياطية', category: 'النسخ الاحتياطي' },
      { key: 'backup:create', name: 'إنشاء نسخة احتياطية', category: 'النسخ الاحتياطي' },
      { key: 'backup:restore', name: 'استعادة نسخة احتياطية', category: 'النسخ الاحتياطي' },
      { key: 'backup:delete', name: 'حذف نسخة احتياطية', category: 'النسخ الاحتياطي' },
      { key: 'backup:schedule', name: 'جدولة النسخ الاحتياطية', category: 'النسخ الاحتياطي' },
      { key: 'backup:download', name: 'تحميل النسخ الاحتياطية', category: 'النسخ الاحتياطي' },

      // السجلات والمراجعة - صلاحيات تفصيلية
      { key: 'audit:view', name: 'عرض سجلات المراجعة', category: 'السجلات والمراجعة' },
      { key: 'audit:export', name: 'تصدير سجلات المراجعة', category: 'السجلات والمراجعة' },
      { key: 'audit:print', name: 'طباعة سجلات المراجعة', category: 'السجلات والمراجعة' },
      { key: 'audit:delete', name: 'حذف سجلات المراجعة', category: 'السجلات والمراجعة' },

      // الذكاء الاصطناعي - صلاحيات تفصيلية
      { key: 'ai:view', name: 'عرض إعدادات الذكاء الاصطناعي', category: 'الذكاء الاصطناعي' },
      { key: 'ai:manage', name: 'إدارة الذكاء الاصطناعي', category: 'الذكاء الاصطناعي' },
      { key: 'ai:chat', name: 'استخدام المحادثة الذكية', category: 'الذكاء الاصطناعي' },

      // صلاحيات النظام - تفصيلية
      { key: 'system_admin', name: 'مدير النظام', category: 'إدارة النظام' },
      { key: 'system_settings', name: 'إعدادات النظام', category: 'إدارة النظام' },
      { key: 'system_logs', name: 'سجلات النظام', category: 'إدارة النظام' },
      { key: 'system_maintenance', name: 'صيانة النظام', category: 'إدارة النظام' },
      { key: 'system_monitoring', name: 'مراقبة النظام', category: 'إدارة النظام' }
    ]

    // إدراج صلاحيات الإعدادات المفقودة (Idempotent Upsert)
    const settingsPermissions: Array<{key: string, name: string, category: string, description?: string}> = [
      // إدارة الأقسام (تم نقلها للقائمة الرئيسية)
      { key: 'departments:print', name: 'طباعة بيانات الأقسام', category: 'إدارة الأقسام' },

      // المحافظات
      { key: 'governorates:view', name: 'عرض المحافظات', category: 'الإعدادات' },
      { key: 'governorates:create', name: 'إضافة محافظة', category: 'الإعدادات' },
      { key: 'governorates:update', name: 'تعديل محافظة', category: 'الإعدادات' },
      { key: 'governorates:delete', name: 'حذف محافظة', category: 'الإعدادات' },

      // الفروع
      { key: 'branches:view', name: 'عرض الفروع', category: 'الإعدادات' },
      { key: 'branches:create', name: 'إضافة فرع', category: 'الإعدادات' },
      { key: 'branches:update', name: 'تعديل فرع', category: 'الإعدادات' },
      { key: 'branches:delete', name: 'حذف فرع', category: 'الإعدادات' },

      // المحاكم
      { key: 'courts:view', name: 'عرض المحاكم', category: 'الإعدادات' },
      { key: 'courts:create', name: 'إضافة محكمة', category: 'الإعدادات' },
      { key: 'courts:update', name: 'تعديل محكمة', category: 'الإعدادات' },
      { key: 'courts:delete', name: 'حذف محكمة', category: 'الإعدادات' },

      // مراكز التكلفة
      { key: 'cost_centers:view', name: 'عرض مراكز التكلفة', category: 'الإعدادات' },
      { key: 'cost_centers:create', name: 'إضافة مركز تكلفة', category: 'الإعدادات' },
      { key: 'cost_centers:update', name: 'تعديل مركز تكلفة', category: 'الإعدادات' },
      { key: 'cost_centers:delete', name: 'حذف مركز تكلفة', category: 'الإعدادات' },

      // الإعلانات
      { key: 'announcements:view', name: 'عرض الإعلانات', category: 'الإعدادات' },
      { key: 'announcements:create', name: 'إضافة إعلان', category: 'الإعدادات' },
      { key: 'announcements:update', name: 'تعديل إعلان', category: 'الإعدادات' },
      { key: 'announcements:delete', name: 'حذف إعلان', category: 'الإعدادات' },

      // بيانات الشركة
      { key: 'company:view', name: 'عرض بيانات الشركة', category: 'الإعدادات' },
      { key: 'company:update', name: 'تعديل بيانات الشركة', category: 'الإعدادات' },

      // WhatsApp
      { key: 'whatsapp:view', name: 'عرض إعدادات WhatsApp', category: 'WhatsApp' },
      { key: 'whatsapp:manage', name: 'إدارة WhatsApp', category: 'WhatsApp' },
      { key: 'whatsapp:send', name: 'إرسال رسائل WhatsApp', category: 'WhatsApp' },
      { key: 'whatsapp:templates', name: 'إدارة قوالب الرسائل', category: 'WhatsApp' },
      { key: 'whatsapp:monitor', name: 'مراقبة حالة WhatsApp', category: 'WhatsApp' },
    ]

    // تجميع الصلاحيات حسب الفئة
    const permissionsByCategory = allPermissions.reduce((acc: any, permission: any) => {
      if (!acc[permission.category]) {
        acc[permission.category] = []
      }
      acc[permission.category].push({
        key: permission.key,
        name: permission.name,
        description: permission.description || ''
      })
      return acc
    }, {})

    console.log(`📊 تم جلب ${allPermissions.length} صلاحية من ${Object.keys(permissionsByCategory).length} فئة`)

    return NextResponse.json({
      success: true,
      data: {
        permissions: allPermissions,
        permissionsByCategory
      }
    })
  } catch (error) {
    console.error('❌ خطأ في جلب الصلاحيات:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الصلاحيات' },
      { status: 500 }
    )
  }
}
