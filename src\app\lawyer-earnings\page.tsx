'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  DollarSign,
  TrendingUp,
  Search,
  Calendar,
  User,
  FileText
} from 'lucide-react'

interface LawyerEarning {
  id: number
  lawyer_id: number
  case_id: number
  service_id: number
  follow_id: number
  allocated_amount: number
  earned_amount: number
  earning_date: string
  notes: string
  case_number: string
  case_title: string
  service_name: string
  lawyer_name: string
}

export default function LawyerEarningsPage() {
  const [earnings, setEarnings] = useState<LawyerEarning[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [dbError, setDbError] = useState<string | null>(null)

  const fetchEarnings = async () => {
    setIsLoading(true)
    setDbError(null)

    try {
      const response = await fetch('/api/lawyer-earnings')
      const result = await response.json()

      if (result.success) {
        setEarnings(result.data)
      } else {
        setDbError(result.error || 'فشل في جلب بيانات الأرباح')
        setEarnings([])
      }
    } catch (error) {
      console.error('Network error:', error)
      setDbError('فشل في الاتصال بقاعدة البيانات')
      setEarnings([])
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchEarnings()
  }, [])

  const filteredEarnings = earnings.filter(earning =>
    (earning.case_number || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (earning.case_title || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (earning.lawyer_name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (earning.service_name || '').toLowerCase().includes(searchTerm.toLowerCase())
  )

  // الإحصائيات
  const stats = {
    total_earnings: filteredEarnings.reduce((sum, earning) => sum + earning.earned_amount, 0),
    total_allocated: filteredEarnings.reduce((sum, earning) => sum + earning.allocated_amount, 0),
    total_transactions: filteredEarnings.length,
    unique_cases: new Set(filteredEarnings.map(e => e.case_id)).size
  }

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">جاري تحميل بيانات الأرباح...</p>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <DollarSign className="h-8 w-8 mr-3 text-green-600" />
              أرباح المحامين
            </h1>
            <p className="text-gray-600 mt-1">تتبع الأرباح والمبالغ المكتسبة من الخدمات</p>
          </div>
        </div>

        {dbError && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center text-red-800">
                <FileText className="h-5 w-5 mr-2" />
                <span>{dbError}</span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* بطاقات الإحصائيات */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <DollarSign className="h-6 w-6 text-green-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">
                    {stats.total_earnings.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-600">إجمالي الأرباح</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <TrendingUp className="h-6 w-6 text-blue-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">
                    {stats.total_allocated.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-600">إجمالي المخصص</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <FileText className="h-6 w-6 text-purple-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.total_transactions}</div>
                  <div className="text-sm text-gray-600">المعاملات</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Calendar className="h-6 w-6 text-orange-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.unique_cases}</div>
                  <div className="text-sm text-gray-600">القضايا</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* البحث */}
        <Card>
          <CardContent className="p-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="البحث في الأرباح..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* جدول الأرباح */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <DollarSign className="h-5 w-5 mr-2" />
              سجل الأرباح ({filteredEarnings.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="text-right p-3 font-semibold">التاريخ</th>
                    <th className="text-right p-3 font-semibold">المحامي</th>
                    <th className="text-right p-3 font-semibold">القضية</th>
                    <th className="text-right p-3 font-semibold">الخدمة</th>
                    <th className="text-center p-3 font-semibold">المبلغ المخصص</th>
                    <th className="text-center p-3 font-semibold">المبلغ المكتسب</th>
                    <th className="text-right p-3 font-semibold">الملاحظات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredEarnings.map((earning) => (
                    <tr key={earning.id} className="border-b hover:bg-gray-50">
                      <td className="p-3">
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                          {new Date(earning.earning_date).toLocaleDateString('ar-SA')}
                        </div>
                      </td>
                      <td className="p-3">
                        <div className="flex items-center">
                          <User className="h-4 w-4 mr-2 text-gray-400" />
                          {earning.lawyer_name || `محامي ${earning.lawyer_id}`}
                        </div>
                      </td>
                      <td className="p-3">
                        <div>
                          <div className="font-medium text-blue-600">{earning.case_number}</div>
                          <div className="text-sm text-gray-500">{earning.case_title}</div>
                        </div>
                      </td>
                      <td className="p-3">
                        <Badge variant="outline">{earning.service_name}</Badge>
                      </td>
                      <td className="text-center p-3">
                        <span className="font-medium text-blue-600">
                          {earning.allocated_amount.toLocaleString()} ريال
                        </span>
                      </td>
                      <td className="text-center p-3">
                        <span className="font-medium text-green-600">
                          {earning.earned_amount.toLocaleString()} ريال
                        </span>
                      </td>
                      <td className="p-3 max-w-xs truncate" title={earning.notes}>
                        {earning.notes || '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {filteredEarnings.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <DollarSign className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>لا توجد أرباح مسجلة</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
