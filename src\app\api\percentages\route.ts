import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب جميع النسب المالية
export async function GET() {
  try {
    const result = await query(`
      SELECT
        id,
        name,
        admin_percentage,
        commission_percentage,
        created_date
      FROM lineages
      ORDER BY created_date DESC
    `)

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching percentages:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات النسب المالية' },
      { status: 500 }
    )
  }
}

// POST - إضافة نسبة مالية جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      name,
      admin_percentage,
      commission_percentage
    } = body

    if (!name) {
      return NextResponse.json(
        { success: false, error: 'اسم النسبة مطلوب' },
        { status: 400 }
      )
    }

    if (admin_percentage === undefined || admin_percentage < 0 || admin_percentage > 100) {
      return NextResponse.json(
        { success: false, error: 'نسبة الإدارة يجب أن تكون بين 0% و 100%' },
        { status: 400 }
      )
    }

    if (commission_percentage === undefined || commission_percentage < 0 || commission_percentage > 100) {
      return NextResponse.json(
        { success: false, error: 'نسبة العمولة يجب أن تكون بين 0% و 100%' },
        { status: 400 }
      )
    }

    // إضافة النسبة الجديدة لقاعدة البيانات
    const result = await query(`
      INSERT INTO lineages (name, admin_percentage, commission_percentage, created_date)
      VALUES ($1, $2, $3, CURRENT_DATE)
      RETURNING id, name, admin_percentage, commission_percentage, created_date
    `, [name, Number(admin_percentage), Number(commission_percentage)])

    const newPercentage = result.rows[0]

    return NextResponse.json({
      success: true,
      message: 'تم إضافة النسبة المالية بنجاح',
      data: newPercentage
    })
  } catch (error) {
    console.error('Error creating percentage:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة النسبة المالية' },
      { status: 500 }
    )
  }
}

// PUT - تحديث نسبة مالية
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      name,
      admin_percentage,
      commission_percentage
    } = body

    if (!id || !name) {
      return NextResponse.json(
        { success: false, error: 'المعرف واسم النسبة مطلوبان' },
        { status: 400 }
      )
    }

    if (admin_percentage === undefined || admin_percentage < 0 || admin_percentage > 100) {
      return NextResponse.json(
        { success: false, error: 'نسبة الإدارة يجب أن تكون بين 0% و 100%' },
        { status: 400 }
      )
    }

    if (commission_percentage === undefined || commission_percentage < 0 || commission_percentage > 100) {
      return NextResponse.json(
        { success: false, error: 'نسبة العمولة يجب أن تكون بين 0% و 100%' },
        { status: 400 }
      )
    }

    // تحديث النسبة في قاعدة البيانات
    const result = await query(`
      UPDATE lineages
      SET name = $1, admin_percentage = $2, commission_percentage = $3
      WHERE id = $4
      RETURNING id, name, admin_percentage, commission_percentage, created_date
    `, [name, Number(admin_percentage), Number(commission_percentage), id])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'النسبة المالية غير موجودة' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم تحديث النسبة المالية بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error updating percentage:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث النسبة المالية' },
      { status: 500 }
    )
  }
}

// DELETE - حذف نسبة مالية
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف النسبة المالية مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من وجود النسبة قبل الحذف
    const checkResult = await query(`
      SELECT id FROM lineages WHERE id = $1
    `, [id])

    if (checkResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'النسبة المالية غير موجودة' },
        { status: 404 }
      )
    }

    // حذف النسبة من قاعدة البيانات
    await query(`
      DELETE FROM lineages WHERE id = $1
    `, [id])

    return NextResponse.json({
      success: true,
      message: 'تم حذف النسبة المالية بنجاح'
    })
  } catch (error) {
    console.error('Error deleting percentage:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف النسبة المالية' },
      { status: 500 }
    )
  }
}
