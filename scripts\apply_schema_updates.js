#!/usr/bin/env node
/**
 * Apply DB schema updates for mohammi: create/alter tables and add constraints and indexes
 * Safe to run multiple
 * 
  times (uses IF NOT EXISTS / checks catalog before adding constraints)
 */
const fs = require('fs');
const path = require('path');
const { Client } = require('pg');

function loadConfig() {
  const rc = JSON.parse(fs.readFileSync(path.join(process.cwd(), 'routing.config.json'), 'utf8'));
  const dc = rc.default_config || {};
  return {
    host: process.env.DB_HOST || dc.db_host || 'localhost',
    port: Number(process.env.DB_PORT || dc.db_port || 5432),
    user: process.env.DB_USER || dc.db_user || 'postgres',
    password: process.env.DB_PASSWORD || dc.db_password || 'postgres',
    database: process.env.DB_NAME || 'mohammi',
  };
}

async function run() {
  const conn = loadConfig();
  const client = new Client(conn);
  await client.connect();
  const exec = (sql, params=[]) => client.query(sql, params);

  try {
    // 0) company_info (used by various services/settings)
    await exec(`
      CREATE TABLE IF NOT EXISTS company_info (
        id SERIAL PRIMARY KEY,
        company_name TEXT NOT NULL DEFAULT 'نظام إدارة المحاماة',
        phone TEXT,
        email TEXT,
        logo_url TEXT,
        description TEXT,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      );
    `);
    // Ensure at least one row exists
    await exec(`
      INSERT INTO company_info (company_name)
      SELECT 'نظام إدارة المحاماة'
      WHERE NOT EXISTS (SELECT 1 FROM company_info);
    `);

    // 0.1) Ensure WhatsApp-related columns exist on company_info
    await exec(`
      ALTER TABLE company_info ADD COLUMN IF NOT EXISTS whatsapp_phone VARCHAR(20);
      ALTER TABLE company_info ADD COLUMN IF NOT EXISTS whatsapp_business_name VARCHAR(100);
      ALTER TABLE company_info ADD COLUMN IF NOT EXISTS whatsapp_enabled BOOLEAN DEFAULT false;
      ALTER TABLE company_info ADD COLUMN IF NOT EXISTS whatsapp_session_name VARCHAR(50);
      ALTER TABLE company_info ADD COLUMN IF NOT EXISTS whatsapp_auto_reply BOOLEAN DEFAULT true;
      ALTER TABLE company_info ADD COLUMN IF NOT EXISTS whatsapp_business_hours_start TIME DEFAULT '08:00';
      ALTER TABLE company_info ADD COLUMN IF NOT EXISTS whatsapp_business_hours_end TIME DEFAULT '17:00';
      ALTER TABLE company_info ADD COLUMN IF NOT EXISTS whatsapp_auto_reply_message TEXT DEFAULT 'مرحباً بك في {{company_name}}. سيتم الرد عليك خلال ساعات العمل.';
    `);

    // 1) follows
    await exec(`
      CREATE TABLE IF NOT EXISTS follows (
        id SERIAL PRIMARY KEY,
        case_id INTEGER NOT NULL,
        service_id INTEGER NOT NULL,
        user_id INTEGER NOT NULL,
        report TEXT,
        date_field DATE NOT NULL DEFAULT CURRENT_DATE,
        status TEXT NOT NULL DEFAULT 'pending',
        next_hearing_id INTEGER NULL,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      );
    `);

    // 2) follow_details
    await exec(`
      CREATE TABLE IF NOT EXISTS follow_details (
        id SERIAL PRIMARY KEY,
        follow_id INTEGER NOT NULL,
        client_amount NUMERIC(18,2) DEFAULT 0,
        employee_amount NUMERIC(18,2) DEFAULT 0,
        created_at TIMESTAMP DEFAULT NOW()
      );
    `);

    // 3) case_documents
    await exec(`
      CREATE TABLE IF NOT EXISTS case_documents (
        id SERIAL PRIMARY KEY,
        case_id INTEGER NOT NULL,
        follow_id INTEGER NULL,
        file_name TEXT NOT NULL,
        mime_type TEXT NOT NULL,
        size_bytes INTEGER NOT NULL,
        content BYTEA NOT NULL,
        uploaded_by INTEGER NULL,
        created_at TIMESTAMP DEFAULT NOW()
      );
    `);

    // Constraints helpers
    async function addConstraintIfMissing(table, cname, sql) {
      const res = await exec(
        `SELECT 1 FROM pg_constraint WHERE conname = $1`, [cname]
      );
      if (res.rowCount === 0) {
        await exec(`ALTER TABLE ${table} ADD CONSTRAINT ${cname} ${sql}`);
        console.log(`+ Added constraint ${cname} on ${table}`);
      }
    }

    // FKs for follows
    await addConstraintIfMissing('follows', 'follows_case_id_fkey', 'FOREIGN KEY (case_id) REFERENCES issues(id) ON DELETE CASCADE');
    await addConstraintIfMissing('follows', 'follows_service_id_fkey', 'FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE RESTRICT');
    await addConstraintIfMissing('follows', 'follows_user_id_fkey', 'FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT');
    await addConstraintIfMissing('follows', 'follows_next_hearing_id_fkey', 'FOREIGN KEY (next_hearing_id) REFERENCES hearings(id) ON DELETE SET NULL');

    // FKs for follow_details
    await addConstraintIfMissing('follow_details', 'follow_details_follow_id_fkey', 'FOREIGN KEY (follow_id) REFERENCES follows(id) ON DELETE CASCADE');

    // FKs for case_documents
    await addConstraintIfMissing('case_documents', 'case_documents_case_id_fkey', 'FOREIGN KEY (case_id) REFERENCES issues(id) ON DELETE CASCADE');
    await addConstraintIfMissing('case_documents', 'case_documents_follow_id_fkey', 'FOREIGN KEY (follow_id) REFERENCES follows(id) ON DELETE CASCADE');

    // Indexes
    const idx = async (name, sql) => {
      await exec(`CREATE INDEX IF NOT EXISTS ${name} ${sql}`);
      console.log(`+ Ensured index ${name}`);
    };

    await idx('idx_follows_case_id', 'ON follows(case_id)');
    await idx('idx_follows_service_id', 'ON follows(service_id)');
    await idx('idx_follows_user_id', 'ON follows(user_id)');
    await idx('idx_follows_status', 'ON follows(status)');
    await idx('idx_follows_date_field', 'ON follows(date_field DESC)');
    await idx('idx_follows_next_hearing_id', 'ON follows(next_hearing_id)');

    await idx('idx_follow_details_follow_id', 'ON follow_details(follow_id)');
    await idx('idx_follow_details_created_at', 'ON follow_details(created_at DESC)');

    await idx('idx_case_documents_case_id', 'ON case_documents(case_id)');
    await idx('idx_case_documents_follow_id', 'ON case_documents(follow_id)');
    await idx('idx_case_documents_created_at', 'ON case_documents(created_at DESC)');
    await idx('idx_case_documents_file_name', 'ON case_documents(file_name)');

    console.log('\nSchema updates applied successfully.');
  } catch (e) {
    console.error('Schema update failed:', e.message);
    process.exitCode = 1;
  } finally {
    await client.end().catch(()=>{});
  }
}

run();
