/**
 * خدمة WhatsApp المحلية لليمن
 * تستخدم whatsapp-web.js للإرسال المجاني عبر رقم محلي
 */

import { Client, LocalAuth, MessageMedia } from 'whatsapp-web.js'
import { query } from '@/lib/db'
import * as fs from 'fs'
import * as path from 'path'

// أنواع البيانات
interface YemenContact {
  id: number
  name: string
  phone: string
  formattedPhone: string
  type: 'client' | 'employee'
  isActive: boolean
}

interface NotificationMessage {
  contactId: number
  message: string
  imageUrl?: string
  documentUrl?: string
  type: 'text' | 'image' | 'document'
  priority: 'low' | 'normal' | 'high'
}

interface SendResult {
  success: boolean
  messageId?: string
  error?: string
  timestamp: Date
}

// فئة خدمة WhatsApp المحلية
export class YemenWhatsAppService {
  private client: Client | null = null
  private isReady: boolean = false
  private qrCode: string | null = null
  private connectionStatus: 'disconnected' | 'connecting' | 'connected' | 'ready' = 'disconnected'

  constructor() {
    this.initializeClient()
  }

  // تهيئة عميل WhatsApp
  private initializeClient(): void {
    try {
      this.client = new Client({
        authStrategy: new LocalAuth({
          name: 'yemen-law-office',
          dataPath: './whatsapp-session'
        }),
        puppeteer: {
          headless: true,
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--single-process',
            '--disable-gpu'
          ]
        }
      })

      this.setupEventHandlers()
    } catch (error) {
      console.error('خطأ في تهيئة عميل WhatsApp:', error)
    }
  }

  // إعداد معالجات الأحداث
  private setupEventHandlers(): void {
    if (!this.client) return

    // عند توليد QR Code
    this.client.on('qr', (qr) => {
      console.log('QR Code جديد تم توليده')
      this.qrCode = qr
      this.connectionStatus = 'connecting'
    })

    // عند الاتصال بنجاح
    this.client.on('ready', () => {
      console.log('✅ WhatsApp جاهز للاستخدام!')
      this.isReady = true
      this.connectionStatus = 'ready'
      this.qrCode = null
    })

    // عند قطع الاتصال
    this.client.on('disconnected', (reason) => {
      console.log('❌ تم قطع الاتصال:', reason)
      this.isReady = false
      this.connectionStatus = 'disconnected'
    })

    // عند استقبال رسالة
    this.client.on('message', async (message) => {
      await this.handleIncomingMessage(message)
    })

    // معالجة الأخطاء
    this.client.on('auth_failure', (message) => {
      console.error('❌ فشل المصادقة:', message)
      this.connectionStatus = 'disconnected'
    })
  }

  // بدء تشغيل الخدمة
  async start(): Promise<void> {
    try {
      if (!this.client) {
        this.initializeClient()
      }

      console.log('🔄 بدء تشغيل خدمة WhatsApp...')
      await this.client!.initialize()
    } catch (error) {
      console.error('خطأ في بدء تشغيل WhatsApp:', error)
      throw error
    }
  }

  // إيقاف الخدمة
  async stop(): Promise<void> {
    try {
      if (this.client) {
        await this.client.destroy()
        this.client = null
        this.isReady = false
        this.connectionStatus = 'disconnected'
      }
    } catch (error) {
      console.error('خطأ في إيقاف WhatsApp:', error)
    }
  }

  // الحصول على حالة الاتصال
  getStatus(): { isReady: boolean; status: string; qrCode: string | null } {
    return {
      isReady: this.isReady,
      status: this.connectionStatus,
      qrCode: this.qrCode
    }
  }

  // تنسيق رقم الهاتف اليمني
  private formatYemenPhone(phone: string): string {
    // إزالة المسافات والرموز
    let cleanPhone = phone.replace(/[\s\-\(\)]/g, '')
    
    // إزالة الصفر الأول إذا وجد
    if (cleanPhone.startsWith('0')) {
      cleanPhone = cleanPhone.substring(1)
    }
    
    // إضافة رمز اليمن إذا لم يكن موجود
    if (!cleanPhone.startsWith('967')) {
      cleanPhone = '967' + cleanPhone
    }
    
    // إضافة علامة + في البداية
    return '+' + cleanPhone
  }

  // إرسال رسالة نصية
  async sendTextMessage(contactPhone: string, message: string): Promise<SendResult> {
    try {
      if (!this.isReady || !this.client) {
        throw new Error('WhatsApp غير جاهز للاستخدام')
      }

      const formattedPhone = this.formatYemenPhone(contactPhone)
      const chatId = formattedPhone.replace('+', '') + '@c.us'

      // التحقق من وجود الرقم على WhatsApp
      const isRegistered = await this.client.isRegisteredUser(chatId)
      if (!isRegistered) {
        throw new Error('الرقم غير مسجل على WhatsApp')
      }

      // إرسال الرسالة
      const sentMessage = await this.client.sendMessage(chatId, message)

      // حفظ سجل الرسالة
      await this.saveMessageLog({
        phone: formattedPhone,
        message,
        type: 'text',
        status: 'sent',
        messageId: sentMessage.id.id
      })

      return {
        success: true,
        messageId: sentMessage.id.id,
        timestamp: new Date()
      }

    } catch (error) {
      console.error('خطأ في إرسال الرسالة:', error)
      
      await this.saveMessageLog({
        phone: contactPhone,
        message,
        type: 'text',
        status: 'failed',
        error: error instanceof Error ? error.message : 'خطأ غير معروف'
      })

      return {
        success: false,
        error: error instanceof Error ? error.message : 'خطأ غير معروف',
        timestamp: new Date()
      }
    }
  }

  // إرسال صورة (مثل صورة السند)
  async sendImageMessage(contactPhone: string, imagePath: string, caption?: string): Promise<SendResult> {
    try {
      if (!this.isReady || !this.client) {
        throw new Error('WhatsApp غير جاهز للاستخدام')
      }

      const formattedPhone = this.formatYemenPhone(contactPhone)
      const chatId = formattedPhone.replace('+', '') + '@c.us'

      // التحقق من وجود الملف
      if (!fs.existsSync(imagePath)) {
        throw new Error('ملف الصورة غير موجود')
      }

      // إنشاء كائن الوسائط
      const media = MessageMedia.fromFilePath(imagePath)
      
      // إرسال الصورة
      const sentMessage = await this.client.sendMessage(chatId, media, { caption })

      // حفظ سجل الرسالة
      await this.saveMessageLog({
        phone: formattedPhone,
        message: caption || 'صورة',
        type: 'image',
        status: 'sent',
        messageId: sentMessage.id.id,
        mediaPath: imagePath
      })

      return {
        success: true,
        messageId: sentMessage.id.id,
        timestamp: new Date()
      }

    } catch (error) {
      console.error('خطأ في إرسال الصورة:', error)
      
      await this.saveMessageLog({
        phone: contactPhone,
        message: caption || 'صورة',
        type: 'image',
        status: 'failed',
        error: error instanceof Error ? error.message : 'خطأ غير معروف',
        mediaPath: imagePath
      })

      return {
        success: false,
        error: error instanceof Error ? error.message : 'خطأ غير معروف',
        timestamp: new Date()
      }
    }
  }

  // إرسال مستند
  async sendDocumentMessage(contactPhone: string, documentPath: string, filename?: string): Promise<SendResult> {
    try {
      if (!this.isReady || !this.client) {
        throw new Error('WhatsApp غير جاهز للاستخدام')
      }

      const formattedPhone = this.formatYemenPhone(contactPhone)
      const chatId = formattedPhone.replace('+', '') + '@c.us'

      // التحقق من وجود الملف
      if (!fs.existsSync(documentPath)) {
        throw new Error('ملف المستند غير موجود')
      }

      // إنشاء كائن الوسائط
      const media = MessageMedia.fromFilePath(documentPath)
      media.filename = filename || path.basename(documentPath)
      
      // إرسال المستند
      const sentMessage = await this.client.sendMessage(chatId, media)

      // حفظ سجل الرسالة
      await this.saveMessageLog({
        phone: formattedPhone,
        message: `مستند: ${media.filename}`,
        type: 'document',
        status: 'sent',
        messageId: sentMessage.id.id,
        mediaPath: documentPath
      })

      return {
        success: true,
        messageId: sentMessage.id.id,
        timestamp: new Date()
      }

    } catch (error) {
      console.error('خطأ في إرسال المستند:', error)
      
      await this.saveMessageLog({
        phone: contactPhone,
        message: `مستند: ${filename || 'غير محدد'}`,
        type: 'document',
        status: 'failed',
        error: error instanceof Error ? error.message : 'خطأ غير معروف',
        mediaPath: documentPath
      })

      return {
        success: false,
        error: error instanceof Error ? error.message : 'خطأ غير معروف',
        timestamp: new Date()
      }
    }
  }

  // جلب جهات الاتصال من قاعدة البيانات
  async getContacts(type?: 'client' | 'employee'): Promise<YemenContact[]> {
    try {
      let query_text = `
        SELECT 
          c.id,
          c.name,
          c.phone,
          'client' as type,
          c.is_active
        FROM clients c
        WHERE c.phone IS NOT NULL AND c.phone != ''
      `
      
      if (type !== 'client') {
        query_text += `
          UNION ALL
          SELECT 
            e.id,
            e.name,
            e.phone,
            'employee' as type,
            e.is_active
          FROM employees e
          WHERE e.phone IS NOT NULL AND e.phone != ''
        `
      }

      if (type === 'employee') {
        query_text = `
          SELECT 
            e.id,
            e.name,
            e.phone,
            'employee' as type,
            e.is_active
          FROM employees e
          WHERE e.phone IS NOT NULL AND e.phone != ''
        `
      }

      query_text += ` ORDER BY name`

      const result = await query(query_text)
      
      return result.rows.map(row => ({
        id: row.id,
        name: row.name,
        phone: row.phone,
        formattedPhone: this.formatYemenPhone(row.phone),
        type: row.type,
        isActive: row.is_active
      }))

    } catch (error) {
      console.error('خطأ في جلب جهات الاتصال:', error)
      return []
    }
  }

  // حفظ سجل الرسائل
  private async saveMessageLog(logData: {
    phone: string
    message: string
    type: string
    status: string
    messageId?: string
    error?: string
    mediaPath?: string
  }): Promise<void> {
    try {
      await query(`
        INSERT INTO whatsapp_local_messages (
          phone_number, message_content, message_type, status,
          whatsapp_message_id, error_message, media_path, sent_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      `, [
        logData.phone,
        logData.message,
        logData.type,
        logData.status,
        logData.messageId || null,
        logData.error || null,
        logData.mediaPath || null,
        new Date()
      ])
    } catch (error) {
      console.error('خطأ في حفظ سجل الرسالة:', error)
    }
  }

  // معالجة الرسائل الواردة
  private async handleIncomingMessage(message: any): Promise<void> {
    try {
      const contact = await message.getContact()
      const chat = await message.getChat()
      
      console.log(`📨 رسالة واردة من ${contact.name || contact.number}: ${message.body}`)
      
      // حفظ الرسالة الواردة
      await query(`
        INSERT INTO whatsapp_incoming_messages (
          phone_number, sender_name, message_content, message_type, received_at
        ) VALUES ($1, $2, $3, $4, $5)
      `, [
        contact.number,
        contact.name || 'غير معروف',
        message.body,
        message.type,
        new Date()
      ])

      // يمكن إضافة ردود تلقائية هنا
      if (message.body.toLowerCase().includes('مساعدة')) {
        await message.reply('مرحباً بك في مكتب المحاماة. سيتم الرد عليك في أقرب وقت ممكن.')
      }

    } catch (error) {
      console.error('خطأ في معالجة الرسالة الواردة:', error)
    }
  }
}

// إنشاء مثيل واحد من الخدمة
export const yemenWhatsAppService = new YemenWhatsAppService()

// دوال مساعدة للاستخدام السريع
export async function sendYemenWhatsApp(phone: string, message: string): Promise<SendResult> {
  return await yemenWhatsAppService.sendTextMessage(phone, message)
}

export async function sendVoucherImage(phone: string, imagePath: string, voucherInfo: string): Promise<SendResult> {
  const caption = `🧾 ${voucherInfo}\n\nمكتب المحاماة\nشكراً لتعاملكم معنا`
  return await yemenWhatsAppService.sendImageMessage(phone, imagePath, caption)
}

export async function sendDocumentToClient(phone: string, documentPath: string, documentName: string): Promise<SendResult> {
  return await yemenWhatsAppService.sendDocumentMessage(phone, documentPath, documentName)
}
