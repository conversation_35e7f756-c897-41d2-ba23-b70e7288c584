import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع الفروع من قاعدة البيانات
export async function GET() {
  try {
    const result = await query(`
      SELECT 
        b.*, 
        g.name AS governorate_name
      FROM branches b
      LEFT JOIN governorates g ON b.governorate_id = g.id
      ORDER BY b.id
    `)
    
    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching الفروع:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'فشل في جلب بيانات الفروع',
        message: 'تأكد من وجود الجدول في قاعدة البيانات'
      },
      { status: 500 }
    )
  }
}

// POST - إضافة الفروع جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, governorate_id, address, phone, manager_name, is_active } = body

    if (!name || !governorate_id) {
      return NextResponse.json(
        { success: false, error: 'اسم الفرع والمحافظة مطلوبان' },
        { status: 400 }
      )
    }

    const result = await query(
      `INSERT INTO branches (name, governorate_id, address, phone, manager_name, is_active)
       VALUES ($1, $2, $3, $4, $5, $6)
       RETURNING *`,
      [name, Number(governorate_id), address || '', phone || '', manager_name || '', is_active !== false]
    )

    return NextResponse.json({
      success: true,
      message: 'تم إضافة الفرع بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error creating الفروع:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة الفروع' },
      { status: 500 }
    )
  }
}

// PUT - تحديث الفروع
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, name, governorate_id, address, phone, manager_name, is_active } = body

    if (!id || !name || !governorate_id) {
      return NextResponse.json(
        { success: false, error: 'المعرف واسم الفرع والمحافظة مطلوبة' },
        { status: 400 }
      )
    }

    const result = await query(
      `UPDATE branches
       SET name = $1,
           governorate_id = $2,
           address = $3,
           phone = $4,
           manager_name = $5,
           is_active = $6
       WHERE id = $7
       RETURNING *`,
      [name, Number(governorate_id), address || '', phone || '', manager_name || '', is_active !== false, Number(id)]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الفرع غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم تحديث بيانات الفرع بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error updating الفروع:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الفروع' },
      { status: 500 }
    )
  }
}

// DELETE - حذف الفروع
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الفروع مطلوب' },
        { status: 400 }
      )
    }

    await query('DELETE FROM branches WHERE id = $1', [id])

    return NextResponse.json({
      success: true,
      message: 'تم حذف الفروع بنجاح'
    })
  } catch (error) {
    console.error('Error deleting الفروع:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الفروع' },
      { status: 500 }
    )
  }
}