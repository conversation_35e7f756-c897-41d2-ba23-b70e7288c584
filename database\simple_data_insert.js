// إدراج بيانات بسيط ومباشر
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function insertSimpleData() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔄 جاري الاتصال بقاعدة البيانات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 1. إنشاء جدول account_linking_settings
    console.log('🔧 إنشاء جدول account_linking_settings...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS account_linking_settings (
        id SERIAL PRIMARY KEY,
        table_name VARCHAR(100) NOT NULL,
        account_field VARCHAR(100) NOT NULL,
        default_account_id INTEGER,
        is_active BOOLEAN DEFAULT true,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 2. إدراج إعدادات ربط الحسابات
    await client.query(`
      INSERT INTO account_linking_settings (table_name, account_field, default_account_id) 
      VALUES ('employees', 'account_id', 1) 
      ON CONFLICT DO NOTHING
    `);

    // 3. إدراج موظف
    console.log('🔄 إدراج موظف...');
    await client.query(`
      INSERT INTO employees (name, position, phone, email, salary, account_id) 
      VALUES ('أحمد محمد', 'محامي', '*********', '<EMAIL>', 150000, 1)
      ON CONFLICT DO NOTHING
    `);

    // 4. إدراج موكل
    console.log('🔄 إدراج موكل...');
    await client.query(`
      INSERT INTO clients (name, phone, email, address, id_number, client_type, account_id) 
      VALUES ('شركة الأمل', '*********', '<EMAIL>', 'صنعاء', '***********', 'company', 2)
      ON CONFLICT DO NOTHING
    `);

    // 5. إدراج قضية
    console.log('🔄 إدراج قضية...');
    await client.query(`
      INSERT INTO issues (case_number, title, description, status, client_id) 
      VALUES ('CASE-2024-001', 'قضية تجريبية', 'وصف القضية التجريبية', 'active', 1)
      ON CONFLICT DO NOTHING
    `);

    // 6. التحقق من النتائج
    console.log('🔄 التحقق من النتائج...');
    
    const userCount = await client.query('SELECT COUNT(*) FROM users');
    const empCount = await client.query('SELECT COUNT(*) FROM employees');
    const clientCount = await client.query('SELECT COUNT(*) FROM clients');
    const issueCount = await client.query('SELECT COUNT(*) FROM issues');
    const lineageCount = await client.query('SELECT COUNT(*) FROM lineages');
    const serviceCount = await client.query('SELECT COUNT(*) FROM services');

    console.log('📊 ملخص البيانات:');
    console.log(`   - المستخدمين: ${userCount.rows[0].count} سجل`);
    console.log(`   - الموظفين: ${empCount.rows[0].count} سجل`);
    console.log(`   - الموكلين: ${clientCount.rows[0].count} سجل`);
    console.log(`   - القضايا: ${issueCount.rows[0].count} سجل`);
    console.log(`   - النسب المالية: ${lineageCount.rows[0].count} سجل`);
    console.log(`   - الخدمات: ${serviceCount.rows[0].count} سجل`);

    console.log('✅ تم إدراج البيانات بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في إدراج البيانات:', error.message);
  } finally {
    await client.end();
    console.log('🔄 تم قطع الاتصال بقاعدة البيانات');
  }
}

insertSimpleData();
