import { NextRequest, NextResponse } from 'next/server'

// دالة لتحديد قاعدة البيانات حسب المنفذ
function getPortFromRequest(request: NextRequest): string {
  const host = request.headers.get('host') || 'localhost:7443'
  const port = host.split(':')[1] || '7443'
  return port
}

function getDatabaseByPort(port: string): string {
  const portToDb: { [key: string]: string } = {
    '7443': 'mohammi',
    '8914': 'rubaie'
  }
  return portToDb[port] || 'mohammi'
}

export async function GET(request: NextRequest) {
  try {
    // تحديد قاعدة البيانات حسب المنفذ
    const port = getPortFromRequest(request)
    const dbName = getDatabaseByPort(port)

    // استخدام اتصال مباشر بقاعدة البيانات المحددة
    const { Pool } = require('pg')
    const pool = new Pool({
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432'),
      database: dbName,
      user: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD,
    })

    try {
      // جلب إحصائيات العملاء
      const clientsResult = await pool.query('SELECT COUNT(*) as count FROM clients WHERE status = $1', ['active'])
      const clients = parseInt(clientsResult.rows[0]?.count || '0')

      // جلب إحصائيات القضايا
      const issuesResult = await pool.query('SELECT COUNT(*) as count FROM issues')
      const issues = parseInt(issuesResult.rows[0]?.count || '0')

      // جلب إحصائيات القضايا المكتملة
      const completedIssuesResult = await pool.query('SELECT COUNT(*) as count FROM issues WHERE status = $1', ['completed'])
      const completedIssues = parseInt(completedIssuesResult.rows[0]?.count || '0')

      // جلب إحصائيات القضايا الجديدة
      const newIssuesResult = await pool.query(`
        SELECT COUNT(*) as count FROM issues 
        WHERE status = $1 AND created_date >= CURRENT_DATE - INTERVAL '30 days'
      `, ['active'])
      const newIssues = parseInt(newIssuesResult.rows[0]?.count || '0')

      // جلب إحصائيات الموظفين
      const employeesResult = await pool.query('SELECT COUNT(*) as count FROM employees WHERE status = $1', ['active'])
      const employees = parseInt(employeesResult.rows[0]?.count || '0')

      // جلب إحصائيات المحاكم (من جدول courts إذا وجد)
      let courts = 0
      try {
        const courtsResult = await pool.query('SELECT COUNT(*) as count FROM courts')
        courts = parseInt(courtsResult.rows[0]?.count || '0')
      } catch (error) {
        // إذا لم يوجد جدول المحاكم، استخدم عدد افتراضي من القضايا
        const distinctCourtsResult = await pool.query('SELECT COUNT(DISTINCT court_name) as count FROM issues WHERE court_name IS NOT NULL')
        courts = parseInt(distinctCourtsResult.rows[0]?.count || '0')
      }

      // حساب معدل النجاح
      const successRate = issues > 0 ? Math.round((completedIssues / issues) * 100) : 0

      // جلب سنوات الخبرة من بيانات الشركة
      let experienceYears = 0
      try {
        const companyResult = await pool.query('SELECT established_date FROM companies WHERE id = 1')
        if (companyResult.rows.length > 0) {
          const establishedDate = new Date(companyResult.rows[0].established_date)
          const currentDate = new Date()
          experienceYears = currentDate.getFullYear() - establishedDate.getFullYear()
        }
      } catch (error) {
        console.error('خطأ في حساب سنوات الخبرة:', error)
      }

      // جلب عدد الوثائق القانونية
      let legalDocuments = 0
      try {
        const documentsResult = await pool.query('SELECT COUNT(*) as count FROM legal_library WHERE is_active = true')
        legalDocuments = parseInt(documentsResult.rows[0]?.count || '0')
      } catch (error) {
        console.error('خطأ في جلب عدد الوثائق القانونية:', error)
      }

      const stats = {
        clients,
        issues,
        employees,
        completedIssues,
        newIssues,
        courts,
        successRate,
        experienceYears,
        legalDocuments
      }

      await pool.end()

      return NextResponse.json({
        success: true,
        data: stats
      })

    } catch (dbError) {
      console.error('خطأ في قاعدة البيانات:', dbError)
      await pool.end()

      return NextResponse.json({
        success: false,
        error: 'خطأ في جلب الإحصائيات من قاعدة البيانات'
      }, { status: 500 })
    }

  } catch (error) {
    console.error('خطأ في جلب الإحصائيات:', error)
    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب الإحصائيات'
    }, { status: 500 })
  }
}
