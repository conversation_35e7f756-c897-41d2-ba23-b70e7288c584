// استعادة نظيفة لقاعدة البيانات
const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

async function cleanRestore() {
  console.log('🔄 بدء الاستعادة النظيفة...');
  
  try {
    // 1. قراءة ملف النسخة الاحتياطية
    const backupFile = path.join(__dirname, '..', 'mohammi.sql');
    let sqlContent = fs.readFileSync(backupFile, 'utf8');
    
    console.log('✅ تم قراءة ملف النسخة الاحتياطية');
    
    // 2. تنظيف المحتوى
    console.log('🧹 تنظيف محتوى الملف...');
    
    // إزالة الأوامر الإدارية
    const linesToRemove = [
      /^SET /,
      /^SELECT pg_catalog/,
      /^DROP DATABASE/,
      /^CREATE DATABASE/,
      /^ALTER DATABASE/,
      /^\\connect/,
      /^-- /,
      /^\/\*/,
      /^\s*$/,
      /TOC entry/,
      /Name: .+; Type: .+; Schema: .+; Owner:/,
      /^REVOKE/,
      /^GRANT/
    ];
    
    const lines = sqlContent.split('\n');
    const cleanLines = lines.filter(line => {
      return !linesToRemove.some(pattern => pattern.test(line.trim()));
    });
    
    const cleanContent = cleanLines.join('\n');
    
    // 3. استخراج أوامر CREATE TABLE
    console.log('📋 استخراج أوامر إنشاء الجداول...');
    
    const createTableRegex = /CREATE TABLE[^;]+;/gis;
    const createTables = cleanContent.match(createTableRegex) || [];
    
    console.log(`📊 تم العثور على ${createTables.length} جدول`);
    
    // 4. استخراج أوامر INSERT
    console.log('📋 استخراج أوامر إدراج البيانات...');
    
    const insertRegex = /INSERT INTO[^;]+;/gis;
    const insertStatements = cleanContent.match(insertRegex) || [];
    
    console.log(`📊 تم العثور على ${insertStatements.length} أمر إدراج`);
    
    // 5. الاتصال بقاعدة البيانات
    const adminClient = new Client({
      host: 'localhost',
      port: 5432,
      database: 'postgres',
      user: 'postgres',
      password: 'yemen123'
    });
    
    await adminClient.connect();
    console.log('✅ متصل بقاعدة postgres');
    
    // 6. إعادة إنشاء قاعدة البيانات
    try {
      await adminClient.query(`
        SELECT pg_terminate_backend(pid)
        FROM pg_stat_activity
        WHERE datname = 'mohammi' AND pid <> pg_backend_pid()
      `);
    } catch (error) {
      // تجاهل الأخطاء
    }
    
    try {
      await adminClient.query('DROP DATABASE IF EXISTS mohammi');
    } catch (error) {
      // تجاهل الأخطاء
    }
    
    await adminClient.query(`CREATE DATABASE mohammi WITH ENCODING = 'UTF8'`);
    console.log('✅ تم إنشاء قاعدة البيانات');
    
    await adminClient.end();
    
    // 7. الاتصال بقاعدة mohammi
    const client = new Client({
      host: 'localhost',
      port: 5432,
      database: 'mohammi',
      user: 'postgres',
      password: 'yemen123'
    });
    
    await client.connect();
    console.log('✅ متصل بقاعدة mohammi');
    
    // 8. إنشاء الجداول
    console.log('\n🏗️ إنشاء الجداول...');
    let tablesCreated = 0;
    
    for (let i = 0; i < createTables.length; i++) {
      const createTable = createTables[i].trim();
      
      try {
        await client.query(createTable);
        tablesCreated++;
        
        if (i % 10 === 0) {
          console.log(`📈 تم إنشاء ${i + 1}/${createTables.length} جدول`);
        }
      } catch (error) {
        console.log(`⚠️ خطأ في إنشاء جدول ${i + 1}: ${error.message.substring(0, 100)}`);
      }
    }
    
    console.log(`✅ تم إنشاء ${tablesCreated} جدول`);
    
    // 9. إدراج البيانات
    console.log('\n📥 إدراج البيانات...');
    let insertsSuccessful = 0;
    
    for (let i = 0; i < insertStatements.length; i++) {
      const insertStatement = insertStatements[i].trim();
      
      try {
        await client.query(insertStatement);
        insertsSuccessful++;
        
        if (i % 100 === 0) {
          console.log(`📈 تم إدراج ${i + 1}/${insertStatements.length} سجل`);
        }
      } catch (error) {
        // تجاهل أخطاء الإدراج المكررة
        if (!error.message.includes('duplicate key')) {
          console.log(`⚠️ خطأ في إدراج ${i + 1}: ${error.message.substring(0, 100)}`);
        }
      }
    }
    
    console.log(`✅ تم إدراج ${insertsSuccessful} سجل`);
    
    // 10. التحقق من النتائج
    console.log('\n🔍 التحقق من النتائج...');
    
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    console.log(`📊 عدد الجداول النهائي: ${tablesResult.rows.length}`);
    
    if (tablesResult.rows.length > 0) {
      console.log('📋 الجداول المستعادة:');
      tablesResult.rows.slice(0, 15).forEach((row, index) => {
        console.log(`   ${index + 1}. ${row.table_name}`);
      });
      
      if (tablesResult.rows.length > 15) {
        console.log(`   ... و ${tablesResult.rows.length - 15} جدول آخر`);
      }
    }
    
    // فحص الجداول المهمة
    const importantTables = ['companies', 'users', 'clients', 'issues', 'employees'];
    
    console.log('\n📊 فحص الجداول المهمة:');
    for (const tableName of importantTables) {
      try {
        const result = await client.query(`SELECT COUNT(*) as count FROM ${tableName}`);
        console.log(`   ✅ ${tableName}: ${result.rows[0].count} سجل`);
      } catch (error) {
        console.log(`   ❌ ${tableName}: غير موجود`);
      }
    }
    
    await client.end();
    
    if (tablesResult.rows.length > 0) {
      console.log('\n🎉 تمت الاستعادة بنجاح!');
      console.log('✅ قاعدة البيانات جاهزة للاستخدام');
      console.log('🔄 يمكنك الآن إعادة تشغيل النظام');
    } else {
      console.log('\n❌ فشلت الاستعادة - لم يتم إنشاء أي جداول');
    }
    
  } catch (error) {
    console.error('❌ خطأ في الاستعادة:', error.message);
  }
}

cleanRestore();
