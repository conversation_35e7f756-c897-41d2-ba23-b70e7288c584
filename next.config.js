/** @type {import('next').NextConfig} */
const nextConfig = {
  // تفعيل إخراج standalone لتقليص حجم الاعتمادات اللازمة للتشغيل
  output: 'standalone',
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },

  // إعدادات الخادم - متوافقة مع النظام الموحد
  serverRuntimeConfig: {
    // إعدادات قاعدة البيانات
    database: {
      charset: 'utf8mb4',
      collation: 'utf8mb4_unicode_ci'
    }
  },
  // إعدادات العامة
  publicRuntimeConfig: {
    // إعدادات الترميز
    charset: 'UTF-8'
  },
  // إعدادات webpack محسنة لتجنب مشاكل SSR
  webpack: (config, { isServer, dev }) => {
    // تجنب تحميل مكتبات Node.js في العميل
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        stream: false,
        url: false,
        zlib: false,
        http: false,
        https: false,
        assert: false,
        os: false,
        path: false,
        child_process: false,
        worker_threads: false,
      }
    }

    // تحسينات إضافية للإنتاج
    if (!dev && !isServer) {
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          ...config.optimization.splitChunks,
          cacheGroups: {
            ...config.optimization.splitChunks.cacheGroups,
            whatsapp: {
              test: /[\\/]node_modules[\\/](qrcode\.react|whatsapp-web\.js)[\\/]/,
              name: 'whatsapp',
              chunks: 'all',
              priority: 10,
            },
          },
        },
      }
    }

    return config
  },
  // إعدادات الحزم الخارجية - متوافقة مع Next.js 15
  serverExternalPackages: ['whatsapp-web.js', 'puppeteer'],
  poweredByHeader: false,
  // إعدادات الصور
  images: {
    domains: ['localhost'],
    unoptimized: true
  }
}

module.exports = nextConfig