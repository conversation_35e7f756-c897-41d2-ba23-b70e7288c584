'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import {
  Scale,
  Users,
  FileText,
  Shield,
  Building,
  Gavel,
  BookOpen,
  Award,
  Phone,
  Mail,
  MapPin,
  Star,
  TrendingUp,
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  MessageCircle,
  LogIn,
  Download,
  Home
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

interface Service {
  id: number
  title: string
  slug: string
  description: string
  content: string
  icon_name: string
  icon_color: string
  image_url?: string
  meta_title?: string
  meta_description?: string
}

interface CompanyData {
  id: number
  name: string
  logo_url?: string
  logo_image_url?: string
  phone?: string
  email?: string
  address?: string
  city?: string
  country?: string
  description?: string
}

// خريطة الأيقونات
const iconMap: { [key: string]: any } = {
  Scale,
  Users,
  FileText,
  Shield,
  Building,
  Gavel,
  BookO<PERSON>,
  Award,
  Phone,
  Mail,
  MapPin,
  Star,
  TrendingUp,
  CheckCircle,
  ArrowRight,
  MessageCircle,
  LogIn,
  Download
}

export default function ServicePage() {
  const params = useParams()
  const router = useRouter()
  const [service, setService] = useState<Service | null>(null)
  const [companyData, setCompanyData] = useState<CompanyData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (params.slug) {
      fetchData()
    }
  }, [params.slug])

  const fetchData = async () => {
    try {
      setLoading(true)
      setError(null)

      // جلب بيانات الخدمة
      const serviceResponse = await fetch(`/api/services/${params.slug}`)
      const serviceResult = await serviceResponse.json()
      
      if (!serviceResult.success) {
        setError('الخدمة غير موجودة')
        return
      }
      
      setService(serviceResult.data)

      // جلب بيانات الشركة
      const companyResponse = await fetch('/api/company')
      const companyResult = await companyResponse.json()
      if (companyResult.success && companyResult.data.length > 0) {
        setCompanyData(companyResult.data[0])
      }

    } catch (error) {
      console.error('Error fetching data:', error)
      setError('حدث خطأ في تحميل البيانات')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <div className="animate-pulse">
          <div className="h-16 bg-gray-200"></div>
          <div className="container mx-auto px-4 py-8">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
            <div className="space-y-4">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
              <div className="h-4 bg-gray-200 rounded w-4/6"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !service) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Scale className="h-12 w-12 text-red-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">الخدمة غير موجودة</h1>
          <p className="text-gray-600 mb-6">{error || 'لم يتم العثور على الخدمة المطلوبة'}</p>
          <Link href="/">
            <Button className="bg-blue-600 hover:bg-blue-700 text-white">
              <Home className="h-4 w-4 mr-2" />
              العودة للرئيسية
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  const IconComponent = iconMap[service.icon_name] || Scale

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm border-b sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              {companyData?.logo_image_url ? (
                <Image
                  src={companyData.logo_image_url}
                  alt="شعار الشركة"
                  width={60}
                  height={60}
                  className="rounded-lg mr-3 object-contain"
                />
              ) : companyData?.logo_url ? (
                <Image
                  src={companyData.logo_url}
                  alt="شعار الشركة"
                  width={60}
                  height={60}
                  className="rounded-lg mr-3 object-contain"
                />
              ) : (
                <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                  <Scale className="h-6 w-6 text-white" />
                </div>
              )}
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  {companyData?.name || 'مؤسسة المحاماة والاستشارات القانونية'}
                </h1>
                <p className="text-sm text-gray-600">Excellence in Legal Services</p>
              </div>
            </div>

            <nav className="hidden md:flex items-center space-x-8 space-x-reverse">
              <Link href="/" className="text-gray-700 hover:text-blue-600 font-medium">الرئيسية</Link>
              <Link href="/#services" className="text-gray-700 hover:text-blue-600 font-medium">خدماتنا</Link>
              <Link href="/#about" className="text-gray-700 hover:text-blue-600 font-medium">من نحن</Link>
              <Link href="/#library" className="text-gray-700 hover:text-blue-600 font-medium">المكتبة القانونية</Link>
            </nav>

            <Link href="/dashboard">
              <Button className="bg-green-600 hover:bg-green-700 text-white">
                <LogIn className="h-4 w-4 mr-2" />
                دخول النظام
              </Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Breadcrumb */}
      <div className="bg-gray-50 py-4">
        <div className="container mx-auto px-4">
          <nav className="flex items-center space-x-2 space-x-reverse text-sm">
            <Link href="/" className="text-green-600 hover:text-green-800">الرئيسية</Link>
            <ArrowLeft className="h-4 w-4 text-gray-400" />
            <Link href="/#services" className="text-green-600 hover:text-green-800">الخدمات</Link>
            <ArrowLeft className="h-4 w-4 text-gray-400" />
            <span className="text-gray-600">{service.title}</span>
          </nav>
        </div>
      </div>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center text-white">
            <div className="flex justify-center mb-6">
              <div 
                className="w-20 h-20 rounded-2xl flex items-center justify-center shadow-2xl"
                style={{ backgroundColor: service.icon_color }}
              >
                <IconComponent className="h-10 w-10 text-white" />
              </div>
            </div>
            
            <h1 className="text-4xl lg:text-5xl font-bold mb-6">{service.title}</h1>
            <p className="text-xl text-gray-300 leading-relaxed max-w-3xl mx-auto">
              {service.description}
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mt-8">
              <Button size="lg" className="bg-white text-gray-900 hover:bg-gray-100 px-8 py-4 text-lg font-semibold">
                <MessageCircle className="h-5 w-5 mr-2" />
                استشارة مجانية
              </Button>
              <Button size="lg" variant="outline" className="border-2 border-white/30 text-white hover:bg-white/10 backdrop-blur-sm px-8 py-4 text-lg font-semibold">
                <Phone className="h-5 w-5 mr-2" />
                {companyData?.phone || '+967-1-234567'}
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Content Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <Card className="shadow-xl border-0 bg-white rounded-2xl overflow-hidden">
              <CardContent className="p-8 lg:p-12">
                <div 
                  className="prose prose-lg max-w-none"
                  dangerouslySetInnerHTML={{ __html: service.content }}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="bg-gray-50 py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">هل تحتاج لاستشارة قانونية؟</h2>
            <p className="text-xl text-gray-600 mb-8">
              تواصل معنا الآن للحصول على استشارة مجانية من فريقنا المتخصص
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="bg-white p-6 rounded-xl shadow-lg">
                <Phone className="h-8 w-8 text-blue-600 mx-auto mb-4" />
                <h3 className="font-semibold text-gray-900 mb-2">اتصل بنا</h3>
                <p className="text-gray-600">{companyData?.phone || '+967-1-234567'}</p>
              </div>
              
              <div className="bg-white p-6 rounded-xl shadow-lg">
                <Mail className="h-8 w-8 text-blue-600 mx-auto mb-4" />
                <h3 className="font-semibold text-gray-900 mb-2">راسلنا</h3>
                <p className="text-gray-600">{companyData?.email || '<EMAIL>'}</p>
              </div>
              
              <div className="bg-white p-6 rounded-xl shadow-lg">
                <MapPin className="h-8 w-8 text-blue-600 mx-auto mb-4" />
                <h3 className="font-semibold text-gray-900 mb-2">زورنا</h3>
                <p className="text-gray-600">{companyData?.address || 'العنوان غير محدد'}</p>
              </div>
            </div>
            
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg font-semibold">
              <MessageCircle className="h-5 w-5 mr-2" />
              احجز استشارة مجانية
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="flex items-center justify-center mb-4">
              {companyData?.logo_image_url ? (
                <Image
                  src={companyData.logo_image_url}
                  alt="شعار الشركة"
                  width={40}
                  height={40}
                  className="rounded-lg mr-3 object-contain"
                />
              ) : companyData?.logo_url ? (
                <Image
                  src={companyData.logo_url}
                  alt="شعار الشركة"
                  width={40}
                  height={40}
                  className="rounded-lg mr-3 object-contain"
                />
              ) : (
                <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                  <Scale className="h-5 w-5 text-white" />
                </div>
              )}
              <h3 className="text-xl font-bold">
                {companyData?.name || 'مؤسسة المحاماة والاستشارات القانونية'}
              </h3>
            </div>
            <p className="text-gray-400 mb-4">
              &copy; {new Date().getFullYear()} {companyData?.name || 'مؤسسة المحاماة والاستشارات القانونية'}. جميع الحقوق محفوظة.
            </p>
            <Link href="/" className="text-blue-400 hover:text-blue-300">
              العودة للرئيسية
            </Link>
          </div>
        </div>
      </footer>
    </div>
  )
}
