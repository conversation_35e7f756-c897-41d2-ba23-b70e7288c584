/**
 * سكريبت لإعداد قاعدة البيانات mohammidev للتطوير
 * ينسخ البيانات الأساسية من قاعدة mohammi إلى mohammidev
 */

const { Client } = require('pg');

// إعدادات قاعدة البيانات الأصلية (mohammi)
const sourceDbConfig = {
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'mohammi'
};

// إعدادات قاعدة البيانات الجديدة (mohammidev)
const targetDbConfig = {
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'mohammidev'
};

async function setupMohammiDev() {
  let sourceClient = null;
  let targetClient = null;

  try {
    console.log('🚀 بدء إعداد قاعدة البيانات mohammidev...\n');

    // الاتصال بقاعدة البيانات الأصلية
    sourceClient = new Client(sourceDbConfig);
    await sourceClient.connect();
    console.log('✅ متصل بقاعدة البيانات الأصلية (mohammi)');

    // الاتصال بقاعدة البيانات الجديدة
    targetClient = new Client(targetDbConfig);
    await targetClient.connect();
    console.log('✅ متصل بقاعدة البيانات الجديدة (mohammidev)');

    // 1. إنشاء جدول الأقسام
    console.log('\n📋 إنشاء جدول الأقسام...');
    await targetClient.query(`
      CREATE TABLE IF NOT EXISTS departments (
        id SERIAL PRIMARY KEY,
        dept_code VARCHAR(50) UNIQUE,
        dept_name VARCHAR(200) NOT NULL,
        description TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_date TIMESTAMP
      )
    `);

    // 2. نسخ الأقسام من قاعدة البيانات الأصلية
    console.log('📋 نسخ الأقسام من قاعدة البيانات الأصلية...');
    try {
      const departments = await sourceClient.query('SELECT * FROM departments ORDER BY id');
      
      if (departments.rows.length > 0) {
        for (const dept of departments.rows) {
          await targetClient.query(`
            INSERT INTO departments (id, dept_code, dept_name, description, is_active, created_date, updated_date)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            ON CONFLICT (id) DO UPDATE SET
              dept_code = EXCLUDED.dept_code,
              dept_name = EXCLUDED.dept_name,
              description = EXCLUDED.description,
              is_active = EXCLUDED.is_active,
              updated_date = CURRENT_TIMESTAMP
          `, [
            dept.id,
            dept.dept_code,
            dept.dept_name || dept.name, // دعم التوافق العكسي
            dept.description,
            dept.is_active !== false,
            dept.created_date || new Date(),
            dept.updated_date
          ]);
        }
        console.log(`✅ تم نسخ ${departments.rows.length} قسم`);
      } else {
        console.log('⚠️ لا توجد أقسام في قاعدة البيانات الأصلية');
      }
    } catch (error) {
      console.log('⚠️ خطأ في نسخ الأقسام، سيتم إنشاء أقسام افتراضية');
    }

    // 3. إنشاء أقسام افتراضية إذا لم توجد
    const deptCount = await targetClient.query('SELECT COUNT(*) FROM departments');
    if (parseInt(deptCount.rows[0].count) === 0) {
      console.log('📋 إنشاء أقسام افتراضية...');
      await targetClient.query(`
        INSERT INTO departments (dept_code, dept_name, description, is_active) VALUES
        ('LEGAL', 'القسم القانوني', 'قسم الشؤون القانونية والاستشارات', true),
        ('ADMIN', 'الإدارة العامة', 'قسم الإدارة العامة والموارد البشرية', true),
        ('FINANCE', 'المالية والمحاسبة', 'قسم الشؤون المالية والمحاسبة', true),
        ('IT', 'تقنية المعلومات', 'قسم تقنية المعلومات والدعم التقني', true)
      `);
      console.log('✅ تم إنشاء 4 أقسام افتراضية');
    }

    // 4. إنشاء جداول أخرى مطلوبة
    console.log('\n🏢 إنشاء الجداول الأساسية الأخرى...');

    // جدول الشركات
    await targetClient.query(`
      CREATE TABLE IF NOT EXISTS companies (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        legal_name VARCHAR(255),
        phone VARCHAR(50),
        email VARCHAR(100),
        address TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // جدول المحافظات
    await targetClient.query(`
      CREATE TABLE IF NOT EXISTS governorates (
        id SERIAL PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        code VARCHAR(10),
        is_active BOOLEAN DEFAULT TRUE
      )
    `);

    // جدول الفروع
    await targetClient.query(`
      CREATE TABLE IF NOT EXISTS branches (
        id SERIAL PRIMARY KEY,
        name VARCHAR(200) NOT NULL,
        address TEXT,
        phone VARCHAR(50),
        is_active BOOLEAN DEFAULT TRUE
      )
    `);

    // جدول المحاكم
    await targetClient.query(`
      CREATE TABLE IF NOT EXISTS courts (
        id SERIAL PRIMARY KEY,
        name VARCHAR(200) NOT NULL,
        type VARCHAR(100),
        location VARCHAR(200),
        address TEXT,
        phone VARCHAR(50),
        email VARCHAR(100),
        governorate_id INTEGER,
        is_active BOOLEAN DEFAULT TRUE,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 5. إدراج بيانات أساسية
    console.log('📊 إدراج البيانات الأساسية...');

    // شركة افتراضية
    await targetClient.query(`
      INSERT INTO companies (name, legal_name, phone, email, address, is_active)
      VALUES ('نظام إدارة المحاماة - محمد (التطوير)', 'مكتب محمد للمحاماة والاستشارات القانونية', '+967771234567', '<EMAIL>', 'صنعاء، اليمن', true)
      ON CONFLICT DO NOTHING
    `);

    // محافظات أساسية
    await targetClient.query(`
      INSERT INTO governorates (name, code, is_active) VALUES
      ('صنعاء', 'SA', true),
      ('عدن', 'AD', true),
      ('تعز', 'TA', true),
      ('الحديدة', 'HD', true)
      ON CONFLICT DO NOTHING
    `);

    // فرع افتراضي
    await targetClient.query(`
      INSERT INTO branches (name, address, phone, is_active)
      VALUES ('الفرع الرئيسي', 'صنعاء - شارع الستين', '+967-1-111111', true)
      ON CONFLICT DO NOTHING
    `);

    // محاكم أساسية
    await targetClient.query(`
      INSERT INTO courts (name, type, location, address, phone, governorate_id, is_active) VALUES
      ('المحكمة الابتدائية بصنعاء', 'ابتدائية', 'صنعاء', 'صنعاء - شارع الستين', '+967-1-111111', 1, true),
      ('محكمة الاستئناف بصنعاء', 'استئناف', 'صنعاء', 'صنعاء - شارع الزبيري', '+967-1-222222', 1, true)
      ON CONFLICT DO NOTHING
    `);

    // 6. التحقق من البيانات
    console.log('\n🔍 التحقق من البيانات المُدرجة...');
    
    const checks = [
      { table: 'departments', name: 'الأقسام' },
      { table: 'companies', name: 'الشركات' },
      { table: 'governorates', name: 'المحافظات' },
      { table: 'branches', name: 'الفروع' },
      { table: 'courts', name: 'المحاكم' }
    ];
    
    for (const check of checks) {
      try {
        const result = await targetClient.query(`SELECT COUNT(*) as count FROM ${check.table}`);
        console.log(`   ✅ ${check.name}: ${result.rows[0].count} سجل`);
      } catch (error) {
        console.log(`   ❌ ${check.name}: خطأ - ${error.message}`);
      }
    }

    console.log('\n🎉 تم إعداد قاعدة البيانات mohammidev بنجاح!');
    console.log('💡 يمكنك الآن استخدام المنفذ 3300 لإضافة الموظفين');

  } catch (error) {
    console.error('❌ خطأ في إعداد قاعدة البيانات:', error.message);
    throw error;
  } finally {
    if (sourceClient) await sourceClient.end();
    if (targetClient) await targetClient.end();
  }
}

// تشغيل السكريبت
if (require.main === module) {
  setupMohammiDev().catch(console.error);
}

module.exports = { setupMohammiDev };
