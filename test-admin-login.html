<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل دخول المدير</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 اختبار تسجيل دخول المدير</h1>
        
        <div class="form-group">
            <label>اختر مستخدم admin:</label>
            <select id="adminUser">
                <option value="admin_departments:123456">admin_departments</option>
                <option value="testuser4:123456">testuser4</option>
                <option value="admin_test2:123456">admin_test2</option>
            </select>
        </div>
        
        <button onclick="loginAsAdmin()">تسجيل دخول كمدير</button>
        <button onclick="checkPermissions()">فحص الصلاحيات</button>
        <button onclick="testDepartmentsAPI()">اختبار API الأقسام</button>
        <button onclick="openDepartmentsPage()">فتح صفحة الأقسام</button>
        
        <div id="result"></div>
    </div>

    <script>
        function showResult(message, isError = false) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${isError ? 'error' : 'success'}`;
            resultDiv.textContent = message;
        }

        async function loginAsAdmin() {
            try {
                const selectedUser = document.getElementById('adminUser').value;
                const [username, password] = selectedUser.split(':');
                
                showResult('جاري تسجيل الدخول...');
                
                const response = await fetch('/api/auth/users', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    // حفظ بيانات المستخدم في localStorage
                    localStorage.setItem('userSession', JSON.stringify(result.user));
                    
                    showResult(`✅ تم تسجيل الدخول بنجاح!
المستخدم: ${result.user.username}
النوع: ${result.user.user_type}
الدور: ${result.user.role}
عدد الصلاحيات: ${result.user.permissions?.length || 0}`);
                } else {
                    showResult(`❌ فشل تسجيل الدخول: ${result.error}`, true);
                }
            } catch (error) {
                showResult(`❌ خطأ في تسجيل الدخول: ${error.message}`, true);
            }
        }

        function checkPermissions() {
            try {
                const userSession = localStorage.getItem('userSession');
                if (!userSession) {
                    showResult('❌ لم يتم تسجيل الدخول', true);
                    return;
                }
                
                const user = JSON.parse(userSession);
                
                const hasDeptsView = user.permissions?.includes('departments:view');
                const isAdmin = user.user_type === 'admin' || user.role === 'admin';
                
                showResult(`📊 فحص الصلاحيات:
المستخدم: ${user.username}
النوع: ${user.user_type}
الدور: ${user.role}
هل هو مدير؟ ${isAdmin ? 'نعم' : 'لا'}
صلاحية عرض الأقسام: ${hasDeptsView ? 'نعم' : 'لا'}
إجمالي الصلاحيات: ${user.permissions?.length || 0}

أول 10 صلاحيات:
${user.permissions?.slice(0, 10).join('\n') || 'لا توجد صلاحيات'}`);
            } catch (error) {
                showResult(`❌ خطأ في فحص الصلاحيات: ${error.message}`, true);
            }
        }

        async function testDepartmentsAPI() {
            try {
                showResult('جاري اختبار API الأقسام...');
                
                const response = await fetch('/api/departments');
                const result = await response.json();
                
                if (result.success) {
                    showResult(`✅ API الأقسام يعمل بنجاح!
عدد الأقسام: ${result.departments?.length || 0}

الأقسام:
${result.departments?.map(d => `- ${d.dept_name} (${d.dept_code})`).join('\n') || 'لا توجد أقسام'}`);
                } else {
                    showResult(`❌ خطأ في API الأقسام: ${result.error}`, true);
                }
            } catch (error) {
                showResult(`❌ خطأ في اختبار API: ${error.message}`, true);
            }
        }

        function openDepartmentsPage() {
            window.open('/settings/departments', '_blank');
        }

        // فحص حالة تسجيل الدخول عند تحميل الصفحة
        window.onload = function() {
            const userSession = localStorage.getItem('userSession');
            if (userSession) {
                const user = JSON.parse(userSession);
                showResult(`🔍 مستخدم مسجل دخول حالياً:
المستخدم: ${user.username}
النوع: ${user.user_type}
الدور: ${user.role}`);
            } else {
                showResult('لم يتم تسجيل الدخول بعد');
            }
        };
    </script>
</body>
</html>
