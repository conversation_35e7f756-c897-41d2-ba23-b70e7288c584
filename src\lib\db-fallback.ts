import fs from 'fs'
import path from 'path'

// JSON Database fallback when PostgreSQL is not available
const dbPath = path.join(process.cwd(), 'database', 'case_movements.json')

// Initialize database if it doesn't exist
function initializeDatabase() {
  const dbDir = path.dirname(dbPath)
  if (!fs.existsSync(dbDir)) {
    fs.mkdirSync(dbDir, { recursive: true })
  }
  
  if (!fs.existsSync(dbPath)) {
    console.log('⚠️ PostgreSQL not available, creating local JSON database...')
    
    const initialData = {
      case_movements: [
        {
          id: 1,
          case_id: 1,
          case_number: '2024/001',
          case_title: 'قضية تجارية - شركة الأمل',
          movement_type: 'case_created',
          description: 'تم إنشاء القضية في النظام',
          created_by_name: 'النظام',
          priority: 'normal',
          client_name: 'شركة الأمل للتجارة',
          case_status: 'نشطة',
          created_at: new Date().toISOString(),
          movement_date: new Date().toISOString().split('T')[0]
        },
        {
          id: 2,
          case_id: 1,
          case_number: '2024/001',
          case_title: 'قضية تجارية - شركة الأمل',
          movement_type: 'case_assigned',
          description: 'تم توزيع القضية على المحامي أحمد محمد',
          created_by_name: 'المدير العام',
          priority: 'high',
          client_name: 'شركة الأمل للتجارة',
          case_status: 'موزعة',
          created_at: new Date(Date.now() - 86400000).toISOString(),
          movement_date: new Date(Date.now() - 86400000).toISOString().split('T')[0]
        },
        {
          id: 3,
          case_id: 2,
          case_number: '2024/002',
          case_title: 'قضية عمالية - محمد علي',
          movement_type: 'case_created',
          description: 'تم إنشاء القضية في النظام',
          created_by_name: 'النظام',
          priority: 'normal',
          client_name: 'محمد علي أحمد',
          case_status: 'نشطة',
          created_at: new Date(Date.now() - 172800000).toISOString(),
          movement_date: new Date(Date.now() - 172800000).toISOString().split('T')[0]
        },
        {
          id: 4,
          case_id: 2,
          case_number: '2024/002',
          case_title: 'قضية عمالية - محمد علي',
          movement_type: 'hearing_scheduled',
          description: 'تم تحديد جلسة للقضية في المحكمة العمالية',
          created_by_name: 'أحمد محمد',
          priority: 'high',
          client_name: 'محمد علي أحمد',
          case_status: 'جلسة مجدولة',
          created_at: new Date(Date.now() - 86400000).toISOString(),
          movement_date: new Date(Date.now() - 86400000).toISOString().split('T')[0]
        },
        {
          id: 5,
          case_id: 3,
          case_number: '2024/003',
          case_title: 'قضية مدنية - فاطمة سالم',
          movement_type: 'case_created',
          description: 'تم إنشاء القضية في النظام',
          created_by_name: 'النظام',
          priority: 'normal',
          client_name: 'فاطمة سالم محمد',
          case_status: 'نشطة',
          created_at: new Date(Date.now() - 259200000).toISOString(),
          movement_date: new Date(Date.now() - 259200000).toISOString().split('T')[0]
        },
        {
          id: 6,
          case_id: 3,
          case_number: '2024/003',
          case_title: 'قضية مدنية - فاطمة سالم',
          movement_type: 'document_uploaded',
          description: 'تم رفع وثيقة "عقد الإيجار الأصلي" للقضية',
          created_by_name: 'سارة أحمد',
          priority: 'normal',
          client_name: 'فاطمة سالم محمد',
          case_status: 'قيد المراجعة',
          created_at: new Date(Date.now() - 172800000).toISOString(),
          movement_date: new Date(Date.now() - 172800000).toISOString().split('T')[0]
        },
        {
          id: 7,
          case_id: 4,
          case_number: '2024/004',
          case_title: 'قضية جنائية - خالد يوسف',
          movement_type: 'case_created',
          description: 'تم إنشاء القضية في النظام',
          created_by_name: 'النظام',
          priority: 'urgent',
          client_name: 'خالد يوسف علي',
          case_status: 'نشطة',
          created_at: new Date(Date.now() - 345600000).toISOString(),
          movement_date: new Date(Date.now() - 345600000).toISOString().split('T')[0]
        },
        {
          id: 8,
          case_id: 4,
          case_number: '2024/004',
          case_title: 'قضية جنائية - خالد يوسف',
          movement_type: 'follow_added',
          description: 'تم إضافة متابعة: تم تقديم الاستئناف للمحكمة العليا',
          created_by_name: 'محمد الحاشدي',
          priority: 'urgent',
          client_name: 'خالد يوسف علي',
          case_status: 'استئناف',
          created_at: new Date(Date.now() - 86400000).toISOString(),
          movement_date: new Date(Date.now() - 86400000).toISOString().split('T')[0]
        },
        {
          id: 9,
          case_id: 5,
          case_number: '2024/005',
          case_title: 'قضية إدارية - شركة النور',
          movement_type: 'case_created',
          description: 'تم إنشاء القضية في النظام',
          created_by_name: 'النظام',
          priority: 'normal',
          client_name: 'شركة النور للمقاولات',
          case_status: 'نشطة',
          created_at: new Date(Date.now() - 432000000).toISOString(),
          movement_date: new Date(Date.now() - 432000000).toISOString().split('T')[0]
        },
        {
          id: 10,
          case_id: 5,
          case_number: '2024/005',
          case_title: 'قضية إدارية - شركة النور',
          movement_type: 'case_status_changed',
          description: 'تم تغيير حالة القضية من "نشطة" إلى "قيد المراجعة"',
          created_by_name: 'أحمد محمد',
          priority: 'normal',
          client_name: 'شركة النور للمقاولات',
          case_status: 'قيد المراجعة',
          created_at: new Date(Date.now() - 259200000).toISOString(),
          movement_date: new Date(Date.now() - 259200000).toISOString().split('T')[0]
        }
      ],
      metadata: {
        total_movements: 10,
        total_cases: 5,
        last_updated: new Date().toISOString(),
        database_type: 'JSON_FALLBACK'
      }
    }
    
    fs.writeFileSync(dbPath, JSON.stringify(initialData, null, 2))
    console.log('✅ Created local JSON database with sample data')
  }
}

// Read database
function readDatabase() {
  try {
    const data = fs.readFileSync(dbPath, 'utf8')
    return JSON.parse(data)
  } catch (error) {
    console.error('❌ Error reading database:', error)
    return { case_movements: [], metadata: { total_movements: 0, total_cases: 0 } }
  }
}

// Helper function for queries (PostgreSQL compatible)
export async function query(text: string, params?: any[]) {
  initializeDatabase()
  
  const start = Date.now()
  
  try {
    const db = readDatabase()
    let result: any = { rows: [], rowCount: 0 }
    
    // Parse SQL-like queries and convert to JSON operations
    const queryLower = text.toLowerCase().trim()
    
    if (queryLower.includes('select') && queryLower.includes('case_movements')) {
      // Handle SELECT queries
      let movements = db.case_movements || []
      
      // Apply filters based on WHERE conditions
      if (queryLower.includes('where')) {
        if (queryLower.includes('movement_type')) {
          const typeMatch = text.match(/movement_type\s*=\s*['"](.*?)['"]/i)
          if (typeMatch) {
            movements = movements.filter((m: any) => m.movement_type === typeMatch[1])
          }
        }
        
        if (queryLower.includes('priority')) {
          const priorityMatch = text.match(/priority\s*=\s*['"](.*?)['"]/i)
          if (priorityMatch) {
            movements = movements.filter((m: any) => m.priority === priorityMatch[1])
          }
        }
        
        if (queryLower.includes('case_number') || queryLower.includes('case_title')) {
          const searchMatch = text.match(/(?:case_number|case_title)\s*(?:like|ilike)\s*['"](.*?)['"]/i)
          if (searchMatch) {
            const searchTerm = searchMatch[1].replace(/%/g, '').toLowerCase()
            movements = movements.filter((m: any) => 
              m.case_number?.toLowerCase().includes(searchTerm) ||
              m.case_title?.toLowerCase().includes(searchTerm)
            )
          }
        }
      }
      
      // Apply ORDER BY
      if (queryLower.includes('order by')) {
        if (queryLower.includes('created_at desc')) {
          movements.sort((a: any, b: any) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        }
      }
      
      // Apply LIMIT
      if (queryLower.includes('limit')) {
        const limitMatch = text.match(/limit\s+(\d+)/i)
        if (limitMatch) {
          movements = movements.slice(0, parseInt(limitMatch[1]))
        }
      }
      
      result.rows = movements
      result.rowCount = movements.length
      
    } else if (queryLower.includes('count') && queryLower.includes('case_movements')) {
      // Handle COUNT queries
      let count = db.case_movements?.length || 0
      
      if (queryLower.includes('where')) {
        let movements = db.case_movements || []
        
        if (queryLower.includes('movement_type')) {
          const typeMatch = text.match(/movement_type\s*=\s*['"](.*?)['"]/i)
          if (typeMatch) {
            movements = movements.filter((m: any) => m.movement_type === typeMatch[1])
          }
        }
        
        if (queryLower.includes('priority')) {
          const priorityMatch = text.match(/priority\s*=\s*['"](.*?)['"]/i)
          if (priorityMatch) {
            movements = movements.filter((m: any) => m.priority === priorityMatch[1])
          }
        }
        
        count = movements.length
      }
      
      result.rows = [{ count: count.toString() }]
      result.rowCount = 1
    }
    
    const duration = Date.now() - start
    console.log('📊 JSON Query executed', { 
      query: text.substring(0, 50) + '...', 
      duration, 
      rows: result.rowCount 
    })
    
    return result
    
  } catch (error) {
    console.error('❌ JSON Query error:', error)
    throw error
  }
}

// Mock pool object for compatibility
export const pool = {
  query: async (text: string, params?: any[]) => {
    return query(text, params)
  }
}

console.log(`📁 Using JSON fallback database: ${dbPath}`)
