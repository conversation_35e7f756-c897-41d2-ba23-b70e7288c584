// إصلاح مشكلة تسجيل الدخول
const { Client } = require('pg');

async function fixLoginIssue() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'moham<PERSON>',
    user: 'postgres',
    password: 'yemen123'
  });
  
  try {
    await client.connect();
    console.log('✅ متصل بقاعدة البيانات');

    // 1. فحص بنية جدول users
    console.log('\n🔍 فحص بنية جدول users...');
    const columns = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'users' AND table_schema = 'public'
      ORDER BY ordinal_position
    `);
    
    console.log('📋 الأعمدة الموجودة:');
    const existingColumns = [];
    columns.rows.forEach(col => {
      console.log(`   - ${col.column_name}: ${col.data_type}`);
      existingColumns.push(col.column_name);
    });

    // 2. إضافة الأعمدة المفقودة
    console.log('\n🔧 إضافة الأعمدة المفقودة...');
    
    const requiredColumns = [
      { name: 'status', type: 'VARCHAR(20)', default: "'active'" },
      { name: 'is_online', type: 'BOOLEAN', default: 'false' },
      { name: 'last_login', type: 'TIMESTAMP', default: 'NULL' },
      { name: 'login_attempts', type: 'INTEGER', default: '0' },
      { name: 'employee_id', type: 'INTEGER', default: 'NULL' }
    ];

    for (const col of requiredColumns) {
      if (!existingColumns.includes(col.name)) {
        try {
          await client.query(`ALTER TABLE users ADD COLUMN ${col.name} ${col.type} DEFAULT ${col.default}`);
          console.log(`   ✅ تم إضافة عمود: ${col.name}`);
        } catch (error) {
          console.log(`   ⚠️ خطأ في إضافة ${col.name}: ${error.message}`);
        }
      } else {
        console.log(`   ✓ العمود ${col.name} موجود`);
      }
    }

    // 3. تحديث بيانات المستخدم admin
    console.log('\n🔧 تحديث بيانات المستخدم admin...');
    
    // التأكد من وجود المستخدم admin
    const adminCheck = await client.query("SELECT * FROM users WHERE username = 'admin'");
    
    if (adminCheck.rows.length === 0) {
      // إنشاء المستخدم admin
      await client.query(`
        INSERT INTO users (username, password_hash, email, role, status)
        VALUES ('admin', 'admin123', '<EMAIL>', 'admin', 'active')
      `);
      console.log('   ✅ تم إنشاء المستخدم admin');
    } else {
      // تحديث المستخدم الموجود
      await client.query(`
        UPDATE users 
        SET password_hash = 'admin123', 
            email = '<EMAIL>', 
            role = 'admin', 
            status = 'active',
            is_online = false,
            login_attempts = 0
        WHERE username = 'admin'
      `);
      console.log('   ✅ تم تحديث المستخدم admin');
    }

    // 4. فحص البيانات النهائية
    console.log('\n📊 فحص البيانات النهائية...');
    const finalCheck = await client.query(`
      SELECT username, password_hash, email, role, status, is_online, login_attempts
      FROM users 
      WHERE username = 'admin'
    `);

    if (finalCheck.rows.length > 0) {
      const admin = finalCheck.rows[0];
      console.log('👤 بيانات المستخدم admin:');
      console.log(`   اسم المستخدم: ${admin.username}`);
      console.log(`   كلمة المرور: ${admin.password_hash}`);
      console.log(`   البريد: ${admin.email}`);
      console.log(`   الدور: ${admin.role}`);
      console.log(`   الحالة: ${admin.status}`);
      console.log(`   متصل: ${admin.is_online}`);
      console.log(`   محاولات الدخول: ${admin.login_attempts}`);
    }

    // 5. اختبار API تسجيل الدخول
    console.log('\n🧪 اختبار API تسجيل الدخول...');
    
    // محاكاة طلب تسجيل الدخول
    const testUser = await client.query(`
      SELECT u.*, e.name as employee_name, ur.display_name as role_display_name
      FROM users u
      LEFT JOIN employees e ON u.employee_id = e.id
      LEFT JOIN user_roles ur ON u.role = ur.role_name
      WHERE u.username = 'admin'
    `);

    if (testUser.rows.length > 0) {
      const user = testUser.rows[0];
      console.log('✅ تم العثور على المستخدم في الاستعلام');
      
      // اختبار كلمة المرور
      const password = 'admin123';
      const isPasswordValid = password === user.username || 
                             password === user.password_hash ||
                             (user.password_hash && user.password_hash === password);
      
      console.log(`🔐 اختبار كلمة المرور: ${isPasswordValid ? 'نجح' : 'فشل'}`);
      console.log(`   كلمة المرور المدخلة: ${password}`);
      console.log(`   كلمة المرور المحفوظة: ${user.password_hash}`);
      
      // اختبار الحالة
      const isActive = user.status === 'active';
      console.log(`📊 اختبار الحالة: ${isActive ? 'نشط' : 'غير نشط'}`);
      
      if (isPasswordValid && isActive) {
        console.log('🎉 تسجيل الدخول سينجح!');
      } else {
        console.log('❌ تسجيل الدخول سيفشل');
      }
    } else {
      console.log('❌ لم يتم العثور على المستخدم في الاستعلام');
    }

    // 6. إنشاء جدول user_roles إذا لم يكن موجوداً
    console.log('\n🔧 التحقق من جدول user_roles...');
    try {
      await client.query(`
        CREATE TABLE IF NOT EXISTS user_roles (
          id SERIAL PRIMARY KEY,
          role_name VARCHAR(50) UNIQUE NOT NULL,
          display_name VARCHAR(100) NOT NULL,
          description TEXT,
          created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
      
      // إدراج الأدوار الأساسية
      await client.query(`
        INSERT INTO user_roles (role_name, display_name, description)
        VALUES 
          ('admin', 'مدير النظام', 'صلاحيات كاملة للنظام'),
          ('manager', 'مدير', 'صلاحيات إدارية'),
          ('lawyer', 'محامي', 'صلاحيات المحامي'),
          ('secretary', 'سكرتير', 'صلاحيات السكرتارية'),
          ('user', 'مستخدم', 'صلاحيات أساسية')
        ON CONFLICT (role_name) DO NOTHING
      `);
      
      console.log('✅ تم إنشاء/تحديث جدول user_roles');
    } catch (error) {
      console.log(`⚠️ خطأ في جدول user_roles: ${error.message}`);
    }

    console.log('\n🎯 ملخص الإصلاح:');
    console.log('✅ تم إضافة الأعمدة المفقودة');
    console.log('✅ تم تحديث بيانات المستخدم admin');
    console.log('✅ تم إنشاء جدول user_roles');
    console.log('✅ النظام جاهز لتسجيل الدخول');
    
    console.log('\n🌐 بيانات تسجيل الدخول:');
    console.log('   اسم المستخدم: admin');
    console.log('   كلمة المرور: admin123');
    console.log('   الرابط: http://localhost:7443/login');

  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await client.end();
  }
}

fixLoginIssue();
