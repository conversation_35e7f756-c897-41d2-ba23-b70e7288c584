import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب جميع الموردين
export async function GET() {
  try {
    const result = await query(`
      SELECT
        s.*,
        -- الحساب الأب (حسابات الموردين)
        parent_coa.account_name as parent_account_name,
        parent_coa.account_code as parent_account_code,
        parent_coa.current_balance as parent_account_balance,
        -- الحساب الفردي للمورد (إذا وجد)
        individual_coa.id as individual_account_id,
        individual_coa.account_name as individual_account_name,
        individual_coa.account_code as individual_account_code,
        individual_coa.current_balance as individual_balance
      FROM suppliers s
      LEFT JOIN chart_of_accounts parent_coa ON s.account_id = parent_coa.id
      LEFT JOIN chart_of_accounts individual_coa ON individual_coa.linked_record_id = s.id
        AND individual_coa.original_table = 'suppliers'
      ORDER BY s.created_date DESC
    `)

    return NextResponse.json({
      success: true,
      suppliers: result.rows
    })
  } catch (error) {
    console.error('Error fetching suppliers:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات الموردين' },
      { status: 500 }
    )
  }
}

// POST - إضافة مورد جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      name,
      company_name,
      phone,
      email,
      address,
      tax_number,
      commercial_register,
      contact_person,
      payment_terms,
      credit_limit = 0,
      account_id
    } = body

    // التحقق من صحة البيانات
    if (!name) {
      return NextResponse.json(
        { success: false, error: 'اسم المورد مطلوب' },
        { status: 400 }
      )
    }

    // إدراج المورد الجديد أولاً
    const result = await query(`
      INSERT INTO suppliers (
        name, company_name, phone, email, address, 
        tax_number, commercial_register, contact_person, 
        payment_terms, credit_limit
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING *
    `, [
      name, company_name, phone, email, address,
      tax_number, commercial_register, contact_person,
      payment_terms, credit_limit
    ])

    const newSupplier = result.rows[0]

    // إنشاء حساب مرتبط في دليل الحسابات
    const supplierAccountCode = `2101${String(newSupplier.id).padStart(4, '0')}`
    const accountResult = await query(`
      INSERT INTO chart_of_accounts (
        account_code, account_name, account_level, parent_id, account_type, 
        account_nature, allow_transactions, is_linked_record, original_table, 
        linked_record_id, opening_balance, current_balance
      ) VALUES (
        $1, $2, 4, 
        (SELECT id FROM chart_of_accounts WHERE account_code = '2101'), 
        'خصوم', 'دائن', true, true, 'suppliers', $3, 0, 0
      ) RETURNING id
    `, [supplierAccountCode, `حساب المورد: ${name}`, newSupplier.id])

    // تحديث المورد بمعرف الحساب
    await query(`
      UPDATE suppliers SET account_id = $1, account_code = $2 WHERE id = $3
    `, [accountResult.rows[0].id, supplierAccountCode, newSupplier.id])

    // تحديث بيانات المورد مع معرف الحساب
    newSupplier.account_id = accountResult.rows[0].id
    newSupplier.account_code = supplierAccountCode

    return NextResponse.json({
      success: true,
      message: 'تم إضافة المورد بنجاح',
      supplier: newSupplier
    })

  } catch (error) {
    console.error('Error creating supplier:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة المورد' },
      { status: 500 }
    )
  }
}

// PUT - تحديث مورد
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      name,
      company_name,
      phone,
      email,
      address,
      tax_number,
      commercial_register,
      contact_person,
      payment_terms,
      credit_limit,
      current_balance,
      status,
      account_id
    } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف المورد مطلوب' },
        { status: 400 }
      )
    }

    // تحديث بيانات المورد
    const result = await query(`
      UPDATE suppliers SET
        name = $1,
        company_name = $2,
        phone = $3,
        email = $4,
        address = $5,
        tax_number = $6,
        commercial_register = $7,
        contact_person = $8,
        payment_terms = $9,
        credit_limit = $10,
        current_balance = $11,
        status = $12,
        account_id = $13,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $14
      RETURNING *
    `, [
      name, company_name, phone, email, address,
      tax_number, commercial_register, contact_person,
      payment_terms, credit_limit, current_balance, status, account_id, id
    ])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'المورد غير موجود' },
        { status: 404 }
      )
    }

    const updatedSupplier = result.rows[0]

    return NextResponse.json({
      success: true,
      message: 'تم تحديث بيانات المورد بنجاح',
      supplier: updatedSupplier
    })

  } catch (error) {
    console.error('Error updating supplier:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث بيانات المورد' },
      { status: 500 }
    )
  }
}

// DELETE - حذف مورد
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف المورد مطلوب' },
        { status: 400 }
      )
    }

    // حذف المورد
    const result = await query('DELETE FROM suppliers WHERE id = $1 RETURNING *', [id])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'المورد غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف المورد بنجاح'
    })

  } catch (error) {
    console.error('Error deleting supplier:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف المورد' },
      { status: 500 }
    )
  }
}
