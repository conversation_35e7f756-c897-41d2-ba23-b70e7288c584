#!/usr/bin/env node
/**
 * Apply core HR schema updates (departments, employees) to match development.
 * - Ensures departments structure (dept_name, dept_code, ...)
 * - Ensures employees table and columns (employee_number, account_id, department_id, department, ...)
 * - Drops any legacy FK on employees.department_id to avoid mismatched schema during rollout
 *
 * Safe to run multiple times.
 */
const fs = require('fs');
const path = require('path');
const { Client } = require('pg');

function loadConfig() {
  const rcPath = path.join(process.cwd(), 'routing.config.json');
  const rc = fs.existsSync(rcPath) ? JSON.parse(fs.readFileSync(rcPath, 'utf8')) : {};
  const dc = rc.default_config || {};
  return {
    host: process.env.DB_HOST || dc.db_host || 'localhost',
    port: Number(process.env.DB_PORT || dc.db_port || 5432),
    user: process.env.DB_USER || dc.db_user || 'postgres',
    password: process.env.DB_PASSWORD || dc.db_password || 'postgres',
    database: process.env.DB_NAME || dc.db_name || 'mohammi',
  };
}

async function run() {
  const conn = loadConfig();
  const client = new Client(conn);
  await client.connect();
  const exec = (sql, params=[]) => client.query(sql, params);

  try {
    // 1) Departments table and columns
    await exec(`
      CREATE TABLE IF NOT EXISTS departments (
        id SERIAL PRIMARY KEY,
        dept_code VARCHAR(50) UNIQUE,
        dept_name VARCHAR(200) NOT NULL,
        description TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_date TIMESTAMP
      );
    `);

    // Backward compatibility: add columns if missing and copy name -> dept_name when needed
    await exec(`
      DO $$
      BEGIN
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.columns
          WHERE table_name = 'departments' AND column_name = 'dept_name'
        ) THEN
          ALTER TABLE departments ADD COLUMN dept_name VARCHAR(200);
        END IF;

        IF NOT EXISTS (
          SELECT 1 FROM information_schema.columns
          WHERE table_name = 'departments' AND column_name = 'dept_code'
        ) THEN
          ALTER TABLE departments ADD COLUMN dept_code VARCHAR(50);
        END IF;

        IF NOT EXISTS (
          SELECT 1 FROM information_schema.columns
          WHERE table_name = 'departments' AND column_name = 'created_date'
        ) THEN
          ALTER TABLE departments ADD COLUMN created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        END IF;

        IF NOT EXISTS (
          SELECT 1 FROM information_schema.columns
          WHERE table_name = 'departments' AND column_name = 'updated_date'
        ) THEN
          ALTER TABLE departments ADD COLUMN updated_date TIMESTAMP;
        END IF;

        IF EXISTS (
          SELECT 1 FROM information_schema.columns
          WHERE table_name = 'departments' AND column_name = 'name'
        ) THEN
          UPDATE departments SET dept_name = COALESCE(dept_name, name) WHERE dept_name IS NULL;
        END IF;
      END $$;
    `);

    // 2) Employees table and columns
    await exec(`
      CREATE TABLE IF NOT EXISTS employees (
        id SERIAL PRIMARY KEY,
        employee_number VARCHAR(50),
        name VARCHAR(200) NOT NULL,
        position VARCHAR(200),
        department_id INTEGER,
        department VARCHAR(200),
        account_id INTEGER,
        branch_id INTEGER,
        governorate_id INTEGER,
        court_id INTEGER,
        phone VARCHAR(50),
        email VARCHAR(200),
        address TEXT,
        id_number VARCHAR(100),
        salary NUMERIC,
        hire_date DATE,
        status VARCHAR(50) DEFAULT 'active',
        is_active BOOLEAN DEFAULT TRUE,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Ensure missing columns (idempotent)
    async function addCol(col, ddl) {
      const res = await exec(`SELECT 1 FROM information_schema.columns WHERE table_name='employees' AND column_name=$1 AND table_schema='public'`, [col]);
      if (res.rowCount === 0) {
        await exec(`ALTER TABLE employees ADD COLUMN ${ddl}`);
        console.log(`+ Added column employees.${col}`);
      }
    }
    await addCol('employee_number', 'employee_number VARCHAR(50)');
    await addCol('account_id', 'account_id INTEGER');
    await addCol('department_id', 'department_id INTEGER');
    await addCol('department', 'department VARCHAR(200)');
    await addCol('branch_id', 'branch_id INTEGER');
    await addCol('governorate_id', 'governorate_id INTEGER');
    await addCol('court_id', 'court_id INTEGER');
    await addCol('id_number', 'id_number VARCHAR(100)');
    await addCol('salary', 'salary NUMERIC');
    await addCol('hire_date', 'hire_date DATE');
    await addCol('status', "status VARCHAR(50) DEFAULT 'active'");
    await addCol('is_active', 'is_active BOOLEAN DEFAULT TRUE');

    // 3) Drop any legacy FK on employees.department_id to avoid mismatches across schemas
    await exec(`
      DO $$
      DECLARE r RECORD; BEGIN
        FOR r IN
          SELECT c.conname
          FROM pg_constraint c
          JOIN pg_class t ON t.oid = c.conrelid AND t.relname = 'employees'
          JOIN pg_namespace n ON n.oid = t.relnamespace AND n.nspname = 'public'
          WHERE c.contype = 'f' AND c.conkey = ARRAY(
            SELECT attnum FROM pg_attribute WHERE attrelid = t.oid AND attname = 'department_id'
          )
        LOOP
          BEGIN
            EXECUTE 'ALTER TABLE employees DROP CONSTRAINT ' || quote_ident(r.conname);
          EXCEPTION WHEN undefined_object THEN NULL; END;
        END LOOP;
      END $$;
    `);

    // 4) Align sequence for employees.id
    await exec(`
      DO $$
      DECLARE seq_name text := 'employees_id_seq'; has_default boolean := false; BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_class WHERE relkind='S' AND relname=seq_name) THEN
          EXECUTE 'CREATE SEQUENCE ' || seq_name;
        END IF;
        BEGIN EXECUTE 'ALTER SEQUENCE ' || seq_name || ' OWNED BY employees.id'; EXCEPTION WHEN OTHERS THEN NULL; END;
        SELECT (pg_get_expr(adbin, adrelid) LIKE ('nextval(%' || seq_name || '%)')) INTO has_default
        FROM pg_attrdef d JOIN pg_attribute a ON a.attrelid=d.adrelid AND a.attnum=d.adnum
        JOIN pg_class c ON c.oid=d.adrelid WHERE c.relname='employees' AND a.attname='id';
        IF NOT has_default THEN
          EXECUTE 'ALTER TABLE employees ALTER COLUMN id SET DEFAULT nextval(''' || seq_name || ''')';
        END IF;
        PERFORM setval(seq_name, COALESCE((SELECT MAX(id) FROM employees), 0));
      END $$;
    `);

    console.log('\nHR schema updates applied successfully.');
  } catch (e) {
    console.error('HR schema update failed:', e.message);
    process.exitCode = 1;
  } finally {
    await client.end().catch(()=>{});
  }
}

run();
