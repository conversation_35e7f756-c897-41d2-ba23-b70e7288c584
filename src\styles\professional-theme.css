/* Professional Legal Theme Colors */
:root {
  /* Primary Brand Colors */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;
  --primary-950: #172554;

  /* Secondary Colors - Professional Gray */
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
  --gray-950: #020617;

  /* Accent Colors */
  --accent-gold: #f59e0b;
  --accent-gold-light: #fbbf24;
  --accent-gold-dark: #d97706;
  
  --accent-green: #10b981;
  --accent-green-light: #34d399;
  --accent-green-dark: #059669;
  
  --accent-red: #ef4444;
  --accent-red-light: #f87171;
  --accent-red-dark: #dc2626;

  /* Professional Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--gray-800) 0%, var(--gray-900) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-gold) 0%, var(--accent-gold-dark) 100%);
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  
  /* Professional Typography */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-size-6xl: 3.75rem;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-3xl: 2rem;
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;
}

/* Professional Button Styles */
.btn-primary {
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: var(--radius-xl);
  padding: var(--spacing-md) var(--spacing-xl);
  font-weight: 600;
  font-size: var(--font-size-base);
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.btn-secondary {
  background: transparent;
  color: var(--primary-600);
  border: 2px solid var(--primary-600);
  border-radius: var(--radius-xl);
  padding: var(--spacing-md) var(--spacing-xl);
  font-weight: 600;
  font-size: var(--font-size-base);
  transition: all var(--transition-normal);
}

.btn-secondary:hover {
  background: var(--primary-600);
  color: white;
  transform: translateY(-1px);
}

/* Professional Card Styles */
.card-professional {
  background: white;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-normal);
}

.card-professional:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl);
}

/* Professional Text Styles */
.text-primary {
  color: var(--primary-600);
}

.text-secondary {
  color: var(--gray-600);
}

.text-accent {
  color: var(--accent-gold);
}

/* Professional Background Styles */
.bg-primary {
  background: var(--gradient-primary);
}

.bg-secondary {
  background: var(--gradient-secondary);
}

.bg-accent {
  background: var(--gradient-accent);
}

/* Professional Section Styles */
.section-professional {
  padding: var(--spacing-3xl) 0;
}

.section-hero {
  background: var(--gradient-secondary);
  min-height: 100vh;
  display: flex;
  align-items: center;
}

/* Professional Navigation Styles */
.nav-professional {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  border-bottom: 1px solid var(--gray-200);
  transition: all var(--transition-normal);
}

.nav-link {
  color: var(--gray-700);
  font-weight: 500;
  transition: all var(--transition-fast);
  position: relative;
}

.nav-link:hover {
  color: var(--primary-600);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-600);
  transition: width var(--transition-normal);
}

.nav-link:hover::after {
  width: 100%;
}

/* Professional Form Styles */
.form-professional input,
.form-professional textarea,
.form-professional select {
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  font-size: var(--font-size-base);
  transition: all var(--transition-fast);
}

.form-professional input:focus,
.form-professional textarea:focus,
.form-professional select:focus {
  border-color: var(--primary-600);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

/* Professional Animation Classes */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.8s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Professional Responsive Utilities */
@media (max-width: 768px) {
  .section-professional {
    padding: var(--spacing-2xl) 0;
  }
  
  .btn-primary,
  .btn-secondary {
    width: 100%;
    margin-bottom: var(--spacing-md);
  }
}

/* Professional Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --gray-50: #0f172a;
    --gray-100: #1e293b;
    --gray-200: #334155;
    --gray-300: #475569;
    --gray-400: #64748b;
    --gray-500: #94a3b8;
    --gray-600: #cbd5e1;
    --gray-700: #e2e8f0;
    --gray-800: #f1f5f9;
    --gray-900: #f8fafc;
  }
}
