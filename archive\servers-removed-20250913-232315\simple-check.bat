@echo off
echo Server Status Check
echo ===================

echo Checking port 7443...
netstat -an | findstr ":7443" | findstr "LISTENING"
if %errorLevel% equ 0 (
    echo Port 7443: RUNNING
) else (
    echo Port 7443: STOPPED
)

echo Checking port 8914...
netstat -an | findstr ":8914" | findstr "LISTENING"
if %errorLevel% equ 0 (
    echo Port 8914: RUNNING
) else (
    echo Port 8914: STOPPED
)

echo Checking scheduled task...
schtasks /query /tn AdvancedUnifiedServer | findstr "Running"
if %errorLevel% equ 0 (
    echo Task: RUNNING
) else (
    echo Task: STOPPED
)

echo.
echo Access URLs:
echo - Production: http://localhost:7443
echo - Rabei: http://localhost:8914
echo - Development: http://localhost:3300
echo.

pause
