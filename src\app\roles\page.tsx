'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Checkbox } from '@/components/ui/checkbox'
import { Users, Shield, Plus, Edit, Trash2, Search, Settings } from 'lucide-react'
import { useAuth } from '@/hooks/useAuth'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'

interface Role {
  role_name: string
  display_name: string
  description: string
  permissions: string[]
  permissions_count: number
  is_active: boolean
  created_date: string
}

interface Permission {
  key: string
  name: string
  category: string
}

function RolesPageContent() {
  const { user } = useAuth()
  const [roles, setRoles] = useState<Role[]>([])
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalType, setModalType] = useState<'add' | 'edit'>('add')
  const [selectedRole, setSelectedRole] = useState<Role | null>(null)
  const [formData, setFormData] = useState({
    role_name: '',
    display_name: '',
    description: '',
    permissions: [] as string[]
  })

  useEffect(() => {
    fetchRoles()
    fetchPermissions()
  }, [])

  const fetchRoles = async () => {
    try {
      const response = await fetch('/api/user-roles')
      const result = await response.json()
      if (result.success) {
        setRoles(result.data)
      }
    } catch (error) {
      console.error('خطأ في جلب الأدوار:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchPermissions = async () => {
    try {
      const response = await fetch('/api/permissions')
      const result = await response.json()
      if (result.success) {
        setPermissions(result.data.permissions)
      }
    } catch (error) {
      console.error('خطأ في جلب الصلاحيات:', error)
    }
  }

  const handleAddRole = () => {
    setModalType('add')
    setSelectedRole(null)
    setFormData({
      role_name: '',
      display_name: '',
      description: '',
      permissions: []
    })
    setIsModalOpen(true)
  }

  const handleEditRole = (role: Role) => {
    setModalType('edit')
    setSelectedRole(role)
    setFormData({
      role_name: role.role_name,
      display_name: role.display_name,
      description: role.description || '',
      permissions: role.permissions || []
    })
    setIsModalOpen(true)
  }

  const handleSaveRole = async () => {
    try {
      const response = await fetch('/api/user-roles', {
        method: modalType === 'add' ? 'POST' : 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      const result = await response.json()
      if (result.success) {
        setIsModalOpen(false)
        fetchRoles()
      }
    } catch (error) {
      console.error('خطأ في حفظ الدور:', error)
    }
  }

  const filteredRoles = roles.filter(role =>
    role.display_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    role.role_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (role.description && role.description.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  const getPermissionsByCategory = () => {
    const categories: { [key: string]: Permission[] } = {}
    permissions.forEach(permission => {
      if (!categories[permission.category]) {
        categories[permission.category] = []
      }
      categories[permission.category].push(permission)
    })
    return categories
  }

  const getRoleColor = (roleName: string) => {
    const colors: { [key: string]: string } = {
      'admin': 'bg-red-100 text-red-800 border-red-200',
      'manager': 'bg-blue-100 text-blue-800 border-blue-200',
      'lawyer': 'bg-green-100 text-green-800 border-green-200',
      'secretary': 'bg-yellow-100 text-yellow-800 border-yellow-200',
      'accountant': 'bg-purple-100 text-purple-800 border-purple-200',
      'viewer': 'bg-gray-100 text-gray-800 border-gray-200'
    }
    return colors[roleName] || 'bg-gray-100 text-gray-800 border-gray-200'
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Users className="h-8 w-8 mr-3 text-blue-600" />
              إدارة الأدوار والصلاحيات
            </h1>
            <p className="text-gray-600 mt-2">
              إدارة أدوار المستخدمين وتحديد الصلاحيات لكل دور
            </p>
          </div>
          <Button onClick={handleAddRole} className="bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            إضافة دور جديد
          </Button>
        </div>

        {/* شريط البحث */}
        <div className="mb-6">
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="البحث في الأدوار..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pr-10"
            />
          </div>
        </div>

        {/* قائمة الأدوار */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredRoles.map((role) => (
            <Card key={role.role_name} className={`border-2 ${getRoleColor(role.role_name)}`}>
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">{role.display_name}</CardTitle>
                    <Badge variant="outline" className="mt-1">
                      {role.role_name}
                    </Badge>
                  </div>
                  <div className="flex space-x-1 space-x-reverse">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleEditRole(role)}
                      className="h-8 w-8 p-0"
                    >
                      <Edit className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {role.description && (
                    <p className="text-sm text-gray-600">{role.description}</p>
                  )}
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Shield className="h-4 w-4 mr-1 text-blue-600" />
                      <span className="text-sm font-medium">
                        {role.permissions_count || 0} صلاحية
                      </span>
                    </div>
                    <Badge variant={role.is_active ? 'default' : 'secondary'}>
                      {role.is_active ? 'نشط' : 'غير نشط'}
                    </Badge>
                  </div>

                  {role.permissions && role.permissions.length > 0 && (
                    <div className="mt-3">
                      <div className="text-xs text-gray-500 mb-2">أمثلة على الصلاحيات:</div>
                      <div className="flex flex-wrap gap-1">
                        {role.permissions.slice(0, 3).map(permission => (
                          <Badge key={permission} variant="outline" className="text-xs">
                            {permission}
                          </Badge>
                        ))}
                        {role.permissions.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{role.permissions.length - 3} أخرى
                          </Badge>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* نافذة إضافة/تعديل الدور */}
        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center">
                <Settings className="h-5 w-5 mr-2" />
                {modalType === 'add' ? 'إضافة دور جديد' : 'تعديل الدور'}
              </DialogTitle>
            </DialogHeader>

            <div className="space-y-6">
              {/* معلومات الدور */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="role_name">اسم الدور (بالإنجليزية)</Label>
                  <Input
                    id="role_name"
                    value={formData.role_name}
                    onChange={(e) => setFormData({...formData, role_name: e.target.value})}
                    placeholder="مثال: senior_lawyer"
                    disabled={modalType === 'edit'}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="display_name">الاسم المعروض</Label>
                  <Input
                    id="display_name"
                    value={formData.display_name}
                    onChange={(e) => setFormData({...formData, display_name: e.target.value})}
                    placeholder="مثال: محامي أول"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">الوصف</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  placeholder="وصف مختصر للدور وصلاحياته"
                  rows={3}
                />
              </div>

              {/* الصلاحيات */}
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <Label className="text-lg font-medium">الصلاحيات</Label>
                  <div className="flex space-x-2 space-x-reverse">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setFormData({...formData, permissions: permissions.map(p => p.key)})}
                    >
                      تحديد الكل
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setFormData({...formData, permissions: []})}
                    >
                      إلغاء الكل
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Object.entries(getPermissionsByCategory()).map(([category, categoryPermissions]) => (
                    <Card key={category} className="border">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm">{category}</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        {categoryPermissions.map(permission => (
                          <div key={permission.key} className="flex items-center space-x-2 space-x-reverse">
                            <Checkbox
                              id={permission.key}
                              checked={formData.permissions.includes(permission.key)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setFormData({
                                    ...formData,
                                    permissions: [...formData.permissions, permission.key]
                                  })
                                } else {
                                  setFormData({
                                    ...formData,
                                    permissions: formData.permissions.filter(p => p !== permission.key)
                                  })
                                }
                              }}
                            />
                            <Label htmlFor={permission.key} className="text-sm cursor-pointer">
                              {permission.name}
                            </Label>
                          </div>
                        ))}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              {/* أزرار الحفظ */}
              <div className="flex justify-end space-x-3 space-x-reverse pt-4 border-t">
                <Button variant="outline" onClick={() => setIsModalOpen(false)}>
                  إلغاء
                </Button>
                <Button onClick={handleSaveRole} className="bg-blue-600 hover:bg-blue-700">
                  {modalType === 'add' ? 'إضافة الدور' : 'حفظ التغييرات'}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  )
}

export default function RolesPage() {
  return (
    <ProtectedRoute
      userType="user"
      requiredPermissions={['users_permissions', 'system_admin']}
    >
      <RolesPageContent />
    </ProtectedRoute>
  )
}
