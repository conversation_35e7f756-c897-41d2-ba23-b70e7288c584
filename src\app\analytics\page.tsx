'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  BarChart3,
  TrendingUp,
  Users,
  Calendar,
  FileText,
  Clock,
  Target,
  Award,
  AlertCircle,
  Download,
  Filter,
  RefreshCw
} from 'lucide-react'

interface AnalyticsData {
  general_stats: any
  sessions_stats: any
  movements_stats: any
  employee_performance: any[]
  case_type_distribution: any[]
  trends: any[]
  most_active_cases: any[]
  notifications_analysis: any[]
}

export default function AnalyticsPage() {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [period, setPeriod] = useState('month')

  // جلب البيانات التحليلية
  const fetchAnalytics = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/case-analytics?period=${period}`)
      const data = await response.json()

      if (data.success) {
        setAnalytics(data.data)
      }
    } catch (error) {
      console.error('خطأ في جلب التحليلات:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAnalytics()
  }, [period])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل التحليلات...</p>
        </div>
      </div>
    )
  }

  if (!analytics) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-gray-600">فشل في تحميل البيانات التحليلية</p>
          <Button onClick={fetchAnalytics} className="mt-4">
            <RefreshCw className="h-4 w-4 mr-2" />
            إعادة المحاولة
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6" dir="rtl">
      {/* العنوان والتحكم */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <BarChart3 className="h-8 w-8 mr-3 text-blue-600" />
            التحليلات والتقارير
          </h1>
          <p className="text-gray-600 mt-1">تحليل شامل لأداء المكتب وحركة القضايا</p>
        </div>

        <div className="flex items-center space-x-4 space-x-reverse">
          <select
            value={period}
            onChange={(e) => setPeriod(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            <option value="day">اليوم</option>
            <option value="week">هذا الأسبوع</option>
            <option value="month">هذا الشهر</option>
            <option value="year">هذا العام</option>
          </select>

          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            تصدير التقرير
          </Button>

          <Button onClick={fetchAnalytics}>
            <RefreshCw className="h-4 w-4 mr-2" />
            تحديث
          </Button>
        </div>
      </div>

      {/* الإحصائيات العامة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="border-r-4 border-r-blue-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي القضايا</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.general_stats.total_cases}</p>
                <p className="text-xs text-gray-500">
                  نشط: {analytics.general_stats.active_cases} |
                  مغلق: {analytics.general_stats.closed_cases}
                </p>
              </div>
              <FileText className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-r-4 border-r-green-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">متوسط مدة القضية</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.general_stats.avg_case_duration ?
                    Math.round(analytics.general_stats.avg_case_duration) : 0} يوم
                </p>
                <p className="text-xs text-gray-500">من التسجيل للإغلاق</p>
              </div>
              <Clock className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-r-4 border-r-yellow-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">الجلسات القادمة</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.sessions_stats.upcoming_sessions}</p>
                <p className="text-xs text-gray-500">
                  اليوم: {analytics.sessions_stats.today_sessions}
                </p>
              </div>
              <Calendar className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-r-4 border-r-red-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">قضايا عالية الأولوية</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.general_stats.high_priority_cases}</p>
                <p className="text-xs text-gray-500">
                  متوقفة: {analytics.general_stats.inactive_cases}
                </p>
              </div>
              <AlertCircle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* أداء الموظفين */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="h-5 w-5 mr-2" />
            أداء الموظفين
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-2 text-right text-sm font-medium text-gray-500">الموظف</th>
                  <th className="px-4 py-2 text-right text-sm font-medium text-gray-500">القضايا المكلف بها</th>
                  <th className="px-4 py-2 text-right text-sm font-medium text-gray-500">إجمالي الحركات</th>
                  <th className="px-4 py-2 text-right text-sm font-medium text-gray-500">القضايا المغلقة</th>
                  <th className="px-4 py-2 text-right text-sm font-medium text-gray-500">متوسط المدة</th>
                  <th className="px-4 py-2 text-right text-sm font-medium text-gray-500">التقييم</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {analytics.employee_performance.map((employee, index) => (
                  <tr key={employee.id} className="hover:bg-gray-50">
                    <td className="px-4 py-2">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                          <span className="text-sm font-medium text-blue-600">
                            {employee.name?.charAt(0)}
                          </span>
                        </div>
                        <span className="font-medium">{employee.name}</span>
                      </div>
                    </td>
                    <td className="px-4 py-2 text-center">
                      <Badge variant="outline">{employee.assigned_cases}</Badge>
                    </td>
                    <td className="px-4 py-2 text-center">{employee.total_movements}</td>
                    <td className="px-4 py-2 text-center">
                      <Badge className="bg-green-100 text-green-800">{employee.closed_cases}</Badge>
                    </td>
                    <td className="px-4 py-2 text-center">
                      {employee.avg_case_duration ? Math.round(employee.avg_case_duration) : 0} يوم
                    </td>
                    <td className="px-4 py-2 text-center">
                      {index < 3 ? (
                        <Badge className="bg-yellow-100 text-yellow-800">
                          <Award className="h-3 w-3 mr-1" />
                          متميز
                        </Badge>
                      ) : (
                        <Badge variant="outline">جيد</Badge>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* توزيع أنواع القضايا */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Target className="h-5 w-5 mr-2" />
              توزيع أنواع القضايا
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.case_type_distribution.map((type, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-4 h-4 rounded-full mr-3"
                         style={{ backgroundColor: `hsl(${index * 60}, 70%, 50%)` }}></div>
                    <span className="font-medium">{type.case_type || 'غير محدد'}</span>
                  </div>
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <span className="text-sm text-gray-600">{type.count}</span>
                    <Badge variant="outline">{type.percentage}%</Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* القضايا الأكثر نشاطاً */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="h-5 w-5 mr-2" />
              القضايا الأكثر نشاطاً
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.most_active_cases.slice(0, 5).map((caseItem, index) => (
                <div key={caseItem.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium">{caseItem.case_number}</p>
                    <p className="text-sm text-gray-600">{caseItem.title}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm font-medium">{caseItem.movements_count} حركة</p>
                    <p className="text-xs text-gray-500">{caseItem.sessions_count} جلسة</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* إحصائيات الحركات */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            إحصائيات الحركات حسب النوع
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {analytics.movements_stats.by_type.slice(0, 6).map((movement, index) => (
              <div key={index} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">{movement.movement_type}</h4>
                  <Badge variant="outline">{movement.movement_count}</Badge>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full"
                    style={{
                      width: `${(movement.movement_count / analytics.movements_stats.total) * 100}%`
                    }}
                  ></div>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {((movement.movement_count / analytics.movements_stats.total) * 100).toFixed(1)}% من إجمالي الحركات
                </p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
