/**
 * اختبار الاتصال بقاعدة البيانات
 */

const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'mohammidev'
};

async function testConnection() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔄 محاولة الاتصال بقاعدة البيانات...');
    console.log('📋 إعدادات الاتصال:');
    console.log(`   - المضيف: ${dbConfig.host}`);
    console.log(`   - المنفذ: ${dbConfig.port}`);
    console.log(`   - المستخدم: ${dbConfig.user}`);
    console.log(`   - قاعدة البيانات: ${dbConfig.database}`);
    
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح!');
    
    // اختبار استعلام بسيط
    const result = await client.query('SELECT NOW() as current_time, version() as postgres_version');
    console.log('⏰ الوقت الحالي:', result.rows[0].current_time);
    console.log('🗄️ إصدار PostgreSQL:', result.rows[0].postgres_version);
    
    // فحص الجداول الموجودة
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    console.log('\n📊 الجداول الموجودة:');
    if (tablesResult.rows.length === 0) {
      console.log('   ❌ لا توجد جداول في قاعدة البيانات');
    } else {
      tablesResult.rows.forEach(row => {
        console.log(`   ✅ ${row.table_name}`);
      });
    }
    
    // فحص جداول النظام المطلوبة
    const requiredTables = [
      'cases', 'case_movements', 'court_sessions', 
      'case_stages', 'case_notifications', 'tracking_settings',
      'employees', 'clients'
    ];
    
    console.log('\n🔍 فحص الجداول المطلوبة:');
    for (const tableName of requiredTables) {
      const tableExists = await client.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = $1
        )
      `, [tableName]);
      
      const exists = tableExists.rows[0].exists;
      console.log(`   ${exists ? '✅' : '❌'} ${tableName}`);
      
      if (exists) {
        // عد الصفوف في الجدول
        try {
          const countResult = await client.query(`SELECT COUNT(*) as count FROM ${tableName}`);
          console.log(`      📊 عدد الصفوف: ${countResult.rows[0].count}`);
        } catch (error) {
          console.log(`      ⚠️ خطأ في قراءة الجدول: ${error.message}`);
        }
      }
    }
    
  } catch (error) {
    console.error('❌ خطأ في الاتصال بقاعدة البيانات:');
    console.error('📝 تفاصيل الخطأ:', error.message);
    console.error('🔧 الكود:', error.code);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('\n💡 الحلول المقترحة:');
      console.error('   1. تأكد من تشغيل خادم PostgreSQL');
      console.error('   2. تحقق من المنفذ 5432');
      console.error('   3. تأكد من إعدادات الشبكة');
    } else if (error.code === '28P01') {
      console.error('\n💡 الحلول المقترحة:');
      console.error('   1. تحقق من كلمة المرور');
      console.error('   2. تأكد من صحة اسم المستخدم');
    } else if (error.code === '3D000') {
      console.error('\n💡 الحلول المقترحة:');
      console.error('   1. تأكد من وجود قاعدة البيانات mohammidev');
      console.error('   2. قم بإنشاء قاعدة البيانات إذا لم تكن موجودة');
    }
    
    throw error;
  } finally {
    await client.end();
    console.log('\n🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الاختبار
if (require.main === module) {
  testConnection()
    .then(() => {
      console.log('\n🎉 اكتمل اختبار الاتصال بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 فشل اختبار الاتصال');
      process.exit(1);
    });
}

module.exports = { testConnection };
