import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب جميع أنواع القضايا من قاعدة البيانات
export async function GET() {
  try {

    const result = await query(`
      SELECT
        id,
        name,
        description,
        category,
        color,
        is_active,
        created_at
      FROM issue_types
      WHERE is_active = true OR is_active IS NULL
      ORDER BY name
    `)

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('GET Issue Types API: Error fetching issue types:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'فشل في جلب بيانات أنواع القضايا',
        message: error.message
      },
      { status: 500 }
    )
  }
}

// POST - إضافة نوع قضية جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, description, category, color } = body

    // التحقق من البيانات المطلوبة
    if (!name) {
      return NextResponse.json(
        { success: false, error: 'اسم نوع القضية مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من عدم تكرار الاسم
    const duplicateCheck = await query(
      'SELECT id FROM issue_types WHERE name = $1',
      [name]
    )

    if (duplicateCheck.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'نوع القضية موجود مسبقاً' },
        { status: 400 }
      )
    }

    // إدراج نوع القضية الجديد
    const result = await query(`
      INSERT INTO issue_types (name, description, category, color, is_active, created_at, updated_at)
      VALUES ($1, $2, $3, $4, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING *
    `, [name, description || '', category || 'عام', color || '#2563eb'])

    return NextResponse.json({
      success: true,
      message: 'تم إضافة نوع القضية بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('POST Issue Types API: Error creating issue type:', error)
    return NextResponse.json(
      { success: false, error: `فشل في إضافة نوع القضية: ${error.message}` },
      { status: 500 }
    )
  }
}

// PUT - تحديث أنواع القضايا
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()

    // هنا يجب إضافة منطق التحديث حسب كل جدول
    // سيتم تحديثه لاحقاً حسب هيكل كل جدول

    return NextResponse.json({
      success: true,
      message: 'تم تحديث أنواع القضايا بنجاح'
    })
  } catch (error) {
    console.error('Error updating أنواع القضايا:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث أنواع القضايا' },
      { status: 500 }
    )
  }
}

// DELETE - حذف أنواع القضايا
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف أنواع القضايا مطلوب' },
        { status: 400 }
      )
    }

    await query('DELETE FROM issue_types WHERE id = $1', [id])

    return NextResponse.json({
      success: true,
      message: 'تم حذف أنواع القضايا بنجاح'
    })
  } catch (error) {
    console.error('Error deleting أنواع القضايا:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف أنواع القضايا' },
      { status: 500 }
    )
  }
}