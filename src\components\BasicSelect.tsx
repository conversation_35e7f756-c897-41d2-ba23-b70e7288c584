'use client'

import { useState, useEffect } from 'react'

interface BasicSelectProps {
  label: string
  placeholder: string
  value: string
  onChange: (value: string) => void
  options: Array<{
    value: string
    label: string
    group?: string
  }>
  required?: boolean
}

export function BasicSelect({
  label,
  placeholder,
  value,
  onChange,
  options,
  required = false
}: BasicSelectProps) {
  // تجميع الخيارات حسب المجموعة
  const groupedOptions = options.reduce((groups, option) => {
    const group = option.group || 'default'
    if (!groups[group]) {
      groups[group] = []
    }
    groups[group].push(option)
    return groups
  }, {} as Record<string, typeof options>)

  return (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-700">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>

      <select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="w-full p-3 border border-gray-300 rounded-md bg-white focus:border-blue-500 focus:ring-1 focus:ring-blue-500 text-sm"
        required={required}
      >
        <option value="">{placeholder}</option>

        {Object.entries(groupedOptions).map(([groupName, groupOptions]) => {
          if (groupName === 'default') {
            return groupOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))
          } else {
            return (
              <optgroup key={groupName} label={groupName}>
                {groupOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </optgroup>
            )
          }
        })}
      </select>

      {/* عرض القيمة المحددة */}
      {value && (
        <div className="text-xs text-blue-600 bg-blue-50 p-2 rounded">
          المحدد: {options.find(opt => opt.value === value)?.label || value}
        </div>
      )}
    </div>
  )
}

// مكون لاختيار المستفيدين
interface BeneficiarySelectProps {
  label: string
  value: string
  onChange: (value: string, data?: any) => void
  required?: boolean
}

export function BeneficiarySelect({
  label,
  value,
  onChange,
  required = false
}: BeneficiarySelectProps) {
  const [options, setOptions] = useState<Array<{value: string, label: string, group: string, data: any}>>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        const allOptions: any[] = []

        // جلب العملاء
        try {
          const clientsRes = await fetch('/api/clients')
          const clientsData = await clientsRes.json()
          if (clientsData.success && clientsData.clients) {
            clientsData.clients.forEach((client: any) => {
              allOptions.push({
                value: `client_${client.id}`,
                label: `${client.name} - ${client.account_code || 'بدون كود'}`,
                group: '👤 العملاء',
                data: { ...client, type: 'client' }
              })
            })
          }
        } catch (e) {
          console.error('خطأ في جلب العملاء:', e)
        }

        // جلب الموظفين
        try {
          const employeesRes = await fetch('/api/employees')
          const employeesData = await employeesRes.json()
          if (employeesData.success && employeesData.employees) {
            employeesData.employees.forEach((employee: any) => {
              allOptions.push({
                value: `employee_${employee.id}`,
                label: `${employee.name} - ${employee.account_code || 'بدون كود'}`,
                group: '👨‍💼 الموظفين',
                data: { ...employee, type: 'employee' }
              })
            })
          }
        } catch (e) {
          console.error('خطأ في جلب الموظفين:', e)
        }

        setOptions(allOptions)
      } catch (error) {
        console.error('خطأ عام:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  const handleChange = (selectedValue: string) => {
    const selectedOption = options.find(opt => opt.value === selectedValue)
    onChange(selectedValue, selectedOption?.data)
  }

  if (loading) {
    return (
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">{label}</label>
        <div className="w-full p-3 border border-gray-300 rounded-md bg-gray-100 text-gray-500">
          جاري التحميل...
        </div>
      </div>
    )
  }

  return (
    <BasicSelect
      label={label}
      placeholder="اختر المستفيد..."
      value={value}
      onChange={handleChange}
      options={options}
      required={required}
    />
  )
}

// مكون لاختيار الحسابات المالية
interface AccountSelectProps {
  label: string
  value: string
  onChange: (value: string) => void
  accountTypes?: string[]
  required?: boolean
}

export function AccountSelect({
  label,
  value,
  onChange,
  accountTypes = [],
  required = false
}: AccountSelectProps) {
  const [options, setOptions] = useState<Array<{value: string, label: string, group: string}>>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchAccounts = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/accounting/chart-of-accounts?only_transactional=true&include_linked=false')
        const data = await response.json()

        if (data.success && data.accounts) {
          let accounts = data.accounts.filter((acc: any) => acc.allow_transactions)

          // فلترة حسب نوع الحساب إذا تم تحديده
          if (accountTypes.length > 0) {
            accounts = accounts.filter((acc: any) => accountTypes.includes(acc.account_type))
          }

          const accountOptions = accounts.map((account: any) => ({
            value: account.id.toString(),
            label: `${account.account_code} - ${account.account_name}`,
            group: account.account_type || 'أخرى'
          }))

          setOptions(accountOptions)
        }
      } catch (error) {
        console.error('خطأ في جلب الحسابات:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchAccounts()
  }, [accountTypes])

  if (loading) {
    return (
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">{label}</label>
        <div className="w-full p-3 border border-gray-300 rounded-md bg-gray-100 text-gray-500">
          جاري التحميل...
        </div>
      </div>
    )
  }

  return (
    <BasicSelect
      label={label}
      placeholder="اختر الحساب..."
      value={value}
      onChange={onChange}
      options={options}
      required={required}
    />
  )
}
