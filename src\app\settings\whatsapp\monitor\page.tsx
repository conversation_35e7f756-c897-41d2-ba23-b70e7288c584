'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Monitor,
  Wifi,
  WifiOff,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Smartphone,
  MessageSquare,
  Activity,
  RefreshCw,
  Settings,
  Zap,
  Shield,
  Database,
  Server
} from 'lucide-react'
import MainLayout from '@/components/layout/main-layout'

interface SystemStatus {
  whatsapp: {
    connected: boolean
    lastSeen: string
    qrCodeRequired: boolean
    sessionActive: boolean
    messagesSent: number
    messagesReceived: number
    errors: number
  }
  database: {
    connected: boolean
    responseTime: number
    lastBackup: string
    recordsCount: number
  }
  server: {
    uptime: string
    memoryUsage: number
    cpuUsage: number
    diskSpace: number
  }
  scheduler: {
    running: boolean
    lastExecution: string
    nextExecution: string
    tasksCompleted: number
    tasksFailed: number
  }
}

export default function WhatsAppMonitorPage() {
  const [status, setStatus] = useState<SystemStatus | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date())
  const [autoRefresh, setAutoRefresh] = useState(true)

  // محاكاة بيانات الحالة
  const mockStatus: SystemStatus = {
    whatsapp: {
      connected: true,
      lastSeen: '2024-01-15 14:30:25',
      qrCodeRequired: false,
      sessionActive: true,
      messagesSent: 156,
      messagesReceived: 23,
      errors: 2
    },
    database: {
      connected: true,
      responseTime: 45,
      lastBackup: '2024-01-15 02:00:00',
      recordsCount: 15847
    },
    server: {
      uptime: '7 أيام، 14 ساعة',
      memoryUsage: 68,
      cpuUsage: 23,
      diskSpace: 45
    },
    scheduler: {
      running: true,
      lastExecution: '2024-01-15 14:00:00',
      nextExecution: '2024-01-15 15:00:00',
      tasksCompleted: 45,
      tasksFailed: 1
    }
  }

  useEffect(() => {
    loadStatus()
    
    if (autoRefresh) {
      const interval = setInterval(loadStatus, 30000) // تحديث كل 30 ثانية
      return () => clearInterval(interval)
    }
  }, [autoRefresh])

  const loadStatus = async () => {
    setIsLoading(true)
    try {
      // محاكاة استدعاء API
      await new Promise(resolve => setTimeout(resolve, 1000))
      setStatus(mockStatus)
      setLastUpdate(new Date())
    } catch (error) {
      console.error('خطأ في تحميل الحالة:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusBadge = (connected: boolean, label: string) => {
    return (
      <Badge className={connected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
        {connected ? <CheckCircle className="h-3 w-3 ml-1" /> : <XCircle className="h-3 w-3 ml-1" />}
        {label}
      </Badge>
    )
  }

  const getUsageColor = (percentage: number) => {
    if (percentage < 50) return 'bg-green-500'
    if (percentage < 80) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('ar-SA')
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان والتحكم */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">مراقبة نظام WhatsApp</h1>
            <p className="text-gray-600 mt-1">
              مراقبة مباشرة لحالة النظام والخدمات
            </p>
          </div>
          <div className="flex space-x-2 space-x-reverse">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => setAutoRefresh(!autoRefresh)}
              className={autoRefresh ? 'bg-green-50 text-green-700' : ''}
            >
              <RefreshCw className={`h-4 w-4 ml-2 ${autoRefresh ? 'animate-spin' : ''}`} />
              {autoRefresh ? 'تحديث تلقائي' : 'تحديث يدوي'}
            </Button>
            <Button onClick={loadStatus} disabled={isLoading}>
              <RefreshCw className={`h-4 w-4 ml-2 ${isLoading ? 'animate-spin' : ''}`} />
              تحديث الآن
            </Button>
          </div>
        </div>

        {/* معلومات آخر تحديث */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2 space-x-reverse">
                <Clock className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-600">
                  آخر تحديث: {lastUpdate.toLocaleString('ar-SA')}
                </span>
              </div>
              <div className="flex items-center space-x-2 space-x-reverse">
                <div className={`w-2 h-2 rounded-full ${status ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <span className="text-sm text-gray-600">
                  {status ? 'النظام يعمل' : 'النظام متوقف'}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {status && (
          <>
            {/* حالة WhatsApp */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 space-x-reverse">
                  <MessageSquare className="h-5 w-5 text-green-600" />
                  <span>حالة WhatsApp</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-700">حالة الاتصال</p>
                    {getStatusBadge(status.whatsapp.connected, status.whatsapp.connected ? 'متصل' : 'غير متصل')}
                    <p className="text-xs text-gray-500">
                      آخر ظهور: {formatTime(status.whatsapp.lastSeen)}
                    </p>
                  </div>

                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-700">حالة الجلسة</p>
                    {getStatusBadge(status.whatsapp.sessionActive, status.whatsapp.sessionActive ? 'نشطة' : 'غير نشطة')}
                    {status.whatsapp.qrCodeRequired && (
                      <Badge className="bg-yellow-100 text-yellow-800">
                        <AlertTriangle className="h-3 w-3 ml-1" />
                        QR Code مطلوب
                      </Badge>
                    )}
                  </div>

                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-700">الرسائل المرسلة</p>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <Smartphone className="h-4 w-4 text-blue-600" />
                      <span className="text-lg font-bold text-blue-600">{status.whatsapp.messagesSent}</span>
                    </div>
                    <p className="text-xs text-gray-500">اليوم</p>
                  </div>

                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-700">الرسائل الواردة</p>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <MessageSquare className="h-4 w-4 text-green-600" />
                      <span className="text-lg font-bold text-green-600">{status.whatsapp.messagesReceived}</span>
                    </div>
                    <p className="text-xs text-gray-500">اليوم</p>
                  </div>
                </div>

                {status.whatsapp.errors > 0 && (
                  <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <AlertTriangle className="h-4 w-4 text-red-600" />
                      <span className="text-sm font-medium text-red-800">
                        {status.whatsapp.errors} خطأ في آخر 24 ساعة
                      </span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* حالة قاعدة البيانات */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 space-x-reverse">
                  <Database className="h-5 w-5 text-blue-600" />
                  <span>حالة قاعدة البيانات</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-700">حالة الاتصال</p>
                    {getStatusBadge(status.database.connected, status.database.connected ? 'متصلة' : 'غير متصلة')}
                  </div>

                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-700">زمن الاستجابة</p>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <Activity className="h-4 w-4 text-green-600" />
                      <span className="text-lg font-bold text-green-600">{status.database.responseTime}ms</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-700">عدد السجلات</p>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <Database className="h-4 w-4 text-purple-600" />
                      <span className="text-lg font-bold text-purple-600">{status.database.recordsCount.toLocaleString()}</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-700">آخر نسخة احتياطية</p>
                    <p className="text-sm text-gray-600">{formatTime(status.database.lastBackup)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* حالة الخادم */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 space-x-reverse">
                  <Server className="h-5 w-5 text-purple-600" />
                  <span>حالة الخادم</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-700">وقت التشغيل</p>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <Clock className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium text-green-600">{status.server.uptime}</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-700">استخدام الذاكرة</p>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <div className="flex-1 bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${getUsageColor(status.server.memoryUsage)}`}
                          style={{ width: `${status.server.memoryUsage}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium">{status.server.memoryUsage}%</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-700">استخدام المعالج</p>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <div className="flex-1 bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${getUsageColor(status.server.cpuUsage)}`}
                          style={{ width: `${status.server.cpuUsage}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium">{status.server.cpuUsage}%</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-700">مساحة القرص</p>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <div className="flex-1 bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${getUsageColor(status.server.diskSpace)}`}
                          style={{ width: `${status.server.diskSpace}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium">{status.server.diskSpace}%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* حالة الجدولة */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 space-x-reverse">
                  <Zap className="h-5 w-5 text-orange-600" />
                  <span>حالة الجدولة التلقائية</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-700">حالة التشغيل</p>
                    {getStatusBadge(status.scheduler.running, status.scheduler.running ? 'يعمل' : 'متوقف')}
                  </div>

                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-700">آخر تنفيذ</p>
                    <p className="text-sm text-gray-600">{formatTime(status.scheduler.lastExecution)}</p>
                  </div>

                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-700">التنفيذ القادم</p>
                    <p className="text-sm text-gray-600">{formatTime(status.scheduler.nextExecution)}</p>
                  </div>

                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-700">المهام المكتملة</p>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-lg font-bold text-green-600">{status.scheduler.tasksCompleted}</span>
                    </div>
                  </div>
                </div>

                {status.scheduler.tasksFailed > 0 && (
                  <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <AlertTriangle className="h-4 w-4 text-yellow-600" />
                      <span className="text-sm font-medium text-yellow-800">
                        {status.scheduler.tasksFailed} مهمة فاشلة في آخر 24 ساعة
                      </span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* أزرار الإجراءات السريعة */}
            <Card>
              <CardHeader>
                <CardTitle>إجراءات سريعة</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Button variant="outline" className="flex flex-col items-center p-4 h-auto">
                    <Settings className="h-6 w-6 mb-2" />
                    <span className="text-sm">إعادة تشغيل WhatsApp</span>
                  </Button>
                  
                  <Button variant="outline" className="flex flex-col items-center p-4 h-auto">
                    <Database className="h-6 w-6 mb-2" />
                    <span className="text-sm">نسخة احتياطية</span>
                  </Button>
                  
                  <Button variant="outline" className="flex flex-col items-center p-4 h-auto">
                    <Zap className="h-6 w-6 mb-2" />
                    <span className="text-sm">إعادة تشغيل الجدولة</span>
                  </Button>
                  
                  <Button variant="outline" className="flex flex-col items-center p-4 h-auto">
                    <Shield className="h-6 w-6 mb-2" />
                    <span className="text-sm">فحص النظام</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </>
        )}

        {/* حالة التحميل */}
        {isLoading && !status && (
          <Card>
            <CardContent className="text-center py-8">
              <RefreshCw className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
              <p className="text-gray-600">جاري تحميل حالة النظام...</p>
            </CardContent>
          </Card>
        )}
      </div>
    </MainLayout>
  )
}
