@echo off
chcp 65001 >nul
echo ========================================
echo   Legal System - Production Startup
echo ========================================
echo.

echo [1/6] Checking Node.js installation...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js 18+ from: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo [2/6] Checking npm availability...
npm --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: npm is not available
    echo.
    pause
    exit /b 1
)

echo [3/6] Checking package.json...
if not exist "package.json" (
    echo ERROR: package.json not found!
    echo Make sure you're in the correct directory
    echo.
    pause
    exit /b 1
)

echo [4/6] Installing dependencies...
call npm install
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    echo Try: npm cache clean --force
    echo.
    pause
    exit /b 1
)

echo [5/6] Checking Next.js installation...
call npx next --version >nul 2>&1
if errorlevel 1 (
    echo WARNING: Next.js not found, installing...
    call npm install next@latest react@latest react-dom@latest
    if errorlevel 1 (
        echo ERROR: Failed to install Next.js
        echo.
        pause
        exit /b 1
    )
)

echo [6/6] Building application...
call npx next build
if errorlevel 1 (
    echo ERROR: Build failed!
    echo Check the error messages above
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo   BUILD SUCCESSFUL!
echo ========================================
echo.
echo Starting server on: http://localhost:3000
echo Press Ctrl+C to stop the server
echo.

call npx next start -p 3000
