'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Plus, Edit, Trash2, DollarSign, Star, X, Save } from 'lucide-react'

interface Currency {
  id: number
  currency_name: string
  symbol: string
  exchange_rate: number
  is_base_currency: boolean
  created_at?: string
  updated_at?: string
}

export default function CurrenciesPage() {
  const [currencies, setCurrencies] = useState<Currency[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [showModal, setShowModal] = useState(false)
  const [editingId, setEditingId] = useState<number | null>(null)
  const [error, setError] = useState<string | null>(null)

  // بيانات النموذج
  const [formData, setFormData] = useState({
    currency_name: '',
    symbol: '',
    exchange_rate: 1,
    is_base_currency: false
  })

  // جلب العملات
  const fetchCurrencies = async () => {
    setIsLoading(true)
    setError(null)
    try {
      const response = await fetch('/api/currencies')
      const result = await response.json()

      if (result.success) {
        setCurrencies(result.data || [])
      } else {
        setError(result.error || 'فشل في جلب البيانات')
      }
    } catch (err) {
      setError('خطأ في الاتصال بالخادم')
      console.error('Error:', err)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchCurrencies()
  }, [])

  // فتح نموذج الإضافة
  const handleAdd = () => {
    setEditingId(null)
    setFormData({
      currency_name: '',
      symbol: '',
      exchange_rate: 1,
      is_base_currency: false
    })
    setError(null)
    setShowModal(true)
  }

  // فتح نموذج التعديل
  const handleEdit = (currency: Currency) => {
    setEditingId(currency.id)
    setFormData({
      currency_name: currency.currency_name,
      symbol: currency.symbol,
      exchange_rate: currency.exchange_rate,
      is_base_currency: currency.is_base_currency
    })
    setError(null)
    setShowModal(true)
  }

  // حفظ العملة
  const handleSave = async () => {
    if (!formData.currency_name.trim() || !formData.symbol.trim()) {
      setError('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    if (formData.exchange_rate <= 0) {
      setError('سعر الصرف يجب أن يكون أكبر من صفر')
      return
    }

    setIsLoading(true)
    try {
      const method = editingId ? 'PUT' : 'POST'
      const url = editingId ? `/api/currencies?id=${editingId}` : '/api/currencies'

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })

      const result = await response.json()

      if (result.success) {
        await fetchCurrencies()
        handleCancel()
      } else {
        setError(result.error || 'فشل في الحفظ')
      }
    } catch (err) {
      setError('خطأ في الحفظ')
      console.error('Error:', err)
    } finally {
      setIsLoading(false)
    }
  }

  // إلغاء النموذج
  const handleCancel = () => {
    setShowModal(false)
    setEditingId(null)
    setFormData({
      currency_name: '',
      symbol: '',
      exchange_rate: 1,
      is_base_currency: false
    })
    setError(null)
  }

  // حذف العملة
  const handleDelete = async (id: number) => {
    const currency = currencies.find(c => c.id === id)

    if (currency?.is_base_currency) {
      setError('لا يمكن حذف العملة الأساسية')
      return
    }

    if (!confirm('هل أنت متأكد من حذف هذه العملة؟')) return

    setIsLoading(true)
    try {
      const response = await fetch(`/api/currencies?id=${id}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (result.success) {
        await fetchCurrencies()
      } else {
        setError(result.error || 'فشل في الحذف')
      }
    } catch (err) {
      setError('خطأ في الحذف')
      console.error('Error:', err)
    } finally {
      setIsLoading(false)
    }
  }

  // تعيين العملة الأساسية
  const handleSetBaseCurrency = async (id: number) => {
    if (!confirm('هل تريد تعيين هذه العملة كعملة أساسية؟')) return

    setIsLoading(true)
    try {
      const response = await fetch(`/api/currencies/set-base?id=${id}`, {
        method: 'PUT'
      })

      const result = await response.json()

      if (result.success) {
        await fetchCurrencies()
      } else {
        setError(result.error || 'فشل في تعيين العملة الأساسية')
      }
    } catch (err) {
      setError('خطأ في تعيين العملة الأساسية')
      console.error('Error:', err)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <MainLayout>
      <div className="container mx-auto p-6 space-y-6">
        {/* العنوان والأزرار */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <DollarSign className="h-8 w-8 mr-3 text-green-600" />
              إدارة العملات
            </h1>
            <p className="text-gray-600 mt-1">إدارة العملات وأسعار الصرف</p>
          </div>
          <Button
            onClick={handleAdd}
            className="bg-blue-600 hover:bg-blue-700"
            disabled={isLoading}
          >
            <Plus className="h-4 w-4 mr-2" />
            إضافة عملة جديدة
          </Button>
        </div>

        {/* رسائل الخطأ */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        {/* جدول العملات */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <DollarSign className="h-5 w-5 mr-2" />
              قائمة العملات ({currencies.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading && !showModal ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">جاري التحميل...</p>
              </div>
            ) : currencies.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <DollarSign className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>لا توجد عملات مضافة</p>
                <p className="text-sm">اضغط "إضافة عملة جديدة" لإضافة أول عملة</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b bg-gray-50">
                      <th className="text-right p-3 font-semibold">اسم العملة</th>
                      <th className="text-right p-3 font-semibold">الرمز</th>
                      <th className="text-right p-3 font-semibold">سعر الصرف</th>
                      <th className="text-right p-3 font-semibold">النوع</th>
                      <th className="text-right p-3 font-semibold">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {currencies.map((currency) => (
                      <tr key={currency.id} className="border-b hover:bg-gray-50">
                        <td className="p-3">
                          <div className="font-medium">{currency.currency_name}</div>
                        </td>
                        <td className="p-3">
                          <Badge variant="outline" className="font-mono">
                            {currency.symbol}
                          </Badge>
                        </td>
                        <td className="p-3">
                          <div className="font-medium text-blue-600">
                            {currency.exchange_rate.toLocaleString()}
                          </div>
                        </td>
                        <td className="p-3">
                          {currency.is_base_currency ? (
                            <Badge className="bg-yellow-100 text-yellow-800">
                              <Star className="h-3 w-3 mr-1" />
                              عملة أساسية
                            </Badge>
                          ) : (
                            <Badge variant="secondary">
                              عملة فرعية
                            </Badge>
                          )}
                        </td>
                        <td className="p-3">
                          <div className="flex space-x-1 space-x-reverse">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEdit(currency)}
                              disabled={isLoading}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            {!currency.is_base_currency && (
                              <>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleSetBaseCurrency(currency.id)}
                                  disabled={isLoading}
                                  className="text-yellow-600 hover:text-yellow-700"
                                  title="تعيين كعملة أساسية"
                                >
                                  <Star className="h-4 w-4" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleDelete(currency.id)}
                                  disabled={isLoading}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* النافذة المنبثقة للإضافة/التعديل */}
        {showModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
              <div className="flex items-center justify-between p-6 border-b">
                <h2 className="text-xl font-semibold">
                  {editingId ? 'تعديل العملة' : 'إضافة عملة جديدة'}
                </h2>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancel}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="p-6 space-y-4">
                {/* اسم العملة */}
                <div>
                  <Label htmlFor="currency_name">اسم العملة *</Label>
                  <Input
                    id="currency_name"
                    value={formData.currency_name}
                    onChange={(e) => setFormData({...formData, currency_name: e.target.value})}
                    placeholder="مثال: دولار أمريكي"
                    className="mt-1"
                  />
                </div>

                {/* رمز العملة */}
                <div>
                  <Label htmlFor="symbol">رمز العملة *</Label>
                  <Input
                    id="symbol"
                    value={formData.symbol}
                    onChange={(e) => setFormData({...formData, symbol: e.target.value})}
                    placeholder="مثال: $"
                    className="mt-1"
                  />
                </div>

                {/* سعر الصرف */}
                <div>
                  <Label htmlFor="exchange_rate">سعر الصرف *</Label>
                  <Input
                    id="exchange_rate"
                    type="number"
                    step="0.01"
                    min="0.01"
                    value={formData.exchange_rate}
                    onChange={(e) => setFormData({...formData, exchange_rate: parseFloat(e.target.value) || 1})}
                    placeholder="1.00"
                    className="mt-1"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    كم ريال يمني يساوي وحدة واحدة من هذه العملة
                  </p>
                </div>

                {/* العملة الأساسية */}
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="checkbox"
                    id="is_base_currency"
                    checked={formData.is_base_currency}
                    onChange={(e) => setFormData({...formData, is_base_currency: e.target.checked})}
                    className="rounded"
                  />
                  <Label htmlFor="is_base_currency" className="text-sm">
                    تعيين كعملة أساسية
                  </Label>
                </div>
                {formData.is_base_currency && (
                  <p className="text-xs text-yellow-600 bg-yellow-50 p-2 rounded">
                    ⚠️ العملة الأساسية هي العملة المرجعية للنظام
                  </p>
                )}
              </div>

              {/* أزرار النافذة */}
              <div className="flex justify-end space-x-2 space-x-reverse p-6 border-t bg-gray-50">
                <Button
                  onClick={handleCancel}
                  variant="outline"
                  disabled={isLoading}
                >
                  إلغاء
                </Button>
                <Button
                  onClick={handleSave}
                  disabled={isLoading || !formData.currency_name.trim() || !formData.symbol.trim()}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isLoading ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      جاري الحفظ...
                    </div>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      {editingId ? 'تحديث' : 'حفظ'}
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}
