# Dependencies
node_modules/
/.pnp
.pnp.js

# Testing
/coverage

# Next.js
/.next/
/out/

# Production
/build

# Misc
.DS_Store
*.pem

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local env files
.env*.local

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
jspm_packages/

# Snowpack dependency directory (https://snowpack.dev/)
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# ========================================
# 🚫 FORBIDDEN SERVER FILES
# ========================================
# These files are NOT allowed in this project
# Only unified-server-manager.js is permitted

# Forbidden server files
server.js
start.js
unified-server.js
service-runner.js
app-server.js
express-server.js
http-server.js
web-server.js

# Forbidden startup scripts
start_*.ps1
start-*.ps1
run_*.ps1
run-*.ps1
launch_*.ps1
launch-*.ps1
boot_*.ps1
boot-*.ps1

# Forbidden batch files
start.bat
run.bat
launch.bat
boot.bat
server.bat

# Forbidden shell scripts
start.sh
run.sh
launch.sh
boot.sh
server.sh

# Forbidden service files
service-*.js
*-service.js
daemon-*.js
*-daemon.js

# Forbidden PM2 files (except ecosystem.config.json if needed)
pm2.config.js
process.json

# ========================================
# ✅ ALLOWED FILES
# ========================================
# Only these server-related files are allowed:
# - unified-server-manager.js (THE ONLY SERVER)
# - routing.config.json (REQUIRED CONFIG)
# - package.json (for npm scripts)
# ========================================
