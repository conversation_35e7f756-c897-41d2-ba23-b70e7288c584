import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'
import jwt from 'jsonwebtoken'

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'

// التحقق من صحة رمز العميل
async function verifyClientToken(request: NextRequest) {
  const authHeader = request.headers.get('authorization')
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null
  }

  const token = authHeader.substring(7)
  
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any
    if (decoded.type !== 'client') {
      return null
    }
    return decoded
  } catch (error) {
    return null
  }
}

// GET - جلب إشعارات العميل
export async function GET(request: NextRequest) {
  try {
    const clientToken = await verifyClientToken(request)
    if (!clientToken) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح بالوصول' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const unreadOnly = searchParams.get('unread_only') === 'true'
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = (page - 1) * limit

    let whereConditions = ['cn.client_id = $1']
    let queryParams: any[] = [clientToken.clientId]
    let paramIndex = 2

    // تصفية الإشعارات غير المقروءة فقط
    if (unreadOnly) {
      whereConditions.push('cn.is_read = false')
    }

    const whereClause = whereConditions.join(' AND ')

    // الاستعلام الرئيسي
    const notificationsQuery = `
      SELECT 
        cn.*,
        i.title as case_title,
        i.case_number,
        COUNT(*) OVER() as total_count
      FROM client_notifications cn
      LEFT JOIN issues i ON cn.case_id = i.id
      WHERE ${whereClause}
      ORDER BY cn.created_date DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `

    queryParams.push(limit, offset)
    const result = await query(notificationsQuery, queryParams)

    // جلب إحصائيات الإشعارات
    const statsQuery = `
      SELECT 
        COUNT(*) as total_notifications,
        COUNT(CASE WHEN is_read = false THEN 1 END) as unread_count,
        COUNT(CASE WHEN type = 'info' THEN 1 END) as info_count,
        COUNT(CASE WHEN type = 'warning' THEN 1 END) as warning_count,
        COUNT(CASE WHEN type = 'success' THEN 1 END) as success_count,
        COUNT(CASE WHEN type = 'error' THEN 1 END) as error_count
      FROM client_notifications
      WHERE client_id = $1
    `
    const statsResult = await query(statsQuery, [clientToken.clientId])

    return NextResponse.json({
      success: true,
      data: result.rows,
      pagination: {
        currentPage: page,
        totalPages: result.rows.length > 0 ? Math.ceil(parseInt(result.rows[0].total_count) / limit) : 0,
        totalCount: result.rows.length > 0 ? parseInt(result.rows[0].total_count) : 0
      },
      statistics: statsResult.rows[0]
    })

  } catch (error) {
    console.error('خطأ في جلب إشعارات العميل:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الإشعارات' },
      { status: 500 }
    )
  }
}

// PUT - تحديث حالة الإشعار (قراءة/عدم قراءة)
export async function PUT(request: NextRequest) {
  try {
    const clientToken = await verifyClientToken(request)
    if (!clientToken) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح بالوصول' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { notificationId, isRead, markAllAsRead } = body

    if (markAllAsRead) {
      // تحديد جميع الإشعارات كمقروءة
      const updateAllQuery = `
        UPDATE client_notifications 
        SET is_read = true, read_at = CURRENT_TIMESTAMP
        WHERE client_id = $1 AND is_read = false
        RETURNING id
      `
      
      const result = await query(updateAllQuery, [clientToken.clientId])
      
      return NextResponse.json({
        success: true,
        message: `تم تحديد ${result.rows.length} إشعار كمقروء`,
        updatedCount: result.rows.length
      })
    }

    if (!notificationId) {
      return NextResponse.json(
        { success: false, error: 'معرف الإشعار مطلوب' },
        { status: 400 }
      )
    }

    // تحديث إشعار محدد
    const updateQuery = `
      UPDATE client_notifications 
      SET 
        is_read = $2,
        read_at = CASE WHEN $2 = true THEN CURRENT_TIMESTAMP ELSE NULL END
      WHERE id = $1 AND client_id = $3
      RETURNING *
    `

    const result = await query(updateQuery, [notificationId, isRead, clientToken.clientId])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الإشعار غير موجود أو غير مصرح بالوصول إليه' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: isRead ? 'تم تحديد الإشعار كمقروء' : 'تم تحديد الإشعار كغير مقروء'
    })

  } catch (error) {
    console.error('خطأ في تحديث الإشعار:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الإشعار' },
      { status: 500 }
    )
  }
}

// POST - إنشاء طلب جديد من العميل
export async function POST(request: NextRequest) {
  try {
    const clientToken = await verifyClientToken(request)
    if (!clientToken) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح بالوصول' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      caseId,
      requestType,
      title,
      description,
      priority
    } = body

    // التحقق من البيانات المطلوبة
    if (!requestType || !title || !description) {
      return NextResponse.json(
        { success: false, error: 'البيانات المطلوبة مفقودة' },
        { status: 400 }
      )
    }

    // التحقق من ملكية القضية إذا تم تحديدها
    if (caseId) {
      const caseCheck = await query(
        'SELECT id FROM issues WHERE id = $1 AND client_id = $2',
        [caseId, clientToken.clientId]
      )
      
      if (caseCheck.rows.length === 0) {
        return NextResponse.json(
          { success: false, error: 'القضية غير موجودة أو غير مصرح بالوصول إليها' },
          { status: 403 }
        )
      }
    }

    // إنشاء الطلب
    const insertQuery = `
      INSERT INTO client_requests (
        client_id, case_id, request_type, title, description, priority
      ) VALUES (
        $1, $2, $3, $4, $5, $6
      ) RETURNING *
    `

    const values = [
      clientToken.clientId,
      caseId || null,
      requestType,
      title,
      description,
      priority || 'medium'
    ]

    const result = await query(insertQuery, values)

    // إنشاء إشعار للمحامين
    await query(`
      INSERT INTO client_notifications (client_id, case_id, title, message, type)
      VALUES ($1, $2, $3, $4, $5)
    `, [
      clientToken.clientId,
      caseId,
      'طلب جديد من العميل',
      `تم إنشاء طلب جديد: ${title}`,
      'info'
    ])

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم إنشاء الطلب بنجاح'
    })

  } catch (error) {
    console.error('خطأ في إنشاء الطلب:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إنشاء الطلب' },
      { status: 500 }
    )
  }
}

// DELETE - حذف إشعار
export async function DELETE(request: NextRequest) {
  try {
    const clientToken = await verifyClientToken(request)
    if (!clientToken) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح بالوصول' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const notificationId = searchParams.get('id')

    if (!notificationId) {
      return NextResponse.json(
        { success: false, error: 'معرف الإشعار مطلوب' },
        { status: 400 }
      )
    }

    const deleteQuery = `
      DELETE FROM client_notifications 
      WHERE id = $1 AND client_id = $2
      RETURNING id, title
    `

    const result = await query(deleteQuery, [parseInt(notificationId), clientToken.clientId])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الإشعار غير موجود أو غير مصرح بحذفه' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف الإشعار بنجاح'
    })

  } catch (error) {
    console.error('خطأ في حذف الإشعار:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الإشعار' },
      { status: 500 }
    )
  }
}