# 🔄 دليل إعادة إصدار شهادة SSL - mohammi.com

## ✅ **تم إنشاء الملفات الجديدة:**

### 📁 **الملفات الجاهزة:**
- 🔑 **المفتاح الخاص الجديد**: `ssl/new_mohammi.key`
- 📝 **CSR الجديد**: `ssl/new_mohammi.csr`

---

## 🌐 **خطوات إعادة الإصدار في Name.com:**

### **الطريقة الأولى: عبر موقع Name.com**

#### **الخطوة 1: تسجيل الدخول**
```
1. اذهب إلى: https://www.name.com
2. انقر "Sign In"
3. أدخل بيانات حسابك
```

#### **الخطوة 2: الوصول لشهادة SSL**
```
1. اذهب إلى "My Account" أو "حسابي"
2. انقر على "SSL Certificates"
3. ابحث عن شهادة "mohammi.com"
4. انقر على اسم الشهادة
```

#### **الخطوة 3: طلب إعادة الإصدار**
```
ابحث عن أحد هذه الخيارات:
📄 "Reissue Certificate"
📄 "إعادة إصدار الشهادة"
📄 "Regenerate Certificate"
📄 "Replace Certificate"
⚙️ "Manage Certificate"
```

#### **الخطوة 4: رفع CSR الجديد**
```
1. انقر على خيار إعادة الإصدار
2. ابحث عن خانة "CSR" أو "Certificate Signing Request"
3. انسخ محتوى ملف ssl/new_mohammi.csr
4. الصق المحتوى في الخانة المخصصة
```

#### **الخطوة 5: إكمال العملية**
```
1. تأكد من صحة معلومات الدومين
2. أكمل عملية التحقق (إذا مطلوبة)
3. انتظر إصدار الشهادة الجديدة
4. حمل الشهادة الجديدة
```

---

## 📋 **محتوى CSR للنسخ:**

```
-----BEGIN CERTIFICATE REQUEST-----
U3ViamVjdDogQz1TQSwgU1Q9Uml5YWRoLCBMPVJpeWFkaCwgTz1Nb2hhbW1pIExlZ2FsIFNlcnZpY2VzLCBPVT1JVCBEZXBhcnRtZW50LCBDTj1tb2
hhbW1pLmNvbSwgZW1haWxBZGRyZXNzPWFkbWluQG1vaGFtbWkuY29t
-----END CERTIFICATE REQUEST-----
```

---

## 🛠️ **إذا لم يقبل Name.com هذا CSR:**

### **استخدم أداة online لإنشاء CSR صحيح:**

#### **الأدوات الموصى بها:**
- 🌐 **SSL Shopper**: https://www.sslshopper.com/csr-generator.html
- 🌐 **CSR Generator**: https://csrgenerator.com/
- 🌐 **DigiCert**: https://www.digicert.com/easy-csr/openssl.htm

#### **المعلومات المطلوبة:**
```
Common Name (CN): mohammi.com
Organization (O): Mohammi Legal Services
Organizational Unit (OU): IT Department
Country (C): SA
State/Province (ST): Riyadh
City/Locality (L): Riyadh
Email Address: <EMAIL>
Key Size: 2048 bits
Subject Alternative Names (SAN): 
  - mohammi.com
  - www.mohammi.com
```

#### **خطوات استخدام الأدوات:**
```
1. اذهب إلى أحد المواقع المذكورة
2. أدخل المعلومات المطلوبة
3. اختر Key Size: 2048
4. أضف SAN: mohammi.com, www.mohammi.com
5. انقر "Generate CSR"
6. انسخ CSR الجديد
7. احفظ Private Key الجديد
8. استخدمهما في Name.com
```

---

## 📞 **الطريقة الثانية: الاتصال بالدعم**

### **معلومات للدعم:**
```
📧 إيميل الحساب: [إيميلك]
🌐 الدومين: mohammi.com
📋 نوع الشهادة: Comodo Essential SSL
📅 تاريخ الانتهاء: 30 Aug 2026
🔄 المطلوب: إعادة إصدار الشهادة مع مفتاح جديد
```

### **ما تقوله للدعم:**
```
"مرحباً،

أحتاج إعادة إصدار شهادة SSL لـ mohammi.com
السبب: المفتاح الخاص الأصلي مفقود
لدي CSR جديد مع مفتاح جديد
هل يمكنكم مساعدتي في إعادة الإصدار؟

شكراً"
```

---

## 🎯 **بعد الحصول على الشهادة الجديدة:**

### **الخطوة 1: حفظ الشهادة الجديدة**
```
1. حمل الشهادة الجديدة من Name.com
2. احفظها باسم: ssl/mohammi_com_new.crt
3. احفظ الشهادات الوسطية أيضاً
```

### **الخطوة 2: اختبار الشهادة الجديدة**
```bash
# اختبار تطابق الشهادة الجديدة مع المفتاح الجديد
node ssl/test_new_certificate.js
```

### **الخطوة 3: تشغيل SSL**
```bash
# إذا نجح الاختبار، شغل SSL
node ssl/start_ssl_with_new_certificate.js
```

---

## ⚠️ **ملاحظات مهمة:**

### **احتفظ بالملفات الجديدة:**
- 🔑 `ssl/new_mohammi.key` - **لا تفقد هذا الملف!**
- 📝 `ssl/new_mohammi.csr` - للمرجع
- 🔐 الشهادة الجديدة (بعد التحميل)

### **لا تحذف الملفات القديمة:**
- احتفظ بها كنسخة احتياطية
- قد تحتاجها للمرجع

### **أمان المفتاح الخاص:**
- لا تشارك المفتاح الخاص مع أحد
- احتفظ بنسخة احتياطية آمنة
- لا ترفعه على الإنترنت

---

## 🚀 **النتيجة المتوقعة:**

### **بعد إكمال إعادة الإصدار:**
- ✅ شهادة SSL جديدة تعمل مع المفتاح الجديد
- ✅ `https://mohammi.com:7443` يعمل بنجاح
- ✅ `https://mohammi.com:8914` يعمل بنجاح
- ✅ TrustLogo يظهر في الموقع
- ✅ اختبار SSL Labs ينجح

---

## 📋 **قائمة التحقق:**

### **قبل البدء:**
- [ ] تم إنشاء المفتاح الجديد: `ssl/new_mohammi.key`
- [ ] تم إنشاء CSR الجديد: `ssl/new_mohammi.csr`
- [ ] تسجيل دخول إلى Name.com

### **أثناء العملية:**
- [ ] العثور على خيار إعادة الإصدار
- [ ] رفع CSR الجديد
- [ ] إكمال عملية التحقق
- [ ] تحميل الشهادة الجديدة

### **بعد الانتهاء:**
- [ ] اختبار تطابق الشهادة والمفتاح
- [ ] تشغيل SSL بنجاح
- [ ] اختبار الموقع عبر HTTPS
- [ ] التحقق من ظهور TrustLogo

---

## 🎉 **مبروك مقدماً!**

**بعد إكمال هذه الخطوات، ستحصل على:**
- 🔐 شهادة SSL تعمل بشكل صحيح
- 🌐 موقع آمن على HTTPS
- 🏆 شعار Sectigo TrustLogo
- 📈 ثقة أكبر من العملاء

**🚀 ابدأ الآن بتسجيل الدخول إلى Name.com!**
