"use client"

import React, { useState } from 'react'

function qs(params: Record<string, any>) {
  const sp = new URLSearchParams()
  Object.entries(params).forEach(([k, v]) => {
    if (v !== undefined && v !== null && v !== '' && v !== 'all') sp.append(k, String(v))
  })
  return sp.toString()
}

export default function CasesReportsPage() {
  const [caseId, setCaseId] = useState('')
  const [clientId, setClientId] = useState('')
  const [employeeId, setEmployeeId] = useState('')
  const [dateFrom, setDateFrom] = useState('')
  const [dateTo, setDateTo] = useState('')
  const [dateField, setDateField] = useState<'created' | 'hearing'>('created')
  const [summary, setSummary] = useState(true)

  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [data, setData] = useState<any>(null)

  const search = async () => {
    try {
      setLoading(true)
      setError(null)
      setData(null)
      const query = qs({
        case_id: caseId || undefined,
        client_id: clientId || undefined,
        employee_id: employeeId || undefined,
        date_from: dateFrom || undefined,
        date_to: dateTo || undefined,
        date_field: dateField,
        summary: summary ? 'true' : 'false',
      })
      const res = await fetch(`/api/reports/cases?${query}`)
      const json = await res.json()
      if (!res.ok || json.success === false) throw new Error(json.error || 'فشل الجلب')
      setData(json)
    } catch (e: any) {
      setError(e.message || 'خطأ غير معروف')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-6 space-y-6">
      <div>
        <h1 className="text-2xl font-bold">تقرير القضايا</h1>
        <p className="text-gray-600">فلترة حسب القضية/العميل/الموظف والفترة</p>
      </div>

      <div className="space-y-4 bg-white rounded border p-4">
        <h2 className="font-semibold">المرشحات</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm text-gray-700 mb-1">معرف القضية</label>
            <input value={caseId} onChange={(e)=>setCaseId(e.target.value)} className="w-full border rounded p-2" placeholder="مثال: 101" />
          </div>
          <div>
            <label className="block text-sm text-gray-700 mb-1">معرف العميل</label>
            <input value={clientId} onChange={(e)=>setClientId(e.target.value)} className="w-full border rounded p-2" placeholder="clients.id" />
          </div>
          <div>
            <label className="block text-sm text-gray-700 mb-1">معرف الموظف</label>
            <input value={employeeId} onChange={(e)=>setEmployeeId(e.target.value)} className="w-full border rounded p-2" placeholder="اختياري" />
          </div>
          <div>
            <label className="block text-sm text-gray-700 mb-1">نوع التاريخ</label>
            <select value={dateField} onChange={(e)=>setDateField(e.target.value as any)} className="w-full border rounded p-2">
              <option value="created">تاريخ الإنشاء</option>
              <option value="hearing">تاريخ الجلسة/القادم</option>
            </select>
          </div>
          <div>
            <label className="block text-sm text-gray-700 mb-1">من تاريخ</label>
            <input type="date" value={dateFrom} onChange={(e)=>setDateFrom(e.target.value)} className="w-full border rounded p-2" />
          </div>
          <div>
            <label className="block text-sm text-gray-700 mb-1">إلى تاريخ</label>
            <input type="date" value={dateTo} onChange={(e)=>setDateTo(e.target.value)} className="w-full border rounded p-2" />
          </div>
          <div className="flex items-center gap-2">
            <input id="summary" type="checkbox" checked={summary} onChange={(e)=>setSummary(e.target.checked)} />
            <label htmlFor="summary" className="text-sm text-gray-700">ملخص</label>
          </div>
        </div>
        <div className="flex gap-3">
          <button onClick={search} className="px-4 py-2 rounded bg-blue-600 text-white">بحث</button>
          <button onClick={()=>{setData(null); setError(null)}} className="px-4 py-2 rounded bg-gray-100">مسح النتائج</button>
        </div>
      </div>

      <div className="bg-white rounded border p-4">
        <h2 className="font-semibold mb-3">النتائج</h2>
        {loading && <div className="text-gray-600">جاري التحميل...</div>}
        {error && <div className="text-red-600">خطأ: {error}</div>}
        {!loading && !error && data && (
          <div className="overflow-x-auto">
            {data.summary && (
              <div className="mb-4 p-3 bg-gray-50 rounded border text-sm">
                <div>إجمالي القضايا: <b>{data.summary.total_issues}</b></div>
                <div>الإجمالي المالي: <b>{data.summary.total_amount}</b></div>
                <div>جديدة: {data.summary.new_count} | قيد المعالجة: {data.summary.in_progress_count} | مغلقة: {data.summary.closed_count}</div>
              </div>
            )}
            <table className="min-w-full text-sm">
              <thead className="bg-gray-100">
                <tr>
                  <th className="p-2 text-right">رقم القضية</th>
                  <th className="p-2 text-right">العنوان</th>
                  <th className="p-2 text-right">الحالة</th>
                  <th className="p-2 text-right">العميل</th>
                  <th className="p-2 text-right">المحكمة</th>
                  <th className="p-2 text-right">المبلغ</th>
                  <th className="p-2 text-right">تاريخ الإنشاء</th>
                  <th className="p-2 text-right">الجلسة القادمة</th>
                </tr>
              </thead>
              <tbody>
                {data.data?.map((r: any) => (
                  <tr key={r.id} className="border-b">
                    <td className="p-2">{r.case_number}</td>
                    <td className="p-2">{r.title}</td>
                    <td className="p-2">{r.status}</td>
                    <td className="p-2">{r.client_name}</td>
                    <td className="p-2">{r.court_name}</td>
                    <td className="p-2">{r.case_amount}</td>
                    <td className="p-2">{r.created_date?.slice(0,10)}</td>
                    <td className="p-2">{r.next_hearing?.slice(0,10)}</td>
                  </tr>
                ))}
                {(!data.data || data.data.length === 0) && (
                  <tr><td className="p-3 text-center text-gray-500" colSpan={8}>لا توجد نتائج</td></tr>
                )}
              </tbody>
            </table>
          </div>
        )}
        {!loading && !error && !data && <div className="text-gray-500">اضغط على زر بحث لعرض النتائج</div>}
      </div>
    </div>
  )
}
