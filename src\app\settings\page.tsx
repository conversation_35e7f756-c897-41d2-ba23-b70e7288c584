'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Settings,
  Users,
  Building2,
  FileText,
  Globe,
  Navigation,
  DollarSign,
  Shield,
  Bell,
  Database,
  MessageSquare,
  ChevronRight,
  Lock,
  Unlock,
  Smartphone
} from 'lucide-react'
import Link from 'next/link'

interface SettingCard {
  title: string
  description: string
  icon: React.ReactNode
  href: string
  permission?: string
  adminOnly?: boolean
  badge?: string
  color: string
}

export default function SettingsPage() {
  const { user, isAdmin, hasPermission } = useAuth()
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    setLoading(false)
  }, [])

  const settingsCards: SettingCard[] = [
    {
      title: 'إدارة المستخدمين',
      description: 'إضافة وتعديل وحذف المستخدمين وإدارة الصلاحيات',
      icon: <Users className="h-6 w-6" />,
      href: '/users',
      permission: 'users:view',
      adminOnly: false,
      color: 'bg-blue-500'
    },
    {
      title: 'إدارة الأقسام',
      description: 'إضافة وتعديل الأقسام والوحدات التنظيمية',
      icon: <Building2 className="h-6 w-6" />,
      href: '/settings/departments',
      permission: 'departments:view',
      adminOnly: false,
      color: 'bg-green-500'
    },
    {
      title: 'مراكز التكلفة',
      description: 'إدارة مراكز التكلفة والأقسام المالية',
      icon: <DollarSign className="h-6 w-6" />,
      href: '/settings/cost-centers',
      permission: 'cost_centers:view',
      adminOnly: false,
      color: 'bg-yellow-500'
    },
    {
      title: 'روابط التذييل',
      description: 'إدارة الروابط التي تظهر في أسفل الموقع',
      icon: <Globe className="h-6 w-6" />,
      href: '/settings/footer-links',
      permission: 'footer_links:view',
      adminOnly: false,
      color: 'bg-purple-500'
    },
    {
      title: 'صفحات التنقل',
      description: 'إدارة صفحات النظام للبحث الذكي',
      icon: <Navigation className="h-6 w-6" />,
      href: '/settings/navigation-pages',
      permission: 'navigation_pages:view',
      adminOnly: false,
      color: 'bg-indigo-500'
    },
    {
      title: 'إدارة العملات',
      description: 'إدارة العملات وأسعار الصرف والعملة الأساسية',
      icon: <DollarSign className="h-6 w-6" />,
      href: '/settings/currencies',
      permission: 'currencies:view',
      adminOnly: false,
      badge: 'جديد',
      color: 'bg-green-600'
    },
    {
      title: 'الإعلانات',
      description: 'إدارة الإعلانات والرسائل العامة',
      icon: <Bell className="h-6 w-6" />,
      href: '/settings/announcements',
      permission: 'announcements:view',
      adminOnly: false,
      color: 'bg-red-500'
    },
    {
      title: 'إدارة الخادم',
      description: 'إعدادات الخادم والمنافذ والقواعد',
      icon: <Database className="h-6 w-6" />,
      href: '/admin/server-management',
      adminOnly: true,
      color: 'bg-gray-700'
    },
    {
      title: 'إعدادات الذكاء الاصطناعي',
      description: 'إعدادات المساعد الذكي والرد التلقائي',
      icon: <Shield className="h-6 w-6" />,
      href: '/admin/ai-settings',
      adminOnly: true,
      color: 'bg-cyan-500'
    },
    {
      title: 'إعدادات WhatsApp',
      description: 'إدارة خدمة WhatsApp والإشعارات التلقائية',
      icon: <MessageSquare className="h-6 w-6" />,
      href: '/settings/whatsapp',
      permission: 'whatsapp:view',
      adminOnly: false,
      badge: 'جديد',
      color: 'bg-green-500'
    },
    {
      title: 'إدارة الأجهزة',
      description: 'إدارة معرفات الأجهزة المصرح بها للمستخدمين',
      icon: <Smartphone className="h-6 w-6" />,
      href: '/settings/device-management',
      permission: 'devices:view',
      adminOnly: true,
      badge: 'أمان',
      color: 'bg-blue-500'
    }
  ]

  // تصفية البطاقات حسب الصلاحيات
  const filteredCards = settingsCards.filter(card => {
    if (card.adminOnly) {
      return isAdmin()
    }
    if (card.permission) {
      return isAdmin() || hasPermission(card.permission)
    }
    return true
  })

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">جاري التحميل...</p>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Settings className="h-8 w-8 mr-3 text-blue-600" />
              إعدادات النظام
            </h1>
            <p className="text-gray-600 mt-2">إدارة إعدادات النظام والصلاحيات</p>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-sm">
              {user?.role === 'admin' ? 'مدير النظام' : user?.role || 'مستخدم'}
            </Badge>
            <Badge variant={isAdmin() ? 'default' : 'secondary'} className="text-sm">
              {isAdmin() ? 'صلاحيات كاملة' : 'صلاحيات محدودة'}
            </Badge>
          </div>
        </div>

        {/* بطاقات الإعدادات */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCards.map((card, index) => (
            <Link key={index} href={card.href}>
              <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer group">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className={`p-3 rounded-lg ${card.color} text-white group-hover:scale-110 transition-transform duration-200`}>
                      {card.icon}
                    </div>
                    <div className="flex items-center space-x-2">
                      {card.adminOnly && (
                        <Badge variant="destructive" className="text-xs">
                          <Lock className="h-3 w-3 mr-1" />
                          مدير فقط
                        </Badge>
                      )}
                      {card.permission && !card.adminOnly && (
                        <Badge variant="secondary" className="text-xs">
                          <Unlock className="h-3 w-3 mr-1" />
                          بصلاحية
                        </Badge>
                      )}
                      <ChevronRight className="h-4 w-4 text-gray-400 group-hover:text-gray-600 transition-colors" />
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardTitle className="text-lg mb-2 group-hover:text-blue-600 transition-colors">
                    {card.title}
                  </CardTitle>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {card.description}
                  </p>
                  {card.badge && (
                    <Badge className="mt-3" variant="outline">
                      {card.badge}
                    </Badge>
                  )}
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        {/* معلومات إضافية */}
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="pt-6">
            <div className="flex items-start space-x-4">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Shield className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h3 className="font-semibold text-blue-900 mb-2">معلومات الصلاحيات</h3>
                <p className="text-blue-700 text-sm leading-relaxed">
                  يمكن للمديرين الوصول إلى جميع الإعدادات. المستخدمون العاديون يمكنهم الوصول فقط للإعدادات التي لديهم صلاحية لها.
                  {isAdmin() && (
                    <span className="block mt-2 font-medium">
                      أنت تملك صلاحيات كاملة كمدير للنظام.
                    </span>
                  )}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
