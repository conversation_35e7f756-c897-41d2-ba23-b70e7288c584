'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  Plus,
  Edit,
  Trash2,
  ExternalLink,
  Save,
  X,
  Link as LinkIcon,
  Globe,
  ArrowUpDown
} from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface FooterLink {
  id: number
  category: string
  name: string
  href: string
  sort_order: number
  is_active: boolean
  created_date: string
  updated_at: string
}

export default function FooterLinksPage() {
  const [links, setLinks] = useState<FooterLink[]>([])
  const [loading, setLoading] = useState(true)
  const [showDialog, setShowDialog] = useState(false)
  const [editingLink, setEditingLink] = useState<FooterLink | null>(null)
  const [formData, setFormData] = useState({
    category: '',
    name: '',
    href: '',
    sort_order: 0,
    is_active: true
  })

  const categories = [
    'روابط سريعة',
    'خدماتنا',
    'المستندات القانونية'
  ]

  useEffect(() => {
    fetchLinks()
  }, [])

  const fetchLinks = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/footer-links')
      const data = await response.json()
      
      if (data.success) {
        setLinks(data.data)
      }
    } catch (error) {
      console.error('Error fetching links:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const method = editingLink ? 'PUT' : 'POST'
      const url = editingLink ? `/api/footer-links/${editingLink.id}` : '/api/footer-links'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })
      
      const data = await response.json()
      
      if (data.success) {
        await fetchLinks()
        setShowDialog(false)
        setEditingLink(null)
        setFormData({
          category: '',
          name: '',
          href: '',
          sort_order: 0,
          is_active: true
        })
      }
    } catch (error) {
      console.error('Error saving link:', error)
    }
  }

  const handleEdit = (link: FooterLink) => {
    setEditingLink(link)
    setFormData({
      category: link.category,
      name: link.name,
      href: link.href,
      sort_order: link.sort_order,
      is_active: link.is_active
    })
    setShowDialog(true)
  }

  const handleDelete = async (id: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا الرابط؟')) return
    
    try {
      const response = await fetch(`/api/footer-links/${id}`, {
        method: 'DELETE'
      })
      
      const data = await response.json()
      
      if (data.success) {
        await fetchLinks()
      }
    } catch (error) {
      console.error('Error deleting link:', error)
    }
  }

  const handleToggleActive = async (link: FooterLink) => {
    try {
      const response = await fetch(`/api/footer-links/${link.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...link,
          is_active: !link.is_active
        })
      })

      const data = await response.json()
      if (data.success) {
        await fetchLinks()
      }
    } catch (error) {
      console.error('Error toggling link status:', error)
    }
  }

  const groupedLinks = links.reduce((acc, link) => {
    if (!acc[link.category]) {
      acc[link.category] = []
    }
    acc[link.category].push(link)
    return acc
  }, {} as Record<string, FooterLink[]>)

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة روابط التذييل</h1>
            <p className="text-gray-600 mt-2">إدارة الروابط التي تظهر في أسفل الموقع الرئيسي</p>
          </div>
          <div className="flex gap-2">
            <Button 
              onClick={() => setShowDialog(true)}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Plus className="h-4 w-4 mr-2" />
              إضافة رابط جديد
            </Button>
            <Button
              onClick={() => window.open('/home', '_blank')}
              variant="outline"
            >
              <Globe className="h-4 w-4 mr-2" />
              معاينة الموقع
            </Button>
          </div>
        </div>

        {/* الروابط مجمعة حسب الفئة */}
        {Object.entries(groupedLinks).map(([category, categoryLinks]) => (
          <Card key={category} className="shadow-lg">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-cyan-50 border-b">
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center">
                  <LinkIcon className="h-5 w-5 mr-2 text-blue-600" />
                  {category} ({categoryLinks.length})
                </span>
                <Badge variant="outline" className="text-blue-600">
                  {categoryLinks.filter(l => l.is_active).length} نشط
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              {categoryLinks.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  لا توجد روابط في هذه الفئة
                </div>
              ) : (
                <div className="divide-y">
                  {categoryLinks.map((link) => (
                    <div key={link.id} className="p-4 hover:bg-gray-50 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center mb-2">
                            <h3 className="font-semibold text-gray-900 mr-3">{link.name}</h3>
                            <Badge 
                              variant={link.is_active ? "default" : "secondary"}
                              className={link.is_active ? "bg-green-100 text-green-800" : ""}
                            >
                              {link.is_active ? 'نشط' : 'غير نشط'}
                            </Badge>
                          </div>
                          <div className="flex items-center text-sm text-gray-600">
                            <ExternalLink className="h-4 w-4 mr-1" />
                            <a 
                              href={link.href} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:text-blue-800 underline"
                            >
                              {link.href}
                            </a>
                            <span className="mx-2">|</span>
                            <ArrowUpDown className="h-4 w-4 mr-1" />
                            <span>ترتيب: {link.sort_order}</span>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2 space-x-reverse">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleToggleActive(link)}
                            className={link.is_active ? "text-orange-600 hover:text-orange-700" : "text-green-600 hover:text-green-700"}
                          >
                            {link.is_active ? 'إلغاء تفعيل' : 'تفعيل'}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEdit(link)}
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelete(link.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        ))}

        {/* نموذج إضافة/تعديل */}
        <Dialog open={showDialog} onOpenChange={setShowDialog}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>
                {editingLink ? 'تعديل الرابط' : 'إضافة رابط جديد'}
              </DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="category">الفئة *</Label>
                <Select 
                  value={formData.category} 
                  onValueChange={(value) => setFormData({...formData, category: value})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر الفئة" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="name">اسم الرابط *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  placeholder="أدخل اسم الرابط"
                  required
                />
              </div>

              <div>
                <Label htmlFor="href">الرابط *</Label>
                <Input
                  id="href"
                  value={formData.href}
                  onChange={(e) => setFormData({...formData, href: e.target.value})}
                  placeholder="/home أو https://example.com"
                  required
                />
              </div>

              <div>
                <Label htmlFor="sort_order">ترتيب العرض</Label>
                <Input
                  id="sort_order"
                  type="number"
                  value={formData.sort_order}
                  onChange={(e) => setFormData({...formData, sort_order: parseInt(e.target.value) || 0})}
                  placeholder="0"
                />
              </div>

              <div className="flex items-center justify-between pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setShowDialog(false)
                    setEditingLink(null)
                    setFormData({
                      category: '',
                      name: '',
                      href: '',
                      sort_order: 0,
                      is_active: true
                    })
                  }}
                >
                  <X className="h-4 w-4 mr-2" />
                  إلغاء
                </Button>
                <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
                  <Save className="h-4 w-4 mr-2" />
                  {editingLink ? 'تحديث' : 'إضافة'}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  )
}
