const { Client } = require('pg');

async function compareDatabases() {
  console.log('🔍 مقارنة قواعد البيانات mohammi و mohammidev...\n');

  const databases = ['mohammi', 'mohammidev'];
  const results = {};

  for (const dbName of databases) {
    console.log(`📊 فحص قاعدة البيانات: ${dbName}`);
    console.log('='.repeat(50));

    const client = new Client({
      host: 'localhost',
      port: 5432,
      user: 'postgres',
      password: 'yemen123',
      database: dbName,
      connectTimeoutMillis: 10000
    });

    try {
      await client.connect();
      console.log(`✅ متصل بقاعدة البيانات ${dbName}`);

      // 1. عدد الجداول
      const tablesResult = await client.query(`
        SELECT COUNT(*) as table_count 
        FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
      `);
      const tableCount = parseInt(tablesResult.rows[0].table_count);

      // 2. قائمة الجداول
      const tableListResult = await client.query(`
        SELECT table_name, 
               (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name AND table_schema = 'public') as column_count
        FROM information_schema.tables t
        WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
        ORDER BY table_name
      `);

      // 3. فحص الجداول المهمة
      const importantTables = [
        'users', 'clients', 'issues', 'case_movements', 'companies', 
        'chart_of_accounts', 'journal_entries', 'journal_entry_details',
        'vouchers', 'payments', 'documents', 'notifications'
      ];

      const tableDetails = {};
      for (const tableName of importantTables) {
        try {
          const countResult = await client.query(`SELECT COUNT(*) as count FROM ${tableName}`);
          const count = parseInt(countResult.rows[0].count);
          
          // آخر تحديث إذا كان هناك عمود created_at أو updated_at
          let lastUpdate = null;
          try {
            const updateResult = await client.query(`
              SELECT MAX(GREATEST(
                COALESCE(created_at, '1970-01-01'::timestamp),
                COALESCE(updated_at, '1970-01-01'::timestamp)
              )) as last_update 
              FROM ${tableName}
            `);
            lastUpdate = updateResult.rows[0].last_update;
          } catch (e) {
            // الجدول قد لا يحتوي على هذه الأعمدة
          }

          tableDetails[tableName] = { count, lastUpdate };
        } catch (error) {
          tableDetails[tableName] = { error: 'الجدول غير موجود' };
        }
      }

      // 4. حجم قاعدة البيانات
      const sizeResult = await client.query(`
        SELECT pg_size_pretty(pg_database_size('${dbName}')) as size
      `);
      const dbSize = sizeResult.rows[0].size;

      // 5. آخر تعديل على قاعدة البيانات
      const lastModResult = await client.query(`
        SELECT MAX(GREATEST(
          COALESCE(created_at, '1970-01-01'::timestamp),
          COALESCE(updated_at, '1970-01-01'::timestamp)
        )) as last_modification
        FROM (
          SELECT created_at, updated_at FROM users WHERE created_at IS NOT NULL OR updated_at IS NOT NULL
          UNION ALL
          SELECT created_at, updated_at FROM clients WHERE created_at IS NOT NULL OR updated_at IS NOT NULL
          UNION ALL
          SELECT created_at, updated_at FROM issues WHERE created_at IS NOT NULL OR updated_at IS NOT NULL
        ) combined
      `);

      results[dbName] = {
        tableCount,
        tables: tableListResult.rows,
        tableDetails,
        dbSize,
        lastModification: lastModResult.rows[0]?.last_modification,
        connected: true
      };

      console.log(`📈 عدد الجداول: ${tableCount}`);
      console.log(`💾 حجم قاعدة البيانات: ${dbSize}`);
      console.log(`📅 آخر تعديل: ${results[dbName].lastModification || 'غير محدد'}`);
      console.log('');

    } catch (error) {
      console.log(`❌ خطأ في الاتصال بقاعدة البيانات ${dbName}:`, error.message);
      results[dbName] = { error: error.message, connected: false };
    } finally {
      await client.end();
    }
  }

  // مقارنة النتائج
  console.log('\n🔍 مقارنة شاملة بين القاعدتين:');
  console.log('='.repeat(60));

  if (results.mohammi.connected && results.mohammidev.connected) {
    console.log('\n📊 الإحصائيات العامة:');
    console.log(`mohammi     - الجداول: ${results.mohammi.tableCount}, الحجم: ${results.mohammi.dbSize}`);
    console.log(`mohammidev  - الجداول: ${results.mohammidev.tableCount}, الحجم: ${results.mohammidev.dbSize}`);

    console.log('\n📋 مقارنة الجداول المهمة:');
    const importantTables = ['users', 'clients', 'issues', 'case_movements', 'companies', 'chart_of_accounts'];
    
    for (const table of importantTables) {
      const mohammiData = results.mohammi.tableDetails[table];
      const mohammidevData = results.mohammidev.tableDetails[table];
      
      console.log(`\n${table}:`);
      if (mohammiData && !mohammiData.error) {
        console.log(`  mohammi:    ${mohammiData.count} سجل`);
      } else {
        console.log(`  mohammi:    ${mohammiData?.error || 'غير متاح'}`);
      }
      
      if (mohammidevData && !mohammidevData.error) {
        console.log(`  mohammidev: ${mohammidevData.count} سجل`);
      } else {
        console.log(`  mohammidev: ${mohammidevData?.error || 'غير متاح'}`);
      }
    }

    // تحديد القاعدة الأحدث
    console.log('\n🎯 التوصية:');
    console.log('='.repeat(30));

    let recommendation = '';
    let reasons = [];

    // مقارنة عدد الجداول
    if (results.mohammi.tableCount > results.mohammidev.tableCount) {
      reasons.push(`mohammi تحتوي على جداول أكثر (${results.mohammi.tableCount} مقابل ${results.mohammidev.tableCount})`);
    } else if (results.mohammidev.tableCount > results.mohammi.tableCount) {
      reasons.push(`mohammidev تحتوي على جداول أكثر (${results.mohammidev.tableCount} مقابل ${results.mohammi.tableCount})`);
    }

    // مقارنة البيانات
    let mohammiDataCount = 0;
    let mohammidevDataCount = 0;

    for (const table of importantTables) {
      if (results.mohammi.tableDetails[table] && !results.mohammi.tableDetails[table].error) {
        mohammiDataCount += results.mohammi.tableDetails[table].count;
      }
      if (results.mohammidev.tableDetails[table] && !results.mohammidev.tableDetails[table].error) {
        mohammidevDataCount += results.mohammidev.tableDetails[table].count;
      }
    }

    if (mohammiDataCount > mohammidevDataCount) {
      reasons.push(`mohammi تحتوي على بيانات أكثر (${mohammiDataCount} مقابل ${mohammidevDataCount} سجل)`);
      recommendation = 'mohammi';
    } else if (mohammidevDataCount > mohammiDataCount) {
      reasons.push(`mohammidev تحتوي على بيانات أكثر (${mohammidevDataCount} مقابل ${mohammiDataCount} سجل)`);
      recommendation = 'mohammidev';
    }

    // مقارنة آخر تعديل
    if (results.mohammi.lastModification && results.mohammidev.lastModification) {
      const mohammiDate = new Date(results.mohammi.lastModification);
      const mohammidevDate = new Date(results.mohammidev.lastModification);
      
      if (mohammiDate > mohammidevDate) {
        reasons.push(`mohammi تم تحديثها مؤخراً (${mohammiDate.toLocaleString('ar')})`);
        if (!recommendation) recommendation = 'mohammi';
      } else if (mohammidevDate > mohammiDate) {
        reasons.push(`mohammidev تم تحديثها مؤخراً (${mohammidevDate.toLocaleString('ar')})`);
        if (!recommendation) recommendation = 'mohammidev';
      }
    }

    if (recommendation) {
      console.log(`🏆 القاعدة المُوصى بها: ${recommendation}`);
      console.log('\n📝 الأسباب:');
      reasons.forEach(reason => console.log(`  • ${reason}`));
    } else {
      console.log('⚖️ القاعدتان متشابهتان، يمكن استخدام أي منهما');
    }

    console.log('\n🔧 لتحديث إعدادات المشروع:');
    console.log(`1. افتح routing.config.json`);
    console.log(`2. غير database للمنفذ 3300 إلى: "${recommendation || 'mohammi'}"`);
    console.log(`3. أعد تشغيل الخادم`);

  } else {
    console.log('❌ لا يمكن المقارنة - إحدى القواعد أو كلاهما غير متاح');
  }
}

compareDatabases().catch(console.error);
