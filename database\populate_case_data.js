/**
 * إضافة البيانات التجريبية لنظام تتبع القضايا
 */

const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'mohammidev'
};

async function populateCaseData() {
  const client = new Client(dbConfig);
  
  try {
    await client.connect();
    console.log('🔗 متصل بقاعدة البيانات');

    // 1. إضافة حركات للقضايا الموجودة
    console.log('📝 إضافة حركات القضايا...');
    
    const movements = [
      {
        case_id: 1,
        movement_type: 'case_created',
        description: 'تم إنشاء القضية وتسجيلها في النظام',
        employee_id: 1,
        priority_level: 'medium'
      },
      {
        case_id: 1,
        movement_type: 'document_review',
        description: 'مراجعة الوثائق المقدمة من العميل',
        employee_id: 1,
        priority_level: 'high'
      },
      {
        case_id: 1,
        movement_type: 'court_filing',
        description: 'تقديم الدعوى للمحكمة المختصة',
        employee_id: 1,
        priority_level: 'high'
      },
      {
        case_id: 2,
        movement_type: 'case_created',
        description: 'تم إنشاء قضية عمالية جديدة',
        employee_id: 2,
        priority_level: 'medium'
      },
      {
        case_id: 2,
        movement_type: 'client_meeting',
        description: 'اجتماع مع العميل لمناقشة تفاصيل القضية',
        employee_id: 2,
        priority_level: 'medium'
      },
      {
        case_id: 3,
        movement_type: 'case_created',
        description: 'تم إنشاء قضية مدنية للتعويض',
        employee_id: 1,
        priority_level: 'low'
      },
      {
        case_id: 3,
        movement_type: 'evidence_collection',
        description: 'جمع الأدلة والشهادات المطلوبة',
        employee_id: 1,
        priority_level: 'medium'
      }
    ];

    for (const movement of movements) {
      try {
        await client.query(`
          INSERT INTO case_movements (case_id, movement_type, description, employee_id, priority_level, movement_date)
          VALUES ($1, $2, $3, $4, $5, NOW() - INTERVAL '${Math.floor(Math.random() * 10)} days')
        `, [movement.case_id, movement.movement_type, movement.description, movement.employee_id, movement.priority_level]);
      } catch (error) {
        console.log(`⚠️ تخطي حركة مكررة: ${movement.description}`);
      }
    }
    console.log('✅ تم إضافة حركات القضايا');

    // 2. إضافة جلسات المحكمة
    console.log('📅 إضافة جلسات المحكمة...');
    
    const sessions = [
      {
        case_id: 1,
        session_date: '2025-01-15 10:00:00',
        court_name: 'المحكمة التجارية',
        session_type: 'hearing',
        reminder_days: 3,
        notes: 'جلسة أولى للنظر في الدعوى'
      },
      {
        case_id: 1,
        session_date: '2025-01-25 14:00:00',
        court_name: 'المحكمة التجارية',
        session_type: 'follow_up',
        reminder_days: 3,
        notes: 'جلسة متابعة'
      },
      {
        case_id: 2,
        session_date: '2025-01-12 09:00:00',
        court_name: 'محكمة العمل',
        session_type: 'hearing',
        reminder_days: 2,
        notes: 'جلسة استماع للطرفين'
      },
      {
        case_id: 3,
        session_date: '2025-01-20 11:00:00',
        court_name: 'المحكمة المدنية',
        session_type: 'preliminary',
        reminder_days: 5,
        notes: 'جلسة تمهيدية'
      }
    ];

    for (const session of sessions) {
      try {
        await client.query(`
          INSERT INTO court_sessions (case_id, session_date, court_name, session_type, reminder_days, notes)
          VALUES ($1, $2, $3, $4, $5, $6)
        `, [session.case_id, session.session_date, session.court_name, session.session_type, session.reminder_days, session.notes]);
      } catch (error) {
        console.log(`⚠️ تخطي جلسة مكررة: ${session.notes}`);
      }
    }
    console.log('✅ تم إضافة جلسات المحكمة');

    // 3. إضافة مراحل القضايا
    console.log('🔄 إضافة مراحل القضايا...');
    
    const stages = [
      // مراحل القضية الأولى
      {
        case_id: 1,
        stage_name: 'تسجيل القضية',
        stage_type: 'initial',
        status: 'completed',
        start_date: '2025-01-01',
        responsible_employee_id: 1,
        order_index: 1
      },
      {
        case_id: 1,
        stage_name: 'جمع الأدلة',
        stage_type: 'preparation',
        status: 'in_progress',
        start_date: '2025-01-03',
        responsible_employee_id: 1,
        order_index: 2
      },
      {
        case_id: 1,
        stage_name: 'المرافعة',
        stage_type: 'court',
        status: 'pending',
        start_date: null,
        responsible_employee_id: 1,
        order_index: 3
      },
      // مراحل القضية الثانية
      {
        case_id: 2,
        stage_name: 'تسجيل القضية',
        stage_type: 'initial',
        status: 'completed',
        start_date: '2025-01-02',
        responsible_employee_id: 2,
        order_index: 1
      },
      {
        case_id: 2,
        stage_name: 'دراسة الحالة',
        stage_type: 'preparation',
        status: 'in_progress',
        start_date: '2025-01-04',
        responsible_employee_id: 2,
        order_index: 2
      },
      // مراحل القضية الثالثة
      {
        case_id: 3,
        stage_name: 'تسجيل القضية',
        stage_type: 'initial',
        status: 'completed',
        start_date: '2025-01-03',
        responsible_employee_id: 1,
        order_index: 1
      },
      {
        case_id: 3,
        stage_name: 'تقييم الأضرار',
        stage_type: 'assessment',
        status: 'pending',
        start_date: null,
        responsible_employee_id: 1,
        order_index: 2
      }
    ];

    for (const stage of stages) {
      try {
        await client.query(`
          INSERT INTO case_stages (case_id, stage_name, stage_type, status, start_date, responsible_employee_id, order_index)
          VALUES ($1, $2, $3, $4, $5, $6, $7)
        `, [stage.case_id, stage.stage_name, stage.stage_type, stage.status, stage.start_date, stage.responsible_employee_id, stage.order_index]);
      } catch (error) {
        console.log(`⚠️ تخطي مرحلة مكررة: ${stage.stage_name}`);
      }
    }
    console.log('✅ تم إضافة مراحل القضايا');

    // 4. إضافة إشعارات تجريبية
    console.log('🔔 إضافة الإشعارات...');
    
    const notifications = [
      {
        case_id: 1,
        recipient_type: 'client',
        recipient_id: 1,
        notification_type: 'session_reminder',
        title: 'تذكير بموعد الجلسة',
        message: 'لديك جلسة محكمة غداً في تمام الساعة 10:00 صباحاً',
        scheduled_date: '2025-01-14 09:00:00',
        status: 'pending'
      },
      {
        case_id: 1,
        recipient_type: 'employee',
        recipient_id: 1,
        notification_type: 'session_reminder',
        title: 'تذكير بموعد الجلسة',
        message: 'جلسة القضية رقم CASE-2025-001 غداً',
        scheduled_date: '2025-01-14 08:00:00',
        status: 'pending'
      },
      {
        case_id: 2,
        recipient_type: 'client',
        recipient_id: 2,
        notification_type: 'case_update',
        title: 'تحديث حالة القضية',
        message: 'تم تحديث حالة قضيتكم',
        scheduled_date: '2025-01-10 10:00:00',
        status: 'sent'
      }
    ];

    for (const notification of notifications) {
      try {
        await client.query(`
          INSERT INTO case_notifications (case_id, recipient_type, recipient_id, notification_type, title, message, scheduled_date, status)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        `, [notification.case_id, notification.recipient_type, notification.recipient_id, notification.notification_type, notification.title, notification.message, notification.scheduled_date, notification.status]);
      } catch (error) {
        console.log(`⚠️ تخطي إشعار مكرر: ${notification.title}`);
      }
    }
    console.log('✅ تم إضافة الإشعارات');

    // 5. تحديث تواريخ آخر تحديث للقضايا
    await client.query(`
      UPDATE cases SET updated_at = CURRENT_TIMESTAMP WHERE id IN (1, 2, 3);
    `);
    console.log('✅ تم تحديث تواريخ القضايا');

    // عرض ملخص البيانات النهائي
    const summary = await client.query(`
      SELECT 
        (SELECT COUNT(*) FROM cases) as total_cases,
        (SELECT COUNT(*) FROM case_movements) as total_movements,
        (SELECT COUNT(*) FROM court_sessions) as total_sessions,
        (SELECT COUNT(*) FROM case_stages) as total_stages,
        (SELECT COUNT(*) FROM case_notifications) as total_notifications,
        (SELECT COUNT(*) FROM employees) as total_employees,
        (SELECT COUNT(*) FROM clients) as total_clients
    `);

    console.log('\n📊 ملخص البيانات النهائي:');
    console.log(`   - القضايا: ${summary.rows[0].total_cases}`);
    console.log(`   - الحركات: ${summary.rows[0].total_movements}`);
    console.log(`   - الجلسات: ${summary.rows[0].total_sessions}`);
    console.log(`   - المراحل: ${summary.rows[0].total_stages}`);
    console.log(`   - الإشعارات: ${summary.rows[0].total_notifications}`);
    console.log(`   - الموظفين: ${summary.rows[0].total_employees}`);
    console.log(`   - العملاء: ${summary.rows[0].total_clients}`);

    console.log('\n🎉 تم إضافة جميع البيانات التجريبية بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في إضافة البيانات:', error.message);
    throw error;
  } finally {
    await client.end();
  }
}

// تشغيل الدالة
if (require.main === module) {
  populateCaseData()
    .then(() => {
      console.log('✅ اكتملت إضافة البيانات التجريبية');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ فشل في إضافة البيانات:', error);
      process.exit(1);
    });
}

module.exports = { populateCaseData };
