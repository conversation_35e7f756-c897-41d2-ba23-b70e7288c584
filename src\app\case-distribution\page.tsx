'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Plus, Edit, Trash2, FileText } from 'lucide-react'

interface ServiceDistribution {
  id: number
  service_id: number
  service_name: string
  percentage: number
  amount: number
  amount_client?: number
  employee_id: number
  employee_name: string
}

interface CaseDistribution {
  id: number
  issue_id: number
  issue_title: string
  case_number: string
  case_amount: number
  admin_amount: number
  remaining_amount: number
  admin_percentage: number
  court_id?: number
  created_date: string
  client_name?: string
  court_name?: string
  service_distributions?: ServiceDistribution[]
  judge_id?: number
  judge_name?: string
}

interface Issue {
  id: number
  title: string
  case_number: string
  case_amount: number
  court_id?: number
  client_name?: string
  court_name?: string
}

interface Lineage {
  id: number
  name: string
  admin_percentage: number
  commission_percentage: number
}

interface Service {
  id: number
  name: string
  description?: string
}

interface Employee {
  id: number
  name: string
  position?: string
}

export default function CaseDistributionPage() {
  const [distributions, setDistributions] = useState<CaseDistribution[]>([])
  const [issues, setIssues] = useState<Issue[]>([])
  const [lineages, setLineages] = useState<Lineage[]>([])
  const [services, setServices] = useState<Service[]>([])
  const [employees, setEmployees] = useState<Employee[]>([])
  const [judges, setJudges] = useState<Array<{id:number;name:string}>>([])
  const [isLoading, setIsLoading] = useState(false)
  const [showModal, setShowModal] = useState(false)
  const [editingId, setEditingId] = useState<number | null>(null)
  const [error, setError] = useState<string | null>(null)

  // حالات الفلاتر والفرز للجدول
  const [filters, setFilters] = useState({
    issue: '',          // يبحث في العنوان ورقم القضية
    client_name: '',
    case_amount: '',
    admin_amount: '',
    remaining_amount: '',
    admin_percentage: '',
    judge_name: ''
  })
  const [sortBy, setSortBy] = useState<keyof CaseDistribution | 'issue' | 'client_name' | 'case_amount' | 'admin_amount' | 'remaining_amount' | 'admin_percentage' | 'judge_name'>('id')
  const [sortDir, setSortDir] = useState<'asc' | 'desc'>('desc')
  const [colMenu, setColMenu] = useState<{open:boolean; key: keyof typeof filters | null; x:number; y:number}>({open:false, key:null, x:0, y:0})

  // نطاقات المبالغ
  const [caseAmountMin, setCaseAmountMin] = useState<string>('')
  const [caseAmountMax, setCaseAmountMax] = useState<string>('')
  const [adminAmountMin, setAdminAmountMin] = useState<string>('')
  const [adminAmountMax, setAdminAmountMax] = useState<string>('')
  const [remainingAmountMin, setRemainingAmountMin] = useState<string>('')
  const [remainingAmountMax, setRemainingAmountMax] = useState<string>('')

  // بيانات النموذج
  const [formData, setFormData] = useState({
    issue_id: '',
    lineage_id: '',
    admin_amount: 0,
    remaining_amount: 0,
    service_distributions: [] as ServiceDistribution[],
    judge_id: ''
  })

  // جلب التوزيعات
  const fetchDistributions = async () => {
    setIsLoading(true)
    setError(null)
    try {
      const response = await fetch('/api/case-distribution')
      const result = await response.json()

      if (result.success) {
        setDistributions(result.data || [])
      } else {
        setError(result.error || 'فشل في جلب البيانات')
      }
    } catch (err) {
      setError('خطأ في الاتصال بالخادم')
      console.error('Error:', err)
    } finally {
      setIsLoading(false)
    }
  }

  // جلب القضايا غير الموزعة
  const fetchIssues = async () => {
    try {
      const response = await fetch('/api/issues?undistributed=true')
      const result = await response.json()

      if (result.success) {
        setIssues(result.data || [])
      }
    } catch (err) {
      console.error('Error fetching issues:', err)
    }
  }

  // جلب النسب المالية
  const fetchLineages = async () => {
    try {
      const response = await fetch('/api/lineages')
      const result = await response.json()

      if (result.success) {
        setLineages(result.data || [])
      }
    } catch (err) {
      console.error('Error fetching lineages:', err)
    }
  }

  // جلب الخدمات
  const fetchServices = async () => {
    try {
      const response = await fetch('/api/services')
      const result = await response.json()

      if (result.success) {
        setServices(result.data || [])
      }
    } catch (err) {
      console.error('Error fetching services:', err)
    }
  }

  // جلب القضاة
  const fetchJudges = async () => {
    try {
      const res = await fetch('/api/judges')
      const data = await res.json()
      if (data.success) setJudges(data.data || [])
    } catch (e) { console.error('Error fetching judges:', e) }
  }

  // جلب الموظفين
  const fetchEmployees = async () => {
    try {
      const response = await fetch('/api/employees')
      const result = await response.json()

      if (result.success) {
        setEmployees(result.data || [])
      }
    } catch (err) {
      console.error('Error fetching employees:', err)
    }
  }

  useEffect(() => {
    fetchDistributions()
    fetchIssues()
    fetchLineages()
    fetchServices()
    fetchJudges()
    fetchEmployees()
  }, [])

  // أدوات منيو الفلاترة للأعمدة
  const openColMenu = (key: keyof typeof filters) => (e: React.MouseEvent<HTMLElement>) => {
    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect()
    setColMenu({ open:true, key, x: rect.left + rect.width, y: rect.bottom })
  }
  const getFilterValueForKey = () => {
    if (!colMenu.key) return ''
    return filters[colMenu.key]
  }
  const setFilterForKey = (v: string) => {
    if (!colMenu.key) return
    setFilters(prev => ({ ...prev, [colMenu.key!]: v }))
  }
  const clearFilterForKey = () => {
    if (!colMenu.key) return
    setFilters(prev => ({ ...prev, [colMenu.key!]: '' }))
  }
  const applySortAsc = () => { if (colMenu.key){ setSortBy(colMenu.key as any); setSortDir('asc'); setColMenu(prev=>({...prev, open:false})) } }
  const applySortDesc = () => { if (colMenu.key){ setSortBy(colMenu.key as any); setSortDir('desc'); setColMenu(prev=>({...prev, open:false})) } }

  // تطبيق الفلاتر والفرز
  const visibleDistributions = distributions
    .filter((dist) => {
      const issueText = `${dist.issue_title || ''} ${dist.case_number || ''}`.toLowerCase()
      const f1 = filters.issue ? issueText.includes(filters.issue.toLowerCase()) : true
      const f2 = filters.client_name ? (dist.client_name || '').toLowerCase().includes(filters.client_name.toLowerCase()) : true
      const f3 = filters.case_amount ? dist.case_amount?.toString().includes(filters.case_amount) : true
      const f4 = filters.admin_amount ? dist.admin_amount?.toString().includes(filters.admin_amount) : true
      const f5 = filters.remaining_amount ? dist.remaining_amount?.toString().includes(filters.remaining_amount) : true
      const f6 = filters.admin_percentage ? dist.admin_percentage?.toString().includes(filters.admin_percentage) : true
      const f7 = filters.judge_name ? (dist.judge_name || '').toLowerCase().includes(filters.judge_name.toLowerCase()) : true
      // نطاقات المبالغ
      const passCaseMin = caseAmountMin ? (dist.case_amount || 0) >= Number(caseAmountMin) : true
      const passCaseMax = caseAmountMax ? (dist.case_amount || 0) <= Number(caseAmountMax) : true
      const passAdminMin = adminAmountMin ? (dist.admin_amount || 0) >= Number(adminAmountMin) : true
      const passAdminMax = adminAmountMax ? (dist.admin_amount || 0) <= Number(adminAmountMax) : true
      const passRemainMin = remainingAmountMin ? (dist.remaining_amount || 0) >= Number(remainingAmountMin) : true
      const passRemainMax = remainingAmountMax ? (dist.remaining_amount || 0) <= Number(remainingAmountMax) : true

      return f1 && f2 && f3 && f4 && f5 && f6 && f7 && passCaseMin && passCaseMax && passAdminMin && passAdminMax && passRemainMin && passRemainMax
    })
    .sort((a, b) => {
      const dir = sortDir === 'asc' ? 1 : -1
      switch (sortBy) {
        case 'issue': {
          const av = `${a.issue_title || ''} ${a.case_number || ''}`.toLowerCase()
          const bv = `${b.issue_title || ''} ${b.case_number || ''}`.toLowerCase()
          if (av < bv) return -1 * dir
          if (av > bv) return 1 * dir
          return 0
        }
        case 'client_name': {
          const av = (a.client_name || '').toLowerCase()
          const bv = (b.client_name || '').toLowerCase()
          if (av < bv) return -1 * dir
          if (av > bv) return 1 * dir
          return 0
        }
        case 'case_amount': return ((a.case_amount || 0) - (b.case_amount || 0)) * dir
        case 'admin_amount': return ((a.admin_amount || 0) - (b.admin_amount || 0)) * dir
        case 'remaining_amount': return ((a.remaining_amount || 0) - (b.remaining_amount || 0)) * dir
        case 'admin_percentage': return ((a.admin_percentage || 0) - (b.admin_percentage || 0)) * dir
        case 'judge_name': {
          const av = (a.judge_name || '').toLowerCase()
          const bv = (b.judge_name || '').toLowerCase()
          if (av < bv) return -1 * dir
          if (av > bv) return 1 * dir
          return 0
        }
        default:
          return ((a.id || 0) - (b.id || 0)) * dir
      }
    })

  // حساب المبالغ عند تغيير القضية أو النسبة
  const calculateAmounts = (issueId: string, lineageId: string) => {
    const issue = issues.find(i => i.id.toString() === issueId)
    const lineage = lineages.find(l => l.id.toString() === lineageId)

    if (issue && lineage) {
      const caseAmount = issue.case_amount || 0
      const adminAmount = (caseAmount * lineage.admin_percentage) / 100
      const remainingAmount = caseAmount - adminAmount

      setFormData(prev => ({
        ...prev,
        admin_amount: adminAmount,
        remaining_amount: remainingAmount
      }))
    }
  }

  // معالجة تغيير القضية
  const handleIssueChange = (issueId: string) => {
    setFormData(prev => ({ ...prev, issue_id: issueId }))
    if (issueId && formData.lineage_id) {
      calculateAmounts(issueId, formData.lineage_id)
    }
  }

  // معالجة تغيير النسبة
  const handleLineageChange = (lineageId: string) => {
    setFormData(prev => ({ ...prev, lineage_id: lineageId }))
    if (lineageId && formData.issue_id) {
      calculateAmounts(formData.issue_id, lineageId)
    }
  }

  // حفظ التوزيع
  const handleSave = async () => {
    if (!formData.issue_id || !formData.lineage_id) {
      setError('يرجى اختيار القضية والنسبة المالية')
      return
    }

    setIsLoading(true)
    try {
      const method = editingId ? 'PUT' : 'POST'
      const url = editingId ? `/api/case-distribution?id=${editingId}` : '/api/case-distribution'

      // إضافة court_id من القضية المختارة
      const selectedIssue = issues.find(issue => issue.id.toString() === formData.issue_id)
      const dataToSend = {
        ...formData,
        court_id: selectedIssue?.court_id || null,
        judge_id: formData.judge_id ? parseInt(formData.judge_id) : null
      }

      console.log('📤 البيانات المرسلة إلى API:', dataToSend)
      console.log('📋 service_distributions المرسلة:', dataToSend.service_distributions)

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(dataToSend)
      })

      const result = await response.json()
      console.log('📥 استجابة API:', result)

      if (result.success) {
        await fetchDistributions()
        await fetchIssues()
        handleCancel()
        alert('تم حفظ التوزيع بنجاح!')
      } else {
        console.error('❌ خطأ من API:', result)
        setError(result.error || 'فشل في الحفظ')
        if (result.details) {
          console.error('❌ تفاصيل الخطأ:', result.details)
        }
      }
    } catch (err) {
      setError('خطأ في الحفظ')
      console.error('Error:', err)
    } finally {
      setIsLoading(false)
    }
  }

  // إضافة خدمة جديدة
  const addServiceDistribution = () => {
    const newService: ServiceDistribution = {
      id: Date.now(),
      service_id: 0,
      service_name: '',
      percentage: 0,
      amount: 0,
      amount_client: 0,
      employee_id: 0,
      employee_name: ''
    }
    setFormData(prev => ({
      ...prev,
      service_distributions: [...prev.service_distributions, newService]
    }))
  }

  // حذف خدمة
  const removeServiceDistribution = (serviceId: number) => {
    setFormData(prev => ({
      ...prev,
      service_distributions: prev.service_distributions.filter(s => s.id !== serviceId)
    }))
  }

  // تحديث خدمة
  const updateServiceDistribution = (serviceId: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      service_distributions: prev.service_distributions.map(s =>
        s.id === serviceId ? { ...s, [field]: value } : s
      )
    }))
  }

  // إعادة حساب المبالغ للخدمات
  const recalculateServiceAmounts = () => {
    const remainingAmount = formData.remaining_amount
    const totalPercentage = formData.service_distributions.reduce((sum, s) => sum + s.percentage, 0)

    if (totalPercentage > 0) {
      setFormData(prev => ({
        ...prev,
        service_distributions: prev.service_distributions.map(s => ({
          ...s,
          amount: (remainingAmount * s.percentage) / 100
        }))
      }))
    }
  }

  // إلغاء النموذج
  const handleCancel = () => {
    setShowModal(false)
    setEditingId(null)
    setFormData({
      issue_id: '',
      lineage_id: '',
      admin_amount: 0,
      remaining_amount: 0,
      service_distributions: [],
      judge_id: ''
    })
    setError(null)
  }

  // حذف التوزيع
  const handleDelete = async (id: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا التوزيع؟')) return

    setIsLoading(true)
    try {
      const response = await fetch(`/api/case-distribution?id=${id}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (result.success) {
        await fetchDistributions()
        await fetchIssues()
      } else {
        setError(result.error || 'فشل في الحذف')
      }
    } catch (err) {
      setError('خطأ في الحذف')
      console.error('Error:', err)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
        {/* العنوان والأزرار */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">توزيع القضايا</h1>
            <p className="text-gray-600 mt-1">إدارة توزيع القضايا والنسب المالية</p>
          </div>
          <div className="flex items-center gap-2 flex-wrap">
            {/* نطاق مبلغ القضية */}
            <div className="flex items-center gap-1">
              <Input type="number" placeholder="مبلغ القضية من" value={caseAmountMin} onChange={(e)=> setCaseAmountMin(e.target.value)} className="w-36 text-black" />
              <span className="text-gray-500">-</span>
              <Input type="number" placeholder="إلى" value={caseAmountMax} onChange={(e)=> setCaseAmountMax(e.target.value)} className="w-28 text-black" />
            </div>
            {/* نطاق مبلغ الإدارة */}
            <div className="flex items-center gap-1">
              <Input type="number" placeholder="الإدارة من" value={adminAmountMin} onChange={(e)=> setAdminAmountMin(e.target.value)} className="w-32 text-black" />
              <span className="text-gray-500">-</span>
              <Input type="number" placeholder="إلى" value={adminAmountMax} onChange={(e)=> setAdminAmountMax(e.target.value)} className="w-28 text-black" />
            </div>
            {/* نطاق المبلغ المتبقي */}
            <div className="flex items-center gap-1">
              <Input type="number" placeholder="المتبقي من" value={remainingAmountMin} onChange={(e)=> setRemainingAmountMin(e.target.value)} className="w-32 text-black" />
              <span className="text-gray-500">-</span>
              <Input type="number" placeholder="إلى" value={remainingAmountMax} onChange={(e)=> setRemainingAmountMax(e.target.value)} className="w-28 text-black" />
            </div>
            <Button
              variant="outline"
              className="border-gray-500 text-gray-700 hover:bg-gray-50"
              onClick={() => { setFilters({ issue:'', client_name:'', case_amount:'', admin_amount:'', remaining_amount:'', admin_percentage:'', judge_name:'' }); setCaseAmountMin(''); setCaseAmountMax(''); setAdminAmountMin(''); setAdminAmountMax(''); setRemainingAmountMin(''); setRemainingAmountMax(''); setSortBy('id'); setSortDir('desc'); setColMenu({open:false, key:null, x:0, y:0}) }}
            >
              مسح الفلاتر
            </Button>
            {/* أزرار التصدير */}
            <Button
              variant="outline"
              className="border-blue-600 text-blue-600 hover:bg-blue-50"
              onClick={() => {
                const headers = ['القضية','العميل','مبلغ القضية','مبلغ الإدارة','المبلغ المتبقي','النسبة','القاضي']
                const rows = visibleDistributions.map(d => [
                  `${d.case_number || ''} ${d.issue_title || ''}`.trim(),
                  d.client_name || '',
                  (d.case_amount ?? '').toString(),
                  (d.admin_amount ?? '').toString(),
                  (d.remaining_amount ?? '').toString(),
                  (d.admin_percentage ?? '').toString(),
                  d.judge_name || ''
                ])
                const csv = [headers, ...rows].map(r => r.map(c=>`"${(c ?? '').toString().replace(/"/g,'""')}"`).join(',')).join('\n')
                const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' })
                const url = URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.href = url
                a.download = 'case_distributions.csv'
                document.body.appendChild(a)
                a.click()
                document.body.removeChild(a)
                URL.revokeObjectURL(url)
              }}
            >
              تصدير CSV
            </Button>
            <Button
              variant="outline"
              className="border-green-600 text-green-600 hover:bg-green-50"
              onClick={() => {
                const headers = ['القضية','العميل','مبلغ القضية','مبلغ الإدارة','المبلغ المتبقي','النسبة','القاضي']
                const rows = visibleDistributions.map(d => [
                  `${d.case_number || ''} ${d.issue_title || ''}`.trim(),
                  d.client_name || '',
                  (d.case_amount ?? '').toString(),
                  (d.admin_amount ?? '').toString(),
                  (d.remaining_amount ?? '').toString(),
                  (d.admin_percentage ?? '').toString(),
                  d.judge_name || ''
                ])
                const csv = [headers, ...rows].map(r => r.map(c=>`"${(c ?? '').toString().replace(/"/g,'""')}"`).join(',')).join('\n')
                const blob = new Blob([csv], { type: 'application/vnd.ms-excel' })
                const url = URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.href = url
                a.download = 'case_distributions.xls'
                document.body.appendChild(a)
                a.click()
                document.body.removeChild(a)
                URL.revokeObjectURL(url)
              }}
            >
              تصدير Excel
            </Button>
            <Button
              onClick={() => setShowModal(true)}
              className="bg-blue-600 hover:bg-blue-700"
              disabled={isLoading}
            >
              <Plus className="h-4 w-4 mr-2" />
              إضافة توزيع جديد
            </Button>
          </div>
        </div>

        {/* رسائل الخطأ */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        {/* النافذة المنبثقة */}
        {showModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between p-6 border-b">
                <h2 className="text-xl font-semibold">
                  {editingId ? 'تعديل توزيع القضية' : 'إضافة توزيع جديد'}
                </h2>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancel}
                  className="text-gray-500 hover:text-gray-700"
                >
                  ✕
                </Button>
              </div>

              <div className="p-6 space-y-6">
                {/* القسم الأول: اختيار القضية والنسبة */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* اختيار القضية - 50% من الحجم */}
                  <div className="md:col-span-1">
                    <Label htmlFor="issue">القضية</Label>
                    <select
                      id="issue"
                      value={formData.issue_id}
                      onChange={(e) => handleIssueChange(e.target.value)}
                      className="w-full mt-1 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">اختر القضية</option>
                      {issues.map(issue => (
                        <option key={issue.id} value={issue.id}>
                          {issue.case_number} - {issue.title}
                          {issue.case_amount > 0 && ` (${issue.case_amount.toLocaleString()} ريال)`}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* عرض اسم المحكمة */}
                  {formData.issue_id && (
                    <div className="md:col-span-1">
                      <Label>المحكمة</Label>
                      <div className="mt-1 p-2 bg-blue-50 border border-blue-200 rounded-md">
                        <span className="text-blue-800 font-medium">
                          {issues.find(issue => issue.id.toString() === formData.issue_id)?.court_name || 'غير محدد'}
                        </span>
                      </div>
                    </div>
                  )}

                  {/* اختيار القاضي */}
                  <div className="md:col-span-1">
                    <Label htmlFor="judge_id">لدى القاضي</Label>
                    <select
                      id="judge_id"
                      value={formData.judge_id}
                      onChange={(e)=> setFormData(prev=>({...prev, judge_id: e.target.value }))}
                      className="w-full mt-1 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">اختر القاضي...</option>
                      {judges.map(j => (
                        <option key={j.id} value={j.id}>{j.name}</option>
                      ))}
                    </select>
                  </div>

                  {/* اختيار النسبة المالية - 30% من الحجم */}
                  <div className={`${formData.issue_id ? 'md:col-span-1' : 'md:col-span-2'}`}>
                    <Label htmlFor="lineage">النسبة المالية</Label>
                    <select
                      id="lineage"
                      value={formData.lineage_id}
                      onChange={(e) => handleLineageChange(e.target.value)}
                      className="w-full mt-1 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">اختر النسبة</option>
                      {lineages.map(lineage => (
                        <option key={lineage.id} value={lineage.id}>
                          {lineage.name} ({lineage.admin_percentage}% إدارة)
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* عرض المبالغ المحسوبة */}
                {formData.admin_amount > 0 && (
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-semibold mb-3">المبالغ المحسوبة:</h4>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="text-center">
                        <span className="text-sm text-gray-600">مبلغ القضية</span>
                        <div className="text-lg font-bold text-blue-600">
                          {(formData.admin_amount + formData.remaining_amount).toLocaleString()} ريال
                        </div>
                      </div>
                      <div className="text-center">
                        <span className="text-sm text-gray-600">مبلغ الإدارة</span>
                        <div className="text-lg font-bold text-purple-600">
                          {formData.admin_amount.toLocaleString()} ريال
                        </div>
                      </div>
                      <div className="text-center">
                        <span className="text-sm text-gray-600">المبلغ المتبقي</span>
                        <div className="text-lg font-bold text-green-600">
                          {formData.remaining_amount.toLocaleString()} ريال
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* جدول الخدمات */}
                {formData.remaining_amount > 0 && (
                  <div>
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="text-lg font-semibold">توزيع الخدمات</h4>
                      <Button
                        onClick={addServiceDistribution}
                        size="sm"
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        إضافة خدمة
                      </Button>
                    </div>

                    {formData.service_distributions.length === 0 ? (
                      <div className="text-center py-8 text-gray-500 border-2 border-dashed border-gray-300 rounded-lg">
                        <p>لم يتم إضافة خدمات بعد</p>
                        <p className="text-sm">اضغط "إضافة خدمة" لبدء توزيع الخدمات</p>
                      </div>
                    ) : (
                      <div className="overflow-x-auto">
                        <table className="w-full border border-gray-300">
                          <thead>
                            <tr className="bg-gray-100">
                              <th className="border border-gray-300 p-3 text-right w-[15%]">الخدمة</th>
                              <th className="border border-gray-300 p-3 text-right w-[10%]">النسبة %</th>
                              <th className="border border-gray-300 p-3 text-right w-[20%]">مبلغ المحامي</th>
                              <th className="border border-gray-300 p-3 text-right w-[30%]">المحامي</th>
                              <th className="border border-gray-300 p-3 text-right w-[10%]">إجراءات</th>
                            </tr>
                          </thead>
                          <tbody>
                            {formData.service_distributions.map((service) => (
                              <tr key={service.id} className="hover:bg-gray-50">
                                {/* الخدمة */}
                                <td className="border border-gray-300 p-2 w-[15%]">
                                  <select
                                    value={service.service_id}
                                    onChange={(e) => {
                                      const selectedService = services.find(s => s.id.toString() === e.target.value)
                                      updateServiceDistribution(service.id, 'service_id', parseInt(e.target.value))
                                      updateServiceDistribution(service.id, 'service_name', selectedService?.name || '')
                                    }}
                                    className="w-full p-1 border border-gray-300 rounded text-sm"
                                  >
                                    <option value={0}>اختر الخدمة</option>
                                    {services.map(s => (
                                      <option key={s.id} value={s.id}>{s.name}</option>
                                    ))}
                                  </select>
                                </td>

                                {/* النسبة */}
                                <td className="border border-gray-300 p-2 w-[10%]">
                                  <input
                                    type="text"
                                    inputMode="decimal"
                                    pattern="[0-9]*\.?[0-9]*"
                                    value={service.percentage}
                                    onChange={(e) => {
                                      const value = e.target.value
                                      if (value === '' || /^\d*\.?\d*$/.test(value)) {
                                        const percentage = parseFloat(value) || 0
                                        updateServiceDistribution(service.id, 'percentage', percentage)
                                        updateServiceDistribution(service.id, 'amount', (formData.remaining_amount * percentage) / 100)
                                      }
                                    }}
                                    className="w-full p-1 border border-gray-300 rounded text-sm text-center"
                                    placeholder="0"
                                  />
                                </td>

                                {/* مبلغ المحامي */}
                                <td className="border border-gray-300 p-2 w-[20%]">
                                  <input
                                    type="text"
                                    inputMode="decimal"
                                    pattern="[0-9]*\.?[0-9]*"
                                    value={service.amount}
                                    onChange={(e) => {
                                      const value = e.target.value
                                      if (value === '' || /^\d*\.?\d*$/.test(value)) {
                                        const amount = parseFloat(value) || 0
                                        updateServiceDistribution(service.id, 'amount', amount)
                                        updateServiceDistribution(service.id, 'percentage', (amount / formData.remaining_amount) * 100)
                                      }
                                    }}
                                    className="w-full p-1 border border-gray-300 rounded text-sm text-center"
                                    placeholder="0"
                                  />
                                </td>

                                {/* المحامي */}
                                <td className="border border-gray-300 p-2 w-[30%]">
                                  <select
                                    value={service.employee_id}
                                    onChange={(e) => {
                                      const selectedEmployee = employees.find(emp => emp.id.toString() === e.target.value)
                                      updateServiceDistribution(service.id, 'employee_id', parseInt(e.target.value))
                                      updateServiceDistribution(service.id, 'employee_name', selectedEmployee?.name || '')
                                    }}
                                    className="w-full p-1 border border-gray-300 rounded text-sm"
                                  >
                                    <option value={0}>اختر المحامي</option>
                                    {employees.map(emp => (
                                      <option key={emp.id} value={emp.id}>{emp.name}</option>
                                    ))}
                                  </select>
                                </td>

                                {/* إجراءات */}
                                <td className="border border-gray-300 p-2 text-center">
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => removeServiceDistribution(service.id)}
                                    className="text-red-600 hover:text-red-700"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>

                        {/* ملخص النسب */}
                        <div className="mt-4 p-3 bg-gray-50 rounded border">
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <span>مجموع النسب: </span>
                              <span className={`font-bold ${
                                formData.service_distributions.reduce((sum, s) => sum + s.percentage, 0) > 100
                                  ? 'text-red-600'
                                  : 'text-green-600'
                              }`}>
                                {formData.service_distributions.reduce((sum, s) => sum + s.percentage, 0).toFixed(1)}%
                              </span>
                            </div>
                            <div>
                              <span>مجموع المبالغ: </span>
                              <span className="font-bold text-blue-600">
                                {formData.service_distributions.reduce((sum, s) => sum + s.amount, 0).toLocaleString()} ريال
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* أزرار النافذة */}
              <div className="flex justify-end space-x-2 space-x-reverse p-6 border-t bg-gray-50">
                <Button
                  onClick={handleCancel}
                  variant="outline"
                  disabled={isLoading}
                >
                  إلغاء
                </Button>
                <Button
                  onClick={handleSave}
                  disabled={isLoading || !formData.issue_id || !formData.lineage_id}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isLoading ? 'جاري الحفظ...' : 'حفظ التوزيع'}
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* قائمة التوزيعات */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              قائمة توزيعات القضايا ({distributions.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading && !showModal ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">جاري التحميل...</p>
              </div>
            ) : distributions.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>لا توجد توزيعات قضايا</p>
                <p className="text-sm">اضغط "إضافة توزيع جديد" لإضافة أول توزيع</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b bg-gray-50">
                      <th className="text-right p-3 font-semibold cursor-pointer" onMouseDown={openColMenu('issue')}>القضية {sortBy==='issue' ? (sortDir==='asc' ? '▲' : '▼') : ''}</th>
                      <th className="text-right p-3 font-semibold cursor-pointer" onMouseDown={openColMenu('client_name')}>العميل {sortBy==='client_name' ? (sortDir==='asc' ? '▲' : '▼') : ''}</th>
                      <th className="text-right p-3 font-semibold cursor-pointer" onMouseDown={openColMenu('case_amount')}>مبلغ القضية {sortBy==='case_amount' ? (sortDir==='asc' ? '▲' : '▼') : ''}</th>
                      <th className="text-right p-3 font-semibold cursor-pointer" onMouseDown={openColMenu('admin_amount')}>مبلغ الإدارة {sortBy==='admin_amount' ? (sortDir==='asc' ? '▲' : '▼') : ''}</th>
                      <th className="text-right p-3 font-semibold cursor-pointer" onMouseDown={openColMenu('remaining_amount')}>المبلغ المتبقي {sortBy==='remaining_amount' ? (sortDir==='asc' ? '▲' : '▼') : ''}</th>
                      <th className="text-right p-3 font-semibold cursor-pointer" onMouseDown={openColMenu('admin_percentage')}>النسبة {sortBy==='admin_percentage' ? (sortDir==='asc' ? '▲' : '▼') : ''}</th>
                      <th className="text-right p-3 font-semibold cursor_pointer" onMouseDown={openColMenu('judge_name')}>القاضي {sortBy==='judge_name' ? (sortDir==='asc' ? '▲' : '▼') : ''}</th>
                      <th className="text-right p-3 font-semibold">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {visibleDistributions.map((dist) => (
                      <tr key={dist.id} className="border-b hover:bg-gray-50">
                        <td className="p-3">
                          <div>
                            <div className="font-medium">{dist.issue_title}</div>
                            <div className="text-sm text-gray-500">{dist.case_number}</div>
                          </div>
                        </td>
                        <td className="p-3">
                          <div className="text-sm">
                            {dist.client_name || 'غير محدد'}
                          </div>
                        </td>
                        <td className="p-3">
                          <div className="font-medium text-blue-600">
                            {dist.case_amount.toLocaleString()} ريال
                          </div>
                        </td>
                        <td className="p-3">
                          <div className="font-medium text-purple-600">
                            {dist.admin_amount.toLocaleString()} ريال
                          </div>
                        </td>
                        <td className="p-3">
                          <div className="font-medium text-green-600">
                            {dist.remaining_amount.toLocaleString()} ريال
                          </div>
                        </td>
                        <td className="p-3">
                          <Badge variant="outline">
                            {dist.admin_percentage.toFixed(1)}%
                          </Badge>
                        </td>
                        <td className="p-3">
                          <div className="text-sm">
                            {dist.judge_name || 'غير محدد'}
                          </div>
                        </td>
                        <td className="p-3">
                          <div className="flex space-x-1 space-x-reverse">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                setEditingId(dist.id)
                                setFormData({
                                  issue_id: dist.issue_id.toString(),
                                  lineage_id: '',
                                  admin_amount: dist.admin_amount,
                                  remaining_amount: dist.remaining_amount,
                                  service_distributions: dist.service_distributions || [],
                                  judge_id: (dist.judge_id?.toString() || '')
                                })
                                setShowModal(true)
                              }}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDelete(dist.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* منيو العمود المنبثقة للفلاترة */}
        {colMenu.open && colMenu.key && (
          <div
            className="fixed z-50 bg-white border border-gray-200 rounded-md shadow-lg p-2 w-72"
            style={{ top: colMenu.y + 8, left: Math.max(8, colMenu.x - 288) }}
          >
            <div className="relative">
              <input
                type="text"
                value={getFilterValueForKey()}
                onChange={(e)=> setFilterForKey(e.target.value)}
                placeholder={`بحث في العمود`}
                className="h-9 w-full border border-gray-300 rounded-md pr-3 pl-28 text-sm text-black"
              />
              <div className="absolute inset-y-0 left-2 flex items-center gap-1">
                <Button size="sm" variant="outline" className="h-7 px-2" onClick={applySortAsc} title="تصاعدي">↑</Button>
                <Button size="sm" variant="outline" className="h-7 px-2" onClick={applySortDesc} title="تنازلي">↓</Button>
                <Button size="sm" variant="outline" className="h-7 px-2 text-red-600" onClick={clearFilterForKey} title="مسح">×</Button>
              </div>
            </div>
          </div>
        )}
      </div>
  )
}
