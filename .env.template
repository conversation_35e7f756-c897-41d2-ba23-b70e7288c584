# ملف البيئة للإنتاج - النظام القانوني
# انسخ هذا الملف إلى .env.local وقم بتعديل القيم

# ⚠️ تحذير: لا تشارك هذا الملف مع أي شخص بعد إضافة كلمات المرور الحقيقية

# إعدادات قاعدة البيانات (مطلوبة)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=legal_system_production
DB_USER=postgres
DB_PASSWORD=your_secure_password_here

# إعدادات الأمان (مطلوبة)
JWT_SECRET=your_jwt_secret_here_minimum_32_characters_long
NEXTAUTH_SECRET=your_nextauth_secret_here_minimum_32_characters

# إعدادات الخادم
PORT=3000
NODE_ENV=production

# إعدادات البريد الإلكتروني (اختيارية)
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=

# إعدادات التخزين (اختيارية)
UPLOAD_PATH=./public/uploads
MAX_FILE_SIZE=10485760

# إعدادات النسخ الاحتياطي (اختيارية)
BACKUP_PATH=./backups
BACKUP_RETENTION_DAYS=30

# إعدادات إضافية (اختيارية)
ENABLE_DEBUG_LOGS=false
ENABLE_API_RATE_LIMITING=true
SESSION_TIMEOUT_MINUTES=60

# ملاحظات مهمة:
# 1. استخدم كلمات مرور قوية (12 حرف كحد أدنى)
# 2. غيّر جميع القيم الافتراضية
# 3. لا تستخدم كلمات مرور بسيطة مثل "123456" أو "password"
# 4. احفظ نسخة آمنة من كلمات المرور في مكان منفصل