'use client'

import { useState, useEffect } from 'react'
import { Search, Download, FileText, File, Filter, Grid, List, Eye } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

interface LegalFile {
  id: string
  name: string
  originalName: string
  fileName: string
  category: string
  type: string
  fileType: string
  size: number
  sizeFormatted: string
  lastModified: string
  downloadUrl: string
}

export default function LegalLibraryPage() {
  const [files, setFiles] = useState<LegalFile[]>([])
  const [filteredFiles, setFilteredFiles] = useState<LegalFile[]>([])
  const [categories, setCategories] = useState<Record<string, LegalFile[]>>({})
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [sortBy, setSortBy] = useState<'name' | 'date' | 'size'>('name')

  // جلب الملفات القانونية
  useEffect(() => {
    fetchLegalFiles()
  }, [])

  // تصفية وترتيب الملفات
  useEffect(() => {
    let filtered = files

    // تصفية حسب البحث
    if (searchTerm) {
      filtered = filtered.filter(file =>
        file.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        file.category.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // تصفية حسب الفئة
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(file => file.category === selectedCategory)
    }

    // ترتيب الملفات
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name, 'ar')
        case 'date':
          return new Date(b.lastModified).getTime() - new Date(a.lastModified).getTime()
        case 'size':
          return b.size - a.size
        default:
          return 0
      }
    })

    setFilteredFiles(filtered)
  }, [files, searchTerm, selectedCategory, sortBy])

  const fetchLegalFiles = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/legal-library')
      const result = await response.json()

      if (result.success) {
        setFiles(result.data)
        
        // تجميع الملفات حسب الفئة
        const categorizedFiles = result.data.reduce((acc: Record<string, LegalFile[]>, file: LegalFile) => {
          if (!acc[file.category]) {
            acc[file.category] = []
          }
          acc[file.category].push(file)
          return acc
        }, {})
        
        setCategories(categorizedFiles)
      }
    } catch (error) {
      console.error('خطأ في جلب الملفات:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDownload = (file: LegalFile) => {
    const link = document.createElement('a')
    link.href = file.downloadUrl
    link.download = file.fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const getFileIcon = (fileType: string) => {
    switch (fileType.toLowerCase()) {
      case 'pdf':
        return <File className="h-8 w-8 text-red-500" />
      case 'txt':
        return <FileText className="h-8 w-8 text-blue-500" />
      case 'doc':
      case 'docx':
        return <FileText className="h-8 w-8 text-blue-600" />
      default:
        return <File className="h-8 w-8 text-gray-500" />
    }
  }

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      'الدستور': 'bg-purple-100 text-purple-800',
      'مالي وضريبي': 'bg-green-100 text-green-800',
      'عمل ونقابات': 'bg-blue-100 text-blue-800',
      'تجاري': 'bg-orange-100 text-orange-800',
      'جنائي': 'bg-red-100 text-red-800',
      'حقوق الإنسان': 'bg-pink-100 text-pink-800',
      'ملكية فكرية': 'bg-indigo-100 text-indigo-800',
      'مصرفي ومالي': 'bg-emerald-100 text-emerald-800',
      'إعلام وصحافة': 'bg-yellow-100 text-yellow-800',
      'بيئة وموارد': 'bg-teal-100 text-teal-800',
      'تحكيم ومنازعات': 'bg-violet-100 text-violet-800',
      'لوائح تنفيذية': 'bg-slate-100 text-slate-800',
      'عام': 'bg-gray-100 text-gray-800'
    }
    return colors[category] || colors['عام']
  }

  if (loading) {
    return (
      <div className="container mx-auto px-6 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل المكتبة القانونية...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-6 py-8">
      {/* العنوان */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">المكتبة القانونية</h1>
        <p className="text-gray-600">مجموعة شاملة من القوانين واللوائح والتشريعات اليمنية</p>
      </div>

      {/* أدوات البحث والتصفية */}
      <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
          {/* البحث */}
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="البحث في المكتبة..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pr-10"
            />
          </div>

          {/* تصفية حسب الفئة */}
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger>
              <SelectValue placeholder="جميع الفئات" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">جميع الفئات</SelectItem>
              {Object.keys(categories).map(category => (
                <SelectItem key={category} value={category}>
                  {category} ({categories[category].length})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* ترتيب */}
          <Select value={sortBy} onValueChange={(value: 'name' | 'date' | 'size') => setSortBy(value)}>
            <SelectTrigger>
              <SelectValue placeholder="ترتيب حسب" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="name">الاسم</SelectItem>
              <SelectItem value="date">التاريخ</SelectItem>
              <SelectItem value="size">الحجم</SelectItem>
            </SelectContent>
          </Select>

          {/* طريقة العرض */}
          <div className="flex gap-2">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* إحصائيات */}
        <div className="flex items-center gap-4 text-sm text-gray-600">
          <span>إجمالي الملفات: {files.length}</span>
          <span>•</span>
          <span>النتائج المعروضة: {filteredFiles.length}</span>
          <span>•</span>
          <span>الفئات: {Object.keys(categories).length}</span>
        </div>
      </div>

      {/* عرض الملفات */}
      {filteredFiles.length === 0 ? (
        <div className="text-center py-12">
          <FileText className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد ملفات</h3>
          <p className="text-gray-600">لم يتم العثور على ملفات تطابق معايير البحث</p>
        </div>
      ) : (
        <div className={viewMode === 'grid' 
          ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
          : 'space-y-4'
        }>
          {filteredFiles.map((file) => (
            <Card key={file.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    {getFileIcon(file.fileType)}
                    <div className="flex-1 min-w-0">
                      <CardTitle className="text-sm font-medium text-gray-900 line-clamp-2">
                        {file.name}
                      </CardTitle>
                      <Badge className={`mt-1 ${getCategoryColor(file.category)}`}>
                        {file.category}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
                  <span>{file.fileType}</span>
                  <span>{file.sizeFormatted}</span>
                </div>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    onClick={() => handleDownload(file)}
                    className="flex-1"
                  >
                    <Download className="h-4 w-4 ml-2" />
                    تحميل
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
