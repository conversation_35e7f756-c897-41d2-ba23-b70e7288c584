'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { useAuth } from '@/hooks/useAuth'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ChatWidget } from '@/components/chat/chat-widget'
import {
  User,
  FileText,
  MessageCircle,
  Calendar,
  Bell,
  LogOut,
  Eye,
  Download,
  Clock,
  CheckCircle,
  AlertCircle,
  Building2
} from 'lucide-react'

interface ClientSession {
  id: number
  username: string
  name: string
  type: 'client'
  token: string
}

interface Case {
  id: number
  case_number: string
  title: string
  status: string
  court_name: string
  created_date: string
  next_hearing_date?: string
}

function ClientPortalContent() {
  const router = useRouter()
  const { user: clientSession, logout } = useAuth()
  const [cases, setCases] = useState<Case[]>([])
  const [notifications, setNotifications] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // جلب بيانات العميل عند تحميل الجلسة
  useEffect(() => {
    if (clientSession) {
      fetchClientData(clientSession.id)
    }
  }, [clientSession])

  // جلب بيانات العميل
  const fetchClientData = async (clientId: number) => {
    try {
      setIsLoading(true)
      
      // جلب بيانات لوحة التحكم
      const dashboardResponse = await fetch('/api/client-portal/dashboard', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('clientToken')}`
        }
      })
      
      if (dashboardResponse.ok) {
        const dashboardResult = await dashboardResponse.json()
        if (dashboardResult.success) {
          setCases(dashboardResult.data.cases || [])
          setNotifications(dashboardResult.data.notifications || [])
        }
      }

    } catch (error) {
      console.error('Error fetching client data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // تسجيل الخروج
  const handleLogout = () => {
    logout()
  }

  // تنسيق التاريخ
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA')
  }

  // تنسيق حالة القضية
  const getStatusBadge = (status: string) => {
    const statusMap: { [key: string]: { label: string; className: string } } = {
      'active': { label: 'نشطة', className: 'bg-green-100 text-green-800' },
      'pending': { label: 'معلقة', className: 'bg-yellow-100 text-yellow-800' },
      'closed': { label: 'مغلقة', className: 'bg-gray-100 text-gray-800' },
      'on_hold': { label: 'متوقفة', className: 'bg-red-100 text-red-800' }
    }
    
    const statusInfo = statusMap[status] || { label: status, className: 'bg-gray-100 text-gray-800' }
    return (
      <Badge className={statusInfo.className}>
        {statusInfo.label}
      </Badge>
    )
  }

  if (!clientSession) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* رأس الصفحة */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <Building2 className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">بوابة العملاء</h1>
                <p className="text-sm text-gray-600">مرحباً، {clientSession?.name}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4 space-x-reverse">
              <Button
                variant="outline"
                onClick={handleLogout}
                className="flex items-center"
              >
                <LogOut className="h-4 w-4 mr-2" />
                تسجيل الخروج
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* المحتوى الرئيسي */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <div className="space-y-8">
            {/* إحصائيات سريعة */}
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <FileText className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="mr-4">
                      <div className="text-2xl font-bold text-gray-900">{cases.length}</div>
                      <div className="text-sm text-gray-600">إجمالي القضايا</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <CheckCircle className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="mr-4">
                      <div className="text-2xl font-bold text-gray-900">
                        {cases.filter(c => c.status === 'active').length}
                      </div>
                      <div className="text-sm text-gray-600">القضايا النشطة</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-yellow-100 rounded-lg">
                      <Clock className="h-6 w-6 text-yellow-600" />
                    </div>
                    <div className="mr-4">
                      <div className="text-2xl font-bold text-gray-900">
                        {cases.filter(c => c.status === 'pending').length}
                      </div>
                      <div className="text-sm text-gray-600">القضايا المعلقة</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-red-100 rounded-lg">
                      <Bell className="h-6 w-6 text-red-600" />
                    </div>
                    <div className="mr-4">
                      <div className="text-2xl font-bold text-gray-900">
                        {notifications.filter(n => !n.is_read).length}
                      </div>
                      <div className="text-sm text-gray-600">إشعارات جديدة</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* قائمة القضايا */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="h-5 w-5 mr-2" />
                  قضاياي
                </CardTitle>
              </CardHeader>
              <CardContent>
                {cases.length === 0 ? (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-500">لا توجد قضايا مسجلة</p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-right p-3 font-semibold">رقم القضية</th>
                          <th className="text-right p-3 font-semibold">عنوان القضية</th>
                          <th className="text-center p-3 font-semibold">المحكمة</th>
                          <th className="text-center p-3 font-semibold">الحالة</th>
                          <th className="text-center p-3 font-semibold">تاريخ الإنشاء</th>
                          <th className="text-center p-3 font-semibold">الجلسة القادمة</th>
                          <th className="text-center p-3 font-semibold">الإجراءات</th>
                        </tr>
                      </thead>
                      <tbody>
                        {cases.map((caseItem) => (
                          <tr key={caseItem.id} className="border-b hover:bg-gray-50">
                            <td className="p-3">
                              <Badge variant="outline" className="bg-blue-50 text-blue-700">
                                {caseItem.case_number}
                              </Badge>
                            </td>
                            <td className="p-3 font-medium">{caseItem.title}</td>
                            <td className="text-center p-3">{caseItem.court_name}</td>
                            <td className="text-center p-3">
                              {getStatusBadge(caseItem.status)}
                            </td>
                            <td className="text-center p-3">{formatDate(caseItem.created_date)}</td>
                            <td className="text-center p-3">
                              {caseItem.next_hearing_date ? (
                                <span className="text-orange-600 font-medium">
                                  {formatDate(caseItem.next_hearing_date)}
                                </span>
                              ) : (
                                <span className="text-gray-400">غير محدد</span>
                              )}
                            </td>
                            <td className="text-center p-3">
                              <Button
                                size="sm"
                                variant="outline"
                                className="bg-blue-50 hover:bg-blue-100 text-blue-700"
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* الإشعارات الحديثة */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Bell className="h-5 w-5 mr-2" />
                  الإشعارات الحديثة
                </CardTitle>
              </CardHeader>
              <CardContent>
                {notifications.length === 0 ? (
                  <div className="text-center py-8">
                    <Bell className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-500">لا توجد إشعارات</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {notifications.slice(0, 5).map((notification) => (
                      <div
                        key={notification.id}
                        className={`p-4 rounded-lg border ${
                          notification.is_read ? 'bg-gray-50' : 'bg-blue-50 border-blue-200'
                        }`}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className="font-medium text-gray-900">{notification.title}</h4>
                            <p className="text-sm text-gray-600 mt-1">{notification.content}</p>
                            <p className="text-xs text-gray-400 mt-2">
                              {formatDate(notification.created_at)}
                            </p>
                          </div>
                          {!notification.is_read && (
                            <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}
      </main>

      {/* مكون المحادثات */}
      <ChatWidget
        userType="client"
        userId={clientSession?.id || 0}
        userName={clientSession?.name || 'عميل'}
      />
    </div>
  )
}

export default function ClientPortalPage() {
  return (
    <ProtectedRoute userType="client">
      <ClientPortalContent />
    </ProtectedRoute>
  )
}
