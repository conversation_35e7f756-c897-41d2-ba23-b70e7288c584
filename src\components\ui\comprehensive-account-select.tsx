'use client'

import { useState, useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { ChevronDown, Search, BookOpen, User, Users, Building } from 'lucide-react'

interface Account {
  id: string | number
  account_code: string
  account_name: string
  account_type: string
  account_nature: string
  is_linked_record?: boolean
  original_table?: string
  parent_account_name?: string
  allow_transactions: boolean
}

interface ComprehensiveAccountSelectProps {
  value: string
  onChange: (accountId: string) => void
  label?: string
  placeholder?: string
  required?: boolean
  className?: string
  accountTypes?: string[] // فلترة حسب نوع الحساب
  showOnlyTransactional?: boolean // عرض الحسابات التي تقبل معاملات فقط
  groupByType?: boolean // تجميع الحسابات حسب النوع
}

export function ComprehensiveAccountSelect({ 
  value, 
  onChange, 
  label = "الحساب", 
  placeholder = "اختر الحساب أو ابحث...", 
  required = false,
  className = "",
  accountTypes = [],
  showOnlyTransactional = true,
  groupByType = true
}: ComprehensiveAccountSelectProps) {
  const [accounts, setAccounts] = useState<Account[]>([])
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedAccount, setSelectedAccount] = useState<Account | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  const fetchAccounts = async () => {
    setIsLoading(true)
    try {
      const params = new URLSearchParams()
      params.append('include_linked', 'true')
      if (showOnlyTransactional) {
        params.append('only_transactional', 'true')
      }
      
      const response = await fetch(`/api/accounting/chart-of-accounts?${params.toString()}`)
      if (response.ok) {
        const data = await response.json()
        let filteredAccounts = data.accounts || []
        
        // فلترة حسب نوع الحساب إذا تم تحديده
        if (accountTypes.length > 0) {
          filteredAccounts = filteredAccounts.filter((account: Account) => 
            accountTypes.includes(account.account_type)
          )
        }
        
        // ترتيب الحسابات: الحسابات العادية أولاً، ثم المرتبطة
        filteredAccounts.sort((a: Account, b: Account) => {
          // ترتيب حسب النوع أولاً
          if (a.account_type !== b.account_type) {
            return a.account_type.localeCompare(b.account_type, 'ar')
          }
          
          // ثم حسب كونها مرتبطة أم لا
          if (a.is_linked_record && !b.is_linked_record) return 1
          if (!a.is_linked_record && b.is_linked_record) return -1
          
          // أخيراً حسب رمز الحساب
          return a.account_code.localeCompare(b.account_code)
        })
        
        setAccounts(filteredAccounts)
      } else {
        console.error('فشل في جلب الحسابات:', response.status)
        setAccounts([])
      }
    } catch (error) {
      console.error('خطأ في جلب الحسابات:', error)
      setAccounts([])
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchAccounts()
  }, [accountTypes, showOnlyTransactional])

  useEffect(() => {
    if (value && accounts.length > 0) {
      const account = accounts.find(a => a.id.toString() === value)
      setSelectedAccount(account || null)
    } else {
      setSelectedAccount(null)
    }
  }, [value, accounts])

  const filteredAccounts = accounts.filter(account =>
    account.account_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    account.account_code.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // تجميع الحسابات حسب النوع
  const groupedAccounts = groupByType ? 
    filteredAccounts.reduce((groups, account) => {
      const type = account.account_type
      if (!groups[type]) {
        groups[type] = []
      }
      groups[type].push(account)
      return groups
    }, {} as { [key: string]: Account[] }) : 
    { 'جميع الحسابات': filteredAccounts }

  const handleSelect = (account: Account) => {
    setSelectedAccount(account)
    onChange(account.id.toString())
    setIsOpen(false)
    setSearchTerm('')
  }

  const handleClear = () => {
    setSelectedAccount(null)
    onChange('')
    setSearchTerm('')
  }

  const getAccountTypeColor = (accountType: string) => {
    switch (accountType) {
      case 'أصول': return 'bg-green-100 text-green-800'
      case 'خصوم': return 'bg-red-100 text-red-800'
      case 'حقوق ملكية': return 'bg-indigo-100 text-indigo-800'
      case 'إيرادات': return 'bg-purple-100 text-purple-800'
      case 'مصروفات': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getAccountIcon = (account: Account) => {
    if (account.is_linked_record) {
      switch (account.original_table) {
        case 'clients': return '👤'
        case 'employees': return '👨‍💼'
        case 'suppliers': return '🏢'
        default: return '📋'
      }
    }
    return <BookOpen className="h-4 w-4 text-purple-500" />
  }

  const getLinkedAccountBadge = (account: Account) => {
    if (!account.is_linked_record) return null
    
    const labels = {
      'clients': 'عميل',
      'employees': 'موظف',
      'suppliers': 'مورد'
    }
    
    return (
      <Badge className="bg-blue-100 text-blue-800 mr-2" size="sm">
        {labels[account.original_table as keyof typeof labels] || 'مرتبط'}
      </Badge>
    )
  }

  return (
    <div className={`relative ${className}`}>
      <Label className="block text-sm font-medium text-gray-700 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </Label>
      
      <div className="relative">
        <div
          className="w-full px-3 py-2 border border-gray-300 rounded-md cursor-pointer bg-white flex items-center justify-between hover:border-gray-400 focus-within:border-blue-500 transition-colors"
          onClick={() => setIsOpen(!isOpen)}
        >
          <div className="flex items-center flex-1 min-w-0">
            {selectedAccount ? (
              <>
                {typeof getAccountIcon(selectedAccount) === 'string' ? (
                  <span className="text-lg mr-2">{getAccountIcon(selectedAccount)}</span>
                ) : (
                  getAccountIcon(selectedAccount)
                )}
                <span className="text-gray-900 mr-2 truncate">{selectedAccount.account_name}</span>
                <Badge className={`${getAccountTypeColor(selectedAccount.account_type)} ml-2`} size="sm">
                  {selectedAccount.account_type}
                </Badge>
                {getLinkedAccountBadge(selectedAccount)}
              </>
            ) : (
              <>
                <BookOpen className="h-4 w-4 mr-2 text-gray-400" />
                <span className="text-gray-500 mr-2 truncate">{placeholder}</span>
              </>
            )}
          </div>
          <ChevronDown className={`h-4 w-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </div>

        {isOpen && (
          <div className="absolute z-[100] w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-[600px] overflow-hidden">
            {/* شريط البحث */}
            <div className="p-3 border-b bg-gray-50">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="ابحث بالاسم أو الرمز..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                  autoFocus
                />
              </div>
            </div>

            {/* قائمة الحسابات */}
            <div className="max-h-[500px] overflow-y-auto">
              {isLoading ? (
                <div className="p-4 text-center text-gray-500">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto mb-2"></div>
                  جاري التحميل...
                </div>
              ) : (
                <>
                  {selectedAccount && (
                    <div
                      className="p-3 hover:bg-red-50 cursor-pointer border-b text-red-600 bg-red-25"
                      onClick={handleClear}
                    >
                      <div className="flex items-center">
                        <span className="text-sm font-medium">✕ إلغاء الاختيار</span>
                      </div>
                    </div>
                  )}

                  {Object.keys(groupedAccounts).length > 0 ? (
                    Object.entries(groupedAccounts).map(([groupName, groupAccounts]) => (
                      <div key={groupName}>
                        {groupByType && Object.keys(groupedAccounts).length > 1 && (
                          <div className="px-3 py-2 bg-gray-100 border-b text-sm font-semibold text-gray-700">
                            {groupName} ({groupAccounts.length})
                          </div>
                        )}
                        {groupAccounts.map((account) => (
                          <div
                            key={account.id}
                            className="p-3 hover:bg-gray-50 cursor-pointer border-b last:border-b-0"
                            onClick={() => handleSelect(account)}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center">
                                  {typeof getAccountIcon(account) === 'string' ? (
                                    <span className="text-lg mr-2">{getAccountIcon(account)}</span>
                                  ) : (
                                    getAccountIcon(account)
                                  )}
                                  <span className="font-medium text-gray-900 mr-2 truncate">{account.account_name}</span>
                                  <Badge className={getAccountTypeColor(account.account_type)} size="sm">
                                    {account.account_type}
                                  </Badge>
                                  {getLinkedAccountBadge(account)}
                                </div>
                                <div className="text-sm text-gray-500 mt-1">
                                  <span className="font-mono">{account.account_code}</span>
                                  <span className="mx-2">•</span>
                                  <span>{account.account_nature}</span>
                                  {account.parent_account_name && (
                                    <>
                                      <span className="mx-2">•</span>
                                      <span className="text-blue-600">تحت: {account.parent_account_name}</span>
                                    </>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ))
                  ) : (
                    <div className="p-4 text-center text-gray-500">
                      {searchTerm ? 'لا توجد نتائج للبحث' : 'لا توجد حسابات'}
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        )}
      </div>

      {/* عرض تفاصيل الحساب المختار */}
      {selectedAccount && (
        <div className="mt-2 p-3 bg-gray-50 border border-gray-200 rounded-md">
          <div className="text-sm text-gray-700">
            <div className="grid grid-cols-2 gap-2">
              <div><strong>الرمز:</strong> {selectedAccount.account_code}</div>
              <div><strong>النوع:</strong> {selectedAccount.account_type}</div>
              <div><strong>الطبيعة:</strong> {selectedAccount.account_nature}</div>
              {selectedAccount.is_linked_record && (
                <div><strong>مرتبط بـ:</strong> {
                  selectedAccount.original_table === 'clients' ? 'العملاء' :
                  selectedAccount.original_table === 'employees' ? 'الموظفين' :
                  selectedAccount.original_table === 'suppliers' ? 'الموردين' : 'جدول آخر'
                }</div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}