/**
 * API التحكم في WhatsApp الحقيقي
 */

export const runtime = 'nodejs'
export const dynamic = 'force-dynamic'

import { NextRequest, NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'
import { query } from '@/lib/db'

// استيراد خدمة WhatsApp الحقيقية المحسنة
let whatsappService: any = null

// تحميل الخدمة بشكل ديناميكي
async function getWhatsAppService() {
  if (!whatsappService) {
    try {
      console.log('🔄 محاولة تحميل خدمة WhatsApp...')
      whatsappService = require('../../../../../whatsapp-real-service')
      console.log('✅ تم تحميل خدمة WhatsApp الحقيقية المحسنة بنجاح')
      console.log('📊 نوع الخدمة:', typeof whatsappService)
    } catch (error) {
      console.error('❌ خطأ في تحميل خدمة WhatsApp:', error)
      console.error('❌ تفاصيل الخطأ:', error.message)
      console.error('❌ مسار الملف:', '../../../../../whatsapp-real-service')

      // محاولة مسار بديل
      try {
        console.log('🔄 محاولة مسار بديل...')
        const path = require('path')
        const servicePath = path.join(process.cwd(), 'whatsapp-real-service.js')
        console.log('📁 المسار الكامل:', servicePath)
        whatsappService = require(servicePath)
        console.log('✅ تم تحميل الخدمة من المسار البديل')
      } catch (altError) {
        console.error('❌ فشل في المسار البديل أيضاً:', altError.message)
        throw new Error(`فشل في تحميل خدمة WhatsApp: ${error.message}`)
      }
    }
  }
  return whatsappService
}

// تحميل إعدادات التوجيه
function loadRoutingConfig(): any | null {
  try {
    const cwd = process.cwd()
    const mainPath = path.join(cwd, 'routing.config.json')
    const prodPath = path.join(cwd, 'production', 'routing.config.json')

    let configPath = ''
    if (fs.existsSync(mainPath)) configPath = mainPath
    else if (fs.existsSync(prodPath)) configPath = prodPath
    else return null

    const content = fs.readFileSync(configPath, 'utf-8')
    return JSON.parse(content)
  } catch (e: any) {
    console.warn('⚠️ تعذّر تحميل routing.config.json:', e?.message)
    return null
  }
}

// تحميل إعدادات WhatsApp الخاصة بكل منفذ (إن وجدت)
function loadWhatsAppConfig(): any | null {
  try {
    const cwd = process.cwd()
    const mainPath = path.join(cwd, 'whatsapp.config.json')
    const prodPath = path.join(cwd, 'production', 'whatsapp.config.json')
    const configPath = fs.existsSync(mainPath) ? mainPath : (fs.existsSync(prodPath) ? prodPath : '')
    if (!configPath) return null
    return JSON.parse(fs.readFileSync(configPath, 'utf-8'))
  } catch (e: any) {
    console.warn('⚠️ تعذّر تحميل whatsapp.config.json:', e?.message)
    return null
  }
}

// تحديد إعداد المسار بناءً على منفذ الطلب
function getRouteForRequest(request: NextRequest) {
  try {
    // دعم المنافذ عبر البروكسي: X-Forwarded-Host / X-Forwarded-Port
    const xfHost = request.headers.get('x-forwarded-host') || ''
    const xfPortHeader = request.headers.get('x-forwarded-port') || ''

    const host = xfHost || request.headers.get('host') || ''
    // أمثلة: localhost:3000 أو 127.0.0.1:3001 أو example.com:7443
    const portStr = xfPortHeader || (host.includes(':') ? host.split(':').pop() : '')
    const port = portStr ? parseInt(portStr, 10) : NaN

    const cfg = loadRoutingConfig()
    if (!cfg) return { key: 'default', config: cfg?.default_config || {} }

    const routes = cfg.routes || {}
    const entries = Object.entries(routes) as Array<[string, any]>

    // 1) مطابقة المنفذ الداخلي (Next.js الداخلي)
    let matched = entries.find(([, v]) => Number(v?.internal_next_port) === port)
    if (matched) {
      return { key: matched[0], config: matched[1], raw: cfg }
    }

    // 2) مطابقة المنفذ الخارجي (مفتاح route مثل 7443/8914)
    if (portStr && entries.some(([k]) => k === portStr)) {
      matched = entries.find(([k]) => k === portStr) as [string, any] | undefined
      if (matched) {
        return { key: matched[0], config: matched[1], raw: cfg }
      }
    }

    // fallback: استخدم الإعداد الافتراضي
    return { key: 'default', config: cfg.default_config || {}, raw: cfg }
  } catch (e) {
    return { key: 'default', config: {} }
  }
}

// دالة توليد QR Code حقيقي المظهر
function generateRealQRCode(): string {
  // توليد نص QR Code يشبه WhatsApp Web
  const timestamp = Date.now()
  const randomString = Math.random().toString(36).substring(2, 15)
  const qrData = `1@${randomString},${timestamp},1,0`

  // إنشاء SVG QR Code بصري
  const size = 256
  const modules = 25 // عدد المربعات في QR Code
  const moduleSize = size / modules

  let svg = `<svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">`
  svg += `<rect width="${size}" height="${size}" fill="white"/>`

  // إضافة نمط QR Code
  for (let i = 0; i < modules; i++) {
    for (let j = 0; j < modules; j++) {
      // خوارزمية بسيطة لتوليد نمط QR Code
      const shouldFill = (i + j + timestamp) % 3 === 0 ||
                        (i === 0 || i === modules-1 || j === 0 || j === modules-1) ||
                        (i < 7 && j < 7) || (i < 7 && j > modules-8) || (i > modules-8 && j < 7)

      if (shouldFill) {
        const x = j * moduleSize
        const y = i * moduleSize
        svg += `<rect x="${x}" y="${y}" width="${moduleSize}" height="${moduleSize}" fill="black"/>`
      }
    }
  }

  // إضافة المربعات الثلاثة المميزة لـ QR Code
  const cornerSize = moduleSize * 7

  // المربع العلوي الأيسر
  svg += `<rect x="0" y="0" width="${cornerSize}" height="${cornerSize}" fill="none" stroke="black" stroke-width="2"/>`
  svg += `<rect x="${moduleSize*2}" y="${moduleSize*2}" width="${moduleSize*3}" height="${moduleSize*3}" fill="black"/>`

  // المربع العلوي الأيمن
  svg += `<rect x="${size-cornerSize}" y="0" width="${cornerSize}" height="${cornerSize}" fill="none" stroke="black" stroke-width="2"/>`
  svg += `<rect x="${size-moduleSize*5}" y="${moduleSize*2}" width="${moduleSize*3}" height="${moduleSize*3}" fill="black"/>`

  // المربع السفلي الأيسر
  svg += `<rect x="0" y="${size-cornerSize}" width="${cornerSize}" height="${cornerSize}" fill="none" stroke="black" stroke-width="2"/>`
  svg += `<rect x="${moduleSize*2}" y="${size-moduleSize*5}" width="${moduleSize*3}" height="${moduleSize*3}" fill="black"/>`

  svg += '</svg>'

  // تحويل إلى base64
  const base64 = Buffer.from(svg).toString('base64')
  return `data:image/svg+xml;base64,${base64}`
}

// دالة بدء WhatsApp الحقيقي
async function startRealWhatsApp(sessionName?: string): Promise<{success: boolean, message?: string, error?: string}> {
  try {
    console.log('🚀 بدء خدمة WhatsApp الحقيقية...')

    const service = await getWhatsAppService()
    if (sessionName) {
      service.sessionName = sessionName
      console.log('🆔 استخدام اسم جلسة:', service.sessionName)
    }
    const result = await service.start()

    return result
  } catch (error) {
    console.error('❌ خطأ في بدء WhatsApp:', error)
    return {
      success: false,
      error: 'فشل في بدء WhatsApp: ' + error.message
    }
  }
}

// دالة إيقاف WhatsApp الحقيقي
async function stopRealWhatsApp(): Promise<{success: boolean, message?: string, error?: string}> {
  try {
    console.log('🛑 إيقاف خدمة WhatsApp الحقيقية...')

    const service = await getWhatsAppService()
    const result = await service.stop()

    return result
  } catch (error) {
    console.error('❌ خطأ في إيقاف WhatsApp:', error)
    return {
      success: false,
      error: 'فشل في إيقاف WhatsApp: ' + error.message
    }
  }
}

// دالة جلب حالة WhatsApp الحقيقي
async function getRealWhatsAppStatus(): Promise<any> {
  try {
    const service = await getWhatsAppService()
    return service.getStatus()
  } catch (error) {
    console.error('❌ خطأ في جلب حالة WhatsApp:', error)
    return {
      status: 'disconnected',
      isReady: false,
      qrCode: null,
      hasClient: false
    }
  }
}

// POST - بدء أو إيقاف WhatsApp
export async function POST(request: NextRequest) {
  try {
    console.log('📥 تم استلام طلب POST لـ WhatsApp')

    // دعم استخراج الإجراء من عدة صيغ: JSON، نص خام، form-urlencoded، أو بارامتر استعلام
    let action: string | null = null
    let chromePath: string | null = null
    let body: any = {}
    let chromeArgs: string | string[] | null = null
    let headless: string | boolean | null = null
    // معلمات ضبط المهلة وعدد المحاولات
    let connectionTimeout: string | number | null = null
    let connectionTimeoutMs: string | number | null = null
    let maxRetries: string | number | null = null
    // 1) محاولة JSON مباشرة
    try {
      body = await request.json()
      action = body?.action
      chromePath = body?.chromePath || body?.chrome_path
      chromeArgs = body?.chromeArgs || body?.chrome_args
      if (typeof body?.headless !== 'undefined') headless = body.headless
      if (typeof body?.connectionTimeout !== 'undefined') connectionTimeout = body.connectionTimeout
      if (typeof body?.connection_timeout !== 'undefined') connectionTimeout = body.connection_timeout
      if (typeof body?.connectionTimeoutMs !== 'undefined') connectionTimeoutMs = body.connectionTimeoutMs
      if (typeof body?.connection_timeout_ms !== 'undefined') connectionTimeoutMs = body.connection_timeout_ms
      if (typeof body?.maxRetries !== 'undefined') maxRetries = body.maxRetries
      if (typeof body?.max_retries !== 'undefined') maxRetries = body.max_retries
    } catch (_jsonErr) {
      // 2) محاولة قراءة نص خام ثم JSON أو form-urlencoded
      try {
        const raw = await request.text()
        if (raw) {
          // أولوية: JSON إن أمكن
          try {
            body = JSON.parse(raw)
            action = body?.action
            chromePath = body?.chromePath || body?.chrome_path
            chromeArgs = body?.chromeArgs || body?.chrome_args
            if (typeof body?.headless !== 'undefined') headless = body.headless
            if (typeof body?.connectionTimeout !== 'undefined') connectionTimeout = body.connectionTimeout
            if (typeof body?.connection_timeout !== 'undefined') connectionTimeout = body.connection_timeout
            if (typeof body?.connectionTimeoutMs !== 'undefined') connectionTimeoutMs = body.connectionTimeoutMs
            if (typeof body?.connection_timeout_ms !== 'undefined') connectionTimeoutMs = body.connection_timeout_ms
            if (typeof body?.maxRetries !== 'undefined') maxRetries = body.maxRetries
            if (typeof body?.max_retries !== 'undefined') maxRetries = body.max_retries
          } catch (_rawJsonErr) {
            // تجربة form-urlencoded: action=start
            try {
              const params = new URLSearchParams(raw)
              action = params.get('action')
              chromePath = params.get('chromePath') || params.get('chrome_path')
              const argsParam = params.get('chromeArgs') || params.get('chrome_args')
              if (argsParam) chromeArgs = argsParam
              const headlessParam = params.get('headless')
              if (headlessParam !== null) headless = headlessParam
              const ct = params.get('connectionTimeout') || params.get('connection_timeout')
              if (ct !== null) connectionTimeout = ct
              const ctms = params.get('connectionTimeoutMs') || params.get('connection_timeout_ms')
              if (ctms !== null) connectionTimeoutMs = ctms
              const mr = params.get('maxRetries') || params.get('max_retries')
              if (mr !== null) maxRetries = mr
            } catch (_formErr) {
              // تجاهل
            }
          }
        }
      } catch (_rawErr) {
        // تجاهل وسنجرّب بارامترات الاستعلام
      }
    }

    // 3) بارامترات الاستعلام كخيار أخير: ?action=start
    if (!action) {
      const url = new URL(request.url)
      action = url.searchParams.get('action')
      chromePath = url.searchParams.get('chromePath') || url.searchParams.get('chrome_path')
      const argsParam = url.searchParams.get('chromeArgs') || url.searchParams.get('chrome_args')
      if (argsParam) chromeArgs = argsParam
      const headlessParam = url.searchParams.get('headless')
      if (headlessParam !== null) headless = headlessParam
      const ct = url.searchParams.get('connectionTimeout') || url.searchParams.get('connection_timeout')
      if (ct !== null) connectionTimeout = ct
      const ctms = url.searchParams.get('connectionTimeoutMs') || url.searchParams.get('connection_timeout_ms')
      if (ctms !== null) connectionTimeoutMs = ctms
      const mr = url.searchParams.get('maxRetries') || url.searchParams.get('max_retries')
      if (mr !== null) maxRetries = mr
    }

    if (!action) {
      return NextResponse.json({ success: false, error: 'لم يتم تحديد إجراء صالح (action)' }, { status: 400 })
    }

    console.log(`🔄 ${action === 'start' ? 'بدء' : action === 'stop' ? 'إيقاف' : action} خدمة WhatsApp...`)
    console.log('📊 بيانات الطلب (parsed):', body)
    if (chromePath) {
      process.env.CHROME_PATH = chromePath
      console.log('🟢 تم ضبط CHROME_PATH من الطلب:', chromePath)
    }
    if (chromeArgs) {
      const flags = Array.isArray(chromeArgs) ? chromeArgs.join(' ') : String(chromeArgs)
      process.env.CHROME_FLAGS = flags
      console.log('🟢 تم ضبط CHROME_FLAGS من الطلب:', flags)
    }
    if (headless !== null && typeof headless !== 'undefined') {
      const headlessStr = (headless === true || String(headless).toLowerCase() === 'true') ? 'true' : 'false'
      process.env.CHROME_HEADLESS = headlessStr
      console.log('🟢 تم ضبط CHROME_HEADLESS من الطلب:', headlessStr)
    }
    // ضبط المهلة وعدد المحاولات إذا وردت
    try {
      if (connectionTimeoutMs !== null && typeof connectionTimeoutMs !== 'undefined' && String(connectionTimeoutMs).trim() !== '') {
        const v = String(connectionTimeoutMs).trim()
        if (!Number.isNaN(Number(v))) {
          process.env.CONNECTION_TIMEOUT_MS = v
          console.log('🕒 تم ضبط CONNECTION_TIMEOUT_MS من الطلب:', v)
        }
      } else if (connectionTimeout !== null && typeof connectionTimeout !== 'undefined' && String(connectionTimeout).trim() !== '') {
        const v = String(connectionTimeout).trim()
        if (!Number.isNaN(Number(v))) {
          process.env.CONNECTION_TIMEOUT = v
          console.log('🕒 تم ضبط CONNECTION_TIMEOUT (بالثواني) من الطلب:', v)
        }
      }
      if (maxRetries !== null && typeof maxRetries !== 'undefined' && String(maxRetries).trim() !== '') {
        const v = String(maxRetries).trim()
        if (!Number.isNaN(Number(v))) {
          process.env.MAX_RETRIES = v
          console.log('🔁 تم ضبط MAX_RETRIES من الطلب:', v)
        }
      }
    } catch (_e) {}

    // تحديد إعداد التوجيه والجلسة
    const routeInfo = getRouteForRequest(request)
    const waCfg = loadWhatsAppConfig()
    const defaultSession = `whatsapp_${routeInfo?.config?.database || routeInfo?.key || 'default'}`
    const overrideSession = waCfg?.whatsapp_settings?.[routeInfo.key]?.session_name
    const sessionName = overrideSession || defaultSession

    if (action === 'start') {
      // قبل أي شيء، تحقّق من حالة الخدمة الحالية لتجنّب أخطاء "قيد التشغيل بالفعل"
      const service = await getWhatsAppService()
      // ضبط اسم الجلسة وفق التوجيه
      service.sessionName = sessionName
      // تطبيق ضبط المهلة وعدد المحاولات مباشرة على كائن الخدمة في حال تمريرها بالطلب
      try {
        if (connectionTimeoutMs !== null && typeof connectionTimeoutMs !== 'undefined' && String(connectionTimeoutMs).trim() !== '') {
          const v = Number(String(connectionTimeoutMs).trim())
          if (!Number.isNaN(v) && v > 0) {
            service.connectionTimeoutMs = v
            console.log('🕒 [OVERRIDE] connectionTimeoutMs على مستوى الخدمة =', v)
          }
        } else if (connectionTimeout !== null && typeof connectionTimeout !== 'undefined' && String(connectionTimeout).trim() !== '') {
          const vSec = Number(String(connectionTimeout).trim())
          if (!Number.isNaN(vSec) && vSec > 0) {
            service.connectionTimeoutMs = vSec * 1000
            console.log('🕒 [OVERRIDE] connectionTimeoutMs (من الثواني) على مستوى الخدمة =', service.connectionTimeoutMs)
          }
        }
        if (maxRetries !== null && typeof maxRetries !== 'undefined' && String(maxRetries).trim() !== '') {
          const m = Number(String(maxRetries).trim())
          if (!Number.isNaN(m) && m > 0) {
            service.maxRetries = m
            console.log('🔁 [OVERRIDE] maxRetries على مستوى الخدمة =', m)
          }
        }
      } catch (_e) {
        // تجاهل أخطاء التحويل
      }
      const current = service.getStatus()

      // إذا كانت متصلة بالفعل
      if (current.isReady || current.status === 'connected') {
        return NextResponse.json({
          success: true,
          message: 'WhatsApp متصل بالفعل',
          data: {
            connected: true,
            qrCode: null,
            status: 'connected',
            uptime: current.uptime || 0,
            lastActivity: current.lastActivity,
            isStarting: false,
            failureCount: current.failureCount,
            maxRetries: current.maxRetries,
            lastError: current.lastError,
            canRetry: current.canRetry,
            sessionName
          }
        })
      }

      // إذا كان قيد البدء أو في حالة connecting، اعتبرها نجاحاً لتسمح للواجهة ببدء التحديث
      if (current.isStarting || current.status === 'connecting') {
        return NextResponse.json({
          success: true,
          message: 'خدمة WhatsApp قيد البدء...',
          data: {
            connected: false,
            qrCode: current.qrCode,
            status: 'connecting',
            uptime: current.uptime || 0,
            lastActivity: current.lastActivity,
            isStarting: true,
            failureCount: current.failureCount,
            maxRetries: current.maxRetries,
            lastError: current.lastError,
            canRetry: current.canRetry,
            sessionName
          }
        })
      }

      // جلب إعدادات الشركة (مع تحمل أخطاء قاعدة البيانات)
      let company: any = null
      try {
        const companyResult = await query(`
          SELECT
            id,
            name,
            whatsapp_phone,
            whatsapp_business_name,
            whatsapp_enabled,
            whatsapp_session_name
          FROM companies
          WHERE is_active = true AND whatsapp_enabled = true
          LIMIT 1
        `)

        if (companyResult.rows.length > 0) {
          company = companyResult.rows[0]
        } else {
          console.warn('⚠️ لم يتم تفعيل WhatsApp لأي شركة، المتابعة بالإعدادات الافتراضية')
        }
      } catch (dbErr: any) {
        console.warn('⚠️ تعذّر جلب إعدادات الشركة من قاعدة البيانات، المتابعة بالإعدادات الافتراضية. السبب:', dbErr?.message)
      }

      // بدء خدمة WhatsApp الحقيقية
      const result = await startRealWhatsApp(sessionName)

      if (result.success) {
        return NextResponse.json({
          success: true,
          message: result.message || 'تم بدء خدمة WhatsApp',
          data: {
            connected: false,
            qrCode: (service.getStatus() || {}).qrCode || null,
            status: 'connecting',
            uptime: 0,
            lastActivity: null,
            isStarting: true,
            failureCount: (service.getStatus() || {}).failureCount || 0,
            maxRetries: (service.getStatus() || {}).maxRetries || 3,
            lastError: (service.getStatus() || {}).lastError || null,
            canRetry: (service.getStatus() || {}).canRetry !== false,
            sessionName
          }
        })
      } else {
        // إذا كان الخطأ بسبب أن الخدمة قيد التشغيل بالفعل، أعد نجاحاً مع حالة connecting
        const msg = (result.error || '').toString()
        if (msg.includes('قيد التشغيل بالفعل') || msg.includes('already')) {
          const st = service.getStatus()
          return NextResponse.json({
            success: true,
            message: 'الخدمة قيد التشغيل بالفعل',
            data: {
              connected: false,
              qrCode: st.qrCode,
              status: 'connecting',
              uptime: st.uptime || 0,
              lastActivity: st.lastActivity,
              isStarting: true,
              failureCount: st.failureCount,
              maxRetries: st.maxRetries,
              lastError: st.lastError,
              canRetry: st.canRetry,
              sessionName
            }
          })
        }

        return NextResponse.json({
          success: false,
          error: result.error
        }, { status: 500 })
      }

    } else if (action === 'stop') {
      // إيقاف خدمة WhatsApp الحقيقية
      const result = await stopRealWhatsApp()

      if (result.success) {
        return NextResponse.json({
          success: true,
          message: result.message || 'تم إيقاف خدمة WhatsApp',
          data: {
            status: 'stopped',
            sessionName
          }
        })
      } else {
        return NextResponse.json({
          success: false,
          error: result.error
        }, { status: 500 })
      }
    } else if (action === 'send') {
      // إرسال رسالة باستخدام نفس العملية لضمان وجود client جاهز
      const bodyJson = body || {}
      const phoneNumber = bodyJson.phoneNumber || bodyJson.phone_number
      const message = bodyJson.message
      const recipientName = bodyJson.recipientName || bodyJson.recipient_name || ''

      if (!phoneNumber || !message) {
        return NextResponse.json({ success: false, error: 'رقم الهاتف والرسالة مطلوبان' }, { status: 400 })
      }

      const service = await getWhatsAppService()
      // تأكيد اسم الجلسة وفق التوجيه
      service.sessionName = sessionName

      // تحقق من الاتصال، مع الرجوع إلى ملف الحالة المشترك
      let st = service.getStatus()
      try {
        if (!st || st.status !== 'connected') {
          const dataDir = path.join(process.cwd(), '.whatsapp_real')
          const statusFile = path.join(dataDir, `status-${sessionName}.json`)
          if (fs.existsSync(statusFile)) {
            const raw = fs.readFileSync(statusFile, 'utf-8')
            const fileStatus = JSON.parse(raw)
            if (fileStatus && (fileStatus.status === 'connected' || fileStatus.isReady)) {
              st = fileStatus
            }
          }
        }
      } catch (_e) {}

      if (!st?.isReady && st?.status !== 'connected') {
        return NextResponse.json({ success: false, error: 'WhatsApp غير متصل' }, { status: 400 })
      }

      // محاولة ربط سريع إن لم يكن هناك عميل في هذه العملية
      try {
        if ((!service.client || !service.isReady) && typeof (service as any).ensureClientReady === 'function') {
          const desiredTimeout = Math.min(Number(process.env.SEND_READY_TIMEOUT_MS || service.connectionTimeoutMs || 30000), 60000)
          if (!service.connectionTimeoutMs || service.connectionTimeoutMs < desiredTimeout) {
            service.connectionTimeoutMs = desiredTimeout
          }
          if (typeof (service as any).refreshSessionPaths === 'function') {
            (service as any).refreshSessionPaths()
          }
          await (service as any).ensureClientReady(desiredTimeout)
        }
      } catch (_e) {}

      const sendResult = await service.sendMessage(phoneNumber, message, recipientName)
      if (!sendResult.success) {
        return NextResponse.json({ success: false, error: sendResult.error }, { status: 400 })
      }
      return NextResponse.json({ success: true, message: 'تم إرسال الرسالة بنجاح', data: sendResult })

    } else if (action === 'reset') {
      const url = new URL(request.url)
      // دعم قراءة restart من الجسم/النص/الفورم أو من بارامتر الاستعلام
      let restart = false
      if (typeof (body as any)?.restart !== 'undefined') {
        const val = (body as any).restart
        restart = val === true || String(val).toLowerCase() === 'true'
      } else {
        restart = (url.searchParams.get('restart') || '').toLowerCase() === 'true'
      }

      const service = await getWhatsAppService()
      service.sessionName = sessionName
      try {
        // 1) إيقاف العميل إن كان يعمل
        await service.stop()
      } catch (e: any) {
        console.warn('⚠️ تعذّر إيقاف الخدمة قبل إعادة التهيئة:', e?.message)
      }

      // 2) حذف مجلدات الجلسة الخاصة بـ LocalAuth
      try {
        const baseDir = path.join(process.cwd(), '.whatsapp_real')
        const sessionDir = path.join(baseDir, service.sessionName)
        // حذف مجلد الجلسة الخاص فقط
        try {
          if (fs.existsSync(sessionDir)) {
            fs.rmSync(sessionDir, { recursive: true, force: true })
            console.log('🗑️ تم حذف مجلد الجلسة:', sessionDir)
          }
        } catch (delErr) {
          console.error('❌ فشل حذف مجلد الجلسة:', sessionDir, delErr)
        }
        // حذف ملف الحالة الخاص بالجولة الحالية
        const statusFile = path.join(baseDir, `status-${service.sessionName}.json`)
        try {
          if (fs.existsSync(statusFile)) {
            fs.rmSync(statusFile, { force: true })
            console.log('🗑️ تم حذف ملف الحالة:', statusFile)
          }
        } catch (delStatusErr) {
          console.error('❌ فشل حذف ملف الحالة:', statusFile, delStatusErr)
        }
      } catch (fsErr) {
        console.error('❌ خطأ أثناء تنظيف بيانات الجلسة:', fsErr)
      }

      // 3) إعادة تعيين حالة الفشل داخلياً
      try {
        if (typeof service.resetFailureState === 'function') {
          service.resetFailureState()
        }
      } catch (e) {
        // تجاهل
      }

      // 4) إعادة البدء اختيارياً
      if (restart) {
        if (chromePath) {
          process.env.CHROME_PATH = chromePath
        }
        console.log('🔁 إعادة البدء بعد reset للجلسة:', service.sessionName)
        // تطبيق أي تجاوزات للوقت وعدد المحاولات قبل إعادة البدء
        try {
          if (connectionTimeoutMs !== null && typeof connectionTimeoutMs !== 'undefined' && String(connectionTimeoutMs).trim() !== '') {
            const v = Number(String(connectionTimeoutMs).trim())
            if (!Number.isNaN(v) && v > 0) {
              service.connectionTimeoutMs = v
              console.log('🕒 [OVERRIDE] connectionTimeoutMs على مستوى الخدمة (بعد reset) =', v)
            }
          } else if (connectionTimeout !== null && typeof connectionTimeout !== 'undefined' && String(connectionTimeout).trim() !== '') {
            const vSec = Number(String(connectionTimeout).trim())
            if (!Number.isNaN(vSec) && vSec > 0) {
              service.connectionTimeoutMs = vSec * 1000
              console.log('🕒 [OVERRIDE] connectionTimeoutMs (من الثواني) على مستوى الخدمة (بعد reset) =', service.connectionTimeoutMs)
            }
          }
          if (maxRetries !== null && typeof maxRetries !== 'undefined' && String(maxRetries).trim() !== '') {
            const m = Number(String(maxRetries).trim())
            if (!Number.isNaN(m) && m > 0) {
              service.maxRetries = m
              console.log('🔁 [OVERRIDE] maxRetries على مستوى الخدمة (بعد reset) =', m)
            }
          }
        } catch (_e) {}
        const result = await service.start()
        return NextResponse.json({ success: true, message: 'تم التنفيذ: reset ثم start', data: service.getStatus(), details: result })
      }

      return NextResponse.json({ success: true, message: 'تم تنفيذ reset للجلسة وحذف بيانات LocalAuth. يمكنك البدء الآن.' })
    }

    return NextResponse.json({
      success: false,
      error: 'إجراء غير صحيح'
    }, { status: 400 })

  } catch (error) {
    console.error('❌ خطأ في التحكم بـ WhatsApp:', error)
    console.error('❌ تفاصيل الخطأ:', error.message)
    console.error('❌ stack trace:', error.stack)

    return NextResponse.json({
      success: false,
      error: `خطأ في الخادم: ${error.message}`,
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    }, { status: 500 })
  }
}

// GET - جلب حالة WhatsApp الحقيقية
export async function GET(request: NextRequest) {
  try {
    const routeInfo = getRouteForRequest(request)
    const waCfg = loadWhatsAppConfig()
    const defaultSession = `whatsapp_${routeInfo?.config?.database || routeInfo?.key || 'default'}`
    const overrideSession = waCfg?.whatsapp_settings?.[routeInfo.key]?.session_name
    const sessionName = overrideSession || defaultSession
    const service = await getWhatsAppService()
    // تأكيد اسم الجلسة وفق التوجيه
    service.sessionName = sessionName
    let status = service.getStatus()

    // إذا كانت الحالة ليست "connected"، استخدم الملف المشترك كمرجع موثوق لتغطية اختلاف الـ worker
    try {
      if (!status || status.status !== 'connected') {
        const dataDir = path.join(process.cwd(), '.whatsapp_real')
        const statusFile = path.join(dataDir, `status-${sessionName}.json`)
        if (fs.existsSync(statusFile)) {
          const raw = fs.readFileSync(statusFile, 'utf-8')
          const fileStatus = JSON.parse(raw)
          if (fileStatus && (fileStatus.status === 'connected' || fileStatus.qrCode)) {
            status = fileStatus
          }
        }
      }
    } catch (_e) {
      // تجاهل أخطاء القراءة
    }

    console.log('📊 حالة WhatsApp الحالية:', {
      isReady: status.isReady,
      status: status.status,
      hasQR: !!status.qrCode
    })

    return NextResponse.json({
      success: true,
      data: {
        connected: status.isReady,
        qrCode: status.qrCode,
        status: status.status,
        uptime: status.uptime || 0,
        lastActivity: status.lastActivity,
        hasClient: status.hasClient,
        isStarting: status.isStarting,
        failureCount: status.failureCount,
        maxRetries: status.maxRetries || 5,
        lastError: status.lastError,
        canRetry: status.canRetry,
        connectionTimeoutMs: status.connectionTimeoutMs || null,
        sessionName
      }
    })
  } catch (error) {
    console.error('❌ خطأ في جلب حالة WhatsApp:', error)
    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم'
    }, { status: 500 })
  }
}

// دالة توليد QR Code وهمي
function generateQRCode() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < 200; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return `data:image/svg+xml;base64,${Buffer.from(`
    <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
      <rect width="200" height="200" fill="white"/>
      <text x="100" y="100" text-anchor="middle" font-family="Arial" font-size="12" fill="black">
        QR Code للاتصال بـ WhatsApp
      </text>
      <text x="100" y="120" text-anchor="middle" font-family="Arial" font-size="10" fill="gray">
        امسح هذا الكود من هاتفك
      </text>
      <rect x="20" y="20" width="160" height="160" fill="none" stroke="black" stroke-width="2"/>
      ${generateQRPattern()}
    </svg>
  `).toString('base64')}`
}

function generateQRPattern() {
  let pattern = ''
  for (let i = 0; i < 10; i++) {
    for (let j = 0; j < 10; j++) {
      if (Math.random() > 0.5) {
        pattern += `<rect x="${30 + i * 14}" y="${30 + j * 14}" width="12" height="12" fill="black"/>`
      }
    }
  }
  return pattern
}
