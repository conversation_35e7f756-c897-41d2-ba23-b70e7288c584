// اختبار بسيط للخادم
console.log('🚀 بدء اختبار الخادم...');

try {
  // فحص Node.js
  console.log('✅ Node.js version:', process.version);
  
  // فحص المجلد الحالي
  console.log('📁 Current directory:', process.cwd());
  
  // فحص وجود الملفات المطلوبة
  const fs = require('fs');
  const path = require('path');
  
  const routingFile = path.join(process.cwd(), 'routing.config.json');
  const serverFile = path.join(process.cwd(), 'advanced-unified-server.js');
  
  console.log('🔍 فحص الملفات:');
  console.log('   - routing.config.json:', fs.existsSync(routingFile) ? '✅ موجود' : '❌ مفقود');
  console.log('   - advanced-unified-server.js:', fs.existsSync(serverFile) ? '✅ موجود' : '❌ مفقود');
  
  if (fs.existsSync(routingFile)) {
    try {
      const config = JSON.parse(fs.readFileSync(routingFile, 'utf8'));
      console.log('📋 إعدادات التوجيه:');
      console.log('   - المنافذ المحددة:', Object.keys(config.routes || {}));
      console.log('   - قواعد البيانات:', Object.values(config.routes || {}).map(r => r.database));
    } catch (error) {
      console.log('❌ خطأ في قراءة ملف التوجيه:', error.message);
    }
  }
  
  // اختبار الاتصال بقاعدة البيانات
  console.log('\n🔗 اختبار الاتصال بقاعدة البيانات...');
  const { Client } = require('pg');
  
  const testDB = async (dbName) => {
    const client = new Client({
      host: 'localhost',
      port: 5432,
      user: 'postgres',
      password: 'yemen123',
      database: dbName
    });
    
    try {
      await client.connect();
      console.log(`   ✅ ${dbName}: متصل`);
      await client.end();
      return true;
    } catch (error) {
      console.log(`   ❌ ${dbName}: فشل الاتصال - ${error.message}`);
      return false;
    }
  };
  
  // اختبار قواعد البيانات
  testDB('mohammi').then(() => testDB('rubaie')).then(() => {
    console.log('\n🎯 انتهاء الاختبار');
  });
  
} catch (error) {
  console.error('❌ خطأ في الاختبار:', error.message);
}
