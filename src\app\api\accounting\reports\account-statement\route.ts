import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database-router'

// GET - كشف حساب
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const accountId = searchParams.get('account_id')
    const dateFrom = searchParams.get('date_from')
    const dateTo = searchParams.get('date_to')

    if (!accountId) {
      return NextResponse.json({
        success: false,
        error: 'معرف الحساب مطلوب'
      }, { status: 400 })
    }

    // التحقق من وجود الحساب
    const accountResult = await query(`
      SELECT
        id, account_code, account_name, account_type, account_nature,
        opening_balance, current_balance
      FROM chart_of_accounts
      WHERE id = $1 AND is_active = true
    `, [accountId])

    if (accountResult.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الحساب غير موجود'
      }, { status: 404 })
    }

    const account = accountResult.rows[0]

    // بناء استعلام كشف الحساب
    let sql = `
      SELECT
        jed.journal_entry_id,
        je.entry_number,
        je.entry_date,
        COALESCE(jed.description, je.description) as description,
        jed.debit_amount,
        jed.credit_amount,
        jed.line_order,
        je.entry_type,
        je.party_name,
        je.created_date
      FROM journal_entry_details jed
      INNER JOIN journal_entries je ON jed.journal_entry_id = je.id
      WHERE jed.account_id = $1
        AND je.status IN ('draft', 'approved')
    `

    const params: any[] = [accountId]
    let paramIndex = 2

    // تصفية حسب التاريخ
    if (dateFrom) {
      sql += ` AND je.entry_date >= $${paramIndex}`
      params.push(dateFrom)
      paramIndex++
    }

    if (dateTo) {
      sql += ` AND je.entry_date <= $${paramIndex}`
      params.push(dateTo)
      paramIndex++
    }

    sql += ` ORDER BY je.entry_date ASC, je.entry_number ASC, jed.line_order ASC`

    // تنفيذ الاستعلام
    const transactionsResult = await query(sql, params)
    const transactions = transactionsResult.rows

    // حساب الرصيد الجاري والإحصائيات
    let runningBalance = account.opening_balance || 0
    let totalDebit = 0
    let totalCredit = 0

    console.log(`📊 بدء حساب كشف الحساب للحساب: ${account.account_name}`)
    console.log(`💰 الرصيد الافتتاحي: ${runningBalance}`)
    console.log(`🔢 طبيعة الحساب: ${account.account_nature}`)
    console.log(`📋 عدد المعاملات: ${transactions.length}`)

    const processedTransactions = transactions.map(transaction => {
      const debitAmount = parseFloat(transaction.debit_amount) || 0
      const creditAmount = parseFloat(transaction.credit_amount) || 0

      totalDebit += debitAmount
      totalCredit += creditAmount

      // حساب الرصيد حسب طبيعة الحساب
      if (account.account_nature === 'مدين') {
        runningBalance += debitAmount - creditAmount
      } else {
        runningBalance += creditAmount - debitAmount
      }

      // تقريب الرصيد إلى رقمين عشريين
      runningBalance = Math.round(runningBalance * 100) / 100

      // نوع المعاملة والوصف
      let transactionType = 'قيد يومي'
      if (transaction.entry_type === 'receipt') transactionType = 'سند قبض'
      else if (transaction.entry_type === 'payment') transactionType = 'سند صرف'

      let description = transaction.description
      if (transaction.party_name) {
        description = `${transactionType} ${transaction.entry_type === 'payment' ? 'إلى' : 'من'} ${transaction.party_name} - ${description}`
      }

      const result = {
        ...transaction,
        transaction_type: transactionType,
        full_description: description,
        running_balance: parseFloat(runningBalance.toFixed(2)),
        debit_amount: parseFloat(debitAmount.toFixed(2)),
        credit_amount: parseFloat(creditAmount.toFixed(2))
      }

      // تسجيل للتشخيص
      console.log(`💰 معاملة ${transaction.entry_number}: مدين=${debitAmount.toFixed(2)}, دائن=${creditAmount.toFixed(2)}, الرصيد=${runningBalance.toFixed(2)}`)

      return result
    })

    // حساب الرصيد النهائي
    const finalBalance = runningBalance
    const balanceType = account.account_nature === 'مدين'
      ? (finalBalance >= 0 ? 'مدين' : 'دائن')
      : (finalBalance >= 0 ? 'دائن' : 'مدين')

    return NextResponse.json({
      success: true,
      data: {
        account: {
          id: account.id,
          code: account.account_code,
          name: account.account_name,
          type: account.account_type,
          nature: account.account_nature,
          opening_balance: account.opening_balance || 0
        },
        period: {
          date_from: dateFrom,
          date_to: dateTo
        },
        transactions: processedTransactions,
        summary: {
          total_debit: parseFloat(totalDebit.toFixed(2)),
          total_credit: parseFloat(totalCredit.toFixed(2)),
          final_balance: parseFloat(Math.abs(finalBalance).toFixed(2)),
          balance_type: balanceType,
          transactions_count: transactions.length
        }
      },
      message: 'تم جلب كشف الحساب بنجاح'
    })

  } catch (error) {
    console.error('خطأ في جلب كشف الحساب:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب كشف الحساب',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
