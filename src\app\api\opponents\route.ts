import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database-router'

async function ensureSchema() {
  await query(`
    CREATE TABLE IF NOT EXISTS opponents (
      id SERIAL PRIMARY KEY,
      name VARCHAR(255) NOT NULL,
      phone VARCHAR(50),
      address TEXT,
      notes TEXT,
      created_at TIMESTAMP DEFAULT NOW()
    );
  `)
  await query(`
    ALTER TABLE issues
    ADD COLUMN IF NOT EXISTS opponent_id INTEGER REFERENCES opponents(id);
  `)
}

// GET /api/opponents - قائمة الخصوم
export async function GET() {
  try {
    await ensureSchema()
    const res = await query('SELECT id, name, phone, address, notes FROM opponents ORDER BY name')
    return NextResponse.json({ success: true, data: res.rows })
  } catch (e: any) {
    console.error('GET opponents error:', e)
    return NextResponse.json({ success: false, error: 'فشل في جلب الخصوم' }, { status: 500 })
  }
}

// POST /api/opponents - إضافة خصم
export async function POST(req: NextRequest) {
  try {
    await ensureSchema()
    const body = await req.json()
    const name = (body?.name || '').trim()
    const phone = (body?.phone || '').trim() || null
    const address = (body?.address || '').trim() || null
    const notes = (body?.notes || '').trim() || null
    if (!name) return NextResponse.json({ success: false, error: 'اسم الخصم مطلوب' }, { status: 400 })

    const ins = await query(
      'INSERT INTO opponents(name, phone, address, notes) VALUES($1,$2,$3,$4) RETURNING id, name, phone, address, notes',
      [name, phone, address, notes]
    )
    return NextResponse.json({ success: true, data: ins.rows[0], message: 'تم إضافة الخصم' })
  } catch (e: any) {
    console.error('POST opponents error:', e)
    return NextResponse.json({ success: false, error: 'فشل في إضافة الخصم' }, { status: 500 })
  }
}

// PUT /api/opponents?id=
export async function PUT(req: NextRequest) {
  try {
    await ensureSchema()
    const { searchParams } = new URL(req.url)
    const id = searchParams.get('id')
    if (!id) return NextResponse.json({ success: false, error: 'المعرف مطلوب' }, { status: 400 })
    const body = await req.json()
    const name = (body?.name || '').trim()
    const phone = (body?.phone || '').trim() || null
    const address = (body?.address || '').trim() || null
    const notes = (body?.notes || '').trim() || null
    if (!name) return NextResponse.json({ success: false, error: 'اسم الخصم مطلوب' }, { status: 400 })

    const upd = await query(
      'UPDATE opponents SET name=$1, phone=$2, address=$3, notes=$4 WHERE id=$5 RETURNING id, name, phone, address, notes',
      [name, phone, address, notes, id]
    )
    if (upd.rows.length === 0) return NextResponse.json({ success: false, error: 'الخصم غير موجود' }, { status: 404 })
    return NextResponse.json({ success: true, data: upd.rows[0], message: 'تم التحديث' })
  } catch (e: any) {
    console.error('PUT opponents error:', e)
    return NextResponse.json({ success: false, error: 'فشل في تحديث بيانات الخصم' }, { status: 500 })
  }
}

// DELETE /api/opponents?id=
export async function DELETE(req: NextRequest) {
  try {
    await ensureSchema()
    const { searchParams } = new URL(req.url)
    const id = searchParams.get('id')
    if (!id) return NextResponse.json({ success: false, error: 'المعرف مطلوب' }, { status: 400 })

    // منع الحذف إذا كان مرتبطًا بقضايا
    const used = await query('SELECT 1 FROM issues WHERE opponent_id=$1 LIMIT 1', [id])
    if (used.rows.length) {
      return NextResponse.json({ success: false, error: 'لا يمكن حذف الخصم لوجود قضايا مرتبطة' }, { status: 400 })
    }

    const del = await query('DELETE FROM opponents WHERE id=$1 RETURNING id', [id])
    if (del.rows.length === 0) return NextResponse.json({ success: false, error: 'الخصم غير موجود' }, { status: 404 })
    return NextResponse.json({ success: true, message: 'تم حذف الخصم' })
  } catch (e: any) {
    console.error('DELETE opponents error:', e)
    return NextResponse.json({ success: false, error: 'فشل في حذف الخصم' }, { status: 500 })
  }
}
