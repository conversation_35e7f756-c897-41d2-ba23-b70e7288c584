const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function addUserRoles() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔄 جاري الاتصال بقاعدة البيانات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // إضافة عمود الأدوار إلى جدول المستخدمين
    console.log('🔄 إضافة عمود الأدوار...');
    await client.query(`
      ALTER TABLE users 
      ADD COLUMN IF NOT EXISTS role VARCHAR(50) DEFAULT 'user'
    `);

    // إضافة عمود الصلاحيات
    console.log('🔄 إضافة عمود الصلاحيات...');
    await client.query(`
      ALTER TABLE users 
      ADD COLUMN IF NOT EXISTS permissions TEXT[] DEFAULT '{}'
    `);

    // تحديث الأدوار للمستخدمين الموجودين
    console.log('🔄 تحديث أدوار المستخدمين...');
    
    // تعيين دور الأدمن للمستخدم admin
    await client.query(`
      UPDATE users 
      SET role = 'admin', 
          permissions = '{manage_users,manage_follows,manage_cases,manage_clients,manage_accounting}'
      WHERE username = 'admin'
    `);

    // تعيين دور المدير للمستخدم majed.manager
    await client.query(`
      UPDATE users 
      SET role = 'manager', 
          permissions = '{manage_follows,manage_cases,view_reports,manage_clients}'
      WHERE username = 'majed.manager'
    `);

    // تعيين دور المحامي للمحامين
    await client.query(`
      UPDATE users 
      SET role = 'lawyer', 
          permissions = '{add_follows,view_cases,manage_hearings}'
      WHERE username IN ('yahya.lawyer', 'ahmed.consultant', 'ahmed.mobile')
    `);

    // تعيين دور المحاسب
    await client.query(`
      UPDATE users 
      SET role = 'accountant', 
          permissions = '{manage_accounting,view_financial_reports}'
      WHERE username = 'mohamed.accountant'
    `);

    // تعيين دور السكرتير
    await client.query(`
      UPDATE users 
      SET role = 'secretary', 
          permissions = '{add_follows,view_cases,manage_documents}'
      WHERE username = 'fatima.secretary'
    `);

    // إنشاء جدول الأدوار والصلاحيات
    console.log('🔄 إنشاء جدول الأدوار...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS user_roles (
        id SERIAL PRIMARY KEY,
        role_name VARCHAR(50) UNIQUE NOT NULL,
        display_name VARCHAR(100) NOT NULL,
        description TEXT,
        permissions TEXT[] DEFAULT '{}',
        is_active BOOLEAN DEFAULT true,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // إدراج الأدوار الافتراضية
    const roles = [
      {
        role_name: 'admin',
        display_name: 'مدير النظام',
        description: 'صلاحيات كاملة لإدارة النظام',
        permissions: ['manage_users', 'manage_follows', 'manage_cases', 'manage_clients', 'manage_accounting', 'view_all_reports']
      },
      {
        role_name: 'manager',
        display_name: 'مدير المكتب',
        description: 'إدارة القضايا والمتابعات والعملاء',
        permissions: ['manage_follows', 'manage_cases', 'view_reports', 'manage_clients', 'approve_follows']
      },
      {
        role_name: 'lawyer',
        display_name: 'محامي',
        description: 'إضافة المتابعات وإدارة القضايا',
        permissions: ['add_follows', 'view_cases', 'manage_hearings', 'view_own_cases']
      },
      {
        role_name: 'accountant',
        display_name: 'محاسب',
        description: 'إدارة الحسابات والتقارير المالية',
        permissions: ['manage_accounting', 'view_financial_reports', 'manage_invoices']
      },
      {
        role_name: 'secretary',
        display_name: 'سكرتير',
        description: 'إضافة المتابعات وإدارة الوثائق',
        permissions: ['add_follows', 'view_cases', 'manage_documents', 'schedule_appointments']
      },
      {
        role_name: 'user',
        display_name: 'مستخدم عادي',
        description: 'صلاحيات محدودة للعرض فقط',
        permissions: ['view_own_data']
      }
    ];

    for (const role of roles) {
      await client.query(`
        INSERT INTO user_roles (role_name, display_name, description, permissions)
        VALUES ($1, $2, $3, $4)
        ON CONFLICT (role_name) DO UPDATE SET
          display_name = EXCLUDED.display_name,
          description = EXCLUDED.description,
          permissions = EXCLUDED.permissions
      `, [role.role_name, role.display_name, role.description, role.permissions]);
    }

    // التحقق من النتائج
    console.log('🔄 التحقق من النتائج...');
    const result = await client.query(`
      SELECT u.id, u.username, u.role, u.permissions, e.name as employee_name
      FROM users u
      LEFT JOIN employees e ON u.employee_id = e.id
      ORDER BY u.id
    `);

    console.log('👥 المستخدمين مع الأدوار الجديدة:');
    result.rows.forEach(user => {
      console.log(`  - ${user.username} (${user.employee_name}): ${user.role} - صلاحيات: ${user.permissions?.join(', ') || 'لا توجد'}`);
    });

    const rolesResult = await client.query('SELECT * FROM user_roles ORDER BY id');
    console.log('\n🎭 الأدوار المتاحة:');
    rolesResult.rows.forEach(role => {
      console.log(`  - ${role.display_name} (${role.role_name}): ${role.permissions?.join(', ')}`);
    });

    console.log('\n✅ تم إضافة نظام الأدوار والصلاحيات بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في إضافة نظام الأدوار:', error);
  } finally {
    await client.end();
  }
}

addUserRoles();