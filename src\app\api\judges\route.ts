import { NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET /api/judges - يعيد قائمة القضاة
export async function GET() {
  try {
    const result = await query(`SELECT id, name FROM judges ORDER BY name`)
    return NextResponse.json({ success: true, data: result.rows })
  } catch (error: any) {
    console.error('Error fetching judges:', error)
    return NextResponse.json({ success: false, error: 'فشل في جلب القضاة' }, { status: 500 })
  }
}

// POST /api/judges - إضافة قاضٍ جديد
export async function POST(req: Request) {
  try {
    const body = await req.json()
    const name = (body?.name || '').trim()
    if (!name) return NextResponse.json({ success: false, error: 'اسم القاضي مطلوب' }, { status: 400 })

    const insert = await query(`INSERT INTO judges(name) VALUES ($1) RETURNING id, name`, [name])
    return NextResponse.json({ success: true, data: insert.rows[0], message: 'تم إضافة القاضي' })
  } catch (error: any) {
    console.error('Error creating judge:', error)
    return NextResponse.json({ success: false, error: 'فشل في إضافة القاضي' }, { status: 500 })
  }
}

// PUT /api/judges?id= - تعديل اسم القاضي
export async function PUT(req: Request) {
  try {
    const { searchParams } = new URL(req.url)
    const id = searchParams.get('id')
    if (!id) return NextResponse.json({ success: false, error: 'المعرف مطلوب' }, { status: 400 })
    const body = await req.json()
    const name = (body?.name || '').trim()
    if (!name) return NextResponse.json({ success: false, error: 'اسم القاضي مطلوب' }, { status: 400 })

    const upd = await query(`UPDATE judges SET name=$1 WHERE id=$2 RETURNING id, name`, [name, id])
    if (upd.rows.length === 0) return NextResponse.json({ success: false, error: 'القاضي غير موجود' }, { status: 404 })
    return NextResponse.json({ success: true, data: upd.rows[0], message: 'تم التحديث' })
  } catch (error: any) {
    console.error('Error updating judge:', error)
    return NextResponse.json({ success: false, error: 'فشل في تحديث القاضي' }, { status: 500 })
  }
}

// DELETE /api/judges?id=
export async function DELETE(req: Request) {
  try {
    const { searchParams } = new URL(req.url)
    const id = searchParams.get('id')
    if (!id) return NextResponse.json({ success: false, error: 'المعرف مطلوب' }, { status: 400 })

    // التحقق من الارتباطات
    const usedInCD = await query(`SELECT 1 FROM case_distribution WHERE judge_id=$1 LIMIT 1`, [id])
    const usedInFollows = await query(`SELECT 1 FROM follows WHERE judge_id=$1 LIMIT 1`, [id])
    if (usedInCD.rows.length || usedInFollows.rows.length) {
      return NextResponse.json({ success: false, error: 'لا يمكن حذف القاضي لوجود سجلات مرتبطة' }, { status: 400 })
    }

    const del = await query(`DELETE FROM judges WHERE id=$1 RETURNING id`, [id])
    if (del.rows.length === 0) return NextResponse.json({ success: false, error: 'القاضي غير موجود' }, { status: 404 })
    return NextResponse.json({ success: true, message: 'تم حذف القاضي' })
  } catch (error: any) {
    console.error('Error deleting judge:', error)
    return NextResponse.json({ success: false, error: 'فشل في حذف القاضي' }, { status: 500 })
  }
}
