/**
 * مدير الجلسات - نظام الجلسة الواحدة
 * يسمح بجلسة واحدة فقط لكل مستخدم في نفس الوقت
 */

import { query } from './db'
import jwt from 'jsonwebtoken'
import crypto from 'crypto'

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-here'

export interface SessionInfo {
  id: number
  userId: number
  sessionToken: string
  deviceInfo?: string
  ipAddress?: string
  userAgent?: string
  loginTime: Date
  lastActivity: Date
  isActive: boolean
}

export interface LoginResult {
  success: boolean
  token?: string
  sessionToken?: string
  user?: any
  message?: string
  wasLoggedOutFromOtherDevices?: boolean
}

export class SessionManager {
  
  /**
   * إنشاء جلسة جديدة وإنهاء الجلسات الأخرى
   */
  static async createSession(
    userId: number, 
    deviceInfo?: string, 
    ipAddress?: string, 
    userAgent?: string
  ): Promise<string> {
    try {
      // توليد رمز جلسة فريد
      const sessionToken = crypto.randomBytes(32).toString('hex')
      
      // إنهاء جميع الجلسات الأخرى للمستخدم
      await this.terminateOtherSessions(userId, sessionToken)
      
      // إنشاء الجلسة الجديدة
      await query(`
        INSERT INTO active_sessions (
          user_id, session_token, device_info, ip_address, user_agent
        ) VALUES ($1, $2, $3, $4, $5)
      `, [userId, sessionToken, deviceInfo, ipAddress, userAgent])
      
      // تحديث رمز الجلسة في جدول المستخدمين
      await query(`
        UPDATE users 
        SET current_session_token = $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
      `, [sessionToken, userId])
      
      console.log(`✅ تم إنشاء جلسة جديدة للمستخدم ${userId}`)
      
      return sessionToken
      
    } catch (error) {
      console.error('❌ خطأ في إنشاء الجلسة:', error)
      throw error
    }
  }
  
  /**
   * إنهاء جميع الجلسات الأخرى للمستخدم
   */
  static async terminateOtherSessions(userId: number, currentSessionToken: string): Promise<void> {
    try {
      await query(`
        SELECT terminate_other_sessions($1, $2)
      `, [userId, currentSessionToken])
      
      console.log(`🔄 تم إنهاء الجلسات الأخرى للمستخدم ${userId}`)
      
    } catch (error) {
      console.error('❌ خطأ في إنهاء الجلسات الأخرى:', error)
      throw error
    }
  }
  
  /**
   * التحقق من صحة الجلسة
   */
  static async validateSession(sessionToken: string): Promise<SessionInfo | null> {
    try {
      const result = await query(`
        SELECT * FROM active_sessions 
        WHERE session_token = $1 
          AND is_active = true
          AND last_activity > NOW() - INTERVAL '24 hours'
      `, [sessionToken])
      
      if (result.rows.length === 0) {
        return null
      }
      
      const session = result.rows[0]
      
      // تحديث آخر نشاط
      await this.updateSessionActivity(sessionToken)
      
      return {
        id: session.id,
        userId: session.user_id,
        sessionToken: session.session_token,
        deviceInfo: session.device_info,
        ipAddress: session.ip_address,
        userAgent: session.user_agent,
        loginTime: session.login_time,
        lastActivity: session.last_activity,
        isActive: session.is_active
      }
      
    } catch (error) {
      console.error('❌ خطأ في التحقق من الجلسة:', error)
      return null
    }
  }
  
  /**
   * تحديث آخر نشاط للجلسة
   */
  static async updateSessionActivity(sessionToken: string): Promise<void> {
    try {
      await query(`
        SELECT update_session_activity($1)
      `, [sessionToken])
      
    } catch (error) {
      console.error('❌ خطأ في تحديث نشاط الجلسة:', error)
    }
  }
  
  /**
   * إنهاء جلسة محددة
   */
  static async terminateSession(sessionToken: string): Promise<void> {
    try {
      await query(`
        UPDATE active_sessions 
        SET is_active = false, updated_at = CURRENT_TIMESTAMP
        WHERE session_token = $1
      `, [sessionToken])
      
      // إزالة رمز الجلسة من جدول المستخدمين
      await query(`
        UPDATE users 
        SET current_session_token = NULL, updated_at = CURRENT_TIMESTAMP
        WHERE current_session_token = $1
      `, [sessionToken])
      
      console.log(`🔚 تم إنهاء الجلسة: ${sessionToken}`)
      
    } catch (error) {
      console.error('❌ خطأ في إنهاء الجلسة:', error)
      throw error
    }
  }
  
  /**
   * إنهاء جميع جلسات المستخدم
   */
  static async terminateAllUserSessions(userId: number): Promise<void> {
    try {
      await query(`
        UPDATE active_sessions 
        SET is_active = false, updated_at = CURRENT_TIMESTAMP
        WHERE user_id = $1 AND is_active = true
      `, [userId])
      
      await query(`
        UPDATE users 
        SET current_session_token = NULL, updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
      `, [userId])
      
      console.log(`🔚 تم إنهاء جميع جلسات المستخدم ${userId}`)
      
    } catch (error) {
      console.error('❌ خطأ في إنهاء جميع الجلسات:', error)
      throw error
    }
  }
  
  /**
   * تنظيف الجلسات المنتهية الصلاحية
   */
  static async cleanupExpiredSessions(): Promise<void> {
    try {
      await query(`SELECT cleanup_expired_sessions()`)
      console.log('🧹 تم تنظيف الجلسات المنتهية الصلاحية')
      
    } catch (error) {
      console.error('❌ خطأ في تنظيف الجلسات:', error)
    }
  }
  
  /**
   * الحصول على معلومات الجلسات النشطة للمستخدم
   */
  static async getUserActiveSessions(userId: number): Promise<SessionInfo[]> {
    try {
      const result = await query(`
        SELECT * FROM active_sessions 
        WHERE user_id = $1 AND is_active = true
        ORDER BY last_activity DESC
      `, [userId])
      
      return result.rows.map(session => ({
        id: session.id,
        userId: session.user_id,
        sessionToken: session.session_token,
        deviceInfo: session.device_info,
        ipAddress: session.ip_address,
        userAgent: session.user_agent,
        loginTime: session.login_time,
        lastActivity: session.last_activity,
        isActive: session.is_active
      }))
      
    } catch (error) {
      console.error('❌ خطأ في جلب الجلسات النشطة:', error)
      return []
    }
  }
  
  /**
   * إنشاء JWT مع معلومات الجلسة
   */
  static createJWT(userId: number, sessionToken: string, userData: any): string {
    return jwt.sign(
      {
        userId,
        sessionToken,
        ...userData
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    )
  }
  
  /**
   * التحقق من JWT واستخراج معلومات الجلسة
   */
  static verifyJWT(token: string): any {
    try {
      return jwt.verify(token, JWT_SECRET)
    } catch (error) {
      return null
    }
  }
}

export default SessionManager
