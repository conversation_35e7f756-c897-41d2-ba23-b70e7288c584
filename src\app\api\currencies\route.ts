import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب جميع العملات
export async function GET() {
  try {
    const result = await query(`
      SELECT
        id,
        currency_code,
        currency_name,
        symbol,
        is_active,
        exchange_rate,
        is_base_currency,
        created_date
      FROM currencies
      WHERE is_active = true
      ORDER BY
        is_base_currency DESC,
        currency_name ASC
    `)

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching currencies:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب العملات' },
      { status: 500 }
    )
  }
}

// POST - إضافة عملة جديدة
export async function POST(request: NextRequest) {
  try {
    const { currency_name, symbol, exchange_rate, is_base_currency } = await request.json()

    if (!currency_name || !symbol) {
      return NextResponse.json(
        { success: false, error: 'اسم العملة ورمز العملة مطلوبان' },
        { status: 400 }
      )
    }

    // إذا كانت العملة الجديدة أساسية، إلغاء العملة الأساسية الحالية
    if (is_base_currency) {
      await query(`UPDATE currencies SET is_base_currency = false WHERE is_base_currency = true`)
    }

    const result = await query(`
      INSERT INTO currencies (currency_name, symbol, exchange_rate, is_base_currency, is_active, created_date)
      VALUES ($1, $2, $3, $4, true, NOW())
      RETURNING *
    `, [currency_name, symbol, exchange_rate || 1.0000, is_base_currency || false])

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم إضافة العملة بنجاح'
    })
  } catch (error) {
    console.error('Error adding currency:', error)
    if (error instanceof Error && error.message.includes('duplicate key')) {
      return NextResponse.json(
        { success: false, error: 'رمز العملة موجود مسبقاً' },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة العملة' },
      { status: 500 }
    )
  }
}

// PUT - تحديث عملة
export async function PUT(request: NextRequest) {
  try {
    const url = new URL(request.url)
    const id = url.searchParams.get('id')
    const { currency_name, symbol, exchange_rate, is_base_currency } = await request.json()

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف العملة مطلوب' },
        { status: 400 }
      )
    }

    if (!currency_name || !symbol) {
      return NextResponse.json(
        { success: false, error: 'اسم العملة ورمز العملة مطلوبان' },
        { status: 400 }
      )
    }

    // إذا كانت العملة المحدثة أساسية، إلغاء العملة الأساسية الحالية
    if (is_base_currency) {
      await query(`UPDATE currencies SET is_base_currency = false WHERE is_base_currency = true AND id != $1`, [id])
    }

    const result = await query(`
      UPDATE currencies
      SET
        currency_name = $1,
        symbol = $2,
        exchange_rate = $3,
        is_base_currency = $4,
        updated_date = NOW()
      WHERE id = $5
      RETURNING *
    `, [currency_name, symbol, exchange_rate || 1.0000, is_base_currency || false, id])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'العملة غير موجودة' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم تحديث العملة بنجاح'
    })
  } catch (error) {
    console.error('Error updating currency:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث العملة: ' + error.message },
      { status: 500 }
    )
  }
}

// DELETE - حذف عملة
export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url)
    const id = url.searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف العملة مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من أن العملة ليست أساسية
    const checkResult = await query(`SELECT is_base_currency FROM currencies WHERE id = $1`, [id])

    if (checkResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'العملة غير موجودة' },
        { status: 404 }
      )
    }

    if (checkResult.rows[0].is_base_currency) {
      return NextResponse.json(
        { success: false, error: 'لا يمكن حذف العملة الأساسية' },
        { status: 400 }
      )
    }

    // حذف العملة (soft delete)
    const result = await query(`
      UPDATE currencies
      SET
        is_active = false,
        updated_date = NOW()
      WHERE id = $1
      RETURNING *
    `, [id])

    return NextResponse.json({
      success: true,
      message: 'تم حذف العملة بنجاح'
    })
  } catch (error) {
    console.error('Error deleting currency:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف العملة: ' + error.message },
      { status: 500 }
    )
  }
}
