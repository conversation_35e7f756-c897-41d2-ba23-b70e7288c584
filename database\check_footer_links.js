// فحص بيانات footer_links
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function checkFooterLinks() {
  const client = new Client(dbConfig);
  
  try {
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات');

    // فحص بنية الجدول
    console.log('\n📋 أعمدة جدول footer_links:');
    const columns = await client.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'footer_links' 
      ORDER BY ordinal_position
    `);
    
    columns.rows.forEach(row => {
      console.log(`   - ${row.column_name}: ${row.data_type}`);
    });

    // فحص البيانات
    console.log('\n📊 عينة من البيانات:');
    const data = await client.query('SELECT * FROM footer_links LIMIT 5');
    
    if (data.rows.length > 0) {
      data.rows.forEach((row, index) => {
        console.log(`\n   ${index + 1}. ID: ${row.id}`);
        console.log(`      title: ${row.title}`);
        console.log(`      url: ${row.url}`);
        console.log(`      category: ${row.category}`);
        console.log(`      is_active: ${row.is_active}`);
      });
    } else {
      console.log('   لا توجد بيانات');
    }

    console.log(`\n📈 إجمالي السجلات: ${data.rowCount}`);

  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await client.end();
  }
}

checkFooterLinks();
