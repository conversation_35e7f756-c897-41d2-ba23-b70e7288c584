{"apps": [{"name": "legal-next-moham<PERSON>", "script": "node_modules/next/dist/bin/next", "args": "start -p 3000", "cwd": "./", "instances": 1, "exec_mode": "fork", "env": {"PORT": "3000", "DB_NAME": "moham<PERSON>"}}, {"name": "legal-next-rubaie", "script": "node_modules/next/dist/bin/next", "args": "start -p 3001", "cwd": "./", "instances": 1, "exec_mode": "fork", "env": {"PORT": "3001", "DB_NAME": "rubaie"}}, {"name": "legal-system-unified", "script": "advanced-unified-server.js", "cwd": "./", "instances": 1, "exec_mode": "fork", "env": {}}]}