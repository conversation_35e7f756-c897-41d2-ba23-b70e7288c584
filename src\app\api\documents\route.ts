import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب جميع الوثائق مع إمكانية البحث والتصفية
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search') || ''
    const category = searchParams.get('category') || ''
    const caseId = searchParams.get('case_id') || ''
    const clientId = searchParams.get('client_id') || ''
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = (page - 1) * limit

    let whereConditions = ['d.is_active = true']
    let queryParams: any[] = []
    let paramIndex = 1

    // البحث في النص والعنوان
    if (search) {
      whereConditions.push(`(
        d.title ILIKE $${paramIndex} OR 
        d.description ILIKE $${paramIndex} OR 
        d.content_text ILIKE $${paramIndex} OR
        $${paramIndex} = ANY(d.tags)
      )`)
      queryParams.push(`%${search}%`)
      paramIndex++
    }

    // تصفية حسب الفئة
    if (category) {
      whereConditions.push(`d.category = $${paramIndex}`)
      queryParams.push(category)
      paramIndex++
    }

    // تصفية حسب القضية
    if (caseId) {
      whereConditions.push(`d.case_id = $${paramIndex}`)
      queryParams.push(parseInt(caseId))
      paramIndex++
    }

    // تصفية حسب العميل
    if (clientId) {
      whereConditions.push(`d.client_id = $${paramIndex}`)
      queryParams.push(parseInt(clientId))
      paramIndex++
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : ''

    // الاستعلام الرئيسي (مبسط)
    const documentsQuery = `
      SELECT 
        d.*,
        c.name as client_name,
        i.title as case_title,
        i.case_number,
        COUNT(*) OVER() as total_count
      FROM documents d
      LEFT JOIN clients c ON d.client_id = c.id
      LEFT JOIN issues i ON d.case_id = i.id
      ${whereClause}
      ORDER BY d.created_date DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `

    queryParams.push(limit, offset)
    const result = await query(documentsQuery, queryParams)

    // جلب إحصائيات الفئات
    const categoriesQuery = `
      SELECT category, COUNT(*) as count
      FROM documents 
      WHERE is_active = true
      GROUP BY category
      ORDER BY count DESC
    `
    const categoriesResult = await query(categoriesQuery)

    return NextResponse.json({
      success: true,
      data: {
        documents: result.rows,
        totalCount: result.rows.length > 0 ? parseInt(result.rows[0].total_count) : 0,
        currentPage: page,
        totalPages: result.rows.length > 0 ? Math.ceil(parseInt(result.rows[0].total_count) / limit) : 0,
        categories: categoriesResult.rows
      }
    })

  } catch (error) {
    console.error('خطأ في جلب الوثائق:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الوثائق' },
      { status: 500 }
    )
  }
}

// POST - إضافة وثيقة جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      title,
      description,
      fileName,
      filePath,
      fileSize,
      fileType,
      mimeType,
      caseId,
      clientId,
      employeeId,
      category,
      subcategory,
      tags,
      contentText,
      accessLevel,
      isConfidential,
      uploadedBy
    } = body

    // التحقق من البيانات المطلوبة
    if (!title || !fileName || !filePath) {
      return NextResponse.json(
        { success: false, error: 'البيانات المطلوبة مفقودة' },
        { status: 400 }
      )
    }

    const insertQuery = `
      INSERT INTO documents (
        title, description, file_name, file_path, file_size, file_type, mime_type,
        case_id, client_id, employee_id, category, subcategory, tags, content_text,
        access_level, is_confidential, uploaded_by
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17
      ) RETURNING *
    `

    const values = [
      title, description, fileName, filePath, fileSize, fileType, mimeType,
      caseId || null, clientId || null, employeeId || null, 
      category || 'general', subcategory, tags || [], contentText,
      accessLevel || 'private', isConfidential || false, uploadedBy
    ]

    const result = await query(insertQuery, values)

    // إنشاء إشعار للعميل إذا كانت الوثيقة مرتبطة بقضية
    if (caseId && clientId) {
      await query(`
        INSERT INTO client_notifications (client_id, case_id, title, message, type)
        VALUES ($1, $2, $3, $4, $5)
      `, [
        clientId,
        caseId,
        'وثيقة جديدة',
        `تم إضافة وثيقة جديدة: ${title}`,
        'info'
      ])
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم إضافة الوثيقة بنجاح'
    })

  } catch (error) {
    console.error('خطأ في إضافة الوثيقة:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة الوثيقة' },
      { status: 500 }
    )
  }
}

// PUT - تحديث وثيقة
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      title,
      description,
      category,
      subcategory,
      tags,
      accessLevel,
      isConfidential
    } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الوثيقة مطلوب' },
        { status: 400 }
      )
    }

    const updateQuery = `
      UPDATE documents 
      SET 
        title = COALESCE($2, title),
        description = COALESCE($3, description),
        category = COALESCE($4, category),
        subcategory = COALESCE($5, subcategory),
        tags = COALESCE($6, tags),
        access_level = COALESCE($7, access_level),
        is_confidential = COALESCE($8, is_confidential),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $1 AND is_active = true
      RETURNING *
    `

    const values = [id, title, description, category, subcategory, tags, accessLevel, isConfidential]
    const result = await query(updateQuery, values)

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الوثيقة غير موجودة' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم تحديث الوثيقة بنجاح'
    })

  } catch (error) {
    console.error('خطأ في تحديث الوثيقة:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الوثيقة' },
      { status: 500 }
    )
  }
}

// DELETE - حذف وثيقة (حذف منطقي)
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الوثيقة مطلوب' },
        { status: 400 }
      )
    }

    const deleteQuery = `
      UPDATE documents 
      SET is_active = false, updated_at = CURRENT_TIMESTAMP
      WHERE id = $1 AND is_active = true
      RETURNING id, title
    `

    const result = await query(deleteQuery, [parseInt(id)])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الوثيقة غير موجودة' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف الوثيقة بنجاح'
    })

  } catch (error) {
    console.error('خطأ في حذف الوثيقة:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الوثيقة' },
      { status: 500 }
    )
  }
}