/**
 * دوال تنسيق العملة والأرقام المالية
 * تضمن عرض رقمين عشريين في جميع أنحاء النظام
 */

/**
 * تنسيق الرقم إلى عملة مع رقمين عشريين
 * @param amount المبلغ
 * @param currency رمز العملة (افتراضي: ر.س)
 * @param showCurrency إظهار رمز العملة (افتراضي: true)
 * @returns النص المنسق
 */
export function formatCurrency(
  amount: number | string | null | undefined,
  currency: string = 'ر.س',
  showCurrency: boolean = true
): string {
  // التعامل مع القيم الفارغة
  if (amount === null || amount === undefined || amount === '') {
    return showCurrency ? `0.00 ${currency}` : '0.00'
  }

  // تحويل إلى رقم
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount

  // التحقق من صحة الرقم
  if (isNaN(numAmount)) {
    return showCurrency ? `0.00 ${currency}` : '0.00'
  }

  // تنسيق الرقم مع رقمين عشريين وفواصل الآلاف (أرقام لاتينية)
  const formatted = numAmount.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })

  return showCurrency ? `${formatted} ${currency}` : formatted
}

/**
 * تنسيق الرقم بدون رمز العملة
 * @param amount المبلغ
 * @returns الرقم المنسق مع رقمين عشريين
 */
export function formatNumber(amount: number | string | null | undefined): string {
  return formatCurrency(amount, '', false)
}

/**
 * تقريب الرقم إلى رقمين عشريين
 * @param amount المبلغ
 * @returns الرقم مقرب إلى رقمين عشريين
 */
export function roundToTwoDecimals(amount: number | string): number {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
  if (isNaN(numAmount)) return 0
  return Math.round(numAmount * 100) / 100
}

/**
 * تحويل الرقم إلى نص مع رقمين عشريين
 * @param amount المبلغ
 * @returns النص مع رقمين عشريين
 */
export function toFixedTwo(amount: number | string | null | undefined): string {
  if (amount === null || amount === undefined || amount === '') return '0.00'
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
  if (isNaN(numAmount)) return '0.00'
  return numAmount.toFixed(2)
}

/**
 * تنسيق الرصيد مع اللون المناسب
 * @param balance الرصيد
 * @param accountNature طبيعة الحساب (مدين/دائن)
 * @returns كائن يحتوي على النص المنسق واللون
 */
export function formatBalance(
  balance: number | string | null | undefined,
  accountNature: string = 'مدين'
): { text: string; color: string; className: string } {
  const numBalance = typeof balance === 'string' ? parseFloat(balance) : (balance || 0)
  const formattedBalance = formatCurrency(Math.abs(numBalance))

  let balanceType = ''
  let color = ''
  let className = ''

  if (accountNature === 'مدين') {
    if (numBalance >= 0) {
      balanceType = 'مدين'
      color = '#28a745' // أخضر
      className = 'text-green-600'
    } else {
      balanceType = 'دائن'
      color = '#dc3545' // أحمر
      className = 'text-red-600'
    }
  } else {
    if (numBalance >= 0) {
      balanceType = 'دائن'
      color = '#28a745' // أخضر
      className = 'text-green-600'
    } else {
      balanceType = 'مدين'
      color = '#dc3545' // أحمر
      className = 'text-red-600'
    }
  }

  return {
    text: `${formattedBalance} ${balanceType}`,
    color,
    className
  }
}

/**
 * تنسيق النسبة المئوية
 * @param percentage النسبة
 * @param decimals عدد الأرقام العشرية (افتراضي: 2)
 * @returns النسبة المنسقة
 */
export function formatPercentage(
  percentage: number | string | null | undefined,
  decimals: number = 2
): string {
  if (percentage === null || percentage === undefined || percentage === '') return '0.00%'
  const numPercentage = typeof percentage === 'string' ? parseFloat(percentage) : percentage
  if (isNaN(numPercentage)) return '0.00%'
  return `${numPercentage.toFixed(decimals)}%`
}

/**
 * تحويل الأرقام العربية إلى لاتينية
 * @param text النص المحتوي على أرقام عربية
 * @returns النص مع أرقام لاتينية
 */
export function convertArabicToLatinNumbers(text: string): string {
  const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩']
  const latinNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']

  let result = text
  for (let i = 0; i < arabicNumbers.length; i++) {
    result = result.replace(new RegExp(arabicNumbers[i], 'g'), latinNumbers[i])
  }

  return result
}

/**
 * ضمان استخدام الأرقام اللاتينية في النص
 * @param value القيمة
 * @returns القيمة مع أرقام لاتينية
 */
export function ensureLatinNumbers(value: any): any {
  if (typeof value === 'string') {
    return convertArabicToLatinNumbers(value)
  }
  if (typeof value === 'number') {
    return value.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })
  }
  return value
}

/**
 * تحويل الرقم من نص إلى رقم مع التعامل مع الفواصل
 * @param value القيمة النصية
 * @returns الرقم
 */
export function parseAmount(value: string | number | null | undefined): number {
  if (value === null || value === undefined || value === '') return 0
  if (typeof value === 'number') return roundToTwoDecimals(value)

  // تحويل الأرقام العربية إلى لاتينية أولاً
  let cleanValue = convertArabicToLatinNumbers(value.toString())

  // إزالة الفواصل ورموز العملة
  cleanValue = cleanValue
    .replace(/[^\d.-]/g, '') // إبقاء الأرقام والنقطة والسالب فقط
    .replace(/,/g, '') // إزالة الفواصل

  const parsed = parseFloat(cleanValue)
  return isNaN(parsed) ? 0 : roundToTwoDecimals(parsed)
}

/**
 * تنسيق مبلغ للإدخال في النماذج
 * @param amount المبلغ
 * @returns النص المنسق للإدخال
 */
export function formatForInput(amount: number | string | null | undefined): string {
  const numAmount = parseAmount(amount)
  return numAmount.toFixed(2)
}

/**
 * التحقق من صحة المبلغ
 * @param amount المبلغ
 * @returns true إذا كان المبلغ صحيح
 */
export function isValidAmount(amount: any): boolean {
  if (amount === null || amount === undefined || amount === '') return false
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
  return !isNaN(numAmount) && isFinite(numAmount)
}

/**
 * إعدادات افتراضية للتنسيق
 */
export const CURRENCY_CONFIG = {
  symbol: 'ر.س',
  locale: 'en-US', // استخدام الأرقام اللاتينية
  decimals: 2,
  thousandsSeparator: ',',
  decimalSeparator: '.'
}

/**
 * تنسيق مخصص للجداول
 * @param amount المبلغ
 * @param showZero إظهار الصفر (افتراضي: true)
 * @returns النص المنسق للجداول
 */
export function formatForTable(
  amount: number | string | null | undefined,
  showZero: boolean = true
): string {
  const numAmount = parseAmount(amount)

  if (numAmount === 0 && !showZero) {
    return '-'
  }

  return formatNumber(numAmount)
}
