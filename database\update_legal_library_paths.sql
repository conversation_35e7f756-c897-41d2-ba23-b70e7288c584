-- تحديث نظام مسارات المكتبة القانونية لدعم مسارات متعددة

-- إنشاء جدول مسارات المكتبة القانونية
CREATE TABLE IF NOT EXISTS legal_library_paths (
    id SERIAL PRIMARY KEY,
    path_name VARCHAR(255) NOT NULL,
    path_value TEXT NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    is_default BOOLEAN DEFAULT false,
    scan_enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء فهرس للبحث السريع
CREATE INDEX IF NOT EXISTS idx_legal_library_paths_active ON legal_library_paths(is_active);
CREATE INDEX IF NOT EXISTS idx_legal_library_paths_default ON legal_library_paths(is_default);

-- إنشاء trigger لتحديث updated_at
CREATE OR REPLACE FUNCTION update_legal_library_paths_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_legal_library_paths_timestamp ON legal_library_paths;
CREATE TRIGGER trigger_update_legal_library_paths_timestamp
    BEFORE UPDATE ON legal_library_paths
    FOR EACH ROW
    EXECUTE FUNCTION update_legal_library_paths_timestamp();

-- إدراج المسارات الافتراضية
INSERT INTO legal_library_paths (path_name, path_value, description, is_active, is_default, scan_enabled) VALUES
('المسار الرئيسي', 'D:\mohaminew\legal-documents', 'مجلد الوثائق القانونية الرئيسي', true, true, true),
('مسار النسخ الاحتياطي', 'D:\mohaminew\legal-backup', 'مجلد النسخ الاحتياطية للوثائق القانونية', true, false, false),
('مسار الأرشيف', 'D:\mohaminew\legal-archive', 'مجلد أرشيف الوثائق القانونية القديمة', true, false, true)
ON CONFLICT DO NOTHING;

-- تحديث الإعدادات الموجودة
UPDATE system_settings 
SET setting_value = 'D:\mohaminew\legal-documents' 
WHERE setting_key = 'legal_library_path';

-- إضافة إعدادات جديدة للمسارات المتعددة
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_editable) VALUES
('legal_library_multi_path_enabled', 'true', 'boolean', 'تفعيل دعم المسارات المتعددة', true),
('legal_library_auto_create_dirs', 'true', 'boolean', 'إنشاء المجلدات تلقائياً إذا لم تكن موجودة', true),
('legal_library_scan_interval', '60', 'number', 'فترة الفحص التلقائي بالدقائق', true),
('legal_library_max_paths', '10', 'number', 'الحد الأقصى لعدد المسارات', true)
ON CONFLICT (setting_key) DO NOTHING;
