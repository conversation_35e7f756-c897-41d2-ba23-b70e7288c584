// تثبيت شهادة SSL لـ mohammi.com على IP ***********
const fs = require('fs');
const https = require('https');
const http = require('http');
const path = require('path');

console.log('🔐 تثبيت شهادة SSL لـ mohammi.com');
console.log('🌐 IP الخارجي: ***********');
console.log('='.repeat(50));

// مسار ملفات SSL
const sslDir = path.join(__dirname);
console.log(`📁 مجلد SSL: ${sslDir}`);

// البحث عن ملفات الشهادة
console.log('\n🔍 البحث عن ملفات الشهادة...');

const possibleFiles = {
  certificate: [
    'mohammi.crt',
    'mohammi.pem', 
    'certificate.crt',
    'cert.pem',
    'mohammi_signed.crt',
    'ssl_certificate.crt'
  ],
  privateKey: [
    'mohammi.key',
    'private.key',
    'privatekey.pem',
    'mohammi_private.key',
    'ssl_private.key'
  ],
  intermediate: [
    'intermediate.crt',
    'ca_bundle.crt',
    'chain.pem',
    'intermediate.pem'
  ]
};

let sslFiles = {
  certificate: null,
  privateKey: null,
  intermediate: null
};

// البحث عن الملفات
for (const [type, filenames] of Object.entries(possibleFiles)) {
  for (const filename of filenames) {
    const filepath = path.join(sslDir, filename);
    if (fs.existsSync(filepath)) {
      sslFiles[type] = filepath;
      console.log(`✅ تم العثور على ${type}: ${filename}`);
      break;
    }
  }
}

// التحقق من وجود الملفات الأساسية
if (!sslFiles.certificate) {
  console.error('❌ لم يتم العثور على ملف الشهادة');
  console.log('💡 تأكد من وجود أحد هذه الملفات:');
  possibleFiles.certificate.forEach(file => console.log(`   - ${file}`));
  process.exit(1);
}

if (!sslFiles.privateKey) {
  console.error('❌ لم يتم العثور على المفتاح الخاص');
  console.log('💡 تأكد من وجود أحد هذه الملفات:');
  possibleFiles.privateKey.forEach(file => console.log(`   - ${file}`));
  process.exit(1);
}

// قراءة ملفات SSL
console.log('\n📖 قراءة ملفات SSL...');
let certificate, privateKey, intermediate;

try {
  certificate = fs.readFileSync(sslFiles.certificate, 'utf8');
  console.log(`✅ تم تحميل الشهادة من: ${path.basename(sslFiles.certificate)}`);
  
  privateKey = fs.readFileSync(sslFiles.privateKey, 'utf8');
  console.log(`✅ تم تحميل المفتاح الخاص من: ${path.basename(sslFiles.privateKey)}`);
  
  if (sslFiles.intermediate) {
    intermediate = fs.readFileSync(sslFiles.intermediate, 'utf8');
    console.log(`✅ تم تحميل الشهادة الوسطية من: ${path.basename(sslFiles.intermediate)}`);
    // دمج الشهادة مع الوسطية
    certificate = certificate + '\n' + intermediate;
  }
  
} catch (error) {
  console.error('❌ خطأ في قراءة ملفات SSL:', error.message);
  process.exit(1);
}

// إعداد خيارات SSL
const sslOptions = {
  key: privateKey,
  cert: certificate,
  // إعدادات أمان متقدمة
  secureProtocol: 'TLSv1_2_method',
  ciphers: [
    'ECDHE-RSA-AES128-GCM-SHA256',
    'ECDHE-RSA-AES256-GCM-SHA384',
    'ECDHE-RSA-AES128-SHA256',
    'ECDHE-RSA-AES256-SHA384',
    'DHE-RSA-AES128-GCM-SHA256',
    'DHE-RSA-AES256-GCM-SHA384'
  ].join(':'),
  honorCipherOrder: true,
  // إعدادات HSTS
  secureOptions: require('constants').SSL_OP_NO_SSLv2 | require('constants').SSL_OP_NO_SSLv3
};

console.log('\n🔧 إعداد خادم HTTPS...');

// إنشاء خادم HTTPS
const httpsServer = https.createServer(sslOptions, (req, res) => {
  // إعداد headers الأمان
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // إعادة توجيه إلى تطبيق Next.js
  const proxyOptions = {
    hostname: 'localhost',
    port: 7443, // منفذ تطبيق mohammi
    path: req.url,
    method: req.method,
    headers: {
      ...req.headers,
      'X-Forwarded-For': req.connection.remoteAddress,
      'X-Forwarded-Proto': 'https',
      'X-Forwarded-Host': req.headers.host
    }
  };
  
  const proxy = http.request(proxyOptions, (proxyRes) => {
    res.writeHead(proxyRes.statusCode, proxyRes.headers);
    proxyRes.pipe(res);
  });
  
  proxy.on('error', (error) => {
    console.error('❌ خطأ في Proxy:', error.message);
    res.writeHead(502, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(`
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>خطأ في الخادم</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5; }
        .error { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <div class="error">
        <h1>🔧 الخادم قيد الصيانة</h1>
        <p>يرجى المحاولة مرة أخرى خلال دقائق قليلة</p>
        <p>إذا استمرت المشكلة، يرجى التواصل مع الدعم الفني</p>
    </div>
</body>
</html>`);
  });
  
  req.pipe(proxy);
});

// إنشاء خادم HTTP لإعادة التوجيه
const httpServer = http.createServer((req, res) => {
  const httpsUrl = `https://${req.headers.host}${req.url}`;
  res.writeHead(301, {
    'Location': httpsUrl,
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload'
  });
  res.end();
});

// بدء الخوادم
const httpsPort = 443;
const httpPort = 80;

console.log('\n🚀 بدء الخوادم...');

// بدء خادم HTTPS
httpsServer.listen(httpsPort, '0.0.0.0', (err) => {
  if (err) {
    console.error(`❌ فشل في بدء خادم HTTPS على المنفذ ${httpsPort}:`, err.message);
    console.log('💡 تأكد من:');
    console.log('   - تشغيل الأمر كمدير (Run as Administrator)');
    console.log('   - عدم استخدام المنفذ 443 من قبل تطبيق آخر');
    console.log('   - فتح المنفذ 443 في جدار الحماية');
    process.exit(1);
  } else {
    console.log(`✅ خادم HTTPS يعمل على المنفذ ${httpsPort}`);
    console.log(`🌐 الرابط الخارجي: https://mohammi.com`);
    console.log(`🌐 IP مباشر: https://***********`);
  }
});

// بدء خادم HTTP لإعادة التوجيه
httpServer.listen(httpPort, '0.0.0.0', (err) => {
  if (err) {
    console.log(`⚠️ لم يتم بدء خادم إعادة التوجيه HTTP على المنفذ ${httpPort}`);
    console.log('   (قد يكون المنفذ مستخدماً من قبل تطبيق آخر)');
  } else {
    console.log(`✅ خادم إعادة التوجيه HTTP يعمل على المنفذ ${httpPort}`);
    console.log(`🔄 http://mohammi.com → https://mohammi.com`);
  }
});

// معلومات إضافية
console.log('\n📊 معلومات الشهادة:');
console.log(`🔐 الشهادة: ${path.basename(sslFiles.certificate)}`);
console.log(`🔑 المفتاح الخاص: ${path.basename(sslFiles.privateKey)}`);
if (sslFiles.intermediate) {
  console.log(`🔗 الشهادة الوسطية: ${path.basename(sslFiles.intermediate)}`);
}

console.log('\n🌐 معلومات الشبكة:');
console.log(`📍 IP الخارجي: ***********`);
console.log(`🌍 الدومين: mohammi.com`);
console.log(`🔌 منفذ HTTPS: ${httpsPort}`);
console.log(`🔌 منفذ HTTP: ${httpPort}`);

console.log('\n🎯 اختبار الشهادة:');
console.log('• https://www.ssllabs.com/ssltest/analyze.html?d=mohammi.com');
console.log('• https://www.sslshopper.com/ssl-checker.html#hostname=mohammi.com');

// معالجة إيقاف الخادم
process.on('SIGINT', () => {
  console.log('\n🛑 إيقاف الخوادم...');
  httpsServer.close(() => {
    console.log('✅ تم إيقاف خادم HTTPS');
  });
  httpServer.close(() => {
    console.log('✅ تم إيقاف خادم HTTP');
  });
  process.exit(0);
});

console.log('\n⏳ الخوادم تعمل... اضغط Ctrl+C للإيقاف');
console.log('🔗 تطبيق mohammi يعمل على: http://localhost:7443');
console.log('🔐 SSL Proxy يعمل على: https://0.0.0.0:443');
