# سكريبت إعداد خدمة الخادم المتقدم للعمل التلقائي
# Advanced Unified Server Auto-Start Service Setup

Write-Host "🚀 بدء إعداد خدمة الخادم المتقدم..." -ForegroundColor Green

# التحقق من صلاحيات المدير
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ يجب تشغيل هذا السكريبت كمدير (Run as Administrator)" -ForegroundColor Red
    Write-Host "انقر بزر الماوس الأيمن على PowerShell واختر 'Run as Administrator'" -ForegroundColor Yellow
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# متغيرات الإعداد
$serviceName = "AdvancedUnifiedServer"
$serviceDisplayName = "Advanced Unified Server - نظام إدارة المحاماة"
$serviceDescription = "خادم نظام إدارة المحاماة المتقدم - يدير المنافذ المتعددة والتوجيه التلقائي"
$workingDirectory = "D:\mohammi"
$nodeExePath = ""
$serverScript = "advanced-unified-server.js"

# البحث عن مسار Node.js
Write-Host "🔍 البحث عن مسار Node.js..." -ForegroundColor Cyan

$possiblePaths = @(
    "C:\Program Files\nodejs\node.exe",
    "C:\Program Files (x86)\nodejs\node.exe",
    "$env:APPDATA\npm\node.exe",
    "$env:LOCALAPPDATA\Programs\nodejs\node.exe"
)

foreach ($path in $possiblePaths) {
    if (Test-Path $path) {
        $nodeExePath = $path
        Write-Host "✅ تم العثور على Node.js في: $nodeExePath" -ForegroundColor Green
        break
    }
}

# إذا لم يتم العثور على Node.js في المسارات المعتادة، جرب البحث في PATH
if (-not $nodeExePath) {
    try {
        $nodeExePath = (Get-Command node -ErrorAction Stop).Source
        Write-Host "✅ تم العثور على Node.js في PATH: $nodeExePath" -ForegroundColor Green
    } catch {
        Write-Host "❌ لم يتم العثور على Node.js. يرجى تثبيت Node.js أولاً." -ForegroundColor Red
        Read-Host "اضغط Enter للخروج"
        exit 1
    }
}

# التحقق من وجود مجلد العمل
if (-not (Test-Path $workingDirectory)) {
    Write-Host "❌ مجلد العمل غير موجود: $workingDirectory" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# التحقق من وجود ملف الخادم
$serverScriptPath = Join-Path $workingDirectory $serverScript
if (-not (Test-Path $serverScriptPath)) {
    Write-Host "❌ ملف الخادم غير موجود: $serverScriptPath" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host "✅ جميع الملفات موجودة" -ForegroundColor Green

# إيقاف الخدمة إذا كانت موجودة
Write-Host "🔄 التحقق من الخدمة الموجودة..." -ForegroundColor Cyan

$existingService = Get-Service -Name $serviceName -ErrorAction SilentlyContinue
if ($existingService) {
    Write-Host "⚠️ الخدمة موجودة بالفعل. سيتم إيقافها وحذفها..." -ForegroundColor Yellow
    
    if ($existingService.Status -eq "Running") {
        Write-Host "⏹️ إيقاف الخدمة..." -ForegroundColor Yellow
        Stop-Service -Name $serviceName -Force
        Start-Sleep -Seconds 3
    }
    
    Write-Host "🗑️ حذف الخدمة القديمة..." -ForegroundColor Yellow
    sc.exe delete $serviceName
    Start-Sleep -Seconds 2
}

# إنشاء الخدمة الجديدة
Write-Host "🔧 إنشاء الخدمة الجديدة..." -ForegroundColor Cyan

$arguments = "`"$serverScriptPath`""

# إنشاء الخدمة باستخدام sc.exe
$createResult = sc.exe create $serviceName binPath= "`"$nodeExePath`" $arguments" start= auto DisplayName= "$serviceDisplayName"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ تم إنشاء الخدمة بنجاح" -ForegroundColor Green
} else {
    Write-Host "❌ فشل في إنشاء الخدمة" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# تعيين وصف الخدمة
sc.exe description $serviceName "$serviceDescription"

# تعيين مجلد العمل للخدمة
Write-Host "📁 تعيين مجلد العمل..." -ForegroundColor Cyan

# إنشاء مفتاح التسجيل لمجلد العمل
$registryPath = "HKLM:\SYSTEM\CurrentControlSet\Services\$serviceName"
if (Test-Path $registryPath) {
    Set-ItemProperty -Path $registryPath -Name "ImagePath" -Value "`"$nodeExePath`" `"$serverScriptPath`""
    
    # إضافة متغيرات البيئة
    $envPath = "$registryPath\Environment"
    if (-not (Test-Path $envPath)) {
        New-Item -Path $envPath -Force | Out-Null
    }
    
    Set-ItemProperty -Path $envPath -Name "NODE_ENV" -Value "production"
    Set-ItemProperty -Path $envPath -Name "PORT" -Value "7443"
    
    Write-Host "✅ تم تعيين مجلد العمل ومتغيرات البيئة" -ForegroundColor Green
}

# تعيين إعدادات الاسترداد (إعادة التشغيل عند الفشل)
Write-Host "🔄 تعيين إعدادات الاسترداد..." -ForegroundColor Cyan

sc.exe failure $serviceName reset= 86400 actions= restart/5000/restart/10000/restart/30000

# بدء الخدمة
Write-Host "▶️ بدء الخدمة..." -ForegroundColor Cyan

Start-Service -Name $serviceName

# التحقق من حالة الخدمة
Start-Sleep -Seconds 5
$service = Get-Service -Name $serviceName

if ($service.Status -eq "Running") {
    Write-Host "🎉 تم إعداد وتشغيل الخدمة بنجاح!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📊 معلومات الخدمة:" -ForegroundColor Cyan
    Write-Host "   - اسم الخدمة: $serviceName" -ForegroundColor White
    Write-Host "   - الاسم المعروض: $serviceDisplayName" -ForegroundColor White
    Write-Host "   - مسار Node.js: $nodeExePath" -ForegroundColor White
    Write-Host "   - مجلد العمل: $workingDirectory" -ForegroundColor White
    Write-Host "   - ملف الخادم: $serverScript" -ForegroundColor White
    Write-Host "   - الحالة: تعمل ✅" -ForegroundColor Green
    Write-Host ""
    Write-Host "🌐 المنافذ المتاحة:" -ForegroundColor Cyan
    Write-Host "   - المنفذ 7443: نظام محمد (الإنتاج)" -ForegroundColor White
    Write-Host "   - المنفذ 8914: نظام الربعي" -ForegroundColor White
    Write-Host "   - المنفذ 3300: نظام محمد (التطوير)" -ForegroundColor White
    Write-Host ""
    Write-Host "🔧 أوامر إدارة الخدمة:" -ForegroundColor Cyan
    Write-Host "   - إيقاف: Stop-Service -Name $serviceName" -ForegroundColor Yellow
    Write-Host "   - تشغيل: Start-Service -Name $serviceName" -ForegroundColor Yellow
    Write-Host "   - إعادة تشغيل: Restart-Service -Name $serviceName" -ForegroundColor Yellow
    Write-Host "   - حذف: sc.exe delete $serviceName" -ForegroundColor Red
    Write-Host ""
    Write-Host "✅ الخدمة ستعمل تلقائياً عند إعادة تشغيل السيرفر" -ForegroundColor Green
} else {
    Write-Host "❌ فشل في تشغيل الخدمة" -ForegroundColor Red
    Write-Host "حالة الخدمة: $($service.Status)" -ForegroundColor Yellow
    
    # عرض سجل الأحداث للمساعدة في التشخيص
    Write-Host "🔍 فحص سجل الأحداث..." -ForegroundColor Cyan
    $events = Get-EventLog -LogName System -Source "Service Control Manager" -Newest 5 | Where-Object { $_.Message -like "*$serviceName*" }
    if ($events) {
        Write-Host "آخر الأحداث المتعلقة بالخدمة:" -ForegroundColor Yellow
        $events | ForEach-Object { Write-Host "   - $($_.TimeGenerated): $($_.Message)" -ForegroundColor White }
    }
}

Write-Host ""
Write-Host "📝 ملاحظات مهمة:" -ForegroundColor Cyan
Write-Host "   - تأكد من أن منافذ 7443 و 8914 و 3300 غير مستخدمة من برامج أخرى" -ForegroundColor White
Write-Host "   - يمكن مراقبة الخدمة من خلال 'Services.msc'" -ForegroundColor White
Write-Host "   - في حالة وجود مشاكل، تحقق من Event Viewer" -ForegroundColor White

Read-Host "اضغط Enter للخروج"
