# تعليمات إعداد نظام حركة القضايا والتنبيهات

## ✅ التحديثات المطبقة:

### 1. **تحديث قاعدة البيانات**
- تم تغيير قاعدة البيانات من `mohammi` إلى `mohammidev`
- تم تحديث ملف `routing.config.json`
- تم تحديث جميع السكريبتات

### 2. **الملفات المنشأة**
- ✅ `src/app/movements/page.tsx` - صفحة حركة القضايا
- ✅ `src/app/api/case-movements/route.ts` - API حركة القضايا
- ✅ `src/app/api/advanced-notifications/route.ts` - API التنبيهات
- ✅ `src/app/setup-case-movements/page.tsx` - صفحة الإعداد
- ✅ `src/app/api/setup-case-movements/route.ts` - API الإعداد
- ✅ `src/app/api/run-setup-script/route.ts` - API تشغيل السكريبت
- ✅ `create-case-movements-mohammidev.js` - سكريبت إنشاء الجداول
- ✅ `setup-case-movements.bat` - ملف batch للإعداد
- ✅ `start-dev-3300.bat` - ملف batch لتشغيل الخادم

## 🚀 خطوات التشغيل:

### **الطريقة الأولى: عبر المتصفح (الأسهل)**
1. تأكد من تشغيل الخادم على المنفذ 3300
2. افتح: http://localhost:3300/setup-case-movements
3. اضغط على "بدء الإعداد"
4. انتظر حتى اكتمال الإعداد
5. افتح: http://localhost:3300/movements

### **الطريقة الثانية: عبر ملف Batch**
1. شغل ملف `setup-case-movements.bat`
2. انتظر حتى اكتمال الإعداد
3. شغل ملف `start-dev-3300.bat` لتشغيل الخادم
4. افتح: http://localhost:3300/movements

### **الطريقة الثالثة: يدوياً**
```bash
# إنشاء الجداول
node create-case-movements-mohammidev.js

# تشغيل الخادم
npx next dev -p 3300
```

## 📋 الجداول التي سيتم إنشاؤها:

1. **`case_movements`** - جدول حركة القضايا
   - تتبع جميع أنشطة القضايا
   - 20 حركة تجريبية

2. **`notification_templates`** - قوالب التنبيهات
   - 8 قوالب أساسية للتنبيهات
   - قابلة للتخصيص

## 🎯 الميزات المتاحة:

### **صفحة حركة القضايا** (`/movements`)
- ✅ عرض جميع حركات القضايا
- ✅ إحصائيات شاملة (إجمالي، جلسات، عاجل، قادم)
- ✅ بحث متقدم في أرقام وعناوين القضايا
- ✅ تصفية حسب نوع الحركة والأولوية
- ✅ عرض زمني مع أيقونات ملونة
- ✅ تصميم متجاوب

### **أنواع الحركات المدعومة**
- إنشاء قضية (`case_created`)
- توزيع قضية (`case_assigned`)
- تحديد جلسة (`hearing_scheduled`)
- إضافة متابعة (`follow_added`)
- رفع وثيقة (`document_uploaded`)
- تغيير حالة القضية (`case_status_changed`)
- وأكثر...

### **نظام التنبيهات**
- تنبيهات الجلسات (3 أيام، يوم واحد)
- كشف القضايا الخاملة
- تنبيهات فورية للأحداث المهمة
- قنوات متعددة (نظام، إيميل، SMS، واتساب)

## 🔧 إعدادات قاعدة البيانات:

```json
{
  "host": "localhost",
  "port": 5432,
  "user": "postgres", 
  "password": "yemen123",
  "database": "mohammidev"
}
```

## 📊 البيانات التجريبية:

سيتم إدراج 20 حركة تجريبية تشمل:
- 10 قضايا مختلفة
- أنواع حركات متنوعة
- أولويات مختلفة (عادي، عالي، عاجل)
- تواريخ متدرجة (آخر 9 أيام)

## 🌐 الروابط المهمة:

- **صفحة الإعداد**: http://localhost:3300/setup-case-movements
- **صفحة حركة القضايا**: http://localhost:3300/movements
- **الصفحة الرئيسية**: http://localhost:3300/

## ⚠️ ملاحظات مهمة:

1. **قاعدة البيانات**: تأكد من وجود قاعدة البيانات `mohammidev`
2. **الخادم**: يجب تشغيل الخادم على المنفذ 3300
3. **الصلاحيات**: تأكد من صلاحيات PostgreSQL
4. **النسخ الاحتياطية**: يُنصح بعمل نسخة احتياطية قبل الإعداد

## 🎉 بعد الإعداد:

1. ✅ ستظهر صفحة حركة القضايا مع البيانات التجريبية
2. ✅ ستعمل جميع وظائف البحث والتصفية
3. ✅ ستظهر الإحصائيات الصحيحة
4. ✅ سيكون النظام جاهزاً للاستخدام الفعلي

---

**🚀 ابدأ الآن بزيارة صفحة الإعداد وتشغيل النظام!**

http://localhost:3300/setup-case-movements
