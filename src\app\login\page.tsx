'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import DeviceIdDisplay, { getOrCreateDeviceId, getDeviceInfo } from '@/components/DeviceIdGenerator'
import {
  User,
  Lock,
  Smartphone,
  UserCheck,
  Building2,
  Eye,
  EyeOff,
  LogIn
} from 'lucide-react'

export default function LoginPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { user, login } = useAuth()
  const [loginType, setLoginType] = useState<'user' | 'client'>('user')
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [sessionMessage, setSessionMessage] = useState<string | null>(null)
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  })
  const [deviceId, setDeviceId] = useState<string>('')

  // جلب رسالة الجلسة من URL parameters
  useEffect(() => {
    const message = searchParams.get('message')
    if (message) {
      setSessionMessage(message)
    }
  }, [searchParams])

  // تهيئة معرف الجهاز التلقائي
  useEffect(() => {
    const initDeviceId = () => {
      const autoDeviceId = getOrCreateDeviceId()
      setDeviceId(autoDeviceId)
    }

    // تأخير قصير للتأكد من تحميل DOM
    const timer = setTimeout(initDeviceId, 100)
    return () => clearTimeout(timer)
  }, [])

  // منع الدخول التلقائي - يجب إدخال بيانات المستخدم دائماً
  useEffect(() => {
    // مسح أي جلسة سابقة عند الوصول لصفحة تسجيل الدخول
    localStorage.removeItem('userSession')
    localStorage.removeItem('clientToken')
    document.cookie = 'userSession=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT'

    // التأكد من عدم وجود مستخدم مسجل دخول
    if (user) {
      // إجبار تسجيل الخروج
      localStorage.removeItem('userSession')
      localStorage.removeItem('clientToken')
      document.cookie = 'userSession=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT'
      window.location.reload() // إعادة تحميل الصفحة لضمان مسح الحالة
    }
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const endpoint = loginType === 'user' ? '/api/auth/simple' : '/api/client-portal/auth/simple'

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: formData.username.trim(),
          password: formData.password.trim(),
          deviceId: loginType === 'user' ? deviceId : undefined,
          deviceInfo: loginType === 'user' ? JSON.stringify(getDeviceInfo()) : undefined
        }),
      })

      const result = await response.json()

      if (result.success) {
        if (loginType === 'user') {
          // التحقق من صحة البيانات المرجعة
          if (!result.user || !result.user.username || !result.user.role) {
            setError('بيانات المستخدم غير مكتملة')
            return
          }

          // التحقق من أن المستخدم نشط
          if (!result.user.is_active || result.user.status !== 'active') {
            setError('حساب المستخدم غير نشط')
            return
          }

          // التحقق من وجود صلاحيات
          if (!result.user.permissions || result.user.permissions.length === 0) {
            setError('المستخدم لا يملك صلاحيات للوصول للنظام')
            return
          }

          // تحويل الصلاحيات إلى مصفوفة مفاتيح نصية فقط لتتوافق مع useAuth
          const permissionKeys: string[] = Array.isArray(result.user.permissions)
            ? result.user.permissions.map((p: any) => typeof p === 'string' ? p : (p?.key || p?.permission_key)).filter(Boolean)
            : []

          // استخدام hook المصادقة لحفظ بيانات المستخدم
          const userData = {
            id: result.user.id,
            username: result.user.username,
            type: loginType as 'user',
            name: result.user.name,
            role: result.user.role,
            role_display_name: result.user.role_display_name,
            permissions: permissionKeys,
            user_type: result.user.user_type,
            status: result.user.status,
            is_active: result.user.is_active,
            token: result.token,
            loginTime: new Date().toISOString(),
            sessionId: `session_${result.user.id}_${Date.now()}`
          }

          // التحقق النهائي من البيانات قبل الحفظ
          if (userData.username && userData.role && userData.permissions.length > 0) {
            login(userData)
          } else {
            setError('فشل في التحقق من بيانات المستخدم')
          }
        } else {
          // استخدام hook المصادقة لحفظ بيانات العميل
          const clientData = {
            id: result.data.client.client_id,
            username: result.data.client.username,
            type: loginType as 'client',
            name: result.data.client.client_name,
            email: result.data.client.email,
            token: result.data.token,
            sessionToken: result.data.sessionToken
          }
          localStorage.setItem('clientToken', result.data.token)
          login(clientData)
        }
      } else {
        alert(result.error || 'فشل في تسجيل الدخول')
      }
    } catch (error) {
      console.error('Login error:', error)
      alert('حدث خطأ في الاتصال')
    } finally {
      setIsLoading(false)
    }
  }



  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* العنوان الرئيسي */}
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            مرحباً بك في نظام الإدارة المتخصص
          </h1>
          <p className="text-gray-600">قم بتسجيل الدخول للمتابعة</p>
        </div>

        <Card className="shadow-xl border-0">
          <CardHeader className="space-y-4">
            {/* رسالة نظام الجلسة الواحدة */}
            {sessionMessage && (
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                <div className="flex items-center gap-2">
                  <LogIn className="h-5 w-5 text-amber-600" />
                  <p className="text-amber-800 font-medium">
                    {sessionMessage}
                  </p>
                </div>
              </div>
            )}

            {/* أزرار نوع المستخدم */}
            <div className="flex rounded-lg bg-gray-100 p-1">
              <Button
                type="button"
                variant={loginType === 'user' ? 'default' : 'ghost'}
                className={`flex-1 ${
                  loginType === 'user'
                    ? 'bg-blue-600 text-white shadow-md'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                onClick={() => setLoginType('user')}
              >
                <UserCheck className="h-4 w-4 mr-2" />
                دخول مستخدم
              </Button>
              <Button
                type="button"
                variant={loginType === 'client' ? 'default' : 'ghost'}
                className={`flex-1 ${
                  loginType === 'client'
                    ? 'bg-blue-600 text-white shadow-md'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                onClick={() => setLoginType('client')}
              >
                <Building2 className="h-4 w-4 mr-2" />
                دخول عميل
              </Button>
            </div>
          </CardHeader>

          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* اسم المستخدم */}
              <div className="space-y-2">
                <Label htmlFor="username" className="text-right block">
                  اسم المستخدم *
                </Label>
                <div className="relative">
                  <Input
                    id="username"
                    type="text"
                    value={formData.username}
                    onChange={(e) => setFormData({...formData, username: e.target.value})}
                    className="pl-10 text-right"
                    placeholder="أدخل اسم المستخدم"
                    required
                  />
                  <User className="h-5 w-5 text-blue-500 absolute left-3 top-1/2 transform -translate-y-1/2" />
                </div>
              </div>

              {/* كلمة المرور */}
              <div className="space-y-2">
                <Label htmlFor="password" className="text-right block">
                  كلمة المرور *
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={(e) => setFormData({...formData, password: e.target.value})}
                    className="pl-10 pr-10 text-right"
                    placeholder="••••••••"
                    required
                  />
                  <Lock className="h-5 w-5 text-blue-500 absolute left-3 top-1/2 transform -translate-y-1/2" />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-400" />
                    )}
                  </Button>
                </div>
              </div>

              {/* معرف الجهاز (إظهار مبسط للمستخدمين فقط) */}
              {loginType === 'user' && (
                <div className="space-y-2">
                  <Label className="text-right block">معرف الجهاز</Label>
                  <DeviceIdDisplay
                    onDeviceIdReady={(id) => setDeviceId(id)}
                    showDetails={false}
                  />
                </div>
              )}

              {/* زر تسجيل الدخول */}
              <Button
                type="submit"
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-3 text-lg font-semibold"
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    جاري تسجيل الدخول...
                  </div>
                ) : (
                  <>
                    <LogIn className="h-5 w-5 mr-2" />
                    {loginType === 'user' ? 'تسجيل دخول المستخدم' : 'تسجيل دخول العميل'}
                  </>
                )}
              </Button>
            </form>

            {/* تم حذف ملاحظات وإرشادات المساعدة وزر ملء البيانات التجريبية */}
          </CardContent>
        </Card>

        {/* تذييل */}
        <div className="text-center mt-6">
          <p className="text-sm text-gray-500">
            © 2024 نظام الإدارة القانونية المتخصص
          </p>
        </div>
      </div>
    </div>
  )
}
