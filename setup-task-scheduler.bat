@echo off
echo Setting up Advanced Unified Server with Task Scheduler...
echo.

REM Check for admin privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

REM Configuration
set TASK_NAME=AdvancedUnifiedServer
set WORKING_DIR=D:\mohammi
set SERVER_SCRIPT=advanced-unified-server.js
set NODE_PATH=

REM Find Node.js path
echo Looking for Node.js...
if exist "C:\Program Files\nodejs\node.exe" (
    set NODE_PATH=C:\Program Files\nodejs\node.exe
    echo Found Node.js at: C:\Program Files\nodejs\node.exe
    goto :found_node
)

where node >nul 2>&1
if %errorLevel% equ 0 (
    for /f "tokens=*" %%i in ('where node') do (
        set NODE_PATH=%%i
        echo Found Node.js in PATH: %%i
        goto :found_node
    )
)

echo ERROR: Node.js not found. Please install Node.js first.
pause
exit /b 1

:found_node

REM Check if working directory exists
if not exist "%WORKING_DIR%" (
    echo ERROR: Working directory not found: %WORKING_DIR%
    pause
    exit /b 1
)

REM Check if server script exists
if not exist "%WORKING_DIR%\%SERVER_SCRIPT%" (
    echo ERROR: Server script not found: %WORKING_DIR%\%SERVER_SCRIPT%
    pause
    exit /b 1
)

echo All files found successfully.
echo.

REM Delete existing task if it exists
echo Checking for existing task...
schtasks /query /tn "%TASK_NAME%" >nul 2>&1
if %errorLevel% equ 0 (
    echo Deleting existing task...
    schtasks /delete /tn "%TASK_NAME%" /f >nul 2>&1
)

REM Create new scheduled task
echo Creating scheduled task...
schtasks /create /tn "%TASK_NAME%" /tr "\"%NODE_PATH%\" \"%WORKING_DIR%\%SERVER_SCRIPT%\"" /sc onstart /ru "SYSTEM" /rl highest /f

if %errorLevel% equ 0 (
    echo Task created successfully.
) else (
    echo ERROR: Failed to create task.
    pause
    exit /b 1
)

REM Start the task immediately
echo Starting task...
schtasks /run /tn "%TASK_NAME%"

REM Wait a moment and check if it's running
timeout /t 5 /nobreak >nul

echo Checking task status...
schtasks /query /tn "%TASK_NAME%" /fo list | find "Status"

echo.
echo SUCCESS: Task setup completed!
echo.
echo Task Information:
echo - Task Name: %TASK_NAME%
echo - Node.js Path: %NODE_PATH%
echo - Working Directory: %WORKING_DIR%
echo - Server Script: %SERVER_SCRIPT%
echo - Trigger: At system startup
echo - Run As: SYSTEM
echo.
echo Available Ports:
echo - Port 7443: Mohamed System (Production)
echo - Port 8914: Rabei System  
echo - Port 3300: Mohamed System (Development)
echo.
echo Task Management Commands:
echo - Start: schtasks /run /tn %TASK_NAME%
echo - Stop: taskkill /f /im node.exe
echo - Delete: schtasks /delete /tn %TASK_NAME% /f
echo - View: schtasks /query /tn %TASK_NAME% /fo list
echo.
echo The task will start automatically on server reboot.
echo You can also manage it through Task Scheduler (taskschd.msc)
echo.

pause
