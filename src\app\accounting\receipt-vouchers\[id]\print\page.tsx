"use client"

import React, { useEffect } from 'react'
import { useParams } from 'next/navigation'

// صفحة طباعة سند قبض
// تعتمد على:
// - API السند: /api/accounting/receipt-vouchers/[id]
// - API الشركة: /api/company (أول سجل)

type EntryDetail = {
  account_id: number | null
  account_name?: string | null
  debit_amount: number
  credit_amount: number
  description?: string | null
  payment_method_id?: number | null
  payment_method_name?: string | null
}

type ReceiptVoucher = {
  id: number
  entry_number: string
  entry_date: string
  description?: string | null
  amount: number
  payer_name?: string | null
  payer_type?: string | null
  reference_number?: string | null
  status?: string
  currency_id?: number | null
  payment_method_id?: number | null
  cost_center_id?: number | null
  case_id?: number | null
  case_number?: string | null
  service_id?: number | null
  details?: EntryDetail[]
}

type Company = {
  id: number
  name: string
  legal_form?: string | null
  phone?: string | null
  email?: string | null
  logo_url?: string | null
  logo_image_url?: string | null
}

function classNames(...classes: (string | false | undefined)[]) {
  return classes.filter(Boolean).join(' ')
}

// محول مبسط للأرقام إلى كلمات بالعربية (للأرقام حتى المليار)
function numberToArabicWords(num: number): string {
  if (!isFinite(num)) return ''
  const ones = ['','واحد','اثنان','ثلاثة','أربعة','خمسة','ستة','سبعة','ثمانية','تسعة']
  const teens = ['عشرة','أحد عشر','اثنا عشر','ثلاثة عشر','أربعة عشر','خمسة عشر','ستة عشر','سبعة عشر','ثمانية عشر','تسعة عشر']
  const tens = ['','عشرة','عشرون','ثلاثون','أربعون','خمسون','ستون','سبعون','ثمانون','تسعون']
  const hundreds = ['','مائة','مائتان','ثلاثمائة','أربعمائة','خمسمائة','ستمائة','سبعمائة','ثمانمائة','تسعمائة']
  const scales = ['','ألف','مليون','مليار']

  const toWordsUnder100 = (n: number): string => {
    if (n < 10) return ones[n]
    if (n < 20) return teens[n - 10]
    const t = Math.floor(n / 10)
    const o = n % 10
    if (o === 0) return tens[t]
    return `${ones[o]} و ${tens[t]}`
  }

  const toWordsUnder1000 = (n: number): string => {
    const h = Math.floor(n / 100)
    const r = n % 100
    const hPart = h ? hundreds[h] : ''
    const rPart = r ? toWordsUnder100(r) : ''
    if (hPart && rPart) return `${rPart} و ${hPart}`
    return hPart || rPart || 'صفر'
  }

  if (num === 0) return 'صفر'

  const parts: string[] = []
  let scaleIndex = 0
  let n = Math.floor(Math.abs(num))

  while (n > 0 && scaleIndex < scales.length) {
    const chunk = n % 1000
    if (chunk) {
      let chunkWords = toWordsUnder1000(chunk)
      let scaleWord = scales[scaleIndex]
      // صياغة الجمع البسيطة
      if (scaleIndex === 1) { // آلاف
        if (chunk === 2) scaleWord = 'ألفان'
        else if (chunk > 2 && chunk < 11) scaleWord = 'آلاف'
      } else if (scaleIndex > 1 && chunk === 2) {
        scaleWord = scales[scaleIndex] + 'ان'
      } else if (scaleIndex > 1 && chunk > 2 && chunk < 11) {
        scaleWord = scales[scaleIndex] + 'ات'
      }
      parts.unshift(classNames(chunkWords, scaleIndex ? scaleWord : ''))
    }
    n = Math.floor(n / 1000)
    scaleIndex++
  }

  return parts.filter(Boolean).join(' و ')
}

function formatDateISO(d?: string) {
  if (!d) return ''
  try {
    const date = new Date(d)
    const y = date.getFullYear()
    const m = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${y}/${m}/${day}`
  } catch {
    return d || ''
  }
}

export default function PrintReceiptVoucherPage() {
  const params = useParams()
  const id = (params?.id as string) || ''

  const [voucher, setVoucher] = React.useState<ReceiptVoucher | null>(null)
  const [company, setCompany] = React.useState<Company | null>(null)
  const [loading, setLoading] = React.useState(true)
  const [error, setError] = React.useState<string | null>(null)

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        // بيانات سند القبض
        const resVoucher = await fetch(`/api/accounting/receipt-vouchers/${id}`, { cache: 'no-store' })
        const jsonVoucher = await resVoucher.json()
        if (!jsonVoucher.success) throw new Error(jsonVoucher.error || 'فشل جلب السند')
        setVoucher(jsonVoucher.voucher)

        // بيانات الشركة
        const resCompany = await fetch('/api/company', { cache: 'no-store' })
        const jsonCompany = await resCompany.json()
        const companyData = jsonCompany?.data?.[0] || null
        setCompany(companyData)
      } catch (e: any) {
        setError(e?.message || 'حدث خطأ غير متوقع')
      } finally {
        setLoading(false)
      }
    }
    if (id) fetchData()
  }, [id])

  useEffect(() => {
    // يمكن تشغيل الطباعة تلقائيًا إن رغبت، لكن سنكتفي بزر طباعة داخل الصفحة
    // if (!loading && voucher) setTimeout(() => window.print(), 300)
  }, [loading, voucher])

  if (loading) return <div style={{padding: 24, direction: 'rtl'}}>جاري التحميل...</div>
  if (error) return <div style={{padding: 24, color: 'red', direction: 'rtl'}}>خطأ: {error}</div>
  if (!voucher) return <div style={{padding: 24, direction: 'rtl'}}>لا توجد بيانات</div>

  // عنوان الطباعة ومبلغ السند
  const title = `سند قبض`
  const amount = voucher.amount || 0
  const amountWords = numberToArabicWords(Math.round(amount)) + ' فقط'

  return (
    <div className="print-container" dir="rtl">
      <style jsx global>{`
        @media print {
          @page { size: A4; margin: 12mm; }
          .no-print { display: none !important; }
        }
        /* Improve color fidelity in print */
        .print-container, .print-container * {
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
          color-adjust: exact;
        }
        :root {
          /* Palette taken to match app identity (approximate to the shown image) */
          --mint-50:   #d1fae5;  /* سندات القبض - فاتح أخضر */
          --mint-300:  #6ee7b7;
          --rose-50:   #fee2e2;  /* سندات الصرف - وردي فاتح */
          --rose-300:  #fca5a5;
          --violet-50: #f3e8ff;  /* ربط الحسابات - بنفسجي فاتح */
          --violet-300:#c4b5fd;
          --amber-50:  #fef3c7;  /* الأرصدة الافتتاحية - أصفر فاتح */
          --amber-300: #fcd34d;
        }
        .print-container { font-family: "Tajawal", system-ui, Arial, sans-serif; color: #000; }
        .header { display: grid; grid-template-columns: 1fr auto 1fr; align-items: center; }
        .right { text-align: right; }
        .left { text-align: left; }
        .center { text-align: center; }
        .company-name { font-size: 20px; font-weight: 800; }
        .legal-form { font-size: 14px; color: #000; }
        .logo { height: 120px; object-fit: contain; }
        .divider-bold { height: 8px; background: #000; margin: 8px 0 12px; }
        .row { display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 12px; align-items: center; margin-bottom: 10px; }
        .title { font-size: 18px; font-weight: 800; text-align: center; padding: 4px 10px; border: 2px solid var(--mint-300) !important; border-radius: 10px; background: var(--mint-50) !important; color: #000; }
        .field { border: 1px solid #ccc; background: #fff; padding: 8px 10px; border-radius: 8px; min-height: 36px; }
        .field.long { min-height: 54px; }
        .label { color: #000; font-weight: 700; margin-left: 6px; }
        .section { margin-top: 10px; }
        .footer { display: flex; justify-content: space-between; margin-top: 18px; }
        .signature { text-align: center; min-width: 180px; }
        .signature .line { border-top: 2px solid #000; margin-top: 36px; }
        .muted { color: #000; }
        .actions { margin-top: 16px; text-align: center; }
        .actions button { padding: 8px 16px; border: 1px solid #ccc; border-radius: 8px; margin: 0 6px; cursor: pointer; background: #fff; color: #000; }
        .print-date { margin-top: 10px; width: 100%; text-align: left; font-size: 12px; }

        /* Themed fields per user request */
        .theme-opening { background: var(--amber-50) !important; border-color: var(--amber-300) !important; }
        .theme-expense { background: var(--rose-50)  !important; border-color: var(--rose-300) !important; }
        .theme-link    { background: var(--violet-50) !important; border-color: var(--violet-300) !important; }
      `}</style>

      {/* ترويسة */}
      <div className="header">
        <div className="right">
          <div className="company-name">{company?.name || ''}</div>
          <div className="legal-form">{company?.legal_form || ''}</div>
        </div>
        <div className="center">
          {company?.logo_image_url || company?.logo_url ? (
            // eslint-disable-next-line @next/next/no-img-element
            <img src={(company?.logo_image_url || company?.logo_url) as string} alt="Logo" className="logo" />
          ) : null}
        </div>
        <div className="left">
          <div>{company?.phone ? <>رقم الهاتف: <span className="muted">{company.phone}</span></> : null}</div>
          <div>{company?.email ? <>الإيميل: <span className="muted">{company.email}</span></> : null}</div>
        </div>
      </div>

      <div className="divider-bold" />

      {/* عنوان ومستطيلات المبلغ والتاريخ */}
      <div className="row">
        <div className="field theme-opening"><span className="label">المبلغ:</span> {amount.toLocaleString()}</div>
        <div className="title">{title}</div>
        <div className="field theme-opening"><span className="label">التاريخ:</span> {formatDateISO(voucher.entry_date)}</div>
      </div>

      {/* بيانات الدافع */}
      <div className="section">
        <div className="field theme-expense"><span className="label">استلمت من الأخ /</span> {voucher.payer_name || ''}</div>
      </div>

      {/* مبلغ وقدره بالحروف */}
      <div className="section">
        <div className="field long"><span className="label">مبلغاً وقدره</span> {amountWords}</div>
      </div>

      {/* مقابل */}
      <div className="section">
        <div className="field theme-link"><span className="label">وذلك مقابل /</span> {voucher.description || voucher.reference_number || ''}</div>
      </div>

      {/* قضية / محكمة / خدمة - اختياري */}
      {(voucher.case_number || (voucher as any).case_title || (voucher as any).court_name || (voucher as any).service_name || voucher.service_id) && (
        <div className="section">
          <div className="row" style={{gridTemplateColumns: '1fr 1fr 1fr'}}>
            {(voucher as any).case_title || voucher.case_number ? (
              <div className="field"><span className="label">القضية /</span> {(voucher as any).case_title || voucher.case_number}</div>
            ) : <div />}
            {(voucher as any).court_name ? (
              <div className="field"><span className="label">المحكمة /</span> {(voucher as any).court_name}</div>
            ) : <div />}
            {(voucher as any).service_name || voucher.service_id ? (
              <div className="field"><span className="label">الخدمة /</span> {(voucher as any).service_name || voucher.service_id}</div>
            ) : <div />}
          </div>
        </div>
      )}

      <div className="divider-bold" />

      {/* تواقيع */}
      <div className="footer">
        <div className="signature">
          <div>أمين الصندوق</div>
          <div className="line" />
        </div>
        <div className="signature">
          <div>المستلم</div>
          <div className="line" />
        </div>
      </div>

      {/* أزرار للطباعة/الإغلاق للشاشة فقط */}
      <div className="no-print actions">
        <button onClick={() => window.print()}>طباعة</button>
        <button onClick={() => window.close()}>إغلاق</button>
      </div>

      {/* تاريخ الطباعة أسفل التواقيع */}
      <div className="print-date">تاريخ الطباعة: {new Date().toLocaleString('en-GB')}</div>
    </div>
  )
}
