/**
 * إضافة بيانات تجريبية لنظام تتبع القضايا
 */

const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'mohammidev'
};

async function addCaseTrackingData() {
  const client = new Client(dbConfig);
  
  try {
    await client.connect();
    console.log('🔗 متصل بقاعدة البيانات');

    // إضافة حركات للقضايا
    await client.query(`
      INSERT INTO case_movements (case_id, movement_type, description, employee_id, priority_level) VALUES
      (1, 'case_created', 'تم إنشاء القضية وتسجيلها في النظام', 1, 'medium'),
      (1, 'document_review', 'مراجعة الوثائق المقدمة من العميل', 1, 'high'),
      (1, 'court_filing', 'تقديم الدعوى للمحكمة المختصة', 1, 'high'),
      (2, 'case_created', 'تم إنشاء قضية عمالية جديدة', 1, 'medium'),
      (2, 'client_meeting', 'اجتماع مع العميل لمناقشة تفاصيل القضية', 1, 'medium'),
      (3, 'case_created', 'تم إنشاء قضية مدنية للتعويض', 1, 'low'),
      (3, 'evidence_collection', 'جمع الأدلة والشهادات', 1, 'medium')
      ON CONFLICT DO NOTHING;
    `);
    console.log('✅ تم إضافة حركات القضايا');

    // إضافة جلسات المحكمة
    await client.query(`
      INSERT INTO court_sessions (case_id, session_date, court_name, session_type, reminder_days, notes) VALUES
      (1, '2025-01-15 10:00:00', 'المحكمة التجارية', 'hearing', 3, 'جلسة أولى للنظر في الدعوى'),
      (1, '2025-01-25 14:00:00', 'المحكمة التجارية', 'follow_up', 3, 'جلسة متابعة'),
      (2, '2025-01-12 09:00:00', 'محكمة العمل', 'hearing', 2, 'جلسة استماع للطرفين'),
      (3, '2025-01-20 11:00:00', 'المحكمة المدنية', 'preliminary', 5, 'جلسة تمهيدية')
      ON CONFLICT DO NOTHING;
    `);
    console.log('✅ تم إضافة جلسات المحكمة');

    // إضافة مراحل القضايا
    await client.query(`
      INSERT INTO case_stages (case_id, stage_name, stage_type, status, start_date, responsible_employee_id, order_index) VALUES
      (1, 'تسجيل القضية', 'initial', 'completed', '2025-01-01', 1, 1),
      (1, 'جمع الأدلة', 'preparation', 'in_progress', '2025-01-03', 1, 2),
      (1, 'المرافعة', 'court', 'pending', NULL, 1, 3),
      (2, 'تسجيل القضية', 'initial', 'completed', '2025-01-02', 1, 1),
      (2, 'دراسة الحالة', 'preparation', 'in_progress', '2025-01-04', 1, 2),
      (3, 'تسجيل القضية', 'initial', 'completed', '2025-01-03', 1, 1),
      (3, 'تقييم الأضرار', 'assessment', 'pending', NULL, 1, 2)
      ON CONFLICT DO NOTHING;
    `);
    console.log('✅ تم إضافة مراحل القضايا');

    // تحديث تواريخ آخر تحديث للقضايا
    await client.query(`
      UPDATE cases SET updated_at = CURRENT_TIMESTAMP WHERE id IN (1, 2, 3);
    `);
    console.log('✅ تم تحديث تواريخ القضايا');

    console.log('\n🎉 تم إضافة جميع البيانات التجريبية بنجاح!');

    // عرض ملخص البيانات
    const summary = await client.query(`
      SELECT 
        (SELECT COUNT(*) FROM cases) as total_cases,
        (SELECT COUNT(*) FROM case_movements) as total_movements,
        (SELECT COUNT(*) FROM court_sessions) as total_sessions,
        (SELECT COUNT(*) FROM case_stages) as total_stages
    `);

    console.log('\n📊 ملخص البيانات:');
    console.log(`   - القضايا: ${summary.rows[0].total_cases}`);
    console.log(`   - الحركات: ${summary.rows[0].total_movements}`);
    console.log(`   - الجلسات: ${summary.rows[0].total_sessions}`);
    console.log(`   - المراحل: ${summary.rows[0].total_stages}`);

  } catch (error) {
    console.error('❌ خطأ في إضافة البيانات:', error.message);
    throw error;
  } finally {
    await client.end();
  }
}

// تشغيل الدالة
if (require.main === module) {
  addCaseTrackingData()
    .then(() => {
      console.log('✅ اكتملت إضافة البيانات التجريبية');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ فشل في إضافة البيانات:', error);
      process.exit(1);
    });
}

module.exports = { addCaseTrackingData };
