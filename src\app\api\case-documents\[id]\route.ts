import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = parseInt(params.id)
    if (!id) return NextResponse.json({ success: false, error: 'id مطلوب' }, { status: 400 })

    const res = await query(
      `SELECT file_name, mime_type, content FROM case_documents WHERE id = $1 LIMIT 1`,
      [id]
    )
    if (res.rowCount === 0) {
      return NextResponse.json({ success: false, error: 'الملف غير موجود' }, { status: 404 })
    }

    const { file_name, mime_type, content } = res.rows[0]
    const headers = new Headers()
    headers.set('Content-Type', mime_type || 'application/octet-stream')
    // تنزيل افتراضي
    const { searchParams } = new URL(request.url)
    const asAttachment = searchParams.get('download') !== '0'
    if (asAttachment) {
      headers.set('Content-Disposition', `attachment; filename*=UTF-8''${encodeURIComponent(file_name)}`)
    } else {
      headers.set('Content-Disposition', `inline; filename*=UTF-8''${encodeURIComponent(file_name)}`)
    }

    return new NextResponse(content, { status: 200, headers })
  } catch (e: any) {
    console.error('Error downloading case document:', e)
    return NextResponse.json({ success: false, error: 'فشل تنزيل الوثيقة' }, { status: 500 })
  }
}
