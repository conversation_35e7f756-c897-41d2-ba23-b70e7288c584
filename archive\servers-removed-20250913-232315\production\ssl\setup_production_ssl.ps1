# سكريپت تثبيت SSL للإنتاج - mohammi.com
# IP: ***********

param(
    [string]$Domain = "mohammi.com",
    [string]$IP = "***********",
    [string]$SSLPath = "D:\mohaminew\ssl"
)

Write-Host "🔐 تثبيت شهادة SSL للإنتاج" -ForegroundColor Green
Write-Host "🌐 الدومين: $Domain" -ForegroundColor Cyan
Write-Host "📍 IP الخارجي: $IP" -ForegroundColor Cyan
Write-Host "📁 مسار SSL: $SSLPath" -ForegroundColor Cyan
Write-Host "=" * 60

# التحقق من وجود مجلد SSL
if (!(Test-Path $SSLPath)) {
    Write-Host "❌ مجلد SSL غير موجود: $SSLPath" -ForegroundColor Red
    exit 1
}

Write-Host "✅ تم العثور على مجلد SSL" -ForegroundColor Green

# البحث عن ملفات الشهادة
Write-Host "`n🔍 البحث عن ملفات الشهادة..." -ForegroundColor Yellow

$CertFiles = @{
    Certificate = @("*.crt", "*.pem", "*certificate*", "*cert*")
    PrivateKey = @("*.key", "*private*", "*key*")
    Intermediate = @("*intermediate*", "*chain*", "*bundle*", "*ca*")
}

$FoundFiles = @{}

foreach ($Type in $CertFiles.Keys) {
    Write-Host "  🔍 البحث عن $Type..." -ForegroundColor Gray
    
    foreach ($Pattern in $CertFiles[$Type]) {
        $Files = Get-ChildItem -Path $SSLPath -Filter $Pattern -File | Where-Object { $_.Name -notlike "*backup*" -and $_.Name -notlike "*old*" }
        
        if ($Files) {
            $FoundFiles[$Type] = $Files[0].FullName
            Write-Host "    ✅ تم العثور على: $($Files[0].Name)" -ForegroundColor Green
            break
        }
    }
    
    if (!$FoundFiles[$Type]) {
        Write-Host "    ⚠️ لم يتم العثور على $Type" -ForegroundColor Yellow
    }
}

# التحقق من الملفات الأساسية
if (!$FoundFiles.Certificate) {
    Write-Host "`n❌ لم يتم العثور على ملف الشهادة" -ForegroundColor Red
    Write-Host "💡 تأكد من وجود ملف .crt أو .pem في مجلد SSL" -ForegroundColor Yellow
    exit 1
}

if (!$FoundFiles.PrivateKey) {
    Write-Host "`n❌ لم يتم العثور على المفتاح الخاص" -ForegroundColor Red
    Write-Host "💡 تأكد من وجود ملف .key في مجلد SSL" -ForegroundColor Yellow
    exit 1
}

Write-Host "`n✅ تم العثور على الملفات الأساسية" -ForegroundColor Green

# إنشاء ملف تكوين Node.js للخادم
Write-Host "`n🔧 إنشاء تكوين خادم الإنتاج..." -ForegroundColor Yellow

$ServerConfig = @"
// خادم إنتاج SSL لـ $Domain
const https = require('https');
const http = require('http');
const fs = require('fs');
const path = require('path');

console.log('🚀 بدء خادم الإنتاج SSL');
console.log('🌐 الدومين: $Domain');
console.log('📍 IP: $IP');

// قراءة ملفات SSL
const sslOptions = {
  key: fs.readFileSync('$($FoundFiles.PrivateKey.Replace('\', '\\'))', 'utf8'),
  cert: fs.readFileSync('$($FoundFiles.Certificate.Replace('\', '\\'))', 'utf8')$(if ($FoundFiles.Intermediate) { ",`n  ca: fs.readFileSync('$($FoundFiles.Intermediate.Replace('\', '\\'))', 'utf8')" }),
  secureProtocol: 'TLSv1_2_method',
  ciphers: 'ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384',
  honorCipherOrder: true
};

// خادم HTTPS - Proxy إلى Next.js
const httpsServer = https.createServer(sslOptions, (req, res) => {
  // Headers الأمان
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  
  // Proxy إلى تطبيق Next.js
  const proxy = http.request({
    hostname: 'localhost',
    port: 7443,
    path: req.url,
    method: req.method,
    headers: {
      ...req.headers,
      'X-Forwarded-For': req.connection.remoteAddress,
      'X-Forwarded-Proto': 'https',
      'X-Forwarded-Host': req.headers.host
    }
  }, (proxyRes) => {
    res.writeHead(proxyRes.statusCode, proxyRes.headers);
    proxyRes.pipe(res);
  });
  
  proxy.on('error', (error) => {
    console.error('❌ خطأ Proxy:', error.message);
    res.writeHead(502, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end('<h1>خطأ في الخادم</h1><p>يرجى المحاولة لاحقاً</p>');
  });
  
  req.pipe(proxy);
});

// خادم HTTP - إعادة توجيه إلى HTTPS
const httpServer = http.createServer((req, res) => {
  const httpsUrl = ``https://`${req.headers.host}`${req.url}``;
  res.writeHead(301, { 'Location': httpsUrl });
  res.end();
});

// بدء الخوادم
httpsServer.listen(443, '0.0.0.0', () => {
  console.log('✅ خادم HTTPS يعمل على المنفذ 443');
  console.log('🌐 https://$Domain');
});

httpServer.listen(80, '0.0.0.0', () => {
  console.log('✅ خادم HTTP يعمل على المنفذ 80 (إعادة توجيه)');
});

process.on('SIGINT', () => {
  console.log('🛑 إيقاف الخوادم...');
  httpsServer.close();
  httpServer.close();
  process.exit(0);
});
"@

$ServerConfig | Out-File -FilePath "$SSLPath\production_server.js" -Encoding UTF8
Write-Host "✅ تم إنشاء: production_server.js" -ForegroundColor Green

# إنشاء ملف خدمة Windows
Write-Host "`n🔧 إنشاء خدمة Windows..." -ForegroundColor Yellow

$ServiceScript = @"
// خدمة Windows لخادم SSL
const Service = require('node-windows').Service;

const svc = new Service({
  name: 'MohammiSSLServer',
  description: 'خادم SSL لموقع mohammi.com',
  script: '$SSLPath\\production_server.js',
  nodeOptions: [
    '--harmony',
    '--max_old_space_size=4096'
  ]
});

svc.on('install', () => {
  console.log('✅ تم تثبيت الخدمة بنجاح');
  svc.start();
});

svc.on('start', () => {
  console.log('✅ تم بدء الخدمة');
});

svc.install();
"@

$ServiceScript | Out-File -FilePath "$SSLPath\install_service.js" -Encoding UTF8
Write-Host "✅ تم إنشاء: install_service.js" -ForegroundColor Green

# إنشاء ملف batch للتشغيل السريع
$BatchScript = @"
@echo off
echo 🚀 بدء خادم SSL للإنتاج
echo 🌐 الدومين: $Domain
echo 📍 IP: $IP
echo.

cd /d "$SSLPath"
node production_server.js

pause
"@

$BatchScript | Out-File -FilePath "$SSLPath\start_ssl_server.bat" -Encoding ASCII
Write-Host "✅ تم إنشاء: start_ssl_server.bat" -ForegroundColor Green

# إنشاء ملف اختبار SSL
$TestScript = @"
// اختبار شهادة SSL
const https = require('https');

console.log('🧪 اختبار شهادة SSL لـ $Domain');

const options = {
  hostname: '$Domain',
  port: 443,
  path: '/',
  method: 'GET',
  rejectUnauthorized: true
};

const req = https.request(options, (res) => {
  console.log('✅ الاتصال SSL ناجح');
  console.log('📊 معلومات الشهادة:');
  console.log('   - الحالة:', res.statusCode);
  console.log('   - البروتوكول:', res.socket.getProtocol());
  console.log('   - التشفير:', res.socket.getCipher());
  
  const cert = res.socket.getPeerCertificate();
  if (cert) {
    console.log('   - الموضوع:', cert.subject.CN);
    console.log('   - المُصدر:', cert.issuer.CN);
    console.log('   - صالح من:', cert.valid_from);
    console.log('   - صالح حتى:', cert.valid_to);
  }
});

req.on('error', (error) => {
  console.error('❌ خطأ في الاتصال SSL:', error.message);
});

req.end();
"@

$TestScript | Out-File -FilePath "$SSLPath\test_ssl.js" -Encoding UTF8
Write-Host "✅ تم إنشاء: test_ssl.js" -ForegroundColor Green

# عرض الملخص
Write-Host "`n📋 ملخص التثبيت:" -ForegroundColor Cyan
Write-Host "=" * 40
Write-Host "📄 الشهادة: $(Split-Path $FoundFiles.Certificate -Leaf)" -ForegroundColor White
Write-Host "🔑 المفتاح الخاص: $(Split-Path $FoundFiles.PrivateKey -Leaf)" -ForegroundColor White
if ($FoundFiles.Intermediate) {
    Write-Host "🔗 الشهادة الوسطية: $(Split-Path $FoundFiles.Intermediate -Leaf)" -ForegroundColor White
}

Write-Host "`n🚀 خطوات التشغيل:" -ForegroundColor Yellow
Write-Host "1. تشغيل كمدير: .\ssl\start_ssl_server.bat" -ForegroundColor White
Write-Host "2. أو تثبيت كخدمة: node .\ssl\install_service.js" -ForegroundColor White
Write-Host "3. اختبار الشهادة: node .\ssl\test_ssl.js" -ForegroundColor White

Write-Host "`n🌐 الروابط:" -ForegroundColor Yellow
Write-Host "• https://$Domain" -ForegroundColor White
Write-Host "• https://www.$Domain" -ForegroundColor White
Write-Host "• https://$IP (مباشر)" -ForegroundColor White

Write-Host "`n🔧 اختبار الشهادة عبر الإنترنت:" -ForegroundColor Yellow
Write-Host "• https://www.ssllabs.com/ssltest/analyze.html?d=$Domain" -ForegroundColor White
Write-Host "• https://www.sslshopper.com/ssl-checker.html#hostname=$Domain" -ForegroundColor White

Write-Host "`n✅ تم الانتهاء من إعداد SSL!" -ForegroundColor Green
