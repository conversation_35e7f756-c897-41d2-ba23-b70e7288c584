const { Client } = require('pg');

async function createNotificationsSystem() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    user: 'postgres',
    password: 'yemen123',
    database: 'mohammidev'
  });

  try {
    await client.connect();
    console.log('🔗 متصل بقاعدة البيانات');

    // جدول قوالب التنبيهات
    await client.query(`
      CREATE TABLE IF NOT EXISTS notification_templates (
        id SERIAL PRIMARY KEY,
        name VARCHAR(100) UNIQUE NOT NULL,
        title_template TEXT NOT NULL,
        body_template TEXT NOT NULL,
        notification_type VARCHAR(50) NOT NULL, -- 'hearing', 'deadline', 'payment', 'inactive_case', etc.
        recipient_type VARCHAR(50) NOT NULL, -- 'client', 'employee', 'manager', 'all'
        channels JSONB DEFAULT '["system"]', -- ['system', 'email', 'sms', 'whatsapp', 'push']
        timing_rules JSONB, -- قواعد التوقيت
        priority VARCHAR(20) DEFAULT 'normal', -- low, normal, high, urgent
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // جدول التنبيهات المجدولة
    await client.query(`
      CREATE TABLE IF NOT EXISTS scheduled_notifications (
        id SERIAL PRIMARY KEY,
        template_id INTEGER REFERENCES notification_templates(id),
        case_id INTEGER REFERENCES issues(id),
        recipient_id INTEGER, -- employee_id or client_id
        recipient_type VARCHAR(50), -- 'client', 'employee'
        recipient_name VARCHAR(255),
        recipient_phone VARCHAR(50),
        recipient_email VARCHAR(255),
        
        -- محتوى التنبيه
        title TEXT NOT NULL,
        body TEXT NOT NULL,
        channels JSONB DEFAULT '["system"]',
        
        -- توقيت التنبيه
        scheduled_for TIMESTAMP NOT NULL,
        timezone VARCHAR(50) DEFAULT 'Asia/Riyadh',
        
        -- حالة التنبيه
        status VARCHAR(50) DEFAULT 'pending', -- pending, sent, delivered, read, failed, cancelled
        priority VARCHAR(20) DEFAULT 'normal',
        
        -- معلومات الإرسال
        sent_at TIMESTAMP,
        delivered_at TIMESTAMP,
        read_at TIMESTAMP,
        failed_at TIMESTAMP,
        error_message TEXT,
        
        -- معلومات إضافية
        metadata JSONB, -- بيانات إضافية حسب نوع التنبيه
        retry_count INTEGER DEFAULT 0,
        max_retries INTEGER DEFAULT 3,
        
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // جدول سجل التنبيهات المرسلة
    await client.query(`
      CREATE TABLE IF NOT EXISTS notification_logs (
        id SERIAL PRIMARY KEY,
        scheduled_notification_id INTEGER REFERENCES scheduled_notifications(id),
        channel VARCHAR(50) NOT NULL, -- system, email, sms, whatsapp, push
        recipient_id INTEGER,
        recipient_type VARCHAR(50),
        recipient_contact VARCHAR(255), -- phone, email, etc.
        
        -- محتوى مرسل
        title TEXT,
        body TEXT,
        
        -- حالة الإرسال
        status VARCHAR(50) NOT NULL, -- sent, delivered, read, failed
        response_data JSONB, -- استجابة من مزود الخدمة
        error_message TEXT,
        
        -- توقيت
        sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        delivered_at TIMESTAMP,
        read_at TIMESTAMP,
        
        -- تكلفة (للرسائل المدفوعة)
        cost DECIMAL(10,4) DEFAULT 0,
        currency VARCHAR(3) DEFAULT 'YER'
      )
    `);

    // جدول إعدادات التنبيهات للمستخدمين
    await client.query(`
      CREATE TABLE IF NOT EXISTS user_notification_preferences (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        user_type VARCHAR(50) NOT NULL, -- 'client', 'employee'
        
        -- تفضيلات القنوات
        email_enabled BOOLEAN DEFAULT true,
        sms_enabled BOOLEAN DEFAULT true,
        whatsapp_enabled BOOLEAN DEFAULT true,
        push_enabled BOOLEAN DEFAULT true,
        system_enabled BOOLEAN DEFAULT true,
        
        -- تفضيلات أنواع التنبيهات
        hearing_notifications BOOLEAN DEFAULT true,
        deadline_notifications BOOLEAN DEFAULT true,
        payment_notifications BOOLEAN DEFAULT true,
        case_update_notifications BOOLEAN DEFAULT true,
        marketing_notifications BOOLEAN DEFAULT false,
        
        -- تفضيلات التوقيت
        quiet_hours_start TIME DEFAULT '22:00',
        quiet_hours_end TIME DEFAULT '08:00',
        weekend_notifications BOOLEAN DEFAULT false,
        
        -- معلومات الاتصال
        preferred_phone VARCHAR(50),
        preferred_email VARCHAR(255),
        whatsapp_number VARCHAR(50),
        
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        UNIQUE(user_id, user_type)
      )
    `);

    // جدول قواعد التنبيهات التلقائية
    await client.query(`
      CREATE TABLE IF NOT EXISTS notification_rules (
        id SERIAL PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        
        -- شروط التفعيل
        trigger_event VARCHAR(100) NOT NULL, -- 'hearing_scheduled', 'case_inactive', etc.
        conditions JSONB, -- شروط إضافية
        
        -- قواعد التوقيت
        timing_offset INTEGER, -- بالدقائق قبل/بعد الحدث
        timing_unit VARCHAR(20) DEFAULT 'minutes', -- minutes, hours, days
        
        -- قالب التنبيه
        template_id INTEGER REFERENCES notification_templates(id),
        
        -- المستهدفون
        target_recipients JSONB, -- ['client', 'assigned_employee', 'manager']
        
        -- حالة القاعدة
        is_active BOOLEAN DEFAULT true,
        priority INTEGER DEFAULT 1, -- ترتيب التنفيذ
        
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    console.log('✅ تم إنشاء جداول نظام التنبيهات بنجاح');

    // إنشاء الفهارس
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_scheduled_for ON scheduled_notifications(scheduled_for);
      CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_status ON scheduled_notifications(status);
      CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_case_id ON scheduled_notifications(case_id);
      CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_recipient ON scheduled_notifications(recipient_id, recipient_type);
      CREATE INDEX IF NOT EXISTS idx_notification_logs_sent_at ON notification_logs(sent_at);
      CREATE INDEX IF NOT EXISTS idx_notification_logs_status ON notification_logs(status);
      CREATE INDEX IF NOT EXISTS idx_user_preferences_user ON user_notification_preferences(user_id, user_type);
    `);

    console.log('✅ تم إنشاء الفهارس بنجاح');

    // إدراج قوالب التنبيهات الأساسية
    const templates = [
      {
        name: 'hearing_reminder_3_days',
        title: 'تذكير: جلسة قضية {{case_number}}',
        body: 'تذكير بأن لديك جلسة في قضية {{case_title}} يوم {{hearing_date}} في {{court_name}}. يرجى الحضور في الموعد المحدد.',
        type: 'hearing',
        recipient: 'client',
        channels: '["system", "email", "sms"]'
      },
      {
        name: 'hearing_reminder_1_day',
        title: 'تذكير عاجل: جلسة غداً',
        body: 'تذكير عاجل: لديك جلسة غداً {{hearing_date}} في قضية {{case_title}} بمحكمة {{court_name}}.',
        type: 'hearing',
        recipient: 'client',
        channels: '["system", "email", "sms", "whatsapp"]'
      },
      {
        name: 'case_inactive_30_days',
        title: 'قضية خاملة: {{case_number}}',
        body: 'القضية {{case_title}} لم تشهد أي حركة منذ 30 يوماً. يرجى المراجعة واتخاذ الإجراء المناسب.',
        type: 'inactive_case',
        recipient: 'employee',
        channels: '["system", "email"]'
      },
      {
        name: 'payment_due_reminder',
        title: 'تذكير: استحقاق دفعة',
        body: 'تذكير بأن لديك دفعة مستحقة بمبلغ {{amount}} {{currency}} لقضية {{case_title}}.',
        type: 'payment',
        recipient: 'client',
        channels: '["system", "email", "sms"]'
      },
      {
        name: 'document_uploaded',
        title: 'وثيقة جديدة: {{case_number}}',
        body: 'تم رفع وثيقة جديدة "{{document_title}}" لقضية {{case_title}}.',
        type: 'document',
        recipient: 'client',
        channels: '["system", "email"]'
      }
    ];

    for (const template of templates) {
      await client.query(`
        INSERT INTO notification_templates (
          name, title_template, body_template, notification_type, 
          recipient_type, channels, priority
        ) VALUES ($1, $2, $3, $4, $5, $6, 'normal')
        ON CONFLICT (name) DO UPDATE SET
          title_template = EXCLUDED.title_template,
          body_template = EXCLUDED.body_template,
          updated_at = CURRENT_TIMESTAMP
      `, [
        template.name,
        template.title,
        template.body,
        template.type,
        template.recipient,
        template.channels
      ]);
    }

    console.log('✅ تم إدراج قوالب التنبيهات الأساسية');

    // إدراج قواعد التنبيهات التلقائية
    const rules = [
      {
        name: 'hearing_reminder_3_days',
        description: 'تذكير بالجلسة قبل 3 أيام',
        trigger: 'hearing_scheduled',
        offset: -4320, // 3 أيام بالدقائق
        template: 'hearing_reminder_3_days',
        recipients: '["client"]'
      },
      {
        name: 'hearing_reminder_1_day',
        description: 'تذكير بالجلسة قبل يوم واحد',
        trigger: 'hearing_scheduled',
        offset: -1440, // يوم واحد بالدقائق
        template: 'hearing_reminder_1_day',
        recipients: '["client"]'
      },
      {
        name: 'case_inactive_check',
        description: 'فحص القضايا الخاملة',
        trigger: 'case_inactive',
        offset: 0,
        template: 'case_inactive_30_days',
        recipients: '["assigned_employee", "manager"]'
      }
    ];

    for (const rule of rules) {
      const templateResult = await client.query(
        'SELECT id FROM notification_templates WHERE name = $1',
        [rule.template]
      );
      
      if (templateResult.rows.length > 0) {
        await client.query(`
          INSERT INTO notification_rules (
            name, description, trigger_event, timing_offset, 
            template_id, target_recipients, is_active
          ) VALUES ($1, $2, $3, $4, $5, $6, true)
          ON CONFLICT (name) DO UPDATE SET
            description = EXCLUDED.description,
            updated_at = CURRENT_TIMESTAMP
        `, [
          rule.name,
          rule.description,
          rule.trigger,
          rule.offset,
          templateResult.rows[0].id,
          rule.recipients
        ]);
      }
    }

    console.log('✅ تم إدراج قواعد التنبيهات التلقائية');

  } catch (error) {
    console.error('❌ خطأ:', error);
  } finally {
    await client.end();
  }
}

createNotificationsSystem();
