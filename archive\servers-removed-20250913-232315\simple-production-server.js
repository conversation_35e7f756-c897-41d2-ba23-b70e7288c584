/**
 * خادم إنتاج بسيط وموثوق - بدون تعقيدات
 * يعمل مباشرة بدون مشاكل
 */

const http = require('http');
const https = require('https');
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

console.log('🚀 بدء الخادم البسيط...');

// إعدادات بسيطة
const PORTS = {
  7443: { name: 'محمد', db: 'mohammi' },
  8914: { name: 'الربعي', db: 'rubaie' }
};

// متغيرات الخادم
const servers = new Map();
let nextProcess = null;

// دالة بدء Next.js واحد فقط
function startNext() {
  if (nextProcess) {
    console.log('⚠️ Next.js يعمل بالفعل');
    return;
  }

  console.log('🔄 بدء Next.js...');
  
  nextProcess = spawn('npx', ['next', 'start', '-p', '3000'], {
    cwd: __dirname,
    stdio: 'inherit'
  });

  nextProcess.on('error', (error) => {
    console.error('❌ خطأ في Next.js:', error.message);
    nextProcess = null;
  });

  nextProcess.on('exit', (code) => {
    console.log(`⚠️ Next.js توقف بالكود: ${code}`);
    nextProcess = null;
    
    // إعادة تشغيل تلقائي
    setTimeout(startNext, 5000);
  });
}

// دالة إنشاء خادم بسيط
function createServer(port, config) {
  const server = http.createServer((req, res) => {
    // إعداد CORS
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', '*');
    res.setHeader('Access-Control-Allow-Headers', '*');
    
    if (req.method === 'OPTIONS') {
      res.writeHead(200);
      res.end();
      return;
    }

    // توجيه بسيط إلى Next.js
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: req.url,
      method: req.method,
      headers: {
        ...req.headers,
        'x-forwarded-port': port,
        'x-database': config.db,
        'x-company': config.name
      }
    };

    const proxyReq = http.request(options, (proxyRes) => {
      res.writeHead(proxyRes.statusCode, proxyRes.headers);
      proxyRes.pipe(res);
    });

    proxyReq.on('error', (error) => {
      console.log(`❌ خطأ في التوجيه ${port}:`, error.message);
      res.writeHead(502, { 'Content-Type': 'text/html; charset=utf-8' });
      res.end(`
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
          <meta charset="utf-8">
          <title>خطأ في الخادم</title>
          <style>
            body { font-family: Arial; text-align: center; padding: 50px; background: #f5f5f5; }
            .error { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #e74c3c; }
            .info { color: #666; margin-top: 20px; }
          </style>
        </head>
        <body>
          <div class="error">
            <h1>🔧 الخادم قيد الصيانة</h1>
            <p>نظام ${config.name} غير متاح حالياً</p>
            <p>يرجى المحاولة بعد قليل</p>
            <div class="info">
              <small>المنفذ: ${port} | قاعدة البيانات: ${config.db}</small><br>
              <small>الوقت: ${new Date().toLocaleString('ar-SA')}</small>
            </div>
          </div>
        </body>
        </html>
      `);
    });

    req.pipe(proxyReq);
  });

  server.listen(port, '0.0.0.0', () => {
    console.log(`✅ خادم ${config.name} يعمل على المنفذ ${port}`);
    console.log(`   🌐 محلي: http://localhost:${port}`);
    console.log(`   🌍 خارجي: https://mohammi.com:${port}`);
  });

  server.on('error', (error) => {
    console.error(`❌ خطأ في المنفذ ${port}:`, error.message);
  });

  return server;
}

// دالة بناء Next.js
async function buildNext() {
  return new Promise((resolve, reject) => {
    console.log('🔨 بناء Next.js...');
    
    const buildProcess = spawn('npx', ['next', 'build'], {
      cwd: __dirname,
      stdio: 'inherit'
    });

    buildProcess.on('close', (code) => {
      if (code === 0) {
        console.log('✅ تم بناء Next.js بنجاح');
        resolve();
      } else {
        console.error('❌ فشل في بناء Next.js');
        reject(new Error(`Build failed with code ${code}`));
      }
    });
  });
}

// دالة البدء الرئيسية
async function start() {
  try {
    console.log('📋 فحص الملفات...');
    
    // فحص package.json
    if (!fs.existsSync('package.json')) {
      console.error('❌ package.json غير موجود');
      process.exit(1);
    }

    // بناء Next.js
    await buildNext();

    // بدء Next.js
    startNext();

    // انتظار قليل لبدء Next.js
    await new Promise(resolve => setTimeout(resolve, 10000));

    // بدء الخوادم
    Object.entries(PORTS).forEach(([port, config]) => {
      const server = createServer(parseInt(port), config);
      servers.set(port, server);
    });

    console.log('\n🎉 جميع الخوادم تعمل بنجاح!');
    console.log('\n📊 الملخص:');
    console.log('   - Next.js: المنفذ 3000');
    console.log('   - محمد: المنفذ 7443 (https://mohammi.com:7443)');
    console.log('   - الربعي: المنفذ 8914 (https://mohammi.com:8914)');
    console.log('\n✨ الخادم جاهز للاستخدام!');

  } catch (error) {
    console.error('❌ فشل في بدء الخادم:', error.message);
    process.exit(1);
  }
}

// معالجة إيقاف الخادم
process.on('SIGINT', () => {
  console.log('\n🛑 إيقاف الخادم...');
  
  if (nextProcess) {
    nextProcess.kill();
  }
  
  servers.forEach((server, port) => {
    server.close();
    console.log(`✅ تم إيقاف المنفذ ${port}`);
  });
  
  process.exit(0);
});

// بدء الخادم
start();
