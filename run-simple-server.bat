@echo off
chcp 65001 >nul
echo.
echo 🚀 الخادم البسيط - حل نهائي
echo ============================
echo.

REM إيقاف جميع عمليات Node.js
echo 🛑 إيقاف العمليات السابقة...
taskkill /f /im node.exe >nul 2>&1

REM انتظار قصير
timeout /t 3 /nobreak >nul

REM الانتقال للمجلد
cd /d "D:\mohammi"

REM تشغيل الخادم البسيط
echo ✅ بدء الخادم البسيط...
echo.
echo 📋 سيتم تشغيل:
echo    - Next.js على المنفذ 3000
echo    - خادم محمد على المنفذ 7443
echo    - خادم الربعي على المنفذ 8914
echo.
echo ⏳ انتظر حتى ظهور رسالة "الخادم جاهز للاستخدام"...
echo.

node simple-production-server.js

pause
