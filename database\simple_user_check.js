// فحص بسيط للمستخدمين
const { Client } = require('pg');

async function checkUsers() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'moham<PERSON>',
    user: 'postgres',
    password: 'yemen123'
  });
  
  try {
    await client.connect();
    console.log('متصل بقاعدة البيانات');

    // فحص المستخدمين
    const result = await client.query('SELECT * FROM users');
    
    console.log('\nالمستخدمين الموجودين:');
    console.log('العدد:', result.rowCount);
    
    if (result.rows.length > 0) {
      result.rows.forEach((user, i) => {
        console.log(`\n${i+1}. اسم المستخدم: ${user.username}`);
        console.log(`   البريد: ${user.email || 'غير محدد'}`);
        console.log(`   الدور: ${user.role || 'غير محدد'}`);
        console.log(`   كلمة المرور المشفرة: ${user.password_hash ? 'موجودة' : 'غير موجودة'}`);
      });
    }

    // إنشاء admin إذا لم يكن موجود
    const adminExists = result.rows.find(u => u.username === 'admin' || u.role === 'admin');
    
    if (!adminExists) {
      console.log('\nإنشاء مستخدم admin...');
      await client.query(`
        INSERT INTO users (username, password_hash, email, role)
        VALUES ('admin', 'admin123', '<EMAIL>', 'admin')
      `);
      console.log('تم إنشاء admin بنجاح');
      console.log('اسم المستخدم: admin');
      console.log('كلمة المرور: admin123');
    } else {
      console.log('\nمستخدم admin موجود:');
      console.log('اسم المستخدم:', adminExists.username);
      console.log('البريد:', adminExists.email);
      console.log('الدور:', adminExists.role);
      
      // إذا كانت كلمة المرور مشفرة، نحدثها لتكون بسيطة للاختبار
      if (adminExists.password_hash && adminExists.password_hash.startsWith('$')) {
        console.log('\nتحديث كلمة المرور لتكون بسيطة...');
        await client.query(`
          UPDATE users 
          SET password_hash = 'admin123' 
          WHERE username = 'admin'
        `);
        console.log('تم تحديث كلمة المرور إلى: admin123');
      } else {
        console.log('كلمة المرور الحالية: admin123');
      }
    }

  } catch (error) {
    console.error('خطأ:', error.message);
  } finally {
    await client.end();
  }
}

checkUsers();
