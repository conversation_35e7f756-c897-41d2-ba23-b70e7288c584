'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { CheckCircle, AlertCircle, Loader2, Users, Building, Truck } from 'lucide-react'
import MainLayout from '@/components/layout/main-layout'

interface ImplementationResult {
  success: boolean
  message: string
  data?: {
    clientsMainAccountId: number
    employeesMainAccountId: number
    suppliersMainAccountId: number
    clientsLinked: number
    employeesLinked: number
  }
  error?: string
}

export default function ImplementStructurePage() {
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<ImplementationResult | null>(null)

  const handleImplement = async () => {
    setIsLoading(true)
    setResult(null)

    try {
      const response = await fetch('/api/accounting/implement-proper-structure', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const data = await response.json()
      setResult(data)
    } catch (error) {
      setResult({
        success: false,
        message: 'فشل في الاتصال بالخادم',
        error: error instanceof Error ? error.message : 'خطأ غير معروف'
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50">
        <div className="space-y-6 p-6 bg-white min-h-screen">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">تطبيق التصميم المحاسبي الصحيح</h1>
              <p className="text-gray-600 mt-2">
                ربط العملاء والموظفين والموردين بدليل الحسابات كحسابات فرعية
              </p>
            </div>
          </div>

          <div className="grid gap-6">
            {/* شرح التصميم */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Building className="h-6 w-6 mr-3 text-blue-600" />
                  التصميم المحاسبي الصحيح
                </CardTitle>
                <CardDescription>
                  سيتم إنشاء حسابات رئيسية للعملاء والموظفين والموردين، وربط كل عميل/موظف/مورد بحساب فرعي منفصل
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-3 gap-4">
                  <div className="flex items-center p-4 bg-green-50 rounded-lg">
                    <Users className="h-8 w-8 text-green-600 mr-3" />
                    <div>
                      <h3 className="font-semibold text-green-800">العملاء</h3>
                      <p className="text-sm text-green-600">حساب رئيسي: 12010000</p>
                      <p className="text-sm text-green-600">حسابات فرعية: 12010001, 12010002...</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center p-4 bg-blue-50 rounded-lg">
                    <Building className="h-8 w-8 text-blue-600 mr-3" />
                    <div>
                      <h3 className="font-semibold text-blue-800">الموظفين</h3>
                      <p className="text-sm text-blue-600">حساب رئيسي: 21010000</p>
                      <p className="text-sm text-blue-600">حسابات فرعية: 21010001, 21010002...</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center p-4 bg-purple-50 rounded-lg">
                    <Truck className="h-8 w-8 text-purple-600 mr-3" />
                    <div>
                      <h3 className="font-semibold text-purple-800">الموردين</h3>
                      <p className="text-sm text-purple-600">حساب رئيسي: 21020000</p>
                      <p className="text-sm text-purple-600">حسابات فرعية: 21020001, 21020002...</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* زر التطبيق */}
            <Card>
              <CardHeader>
                <CardTitle>تطبيق التصميم</CardTitle>
                <CardDescription>
                  اضغط على الزر أدناه لتطبيق التصميم المحاسبي الصحيح
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button 
                  onClick={handleImplement} 
                  disabled={isLoading}
                  className="w-full md:w-auto"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      جاري التطبيق...
                    </>
                  ) : (
                    'تطبيق التصميم المحاسبي'
                  )}
                </Button>
              </CardContent>
            </Card>

            {/* النتائج */}
            {result && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    {result.success ? (
                      <CheckCircle className="h-6 w-6 mr-3 text-green-600" />
                    ) : (
                      <AlertCircle className="h-6 w-6 mr-3 text-red-600" />
                    )}
                    نتائج التطبيق
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Alert className={result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
                    <AlertDescription className={result.success ? 'text-green-800' : 'text-red-800'}>
                      {result.message}
                    </AlertDescription>
                  </Alert>

                  {result.success && result.data && (
                    <div className="mt-4 grid md:grid-cols-2 gap-4">
                      <div className="p-4 bg-gray-50 rounded-lg">
                        <h4 className="font-semibold mb-2">الحسابات الرئيسية المنشأة:</h4>
                        <ul className="space-y-1 text-sm">
                          <li>• حساب العملاء: {result.data.clientsMainAccountId}</li>
                          <li>• حساب الموظفين: {result.data.employeesMainAccountId}</li>
                          <li>• حساب الموردين: {result.data.suppliersMainAccountId}</li>
                        </ul>
                      </div>
                      
                      <div className="p-4 bg-gray-50 rounded-lg">
                        <h4 className="font-semibold mb-2">الحسابات الفرعية المربوطة:</h4>
                        <ul className="space-y-1 text-sm">
                          <li>• عدد العملاء المربوطين: {result.data.clientsLinked}</li>
                          <li>• عدد الموظفين المربوطين: {result.data.employeesLinked}</li>
                        </ul>
                      </div>
                    </div>
                  )}

                  {!result.success && result.error && (
                    <div className="mt-4 p-4 bg-red-50 rounded-lg">
                      <h4 className="font-semibold text-red-800 mb-2">تفاصيل الخطأ:</h4>
                      <p className="text-sm text-red-600">{result.error}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* الخطوات التالية */}
            {result?.success && (
              <Card>
                <CardHeader>
                  <CardTitle>الخطوات التالية</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <p>✅ تم تطبيق التصميم المحاسبي بنجاح</p>
                    <p>✅ الآن يمكنك الذهاب إلى دليل الحسابات لرؤية التغييرات</p>
                    <p>✅ العملاء والموظفين أصبحوا جزءاً من الدليل المحاسبي</p>
                    <p>✅ لن تظهر بيانات الجداول الثلاثة منفصلة في دليل الحسابات</p>
                  </div>
                  
                  <div className="mt-4">
                    <Button 
                      onClick={() => window.location.href = '/accounting/chart-of-accounts'}
                      variant="outline"
                    >
                      عرض دليل الحسابات
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
