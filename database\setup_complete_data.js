/**
 * إعداد البيانات الكاملة لنظام تتبع القضايا
 */

const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'mohammidev'
};

async function setupCompleteData() {
  const client = new Client(dbConfig);

  try {
    await client.connect();
    console.log('🔗 متصل بقاعدة البيانات');

    // إنشاء جدول الموظفين إذا لم يكن موجوداً
    await client.query(`
      CREATE TABLE IF NOT EXISTS employees (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE,
        phone VARCHAR(50),
        position VARCHAR(100),
        department VARCHAR(100),
        hire_date DATE DEFAULT CURRENT_DATE,
        status VARCHAR(50) DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('✅ تم إنشاء/التحقق من جدول employees');

    // إنشاء جدول العملاء إذا لم يكن موجوداً
    await client.query(`
      CREATE TABLE IF NOT EXISTS clients (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255),
        phone VARCHAR(50),
        address TEXT,
        client_type VARCHAR(50) DEFAULT 'individual',
        status VARCHAR(50) DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('✅ تم إنشاء/التحقق من جدول clients');

    // إضافة موظفين تجريبيين
    try {
      await client.query(`
        INSERT INTO employees (name, email, phone, position, department) VALUES
        ('أحمد محمد الصالح', '<EMAIL>', '966501234567', 'محامي أول', 'القضايا التجارية'),
        ('سارة علي الحسن', '<EMAIL>', '966501234568', 'محامية', 'القضايا المدنية'),
        ('محمد عبدالله القاسم', '<EMAIL>', '966501234569', 'محامي', 'القضايا العمالية'),
        ('فاطمة أحمد الزهراني', '<EMAIL>', '966501234570', 'محامية مساعدة', 'الاستشارات');
      `);
    } catch (error) {
      if (!error.message.includes('duplicate key')) {
        throw error;
      }
    }
    console.log('✅ تم إضافة الموظفين');

    // إضافة عملاء تجريبيين
    try {
      await client.query(`
        INSERT INTO clients (name, email, phone, address, client_type) VALUES
        ('شركة الأمل التجارية', '<EMAIL>', '966112345678', 'الرياض، حي الملك فهد', 'company'),
        ('عبدالرحمن محمد الشامي', '<EMAIL>', '966501111111', 'جدة، حي الروضة', 'individual'),
        ('مؤسسة النور للتجارة', '<EMAIL>', '966112222222', 'الدمام، حي الفيصلية', 'company'),
        ('فاطمة أحمد الحداد', '<EMAIL>', '966503333333', 'مكة، حي العزيزية', 'individual');
      `);
    } catch (error) {
      if (!error.message.includes('duplicate key')) {
        throw error;
      }
    }
    console.log('✅ تم إضافة العملاء');

    // تحديث القضايا لربطها بالعملاء والموظفين
    await client.query(`
      UPDATE cases SET
        client_id = 1,
        assigned_lawyer_id = 1
      WHERE id = 1;

      UPDATE cases SET
        client_id = 2,
        assigned_lawyer_id = 2
      WHERE id = 2;

      UPDATE cases SET
        client_id = 3,
        assigned_lawyer_id = 1
      WHERE id = 3;
    `);
    console.log('✅ تم ربط القضايا بالعملاء والموظفين');

    // إضافة حركات للقضايا
    try {
      await client.query(`
        INSERT INTO case_movements (case_id, movement_type, description, employee_id, priority_level) VALUES
        (1, 'case_created', 'تم إنشاء القضية وتسجيلها في النظام', 1, 'medium'),
        (1, 'document_review', 'مراجعة الوثائق المقدمة من العميل', 1, 'high'),
        (1, 'court_filing', 'تقديم الدعوى للمحكمة المختصة', 1, 'high'),
        (2, 'case_created', 'تم إنشاء قضية عمالية جديدة', 2, 'medium'),
        (2, 'client_meeting', 'اجتماع مع العميل لمناقشة تفاصيل القضية', 2, 'medium'),
        (3, 'case_created', 'تم إنشاء قضية مدنية للتعويض', 1, 'low'),
        (3, 'evidence_collection', 'جمع الأدلة والشهادات', 1, 'medium');
      `);
    } catch (error) {
      if (!error.message.includes('duplicate key')) {
        throw error;
      }
    }
    console.log('✅ تم إضافة حركات القضايا');

    // إضافة جلسات المحكمة
    try {
      await client.query(`
        INSERT INTO court_sessions (case_id, session_date, court_name, session_type, reminder_days, notes) VALUES
        (1, '2025-01-15 10:00:00', 'المحكمة التجارية', 'hearing', 3, 'جلسة أولى للنظر في الدعوى'),
        (1, '2025-01-25 14:00:00', 'المحكمة التجارية', 'follow_up', 3, 'جلسة متابعة'),
        (2, '2025-01-12 09:00:00', 'محكمة العمل', 'hearing', 2, 'جلسة استماع للطرفين'),
        (3, '2025-01-20 11:00:00', 'المحكمة المدنية', 'preliminary', 5, 'جلسة تمهيدية');
      `);
    } catch (error) {
      if (!error.message.includes('duplicate key')) {
        throw error;
      }
    }
    console.log('✅ تم إضافة جلسات المحكمة');

    // إضافة مراحل القضايا
    try {
      await client.query(`
        INSERT INTO case_stages (case_id, stage_name, stage_type, status, start_date, responsible_employee_id, order_index) VALUES
        (1, 'تسجيل القضية', 'initial', 'completed', '2025-01-01', 1, 1),
        (1, 'جمع الأدلة', 'preparation', 'in_progress', '2025-01-03', 1, 2),
        (1, 'المرافعة', 'court', 'pending', NULL, 1, 3),
        (2, 'تسجيل القضية', 'initial', 'completed', '2025-01-02', 2, 1),
        (2, 'دراسة الحالة', 'preparation', 'in_progress', '2025-01-04', 2, 2),
        (3, 'تسجيل القضية', 'initial', 'completed', '2025-01-03', 1, 1),
        (3, 'تقييم الأضرار', 'assessment', 'pending', NULL, 1, 2);
      `);
    } catch (error) {
      if (!error.message.includes('duplicate key')) {
        throw error;
      }
    }
    console.log('✅ تم إضافة مراحل القضايا');

    // تحديث تواريخ آخر تحديث للقضايا
    await client.query(`
      UPDATE cases SET updated_at = CURRENT_TIMESTAMP WHERE id IN (1, 2, 3);
    `);
    console.log('✅ تم تحديث تواريخ القضايا');

    console.log('\n🎉 تم إعداد جميع البيانات بنجاح!');

    // عرض ملخص البيانات
    const summary = await client.query(`
      SELECT
        (SELECT COUNT(*) FROM employees) as total_employees,
        (SELECT COUNT(*) FROM clients) as total_clients,
        (SELECT COUNT(*) FROM cases) as total_cases,
        (SELECT COUNT(*) FROM case_movements) as total_movements,
        (SELECT COUNT(*) FROM court_sessions) as total_sessions,
        (SELECT COUNT(*) FROM case_stages) as total_stages
    `);

    console.log('\n📊 ملخص البيانات:');
    console.log(`   - الموظفين: ${summary.rows[0].total_employees}`);
    console.log(`   - العملاء: ${summary.rows[0].total_clients}`);
    console.log(`   - القضايا: ${summary.rows[0].total_cases}`);
    console.log(`   - الحركات: ${summary.rows[0].total_movements}`);
    console.log(`   - الجلسات: ${summary.rows[0].total_sessions}`);
    console.log(`   - المراحل: ${summary.rows[0].total_stages}`);

  } catch (error) {
    console.error('❌ خطأ في إعداد البيانات:', error.message);
    throw error;
  } finally {
    await client.end();
  }
}

// تشغيل الدالة
if (require.main === module) {
  setupCompleteData()
    .then(() => {
      console.log('✅ اكتمل إعداد البيانات');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ فشل في إعداد البيانات:', error);
      process.exit(1);
    });
}

module.exports = { setupCompleteData };
