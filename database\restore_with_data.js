// استعادة قاعدة البيانات مع البيانات الأصلية
const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

async function restoreWithData() {
  console.log('🔄 بدء استعادة قاعدة البيانات مع البيانات الأصلية...');
  
  try {
    // 1. قراءة ملف النسخة الاحتياطية
    const backupFile = path.join(__dirname, '..', 'mohammi.sql');
    let sqlContent = fs.readFileSync(backupFile, 'utf8');
    
    console.log('✅ تم قراءة ملف النسخة الاحتياطية');
    console.log(`📊 حجم الملف: ${(sqlContent.length / 1024 / 1024).toFixed(2)} MB`);

    // 2. تنظيف المحتوى وإزالة الأوامر الإدارية فقط
    console.log('🧹 تنظيف محتوى الملف...');
    
    // إزالة الأوامر الإدارية التي تسبب مشاكل
    sqlContent = sqlContent.replace(/^SET [^;]*;$/gm, '');
    sqlContent = sqlContent.replace(/^SELECT pg_catalog[^;]*;$/gm, '');
    sqlContent = sqlContent.replace(/^DROP DATABASE[^;]*;$/gm, '');
    sqlContent = sqlContent.replace(/^CREATE DATABASE[^;]*;$/gm, '');
    sqlContent = sqlContent.replace(/^ALTER DATABASE[^;]*;$/gm, '');
    sqlContent = sqlContent.replace(/^\\connect[^\n]*$/gm, '');
    sqlContent = sqlContent.replace(/^--[^\n]*$/gm, '');
    sqlContent = sqlContent.replace(/LOCALE_PROVIDER = libc LOCALE = '[^']*'/g, '');
    
    // إزالة التعليقات والأسطر الفارغة
    const lines = sqlContent.split('\n');
    const cleanLines = lines.filter(line => {
      const trimmed = line.trim();
      return trimmed.length > 0 && 
             !trimmed.startsWith('--') && 
             !trimmed.startsWith('/*') && 
             !trimmed.includes('TOC entry') &&
             !trimmed.match(/^Name: .+; Type: .+; Schema: .+; Owner:/);
    });
    
    sqlContent = cleanLines.join('\n');
    
    console.log('✅ تم تنظيف المحتوى');

    // 3. إعادة إنشاء قاعدة البيانات
    const adminClient = new Client({
      host: 'localhost',
      port: 5432,
      database: 'postgres',
      user: 'postgres',
      password: 'yemen123'
    });
    
    await adminClient.connect();
    console.log('✅ متصل بقاعدة postgres');
    
    // إنهاء الاتصالات الحالية
    try {
      await adminClient.query(`
        SELECT pg_terminate_backend(pid)
        FROM pg_stat_activity
        WHERE datname = 'mohammi' AND pid <> pg_backend_pid()
      `);
    } catch (error) {
      // تجاهل الأخطاء
    }
    
    // حذف وإنشاء قاعدة البيانات
    try {
      await adminClient.query('DROP DATABASE IF EXISTS mohammi');
    } catch (error) {
      // تجاهل الأخطاء
    }
    
    await adminClient.query(`CREATE DATABASE mohammi WITH ENCODING = 'UTF8'`);
    console.log('✅ تم إنشاء قاعدة البيانات');
    
    await adminClient.end();

    // 4. الاتصال بقاعدة mohammi وتنفيذ المحتوى
    const client = new Client({
      host: 'localhost',
      port: 5432,
      database: 'mohammi',
      user: 'postgres',
      password: 'yemen123'
    });
    
    await client.connect();
    console.log('✅ متصل بقاعدة mohammi');

    // 5. تقسيم المحتوى إلى أوامر منفصلة
    console.log('📋 تحليل أوامر SQL...');
    
    // تقسيم بناءً على الفاصلة المنقوطة مع مراعاة الأوامر المتعددة الأسطر
    const statements = [];
    let currentStatement = '';
    let inQuotes = false;
    let quoteChar = '';
    
    for (let i = 0; i < sqlContent.length; i++) {
      const char = sqlContent[i];
      const prevChar = i > 0 ? sqlContent[i - 1] : '';
      
      if ((char === "'" || char === '"') && prevChar !== '\\') {
        if (!inQuotes) {
          inQuotes = true;
          quoteChar = char;
        } else if (char === quoteChar) {
          inQuotes = false;
          quoteChar = '';
        }
      }
      
      currentStatement += char;
      
      if (char === ';' && !inQuotes) {
        const statement = currentStatement.trim();
        if (statement.length > 1) {
          statements.push(statement);
        }
        currentStatement = '';
      }
    }
    
    console.log(`📊 تم العثور على ${statements.length} أمر SQL`);

    // 6. تنفيذ الأوامر
    console.log('⚡ تنفيذ أوامر SQL...');
    
    let successCount = 0;
    let errorCount = 0;
    let createTableCount = 0;
    let insertCount = 0;
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      try {
        await client.query(statement);
        successCount++;
        
        // تصنيف نوع الأمر
        if (statement.toUpperCase().includes('CREATE TABLE')) {
          createTableCount++;
        } else if (statement.toUpperCase().includes('INSERT INTO')) {
          insertCount++;
        }
        
        // عرض التقدم كل 100 أمر
        if (i % 100 === 0) {
          console.log(`📈 تم تنفيذ ${i + 1}/${statements.length} أمر`);
        }
        
      } catch (error) {
        errorCount++;
        
        // عرض الأخطاء المهمة فقط
        if (!error.message.includes('already exists') && 
            !error.message.includes('duplicate key') &&
            !error.message.includes('does not exist')) {
          console.log(`⚠️ خطأ في الأمر ${i + 1}: ${error.message.substring(0, 100)}`);
        }
      }
    }
    
    console.log(`✅ تم تنفيذ ${successCount} أمر بنجاح`);
    console.log(`📋 إنشاء الجداول: ${createTableCount}`);
    console.log(`📥 إدراج البيانات: ${insertCount}`);
    console.log(`⚠️ أخطاء: ${errorCount} (معظمها طبيعي)`);

    // 7. التحقق من النتائج
    console.log('\n🔍 التحقق من النتائج...');
    
    // فحص الجداول
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    console.log(`📊 عدد الجداول: ${tablesResult.rows.length}`);

    // فحص البيانات في الجداول المهمة
    const importantTables = ['companies', 'users', 'clients', 'issues', 'employees', 'services'];
    
    console.log('\n📋 فحص البيانات في الجداول المهمة:');
    for (const tableName of importantTables) {
      try {
        const result = await client.query(`SELECT COUNT(*) as count FROM ${tableName}`);
        console.log(`   📊 ${tableName}: ${result.rows[0].count} سجل`);
      } catch (error) {
        console.log(`   ❌ ${tableName}: غير موجود`);
      }
    }

    // فحص بيانات الشركة
    try {
      const companyResult = await client.query('SELECT name, email, phone FROM companies LIMIT 1');
      if (companyResult.rows.length > 0) {
        const company = companyResult.rows[0];
        console.log('\n🏢 بيانات الشركة:');
        console.log(`   الاسم: ${company.name}`);
        console.log(`   البريد: ${company.email}`);
        console.log(`   الهاتف: ${company.phone}`);
      }
    } catch (error) {
      console.log('\n❌ لا يمكن الوصول لبيانات الشركة');
    }

    // فحص المستخدمين
    try {
      const usersResult = await client.query('SELECT username, email, role FROM users LIMIT 5');
      if (usersResult.rows.length > 0) {
        console.log('\n👥 المستخدمين:');
        usersResult.rows.forEach((user, index) => {
          console.log(`   ${index + 1}. ${user.username} (${user.role}) - ${user.email}`);
        });
      }
    } catch (error) {
      console.log('\n❌ لا يمكن الوصول لبيانات المستخدمين');
    }

    await client.end();
    
    if (tablesResult.rows.length > 0) {
      console.log('\n🎉 تمت استعادة قاعدة البيانات بنجاح!');
      console.log('✅ تم استعادة جميع الجداول والبيانات الأصلية');
      console.log('🔄 يمكنك الآن إعادة تشغيل النظام');
    } else {
      console.log('\n❌ فشلت الاستعادة - لم يتم إنشاء أي جداول');
    }
    
  } catch (error) {
    console.error('❌ خطأ في الاستعادة:', error.message);
  }
}

restoreWithData();
