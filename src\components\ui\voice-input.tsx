'use client'

import { useState, useEffect, useRef } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react'
import { Button } from './button'

interface VoiceInputProps {
  onTranscript: (text: string) => void
  language?: string
  className?: string
}

export function VoiceInput({ onTranscript, language = 'ar-SA', className = '' }: VoiceInputProps) {
  const [isListening, setIsListening] = useState(false)
  const [isSupported, setIsSupported] = useState(false)
  const [transcript, setTranscript] = useState('')
  const recognitionRef = useRef<SpeechRecognition | null>(null)

  useEffect(() => {
    // التحقق من دعم المتصفح للتعرف على الصوت
    if (typeof window !== 'undefined') {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
      if (SpeechRecognition) {
        setIsSupported(true)
        
        const recognition = new SpeechRecognition()
        recognition.continuous = true
        recognition.interimResults = true
        recognition.lang = language
        recognition.maxAlternatives = 1
        
        recognition.onstart = () => {
          setIsListening(true)
        }
        
        recognition.onresult = (event) => {
          let finalTranscript = ''
          let interimTranscript = ''

          for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript
            if (event.results[i].isFinal) {
              finalTranscript += transcript
            } else {
              interimTranscript += transcript
            }
          }

          const fullTranscript = finalTranscript || interimTranscript
          setTranscript(fullTranscript)

          if (finalTranscript) {
            // تنظيف النص وإضافة علامات الترقيم
            const cleanedText = finalTranscript.trim()
            if (cleanedText) {
              onTranscript(cleanedText)
            }
          }
        }
        
        recognition.onerror = (event) => {
          console.error('Speech recognition error:', event.error)
          setIsListening(false)
          
          // رسائل خطأ باللغة العربية
          let errorMessage = 'حدث خطأ في التعرف على الصوت'
          switch (event.error) {
            case 'no-speech':
              errorMessage = 'لم يتم اكتشاف صوت. يرجى المحاولة مرة أخرى.'
              break
            case 'audio-capture':
              errorMessage = 'لا يمكن الوصول إلى الميكروفون. يرجى التحقق من الإعدادات.'
              break
            case 'not-allowed':
              errorMessage = 'تم رفض الإذن للوصول إلى الميكروفون.'
              break
            case 'network':
              errorMessage = 'خطأ في الشبكة. يرجى التحقق من الاتصال.'
              break
          }
          
          alert(errorMessage)
        }
        
        recognition.onend = () => {
          setIsListening(false)
          setTranscript('')
        }
        
        recognitionRef.current = recognition
      }
    }
    
    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop()
      }
    }
  }, [language, onTranscript])

  const startListening = () => {
    if (recognitionRef.current && !isListening) {
      try {
        // طلب إذن الوصول للميكروفون أولاً
        navigator.mediaDevices.getUserMedia({ audio: true })
          .then(() => {
            recognitionRef.current?.start()
          })
          .catch((error) => {
            console.error('Microphone access denied:', error)
            alert('يرجى السماح بالوصول إلى الميكروفون لاستخدام ميزة التعرف على الصوت.')
          })
      } catch (error) {
        console.error('Error starting speech recognition:', error)
        alert('حدث خطأ في بدء التعرف على الصوت. يرجى المحاولة مرة أخرى.')
      }
    }
  }

  const stopListening = () => {
    if (recognitionRef.current && isListening) {
      recognitionRef.current.stop()
    }
  }

  const toggleListening = () => {
    if (isListening) {
      stopListening()
    } else {
      startListening()
    }
  }

  if (!isSupported) {
    return null // لا تعرض الزر إذا لم يكن المتصفح يدعم التعرف على الصوت
  }

  return (
    <div className={`relative ${className}`}>
      <Button
        type="button"
        variant={isListening ? "destructive" : "outline"}
        size="sm"
        onClick={toggleListening}
        className={`
          absolute top-2 left-2 z-10 p-2 h-8 w-8 pointer-events-auto
          ${isListening
            ? 'bg-red-500 hover:bg-red-600 text-white animate-pulse border-red-500'
            : 'bg-white hover:bg-gray-50 text-gray-600 border-gray-300 hover:border-blue-400'
          }
          shadow-sm transition-all duration-200 rounded-md
        `}
        title={isListening ? 'إيقاف التسجيل (اضغط لإيقاف)' : 'بدء التسجيل الصوتي (اضغط للبدء)'}
      >
        {isListening ? (
          <Square className="h-3 w-3" />
        ) : (
          <Mic className="h-3 w-3" />
        )}
      </Button>
      
      {isListening && (
        <div className="absolute top-12 left-2 bg-red-500 text-white text-xs px-3 py-1 rounded-full z-20 shadow-lg animate-pulse">
          🎤 جاري الاستماع...
        </div>
      )}

      {transcript && isListening && (
        <div className="absolute top-20 left-2 right-2 bg-blue-50 border border-blue-200 text-blue-800 text-xs p-3 rounded-lg z-20 max-h-24 overflow-y-auto shadow-lg">
          <div className="font-semibold mb-1 text-blue-600">📝 النص المؤقت:</div>
          <div className="leading-relaxed">{transcript}</div>
        </div>
      )}
    </div>
  )
}

// إضافة تعريفات TypeScript للمتصفح
declare global {
  interface Window {
    SpeechRecognition: typeof SpeechRecognition
    webkitSpeechRecognition: typeof SpeechRecognition
  }
}

interface SpeechRecognition extends EventTarget {
  continuous: boolean
  interimResults: boolean
  lang: string
  start(): void
  stop(): void
  abort(): void
  onstart: ((this: SpeechRecognition, ev: Event) => any) | null
  onend: ((this: SpeechRecognition, ev: Event) => any) | null
  onresult: ((this: SpeechRecognition, ev: SpeechRecognitionEvent) => any) | null
  onerror: ((this: SpeechRecognition, ev: SpeechRecognitionErrorEvent) => any) | null
}

interface SpeechRecognitionEvent extends Event {
  resultIndex: number
  results: SpeechRecognitionResultList
}

interface SpeechRecognitionErrorEvent extends Event {
  error: string
  message: string
}

interface SpeechRecognitionResultList {
  length: number
  item(index: number): SpeechRecognitionResult
  [index: number]: SpeechRecognitionResult
}

interface SpeechRecognitionResult {
  length: number
  item(index: number): SpeechRecognitionAlternative
  [index: number]: SpeechRecognitionAlternative
  isFinal: boolean
}

interface SpeechRecognitionAlternative {
  transcript: string
  confidence: number
}

declare var SpeechRecognition: {
  prototype: SpeechRecognition
  new(): SpeechRecognition
}
