import { NextRequest, NextResponse } from 'next/server'
import { Client } from 'pg'

export async function GET() {
  console.log('🔍 اختبار اتصال PostgreSQL...')

  // إعدادات مختلفة للاختبار
  const configs = [
    { host: 'localhost', port: 5432, user: 'postgres', password: 'yemen123', database: 'postgres' },
    { host: 'localhost', port: 5432, user: 'postgres', password: 'postgres', database: 'postgres' },
    { host: 'localhost', port: 5432, user: 'postgres', password: '', database: 'postgres' },
    { host: 'localhost', port: 5432, user: 'postgres', password: 'admin', database: 'postgres' },
    { host: 'localhost', port: 5432, user: 'postgres', password: '123456', database: 'postgres' },
    { host: 'localhost', port: 5432, user: 'postgres', password: 'root', database: 'postgres' },
    { host: 'localhost', port: 5433, user: 'postgres', password: 'yemen123', database: 'postgres' },
    { host: 'localhost', port: 5434, user: 'postgres', password: 'yemen123', database: 'postgres' },
    { host: '127.0.0.1', port: 5432, user: 'postgres', password: 'yemen123', database: 'postgres' },
    { host: 'localhost', port: 5432, user: 'admin', password: 'yemen123', database: 'postgres' },
    { host: 'localhost', port: 5432, user: 'root', password: 'yemen123', database: 'postgres' },
  ]

  const results = []
  let successfulConnection = null
  let detailedError = ''

  for (let i = 0; i < configs.length; i++) {
    const config = configs[i]
    const testResult = {
      config: `${config.user}@${config.host}:${config.port}`,
      password: config.password || 'فارغة',
      status: 'failed',
      error: '',
      databases: [],
      version: '',
      targetDatabases: []
    }

    console.log(`${i + 1}. اختبار: ${config.user}@${config.host}:${config.port}`)
    
    const client = new Client({
      ...config,
      connectTimeoutMillis: 5000
    })

    try {
      await client.connect()
      testResult.status = 'success'
      console.log('   ✅ نجح الاتصال!')
      
      // جلب معلومات الخادم
      try {
        const versionResult = await client.query('SELECT version()')
        testResult.version = versionResult.rows[0].version.split(' ')[1] || 'غير معروف'
      } catch (e) {
        testResult.version = 'لا يمكن تحديده'
      }

      // جلب قائمة قواعد البيانات
      try {
        const dbResult = await client.query(`
          SELECT datname, pg_size_pretty(pg_database_size(datname)) as size
          FROM pg_database 
          WHERE datistemplate = false 
          ORDER BY datname
        `)
        
        testResult.databases = dbResult.rows.map(row => ({
          name: row.datname,
          size: row.size
        }))

        // البحث عن قواعد البيانات المطلوبة
        const targetDatabases = ['mohammi', 'mohammidev']
        
        for (const targetDb of targetDatabases) {
          const found = dbResult.rows.find(row => row.datname === targetDb)
          if (found) {
            testResult.targetDatabases.push(targetDb)
            
            // فحص الجداول في قاعدة البيانات
            const dbClient = new Client({
              ...config,
              database: targetDb,
              connectTimeoutMillis: 5000
            })

            try {
              await dbClient.connect()
              
              const tablesResult = await dbClient.query(`
                SELECT table_name
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                ORDER BY table_name
              `)

              const tables = tablesResult.rows.map(row => row.table_name)
              
              // فحص الجداول المهمة
              const importantTables = ['issues', 'case_movements', 'clients', 'employees', 'users', 'documents']
              const foundImportantTables = []
              
              for (const tableName of importantTables) {
                if (tables.includes(tableName)) {
                  try {
                    const countResult = await dbClient.query(`SELECT COUNT(*) FROM ${tableName}`)
                    foundImportantTables.push({
                      name: tableName,
                      count: parseInt(countResult.rows[0].count)
                    })
                  } catch (e) {
                    foundImportantTables.push({
                      name: tableName,
                      count: 'خطأ في العد'
                    })
                  }
                }
              }
              
              testResult.targetDatabases = testResult.targetDatabases.map(db => 
                db === targetDb ? {
                  name: db,
                  tables: tables.length,
                  importantTables: foundImportantTables
                } : db
              )
              
              await dbClient.end()
            } catch (dbError) {
              console.log(`خطأ في فحص قاعدة البيانات ${targetDb}:`, dbError.message)
            }
          }
        }
        
      } catch (e) {
        testResult.error = `لا يمكن جلب قائمة قواعد البيانات: ${e.message}`
      }

      await client.end()
      
      if (!successfulConnection) {
        successfulConnection = {
          ...config,
          databases: testResult.databases,
          targetDatabases: testResult.targetDatabases,
          version: testResult.version
        }
      }
      
      break // توقف عند أول اتصال ناجح
      
    } catch (error) {
      testResult.error = error.message
      console.log(`   ❌ فشل: ${error.message}`)

      // حفظ تفاصيل الخطأ للتحليل
      if (!detailedError) {
        detailedError = error.message
      }

      // تحليل نوع الخطأ
      if (error.message.includes('ECONNREFUSED')) {
        testResult.error += ' (الخادم غير مشغل أو المنفذ مغلق)'
      } else if (error.message.includes('authentication failed')) {
        testResult.error += ' (كلمة المرور أو اسم المستخدم خاطئ)'
      } else if (error.message.includes('timeout')) {
        testResult.error += ' (انتهت مهلة الاتصال)'
      } else if (error.message.includes('ENOTFOUND')) {
        testResult.error += ' (لا يمكن الوصول للمضيف)'
      }
    }
    
    results.push(testResult)
  }

  // تحليل مفصل للمشكلة
  let problemAnalysis = ''
  if (!successfulConnection && detailedError) {
    if (detailedError.includes('ECONNREFUSED')) {
      problemAnalysis = 'PostgreSQL غير مشغل على المنفذ 5432. الخادم متوقف أو غير مثبت.'
    } else if (detailedError.includes('authentication failed')) {
      problemAnalysis = 'PostgreSQL يعمل ولكن كلمة المرور خاطئة. جرب كلمات مرور أخرى.'
    } else if (detailedError.includes('ENOTFOUND')) {
      problemAnalysis = 'مشكلة في الشبكة المحلية. تحقق من إعدادات localhost.'
    } else if (detailedError.includes('timeout')) {
      problemAnalysis = 'PostgreSQL بطيء في الاستجابة. قد يكون محمل بشدة.'
    } else {
      problemAnalysis = `خطأ غير معروف: ${detailedError}`
    }
  }

  // إعداد الاستجابة
  const response = {
    success: !!successfulConnection,
    message: successfulConnection ? 'تم العثور على اتصال صحيح' : 'لم يتم العثور على أي اتصال صحيح',
    problemAnalysis,
    detailedError,
    testResults: results,
    recommendedConnection: successfulConnection,
    solutions: successfulConnection ? [
      'سيتم تحديث ملف routing.config.json بالإعدادات الصحيحة',
      'سيتم إنشاء جدول case_movements في قاعدة البيانات المناسبة',
      'سيتم إعادة تشغيل الخادم'
    ] : [
      'تحقق من تثبيت PostgreSQL من Control Panel > Programs',
      'تحقق من تشغيل خدمة PostgreSQL في Services (services.msc)',
      'جرب إعادة تشغيل خدمة PostgreSQL: net start postgresql-x64-XX',
      'تحقق من إعدادات الجدار الناري للمنفذ 5432',
      'تحقق من ملف postgresql.conf للاستماع على localhost',
      'تحقق من ملف pg_hba.conf للصلاحيات',
      'إذا لم يكن مثبت، حمل من: https://www.postgresql.org/download/'
    ]
  }

  return NextResponse.json(response)
}
