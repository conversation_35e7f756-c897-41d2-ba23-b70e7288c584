'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

export default function PublicHomePage() {
  const router = useRouter()

  useEffect(() => {
    // توجيه فوري إلى الصفحة الرئيسية المحسنة
    router.replace('/')
  }, [router])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">جاري التوجيه إلى الموقع الرئيسي...</p>
      </div>
    </div>
  )
}