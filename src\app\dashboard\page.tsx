'use client'

import { MainLayout } from '@/components/layout/main-layout'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { useAuth } from '@/hooks/useAuth'
import {
  Scale,
  Users,
  DollarSign,
  CheckCircle,
  Activity,
  FileText,
  Target
} from 'lucide-react'

function DashboardContent() {
  const { user } = useAuth()

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* الإحصائيات الرئيسية */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-6">
            {/* إجمالي القضايا */}
            <div className="text-center group cursor-pointer">
              <div className="relative inline-flex items-center justify-center w-32 h-32 mx-auto mb-4 transition-transform duration-300 group-hover:scale-105">
                <div className="absolute inset-0 rounded-full border-2 border-blue-200 shadow-sm group-hover:border-blue-300 group-hover:shadow-md transition-all duration-300"></div>
                <div className="absolute inset-2 rounded-full bg-blue-50 group-hover:bg-blue-100 transition-colors duration-300"></div>
                <div className="relative flex flex-col items-center justify-center">
                  <Scale className="h-8 w-8 text-blue-600 mb-2 group-hover:text-blue-700 transition-colors duration-300" />
                  <div className="text-2xl font-bold text-blue-600 group-hover:text-blue-700 transition-colors duration-300">156</div>
                </div>
              </div>
              <div className="text-lg font-semibold text-gray-800 group-hover:text-gray-900 transition-colors duration-300">إجمالي القضايا</div>
              <div className="text-sm text-gray-500 mt-1">+12% من الشهر الماضي</div>
            </div>

            {/* الموكلين النشطين */}
            <div className="text-center group cursor-pointer">
              <div className="relative inline-flex items-center justify-center w-32 h-32 mx-auto mb-4 transition-transform duration-300 group-hover:scale-105">
                <div className="absolute inset-0 rounded-full border-2 border-green-200 shadow-sm group-hover:border-green-300 group-hover:shadow-md transition-all duration-300"></div>
                <div className="absolute inset-2 rounded-full bg-green-50 group-hover:bg-green-100 transition-colors duration-300"></div>
                <div className="relative flex flex-col items-center justify-center">
                  <Users className="h-8 w-8 text-green-600 mb-2 group-hover:text-green-700 transition-colors duration-300" />
                  <div className="text-2xl font-bold text-green-600 group-hover:text-green-700 transition-colors duration-300">89</div>
                </div>
              </div>
              <div className="text-lg font-semibold text-gray-800 group-hover:text-gray-900 transition-colors duration-300">الموكلين النشطين</div>
              <div className="text-sm text-gray-500 mt-1">+8% من الشهر الماضي</div>
            </div>

            {/* إجمالي المعاملات المالية */}
            <div className="text-center group cursor-pointer">
              <div className="relative inline-flex items-center justify-center w-32 h-32 mx-auto mb-4 transition-transform duration-300 group-hover:scale-105">
                <div className="absolute inset-0 rounded-full border-2 border-purple-200 shadow-sm group-hover:border-purple-300 group-hover:shadow-md transition-all duration-300"></div>
                <div className="absolute inset-2 rounded-full bg-purple-50 group-hover:bg-purple-100 transition-colors duration-300"></div>
                <div className="relative flex flex-col items-center justify-center">
                  <DollarSign className="h-8 w-8 text-purple-600 mb-2 group-hover:text-purple-700 transition-colors duration-300" />
                  <div className="text-2xl font-bold text-purple-600 group-hover:text-purple-700 transition-colors duration-300">2.3M</div>
                </div>
              </div>
              <div className="text-lg font-semibold text-gray-800 group-hover:text-gray-900 transition-colors duration-300">إجمالي المعاملات المالية</div>
              <div className="text-sm text-gray-500 mt-1">+15% من الشهر الماضي</div>
            </div>
          </div>
          
          {/* إحصائيات إضافية */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 pt-6 border-t border-gray-100">
            {/* القضايا المكتملة */}
            <div className="text-center group cursor-pointer">
              <div className="relative inline-flex items-center justify-center w-24 h-24 mx-auto mb-3 transition-transform duration-300 group-hover:scale-105">
                <div className="absolute inset-0 rounded-full border-2 border-orange-200 shadow-sm group-hover:border-orange-300 group-hover:shadow-md transition-all duration-300"></div>
                <div className="absolute inset-1 rounded-full bg-orange-50 group-hover:bg-orange-100 transition-colors duration-300"></div>
                <div className="relative flex flex-col items-center justify-center">
                  <CheckCircle className="h-6 w-6 text-orange-600 mb-1 group-hover:text-orange-700 transition-colors duration-300" />
                  <div className="text-lg font-bold text-orange-600 group-hover:text-orange-700 transition-colors duration-300">78</div>
                </div>
              </div>
              <div className="text-sm font-semibold text-gray-700 group-hover:text-gray-800 transition-colors duration-300">القضايا المكتملة</div>
              <div className="text-xs text-gray-500">+5%</div>
            </div>

            {/* القضايا الجارية */}
            <div className="text-center group cursor-pointer">
              <div className="relative inline-flex items-center justify-center w-24 h-24 mx-auto mb-3 transition-transform duration-300 group-hover:scale-105">
                <div className="absolute inset-0 rounded-full border-2 border-red-200 shadow-sm group-hover:border-red-300 group-hover:shadow-md transition-all duration-300"></div>
                <div className="absolute inset-1 rounded-full bg-red-50 group-hover:bg-red-100 transition-colors duration-300"></div>
                <div className="relative flex flex-col items-center justify-center">
                  <Activity className="h-6 w-6 text-red-600 mb-1 group-hover:text-red-700 transition-colors duration-300" />
                  <div className="text-lg font-bold text-red-600 group-hover:text-red-700 transition-colors duration-300">23</div>
                </div>
              </div>
              <div className="text-sm font-semibold text-gray-700 group-hover:text-gray-800 transition-colors duration-300">القضايا الجارية</div>
              <div className="text-xs text-gray-500">+3%</div>
            </div>

            {/* المستندات */}
            <div className="text-center group cursor-pointer">
              <div className="relative inline-flex items-center justify-center w-24 h-24 mx-auto mb-3 transition-transform duration-300 group-hover:scale-105">
                <div className="absolute inset-0 rounded-full border-2 border-indigo-200 shadow-sm group-hover:border-indigo-300 group-hover:shadow-md transition-all duration-300"></div>
                <div className="absolute inset-1 rounded-full bg-indigo-50 group-hover:bg-indigo-100 transition-colors duration-300"></div>
                <div className="relative flex flex-col items-center justify-center">
                  <FileText className="h-6 w-6 text-indigo-600 mb-1 group-hover:text-indigo-700 transition-colors duration-300" />
                  <div className="text-lg font-bold text-indigo-600 group-hover:text-indigo-700 transition-colors duration-300">342</div>
                </div>
              </div>
              <div className="text-sm font-semibold text-gray-700 group-hover:text-gray-800 transition-colors duration-300">المستندات</div>
              <div className="text-xs text-gray-500">+18%</div>
            </div>

            {/* معدل النجاح */}
            <div className="text-center group cursor-pointer">
              <div className="relative inline-flex items-center justify-center w-24 h-24 mx-auto mb-3 transition-transform duration-300 group-hover:scale-105">
                <div className="absolute inset-0 rounded-full border-2 border-teal-200 shadow-sm group-hover:border-teal-300 group-hover:shadow-md transition-all duration-300"></div>
                <div className="absolute inset-1 rounded-full bg-teal-50 group-hover:bg-teal-100 transition-colors duration-300"></div>
                <div className="relative flex flex-col items-center justify-center">
                  <Target className="h-6 w-6 text-teal-600 mb-1 group-hover:text-teal-700 transition-colors duration-300" />
                  <div className="text-lg font-bold text-teal-600 group-hover:text-teal-700 transition-colors duration-300">95%</div>
                </div>
              </div>
              <div className="text-sm font-semibold text-gray-700 group-hover:text-gray-800 transition-colors duration-300">معدل النجاح</div>
              <div className="text-xs text-gray-500">+2%</div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}

export default function Dashboard() {
  return (
    <ProtectedRoute userType="user">
      <DashboardContent />
    </ProtectedRoute>
  )
}
