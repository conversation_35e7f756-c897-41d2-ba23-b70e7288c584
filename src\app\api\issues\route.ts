import { NextRequest, NextResponse } from 'next/server'
import { query, getDatabaseName } from '@/lib/database-router'

// GET - جلب جميع القضايا من قاعدة البيانات
export async function GET(request: NextRequest) {
  try {
    console.log('⚖️ API القضايا - جلب البيانات');
    console.log('   قاعدة البيانات:', getDatabaseName());
    console.log('   المنفذ:', process.env.PORT);

    const { searchParams } = new URL(request.url)
    const undistributed = searchParams.get('undistributed') === 'true'

    let queryStr

    if (undistributed) {
      console.log('🔍 جلب القضايا غير الموزعة فقط')
      // جلب القضايا غير الموزعة (التي لا توجد في جدول case_distribution)
      queryStr = `
        SELECT i.* FROM issues i
        LEFT JOIN case_distribution cd ON i.id = cd.issue_id
        WHERE cd.issue_id IS NULL
        ORDER BY i.id DESC
      `
    } else {
      console.log('📋 جلب جميع القضايا')
      // جلب جميع القضايا مع العلاقات الصحيحة والجلسة القادمة والعملة
      queryStr = `
        SELECT
          i.*,
          c.name as client_name,
          c.phone as client_phone,
          it.name as issue_type,
          ct.name as court_name,
          curr.currency_code,
          curr.symbol as currency_symbol,
          curr.exchange_rate,
          i.next_hearing,
          COALESCE(i.amount_yer, i.case_amount, 0) as amount_yer_display
        FROM issues i
        LEFT JOIN clients c ON i.client_id = c.id
        LEFT JOIN issue_types it ON i.issue_type_id = it.id
        LEFT JOIN courts ct ON i.court_id = ct.id
        LEFT JOIN currencies curr ON i.currency_id = curr.id
        ORDER BY i.id DESC
      `
    }

    console.log('Issues API: Query:', queryStr)
    console.log('Issues API: undistributed filter:', undistributed)

    const result = await query(queryStr)

    console.log('Issues API: Found', result.rows.length, 'issues')
    if (undistributed) {
      console.log('🔍 القضايا غير الموزعة:', result.rows.map(i => `${i.id}: ${i.case_number || 'بدون رقم'} - ${i.title || 'بدون عنوان'}`))
    }

    return NextResponse.json({
      success: true,
      data: result.rows,
      message: undistributed ? `تم جلب ${result.rows.length} قضية غير موزعة` : `تم جلب ${result.rows.length} قضية`
    })
  } catch (error) {
    console.error('Error fetching القضايا:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'فشل في جلب بيانات القضايا',
        message: 'تأكد من وجود الجدول في قاعدة البيانات'
      },
      { status: 500 }
    )
  }
}

// POST - إضافة قضية جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    const {
      case_number, title, description, client_id, issue_type_id, court_id,
      status, amount, notes, contract_method, contract_date
    } = body

    console.log('POST Issues API: Received data:', {
      case_number, title, client_id, issue_type_id, court_id, status, amount
    })

    // التحقق من البيانات المطلوبة
    if (!case_number || !title || !client_id) {
      return NextResponse.json(
        { success: false, error: 'رقم القضية والعنوان ومعرف الموكل مطلوبان' },
        { status: 400 }
      )
    }

    // التحقق من عدم تكرار رقم القضية
    const duplicateCheck = await query(
      'SELECT id FROM issues WHERE case_number = $1',
      [case_number]
    )

    if (duplicateCheck.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'رقم القضية موجود مسبقاً' },
        { status: 400 }
      )
    }

    // إدراج القضية الجديدة (الـ triggers ستحدث الأعمدة النصية تلقائياً)
    const result = await query(`
      INSERT INTO issues (
        case_number, title, description, client_id, issue_type_id, court_id,
        status, case_amount, currency_id, notes, contract_method, contract_date, start_date,
        created_date, updated_date
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $12,
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
      ) RETURNING *
    `, [
      case_number, title, description, client_id || null, issue_type_id || null, court_id || null,
      status || 'new', parseFloat(body.case_amount) || 0, body.currency_id || 1, notes || '',
      contract_method || 'بالجلسة', contract_date || new Date().toISOString().split('T')[0]
    ])

    console.log('POST Issues API: Created issue:', result.rows[0])

    return NextResponse.json({
      success: true,
      message: 'تم إضافة القضية بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error creating issue:', error)
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      code: error.code
    })
    return NextResponse.json(
      { success: false, error: `فشل في إضافة القضية: ${error.message}` },
      { status: 500 }
    )
  }
}



