/**
 * API إرسال رسائل WhatsApp البسيطة
 */

import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'
import fs from 'fs'
import path from 'path'

// استيراد خدمة WhatsApp الحقيقية
let whatsappService: any = null

// تحميل الخدمة بشكل آمن
async function getWhatsAppService() {
  if (!whatsappService) {
    try {
      whatsappService = require('../../../../../whatsapp-real-service')
      console.log('✅ تم تحميل خدمة WhatsApp الحقيقية للإرسال')
    } catch (error) {
      console.error('❌ خطأ في تحميل خدمة WhatsApp:', error)
      throw new Error('فشل في تحميل خدمة WhatsApp الحقيقية')
    }
  }
  return whatsappService
}

// تحميل إعدادات التوجيه (مماثل لمسارات التحكم/الحالة)
function loadRoutingConfig(): any | null {
  try {
    const cwd = process.cwd()
    const mainPath = path.join(cwd, 'routing.config.json')
    const prodPath = path.join(cwd, 'production', 'routing.config.json')

    let configPath = ''
    if (fs.existsSync(mainPath)) configPath = mainPath
    else if (fs.existsSync(prodPath)) configPath = prodPath
    else return null

    const content = fs.readFileSync(configPath, 'utf-8')
    return JSON.parse(content)
  } catch (e: any) {
    console.warn('⚠️ تعذّر تحميل routing.config.json:', e?.message)
    return null
  }
}

// تحميل إعدادات WhatsApp الخاصة بكل منفذ (إن وجدت)
function loadWhatsAppConfig(): any | null {
  try {
    const cwd = process.cwd()
    const mainPath = path.join(cwd, 'whatsapp.config.json')
    const prodPath = path.join(cwd, 'production', 'whatsapp.config.json')
    const configPath = fs.existsSync(mainPath) ? mainPath : (fs.existsSync(prodPath) ? prodPath : '')
    if (!configPath) return null
    return JSON.parse(fs.readFileSync(configPath, 'utf-8'))
  } catch (e: any) {
    console.warn('⚠️ تعذّر تحميل whatsapp.config.json:', e?.message)
    return null
  }
}

// تحديد إعداد المسار بناءً على منفذ الطلب
function getRouteForRequest(request: NextRequest) {
  try {
    const xfHost = request.headers.get('x-forwarded-host') || ''
    const xfPortHeader = request.headers.get('x-forwarded-port') || ''

    const host = xfHost || request.headers.get('host') || ''
    const portStr = xfPortHeader || (host.includes(':') ? host.split(':').pop() : '')
    const port = portStr ? parseInt(portStr, 10) : NaN

    const cfg = loadRoutingConfig()
    if (!cfg) return { key: 'default', config: cfg?.default_config || {} }

    const routes = cfg.routes || {}
    const entries = Object.entries(routes) as Array<[string, any]>

    let matched = entries.find(([, v]) => Number(v?.internal_next_port) === port)
    if (matched) {
      return { key: matched[0], config: matched[1], raw: cfg }
    }

    if (portStr && entries.some(([k]) => k === portStr)) {
      matched = entries.find(([k]) => k === portStr) as [string, any] | undefined
      if (matched) {
        return { key: matched[0], config: matched[1], raw: cfg }
      }
    }

    return { key: 'default', config: cfg.default_config || {}, raw: cfg }
  } catch (e) {
    return { key: 'default', config: {} }
  }
}

// POST - إرسال رسالة WhatsApp بسيطة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { phoneNumber, message, recipientName } = body

    // تحديد الجلسة وفق التوجيه لضمان استخدام نفس جلسة الخدمة المتصلة
    const routeInfo = getRouteForRequest(request)
    const waCfg = loadWhatsAppConfig()
    const defaultSession = `whatsapp_${routeInfo?.config?.database || routeInfo?.key || 'default'}`
    const overrideSession = waCfg?.whatsapp_settings?.[routeInfo.key]?.session_name
    const sessionName = overrideSession || defaultSession

    console.log(`📤 إرسال رسالة حقيقية إلى: ${phoneNumber}`)

    // التحقق من البيانات المطلوبة
    if (!phoneNumber || !message) {
      return NextResponse.json({
        success: false,
        error: 'رقم الهاتف والرسالة مطلوبان'
      }, { status: 400 })
    }

    // التحقق من تفعيل WhatsApp
    const companyResult = await query(`
      SELECT id, whatsapp_enabled
      FROM companies
      WHERE is_active = true
      LIMIT 1
    `)

    if (companyResult.rows.length === 0 || !companyResult.rows[0].whatsapp_enabled) {
      return NextResponse.json({
        success: false,
        error: 'خدمة WhatsApp غير مفعلة'
      }, { status: 400 })
    }

    const companyId = companyResult.rows[0].id

    // إرسال الرسالة عبر WhatsApp البسيط
    const service = await getWhatsAppService()
    // ضبط اسم الجلسة قبل أي عملية
    service.sessionName = sessionName

    // تحقق من اتصال الجلسة مع الرجوع إلى ملف الحالة عند الحاجة
    let status = service.getStatus()
    try {
      if (!status || status.status !== 'connected') {
        const dataDir = path.join(process.cwd(), '.whatsapp_real')
        const statusFile = path.join(dataDir, `status-${sessionName}.json`)
        if (fs.existsSync(statusFile)) {
          const raw = fs.readFileSync(statusFile, 'utf-8')
          const fileStatus = JSON.parse(raw)
          if (fileStatus && (fileStatus.status === 'connected' || fileStatus.isReady)) {
            status = fileStatus
          }
        }
      }
    } catch (_e) {}

    if (!status?.isReady && status?.status !== 'connected') {
      return NextResponse.json({
        success: false,
        error: 'WhatsApp غير متصل. يرجى التأكد من الاتصال ثم إعادة المحاولة.'
      }, { status: 400 })
    }

    // محاولة تهيئة/الارتباط بالعميل سريعاً في هذا الـ worker لضمان جاهزية الإرسال
    try {
      // رفع مهلة التهيئة لهذا الطلب إن لزم الأمر (حد أقصى 60 ثانية)
      const desiredTimeout = Math.min(
        Number(process.env.SEND_READY_TIMEOUT_MS || service.connectionTimeoutMs || 30000),
        60000
      )
      if (!service.connectionTimeoutMs || service.connectionTimeoutMs < desiredTimeout) {
        service.connectionTimeoutMs = desiredTimeout
      }
      if (typeof (service as any).refreshSessionPaths === 'function') {
        (service as any).refreshSessionPaths()
      }
      if (typeof (service as any).ensureClientReady === 'function') {
        await (service as any).ensureClientReady(desiredTimeout)
      }
    } catch (_e) {
      // تجاهل، سيتم التعامل داخل sendMessage أيضاً
    }

    const sendResult = await service.sendMessage(phoneNumber, message, recipientName)

    // حفظ الرسالة في قاعدة البيانات
    try {
      const messageResult = await query(`
        INSERT INTO whatsapp_local_messages (
          company_id,
          phone_number,
          recipient_name,
          message_content,
          message_type,
          status,
          whatsapp_message_id,
          error_message,
          sent_at,
          created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, CURRENT_TIMESTAMP)
        RETURNING id
      `, [
        companyId,
        phoneNumber,
        recipientName || 'غير محدد',
        message,
        'text',
        sendResult.success ? 'sent' : 'failed',
        sendResult.messageId || null,
        sendResult.success ? null : sendResult.error,
        sendResult.success ? new Date() : null
      ])

      const messageId = messageResult.rows[0].id

      if (!sendResult.success) {
        return NextResponse.json({
          success: false,
          error: sendResult.error,
          data: {
            messageId,
            phoneNumber,
            status: 'failed'
          }
        }, { status: 400 })
      }

      return NextResponse.json({
        success: true,
        message: 'تم إرسال الرسالة الحقيقية بنجاح',
        data: {
          messageId,
          phoneNumber,
          whatsappMessageId: sendResult.messageId,
          status: 'sent',
          timestamp: sendResult.timestamp
        }
      })

    } catch (dbError) {
      console.error('❌ خطأ في حفظ الرسالة:', dbError)

      // حتى لو فشل حفظ قاعدة البيانات، نعيد نتيجة الإرسال
      if (sendResult.success) {
        return NextResponse.json({
          success: true,
          message: 'تم إرسال الرسالة بنجاح (لكن فشل في الحفظ)',
          data: {
            phoneNumber,
            whatsappMessageId: sendResult.messageId,
            status: 'sent',
            timestamp: sendResult.timestamp
          }
        })
      } else {
        return NextResponse.json({
          success: false,
          error: sendResult.error
        }, { status: 400 })
      }
    }

  } catch (error) {
    console.error('❌ خطأ في إرسال الرسالة:', error)
    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 })
  }
}

// GET - جلب الرسائل المرسلة
export async function GET(request: NextRequest) {
  try {
    const service = await getWhatsAppService()
    const messages = service.getMessages(50)

    return NextResponse.json({
      success: true,
      data: {
        messages,
        count: messages.length
      }
    })
  } catch (error) {
    console.error('❌ خطأ في جلب الرسائل:', error)
    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 })
  }
}
