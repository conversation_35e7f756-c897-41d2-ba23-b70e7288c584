@echo off
chcp 65001 >nul
echo.
echo 📊 حالة الخادم المتقدم
echo ====================
echo.

echo 🔍 فحص المنافذ:
netstat -an | findstr ":7443" | findstr "LISTENING" >nul
if %errorLevel% equ 0 (
    echo ✅ المنفذ 7443: يعمل ^(نظام محمد^)
) else (
    echo ❌ المنفذ 7443: متوقف
)

netstat -an | findstr ":8914" | findstr "LISTENING" >nul
if %errorLevel% equ 0 (
    echo ✅ المنفذ 8914: يعمل ^(نظام الربعي^)
) else (
    echo ❌ المنفذ 8914: متوقف
)

netstat -an | findstr ":3300" | findstr "LISTENING" >nul
if %errorLevel% equ 0 (
    echo ✅ المنفذ 3300: يعمل ^(التطوير^)
) else (
    echo ⚠️ المنفذ 3300: متوقف ^(التطوير^)
)

echo.
echo 🔍 فحص المهمة المجدولة:
schtasks /query /tn AdvancedUnifiedServer 2>nul | findstr "Status" | findstr "Running" >nul
if %errorLevel% equ 0 (
    echo ✅ المهمة المجدولة: تعمل
) else (
    echo ❌ المهمة المجدولة: متوقفة
)

echo.
echo 🔍 فحص عمليات Node.js:
tasklist | findstr "node.exe" | find /c "node.exe" >temp_count.txt
set /p node_count=<temp_count.txt
del temp_count.txt
echo 📊 عدد عمليات Node.js النشطة: %node_count%

echo.
echo 🌐 روابط الوصول:
echo    - نظام محمد ^(الإنتاج^): http://localhost:7443
echo    - نظام الربعي: http://localhost:8914  
echo    - نظام محمد ^(التطوير^): http://localhost:3300
echo.

pause
