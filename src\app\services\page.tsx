'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { LineageSelect } from '@/components/ui/lineage-select'
import { IssueSelect } from '@/components/ui/issue-select'
import {
  Settings,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  X,
  Save,
  DollarSign,
  Package
} from 'lucide-react'

interface Service {
  id: number
  name: string
  lineage_id: number
  lineage_name: string
  created_date: string
}

export default function ServicesPage() {
  const [services, setServices] = useState<Service[]>([])
  const [searchTerm, setSearchTerm] = useState('')

  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalType, setModalType] = useState<'add' | 'edit' | 'view'>('add')
  const [editingService, setEditingService] = useState<Service | null>(null)
  const [dbError, setDbError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [formData, setFormData] = useState({
    name: '',
    lineage_id: ''
  })

  const fetchServices = async () => {
    setIsLoading(true)
    setDbError(null)

    try {
      const response = await fetch('/api/services')
      const result = await response.json()

      if (result.success) {
        setServices(result.data)
      } else {
        setDbError(result.error || 'فشل في جلب بيانات الخدمات')
        setServices([])
      }
    } catch (error) {
      console.error('Network error:', error)
      setDbError('فشل في الاتصال بقاعدة البيانات')
      setServices([])
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchServices()
  }, [])

  const filteredServices = services.filter(service => {
    const matchesSearch = (service.name || '').toLowerCase().includes(searchTerm.toLowerCase())
    return matchesSearch
  })

  const handleDelete = async (id: number) => {
    if (confirm('هل أنت متأكد من حذف هذه الخدمة؟')) {
      try {
        const response = await fetch(`/api/services?id=${id}`, {
          method: 'DELETE'
        })

        const result = await response.json()

        if (result.success) {
          alert('تم حذف الخدمة بنجاح')
          fetchServices()
        } else {
          alert(result.error || 'فشل في حذف الخدمة')
        }
      } catch (error) {
        console.error('Error deleting service:', error)
        alert('حدث خطأ في الاتصال')
      }
    }
  }

  const handleEdit = (service: Service) => {
    setEditingService(service)
    setFormData({
      name: service.name
    })
    setModalType('edit')
    setIsModalOpen(true)
  }

  const handleView = (service: Service) => {
    setEditingService(service)
    setModalType('view')
    setIsModalOpen(true)
  }

  const handleAddNew = () => {
    setEditingService(null)
    setFormData({
      name: ''
    })
    setModalType('add')
    setIsModalOpen(true)
  }



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      if (modalType === 'add') {
        const response = await fetch('/api/services', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(formData)
        })

        const result = await response.json()

        if (result.success) {
          alert('تم إضافة الخدمة بنجاح')
          fetchServices()
        } else {
          alert(result.error || 'فشل في إضافة الخدمة')
          return
        }
      } else if (modalType === 'edit' && editingService) {
        const response = await fetch('/api/services', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ ...formData, id: editingService.id })
        })

        const result = await response.json()

        if (result.success) {
          alert('تم تحديث الخدمة بنجاح')
          fetchServices()
        } else {
          alert(result.error || 'فشل في تحديث الخدمة')
          return
        }
      }

      setIsModalOpen(false)
      setEditingService(null)
    } catch (error) {
      console.error('Error submitting form:', error)
      alert('حدث خطأ في الاتصال')
    }
  }



  const stats = {
    total: services.length
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Settings className="h-8 w-8 mr-3 text-blue-600" />
              إدارة الخدمات
            </h1>
            <p className="text-gray-600 mt-1">إدارة ومتابعة جميع الخدمات القانونية وأسعارها</p>
          </div>

          <Button onClick={handleAddNew} className="bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            إضافة خدمة جديدة
          </Button>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-1 gap-6 max-w-md">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Settings className="h-8 w-8 text-blue-600" />
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">إجمالي الخدمات</dt>
                    <dd className="text-lg font-medium text-gray-900">{stats.total}</dd>
                  </dl>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* فلاتر البحث */}
        <Card>
          <CardContent className="p-4">
            <div className="max-w-md">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في أسماء الخدمات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* عرض رسالة الخطأ */}
        {dbError && (
          <Card>
            <CardContent className="p-4">
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="mr-3">
                    <h3 className="text-sm font-medium text-red-800">خطأ في الاتصال بقاعدة البيانات</h3>
                    <div className="mt-2 text-sm text-red-700">
                      <p>{dbError}</p>
                    </div>
                  </div>
                </div>
                <div className="mt-4">
                  <Button onClick={fetchServices} variant="outline" size="sm" className="bg-white hover:bg-gray-50">
                    إعادة المحاولة
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              قائمة الخدمات ({filteredServices.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {!dbError && !isLoading && (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="text-right p-4 font-semibold text-lg">اسم الخدمة</th>
                      <th className="text-center p-4 font-semibold text-lg">مجموعة النسب المالية</th>
                      <th className="text-center p-4 font-semibold text-lg">تاريخ الإنشاء</th>
                      <th className="text-center p-4 font-semibold text-lg">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredServices.map((service) => (
                      <tr key={service.id} className="border-b hover:bg-gray-50">
                        <td className="p-4">
                          <div className="flex items-center">
                            <div className="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                            <span className="font-medium text-lg">{service.name}</span>
                          </div>
                        </td>
                        <td className="text-center p-4">
                          <Badge className="bg-purple-100 text-purple-800 text-lg px-4 py-2">
                            {service.lineage_name || 'غير محدد'}
                          </Badge>
                        </td>
                        <td className="text-center p-4 text-gray-600">{service.created_date}</td>
                        <td className="text-center p-3">
                          <div className="flex justify-center space-x-2 space-x-reverse">
                            <Button size="sm" variant="outline" onClick={() => handleView(service)}>
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline" onClick={() => handleEdit(service)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDelete(service.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}

            {!dbError && !isLoading && services.length === 0 && (
              <div className="text-center py-8">
                <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد خدمات</h3>
                <p className="text-gray-600">لم يتم العثور على أي خدمات في النظام</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Modal للإضافة/التعديل/المشاهدة */}
        {isModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">
                  {modalType === 'add' && 'إضافة خدمة جديدة'}
                  {modalType === 'edit' && 'تعديل الخدمة'}
                  {modalType === 'view' && 'عرض الخدمة'}
                </h3>
                <Button variant="ghost" size="sm" onClick={() => setIsModalOpen(false)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>

              {modalType === 'view' && editingService ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>رقم الخدمة</Label>
                      <p className="mt-1 p-2 bg-blue-50 rounded text-blue-800 font-bold">#{editingService.id}</p>
                    </div>
                    <div>
                      <Label>اسم الخدمة</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded font-medium">{editingService.name}</p>
                    </div>
                    <div>
                      <Label>مجموعة النسب المالية</Label>
                      <p className="mt-1 p-2 bg-purple-50 rounded text-purple-800 font-medium">
                        {editingService.lineage_name || 'غير محدد'}
                      </p>
                    </div>
                    <div>
                      <Label>تاريخ الإنشاء</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingService.created_date}</p>
                    </div>
                  </div>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <Label htmlFor="name">اسم الخدمة *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({...formData, name: e.target.value})}
                      placeholder="أدخل اسم الخدمة"
                      required
                    />
                  </div>

                  <div className="flex space-x-3 space-x-reverse">
                    <Button type="submit" className="flex-1">
                      <Save className="h-4 w-4 mr-2" />
                      {modalType === 'add' ? 'إضافة' : 'تحديث'}
                    </Button>
                    <Button type="button" variant="outline" onClick={() => setIsModalOpen(false)} className="flex-1">
                      إلغاء
                    </Button>
                  </div>
                </form>
              )}
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}
