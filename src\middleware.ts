import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// الصفحات العامة التي لا تحتاج تسجيل دخول
const publicPaths = [
  '/',
  '/home',
  '/public-home',
  '/login',
  '/client-login',
  '/_next',
  '/favicon.ico',
  '/api/auth/users',
  '/api/users',
  '/api/user-roles',
  '/api/client-portal/auth/simple',
  '/api/company',
  '/api/public-stats',
  '/api/public-announcements',
  '/api/legal-library',
  '/api/settings',
  '/api/departments'
]

// الصفحات المحمية للمستخدمين فقط
const userOnlyPaths = [
  '/dashboard',
  '/follows',
  '/issues',
  '/clients',
  '/users',
  '/accounting',
  '/reports'
]

// الصفحات المحمية للعملاء فقط
const clientOnlyPaths = [
  '/client-portal'
]

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // استخراج معلومات قاعدة البيانات والشركة من رؤوس الطلب
  const databaseName = request.headers.get('X-Database') || ''
  const companyName = request.headers.get('X-Company') || ''
  const themeColor = request.headers.get('X-Theme-Color') || '#1e40af'
  const welcomeMessage = request.headers.get('X-Welcome-Message') || ''
  const notificationPrefix = request.headers.get('X-Notification-Prefix') || ''
  const originalPort = request.headers.get('X-Original-Port') || ''

  // إنشاء استجابة مع إضافة معلومات قاعدة البيانات والشركة
  const response = NextResponse.next()

  // إضافة معلومات قاعدة البيانات والشركة للاستجابة
  response.headers.set('X-Database', databaseName)
  response.headers.set('X-Company', companyName)
  response.headers.set('X-Theme-Color', themeColor)
  response.headers.set('X-Welcome-Message', welcomeMessage)
  response.headers.set('X-Notification-Prefix', notificationPrefix)
  response.headers.set('X-Original-Port', originalPort)

  // تسجيل معلومات قاعدة البيانات في الكوكيز للاستخدام في الواجهة
  response.cookies.set('database', databaseName, {
    path: '/',
    httpOnly: false,
    sameSite: 'strict'
  })

  response.cookies.set('company', companyName, {
    path: '/',
    httpOnly: false,
    sameSite: 'strict'
  })

  response.cookies.set('themeColor', themeColor, {
    path: '/',
    httpOnly: false,
    sameSite: 'strict'
  })

  // السماح بالوصول للصفحات العامة
  if (publicPaths.some(path => pathname.startsWith(path))) {
    return response
  }

  // التحقق من وجود جلسة مستخدم
  const userSession = request.cookies.get('userSession')?.value

  if (!userSession) {
    // السماح بالوصول للصفحة الرئيسية العامة بدون تسجيل دخول
    if (pathname === '/') {
      return response
    }

    // للصفحات المحمية، توجيه إلى الدخول المناسب
    if (clientOnlyPaths.some(path => pathname.startsWith(path))) {
      return NextResponse.redirect(new URL('/login', request.url))
    }

    if (userOnlyPaths.some(path => pathname.startsWith(path))) {
      return NextResponse.redirect(new URL('/login', request.url))
    }

    // للصفحات الأخرى، توجيه إلى الدخول العام
    return NextResponse.redirect(new URL('/login', request.url))
  }

  // التحقق من صحة بيانات الجلسة
  try {
    const userData = JSON.parse(userSession)

    // التحقق من البيانات الأساسية
    if (!userData.username || !userData.role || !userData.sessionId) {
      console.warn('بيانات الجلسة غير مكتملة، إجبار تسجيل الخروج')
      const response = NextResponse.redirect(new URL('/login', request.url))
      response.cookies.delete('userSession')
      return response
    }

    // التحقق من أن المستخدم نشط
    if (!userData.is_active || userData.status !== 'active') {
      console.warn('المستخدم غير نشط، إجبار تسجيل الخروج')
      const response = NextResponse.redirect(new URL('/login', request.url))
      response.cookies.delete('userSession')
      return response
    }

    // التحقق من انتهاء صلاحية الجلسة
    if (userData.loginTime) {
      const loginTime = new Date(userData.loginTime)
      const now = new Date()
      const hoursDiff = (now.getTime() - loginTime.getTime()) / (1000 * 60 * 60)

      if (hoursDiff > 24) {
        console.warn('انتهت صلاحية الجلسة، إجبار تسجيل الخروج')
        const response = NextResponse.redirect(new URL('/login', request.url))
        response.cookies.delete('userSession')
        return response
      }
    }

  } catch (error) {
    console.error('خطأ في تحليل بيانات الجلسة:', error)
    const response = NextResponse.redirect(new URL('/login', request.url))
    response.cookies.delete('userSession')
    return response
  }

  try {
    const sessionData = JSON.parse(userSession)

    // إضافة معلومات قاعدة البيانات إلى جلسة المستخدم
    sessionData.database = databaseName
    sessionData.company = companyName
    sessionData.themeColor = themeColor

    // تحديث كوكي الجلسة
    response.cookies.set('userSession', JSON.stringify(sessionData), {
      path: '/',
      httpOnly: true,
      sameSite: 'strict'
    })

    // التحقق من نوع المستخدم وتوجيهه للصفحات المناسبة
    if (sessionData.type === 'client') {
      // العملاء يمكنهم الوصول فقط لبوابة العملاء
      if (userOnlyPaths.some(path => pathname.startsWith(path))) {
        return NextResponse.redirect(new URL('/client-portal', request.url))
      }

      // السماح للعميل بالوصول للصفحة الرئيسية العامة
      if (pathname === '/') {
        return response
      }
    } else if (sessionData.type === 'user') {
      // المستخدمون لا يمكنهم الوصول لبوابة العملاء
      if (clientOnlyPaths.some(path => pathname.startsWith(path))) {
        return NextResponse.redirect(new URL('/dashboard', request.url))
      }

      // السماح للمستخدم بالوصول للصفحة الرئيسية العامة
      if (pathname === '/') {
        return response
      }
    }

  } catch (error) {
    // إذا كانت بيانات الجلسة تالفة، حذفها وتوجيه للدخول
    const redirectResponse = NextResponse.redirect(new URL('/login', request.url))
    redirectResponse.cookies.delete('userSession')

    // نقل معلومات قاعدة البيانات إلى الاستجابة الجديدة
    redirectResponse.headers.set('X-Database', databaseName)
    redirectResponse.headers.set('X-Company', companyName)
    redirectResponse.headers.set('X-Theme-Color', themeColor)
    redirectResponse.headers.set('X-Welcome-Message', welcomeMessage)
    redirectResponse.headers.set('X-Notification-Prefix', notificationPrefix)
    redirectResponse.headers.set('X-Original-Port', originalPort)

    return redirectResponse
  }

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}