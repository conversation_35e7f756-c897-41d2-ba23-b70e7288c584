/**
 * مولد معرف الجهاز التلقائي
 * يولد معرف فريد للجهاز بناءً على خصائص المتصفح والجهاز
 */

'use client'

import { useEffect, useState } from 'react'

interface DeviceInfo {
  deviceId: string
  fingerprint: string
  userAgent: string
  screen: string
  timezone: string
  language: string
  platform: string
}

export function generateDeviceId(): string {
  try {
    // جمع معلومات الجهاز والمتصفح
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    ctx?.fillText('Device ID Generator', 10, 10)
    const canvasFingerprint = canvas.toDataURL()

    const deviceInfo = {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      screen: `${screen.width}x${screen.height}x${screen.colorDepth}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      canvas: canvasFingerprint.slice(-50), // آخر 50 حرف من canvas fingerprint
      memory: (navigator as any).deviceMemory || 'unknown',
      cores: navigator.hardwareConcurrency || 'unknown',
      timestamp: Date.now()
    }

    // إنشاء hash من المعلومات
    const infoString = JSON.stringify(deviceInfo)
    let hash = 0
    for (let i = 0; i < infoString.length; i++) {
      const char = infoString.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // تحويل إلى 32bit integer
    }

    // إنشاء معرف فريد
    const deviceId = `DEV_${Math.abs(hash).toString(36)}_${Date.now().toString(36)}`
    
    return deviceId.toUpperCase()

  } catch (error) {
    console.error('خطأ في توليد معرف الجهاز:', error)
    // معرف احتياطي
    return `DEV_FALLBACK_${Date.now().toString(36)}`.toUpperCase()
  }
}

export function getStoredDeviceId(): string | null {
  try {
    return localStorage.getItem('device_id')
  } catch (error) {
    return null
  }
}

export function storeDeviceId(deviceId: string): void {
  try {
    localStorage.setItem('device_id', deviceId)
    localStorage.setItem('device_id_created', new Date().toISOString())
  } catch (error) {
    console.error('خطأ في حفظ معرف الجهاز:', error)
  }
}

export function getOrCreateDeviceId(): string {
  let deviceId = getStoredDeviceId()
  
  if (!deviceId) {
    deviceId = generateDeviceId()
    storeDeviceId(deviceId)
  }
  
  return deviceId
}

export function getDeviceInfo(): DeviceInfo {
  const deviceId = getOrCreateDeviceId()
  
  return {
    deviceId,
    fingerprint: deviceId,
    userAgent: navigator.userAgent,
    screen: `${screen.width}x${screen.height}`,
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    language: navigator.language,
    platform: navigator.platform
  }
}

interface DeviceIdDisplayProps {
  onDeviceIdReady?: (deviceId: string) => void
  showDetails?: boolean
}

export default function DeviceIdDisplay({ onDeviceIdReady, showDetails = false }: DeviceIdDisplayProps) {
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const initDeviceId = () => {
      try {
        const info = getDeviceInfo()
        setDeviceInfo(info)
        onDeviceIdReady?.(info.deviceId)
      } catch (error) {
        console.error('خطأ في تهيئة معرف الجهاز:', error)
      } finally {
        setIsLoading(false)
      }
    }

    // تأخير قصير للتأكد من تحميل DOM
    const timer = setTimeout(initDeviceId, 100)
    return () => clearTimeout(timer)
  }, [onDeviceIdReady])

  if (isLoading) {
    return (
      <div className="flex items-center gap-2 text-sm text-gray-600">
        <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
        جاري توليد معرف الجهاز...
      </div>
    )
  }

  if (!deviceInfo) {
    return (
      <div className="text-sm text-red-600">
        ❌ فشل في توليد معرف الجهاز
      </div>
    )
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <span className="text-sm font-medium text-gray-700">معرف الجهاز:</span>
        <code className="px-2 py-1 bg-gray-100 rounded text-sm font-mono text-blue-600">
          {deviceInfo.deviceId}
        </code>
        <span className="text-xs text-green-600">✅ تم التوليد تلقائياً</span>
      </div>

      {showDetails && (
        <div className="mt-3 p-3 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700 mb-2">تفاصيل الجهاز:</h4>
          <div className="space-y-1 text-xs text-gray-600">
            <div><strong>المنصة:</strong> {deviceInfo.platform}</div>
            <div><strong>الشاشة:</strong> {deviceInfo.screen}</div>
            <div><strong>المنطقة الزمنية:</strong> {deviceInfo.timezone}</div>
            <div><strong>اللغة:</strong> {deviceInfo.language}</div>
            <div><strong>المتصفح:</strong> {deviceInfo.userAgent.split(' ')[0]}</div>
          </div>
        </div>
      )}

      <div className="text-xs text-gray-500">
        💡 يتم توليد معرف الجهاز تلقائياً وحفظه في المتصفح
      </div>
    </div>
  )
}
