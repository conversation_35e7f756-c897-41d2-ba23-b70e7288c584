'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Users,
  UserCheck,
  UserX,
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  Mail,
  Phone,
  Calendar,
  Shield,
  Key,
  Settings,
  CheckCircle,
  XCircle,
  AlertCircle,
  Clock,
  Globe,
  Bell
} from 'lucide-react'

interface ClientAccount {
  id: number
  client_id: number
  username: string
  email: string
  is_active: boolean
  is_verified: boolean
  language: string
  timezone: string
  last_login: string
  login_attempts: number
  locked_until: string
  created_date: string
  updated_at: string
  client_name: string
  client_phone: string
  client_address: string
  notification_preferences: any
}

interface AccountStats {
  total_accounts: number
  active_accounts: number
  verified_accounts: number
  recent_logins: number
}

export default function ClientAccountsPage() {
  const [accounts, setAccounts] = useState<ClientAccount[]>([])
  const [stats, setStats] = useState<AccountStats>({
    total_accounts: 0,
    active_accounts: 0,
    verified_accounts: 0,
    recent_logins: 0
  })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [selectedVerification, setSelectedVerification] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [editingAccount, setEditingAccount] = useState<ClientAccount | null>(null)
  const [clients, setClients] = useState<any[]>([])

  // جلب حسابات العملاء
  const fetchAccounts = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20'
      })

      if (searchTerm) params.append('search', searchTerm)
      if (selectedStatus) params.append('is_active', selectedStatus)
      if (selectedVerification) params.append('is_verified', selectedVerification)

      const response = await fetch(`/api/client-accounts?${params}`)
      const data = await response.json()

      if (data.success) {
        setAccounts(data.data.accounts)
        setStats(data.data.statistics)
      }
    } catch (error) {
      console.error('خطأ في جلب حسابات العملاء:', error)
    } finally {
      setLoading(false)
    }
  }

  // جلب قائمة العملاء
  const fetchClients = async () => {
    try {
      const response = await fetch('/api/clients')
      const data = await response.json()
      if (data.success) {
        // تصفية العملاء الذين ليس لديهم حسابات
        const clientsWithoutAccounts = data.data.filter((client: any) => 
          !accounts.some(account => account.client_id === client.id)
        )
        setClients(clientsWithoutAccounts)
      }
    } catch (error) {
      console.error('خطأ في جلب العملاء:', error)
    }
  }

  useEffect(() => {
    fetchAccounts()
  }, [currentPage, selectedStatus, selectedVerification])

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (searchTerm !== '') {
        fetchAccounts()
      }
    }, 500)

    return () => clearTimeout(delayedSearch)
  }, [searchTerm])

  useEffect(() => {
    if (showCreateModal) {
      fetchClients()
    }
  }, [showCreateModal, accounts])

  // تنسيق التاريخ
  const formatDate = (dateString: string) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // إنشاء حساب جديد
  const createAccount = async (formData: any) => {
    try {
      const response = await fetch('/api/client-accounts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })

      const data = await response.json()
      if (data.success) {
        fetchAccounts()
        setShowCreateModal(false)
        alert('تم إنشاء الحساب بنجاح')
      } else {
        alert(data.error || 'فشل في إنشاء الحساب')
      }
    } catch (error) {
      console.error('خطأ في إنشاء الحساب:', error)
      alert('خطأ في إنشاء الحساب')
    }
  }

  // تحديث حساب
  const updateAccount = async (id: number, updates: any) => {
    try {
      const response = await fetch('/api/client-accounts', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id, ...updates })
      })

      const data = await response.json()
      if (data.success) {
        fetchAccounts()
        alert('تم تحديث الحساب بنجاح')
      } else {
        alert(data.error || 'فشل في تحديث الحساب')
      }
    } catch (error) {
      console.error('خطأ في تحديث الحساب:', error)
      alert('خطأ في تحديث الحساب')
    }
  }

  // حذف حساب
  const deleteAccount = async (id: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا الحساب؟')) return

    try {
      const response = await fetch(`/api/client-accounts?id=${id}`, {
        method: 'DELETE'
      })

      const data = await response.json()
      if (data.success) {
        fetchAccounts()
        alert('تم حذف الحساب بنجاح')
      } else {
        alert(data.error || 'فشل في حذف الحساب')
      }
    } catch (error) {
      console.error('خطأ في حذف الحساب:', error)
      alert('خطأ في حذف الحساب')
    }
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان والأدوات */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة حسابات العملاء</h1>
            <p className="text-gray-600 mt-1">
              إجمالي الحسابات: {stats.total_accounts} • النشطة: {stats.active_accounts} • المحققة: {stats.verified_accounts}
            </p>
          </div>
          <Button 
            onClick={() => setShowCreateModal(true)}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Plus className="h-4 w-4 mr-2" />
            حساب جديد
          </Button>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-600" />
                <div className="mr-4">
                  <div className="text-2xl font-bold">{stats.total_accounts}</div>
                  <div className="text-sm text-gray-600">إجمالي الحسابات</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <UserCheck className="h-8 w-8 text-green-600" />
                <div className="mr-4">
                  <div className="text-2xl font-bold">{stats.active_accounts}</div>
                  <div className="text-sm text-gray-600">الحسابات النشطة</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <Shield className="h-8 w-8 text-purple-600" />
                <div className="mr-4">
                  <div className="text-2xl font-bold">{stats.verified_accounts}</div>
                  <div className="text-sm text-gray-600">الحسابات المحققة</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-orange-600" />
                <div className="mr-4">
                  <div className="text-2xl font-bold">{stats.recent_logins}</div>
                  <div className="text-sm text-gray-600">دخول حديث</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* أدوات البحث والتصفية */}
        <Card>
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في الحسابات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>
              <div>
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">جميع الحالات</option>
                  <option value="true">نشط</option>
                  <option value="false">غير نشط</option>
                </select>
              </div>
              <div>
                <select
                  value={selectedVerification}
                  onChange={(e) => setSelectedVerification(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">جميع حالات التحقق</option>
                  <option value="true">محقق</option>
                  <option value="false">غير محقق</option>
                </select>
              </div>
              <div>
                <Button variant="outline" className="w-full">
                  <Filter className="h-4 w-4 mr-2" />
                  تصفية متقدمة
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* قائمة الحسابات */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              حسابات العملاء ({accounts.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">جاري التحميل...</p>
              </div>
            ) : accounts.length === 0 ? (
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">لا توجد حسابات</p>
              </div>
            ) : (
              <div className="space-y-4">
                {accounts.map((account) => (
                  <div
                    key={account.id}
                    className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <div className="flex items-center gap-2">
                            <UserCheck className="h-6 w-6 text-blue-600" />
                            <div>
                              <h3 className="font-semibold text-lg">{account.client_name}</h3>
                              <p className="text-sm text-gray-600">@{account.username}</p>
                            </div>
                          </div>
                        </div>

                        <div className="flex flex-wrap gap-2 mb-3">
                          <Badge className={account.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                            {account.is_active ? 'نشط' : 'غير نشط'}
                          </Badge>
                          <Badge className={account.is_verified ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800'}>
                            {account.is_verified ? 'محقق' : 'غير محقق'}
                          </Badge>
                          {account.locked_until && new Date(account.locked_until) > new Date() && (
                            <Badge className="bg-red-100 text-red-800">
                              مقفل
                            </Badge>
                          )}
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">البريد الإلكتروني:</span>
                            <span className="font-medium mr-2">{account.email}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">الهاتف:</span>
                            <span className="font-medium mr-2">{account.client_phone || '-'}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">آخر دخول:</span>
                            <span className="font-medium mr-2">{formatDate(account.last_login)}</span>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm mt-2">
                          <div>
                            <span className="text-gray-600">اللغة:</span>
                            <span className="font-medium mr-2">{account.language === 'ar' ? 'العربية' : 'English'}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">المنطقة الزمنية:</span>
                            <span className="font-medium mr-2">{account.timezone}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">تاريخ الإنشاء:</span>
                            <span className="font-medium mr-2">{formatDate(account.created_date)}</span>
                          </div>
                        </div>

                        {account.login_attempts > 0 && (
                          <div className="mt-2 p-2 bg-yellow-50 rounded-md">
                            <div className="flex items-center gap-2">
                              <AlertCircle className="h-4 w-4 text-yellow-600" />
                              <span className="text-sm text-yellow-700">
                                محاولات دخول فاشلة: {account.login_attempts}
                              </span>
                            </div>
                          </div>
                        )}
                      </div>

                      <div className="flex flex-col gap-2">
                        <div className="flex gap-2">
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => updateAccount(account.id, { isActive: !account.is_active })}
                          >
                            {account.is_active ? (
                              <UserX className="h-4 w-4 text-red-600" />
                            ) : (
                              <UserCheck className="h-4 w-4 text-green-600" />
                            )}
                          </Button>
                          
                          {!account.is_verified && (
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => updateAccount(account.id, { isVerified: true })}
                            >
                              <CheckCircle className="h-4 w-4 text-blue-600" />
                            </Button>
                          )}
                          
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => setEditingAccount(account)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                        
                        <div className="flex gap-2">
                          <Button size="sm" variant="outline">
                            <Key className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="outline">
                            <Mail className="h-4 w-4" />
                          </Button>
                          <Button 
                            size="sm" 
                            variant="outline"
                            className="text-red-600 hover:text-red-700"
                            onClick={() => deleteAccount(account.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* نافذة إنشاء حساب جديد */}
        {showCreateModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <h3 className="text-lg font-semibold mb-4">إنشاء حساب عميل جديد</h3>
              
              <form onSubmit={(e) => {
                e.preventDefault()
                const formData = new FormData(e.target as HTMLFormElement)
                const data = {
                  clientId: parseInt(formData.get('clientId') as string),
                  username: formData.get('username') as string,
                  email: formData.get('email') as string,
                  password: formData.get('password') as string,
                  language: formData.get('language') as string,
                  timezone: formData.get('timezone') as string,
                  sendWelcomeEmail: formData.get('sendWelcomeEmail') === 'on'
                }
                createAccount(data)
              }}>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">العميل</label>
                    <select name="clientId" className="w-full p-2 border border-gray-300 rounded-md" required>
                      <option value="">اختر العميل</option>
                      {clients.map((client) => (
                        <option key={client.id} value={client.id}>
                          {client.name} - {client.phone}
                        </option>
                      ))}
                    </select>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">اسم المستخدم</label>
                      <Input name="username" placeholder="أدخل اسم المستخدم" required />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">البريد الإلكتروني</label>
                      <Input name="email" type="email" placeholder="أدخل البريد الإلكتروني" required />
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-2">كلمة المرور</label>
                    <Input name="password" type="password" placeholder="أدخل كلمة المرور" required />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">اللغة</label>
                      <select name="language" className="w-full p-2 border border-gray-300 rounded-md">
                        <option value="ar">العربية</option>
                        <option value="en">English</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">المنطقة الزمنية</label>
                      <select name="timezone" className="w-full p-2 border border-gray-300 rounded-md">
                        <option value="Asia/Riyadh">الرياض</option>
                        <option value="Asia/Dubai">دبي</option>
                        <option value="Asia/Kuwait">الكويت</option>
                      </select>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <input type="checkbox" name="sendWelcomeEmail" id="sendWelcomeEmail" defaultChecked />
                    <label htmlFor="sendWelcomeEmail" className="text-sm">
                      إرسال بريد ترحيبي
                    </label>
                  </div>
                </div>
                
                <div className="flex gap-3 mt-6">
                  <Button type="submit" className="flex-1 bg-blue-600 hover:bg-blue-700">
                    <Plus className="h-4 w-4 mr-2" />
                    إنشاء الحساب
                  </Button>
                  <Button 
                    type="button"
                    variant="outline" 
                    onClick={() => setShowCreateModal(false)}
                    className="flex-1"
                  >
                    إلغاء
                  </Button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* نافذة تعديل الحساب */}
        {editingAccount && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <h3 className="text-lg font-semibold mb-4">تعديل حساب العميل</h3>
              
              <form onSubmit={(e) => {
                e.preventDefault()
                const formData = new FormData(e.target as HTMLFormElement)
                const data = {
                  username: formData.get('username') as string,
                  email: formData.get('email') as string,
                  language: formData.get('language') as string,
                  timezone: formData.get('timezone') as string,
                  isActive: formData.get('isActive') === 'on',
                  isVerified: formData.get('isVerified') === 'on'
                }
                updateAccount(editingAccount.id, data)
                setEditingAccount(null)
              }}>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">اسم المستخدم</label>
                      <Input name="username" defaultValue={editingAccount.username} required />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">البريد الإلكتروني</label>
                      <Input name="email" type="email" defaultValue={editingAccount.email} required />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">اللغة</label>
                      <select name="language" defaultValue={editingAccount.language} className="w-full p-2 border border-gray-300 rounded-md">
                        <option value="ar">العربية</option>
                        <option value="en">English</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">المنطقة الزمنية</label>
                      <select name="timezone" defaultValue={editingAccount.timezone} className="w-full p-2 border border-gray-300 rounded-md">
                        <option value="Asia/Riyadh">الرياض</option>
                        <option value="Asia/Dubai">دبي</option>
                        <option value="Asia/Kuwait">الكويت</option>
                      </select>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center gap-2">
                      <input 
                        type="checkbox" 
                        name="isActive" 
                        id="isActive" 
                        defaultChecked={editingAccount.is_active} 
                      />
                      <label htmlFor="isActive" className="text-sm">
                        حساب نشط
                      </label>
                    </div>
                    <div className="flex items-center gap-2">
                      <input 
                        type="checkbox" 
                        name="isVerified" 
                        id="isVerified" 
                        defaultChecked={editingAccount.is_verified} 
                      />
                      <label htmlFor="isVerified" className="text-sm">
                        حساب محقق
                      </label>
                    </div>
                  </div>
                </div>
                
                <div className="flex gap-3 mt-6">
                  <Button type="submit" className="flex-1 bg-blue-600 hover:bg-blue-700">
                    <Edit className="h-4 w-4 mr-2" />
                    تحديث الحساب
                  </Button>
                  <Button 
                    type="button"
                    variant="outline" 
                    onClick={() => setEditingAccount(null)}
                    className="flex-1"
                  >
                    إلغاء
                  </Button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}