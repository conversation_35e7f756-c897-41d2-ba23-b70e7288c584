import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET /api/accounting/payment-vouchers/[id]
export async function GET(
  _req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id, 10)
    if (isNaN(id)) {
      return NextResponse.json({ success: false, error: 'معرّف غير صحيح' }, { status: 400 })
    }

    // جلب القيد الرئيسي لسند الصرف
    const jeResult = await query(
      `SELECT * FROM journal_entries WHERE id = $1 AND entry_type = 'payment' LIMIT 1`,
      [id]
    )

    if (jeResult.rows.length === 0) {
      return NextResponse.json({ success: false, error: 'السند غير موجود' }, { status: 404 })
    }

    const je = jeResult.rows[0]

    // جلب تفاصيل السطور
    const detailsRes = await query(
      `SELECT * FROM journal_entry_details WHERE journal_entry_id = $1 ORDER BY line_order`,
      [id]
    )

    const details = detailsRes.rows
    const debitDetail = details.find((d: any) => parseFloat(d.debit_amount) > 0)
    const creditDetail = details.find((d: any) => parseFloat(d.credit_amount) > 0)

    // محاولة جلب أسماء إضافية إن توفرت مراجعها
    let case_title: string | null = null
    let case_number: string | null = null
    let court_name: string | null = null
    let service_name: string | null = null

    const issuesId = debitDetail?.issues_id || creditDetail?.issues_id || null
    const serviceId = debitDetail?.service_id || creditDetail?.service_id || null

    if (issuesId) {
      try {
        // نحاول عبر جدول القضايا/القضايا القانونية إن وجد
        const issueRes = await query(
          `SELECT title as case_title, case_number, court_name FROM issues WHERE id = $1 LIMIT 1`,
          [issuesId]
        )
        if (issueRes.rows.length > 0) {
          case_title = issueRes.rows[0].case_title || null
          case_number = issueRes.rows[0].case_number || null
          court_name = issueRes.rows[0].court_name || null
        }
      } catch (_) {
        // تجاهل لو لم يوجد الجدول
      }
    }

    if (serviceId) {
      try {
        const srvRes = await query(
          `SELECT name FROM services WHERE id = $1 LIMIT 1`,
          [serviceId]
        )
        if (srvRes.rows.length > 0) {
          service_name = srvRes.rows[0].name || null
        }
      } catch (_) {
        // تجاهل لو لم يوجد الجدول
      }
    }

    const voucher = {
      id: je.id,
      entry_number: je.entry_number,
      entry_date: je.entry_date,
      amount: parseFloat(je.total_debit || 0),
      description: je.description,
      reference_number: je.reference_number,
      status: je.status,
      beneficiary_name: je.party_name,
      beneficiary_type: je.party_type,
      debit_account_id: debitDetail?.account_id ?? null,
      debit_account_name: debitDetail?.account_name ?? null,
      credit_account_id: creditDetail?.account_id ?? null,
      credit_account_name: creditDetail?.account_name ?? null,
      cost_center_id: debitDetail?.cost_center_id || creditDetail?.cost_center_id || null,
      case_id: issuesId,
      case_title,
      case_number,
      court_name,
      service_id: serviceId,
      service_name,
      details,
    }

    return NextResponse.json({ success: true, voucher })
  } catch (error) {
    console.error('خطأ في جلب سند الصرف (مفرد):', error)
    return NextResponse.json({ success: false, error: 'فشل في جلب السند' }, { status: 500 })
  }
}

// PUT /api/accounting/payment-vouchers/[id]
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id, 10)
    if (isNaN(id)) {
      return NextResponse.json({ success: false, error: 'معرّف غير صحيح' }, { status: 400 })
    }

    const body = await req.json()
    await query('BEGIN')
    const upd = await query(
      `UPDATE journal_entries
         SET entry_date = COALESCE($1, entry_date),
             description = COALESCE($2, description),
             reference_number = COALESCE($3, reference_number)
       WHERE id = $4 AND entry_type = 'payment'`,
      [body.entry_date || null, body.description || null, body.reference_number || null, id]
    )
    await query('COMMIT')

    if (upd.rowCount === 0) {
      return NextResponse.json({ success: false, error: 'السند غير موجود أو ليس من نوع صرف' }, { status: 404 })
    }

    return NextResponse.json({ success: true, message: 'تم تحديث السند بنجاح' })
  } catch (error) {
    try { await query('ROLLBACK') } catch {}
    console.error('خطأ في تحديث سند الصرف:', error)
    return NextResponse.json({ success: false, error: `فشل في تحديث السند: ${(error as any)?.message || 'غير معروف'}` }, { status: 500 })
  }
}

// PATCH /api/accounting/payment-vouchers/[id]  - اعتماد/ترحيل
export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id, 10)
    if (isNaN(id)) {
      return NextResponse.json({ success: false, error: 'معرّف غير صحيح' }, { status: 400 })
    }
    const body = await req.json().catch(() => ({}))
    const status = body?.status || 'approved'

    await query('BEGIN')
    const upd = await query(
      `UPDATE journal_entries SET status = $1 WHERE id = $2 AND entry_type = 'payment'`,
      [status, id]
    )
    await query('COMMIT')

    if (upd.rowCount === 0) {
      return NextResponse.json({ success: false, error: 'السند غير موجود أو ليس من نوع صرف' }, { status: 404 })
    }
    return NextResponse.json({ success: true, message: `تم تحديث حالة السند إلى: ${status}` })
  } catch (error) {
    try { await query('ROLLBACK') } catch {}
    console.error('خطأ في اعتماد سند الصرف:', error)
    return NextResponse.json({ success: false, error: `فشل في اعتماد السند: ${(error as any)?.message || 'غير معروف'}` }, { status: 500 })
  }
}

// DELETE /api/accounting/payment-vouchers/[id]
export async function DELETE(
  _req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id, 10)
    if (isNaN(id)) {
      return NextResponse.json({ success: false, error: 'معرّف غير صحيح' }, { status: 400 })
    }

    const exists = await query(`SELECT 1 FROM journal_entries WHERE id=$1 AND entry_type='payment'`, [id])
    if (exists.rows.length === 0) {
      return NextResponse.json({ success: false, error: 'السند غير موجود' }, { status: 404 })
    }

    await query('BEGIN')
    await query(`DELETE FROM journal_entry_details WHERE journal_entry_id=$1`, [id])
    await query(`DELETE FROM journal_entries WHERE id=$1 AND entry_type='payment'`, [id])
    await query('COMMIT')

    return NextResponse.json({ success: true, message: 'تم حذف سند الصرف وجميع تفاصيله' })
  } catch (error) {
    try { await query('ROLLBACK') } catch {}
    console.error('خطأ في حذف سند الصرف:', error)
    return NextResponse.json({ success: false, error: `فشل في حذف السند: ${(error as any)?.message || 'غير معروف'}` }, { status: 500 })
  }
}
