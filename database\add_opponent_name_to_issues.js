const { Client } = require('pg')

// Databases we may use in this project
// Allow override via env DB_NAMES (comma-separated), e.g. DB_NAMES=mohammidev
const TARGET_DATABASES = (process.env.DB_NAMES
  ? process.env.DB_NAMES.split(',').map(s => s.trim()).filter(Boolean)
  : ['mohammidev', 'mohammi'])

const baseConfig = {
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
}

async function migrateDatabase(dbName) {
  const client = new Client({ ...baseConfig, database: dbName })
  try {
    console.log(`\n=== Connecting to database: ${dbName} ===`)
    await client.connect()

    // Check table presence
    const tableRes = await client.query(
      `SELECT to_regclass('public.issues') as issues`
    )
    if (!tableRes.rows[0] || tableRes.rows[0].issues === null) {
      console.warn(`⚠️  Table 'issues' not found in database '${dbName}'. Skipping.`)
      return
    }

    // Check existing columns
    const colsRes = await client.query(
      `SELECT column_name FROM information_schema.columns WHERE table_name = 'issues'`
    )
    const cols = colsRes.rows.map(r => r.column_name)
    console.log('📋 Existing columns in issues:', cols)

    if (!cols.includes('opponent_name')) {
      console.log('🔧 Adding column opponent_name (VARCHAR(255)) to issues...')
      await client.query(`ALTER TABLE issues ADD COLUMN opponent_name VARCHAR(255)`) // no default, nullable
      console.log('✅ Column opponent_name added successfully')
    } else {
      console.log('ℹ️  Column opponent_name already exists. Skipping.')
    }

    // Show quick summary
    const summary = await client.query(
      `SELECT COUNT(*)::int as count, COUNT(opponent_name)::int as with_opponent FROM issues`
    )
    const { count, with_opponent } = summary.rows[0]
    console.log(`📊 Issues total: ${count}, rows with opponent_name set: ${with_opponent}`)
  } catch (err) {
    console.error(`❌ Error migrating database '${dbName}':`, err.message)
  } finally {
    await client.end().catch(() => {})
    console.log(`🔌 Disconnected from ${dbName}`)
  }
}

async function main() {
  for (const db of TARGET_DATABASES) {
    await migrateDatabase(db)
  }
  console.log('\n🎉 Migration completed for all target databases.')
}

main().catch(err => {
  console.error('Unexpected error:', err)
  process.exit(1)
})
