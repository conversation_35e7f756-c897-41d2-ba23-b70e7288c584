import { query } from '@/lib/db'

export interface NotificationData {
  case_id: number
  recipient_id: number
  recipient_type: 'client' | 'employee'
  template_name: string
  scheduled_for: Date
  variables?: Record<string, any>
  priority?: 'low' | 'normal' | 'high' | 'urgent'
  channels?: string[]
}

export interface NotificationTemplate {
  id: number
  name: string
  title_template: string
  body_template: string
  notification_type: string
  recipient_type: string
  channels: string[]
  priority: string
}

// دالة لجدولة تنبيه جديد
export async function scheduleNotification(data: NotificationData): Promise<number | null> {
  try {
    // جلب قالب التنبيه
    const templateResult = await query(`
      SELECT * FROM notification_templates 
      WHERE name = $1 AND is_active = true
    `, [data.template_name])

    if (templateResult.rows.length === 0) {
      console.error('قالب التنبيه غير موجود:', data.template_name)
      return null
    }

    const template = templateResult.rows[0]

    // جلب بيانات القضية والمستلم
    const caseResult = await query(`
      SELECT 
        i.*,
        c.name as client_name,
        c.phone as client_phone,
        c.email as client_email,
        ct.name as court_name
      FROM issues i
      LEFT JOIN clients c ON i.client_id = c.id
      LEFT JOIN courts ct ON i.court_id = ct.id
      WHERE i.id = $1
    `, [data.case_id])

    if (caseResult.rows.length === 0) {
      console.error('القضية غير موجودة:', data.case_id)
      return null
    }

    const caseData = caseResult.rows[0]

    // جلب بيانات المستلم
    let recipientData: any = {}
    if (data.recipient_type === 'client') {
      const clientResult = await query(`
        SELECT name, phone, email FROM clients WHERE id = $1
      `, [data.recipient_id])
      recipientData = clientResult.rows[0] || {}
    } else if (data.recipient_type === 'employee') {
      const employeeResult = await query(`
        SELECT name, phone, email FROM employees WHERE id = $1
      `, [data.recipient_id])
      recipientData = employeeResult.rows[0] || {}
    }

    // تحضير المتغيرات للقالب
    const variables = {
      case_number: caseData.case_number,
      case_title: caseData.title,
      client_name: caseData.client_name,
      court_name: caseData.court_name,
      hearing_date: caseData.next_hearing,
      recipient_name: recipientData.name,
      ...data.variables
    }

    // معالجة القالب
    const title = processTemplate(template.title_template, variables)
    const body = processTemplate(template.body_template, variables)

    // إدراج التنبيه المجدول
    const result = await query(`
      INSERT INTO scheduled_notifications (
        template_id, case_id, recipient_id, recipient_type,
        recipient_name, recipient_phone, recipient_email,
        title, body, channels, scheduled_for, priority, metadata
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
      RETURNING id
    `, [
      template.id,
      data.case_id,
      data.recipient_id,
      data.recipient_type,
      recipientData.name,
      recipientData.phone,
      recipientData.email,
      title,
      body,
      JSON.stringify(data.channels || template.channels),
      data.scheduled_for,
      data.priority || template.priority,
      JSON.stringify(variables)
    ])

    return result.rows[0].id
  } catch (error) {
    console.error('خطأ في جدولة التنبيه:', error)
    return null
  }
}

// دالة لمعالجة قوالب النصوص
function processTemplate(template: string, variables: Record<string, any>): string {
  let processed = template
  
  for (const [key, value] of Object.entries(variables)) {
    const regex = new RegExp(`{{${key}}}`, 'g')
    processed = processed.replace(regex, String(value || ''))
  }
  
  return processed
}

// دالة لجدولة تنبيهات الجلسات تلقائياً
export async function scheduleHearingNotifications(
  caseId: number,
  hearingDate: Date,
  courtName: string
): Promise<void> {
  try {
    // جلب بيانات القضية والعميل
    const caseResult = await query(`
      SELECT i.*, c.id as client_id
      FROM issues i
      LEFT JOIN clients c ON i.client_id = c.id
      WHERE i.id = $1
    `, [caseId])

    if (caseResult.rows.length === 0) return

    const caseData = caseResult.rows[0]

    // جدولة تنبيه قبل 3 أيام
    const threeDaysBefore = new Date(hearingDate)
    threeDaysBefore.setDate(threeDaysBefore.getDate() - 3)
    threeDaysBefore.setHours(9, 0, 0, 0) // 9 صباحاً

    if (caseData.client_id) {
      await scheduleNotification({
        case_id: caseId,
        recipient_id: caseData.client_id,
        recipient_type: 'client',
        template_name: 'hearing_reminder_3_days',
        scheduled_for: threeDaysBefore,
        variables: {
          hearing_date: hearingDate.toLocaleDateString('ar-SA'),
          court_name: courtName
        },
        priority: 'normal'
      })
    }

    // جدولة تنبيه قبل يوم واحد
    const oneDayBefore = new Date(hearingDate)
    oneDayBefore.setDate(oneDayBefore.getDate() - 1)
    oneDayBefore.setHours(18, 0, 0, 0) // 6 مساءً

    if (caseData.client_id) {
      await scheduleNotification({
        case_id: caseId,
        recipient_id: caseData.client_id,
        recipient_type: 'client',
        template_name: 'hearing_reminder_1_day',
        scheduled_for: oneDayBefore,
        variables: {
          hearing_date: hearingDate.toLocaleDateString('ar-SA'),
          court_name: courtName
        },
        priority: 'high'
      })
    }

    console.log('تم جدولة تنبيهات الجلسة بنجاح')
  } catch (error) {
    console.error('خطأ في جدولة تنبيهات الجلسة:', error)
  }
}

// دالة للحصول على التنبيهات المستحقة للإرسال
export async function getPendingNotifications(limit: number = 100) {
  try {
    const result = await query(`
      SELECT 
        sn.*,
        nt.channels as template_channels
      FROM scheduled_notifications sn
      LEFT JOIN notification_templates nt ON sn.template_id = nt.id
      WHERE sn.status = 'pending' 
        AND sn.scheduled_for <= CURRENT_TIMESTAMP
      ORDER BY sn.priority DESC, sn.scheduled_for ASC
      LIMIT $1
    `, [limit])

    return result.rows
  } catch (error) {
    console.error('خطأ في جلب التنبيهات المستحقة:', error)
    return []
  }
}

// دالة لتحديث حالة التنبيه
export async function updateNotificationStatus(
  notificationId: number,
  status: string,
  errorMessage?: string
): Promise<void> {
  try {
    const updateFields = ['status = $2', 'updated_at = CURRENT_TIMESTAMP']
    const params = [notificationId, status]

    if (status === 'sent') {
      updateFields.push('sent_at = CURRENT_TIMESTAMP')
    } else if (status === 'delivered') {
      updateFields.push('delivered_at = CURRENT_TIMESTAMP')
    } else if (status === 'read') {
      updateFields.push('read_at = CURRENT_TIMESTAMP')
    } else if (status === 'failed') {
      updateFields.push('failed_at = CURRENT_TIMESTAMP')
      if (errorMessage) {
        updateFields.push(`error_message = $${params.length + 1}`)
        params.push(errorMessage)
      }
    }

    await query(`
      UPDATE scheduled_notifications 
      SET ${updateFields.join(', ')}
      WHERE id = $1
    `, params)
  } catch (error) {
    console.error('خطأ في تحديث حالة التنبيه:', error)
  }
}

// دالة لتسجيل إرسال التنبيه
export async function logNotificationSent(
  scheduledNotificationId: number,
  channel: string,
  recipientContact: string,
  status: string,
  responseData?: any,
  errorMessage?: string
): Promise<void> {
  try {
    await query(`
      INSERT INTO notification_logs (
        scheduled_notification_id, channel, recipient_contact,
        status, response_data, error_message, sent_at
      ) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)
    `, [
      scheduledNotificationId,
      channel,
      recipientContact,
      status,
      responseData ? JSON.stringify(responseData) : null,
      errorMessage
    ])
  } catch (error) {
    console.error('خطأ في تسجيل إرسال التنبيه:', error)
  }
}

// دالة للحصول على إحصائيات التنبيهات
export async function getNotificationStats(days: number = 30) {
  try {
    const result = await query(`
      SELECT 
        COUNT(*) as total_notifications,
        COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent_count,
        COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered_count,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
        AVG(CASE WHEN sent_at IS NOT NULL THEN 
          EXTRACT(EPOCH FROM (sent_at - created_at))/60 
        END) as avg_processing_time_minutes
      FROM scheduled_notifications
      WHERE created_at >= CURRENT_DATE - INTERVAL '$1 days'
    `, [days])

    return result.rows[0]
  } catch (error) {
    console.error('خطأ في جلب إحصائيات التنبيهات:', error)
    return null
  }
}

// دالة للحصول على التنبيهات القادمة للمستخدم
export async function getUserUpcomingNotifications(
  userId: number,
  userType: 'client' | 'employee',
  limit: number = 10
) {
  try {
    const result = await query(`
      SELECT 
        sn.*,
        i.case_number,
        i.title as case_title
      FROM scheduled_notifications sn
      LEFT JOIN issues i ON sn.case_id = i.id
      WHERE sn.recipient_id = $1 
        AND sn.recipient_type = $2
        AND sn.status = 'pending'
        AND sn.scheduled_for > CURRENT_TIMESTAMP
      ORDER BY sn.scheduled_for ASC
      LIMIT $3
    `, [userId, userType, limit])

    return result.rows
  } catch (error) {
    console.error('خطأ في جلب التنبيهات القادمة للمستخدم:', error)
    return []
  }
}
