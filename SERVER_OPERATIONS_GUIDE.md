# Server Operations Guide (Production)

This guide documents how the current servers operate, where the main files live, how to update routes/compatibility safely, and how to back up/restore without downtime.

## Overview
- The repo supports two server modes:
  - Advanced Unified Proxy: `advanced-unified-server.js` (HTTP/HTTPS proxy per external port to internal Next.js instance per route)
- Routing and DB mapping are defined in: `production/routing.config.json`.
- PM2 config (optional): `ecosystem.config.json` (script points to `service-runner.js` if used).

## Key Files and Paths
- Server (advanced): `D:\mohaminew\advanced-unified-server.js`
- Routes/DB map: `D:\mohaminew\production\routing.config.json`
- PM2 config (optional): `D:\mohaminew\ecosystem.config.json`
- Env files (per-port or system):
  - `D:\mohaminew\.env.7443`
  - `D:\mohaminew\.env.8914`
  - `D:\mohaminew\.env.mohammi`, `D:\mohaminew\.env.rubaie`
  - `D:\mohaminew\.env.template`, `.env.example`
- SSL (if HTTPS enabled): `D:\mohaminew\ssl\` and `D:\mohaminew\production\ssl\`
- Logs (created by servers): `D:\mohaminew\logs\`
- Reference docs: `README-UNIFIED-SERVER.md`, `README-ADVANCED-SERVER.md`, `DEPLOYMENT_GUIDE*.md`, `SERVER-POLICY.md`

## How The Servers Run
- `advanced-unified-server.js`:
  - Loads `production/routing.config.json`.
  - For each route (external port): optionally starts an internal Next.js on `internal_next_port` (default 3000) and exposes external HTTP/HTTPS proxy on the route's port.
  - Sets per-tenant headers (`x-database`, `x-company`, Base64 variants) to the internal app.
  - Validates DB connectivity, can auto-create DB if enabled.
  - Supports HTTPS if `default_config.ssl.{enabled,key_path,cert_path[,ca_path]}` exist and files are present.

## Current Routes (from `production/routing.config.json`)

### **للتطوير المحلي (Internal Development Ports):**
- 3000 → DB `mohammi`, company "نظام إدارة المحاماة - محمد"
  - 🌐 http://localhost:3000
- 3001 → DB `rubaie`, company "نظام إدارة المحاماة - الربعي"
  - 🌐 http://localhost:3001

### **للإنتاج الخارجي (External Production Ports):**
- 7443 → DB `mohammi` (عبر الدومين الخارجي فقط)
  - 🌐 https://mohammi.com:7443
- 8914 → DB `rubaie` (عبر الدومين الخارجي فقط)
  - 🌐 https://mohammi.com:8914

### **إعدادات قاعدة البيانات:**
- Default DB connection: host `localhost`, port `5432`, user `postgres`

## Start/Stop

### **للتطوير المحلي (Local Development):**
```bash
# تشغيل النسخة الأولى (mohammi) على المنفذ 3000
npm run dev

# تشغيل النسخة الثانية (rubaie) على المنفذ 3001
npm run dev -- -p 3001

# أو تشغيل كلا النسختين معاً
# افتح نافذتين منفصلتين من الطرفية (Terminal):
# النافذة 1:
npm run dev
# النافذة 2:
npm run dev -- -p 3001
```

#### **الوصول المحلي:**
- 🌐 **النسخة الأولى (mohammi)**: http://localhost:3000
- 🌐 **النسخة الثانية (rubaie)**: http://localhost:3001

#### **ملاحظة مهمة:**
❌ **لا تستخدم** http://localhost:7443 أو http://localhost:8914 للتطوير المحلي
✅ **استخدم فقط** المنافذ 3000 و 3001 للتطوير المحلي

### **للإنتاج الخارجي (External Production):**
- Node directly (examples):
  - Advanced unified: `node advanced-unified-server.js`
- PM2 (optional, if you maintain a runner script):
  - Start: `pm2 start ecosystem.config.json`
  - Status: `pm2 status`
  - Logs: `pm2 logs`
  - Restart/Stop: `pm2 restart legal-system-unified` / `pm2 stop legal-system-unified`

## Update Process (Safe Changes)
- Goal: add/modify ports, DBs, visual identifiers without downtime.
- Steps:
  1) Back up routing/config files (see Backup section below) before any edit.
  2) Edit `D:\mohaminew\production\routing.config.json`:
     - Add or modify entries under `routes.{PORT}` with at least `database`, `company_name`, `theme_color`.
     - Optional per-route: `homepage`, `internal_next_port`, `welcome_message`, `notification_prefix`, `enabled`.
     - Ensure `default_config` DB credentials are correct.
  3) Save and restart the server mode you are using:
     - Restart the `advanced-unified-server.js` process only.
     - With PM2: `pm2 restart legal-system-unified`.
  4) Verify:
     - Open `http://localhost:<port>` for each route.
     - Check logs in `D:\mohaminew\logs\`.

## HTTPS (optional)
- Enable by adding in `default_config.ssl` within `routing.config.json`:
  - `enabled: true`, and valid `key_path`, `cert_path`, optional `ca_path`.
- Place cert files under `D:\mohaminew\ssl\` or adjust absolute paths.
- Restart advanced server after changes. Verify `🔐` HTTPS log and open `https://localhost:<port>`.

## Backup Procedure
- What to include:
  - Server scripts: `advanced-unified-server.js`.
  - Routing config: `production\routing.config.json`.
  - PM2 config: `ecosystem.config.json` (if used).
  - Env files: all `.env.*` files at repo root.
  - SSL folders: `ssl\` and `production\ssl\` (if exist).
  - Reference docs: `README-UNIFIED-SERVER.md`, `README-ADVANCED-SERVER.md`, `SERVER-POLICY.md`, `DEPLOYMENT_GUIDE*.md`.
- Recommended destination: `D:\mohaminew_backups\server_<YYYYMMDD_HHMMSS>`
- PowerShell one-liner (safe, idempotent):
```powershell
$ts=Get-Date -Format "yyyyMMdd_HHmmss";
$dest="D:\mohaminew_backups\server_$ts";
New-Item -ItemType Directory -Force -Path $dest | Out-Null;
$files=@("advanced-unified-server.js","ecosystem.config.json",
  "README-UNIFIED-SERVER.md","README-ADVANCED-SERVER.md","SERVER-POLICY.md",
  "DEPLOYMENT_GUIDE.md","DEPLOYMENT_GUIDE_UPDATED.md");
foreach($f in $files){ if(Test-Path "D:\mohaminew\$f"){ Copy-Item "D:\mohaminew\$f" -Destination $dest -Force } }
if(Test-Path "D:\mohaminew\production\routing.config.json"){ New-Item -ItemType Directory -Force -Path "$dest\production" | Out-Null; Copy-Item "D:\mohaminew\production\routing.config.json" -Destination "$dest\production" -Force }
Get-ChildItem -Path "D:\mohaminew" -Filter ".env.*" -File | Copy-Item -Destination $dest -Force -ErrorAction SilentlyContinue
foreach($dir in @("ssl","production\ssl")){
  if(Test-Path "D:\mohaminew\$dir"){ Copy-Item -Recurse -Force "D:\mohaminew\$dir" -Destination "$dest\$dir" }
}
Write-Host "Backup completed to $dest"
```

## Restore Procedure
1) Stop the running server/PM2 app.
2) Copy backed up files/folders back to their original paths under `D:\mohaminew\`.
3) Start the server again (see Start/Stop).
4) Verify each route URL and check logs.

## Downtime Avoidance Checklist
- [ ] Backup taken right before changes
- [ ] `routing.config.json` validates as JSON
- [ ] DBs exist or auto-create is enabled
- [ ] If enabling HTTPS, all cert paths exist and are readable
- [ ] Restarted only the relevant server process
- [ ] Verified both localhost links and logs

## Notes
- `SERVER-POLICY.md` states a single-manager policy. هذا المستودع يعمل الآن نمط الخادم الموحد المتقدم فقط؛ تجنّب تشغيل أي مدير آخر بالتوازي لمنع التعارض.
- `ecosystem.config.json` references `service-runner.js` only if PM2 is used; adjust if your runner differs.

## WhatsApp Real Service — Environment Variables
- هذه المتغيرات تتحكم في تشغيل خدمة WhatsApp الحقيقية (`whatsapp-real-service.js`).
  - __MAX_RETRIES__: عدد محاولات بدء الاتصال قبل إعلان الفشل النهائي. افتراضي 5.
  - __CONNECTION_TIMEOUT__: مهلة الاتصال بالثواني. بديل: `CONNECTION_TIMEOUT_MS` بالميللي ثانية. الافتراضي 120 ثانية.
  - __CHROME_HEADLESS__: تشغيل Chromium بدون واجهة ("true"/"1") أو بواجهة ("false"/افتراضي). الافتراضي false على Windows لتجنب مشاكل تعدد الأهداف.
  - __CHROME_PATH__: مسار تنفيذي Chrome/Chromium مخصص إن رغبت بتجاوز النسخة المرفقة مع Puppeteer.
  - __CHROME_FLAGS__: سلسلة أعلام إضافية تفصل بمسافات، مثل: `--remote-allow-origins=* --disable-extensions`.
  - __CHROME_FLAGS_JSON__: مصفوفة JSON لأعلام إضافية: `["--proxy-server=direct://","--proxy-bypass-list=*"]`.

### Defaults used by the service
- أعلام Puppeteer الافتراضية المفعّلة داخل `whatsapp-real-service.js`:
  - `--no-sandbox`, `--disable-setuid-sandbox`, `--disable-dev-shm-usage`,
  - `--disable-accelerated-2d-canvas`, `--no-first-run`, `--no-zygote`,
  - `--disable-gpu`, `--disable-web-security`, `--disable-features=VizDisplayCompositor`,
  - `--proxy-server=direct://`, `--proxy-bypass-list=*`, `--no-default-browser-check`,
  - `--disable-extensions`, `--remote-allow-origins=*`.

### Quick examples (PowerShell)
```powershell
# زيادة مهلة الاتصال إلى 180 ثانية ورفع المحاولات إلى 6
$env:CONNECTION_TIMEOUT = "180"
$env:MAX_RETRIES = "6"

# تفعيل Headless وتحديد Chrome مخصص وتمرير أعلام إضافية
$env:CHROME_HEADLESS = "true"
$env:CHROME_PATH = "C:\Program Files\Google\Chrome\Application\chrome.exe"
$env:CHROME_FLAGS = "--remote-allow-origins=* --disable-extensions"

# أو تمرير الأعلام كمصفوفة JSON
$env:CHROME_FLAGS_JSON = '["--proxy-server=direct://","--proxy-bypass-list=*"]'
```

## Schema Sync (rubaie structure-only)
- __الغرض__: تحديث هيكل وبنية قاعدة `rubaie` فقط (بدون أي بيانات) لمطابقة هيكل `mohammi`.
- __الملفات__: 
  - سكربت SQL: `D:\mohaminew\update_schema_only.sql`
  - سكربت تشغيل: `D:\mohaminew\sync-rubaie-schema.bat`

### التشغيل السريع
- افتح موجه الأوامر كمسؤول (اختياري) وشغّل:
```
D:\mohaminew\sync-rubaie-schema.bat
```
- السكربت يقوم بـ:
  - تشغيل DDL فقط عبر `psql` على قاعدة `rubaie`.
  - إصلاح تسلسلات المحاسبة عبر `node scripts\fix_sequences.js` إن توفّر Node.
  - حفظ سجل في `D:\mohaminew\logs\schema_sync_<date>_<time>.log`.

### إنشاء اختصار على سطح المكتب
- انقر يمين على `D:\mohaminew\sync-rubaie-schema.bat` → Send to → Desktop (create shortcut).
- أعِد تسمية الاختصار إلى: "تحديث هيكل قاعدة الربعي".

### المتطلبات
- PostgreSQL `psql` يجب أن يكون في PATH.
- الاتصال الافتراضي: host `localhost`, port `5432`, user `postgres`, password ضمن السكربت.
- لا يتم إدراج أي بيانات؛ التحديث هيكل فقط (idempotent).

### مخرجات وتشغيل السكربت
- __السجل__: يُحفَظ في `D:\mohaminew\logs\schema_sync_<date>_<time>.log` كما هو محدد في `sync-rubaie-schema.bat`.
- __نجاح/فشل__: يبحث السكربت عن `psql` في PATH، ثم ينفّذ `update_schema_only.sql` مع `ON_ERROR_STOP=1` لضمان إيقاف التنفيذ عند أول خطأ.
- __إصلاح التسلسلات__: إن وُجد Node في PATH، سيستدعي `node scripts\fix_sequences.js` بعد اكتمال DDL.

### تخصيص وإعدادات
- __تعديل اتصال القاعدة__: حدّث المتغيرات داخل `D:\mohaminew\sync-rubaie-schema.bat`:
  - `DB_HOST`, `DB_PORT`, `DB_NAME`, `DB_USER`, `DB_PASSWORD`.
  - المتغير `ROUTE_PORT=8914` يُمرَّر للسكربت Node فقط عند الحاجة.
- __موقع ملفات SQL__: يعتمد السكربت على `D:\mohaminew\update_schema_only.sql`. أبقِ الاسم/المسار كما هو أو عدّل `SQL_FILE` في السكربت.

### خطوات التحقق بعد التشغيل
1) افتح السجل وتأكد من عدم وجود أخطاء.
2) تحقق من وجود الجداول الأساسية: `governorates`, `courts`, `clients`, `currencies`, `issue_types`, `issue_statuses`, `contract_methods`, `issues`.
3) تحقق من الفهارس عبر:
   - `\d indexes issues` داخل `psql` أو الاستعلام عن `pg_indexes`.
4) اختبر الإدراجات المحدودة للتأكد من عمل التريجرات:
   - إدراج عميل ثم قضية ترتبط به؛ تأكد من تعبئة `client_name`, `client_phone` تلقائياً.
   - تحديث `currency_id` في قضية وتحقق من تحديث `amount_yer` و`currency_symbol`.

### الجدولة التلقائية (اختياري)
- يمكنك جدولة تشغيل دوري عبر Windows Task Scheduler:
  - Action: Start a program → Program/script: `D:\mohaminew\sync-rubaie-schema.bat`
  - Start in: `D:\mohaminew\`
  - اجعلها Manual/On-demand فقط عادةً، لأن السكربت DDL؛ لا حاجة لجدولة متكررة إلا عند تغيير هيكل `mohammi`.

### استكشاف الأخطاء الشائعة
- __psql غير موجود__: أضف مجلد PostgreSQL `bin` إلى PATH أو افتح "SQL Shell (psql)". رسالة الخطأ: "لم يتم العثور على psql في PATH".
- __فشل اتصال/مصادقة__: تحقق من `DB_HOST/PORT/USER/PASSWORD`، ومن إعدادات `pg_hba.conf` إذا كانت القاعدة عن بُعد.
- __أخطاء DDL (علاقات/أعمدة)__: السكربت يستخدم `CREATE TABLE IF NOT EXISTS` و`ADD COLUMN IF NOT EXISTS` لتجنب التعارضات. راجع السجل لمعرفة الجدول/العمود المحدد.
- __Node غير متاح__: سيتم تخطي خطوة إصلاح التسلسلات ويظهر تحذير فقط.
- __أقفال/تعطيل مؤقت__: إن ظهرت رسائل lock، أعد المحاولة بعد تقليل الأحمال، أو نفّذ خارج ساعات الذروة.

### ملاحظات أمان
- يحتوي `sync-rubaie-schema.bat` على كلمة مرور نصية (`DB_PASSWORD`).
  - إن لزم، استخدم حساب أقل صلاحية مخصص لـ DDL فقط، أو شغّل من جلسة آمنة.
  - بديل: حذف السطر `set DB_PASSWORD=...` واستخدام مطالبة كلمة مرور تفاعلية عبر `psql` (ستحتاج لتعديل السكربت).

### قابلية التكرار
- سكربت `update_schema_only.sql` مصمم ليكون آمنًا للتشغيل المتكرر (idempotent).
- لا يدرج بيانات، ويركّز على الجداول، الأعمدة، الفهارس، والتريجرات، مع مزامنة تسلسلات تلقائية.
