import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// إنشاء جدول القيود اليومية إذا لم يكن موجوداً
async function ensureJournalEntriesTable() {
  await query(`
    CREATE TABLE IF NOT EXISTS journal_entries (
      id SERIAL PRIMARY KEY,
      entry_number VARCHAR(50) UNIQUE NOT NULL,
      entry_date DATE NOT NULL,
      description TEXT NOT NULL,
      total_debit DECIMAL(15,2) NOT NULL DEFAULT 0,
      total_credit DECIMAL(15,2) NOT NULL DEFAULT 0,
      status VARCHAR(20) DEFAULT 'draft',
      created_by VARCHAR(100) DEFAULT 'النظام',
      created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      entry_type VARCHAR(20),
      source_tag VARCHAR(50)
    )
  `)

  // التوافق مع قواعد بيانات موجودة مسبقاً: إضافة العمود إذا لم يكن موجوداً
  await query(`ALTER TABLE journal_entries ADD COLUMN IF NOT EXISTS entry_type VARCHAR(20)`)
  await query(`ALTER TABLE journal_entries ADD COLUMN IF NOT EXISTS source_tag VARCHAR(50)`)
  // إصلاح قيد CHECK للسماح بكل الأنواع المطلوبة
  await query(`ALTER TABLE journal_entries DROP CONSTRAINT IF EXISTS journal_entries_entry_type_check`)
  await query(`ALTER TABLE journal_entries ADD CONSTRAINT journal_entries_entry_type_check CHECK (entry_type IN ('journal','payment','receipt','opening'))`)

  await query(`
    CREATE TABLE IF NOT EXISTS journal_entry_details (
      id SERIAL PRIMARY KEY,
      journal_entry_id INTEGER REFERENCES journal_entries(id) ON DELETE CASCADE,
      account_id INTEGER,
      account_name VARCHAR(255),
      account_code VARCHAR(100),
      debit_amount DECIMAL(15,2) DEFAULT 0,
      credit_amount DECIMAL(15,2) DEFAULT 0,
      description TEXT,
      line_order INTEGER DEFAULT 1,
      issues_id INTEGER,
      cost_center_id INTEGER
    )
  `)

  // التوافق مع قواعد بيانات موجودة مسبقاً
  await query(`ALTER TABLE journal_entry_details ADD COLUMN IF NOT EXISTS account_code VARCHAR(100)`)
  await query(`ALTER TABLE journal_entry_details ADD COLUMN IF NOT EXISTS cost_center_id INTEGER`)
  await query(`ALTER TABLE journal_entry_details ADD COLUMN IF NOT EXISTS issues_id INTEGER`)
}

// توليد رقم قيد جديد بحسب البادئة (JE, PV, RV, ST) مع ترقيم برقمين يبدأ من 01
async function generateEntryNumber(prefix: 'JE' | 'PV' | 'RV' | 'ST' = 'JE') {
  const result = await query(
    `SELECT COALESCE(MAX(CAST(SUBSTRING(entry_number FROM 3) AS INTEGER)), 0) + 1 AS next_number
       FROM journal_entries
      WHERE entry_number ~ '^${prefix}[0-9]+$'`
  )
  const nextNumber = result.rows[0]?.next_number || 1
  return `${prefix}${String(nextNumber).padStart(2, '0')}`
}

// GET - جلب جميع القيود اليومية
export async function GET(request: NextRequest) {
  try {
    await ensureJournalEntriesTable()

    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const dateFrom = searchParams.get('date_from')
    const dateTo = searchParams.get('date_to')

    // دعم تصفية الأنواع عبر entry_types=journal,opening
    const entryTypesParam = searchParams.get('entry_types') // مثال: 'journal' أو 'journal,opening'
    const entryTypes = entryTypesParam ? entryTypesParam.split(',').map(t => t.trim()).filter(Boolean) : null

    let sql = `
      SELECT
        je.*,
        COUNT(jed.id) as details_count
      FROM journal_entries je
      LEFT JOIN journal_entry_details jed ON je.id = jed.journal_entry_id
      WHERE (${entryTypes && entryTypes.length > 0 ? `je.entry_type = ANY($1)` : `(je.entry_type IN ('journal','opening') OR je.entry_type IS NULL)`})
    `

    const params: any[] = []
    let paramIndex = 1

    if (entryTypes && entryTypes.length > 0) {
      params.push(entryTypes)
      paramIndex++
    }

    // تصفية حسب الحالة
    if (status && status !== 'all') {
      sql += ` AND je.status = $${paramIndex}`
      params.push(status)
      paramIndex++
    }

    // تصفية حسب التاريخ
    if (dateFrom) {
      sql += ` AND je.entry_date >= $${paramIndex}`
      params.push(dateFrom)
      paramIndex++
    }

    if (dateTo) {
      sql += ` AND je.entry_date <= $${paramIndex}`
      params.push(dateTo)
      paramIndex++
    }

    sql += ` GROUP BY je.id ORDER BY je.entry_date DESC, je.entry_number DESC`

    const result = await query(sql, params)

    // جلب تفاصيل كل قيد
    const entries = []
    for (const entry of result.rows) {
      const detailsResult = await query(`
        SELECT
          jed.*,
          COALESCE(coa.account_name, jed.account_name) as account_name,
          coa.account_code
        FROM journal_entry_details jed
        LEFT JOIN chart_of_accounts coa ON jed.account_id = coa.id
        WHERE jed.journal_entry_id = $1
        ORDER BY jed.line_order
      `, [entry.id])

      entries.push({
        ...entry,
        details: detailsResult.rows,
        total_debit: parseFloat(entry.total_debit || 0),
        total_credit: parseFloat(entry.total_credit || 0)
      })
    }

    return NextResponse.json({
      success: true,
      entries,
      total: entries.length,
      message: 'تم جلب القيود اليومية بنجاح'
    })

  } catch (error) {
    console.error('❌ خطأ في جلب القيود اليومية:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب القيود اليومية',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// POST - إضافة قيد يومي جديد
export async function POST(request: NextRequest) {
  // للاستخدام في التتبّع داخل catch
  let lastSql: string = ''
  let lastParams: any[] = []
  try {
    await ensureJournalEntriesTable()

    const body = await request.json()

    const {
      entry_date,
      description,
      details = [],
      status = 'draft',
      entry_type = 'journal'
    } = body

    // التحقق من البيانات المطلوبة
    const missingFields = []
    if (!entry_date) missingFields.push('تاريخ القيد (entry_date)')
    if (!description) missingFields.push('وصف القيد (description)')
    if (!details || !Array.isArray(details) || details.length === 0) {
      missingFields.push('تفاصيل القيد (details) - يجب أن تكون مصفوفة تحتوي على سطر واحد على الأقل')
    }

    if (missingFields.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'البيانات المطلوبة مفقودة',
        details: `البيانات المفقودة: ${missingFields.join(', ')}`,
        missingFields: missingFields,
        receivedData: body
      }, { status: 400 })
    }

    // التحقق من تفاصيل كل سطر
    const detailErrors = []
    details.forEach((detail, index) => {
      const lineErrors = []
      if (!detail.account_id) lineErrors.push('معرف الحساب (account_id)')
      if (detail.debit_amount === undefined && detail.credit_amount === undefined) {
        lineErrors.push('يجب تحديد مبلغ مدين أو دائن')
      }
      if (detail.debit_amount < 0 || detail.credit_amount < 0) {
        lineErrors.push('المبالغ يجب أن تكون موجبة')
      }

      if (lineErrors.length > 0) {
        detailErrors.push(`السطر ${index + 1}: ${lineErrors.join(', ')}`)
      }
    })

    if (detailErrors.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'أخطاء في تفاصيل القيد',
        details: detailErrors.join(' | '),
        detailErrors: detailErrors,
        receivedData: body
      }, { status: 400 })
    }

    // حساب إجمالي المدين والدائن
    let total_debit = 0
    let total_credit = 0

    for (const detail of details) {
      total_debit += parseFloat(detail.debit_amount || 0)
      total_credit += parseFloat(detail.credit_amount || 0)
    }

    // التحقق من توازن القيد
    if (Math.abs(total_debit - total_credit) > 0.01) {
      return NextResponse.json({
        success: false,
        error: 'القيد غير متوازن',
        details: `إجمالي المدين (${total_debit}) لا يساوي إجمالي الدائن (${total_credit})`
      }, { status: 400 })
    }

    // تحديد البادئة حسب نوع السند
    const typeToPrefix = (t: string): 'JE' | 'PV' | 'RV' | 'ST' => {
      const tt = (t || '').toLowerCase()
      if (tt === 'payment' || tt === 'pv') return 'PV'
      if (tt === 'receipt' || tt === 'rv') return 'RV'
      if (tt === 'opening' || tt === 'st') return 'ST'
      return 'JE'
    }
    const prefix = typeToPrefix(entry_type)
    const entry_number = await generateEntryNumber(prefix)

    // إدراج القيد الرئيسي (يتضمن entry_type)
    const source_tag = entry_type === 'opening' ? 'opening_balances' : null
    lastSql = `
      INSERT INTO journal_entries (
        entry_number, entry_date, description,
        total_debit, total_credit, status, created_by, entry_type, source_tag
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING *
    `
    lastParams = [
      entry_number, entry_date, description,
      total_debit, total_credit, status, 'النظام', entry_type, source_tag
    ]
    const entryResult = await query(lastSql, lastParams)

    const newEntry = entryResult.rows[0]

    // إدراج تفاصيل القيد
    for (let i = 0; i < details.length; i++) {
      const detail = details[i]

      // جلب اسم ورمز الحساب من دليل الحسابات
      let accountName = detail.account_name
      let accountCode = detail.account_code

      if ((!accountName || !accountCode) && detail.account_id) {
        const accountResult = await query(
          'SELECT account_name, account_code FROM chart_of_accounts WHERE id = $1',
          [detail.account_id]
        )
        if (accountResult.rows[0]) {
          accountName = accountName || accountResult.rows[0].account_name || `حساب رقم ${detail.account_id}`
          accountCode = accountCode || accountResult.rows[0].account_code
        }
      }

      // إدراج السطر مع تمرير cost_center_id إذا وُجد (سيُتجاهل تلقائياً إن لم يكن العمود موجوداً)
      lastSql = `
        INSERT INTO journal_entry_details (
          journal_entry_id, account_id, account_name, account_code,
          debit_amount, credit_amount, description, line_order${detail.cost_center_id ? ', cost_center_id' : ''}${detail.issues_id ? ', issues_id' : ''}
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8${detail.cost_center_id ? ', $9' : ''}${detail.issues_id ? (detail.cost_center_id ? ', $10' : ', $9') : ''}
        )
      `

      const insertParams: any[] = [
        newEntry.id, detail.account_id, accountName, accountCode,
        detail.debit_amount || 0, detail.credit_amount || 0,
        detail.description, i + 1
      ]
      if (detail.cost_center_id) insertParams.push(detail.cost_center_id)
      if (detail.issues_id) insertParams.push(detail.issues_id)
      lastParams = insertParams
      await query(lastSql, insertParams)
    }

    return NextResponse.json({
      success: true,
      entry: newEntry,
      message: `تم إنشاء القيد اليومي ${entry_number} بنجاح`
    })

  } catch (error: any) {
    console.error('❌ خطأ في إنشاء القيد اليومي:', error)
    // إرجاع تفاصيل أدق في بيئة التطوير
    const isDev = process.env.NODE_ENV !== 'production'
    return NextResponse.json({
      success: false,
      error: 'فشل في إنشاء القيد اليومي',
      details: error?.message || 'خطأ غير معروف',
      stack: isDev ? error?.stack : undefined,
      lastSql: isDev ? (typeof lastSql === 'string' ? lastSql : undefined) : undefined,
      lastParams: isDev ? (typeof lastParams !== 'undefined' ? lastParams : undefined) : undefined,
    }, { status: 500 })
  }
}

// PUT - تحديث قيد يومي
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, ...updateData } = body

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف القيد مطلوب'
      }, { status: 400 })
    }

    // تحديث القيد
    const result = await query(`
      UPDATE journal_entries 
      SET 
        entry_date = COALESCE($2, entry_date),
        description = COALESCE($3, description),
        status = COALESCE($4, status),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `, [
      id, updateData.entry_date, updateData.description, updateData.status
    ])

    if (result.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'القيد غير موجود'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      entry: result.rows[0],
      message: 'تم تحديث القيد اليومي بنجاح'
    })

  } catch (error) {
    console.error('❌ خطأ في تحديث القيد اليومي:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث القيد اليومي',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
