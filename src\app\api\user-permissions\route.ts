import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب صلاحيات مستخدم معين
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'معرف المستخدم مطلوب' },
        { status: 400 }
      )
    }

    console.log(`🔄 جلب صلاحيات المستخدم: ${userId}`)

    // محاولة قراءة الصلاحيات من قاعدة البيانات أولاً
    let userPermissions = []

    try {
      console.log('📊 جلب صلاحيات المستخدم من قاعدة البيانات...')
      const dbPermissions = await query(`
        SELECT up.permission_key, p.name as permission_name, p.category, up.granted_date, up.is_active
        FROM user_permissions up
        LEFT JOIN permissions p ON up.permission_key = p.key
        WHERE up.user_id = $1 AND up.is_active = true
        ORDER BY p.category, p.name
      `, [userId])

      if (dbPermissions.rows.length > 0) {
        userPermissions = dbPermissions.rows.map(row => ({
          permission_key: row.permission_key,
          permission_name: row.permission_name || getPermissionName(row.permission_key),
          category: row.category || getPermissionCategory(row.permission_key),
          is_active: row.is_active,
          granted_date: row.granted_date || new Date().toISOString()
        }))

        console.log(`✅ تم جلب ${userPermissions.length} صلاحية من قاعدة البيانات`)
      } else {
        console.log('⚠️ لا توجد صلاحيات محفوظة في قاعدة البيانات للمستخدم')
      }

    } catch (dbError) {
      console.log('❌ خطأ في قراءة الصلاحيات من قاعدة البيانات:', dbError.message)
      console.log('🔄 التبديل إلى الملف المحلي...')

      // قراءة الصلاحيات من الملف كبديل
      const fs = require('fs')
      const path = require('path')

      const permissionsFile = path.join(process.cwd(), 'logs', 'user-permissions.json')

      if (fs.existsSync(permissionsFile)) {
        try {
          const fileContent = fs.readFileSync(permissionsFile, 'utf8')
          const allUserPermissions = JSON.parse(fileContent)

          if (allUserPermissions[userId]) {
            const savedPermissions = allUserPermissions[userId].permissions || []
            userPermissions = savedPermissions.map(key => ({
              permission_key: key,
              permission_name: getPermissionName(key),
              category: getPermissionCategory(key),
              is_active: true,
              granted_date: allUserPermissions[userId].updatedAt || new Date().toISOString()
            }))
            console.log(`✅ تم جلب ${userPermissions.length} صلاحية من الملف المحلي`)
          }
        } catch (error) {
          console.log('⚠️ خطأ في قراءة ملف الصلاحيات المحلي')
        }
      }
    }

    // إذا لم توجد صلاحيات محفوظة، استخدم الافتراضية
    if (userPermissions.length === 0) {
      console.log('🔄 استخدام الصلاحيات الافتراضية')
      const defaultPermissions = [
        'users:view',
        'cases:view',
        'clients:view',
        'whatsapp:view',
        'settings:view',
        'reports:view'
      ]

      userPermissions = defaultPermissions.map(key => ({
        permission_key: key,
        permission_name: getPermissionName(key),
        category: getPermissionCategory(key),
        is_active: true,
        granted_date: new Date().toISOString()
      }))
    }

    console.log(`📊 المستخدم ${userId} لديه ${userPermissions.length} صلاحية`)

    return NextResponse.json({
      success: true,
      data: userPermissions
    })
  } catch (error) {
    console.error('❌ خطأ في جلب صلاحيات المستخدم:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب صلاحيات المستخدم' },
      { status: 500 }
    )
  }
}

function getPermissionName(key: string): string {
  const names: { [key: string]: string } = {
    // إدارة المستخدمين
    'users:view': 'عرض المستخدمين',
    'users:create': 'إضافة مستخدم',
    'users:update': 'تعديل مستخدم',
    'users:delete': 'حذف مستخدم',
    'users:permissions': 'إدارة صلاحيات المستخدمين',

    // إدارة القضايا
    'cases:view': 'عرض القضايا',
    'cases:create': 'إضافة قضية',
    'cases:update': 'تعديل قضية',
    'cases:delete': 'حذف قضية',
    'cases:assign': 'تعيين القضايا',
    'cases:status': 'تغيير حالة القضية',

    // إدارة العملاء
    'clients:view': 'عرض العملاء',
    'clients:create': 'إضافة عميل',
    'clients:update': 'تعديل عميل',
    'clients:delete': 'حذف عميل',
    'clients:contact': 'التواصل مع العملاء',

    // المحاسبة
    'accounting:view': 'عرض المحاسبة',
    'accounting:create': 'إضافة قيد محاسبي',
    'accounting:update': 'تعديل قيد محاسبي',
    'accounting:delete': 'حذف قيد محاسبي',
    'accounting:approve': 'اعتماد القيود المحاسبية',
    'accounting:reports': 'تقارير محاسبية',

    // WhatsApp
    'whatsapp:view': 'عرض إعدادات WhatsApp',
    'whatsapp:manage': 'إدارة اتصال WhatsApp',
    'whatsapp:send': 'إرسال رسائل WhatsApp',
    'whatsapp:templates': 'إدارة قوالب الرسائل',
    'whatsapp:monitor': 'مراقبة حالة WhatsApp',
    'whatsapp:settings': 'تعديل إعدادات WhatsApp',
    'whatsapp:contacts': 'إدارة جهات اتصال WhatsApp',
    'whatsapp:reminders': 'إدارة تذكيرات WhatsApp',
    'whatsapp:notifications': 'إدارة إشعارات WhatsApp',
    'whatsapp:logs': 'عرض سجلات WhatsApp',
    'whatsapp:statistics': 'عرض إحصائيات WhatsApp',

    // الإعدادات
    'settings:view': 'عرض الإعدادات',
    'settings:update': 'تعديل الإعدادات',
    'settings:company': 'إعدادات الشركة',
    'settings:system': 'إعدادات النظام',
    'settings:security': 'إعدادات الأمان',

    // التقارير
    'reports:view': 'عرض التقارير',
    'reports:export': 'تصدير التقارير',
    'reports:create': 'إنشاء تقارير مخصصة',
    'reports:schedule': 'جدولة التقارير',

    // الأقسام
    'departments:view': 'عرض الأقسام',
    'departments:create': 'إضافة قسم',
    'departments:update': 'تعديل قسم',
    'departments:delete': 'حذف قسم',

    // المكتبة القانونية
    'library:view': 'عرض المكتبة القانونية',
    'library:create': 'إضافة مستند قانوني',
    'library:update': 'تعديل مستند قانوني',
    'library:delete': 'حذف مستند قانوني',
    'library:download': 'تحميل المستندات',

    // المواعيد والتقويم
    'calendar:view': 'عرض التقويم',
    'calendar:create': 'إضافة موعد',
    'calendar:update': 'تعديل موعد',
    'calendar:delete': 'حذف موعد',

    // المهام
    'tasks:view': 'عرض المهام',
    'tasks:create': 'إضافة مهمة',
    'tasks:update': 'تعديل مهمة',
    'tasks:delete': 'حذف مهمة',
    'tasks:assign': 'تعيين المهام',

    // الإشعارات
    'notifications:view': 'عرض الإشعارات',
    'notifications:create': 'إرسال إشعار',
    'notifications:manage': 'إدارة الإشعارات',

    // النسخ الاحتياطي
    'backup:view': 'عرض النسخ الاحتياطية',
    'backup:create': 'إنشاء نسخة احتياطية',
    'backup:restore': 'استعادة نسخة احتياطية',

    // السجلات والمراجعة
    'audit:view': 'عرض سجلات المراجعة',
    'audit:export': 'تصدير سجلات المراجعة',

    // الذكاء الاصطناعي
    'ai:view': 'عرض إعدادات الذكاء الاصطناعي',
    'ai:manage': 'إدارة الذكاء الاصطناعي',
    'ai:chat': 'استخدام المحادثة الذكية'
  }
  return names[key] || key
}

function getPermissionCategory(key: string): string {
  const categories: { [key: string]: string } = {
    'users:view': 'إدارة المستخدمين',
    'users:create': 'إدارة المستخدمين',
    'users:update': 'إدارة المستخدمين',
    'users:delete': 'إدارة المستخدمين',
    'cases:view': 'إدارة القضايا',
    'cases:create': 'إدارة القضايا',
    'cases:update': 'إدارة القضايا',
    'cases:delete': 'إدارة القضايا',
    'clients:view': 'إدارة العملاء',
    'clients:create': 'إدارة العملاء',
    'clients:update': 'إدارة العملاء',
    'clients:delete': 'إدارة العملاء',
    'whatsapp:view': 'WhatsApp',
    'whatsapp:manage': 'WhatsApp',
    'whatsapp:send': 'WhatsApp',
    'settings:view': 'الإعدادات',
    'settings:update': 'الإعدادات',
    'reports:view': 'التقارير',
    'reports:export': 'التقارير'
  }
  return categories[key] || 'عام'
}

// POST - تحديث صلاحيات مستخدم (مؤقت - حفظ في ملف)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { userId, permissions, grantedBy } = body

    if (!userId || !Array.isArray(permissions)) {
      return NextResponse.json(
        { success: false, error: 'بيانات غير صحيحة' },
        { status: 400 }
      )
    }

    console.log(`🔄 تحديث صلاحيات المستخدم: ${userId}`)
    console.log(`📋 الصلاحيات الجديدة:`, permissions)

    // محاولة حفظ في قاعدة البيانات أولاً
    try {
      console.log('💾 حفظ الصلاحيات في قاعدة البيانات...')

      // بداية المعاملة
      await query('BEGIN')

      try {
        // حذف الصلاحيات الحالية
        await query(`
          DELETE FROM user_permissions
          WHERE user_id = $1
        `, [userId])

        // إضافة الصلاحيات الجديدة
        for (const permission of permissions) {
          await query(`
            INSERT INTO user_permissions (user_id, permission_key, granted_by, granted_date, is_active)
            VALUES ($1, $2, $3, NOW(), true)
          `, [userId, permission, grantedBy || 1])
        }

        // تأكيد المعاملة
        await query('COMMIT')

        console.log(`✅ تم حفظ ${permissions.length} صلاحية في قاعدة البيانات للمستخدم ${userId}`)

        return NextResponse.json({
          success: true,
          message: 'تم تحديث الصلاحيات بنجاح في قاعدة البيانات',
          data: { userId, permissionsCount: permissions.length, source: 'database' }
        })

      } catch (error) {
        // إلغاء المعاملة في حالة الخطأ
        await query('ROLLBACK')
        throw error
      }

    } catch (dbError) {
      console.log('❌ خطأ في حفظ الصلاحيات في قاعدة البيانات:', dbError.message)
      console.log('🔄 التبديل إلى الحفظ في الملف المحلي...')

      // حفظ في ملف JSON كبديل
      const fs = require('fs')
      const path = require('path')

      const permissionsFile = path.join(process.cwd(), 'logs', 'user-permissions.json')

      // إنشاء مجلد logs إذا لم يكن موجوداً
      const logsDir = path.join(process.cwd(), 'logs')
      if (!fs.existsSync(logsDir)) {
        fs.mkdirSync(logsDir, { recursive: true })
      }

      // قراءة الصلاحيات الحالية أو إنشاء ملف جديد
      let allUserPermissions = {}
      if (fs.existsSync(permissionsFile)) {
        try {
          const fileContent = fs.readFileSync(permissionsFile, 'utf8')
          allUserPermissions = JSON.parse(fileContent)
        } catch (error) {
          console.log('⚠️ خطأ في قراءة ملف الصلاحيات، إنشاء ملف جديد')
          allUserPermissions = {}
        }
      }

      // تحديث صلاحيات المستخدم
      allUserPermissions[userId] = {
        permissions: permissions,
        grantedBy: grantedBy || 1,
        updatedAt: new Date().toISOString()
      }

      // حفظ الملف
      fs.writeFileSync(permissionsFile, JSON.stringify(allUserPermissions, null, 2))

      console.log(`✅ تم حفظ صلاحيات المستخدم ${userId} في الملف المحلي - ${permissions.length} صلاحية`)
    }

    return NextResponse.json({
      success: true,
      message: 'تم تحديث الصلاحيات بنجاح',
      data: { userId, permissionsCount: permissions.length }
    })

  } catch (error) {
    console.error('❌ خطأ في تحديث صلاحيات المستخدم:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الصلاحيات' },
      { status: 500 }
    )
  }
}
