import { NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'

const LAWS_DIRECTORY = '/home/<USER>/Downloads/legal-system/laws'

export async function GET() {
  try {
    const files = await fs.promises.readdir(LAWS_DIRECTORY)
    const txtFiles = files.filter(file => file.endsWith('.txt'))
    
    const fileStats = await Promise.all(
      txtFiles.map(async (file) => {
        try {
          const filePath = path.join(LAWS_DIRECTORY, file)
          const content = await fs.promises.readFile(filePath, 'utf-8')
          const lines = content.split('\n').length
          const words = content.split(/\s+/).length
          const size = (await fs.promises.stat(filePath)).size
          
          return {
            name: file.replace('.txt', '').replace(/agoyemen\.net_\d+_/, ''),
            fileName: file,
            lines,
            words,
            size: Math.round(size / 1024), // KB
            lastModified: (await fs.promises.stat(filePath)).mtime
          }
        } catch (error) {
          return {
            name: file,
            fileName: file,
            lines: 0,
            words: 0,
            size: 0,
            error: error.message
          }
        }
      })
    )
    
    const totalFiles = fileStats.length
    const totalWords = fileStats.reduce((sum, file) => sum + file.words, 0)
    const totalSize = fileStats.reduce((sum, file) => sum + file.size, 0)
    
    return NextResponse.json({
      success: true,
      data: {
        summary: {
          totalFiles,
          totalWords,
          totalSize: Math.round(totalSize / 1024), // MB
          lastUpdate: new Date().toISOString()
        },
        files: fileStats.sort((a, b) => b.words - a.words) // ترتيب حسب عدد الكلمات
      }
    })
    
  } catch (error) {
    console.error('خطأ في قراءة إحصائيات الملفات القانونية:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في قراءة الملفات القانونية'
    }, { status: 500 })
  }
}
