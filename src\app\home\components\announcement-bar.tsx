import { X, ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';

type Announcement = {
  id: number;
  title: string;
  content: string;
  type: 'primary' | 'secondary';
  is_active: boolean;
  created_at: string;
  updated_at: string;
};

interface AnnouncementBarProps {
  announcements: Announcement[];
  activeAnnouncement: number | null;
  onAnnouncementChange: (id: number | null) => void;
}

export function AnnouncementBar({ 
  announcements, 
  activeAnnouncement, 
  onAnnouncementChange 
}: AnnouncementBarProps) {
  if (announcements.length === 0) return null;

  const currentIndex = announcements.findIndex(a => a.id === activeAnnouncement);
  const currentAnnouncement = activeAnnouncement !== null 
    ? announcements.find(a => a.id === activeAnnouncement)
    : announcements[0];

  const goToPrevious = () => {
    const prevIndex = (currentIndex - 1 + announcements.length) % announcements.length;
    onAnnouncementChange(announcements[prevIndex].id);
  };

  const goToNext = () => {
    const nextIndex = (currentIndex + 1) % announcements.length;
    onAnnouncementChange(announcements[nextIndex].id);
  };

  if (!currentAnnouncement) return null;

  return (
    <div className="bg-green-600 text-white text-center py-2 px-4">
      <div className="container mx-auto flex items-center justify-between">
        <Button 
          variant="ghost" 
          size="sm" 
          className="text-white hover:bg-green-700 p-1 rounded-full"
          onClick={goToPrevious}
        >
          <ChevronRight className="w-4 h-4" />
        </Button>
        
        <div className="flex-1 px-4">
          <div className="flex flex-col items-center">
            <Button 
              variant="link" 
              className="text-white hover:underline p-0 h-auto font-medium"
              onClick={() => onAnnouncementChange(currentAnnouncement.id)}
            >
              {currentAnnouncement.title}
            </Button>
            {activeAnnouncement === currentAnnouncement.id && (
              <p className="text-sm mt-1">{currentAnnouncement.content}</p>
            )}
          </div>
        </div>
        
        <div className="flex items-center space-x-2 space-x-reverse">
          {announcements.map((announcement) => (
            <button
              key={announcement.id}
              className={`w-2 h-2 rounded-full transition-colors ${
                activeAnnouncement === announcement.id ? 'bg-white' : 'bg-white/50'
              }`}
              onClick={() => onAnnouncementChange(announcement.id)}
              aria-label={`إعلان ${announcement.id}`}
            />
          ))}
          
          <Button 
            variant="ghost" 
            size="sm" 
            className="text-white hover:bg-blue-700 p-1 rounded-full"
            onClick={goToNext}
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>
          
          <Button 
            variant="ghost" 
            size="sm" 
            className="text-white hover:bg-blue-700 p-1 rounded-full"
            onClick={() => onAnnouncementChange(null)}
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
