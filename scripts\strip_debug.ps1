# Strip debug logs and TODO/FIXME comments from src code (DESTRUCTIVE)
# Run with: Set-ExecutionPolicy -Scope Process Bypass; ./scripts/strip_debug.ps1
$ErrorActionPreference = 'Continue'
$Root = Split-Path -Parent $MyInvocation.MyCommand.Path
$Root = Split-Path -Parent $Root
$src  = Join-Path $Root 'src'

if (!(Test-Path $src)) { throw "src not found: $src" }

# Target file extensions
$ext = @('*.ts','*.tsx','*.js','*.jsx')

# Regex patterns to remove entire lines
$patterns = @(
  '(?im)^\s*console\.(log|debug)\s*\(.*?\)\s*;?\s*$',
  '(?im)^\s*//\s*(TODO|FIXME).*$',
  '(?is)/\*\s*(TODO|FIXME)[\s\S]*?\*/'
)

Get-ChildItem -Path $src -Recurse -File -Include $ext | ForEach-Object {
  try {
    $content = [System.IO.File]::ReadAllText($_.FullName)
    $original = $content
    foreach ($re in $patterns) {
      $content = [regex]::Replace($content, $re, '')
    }
    if ($content -ne $original) {
      # Normalize multiple blank lines
      $content = [regex]::Replace($content, '(?m)^[\t ]+$', '')
      $content = [regex]::Replace($content, '(?m)(\r?\n){3,}', "`r`n`r`n")
      [System.IO.File]::WriteAllText($_.FullName, $content, (New-Object System.Text.UTF8Encoding($false)))
      Write-Host ("Stripped debug from: " + $_.FullName)
    }
  } catch {
    Write-Warning ("Failed processing: " + $_.FullName + ' -> ' + $_.Exception.Message)
  }
}

Write-Host 'STRIP DEBUG COMPLETE' -ForegroundColor Green
