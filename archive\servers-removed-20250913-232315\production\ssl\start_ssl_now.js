// تشغيل SSL فوري لـ mohammi.com مع المفتاح الموجود
const fs = require('fs');
const https = require('https');
const http = require('http');

console.log('🚀 تشغيل SSL فوري - mohammi.com');
console.log('🔑 المفتاح الخاص: mohammi.key موجود');
console.log('🌐 IP: ***********');
console.log('='.repeat(50));

// ملفات SSL المطلوبة
const sslFiles = {
  cert: 'ssl/mohammi_com.crt',
  key: 'ssl/mohammi.key',
  intermediate: 'ssl/SectigoPublicServerAuthenticationCADVR36.crt'
};

// التحقق من وجود الملفات
console.log('🔍 التحقق من ملفات SSL...');
for (const [name, path] of Object.entries(sslFiles)) {
  if (fs.existsSync(path)) {
    const stats = fs.statSync(path);
    console.log(`✅ ${name}: ${path} (${stats.size} bytes)`);
  } else {
    console.log(`❌ ${name}: ${path} غير موجود`);
    if (name === 'intermediate') {
      console.log('💡 سيتم المتابعة بدون الشهادة الوسطية');
      sslFiles.intermediate = null;
    } else {
      process.exit(1);
    }
  }
}

// قراءة ملفات SSL
console.log('\n📖 قراءة ملفات SSL...');
let sslOptions;

try {
  const privateKey = fs.readFileSync(sslFiles.key, 'utf8');
  let certificate = fs.readFileSync(sslFiles.cert, 'utf8');
  
  console.log('✅ تم قراءة المفتاح الخاص');
  console.log('✅ تم قراءة الشهادة');
  
  // إضافة الشهادة الوسطية إذا كانت متاحة
  if (sslFiles.intermediate) {
    const intermediate = fs.readFileSync(sslFiles.intermediate, 'utf8');
    certificate = certificate + '\n' + intermediate;
    console.log('✅ تم دمج الشهادة الوسطية');
  }
  
  sslOptions = {
    key: privateKey,
    cert: certificate,
    secureProtocol: 'TLSv1_2_method',
    ciphers: [
      'ECDHE-RSA-AES128-GCM-SHA256',
      'ECDHE-RSA-AES256-GCM-SHA384',
      'ECDHE-RSA-AES128-SHA256',
      'ECDHE-RSA-AES256-SHA384'
    ].join(':'),
    honorCipherOrder: true
  };
  
  console.log('✅ تم إعداد خيارات SSL بنجاح');
  
} catch (error) {
  console.error('❌ خطأ في قراءة ملفات SSL:', error.message);
  process.exit(1);
}

// إعدادات المنافذ
const PORTS = {
  https_7443: 7443,  // للتطبيق mohammi
  https_8914: 8914,  // للتطبيق rubaie
  http_7080: 7080,   // إعادة توجيه لـ 7443
  http_8080: 8080    // إعادة توجيه لـ 8914
};

// دالة إنشاء صفحة معلومات SSL
function createSSLInfoPage(port, database) {
  return `<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>🔐 SSL نشط - mohammi.com:${port}</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; margin: 0; padding: 50px; text-align: center;
        }
        .container { 
            background: rgba(255,255,255,0.1); padding: 40px; 
            border-radius: 20px; backdrop-filter: blur(10px);
            max-width: 700px; margin: 0 auto;
        }
        .status { color: #4ade80; font-weight: bold; font-size: 1.2em; }
        .info { 
            background: rgba(255,255,255,0.1); padding: 20px; 
            border-radius: 10px; margin: 20px 0; text-align: right; 
        }
        .links { 
            background: rgba(255,255,255,0.1); padding: 20px; 
            border-radius: 10px; margin: 20px 0; 
        }
        .links a { 
            color: #60a5fa; text-decoration: none; 
            display: block; margin: 5px 0; 
        }
        .links a:hover { color: #93c5fd; }
        .success { 
            background: rgba(34, 197, 94, 0.2); 
            border: 2px solid #22c55e; 
            padding: 20px; border-radius: 10px; 
            margin: 20px 0; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 شهادة SSL تعمل بنجاح!</h1>
        <h2>المنفذ ${port} - قاعدة البيانات ${database}</h2>
        
        <div class="success">
            <h3 class="status">✅ SSL مُفعل ويعمل بنجاح!</h3>
            <p>الاتصال آمن ومشفر بـ TLS 1.2+</p>
        </div>
        
        <div class="info">
            <h3>📊 معلومات الاتصال:</h3>
            <p><strong>البروتوكول:</strong> <span class="status">HTTPS ✅</span></p>
            <p><strong>المنفذ:</strong> ${port}</p>
            <p><strong>قاعدة البيانات:</strong> ${database}</p>
            <p><strong>IP الخارجي:</strong> ***********</p>
            <p><strong>الدومين:</strong> mohammi.com</p>
            <p><strong>التشفير:</strong> TLS 1.2+</p>
            <p><strong>المُصدر:</strong> Sectigo</p>
            <p><strong>الوقت:</strong> ${new Date().toLocaleString('ar-EG')}</p>
        </div>
        
        <div class="info">
            <h3>🔗 روابط الوصول:</h3>
            <p>• <a href="https://mohammi.com:${port}">https://mohammi.com:${port}</a></p>
            <p>• <a href="https://***********:${port}">https://***********:${port}</a></p>
        </div>
        
        <div class="info">
            <h3>🧪 اختبار الشهادة:</h3>
            <p>• <a href="https://www.ssllabs.com/ssltest/analyze.html?d=mohammi.com:${port}" target="_blank">SSL Labs Test</a></p>
            <p>• <a href="https://www.sslshopper.com/ssl-checker.html#hostname=mohammi.com:${port}" target="_blank">SSL Checker</a></p>
        </div>
        
        <div class="success">
            <h3>🎉 تهانينا!</h3>
            <p>تم تثبيت شهادة SSL بنجاح على المنفذ ${port}</p>
            <p>يمكنك الآن استخدام الموقع بأمان عبر HTTPS</p>
        </div>
        
        <div class="info">
            <h3>📋 معلومات إضافية:</h3>
            <p>• الشهادة صالحة ومُوقعة من Sectigo</p>
            <p>• يدعم TLS 1.2 و TLS 1.3</p>
            <p>• Headers الأمان مُفعلة</p>
            <p>• HSTS مُفعل</p>
        </div>
    </div>
</body>
</html>`;
}

// إنشاء خادم HTTPS للمنفذ 7443
const httpsServer7443 = https.createServer(sslOptions, (req, res) => {
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('X-Database', 'mohammi');
  res.setHeader('X-SSL-Port', '7443');
  
  res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
  res.end(createSSLInfoPage(7443, 'mohammi'));
});

// إنشاء خادم HTTPS للمنفذ 8914
const httpsServer8914 = https.createServer(sslOptions, (req, res) => {
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('X-Database', 'rubaie');
  res.setHeader('X-SSL-Port', '8914');
  
  res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
  res.end(createSSLInfoPage(8914, 'rubaie'));
});

// خوادم HTTP لإعادة التوجيه
const httpServer7080 = http.createServer((req, res) => {
  const httpsUrl = `https://${req.headers.host.split(':')[0]}:7443${req.url}`;
  res.writeHead(301, { 'Location': httpsUrl });
  res.end();
});

const httpServer8080 = http.createServer((req, res) => {
  const httpsUrl = `https://${req.headers.host.split(':')[0]}:8914${req.url}`;
  res.writeHead(301, { 'Location': httpsUrl });
  res.end();
});

// بدء جميع الخوادم
console.log('\n🚀 بدء خوادم SSL...');

// خادم HTTPS 7443
httpsServer7443.listen(PORTS.https_7443, '0.0.0.0', (err) => {
  if (err) {
    console.error(`❌ فشل في بدء HTTPS:7443 - ${err.message}`);
  } else {
    console.log('✅ خادم HTTPS يعمل على المنفذ 7443 (mohammi)');
    console.log('   🌐 https://mohammi.com:7443');
    console.log('   🌐 https://***********:7443');
  }
});

// خادم HTTPS 8914
httpsServer8914.listen(PORTS.https_8914, '0.0.0.0', (err) => {
  if (err) {
    console.error(`❌ فشل في بدء HTTPS:8914 - ${err.message}`);
  } else {
    console.log('✅ خادم HTTPS يعمل على المنفذ 8914 (rubaie)');
    console.log('   🌐 https://mohammi.com:8914');
    console.log('   🌐 https://***********:8914');
  }
});

// خادم HTTP 7080
httpServer7080.listen(PORTS.http_7080, '0.0.0.0', (err) => {
  if (err) {
    console.log(`⚠️ لم يتم بدء HTTP:7080 - ${err.message}`);
  } else {
    console.log('✅ خادم HTTP يعمل على المنفذ 7080 (إعادة توجيه → 7443)');
  }
});

// خادم HTTP 8080
httpServer8080.listen(PORTS.http_8080, '0.0.0.0', (err) => {
  if (err) {
    console.log(`⚠️ لم يتم بدء HTTP:8080 - ${err.message}`);
  } else {
    console.log('✅ خادم HTTP يعمل على المنفذ 8080 (إعادة توجيه → 8914)');
  }
});

// معلومات نهائية
setTimeout(() => {
  console.log('\n🎉 تم تشغيل SSL بنجاح!');
  console.log('='.repeat(50));
  console.log('🔐 روابط HTTPS:');
  console.log('   • https://mohammi.com:7443 (قاعدة البيانات mohammi)');
  console.log('   • https://mohammi.com:8914 (قاعدة البيانات rubaie)');
  console.log('   • https://***********:7443 (IP مباشر)');
  console.log('   • https://***********:8914 (IP مباشر)');
  
  console.log('\n🔄 روابط HTTP (إعادة توجيه):');
  console.log('   • http://mohammi.com:7080 → https://mohammi.com:7443');
  console.log('   • http://mohammi.com:8080 → https://mohammi.com:8914');
  
  console.log('\n🧪 اختبار الشهادة:');
  console.log('   • https://www.ssllabs.com/ssltest/analyze.html?d=mohammi.com:7443');
  console.log('   • https://www.ssllabs.com/ssltest/analyze.html?d=mohammi.com:8914');
  
  console.log('\n⏳ الخوادم تعمل... اضغط Ctrl+C للإيقاف');
}, 1000);

// معالجة إيقاف الخوادم
process.on('SIGINT', () => {
  console.log('\n🛑 إيقاف خوادم SSL...');
  httpsServer7443.close(() => console.log('✅ تم إيقاف HTTPS:7443'));
  httpsServer8914.close(() => console.log('✅ تم إيقاف HTTPS:8914'));
  httpServer7080.close(() => console.log('✅ تم إيقاف HTTP:7080'));
  httpServer8080.close(() => console.log('✅ تم إيقاف HTTP:8080'));
  process.exit(0);
});
