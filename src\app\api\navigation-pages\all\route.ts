import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب جميع صفحات التنقل
export async function GET() {
  try {
    // ضمان وجود الجدول
    await query(`
      CREATE TABLE IF NOT EXISTS navigation_pages (
        id SERIAL PRIMARY KEY,
        page_title VARCHAR(255) NOT NULL,
        page_url VARCHAR(512) UNIQUE NOT NULL,
        page_description TEXT DEFAULT '',
        category VARCHAR(100) DEFAULT '',
        keywords VARCHAR(255) DEFAULT '',
        is_active BOOLEAN DEFAULT TRUE,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    const result = await query(`
      SELECT 
        id,
        page_title,
        page_url,
        page_description,
        category,
        keywords,
        is_active,
        created_date,
        updated_at
      FROM navigation_pages
      ORDER BY category, page_title
    `)

    return NextResponse.json({
      success: true,
      data: result.rows,
      total: result.rows.length,
      message: 'تم جلب جميع الصفحات بنجاح'
    })

  } catch (error) {
    console.error('خطأ في جلب الصفحات:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب الصفحات',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
