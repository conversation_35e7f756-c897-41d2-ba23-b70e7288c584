import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب قيد واحد مع تفاصيله
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url)
    const includeDetails = searchParams.get('include_details') === 'true'
    const { id: entryId } = params

    // جلب القيد الرئيسي
    const entryResult = await query(
      `SELECT je.* FROM journal_entries je WHERE je.id = $1`,
      [entryId]
    )

    if (entryResult.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'القيد غير موجود'
      }, { status: 404 })
    }

    let entry = entryResult.rows[0]

    // جلب التفاصيل إذا طُلب ذلك
    if (includeDetails) {
      const detailsResult = await query(
        `SELECT 
           jed.*,
           COALESCE(ca.account_name, jed.account_name) AS account_name,
           ca.account_code
         FROM journal_entry_details jed
         LEFT JOIN chart_of_accounts ca ON jed.account_id = ca.id
         WHERE jed.journal_entry_id = $1
         ORDER BY jed.id`,
        [entryId]
      )
      entry.details = detailsResult.rows
    }

    return NextResponse.json({
      success: true,
      entry,
      message: 'تم جلب القيد بنجاح'
    })

  } catch (error) {
    console.error('خطأ في جلب القيد:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب القيد',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// PUT - تحديث قيد
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const { id: entryId } = params
    const { entry_date, description, status, details = [] } = body

    // التحقق من وجود القيد
    const existingEntry = await query('SELECT id, status FROM journal_entries WHERE id = $1', [entryId])

    if (existingEntry.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'القيد غير موجود'
      }, { status: 404 })
    }

    // منع تعديل القيود المعتمدة
    if (existingEntry.rows[0].status === 'approved') {
      return NextResponse.json({
        success: false,
        error: 'لا يمكن تعديل القيد المعتمد'
      }, { status: 400 })
    }

    // التحقق من توازن القيد
    // لا نتحقق من توازن القيد هنا؛ الواجهة يجب أن تضمن التوازن قبل الإرسال

    // بدء المعاملة
    await query('BEGIN')

    try {
      // تحديث القيد الرئيسي (حقول أساسية فقط لضمان التوافق)
      const result = await query(
        `UPDATE journal_entries
           SET entry_date = COALESCE($2, entry_date),
               description = COALESCE($3, description),
               status = COALESCE($4, status),
               updated_at = CURRENT_TIMESTAMP
         WHERE id = $1
         RETURNING *`,
        [entryId, entry_date || null, description || null, status || null]
      )

      // استبدال تفاصيل القيد إذا وردت
      if (Array.isArray(details) && details.length > 0) {
        await query('DELETE FROM journal_entry_details WHERE journal_entry_id = $1', [entryId])
        for (let i = 0; i < details.length; i++) {
          const d = details[i]
          // جلب اسم الحساب ورمزه إذا لم يرسل
          let accountName = d.account_name
          let accountCode = d.account_code
          if ((!accountName || !accountCode) && d.account_id) {
            const acc = await query('SELECT account_name, account_code FROM chart_of_accounts WHERE id=$1', [d.account_id])
            if (acc.rows[0]) {
              accountName = accountName || acc.rows[0].account_name
              accountCode = accountCode || acc.rows[0].account_code
            }
          }
          await query(
            `INSERT INTO journal_entry_details (
               journal_entry_id, account_id, account_name, account_code,
               debit_amount, credit_amount, description, line_order
             ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`,
            [
              entryId,
              d.account_id || null,
              accountName || null,
              accountCode || null,
              d.debit_amount || 0,
              d.credit_amount || 0,
              d.description || null,
              d.line_order || i + 1,
            ]
          )
        }
      }

      // تأكيد المعاملة
      await query('COMMIT')

      return NextResponse.json({ success: true, entry: result.rows[0], message: 'تم تحديث القيد اليومي بنجاح' })

    } catch (error) {
      // إلغاء المعاملة في حالة الخطأ
      await query('ROLLBACK')
      throw error
    }

  } catch (error) {
    console.error('خطأ في تحديث القيد اليومي:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث القيد اليومي',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// DELETE - حذف قيد
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: entryId } = await params

    // التحقق من وجود القيد وحالته
    const existingEntry = await query(
      'SELECT id, status FROM journal_entries WHERE id = $1',
      [entryId]
    )

    if (existingEntry.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'القيد غير موجود'
      }, { status: 404 })
    }

    // منع حذف القيود المعتمدة
    if (existingEntry.rows[0].status === 'approved') {
      return NextResponse.json({
        success: false,
        error: 'لا يمكن حذف القيد المعتمد'
      }, { status: 400 })
    }

    // بدء المعاملة
    await query('BEGIN')

    try {
      // حذف التفاصيل أولاً
      await query('DELETE FROM journal_entry_details WHERE journal_entry_id = $1', [entryId])

      // حذف القيد الرئيسي
      await query('DELETE FROM journal_entries WHERE id = $1', [entryId])

      // تأكيد المعاملة
      await query('COMMIT')

      return NextResponse.json({
        success: true,
        message: 'تم حذف القيد اليومي بنجاح'
      })

    } catch (error) {
      // إلغاء المعاملة في حالة الخطأ
      await query('ROLLBACK')
      throw error
    }

  } catch (error) {
    console.error('خطأ في حذف القيد اليومي:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في حذف القيد اليومي',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
