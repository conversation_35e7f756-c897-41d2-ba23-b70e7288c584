'use client'

import { useEffect, useState } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Plus, Edit, Trash2, Save, X, Building2 } from 'lucide-react'

interface Department {
  id: number
  name: string
  description?: string
  is_active?: boolean
}

export default function DepartmentsPage() {
  const [departments, setDepartments] = useState<Department[]>([])
  const [search, setSearch] = useState('')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalType, setModalType] = useState<'add' | 'edit'>('add')
  const [formData, setFormData] = useState({ id: 0, name: '', description: '', is_active: true })
  const [loading, setLoading] = useState(false)

  const fetchDepartments = async () => {
    const res = await fetch('/api/departments')
    const json = await res.json()
    if (json.success) setDepartments(json.data)
  }

  useEffect(() => {
    fetchDepartments()
  }, [])

  const openAdd = () => {
    setFormData({ id: 0, name: '', description: '', is_active: true })
    setModalType('add')
    setIsModalOpen(true)
  }

  const openEdit = (dept: Department) => {
    setFormData({ id: dept.id, name: dept.name, description: dept.description || '', is_active: !!dept.is_active })
    setModalType('edit')
    setIsModalOpen(true)
  }

  const handleSave = async () => {
    if (!formData.name.trim()) {
      alert('يرجى إدخال اسم القسم')
      return
    }
    setLoading(true)
    try {
      if (modalType === 'add') {
        const res = await fetch('/api/departments', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ name: formData.name.trim(), description: formData.description })
        })
        const json = await res.json()
        if (!json.success) throw new Error(json.error || 'فشل في الإضافة')
      } else {
        const res = await fetch('/api/departments', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(formData)
        })
        const json = await res.json()
        if (!json.success) throw new Error(json.error || 'فشل في التحديث')
      }
      setIsModalOpen(false)
      await fetchDepartments()
    } catch (e: any) {
      alert(e.message)
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id: number) => {
    if (!confirm('هل تريد حذف هذا القسم؟')) return
    const res = await fetch(`/api/departments?id=${id}`, { method: 'DELETE' })
    const json = await res.json()
    if (json.success) fetchDepartments()
    else alert(json.error || 'فشل الحذف')
  }

  const filtered = departments.filter(d =>
    d.name.toLowerCase().includes(search.toLowerCase()) ||
    (d.description || '').toLowerCase().includes(search.toLowerCase())
  )

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50">
        <div className="space-y-6 p-6 bg-white min-h-screen">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <Building2 className="h-8 w-8 mr-3 text-blue-600" />
                إدارة الأقسام
              </h1>
              <p className="text-gray-600 mt-1">إضافة وتعديل وحذف الأقسام المرجعية</p>
            </div>
            <div className="flex gap-2">
              <Button onClick={openAdd} className="bg-blue-600 hover:bg-blue-700">
                <Plus className="h-4 w-4 mr-2" />
                إضافة قسم
              </Button>
            </div>
          </div>

          <Card>
            <CardContent className="p-4">
              <Input placeholder="ابحث عن قسم..." value={search} onChange={e => setSearch(e.target.value)} />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>قائمة الأقسام ({filtered.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b bg-gray-50">
                      <th className="text-right p-3">#</th>
                      <th className="text-right p-3">الاسم</th>
                      <th className="text-right p-3">الوصف</th>
                      <th className="text-right p-3">الحالة</th>
                      <th className="text-center p-3">إجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filtered.map((d) => (
                      <tr key={d.id} className="border-b hover:bg-gray-50">
                        <td className="p-3">{d.id}</td>
                        <td className="p-3 font-medium">{d.name}</td>
                        <td className="p-3">{d.description || '-'}</td>
                        <td className="p-3">
                          <Badge className={d.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-700'}>
                            {d.is_active ? 'نشط' : 'موقوف'}
                          </Badge>
                        </td>
                        <td className="p-3">
                          <div className="flex justify-center gap-2">
                            <Button size="sm" variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200" onClick={() => openEdit(d)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline" className="bg-red-50 text-red-700 border-red-200" onClick={() => handleDelete(d.id)}>
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                    {filtered.length === 0 && (
                      <tr>
                        <td className="p-4 text-center text-gray-500" colSpan={5}>لا توجد أقسام</td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>

        {isModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-lg">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">{modalType === 'add' ? 'إضافة قسم' : 'تعديل قسم'}</h3>
                <Button variant="ghost" size="sm" onClick={() => setIsModalOpen(false)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-4">
                <div>
                  <Label>اسم القسم *</Label>
                  <Input value={formData.name} onChange={e => setFormData({ ...formData, name: e.target.value })} />
                </div>
                <div>
                  <Label>الوصف</Label>
                  <Input value={formData.description} onChange={e => setFormData({ ...formData, description: e.target.value })} />
                </div>
                {modalType === 'edit' && (
                  <div>
                    <Label>الحالة</Label>
                    <select
                      value={formData.is_active ? 'true' : 'false'}
                      onChange={e => setFormData({ ...formData, is_active: e.target.value === 'true' })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    >
                      <option value="true">نشط</option>
                      <option value="false">موقوف</option>
                    </select>
                  </div>
                )}
                <div className="flex gap-3">
                  <Button className="flex-1" onClick={handleSave} disabled={loading}>
                    <Save className="h-4 w-4 mr-2" /> حفظ
                  </Button>
                  <Button className="flex-1" variant="outline" onClick={() => setIsModalOpen(false)}>إلغاء</Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}
