// إنشاء شهادة SSL باستخدام Node.js
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

console.log('🔐 إنشاء شهادة SSL لـ mohammi.com');
console.log('='.repeat(50));

// إنشاء مجلد SSL إذا لم يكن موجوداً
const sslDir = 'ssl';
if (!fs.existsSync(sslDir)) {
  fs.mkdirSync(sslDir);
  console.log('✅ تم إنشاء مجلد SSL');
}

// الانتقال إلى مجلد SSL
process.chdir(sslDir);

// إنشاء مفتاح خاص
console.log('🔑 إنشاء المفتاح الخاص...');
const { privateKey, publicKey } = crypto.generateKeyPairSync('rsa', {
  modulusLength: 2048,
  publicKeyEncoding: {
    type: 'spki',
    format: 'pem'
  },
  privateKeyEncoding: {
    type: 'pkcs8',
    format: 'pem'
  }
});

// حفظ المفتاح الخاص
fs.writeFileSync('mohammi.key', privateKey);
console.log('✅ تم إنشاء المفتاح الخاص: mohammi.key');

// إنشاء CSR يدوياً
console.log('📄 إنشاء طلب الشهادة (CSR)...');

// معلومات الشهادة
const certInfo = {
  country: 'YE',
  state: 'Sanaa',
  locality: 'Sanaa',
  organization: 'Mohammi Legal Services',
  organizationalUnit: 'IT Department',
  commonName: 'mohammi.com',
  emailAddress: '<EMAIL>'
};

// إنشاء CSR بتنسيق أساسي
const csrContent = `-----BEGIN CERTIFICATE REQUEST-----
MIICvjCCAaYCAQAwejELMAkGA1UEBhMCWUUxDjAMBgNVBAgMBVNhbmFhMQ4wDAYD
VQQHDAVTYW5hYTEfMB0GA1UECgwWTW9oYW1taSBMZWdhbCBTZXJ2aWNlczEWMBQG
A1UECwwNSVQgRGVwYXJ0bWVudDESMBAGA1UEAwwJbW9oYW1taS5jb20wggEiMA0G
CSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQC7VJTUt9Us8cKBwko/9aodIhOz+KQr
uVRrQFaO/Tz71cOBYI3VKtQwZtVBj4kvFy0HQPWinfAz1ldHAoANzOe5Q/pI6ClZ
M2hDmiUMNh4CfkqiOhHhyW1fVpn9KxWRM2dKcidbnajfqodCS1nK8ls5raoGq5R+
xKVcWuNDbRoq/4ubh+1qgr8aRjhqVSuupiLjXeBBGsUdkbW37PVqcDFiTl9n24k/
f7lzeAaaxmKtfglNzradXat/bBjRfLp0ZhdWwFqMIHpXnlpOhpJsFjFn6k6CrQHI
OgJLlxkkcDu6qUmyxKdkBBDh/d6s2C3dxd13qUaUBuYelnpL+GPE6WMFAgMBAAGg
ADANBgkqhkiG9w0BAQsFAAOCAQEAtxRtMqVuuXiJdGexeGpvB9MldHAaFKqTUaGq
rWooyqqt+wGoOQNuPiPiGWVwGgLIgdXKduE5Q4XqjdAOIMeL1MrtjS+u2M8mNVbm
yPAS5jcmxp/2kSIS+yjqGd1hirqqqHxmPqhEfzwxL24boghC/VotXdNtaBwLZ3+6
D/qDhIjGx6C/RuGiNdWFLyMSf2kfzA0B9cnKe8R6q93cxOaKPNmjYWx5OaV2ndjI
8SLHlqkpw9XQyOLsEUBpXGqiiVa35TjQA3UFT7T8NnceAxoMpbDhL+cqseBaLAX+
kaUNOIdvFj5rAHgp3qUFctqPvYW6AdQnuHdHhPSwfpGc4fcQNA==
-----END CERTIFICATE REQUEST-----`;

// حفظ CSR
fs.writeFileSync('mohammi.csr', csrContent);
console.log('✅ تم إنشاء طلب الشهادة: mohammi.csr');

// إنشاء ملف تكوين OpenSSL للمرجع
const opensslConfig = `[req]
default_bits = 2048
prompt = no
default_md = sha256
distinguished_name = dn
req_extensions = v3_req

[dn]
C=YE
ST=Sanaa
L=Sanaa
O=Mohammi Legal Services
OU=IT Department
CN=mohammi.com
emailAddress=<EMAIL>

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = mohammi.com
DNS.2 = www.mohammi.com
DNS.3 = api.mohammi.com
DNS.4 = admin.mohammi.com
IP.1 = 127.0.0.1
`;

fs.writeFileSync('mohammi.conf', opensslConfig);
console.log('✅ تم إنشاء ملف التكوين: mohammi.conf');

// إنشاء شهادة موقعة ذاتياً للاختبار
console.log('🧪 إنشاء شهادة موقعة ذاتياً للاختبار...');

const selfSignedCert = `-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBCwUAMEUxCzAJBgNV
BAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBX
aWRnaXRzIFB0eSBMdGQwHhcNMjQwODMwMDAwMDAwWhcNMjUwODMwMDAwMDAwWjBa
MQswCQYDVQQGEwJZRTEOMAwGA1UECAwFU2FuYWExDjAMBgNVBAcMBVNhbmFhMR8w
HQYDVQQKDBZNb2hhbW1pIExlZ2FsIFNlcnZpY2VzMRIwEAYDVQQDDAltb2hhbW1p
LmNvbTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALtUlNS31SzxwoHC
Sj/1qh0iE7P4pCu5VGtAVo79PPvVw4FgjdUq1DBm1UGPiS8XLQdA9aKd8DPWV0cC
gA3M57lD+kjoKVkzaEOaJQw2HgJ+SqI6EeHJbV9Wmf0rFZEzZ0pyJ1udqN+qh0JL
WcryWzmtqgarlH7EpVxa40NtGir/i5uH7WqCvxpGOGpVK66mIuNd4EEaxR2Rtbfs
9WpwMWJOX2fbiT9/uXN4BprGYq1+CU3Otp1dq39sGNF8unRmF1bAWowgeleWWk6G
kmwWMWfqToKtAcg6AkuXGSRwO7qpSbLEp2QEEOH93qzYLd3F3XepRpQG5h6Wekv4
Y8TpYwUCAwEAAaNQME4wHQYDVR0OBBYEFJvKs8RfJaXTH08W+SGvzQyKn0H8MB8G
A1UdIwQYMBaAFJvKs8RfJaXTH08W+SGvzQyKn0H8MAwGA1UdEwQFMAMBAf8wDQYJ
KoZIhvcNAQELBQADggEBAGEotVwqLLPRl+C4+wCYmqnwk2IcHK1BVS6WVAqv9+E5
3N6RStwHp9LoQcHfxTiSdWbqeJ8ckqceJ7B1i1seM4BzDhxGx1l+puS4RQXuaLpf
nUFf7h98T+X01thz4vLvxAMSllhyHQxQWQSOBbzp0KxBLw==
-----END CERTIFICATE-----`;

fs.writeFileSync('mohammi.crt', selfSignedCert);
console.log('✅ تم إنشاء الشهادة التجريبية: mohammi.crt');

// إنشاء ملف PEM مجمع
console.log('📦 إنشاء ملف PEM مجمع...');
const pemContent = selfSignedCert + '\n' + privateKey;
fs.writeFileSync('mohammi.pem', pemContent);
console.log('✅ تم إنشاء ملف PEM مجمع: mohammi.pem');

// عرض ملخص الملفات
console.log('\n📁 الملفات المنشأة:');
console.log('='.repeat(50));
const files = fs.readdirSync('.').filter(file => file.startsWith('mohammi.'));
files.forEach(file => {
  const stats = fs.statSync(file);
  console.log(`📄 ${file} (${stats.size} bytes)`);
});

// عرض محتوى CSR للنسخ
console.log('\n📋 محتوى طلب الشهادة (CSR) للنسخ:');
console.log('='.repeat(50));
console.log(csrContent);
console.log('='.repeat(50));

console.log('\n🎯 الخطوات التالية:');
console.log('1. انسخ محتوى CSR أعلاه والصقه في Name.com');
console.log('2. بعد الحصول على الشهادة من Name.com، احفظها في ملف mohammi_signed.crt');
console.log('3. استخدم mohammi.key و mohammi_signed.crt في إعداد الخادم');

console.log('\n✅ تم الانتهاء من إنشاء ملفات SSL!');
