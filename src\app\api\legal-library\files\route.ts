import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'
import fs from 'fs'
import path from 'path'

// دالة للحصول على مسار المكتبة القانونية من الإعدادات
async function getLegalLibraryPath(): Promise<string> {
  try {
    const result = await query(
      'SELECT setting_value FROM system_settings WHERE setting_key = $1',
      ['legal_library_path']
    )

    if (result.rows.length > 0) {
      return result.rows[0].setting_value
    }

    // المسار الافتراضي
    return '/home/<USER>/Downloads/legal-system/laws'
  } catch (error) {
    console.error('خطأ في جلب مسار المكتبة القانونية:', error)
    return '/home/<USER>/Downloads/legal-system/laws'
  }
}

// دالة للحصول على الإعدادات
async function getLibrarySettings() {
  try {
    const result = await query(`
      SELECT setting_key, setting_value, setting_type 
      FROM system_settings 
      WHERE setting_key LIKE 'legal_library_%'
    `)

    const settings = {}
    result.rows.forEach(row => {
      let value = row.setting_value

      // تحويل القيم حسب النوع
      if (row.setting_type === 'boolean') {
        value = value === 'true'
      } else if (row.setting_type === 'number') {
        value = parseInt(value)
      }

      settings[row.setting_key] = value
    })

    return settings
  } catch (error) {
    console.error('خطأ في جلب إعدادات المكتبة:', error)
    return {}
  }
}

// دالة للتحقق من وجود المجلد وإنشاؤه إذا لم يكن موجوداً
async function ensureDirectoryExists(dirPath: string): Promise<boolean> {
  try {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true })

    }
    return true
  } catch (error) {
    console.error('خطأ في إنشاء المجلد:', error)
    return false
  }
}

// دالة لفحص الملفات في المجلد
async function scanDirectoryFiles(dirPath: string, allowedExtensions: string[]): Promise<any[]> {
  try {
    if (!fs.existsSync(dirPath)) {
      return []
    }

    const files = fs.readdirSync(dirPath)
    const fileList = []

    for (const file of files) {
      const filePath = path.join(dirPath, file)
      const stats = fs.statSync(filePath)

      if (stats.isFile()) {
        const ext = path.extname(file).toLowerCase()

        if (allowedExtensions.length === 0 || allowedExtensions.includes(ext)) {
          fileList.push({
            name: file,
            path: filePath,
            size: stats.size,
            extension: ext,
            modified: stats.mtime,
            created: stats.birthtime
          })
        }
      }
    }

    return fileList.sort((a, b) => b.modified.getTime() - a.modified.getTime())
  } catch (error) {
    console.error('خطأ في فحص الملفات:', error)
    return []
  }
}

// GET - جلب قائمة الملفات من المجلد
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action') || 'list'

    const settings = await getLibrarySettings()
    const libraryPath = await getLegalLibraryPath()

    if (action === 'settings') {
      // إرجاع الإعدادات
      return NextResponse.json({
        success: true,
        data: {
          path: libraryPath,
          settings: settings
        }
      })
    }

    if (action === 'list') {
      // التحقق من وجود المجلد
      const directoryExists = await ensureDirectoryExists(libraryPath)

      if (!directoryExists) {
        return NextResponse.json({
          success: false,
          error: `تعذر الوصول إلى المجلد: ${libraryPath}`
        }, { status: 500 })
      }

      // جلب الامتدادات المسموحة
      const allowedExtensions = settings.legal_library_allowed_extensions 
        ? settings.legal_library_allowed_extensions.split(',').map(ext => ext.trim())
        : ['.pdf', '.doc', '.docx', '.txt']

      // فحص الملفات
      const files = await scanDirectoryFiles(libraryPath, allowedExtensions)

      return NextResponse.json({
        success: true,
        data: {
          path: libraryPath,
          files: files,
          totalFiles: files.length,
          settings: settings
        }
      })
    }

    return NextResponse.json(
      { success: false, error: 'إجراء غير صحيح' },
      { status: 400 }
    )

  } catch (error) {
    console.error('خطأ في جلب ملفات المكتبة القانونية:', error)
    return NextResponse.json(
      { success: false, error: 'خطأ في جلب الملفات' },
      { status: 500 }
    )
  }
}

// POST - مزامنة الملفات مع قاعدة البيانات
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action } = body

    if (action === 'sync') {
      const settings = await getLibrarySettings()
      const libraryPath = await getLegalLibraryPath()

      // التحقق من وجود المجلد
      const directoryExists = await ensureDirectoryExists(libraryPath)

      if (!directoryExists) {
        return NextResponse.json({
          success: false,
          error: `تعذر الوصول إلى المجلد: ${libraryPath}`
        }, { status: 500 })
      }

      // جلب الامتدادات المسموحة
      const allowedExtensions = settings.legal_library_allowed_extensions 
        ? settings.legal_library_allowed_extensions.split(',').map(ext => ext.trim())
        : ['.pdf', '.doc', '.docx', '.txt']

      // فحص الملفات
      const files = await scanDirectoryFiles(libraryPath, allowedExtensions)

      // مزامنة مع قاعدة البيانات
      let syncedCount = 0
      let errorCount = 0

      for (const file of files) {
        try {
          // التحقق من وجود الملف في قاعدة البيانات
          const existingResult = await query(
            'SELECT id FROM legal_library WHERE file_path = $1',
            [file.path]
          )

          if (existingResult.rows.length === 0) {
            // إضافة الملف الجديد
            await query(`
              INSERT INTO legal_library (
                title, description, file_path, file_size, 
                file_type, category, is_active, created_date
              ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            `, [
              path.basename(file.name, file.extension), // العنوان بدون الامتداد
              `ملف تم اكتشافه تلقائياً من المجلد`, // الوصف
              file.path, // مسار الملف
              file.size, // حجم الملف
              file.extension, // نوع الملف
              'عام', // الفئة الافتراضية
              true, // نشط
              new Date() // تاريخ الإنشاء
            ])

            syncedCount++
          }
        } catch (fileError) {
          console.error(`خطأ في مزامنة الملف ${file.name}:`, fileError)
          errorCount++
        }
      }

      return NextResponse.json({
        success: true,
        data: {
          totalFiles: files.length,
          syncedFiles: syncedCount,
          errorFiles: errorCount,
          path: libraryPath
        },
        message: `تم مزامنة ${syncedCount} ملف جديد`
      })
    }

    return NextResponse.json(
      { success: false, error: 'إجراء غير صحيح' },
      { status: 400 }
    )

  } catch (error) {
    console.error('خطأ في مزامنة ملفات المكتبة القانونية:', error)
    return NextResponse.json(
      { success: false, error: 'خطأ في المزامنة' },
      { status: 500 }
    )
  }
}
