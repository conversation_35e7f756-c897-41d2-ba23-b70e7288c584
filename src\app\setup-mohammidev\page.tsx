'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Loader2, Database, CheckCircle, XCircle, ArrowRight, Settings } from 'lucide-react'

interface SetupResult {
  success: boolean
  message: string
  details?: {
    table_created: boolean
    data_added: boolean
    total_records: number
    sample_data?: Array<{
      case_number: string
      movement_type: string
      priority: string
      created_at: string
    }>
  }
  next_steps?: string[]
  error?: string
}

export default function SetupMohammidevPage() {
  const [setting, setSetting] = useState(false)
  const [result, setResult] = useState<SetupResult | null>(null)

  const setupDatabase = async () => {
    setSetting(true)
    setResult(null)
    
    try {
      const response = await fetch('/api/setup-mohammidev', {
        method: 'POST'
      })
      const data = await response.json()
      setResult(data)
    } catch (error) {
      console.error('خطأ في إعداد قاعدة البيانات:', error)
      setResult({
        success: false,
        message: 'فشل في الاتصال بالخادم',
        error: 'تحقق من تشغيل الخادم وحاول مرة أخرى'
      })
    } finally {
      setSetting(false)
    }
  }

  const goToMovements = () => {
    window.location.href = '/movements'
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl" dir="rtl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">إعداد قاعدة بيانات mohammidev</h1>
        <p className="text-gray-600">
          ربط النظام بقاعدة بيانات PostgreSQL للتطوير وإعداد جدول حركة القضايا
        </p>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            إعداد قاعدة البيانات
          </CardTitle>
          <CardDescription>
            سيتم إنشاء جدول case_movements في قاعدة بيانات mohammidev مع بيانات تجريبية
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-start gap-2">
                <Settings className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <h3 className="font-medium text-blue-800">إعدادات الاتصال</h3>
                  <div className="text-blue-700 text-sm mt-1 space-y-1">
                    <p>• الخادم: localhost:5432</p>
                    <p>• قاعدة البيانات: mohammidev</p>
                    <p>• المستخدم: postgres</p>
                    <p>• كلمة المرور: yemen123</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 border rounded-lg">
                <h3 className="font-medium mb-2">ما سيتم إنشاؤه:</h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• جدول case_movements</li>
                  <li>• الفهارس المطلوبة</li>
                  <li>• 12 سجل تجريبي</li>
                  <li>• 6 قضايا مختلفة</li>
                </ul>
              </div>
              
              <div className="p-4 border rounded-lg">
                <h3 className="font-medium mb-2">أنواع الحركات:</h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• إنشاء القضايا</li>
                  <li>• توزيع القضايا</li>
                  <li>• جدولة الجلسات</li>
                  <li>• رفع الوثائق</li>
                  <li>• إضافة المتابعات</li>
                  <li>• تغيير الحالات</li>
                </ul>
              </div>
            </div>

            <Button 
              onClick={setupDatabase} 
              disabled={setting}
              className="w-full"
              size="lg"
            >
              {setting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  جاري الإعداد...
                </>
              ) : (
                <>
                  <Database className="mr-2 h-4 w-4" />
                  إعداد قاعدة البيانات
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {result && (
        <div className="space-y-6">
          {/* نتيجة الإعداد */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {result.success ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-500" />
                )}
                نتيجة الإعداد
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className={`p-4 rounded-lg ${result.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                <p className={`font-medium ${result.success ? 'text-green-800' : 'text-red-800'}`}>
                  {result.message}
                </p>
                {result.error && (
                  <p className="text-red-600 text-sm mt-2">{result.error}</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* تفاصيل الإعداد */}
          {result.success && result.details && (
            <Card>
              <CardHeader>
                <CardTitle>تفاصيل الإعداد</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{result.details.total_records}</div>
                    <div className="text-sm text-blue-800">إجمالي السجلات</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {result.details.table_created ? 'تم' : 'موجود'}
                    </div>
                    <div className="text-sm text-green-800">إنشاء الجدول</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">
                      {result.details.data_added ? 'تم' : 'موجود'}
                    </div>
                    <div className="text-sm text-purple-800">إضافة البيانات</div>
                  </div>
                </div>

                {result.details.sample_data && result.details.sample_data.length > 0 && (
                  <div className="mb-4">
                    <label className="text-sm font-medium text-gray-500 block mb-2">عينة من البيانات</label>
                    <div className="space-y-2">
                      {result.details.sample_data.map((movement, index) => (
                        <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                          <span className="font-medium">{movement.case_number}</span>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">{movement.movement_type}</Badge>
                            <Badge variant={movement.priority === 'urgent' ? 'destructive' : movement.priority === 'high' ? 'default' : 'secondary'}>
                              {movement.priority}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <Button onClick={goToMovements} className="w-full" size="lg">
                  <ArrowRight className="mr-2 h-4 w-4" />
                  الذهاب لصفحة حركة القضايا
                </Button>
              </CardContent>
            </Card>
          )}

          {/* الخطوات التالية */}
          {result.next_steps && result.next_steps.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>الخطوات التالية</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {result.next_steps.map((step, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span className="text-sm">{step}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  )
}
