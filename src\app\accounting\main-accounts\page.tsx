'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Input } from '@/components/ui/input'
import {
  Link2,
  Save,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  BookOpen,
  Settings,
  Search,
  X,
  Plus,
  Edit,
  Trash2
} from 'lucide-react'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'

interface MainAccount {
  id: number
  account_name: string
  account_code?: string
  chart_account_id?: number
  chart_account_code?: string
}

interface ChartAccount {
  id: number
  account_code: string
  account_name: string
  account_type: string
  account_level: number
}

export default function MainAccountsPage() {
  const [mainAccounts, setMainAccounts] = useState<MainAccount[]>([])
  const [chartAccounts, setChartAccounts] = useState<ChartAccount[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)

  // حالة الربط لكل حساب رئيسي
  const [accountLinks, setAccountLinks] = useState<{ [key: number]: number | null }>({})

  // حالة البحث
  const [searchTerms, setSearchTerms] = useState<{ [key: number]: string }>({})

  // حالة النموذج
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalType, setModalType] = useState<'add' | 'edit'>('add')
  const [editingAccount, setEditingAccount] = useState<MainAccount | null>(null)
  const [formData, setFormData] = useState({
    account_name: '',
    account_code: '',
    chart_account_id: null as number | null
  })

  // حالة البحث في النافذة المنبثقة
  const [modalSearchTerm, setModalSearchTerm] = useState('')
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)

  // تصفية الحسابات في النافذة المنبثقة
  const filteredChartAccounts = chartAccounts.filter(account =>
    account.account_name.toLowerCase().includes(modalSearchTerm.toLowerCase()) ||
    account.account_code.toLowerCase().includes(modalSearchTerm.toLowerCase())
  )

  // دالة للبحث عن الحساب برقم الحساب وحقن الاسم
  const handleAccountCodeChange = (code: string) => {
    setFormData(prev => ({ ...prev, account_code: code }))

    // البحث عن الحساب المطابق
    const matchingAccount = chartAccounts.find(account =>
      account.account_code.toLowerCase() === code.toLowerCase()
    )

    if (matchingAccount) {
      setFormData(prev => ({
        ...prev,
        account_name: matchingAccount.account_name,
        chart_account_id: matchingAccount.id
      }))
      setModalSearchTerm(`${matchingAccount.account_code} - ${matchingAccount.account_name}`)
    } else {
      // إذا لم يتم العثور على حساب مطابق، إزالة الربط
      setFormData(prev => ({
        ...prev,
        chart_account_id: null
      }))
      setModalSearchTerm('')
    }
  }

  // دالة للبحث عن الحساب باسم الحساب وحقن الرقم
  const handleAccountNameChange = (name: string) => {
    setFormData(prev => ({ ...prev, account_name: name }))

    // البحث عن الحساب المطابق
    const matchingAccount = chartAccounts.find(account =>
      account.account_name.toLowerCase() === name.toLowerCase()
    )

    if (matchingAccount) {
      setFormData(prev => ({
        ...prev,
        account_code: matchingAccount.account_code,
        chart_account_id: matchingAccount.id
      }))
      setModalSearchTerm(`${matchingAccount.account_code} - ${matchingAccount.account_name}`)
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  // إغلاق القائمة المنسدلة عند النقر خارجها
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      if (!target.closest('.dropdown-container')) {
        setIsDropdownOpen(false)
      }
    }

    if (isDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isDropdownOpen])

  const fetchData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/accounting/main-accounts')
      if (response.ok) {
        const data = await response.json()
        setMainAccounts(data.mainAccounts || [])
        setChartAccounts(data.chartAccounts || [])

        // تعيين الروابط الحالية
        const links: { [key: number]: number | null } = {}
        data.mainAccounts?.forEach((account: MainAccount) => {
          links[account.id] = account.chart_account_id || null
        })
        setAccountLinks(links)
      } else {
        setMessage({ type: 'error', text: 'فشل في جلب البيانات' })
      }
    } catch (error) {
      console.error('خطأ في جلب البيانات:', error)
      setMessage({ type: 'error', text: 'حدث خطأ في جلب البيانات' })
    } finally {
      setLoading(false)
    }
  }

  const handleAccountChange = (mainAccountId: number, chartAccountId: string) => {
    setAccountLinks(prev => ({
      ...prev,
      [mainAccountId]: chartAccountId === 'none' ? null : parseInt(chartAccountId)
    }))
  }

  const handleSearchChange = (mainAccountId: number, searchTerm: string) => {
    setSearchTerms(prev => ({
      ...prev,
      [mainAccountId]: searchTerm
    }))
  }

  const clearSearch = (mainAccountId: number) => {
    setSearchTerms(prev => ({
      ...prev,
      [mainAccountId]: ''
    }))
  }

  const getFilteredAccounts = (mainAccountId: number) => {
    const searchTerm = searchTerms[mainAccountId] || ''
    if (!searchTerm.trim()) {
      return chartAccounts
    }

    return chartAccounts.filter(account =>
      account.account_code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      account.account_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      account.account_type.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      setMessage(null)

      // تحضير البيانات للحفظ
      const updatedAccounts = mainAccounts.map(account => ({
        id: account.id,
        chart_account_id: accountLinks[account.id],
        account_code: accountLinks[account.id] ?
          chartAccounts.find(ca => ca.id === accountLinks[account.id])?.account_code : null
      }))

      const response = await fetch('/api/accounting/main-accounts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ mainAccounts: updatedAccounts }),
      })

      if (response.ok) {
        const result = await response.json()

        // عرض نتائج الربط التلقائي
        let linkingMessage = 'تم حفظ ربط الحسابات بنجاح'

        if (result.linking_results && result.linking_results.length > 0) {
          const totalUpdated = result.linking_results.reduce((sum: number, lr: any) => {
            if (lr.linking_result && lr.linking_result.success) {
              const details = lr.linking_result.details
              return sum + details.clients_updated + details.employees_updated + details.suppliers_updated
            }
            return sum
          }, 0)

          if (totalUpdated > 0) {
            linkingMessage += `\n🔗 تم ربط ${totalUpdated} سجل تلقائياً بالحسابات الفرعية`
          }
        }

        setMessage({ type: 'success', text: linkingMessage })
        await fetchData() // إعادة جلب البيانات لتحديث العرض
      } else {
        const errorData = await response.json()
        setMessage({ type: 'error', text: errorData.error || 'فشل في حفظ البيانات' })
      }
    } catch (error) {
      console.error('خطأ في الحفظ:', error)
      setMessage({ type: 'error', text: 'حدث خطأ أثناء الحفظ' })
    } finally {
      setSaving(false)
    }
  }

  // دوال إدارة النموذج
  const handleAdd = () => {
    setModalType('add')
    setEditingAccount(null)
    setFormData({
      account_name: '',
      account_code: '',
      chart_account_id: null
    })
    setModalSearchTerm('')
    setIsDropdownOpen(false)
    setIsModalOpen(true)
  }

  const handleEdit = (account: MainAccount) => {
    setModalType('edit')
    setEditingAccount(account)
    setFormData({
      account_name: account.account_name,
      account_code: account.account_code || '',
      chart_account_id: account.chart_account_id || null
    })

    // تعيين النص المناسب للحساب المربوط
    if (account.chart_account_id) {
      const linkedAccount = chartAccounts.find(ca => ca.id === account.chart_account_id)
      if (linkedAccount) {
        setModalSearchTerm(`${linkedAccount.account_code} - ${linkedAccount.account_name}`)
      }
    } else {
      setModalSearchTerm('')
    }

    setIsDropdownOpen(false)
    setIsModalOpen(true)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.account_name.trim()) {
      setMessage({ type: 'error', text: 'يرجى إدخال اسم الحساب' })
      return
    }

    try {
      setSaving(true)
      setMessage(null)

      const url = modalType === 'add'
        ? '/api/accounting/main-accounts'
        : `/api/accounting/main-accounts/${editingAccount?.id}`

      const method = modalType === 'add' ? 'POST' : 'PUT'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        const result = await response.json()

        let successMessage = modalType === 'add' ? 'تم إضافة الحساب بنجاح' : 'تم تحديث الحساب بنجاح'

        // إضافة تفاصيل التحديث للتعديل
        if (modalType === 'edit') {
          successMessage += '\n📝 تم تحديث:'
          successMessage += `\n• اسم الحساب: ${formData.account_name}`
          if (formData.account_code) {
            successMessage += `\n• رقم الحساب: ${formData.account_code}`
          }
          if (formData.chart_account_id) {
            const linkedAccount = chartAccounts.find(acc => acc.id === formData.chart_account_id)
            if (linkedAccount) {
              successMessage += `\n• مربوط بحساب: ${linkedAccount.account_code} - ${linkedAccount.account_name}`
            }
          }
        }

        // عرض نتائج الربط التلقائي
        if (result.linking_result && result.linking_result.success) {
          const details = result.linking_result.details
          const totalUpdated = details.clients_updated + details.employees_updated + details.suppliers_updated

          if (totalUpdated > 0) {
            successMessage += `\n🔗 تم ربط ${totalUpdated} سجل تلقائياً:`
            if (details.clients_updated > 0) successMessage += `\n👥 ${details.clients_updated} عميل`
            if (details.employees_updated > 0) successMessage += `\n👨‍💼 ${details.employees_updated} موظف`
            if (details.suppliers_updated > 0) successMessage += `\n🏪 ${details.suppliers_updated} مورد`
          }
        }

        setMessage({ type: 'success', text: successMessage })
        setIsModalOpen(false)
        await fetchData()
      } else {
        const errorData = await response.json()
        setMessage({ type: 'error', text: errorData.error || 'فشل في حفظ البيانات' })
      }
    } catch (error) {
      console.error('خطأ في الحفظ:', error)
      setMessage({ type: 'error', text: 'حدث خطأ أثناء الحفظ' })
    } finally {
      setSaving(false)
    }
  }

  const handleDelete = async (account: MainAccount) => {
    if (!confirm(`هل أنت متأكد من حذف الحساب "${account.account_name}"؟`)) {
      return
    }

    try {
      setSaving(true)
      setMessage(null)

      const response = await fetch(`/api/accounting/main-accounts/${account.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setMessage({ type: 'success', text: 'تم حذف الحساب بنجاح' })
        await fetchData()
      } else {
        const errorData = await response.json()
        setMessage({ type: 'error', text: errorData.error || 'فشل في حذف الحساب' })
      }
    } catch (error) {
      console.error('خطأ في الحذف:', error)
      setMessage({ type: 'error', text: 'حدث خطأ أثناء الحذف' })
    } finally {
      setSaving(false)
    }
  }

  const getAccountStatus = (mainAccount: MainAccount) => {
    return mainAccount.chart_account_id ? 'مربوط' : 'غير مربوط'
  }

  const getStatusColor = (mainAccount: MainAccount) => {
    return mainAccount.chart_account_id ? 'text-green-600' : 'text-orange-600'
  }

  const getStatusIcon = (mainAccount: MainAccount) => {
    return mainAccount.chart_account_id ? CheckCircle : AlertCircle
  }

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
          <span className="mr-2">جاري التحميل...</span>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان والأزرار */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 space-x-reverse">
            <Link2 className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">الحسابات المخصصة</h1>
              <p className="text-gray-600">إضافة وربط الحسابات الهامة من دليل الحسابات</p>
            </div>
          </div>

          <div className="flex items-center space-x-3 space-x-reverse">
            <Button
              onClick={handleAdd}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              <Plus className="h-4 w-4 ml-2" />
              إضافة حساب رئيسي
            </Button>
          </div>
          <div className="flex space-x-2 space-x-reverse">
            <Button
              onClick={fetchData}
              variant="outline"
              disabled={loading}
            >
              <RefreshCw className="h-4 w-4 ml-2" />
              تحديث
            </Button>
            <Button
              onClick={handleSave}
              disabled={saving}
              className="bg-green-600 hover:bg-green-700"
            >
              {saving ? (
                <RefreshCw className="h-4 w-4 ml-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 ml-2" />
              )}
              حفظ الربط
            </Button>
          </div>
        </div>

        {/* رسائل التنبيه */}
        {message && (
          <Alert className={message.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className={message.type === 'success' ? 'text-green-800' : 'text-red-800'}>
              {message.text}
            </AlertDescription>
          </Alert>
        )}

        {/* جدول الحسابات الرئيسية */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 space-x-reverse">
              <BookOpen className="h-5 w-5" />
              <span>الحسابات الرئيسية</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mainAccounts.map((mainAccount) => {
                const StatusIcon = getStatusIcon(mainAccount)
                const currentSelection = accountLinks[mainAccount.id]

                return (
                  <div key={mainAccount.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <StatusIcon className={`h-5 w-5 ${getStatusColor(mainAccount)}`} />
                        <div>
                          <h3 className="font-medium text-gray-900">{mainAccount.account_name}</h3>
                          {mainAccount.account_code && (
                            <div className="mt-1">
                              <Badge variant="outline" className="text-xs">
                                رقم الحساب: {mainAccount.account_code}
                              </Badge>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3 space-x-reverse">
                      {/* أزرار الإجراءات */}
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEdit(mainAccount)}
                          className="bg-yellow-50 hover:bg-yellow-100 text-yellow-700 border-yellow-200"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDelete(mainAccount)}
                          className="bg-red-50 hover:bg-red-100 text-red-700 border-red-200"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>

                      <Badge
                        variant={mainAccount.chart_account_id ? "default" : "secondary"}
                        className="text-xs"
                      >
                        {getAccountStatus(mainAccount)}
                      </Badge>

                      <div className="flex flex-col space-y-2">
                        {/* حقل البحث */}
                        <div className="relative">
                          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                          <Input
                            placeholder="ابحث في الحسابات..."
                            value={searchTerms[mainAccount.id] || ''}
                            onChange={(e) => handleSearchChange(mainAccount.id, e.target.value)}
                            className="w-80 pr-10 pl-8"
                          />
                          {searchTerms[mainAccount.id] && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => clearSearch(mainAccount.id)}
                              className="absolute left-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          )}
                        </div>

                        {/* قائمة الحسابات */}
                        <Select
                          value={currentSelection?.toString() || 'none'}
                          onValueChange={(value) => handleAccountChange(mainAccount.id, value)}
                        >
                          <SelectTrigger className="w-80">
                            <SelectValue placeholder="اختر الحساب من الدليل" />
                          </SelectTrigger>
                          <SelectContent className="max-h-60">
                            <SelectItem value="none">-- لم يتم الربط --</SelectItem>
                            {getFilteredAccounts(mainAccount.id).map((chartAccount) => (
                              <SelectItem key={chartAccount.id} value={chartAccount.id.toString()}>
                                <div className="flex items-center space-x-2 space-x-reverse">
                                  <Badge variant="outline" className="text-xs">
                                    {chartAccount.account_code}
                                  </Badge>
                                  <span className="truncate max-w-48">{chartAccount.account_name}</span>
                                </div>
                              </SelectItem>
                            ))}
                            {getFilteredAccounts(mainAccount.id).length === 0 && searchTerms[mainAccount.id] && (
                              <div className="p-2 text-center text-gray-500 text-sm">
                                لا توجد نتائج للبحث "{searchTerms[mainAccount.id]}"
                              </div>
                            )}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* نافذة إضافة/تعديل الحساب */}
        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>
                {modalType === 'add' ? 'إضافة حساب رئيسي جديد' : 'تعديل الحساب الرئيسي'}
              </DialogTitle>
            </DialogHeader>

            <form onSubmit={handleSubmit} className="space-y-4">
              {modalType === 'edit' && (
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                  <p className="text-sm text-yellow-800">
                    <strong>ملاحظة:</strong> عند تعديل الحساب الرئيسي:
                  </p>
                  <ul className="text-xs text-yellow-700 mt-1 mr-4">
                    <li>• سيتم تحديث اسم الحساب ورقم الحساب في الجدول</li>
                    <li>• إذا تم اختيار حساب جديد من الدليل، سيتم استبدال جميع البيانات</li>
                    <li>• سيتم حفظ رقم الحساب الجديد إذا لم يكن محفوظاً مسبقاً</li>
                  </ul>
                </div>
              )}
              <div>
                <Label htmlFor="account_name">اسم الحساب *</Label>
                <Input
                  id="account_name"
                  value={formData.account_name}
                  onChange={(e) => handleAccountNameChange(e.target.value)}
                  placeholder="مثال: حسابات العملاء (سيتم حقن رقم الحساب تلقائياً)"
                  required
                />
                {formData.account_name && chartAccounts.find(acc => acc.account_name === formData.account_name) && (
                  <div className="mt-1 p-2 bg-blue-50 border border-blue-200 rounded-md">
                    <p className="text-xs text-blue-700">
                      ✓ تم العثور على الحساب: <strong>{chartAccounts.find(acc => acc.account_name === formData.account_name)?.account_code}</strong>
                    </p>
                    <p className="text-xs text-blue-600">
                      نوع الحساب: {chartAccounts.find(acc => acc.account_name === formData.account_name)?.account_type}
                    </p>
                  </div>
                )}
              </div>

              <div>
                <Label htmlFor="account_code">رقم الحساب</Label>
                <Input
                  id="account_code"
                  value={formData.account_code}
                  onChange={(e) => handleAccountCodeChange(e.target.value)}
                  placeholder="مثال: 1001 (سيتم حقن اسم الحساب تلقائياً)"
                />
                {formData.account_code && chartAccounts.find(acc => acc.account_code === formData.account_code) && (
                  <div className="mt-1 p-2 bg-green-50 border border-green-200 rounded-md">
                    <p className="text-xs text-green-700">
                      ✓ تم العثور على الحساب: <strong>{chartAccounts.find(acc => acc.account_code === formData.account_code)?.account_name}</strong>
                    </p>
                    <p className="text-xs text-green-600">
                      نوع الحساب: {chartAccounts.find(acc => acc.account_code === formData.account_code)?.account_type}
                    </p>
                  </div>
                )}
              </div>

              <div>
                <Label htmlFor="chart_account">
                  ربط بحساب من الدليل (اختياري)
                  {modalType === 'edit' && (
                    <span className="text-xs text-orange-600 mr-2">
                      • سيتم تحديث رقم الحساب واسم الحساب عند الاختيار
                    </span>
                  )}
                </Label>

                <div className="relative dropdown-container">
                  <div className="relative">
                    <Input
                      placeholder="ابحث واختر حساب من الدليل..."
                      value={modalSearchTerm}
                      onChange={(e) => setModalSearchTerm(e.target.value)}
                      onFocus={() => setIsDropdownOpen(true)}
                      className="pr-10"
                    />
                    <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    {modalSearchTerm && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setModalSearchTerm('')
                          setFormData(prev => ({ ...prev, chart_account_id: null }))
                        }}
                        className="absolute left-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    )}
                  </div>

                  {/* القائمة المنسدلة */}
                  {isDropdownOpen && (
                    <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto">
                      <div
                        className="px-3 py-2 hover:bg-gray-100 cursor-pointer border-b"
                        onClick={() => {
                          setFormData(prev => ({ ...prev, chart_account_id: null }))
                          setModalSearchTerm('')
                          setIsDropdownOpen(false)
                        }}
                      >
                        <span className="text-gray-600">بدون ربط</span>
                      </div>

                      {filteredChartAccounts.map((account) => (
                        <div
                          key={account.id}
                          className="px-3 py-2 hover:bg-blue-50 cursor-pointer"
                          onClick={() => {
                            setFormData(prev => ({
                              ...prev,
                              chart_account_id: account.id,
                              account_code: account.account_code,
                              account_name: prev.account_name || account.account_name
                            }))
                            setModalSearchTerm(`${account.account_code} - ${account.account_name}`)
                            setIsDropdownOpen(false)
                          }}
                        >
                          <div className="flex items-center justify-between">
                            <span className="text-sm">{account.account_code} - {account.account_name}</span>
                            <Badge variant="outline" className="text-xs">
                              {account.account_type}
                            </Badge>
                          </div>
                        </div>
                      ))}

                      {filteredChartAccounts.length === 0 && modalSearchTerm && (
                        <div className="px-3 py-2 text-center text-gray-500 text-sm">
                          لا توجد نتائج للبحث "{modalSearchTerm}"
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>

              <div className="flex justify-end space-x-2 space-x-reverse pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsModalOpen(false)}
                  disabled={saving}
                >
                  إلغاء
                </Button>
                <Button
                  type="submit"
                  disabled={saving}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {saving ? (
                    <>
                      <RefreshCw className="h-4 w-4 animate-spin ml-2" />
                      جاري الحفظ...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 ml-2" />
                      {modalType === 'add' ? 'إضافة' : 'تحديث'}
                    </>
                  )}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  )
}