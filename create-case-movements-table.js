const { Client } = require('pg');

async function createCaseMovementsTable() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    user: 'postgres',
    password: 'yemen123',
    database: 'mohammidev'
  });

  try {
    await client.connect();
    console.log('🔗 متصل بقاعدة البيانات');

    // إنشاء جدول حركة القضايا الجديد
    await client.query(`
      CREATE TABLE IF NOT EXISTS case_movements (
        id SERIAL PRIMARY KEY,
        case_id INTEGER NOT NULL REFERENCES issues(id) ON DELETE CASCADE,
        case_number VARCHAR(50),
        case_title VARCHAR(255),
        movement_type VARCHAR(100) NOT NULL, -- 'case_created', 'case_assigned', 'hearing_scheduled', 'follow_added', 'document_uploaded', 'status_changed', etc.
        description TEXT NOT NULL,
        details JSONB, -- تفاصيل إضافية حسب نوع الحركة
        
        -- معلومات الجلسة (إذا كانت الحركة متعلقة بجلسة)
        hearing_date DATE,
        hearing_time TIME,
        court_name VARCHAR(255),
        
        -- معلومات المستخدم
        created_by INTEGER,
        created_by_name VARCHAR(255),
        user_role VARCHAR(100),
        
        -- معلومات التوقيت
        movement_date DATE DEFAULT CURRENT_DATE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        -- أولوية الحركة (للإشعارات)
        priority VARCHAR(20) DEFAULT 'normal', -- low, normal, high, urgent
        
        -- حالة الحركة
        status VARCHAR(50) DEFAULT 'active', -- active, archived, deleted
        
        -- ملاحظات إضافية
        notes TEXT,
        
        -- معرف مرجعي (للربط مع جداول أخرى)
        reference_id INTEGER,
        reference_type VARCHAR(50) -- 'follow', 'document', 'hearing', etc.
      )
    `);

    console.log('✅ تم إنشاء جدول case_movements بنجاح');

    // إنشاء الفهارس
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_case_movements_case_id ON case_movements(case_id);
      CREATE INDEX IF NOT EXISTS idx_case_movements_type ON case_movements(movement_type);
      CREATE INDEX IF NOT EXISTS idx_case_movements_date ON case_movements(movement_date);
      CREATE INDEX IF NOT EXISTS idx_case_movements_hearing_date ON case_movements(hearing_date);
      CREATE INDEX IF NOT EXISTS idx_case_movements_priority ON case_movements(priority);
      CREATE INDEX IF NOT EXISTS idx_case_movements_status ON case_movements(status);
      CREATE INDEX IF NOT EXISTS idx_case_movements_created_by ON case_movements(created_by);
    `);

    console.log('✅ تم إنشاء الفهارس بنجاح');

    // إنشاء دالة لإضافة حركة تلقائياً
    await client.query(`
      CREATE OR REPLACE FUNCTION add_case_movement(
        p_case_id INTEGER,
        p_movement_type VARCHAR(100),
        p_description TEXT,
        p_details JSONB DEFAULT NULL,
        p_hearing_date DATE DEFAULT NULL,
        p_hearing_time TIME DEFAULT NULL,
        p_court_name VARCHAR(255) DEFAULT NULL,
        p_created_by INTEGER DEFAULT NULL,
        p_created_by_name VARCHAR(255) DEFAULT NULL,
        p_priority VARCHAR(20) DEFAULT 'normal',
        p_reference_id INTEGER DEFAULT NULL,
        p_reference_type VARCHAR(50) DEFAULT NULL
      ) RETURNS INTEGER AS $$
      DECLARE
        v_case_number VARCHAR(50);
        v_case_title VARCHAR(255);
        v_movement_id INTEGER;
      BEGIN
        -- جلب معلومات القضية
        SELECT case_number, title INTO v_case_number, v_case_title
        FROM issues WHERE id = p_case_id;
        
        -- إدراج الحركة
        INSERT INTO case_movements (
          case_id, case_number, case_title, movement_type, description, details,
          hearing_date, hearing_time, court_name, created_by, created_by_name,
          priority, reference_id, reference_type
        ) VALUES (
          p_case_id, v_case_number, v_case_title, p_movement_type, p_description, p_details,
          p_hearing_date, p_hearing_time, p_court_name, p_created_by, p_created_by_name,
          p_priority, p_reference_id, p_reference_type
        ) RETURNING id INTO v_movement_id;
        
        RETURN v_movement_id;
      END;
      $$ LANGUAGE plpgsql;
    `);

    console.log('✅ تم إنشاء دالة add_case_movement بنجاح');

    // إدراج بيانات تجريبية
    await client.query(`
      INSERT INTO case_movements (
        case_id, case_number, case_title, movement_type, description, 
        created_by_name, priority, movement_date
      ) 
      SELECT 
        i.id,
        i.case_number,
        i.title,
        'case_created',
        'تم إنشاء القضية في النظام',
        'النظام',
        'normal',
        i.created_date::date
      FROM issues i
      WHERE NOT EXISTS (
        SELECT 1 FROM case_movements cm 
        WHERE cm.case_id = i.id AND cm.movement_type = 'case_created'
      )
      LIMIT 10
    `);

    console.log('✅ تم إدراج البيانات التجريبية بنجاح');

  } catch (error) {
    console.error('❌ خطأ:', error);
  } finally {
    await client.end();
  }
}

createCaseMovementsTable();
