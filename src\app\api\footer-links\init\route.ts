import { NextResponse } from 'next/server'
import { query } from '@/lib/database'

export async function POST() {
  try {
    // إنشاء جدول روابط التذييل
    await query(`
      CREATE TABLE IF NOT EXISTS footer_links (
        id SERIAL PRIMARY KEY,
        category VARCHAR(100) NOT NULL,
        name VARCHAR(200) NOT NULL,
        href VARCHAR(500) NOT NULL,
        sort_order INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // إضافة عمود الإحداثيات لجدول الشركة إذا لم يكن موجوداً
    try {
      await query(`
        ALTER TABLE companies 
        ADD COLUMN IF NOT EXISTS latitude DECIMAL(10, 8),
        ADD COLUMN IF NOT EXISTS longitude DECIMAL(11, 8),
        ADD COLUMN IF NOT EXISTS map_zoom INTEGER DEFAULT 15
      `)
    } catch (error) {

    }

    // إدراج البيانات الافتراضية لروابط التذييل
    const defaultLinks = [
      // روابط سريعة
      { category: 'روابط سريعة', name: 'الرئيسية', href: '/home', sort_order: 1 },
      { category: 'روابط سريعة', name: 'خدماتنا', href: '/serviceslow', sort_order: 2 },
      { category: 'روابط سريعة', name: 'المكتبة القانونية', href: '/home#library', sort_order: 3 },
      { category: 'روابط سريعة', name: 'من نحن', href: '/home#about', sort_order: 4 },
      { category: 'روابط سريعة', name: 'فريق العمل', href: '/home#team', sort_order: 5 },
      { category: 'روابط سريعة', name: 'اتصل بنا', href: '/home#contact', sort_order: 6 },

      // خدماتنا
      { category: 'خدماتنا', name: 'القانون الجنائي والدفاع', href: '/serviceslow/criminal-law-defense', sort_order: 1 },
      { category: 'خدماتنا', name: 'صياغة العقود والاتفاقيات', href: '/serviceslow/contracts-agreements', sort_order: 2 },
      { category: 'خدماتنا', name: 'قانون الشركات والاستثمار', href: '/serviceslow/qnwn-lshrkt-wlstthmr-tjrb', sort_order: 3 },
      { category: 'خدماتنا', name: 'قانون العمل والعلاقات الصناعية', href: '/serviceslow/labor-industrial-relations', sort_order: 4 },
      { category: 'خدماتنا', name: 'القانون المدني والأحوال الشخصية', href: '/serviceslow/civil-personal-status-law', sort_order: 5 },
      { category: 'خدماتنا', name: 'جميع الخدمات', href: '/serviceslow', sort_order: 6 },

      // المستندات القانونية
      { category: 'المستندات القانونية', name: 'نماذج عقود', href: '/home#library', sort_order: 1 },
      { category: 'المستندات القانونية', name: 'لوائح وأنظمة', href: '/home#library', sort_order: 2 },
      { category: 'المستندات القانونية', name: 'أحكام قضائية', href: '/home#library', sort_order: 3 },
      { category: 'المستندات القانونية', name: 'أبحاث قانونية', href: '/home#library', sort_order: 4 },
      { category: 'المستندات القانونية', name: 'أسئلة متكررة', href: '/home#faq', sort_order: 5 },
      { category: 'المستندات القانونية', name: 'المزيد', href: '/home#library', sort_order: 6 }
    ]

    // حذف البيانات الموجودة وإدراج الجديدة
    await query('DELETE FROM footer_links')

    for (const link of defaultLinks) {
      await query(`
        INSERT INTO footer_links (category, name, href, sort_order, is_active)
        VALUES ($1, $2, $3, $4, $5)
      `, [link.category, link.name, link.href, link.sort_order, true])
    }

    return NextResponse.json({
      success: true,
      message: 'تم إنشاء جدول روابط التذييل وإضافة البيانات الافتراضية بنجاح'
    })
  } catch (error) {
    console.error('خطأ في إنشاء جدول روابط التذييل:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إنشاء الجدول' },
      { status: 500 }
    )
  }
}
