/**
 * Hook للتحقق من صحة الجلسة وتطبيق نظام الجلسة الواحدة
 */

import { useEffect, useCallback, useRef } from 'react'
import { useRouter } from 'next/navigation'

interface SessionValidatorOptions {
  checkInterval?: number // بالميلي ثانية، افتراضي: 30 ثانية
  onSessionExpired?: () => void
  onSessionTerminated?: () => void
  enabled?: boolean
}

export function useSessionValidator(options: SessionValidatorOptions = {}) {
  const {
    checkInterval = 30000, // 30 ثانية
    onSessionExpired,
    onSessionTerminated,
    enabled = true
  } = options
  
  const router = useRouter()
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const isCheckingRef = useRef(false)
  
  const checkSession = useCallback(async () => {
    // تجنب التحقق المتزامن
    if (isCheckingRef.current) return
    isCheckingRef.current = true
    
    try {
      const token = localStorage.getItem('userToken') || localStorage.getItem('clientToken')
      
      if (!token) {
        // لا يوجد توكن، توجيه لصفحة تسجيل الدخول
        router.push('/login')
        return
      }
      
      const response = await fetch('/api/auth/logout', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })
      
      if (!response.ok) {
        const result = await response.json()
        
        if (result.reason === 'session_terminated') {
          // تم إنهاء الجلسة من جهاز آخر
          console.log('🔄 تم إنهاء الجلسة من جهاز آخر')
          handleSessionTerminated()
        } else {
          // انتهت صلاحية الجلسة
          console.log('⏰ انتهت صلاحية الجلسة')
          handleSessionExpired()
        }
      }
      
    } catch (error) {
      console.error('❌ خطأ في التحقق من الجلسة:', error)
      // في حالة خطأ الشبكة، لا نقوم بتسجيل الخروج
    } finally {
      isCheckingRef.current = false
    }
  }, [router, onSessionExpired, onSessionTerminated])
  
  const handleSessionExpired = useCallback(() => {
    // تنظيف البيانات المحلية
    localStorage.removeItem('userToken')
    localStorage.removeItem('clientToken')
    localStorage.removeItem('userData')
    localStorage.removeItem('sessionToken')
    
    // استدعاء callback مخصص إذا وُجد
    if (onSessionExpired) {
      onSessionExpired()
    } else {
      // توجيه افتراضي لصفحة انتهاء الجلسة
      router.push('/session-expired?message=' + encodeURIComponent('انتهت صلاحية الجلسة'))
    }
  }, [router, onSessionExpired])
  
  const handleSessionTerminated = useCallback(() => {
    // تنظيف البيانات المحلية
    localStorage.removeItem('userToken')
    localStorage.removeItem('clientToken')
    localStorage.removeItem('userData')
    localStorage.removeItem('sessionToken')
    
    // استدعاء callback مخصص إذا وُجد
    if (onSessionTerminated) {
      onSessionTerminated()
    } else {
      // توجيه لصفحة انتهاء الجلسة مع رسالة مخصصة
      router.push('/session-expired?message=' + encodeURIComponent('تم تسجيل دخولك من جهاز آخر'))
    }
  }, [router, onSessionTerminated])
  
  const startSessionCheck = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }
    
    // تحقق فوري
    checkSession()
    
    // تحقق دوري
    intervalRef.current = setInterval(checkSession, checkInterval)
  }, [checkSession, checkInterval])
  
  const stopSessionCheck = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
  }, [])
  
  useEffect(() => {
    if (enabled) {
      startSessionCheck()
    } else {
      stopSessionCheck()
    }
    
    return () => {
      stopSessionCheck()
    }
  }, [enabled, startSessionCheck, stopSessionCheck])
  
  // التحقق عند تغيير التبويب (focus/blur)
  useEffect(() => {
    const handleFocus = () => {
      if (enabled) {
        checkSession()
      }
    }
    
    const handleVisibilityChange = () => {
      if (enabled && !document.hidden) {
        checkSession()
      }
    }
    
    window.addEventListener('focus', handleFocus)
    document.addEventListener('visibilitychange', handleVisibilityChange)
    
    return () => {
      window.removeEventListener('focus', handleFocus)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [enabled, checkSession])
  
  return {
    checkSession,
    startSessionCheck,
    stopSessionCheck
  }
}

export default useSessionValidator
