// إعداد شامل لقاعدة البيانات مع جميع الجداول الجديدة
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function completeDatabaseSetup() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔄 جاري الاتصال بقاعدة البيانات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 1. إنشاء/تحديث جدول النسب المالية
    console.log('🔧 إنشاء/تحديث جدول النسب المالية...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS lineages (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        admin_percentage DECIMAL(5,2) DEFAULT 0,
        management_share DECIMAL(5,2) DEFAULT 0,
        court_share DECIMAL(5,2) DEFAULT 0,
        commission_share DECIMAL(5,2) DEFAULT 0,
        other_share DECIMAL(5,2) DEFAULT 0,
        description TEXT,
        is_active BOOLEAN DEFAULT true,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 2. إنشاء/تحديث جدول الخدمات
    console.log('🔧 إنشاء/تحديث جدول الخدمات...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS services (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        price DECIMAL(10,2) DEFAULT 0,
        lineage_id INTEGER REFERENCES lineages(id),
        default_percentage DECIMAL(5,2) DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 3. إنشاء جدول توزيع القضايا
    console.log('🔧 إنشاء جدول توزيع القضايا...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS case_distribution (
        id SERIAL PRIMARY KEY,
        issue_id INTEGER REFERENCES issues(id),
        lineage_id INTEGER REFERENCES lineages(id),
        percentage DECIMAL(5,2) DEFAULT 0,
        amount DECIMAL(10,2) DEFAULT 0,
        notes TEXT,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 4. إنشاء جدول تفاصيل توزيع الخدمات
    console.log('🔧 إنشاء جدول تفاصيل توزيع الخدمات...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS service_distributions (
        id SERIAL PRIMARY KEY,
        service_id INTEGER REFERENCES services(id),
        lineage_id INTEGER REFERENCES lineages(id),
        percentage DECIMAL(5,2) DEFAULT 0,
        amount DECIMAL(10,2) DEFAULT 0,
        notes TEXT,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 5. إنشاء جدول serviceslow للموقع
    console.log('🔧 إنشاء جدول serviceslow...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS serviceslow (
        id SERIAL PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        slug VARCHAR(255) UNIQUE NOT NULL,
        description TEXT,
        content TEXT,
        icon_name VARCHAR(100) DEFAULT 'Scale',
        icon_color VARCHAR(50) DEFAULT '#2563eb',
        image_url VARCHAR(500),
        is_active BOOLEAN DEFAULT true,
        sort_order INTEGER DEFAULT 0,
        meta_title VARCHAR(255),
        meta_description TEXT,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 6. إنشاء جدول footer_links
    console.log('🔧 إنشاء جدول footer_links...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS footer_links (
        id SERIAL PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        url VARCHAR(500) NOT NULL,
        category VARCHAR(100) DEFAULT 'general',
        sort_order INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 7. إضافة الأعمدة المفقودة لجدول companies
    console.log('🔧 تحديث جدول companies...');
    const companyColumns = [
      'ALTER TABLE companies ADD COLUMN IF NOT EXISTS latitude DECIMAL(10,8)',
      'ALTER TABLE companies ADD COLUMN IF NOT EXISTS longitude DECIMAL(11,8)',
      'ALTER TABLE companies ADD COLUMN IF NOT EXISTS timezone VARCHAR(50) DEFAULT \'Asia/Riyadh\'',
      'ALTER TABLE companies ADD COLUMN IF NOT EXISTS currency VARCHAR(10) DEFAULT \'YER\''
    ];

    for (const sql of companyColumns) {
      try {
        await client.query(sql);
      } catch (error) {
        console.log(`تم تخطي: ${error.message}`);
      }
    }

    // 8. إضافة الأعمدة المفقودة لجدول chart_of_accounts
    console.log('🔧 تحديث جدول chart_of_accounts...');
    const chartColumns = [
      'ALTER TABLE chart_of_accounts ADD COLUMN IF NOT EXISTS is_linked_record BOOLEAN DEFAULT false',
      'ALTER TABLE chart_of_accounts ADD COLUMN IF NOT EXISTS original_table VARCHAR(100)',
      'ALTER TABLE chart_of_accounts ADD COLUMN IF NOT EXISTS linked_record_id INTEGER'
    ];

    for (const sql of chartColumns) {
      try {
        await client.query(sql);
      } catch (error) {
        console.log(`تم تخطي: ${error.message}`);
      }
    }

    // 9. إضافة الأعمدة المفقودة لجدول journal_entries
    console.log('🔧 تحديث جدول journal_entries...');
    const journalColumns = [
      'ALTER TABLE journal_entries ADD COLUMN IF NOT EXISTS entry_type VARCHAR(50) DEFAULT \'manual\'',
      'ALTER TABLE journal_entries ADD COLUMN IF NOT EXISTS reference_type VARCHAR(50)',
      'ALTER TABLE journal_entries ADD COLUMN IF NOT EXISTS reference_id INTEGER'
    ];

    for (const sql of journalColumns) {
      try {
        await client.query(sql);
      } catch (error) {
        console.log(`تم تخطي: ${error.message}`);
      }
    }

    // 10. إدراج بيانات النسب المالية
    console.log('🔄 إدراج بيانات النسب المالية...');
    const lineagesData = [
      { name: 'نسبة الإدارة', admin_percentage: 30.00, description: 'نسبة الإدارة من الأتعاب' },
      { name: 'نسبة المحكمة', admin_percentage: 20.00, description: 'نسبة رسوم المحكمة' },
      { name: 'نسبة العمولة', admin_percentage: 15.00, description: 'نسبة العمولة للوسطاء' },
      { name: 'نسبة أخرى', admin_percentage: 10.00, description: 'نسب أخرى متنوعة' }
    ];

    for (const lineage of lineagesData) {
      await client.query(`
        INSERT INTO lineages (name, admin_percentage, description)
        VALUES ($1, $2, $3)
        ON CONFLICT DO NOTHING
      `, [lineage.name, lineage.admin_percentage, lineage.description]);
    }

    // 11. إدراج بيانات الخدمات
    console.log('🔄 إدراج بيانات الخدمات...');
    const servicesData = [
      { name: 'استشارة قانونية', description: 'استشارة قانونية عامة', price: 500.00 },
      { name: 'صياغة عقد', description: 'صياغة العقود القانونية', price: 1000.00 },
      { name: 'تمثيل قضائي', description: 'التمثيل أمام المحاكم', price: 2000.00 },
      { name: 'مراجعة قانونية', description: 'مراجعة الوثائق القانونية', price: 750.00 }
    ];

    for (const service of servicesData) {
      await client.query(`
        INSERT INTO services (name, description, price)
        VALUES ($1, $2, $3)
        ON CONFLICT DO NOTHING
      `, [service.name, service.description, service.price]);
    }

    // 12. إدراج بيانات serviceslow
    console.log('🔄 إدراج بيانات serviceslow...');
    const servicesLowData = [
      { title: 'الاستشارات القانونية', slug: 'legal-consultation', description: 'نقدم استشارات قانونية شاملة', icon_name: 'Scale' },
      { title: 'التمثيل القضائي', slug: 'court-representation', description: 'تمثيل قضائي متخصص', icon_name: 'Gavel' },
      { title: 'صياغة العقود', slug: 'contract-drafting', description: 'صياغة العقود القانونية', icon_name: 'FileText' }
    ];

    for (const service of servicesLowData) {
      await client.query(`
        INSERT INTO serviceslow (title, slug, description, icon_name)
        VALUES ($1, $2, $3, $4)
        ON CONFLICT (slug) DO NOTHING
      `, [service.title, service.slug, service.description, service.icon_name]);
    }

    // 13. إدراج بيانات footer_links
    console.log('🔄 إدراج بيانات footer_links...');
    const footerLinksData = [
      { title: 'الرئيسية', url: '/', category: 'main' },
      { title: 'خدماتنا', url: '/services', category: 'main' },
      { title: 'من نحن', url: '/about', category: 'main' },
      { title: 'اتصل بنا', url: '/contact', category: 'main' }
    ];

    for (const link of footerLinksData) {
      await client.query(`
        INSERT INTO footer_links (title, url, category)
        VALUES ($1, $2, $3)
        ON CONFLICT DO NOTHING
      `, [link.title, link.url, link.category]);
    }

    // 14. التحقق من النتائج النهائية
    console.log('🔄 جاري التحقق من النتائج النهائية...');
    
    const results = await Promise.all([
      client.query('SELECT COUNT(*) FROM lineages'),
      client.query('SELECT COUNT(*) FROM services'),
      client.query('SELECT COUNT(*) FROM case_distribution'),
      client.query('SELECT COUNT(*) FROM service_distributions'),
      client.query('SELECT COUNT(*) FROM serviceslow'),
      client.query('SELECT COUNT(*) FROM footer_links'),
      client.query('SELECT COUNT(*) FROM users'),
      client.query('SELECT COUNT(*) FROM employees'),
      client.query('SELECT COUNT(*) FROM clients'),
      client.query('SELECT COUNT(*) FROM issues')
    ]);

    console.log('📊 ملخص قاعدة البيانات النهائي:');
    console.log(`   - النسب المالية: ${results[0].rows[0].count} سجل`);
    console.log(`   - الخدمات: ${results[1].rows[0].count} سجل`);
    console.log(`   - توزيع القضايا: ${results[2].rows[0].count} سجل`);
    console.log(`   - تفاصيل توزيع الخدمات: ${results[3].rows[0].count} سجل`);
    console.log(`   - خدمات الموقع: ${results[4].rows[0].count} سجل`);
    console.log(`   - روابط التذييل: ${results[5].rows[0].count} سجل`);
    console.log(`   - المستخدمين: ${results[6].rows[0].count} سجل`);
    console.log(`   - الموظفين: ${results[7].rows[0].count} سجل`);
    console.log(`   - الموكلين: ${results[8].rows[0].count} سجل`);
    console.log(`   - القضايا: ${results[9].rows[0].count} سجل`);

    console.log('✅ تم إعداد قاعدة البيانات بالكامل بنجاح!');
    console.log('🔗 النظام متصل بقاعدة البيانات ويعمل على المنفذ 7443');

  } catch (error) {
    console.error('❌ خطأ في إعداد قاعدة البيانات:', error.message);
  } finally {
    await client.end();
    console.log('🔄 تم قطع الاتصال بقاعدة البيانات');
  }
}

completeDatabaseSetup();
