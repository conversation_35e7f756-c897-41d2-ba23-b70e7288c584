/**
 * سكريبت لإضافة الأعمدة المفقودة لجدول الموظفين
 * employee_number و account_id
 */

const { Client } = require('pg');

// إعدادات قاعدة البيانات mohammidev
const dbConfig = {
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'mohammidev'
};

async function addEmployeeColumns() {
  const client = new Client(dbConfig);

  try {
    await client.connect();
    console.log('✅ متصل بقاعدة البيانات mohammidev');

    // التحقق من الأعمدة الموجودة
    console.log('\n🔍 فحص الأعمدة الموجودة في جدول employees...');
    const columnsResult = await client.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'employees' AND table_schema = 'public'
      ORDER BY ordinal_position
    `);

    const existingColumns = columnsResult.rows.map(row => row.column_name);
    console.log('📋 الأعمدة الموجودة:', existingColumns);

    // إضافة الأعمدة المفقودة
    const columnsToAdd = [
      { name: 'employee_number', type: 'VARCHAR(50)', description: 'رقم الموظف' },
      { name: 'account_id', type: 'INTEGER', description: 'معرف الحساب في دليل الحسابات' }
    ];

    for (const column of columnsToAdd) {
      if (!existingColumns.includes(column.name)) {
        console.log(`\n➕ إضافة عمود ${column.name}...`);
        await client.query(`ALTER TABLE employees ADD COLUMN ${column.name} ${column.type}`);
        console.log(`✅ تم إضافة عمود ${column.name} (${column.description})`);
      } else {
        console.log(`✅ عمود ${column.name} موجود بالفعل`);
      }
    }

    // التحقق النهائي
    console.log('\n🔍 التحقق النهائي من الأعمدة...');
    const finalCheck = await client.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'employees' AND table_schema = 'public'
      ORDER BY ordinal_position
    `);

    console.log('\n📊 جميع أعمدة جدول employees:');
    finalCheck.rows.forEach(row => {
      console.log(`   - ${row.column_name}: ${row.data_type} ${row.is_nullable === 'YES' ? '(nullable)' : '(not null)'}`);
    });

    console.log('\n🎉 تم تحديث جدول الموظفين بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في تحديث جدول الموظفين:', error.message);
    throw error;
  } finally {
    await client.end();
  }
}

// تشغيل السكريبت
if (require.main === module) {
  addEmployeeColumns().catch(console.error);
}

module.exports = { addEmployeeColumns };
