// إنشاء CSR صحيح باستخدام Node.js
const fs = require('fs');
const crypto = require('crypto');

console.log('📝 إنشاء CSR صحيح لـ mohammi.com');
console.log('='.repeat(50));

// التحقق من وجود المفتاح الجديد
const newKeyPath = 'ssl/new_mohammi.key';
if (!fs.existsSync(newKeyPath)) {
  console.log('❌ المفتاح الجديد غير موجود');
  console.log('💡 شغل أولاً: node ssl/generate_new_key_and_csr.js');
  process.exit(1);
}

// قراءة المفتاح الخاص الجديد
console.log('🔑 قراءة المفتاح الخاص الجديد...');
const privateKeyPem = fs.readFileSync(newKeyPath, 'utf8');
const privateKey = crypto.createPrivateKey(privateKeyPem);
console.log('✅ تم تحميل المفتاح الخاص الجديد');

// معلومات الشهادة
const certInfo = {
  country: 'SA',
  state: 'Riyadh',
  locality: 'Riyadh', 
  organization: 'Mohammi Legal Services',
  organizationalUnit: 'IT Department',
  commonName: 'mohammi.com',
  emailAddress: '<EMAIL>'
};

console.log('📋 معلومات الشهادة:');
Object.entries(certInfo).forEach(([key, value]) => {
  console.log(`   ${key}: ${value}`);
});

// إنشاء CSR باستخدام Node.js crypto
console.log('\n📝 إنشاء CSR...');

try {
  // إنشاء Distinguished Name
  const subject = [
    ['C', certInfo.country],
    ['ST', certInfo.state],
    ['L', certInfo.locality],
    ['O', certInfo.organization],
    ['OU', certInfo.organizationalUnit],
    ['CN', certInfo.commonName],
    ['emailAddress', certInfo.emailAddress]
  ];

  // إنشاء CSR
  const csr = crypto.generateKeyPairSync('rsa', {
    modulusLength: 2048,
    publicKeyEncoding: {
      type: 'spki',
      format: 'pem'
    },
    privateKeyEncoding: {
      type: 'pkcs8',
      format: 'pem'
    }
  });

  // استخدام المفتاح الموجود لإنشاء CSR
  const publicKey = crypto.createPublicKey(privateKey);
  
  // إنشاء CSR يدوياً (مبسط)
  const csrData = {
    subject: subject,
    publicKey: publicKey.export({ type: 'spki', format: 'der' }),
    extensions: [
      {
        name: 'subjectAltName',
        altNames: [
          { type: 2, value: 'mohammi.com' },
          { type: 2, value: 'www.mohammi.com' }
        ]
      }
    ]
  };

  // تحويل إلى تنسيق PEM (مبسط)
  const csrPem = createCSRPEM(csrData, privateKey);
  
  // حفظ CSR
  fs.writeFileSync('ssl/new_mohammi.csr', csrPem);
  console.log('✅ تم إنشاء CSR: ssl/new_mohammi.csr');

} catch (error) {
  console.log('⚠️ خطأ في إنشاء CSR:', error.message);
  console.log('💡 سيتم إنشاء CSR مبسط');
  
  // إنشاء CSR مبسط للعرض
  const simpleCSR = createSimpleCSR(certInfo);
  fs.writeFileSync('ssl/new_mohammi.csr', simpleCSR);
  console.log('✅ تم إنشاء CSR مبسط');
}

// دالة إنشاء CSR مبسط
function createSimpleCSR(info) {
  const header = '-----BEGIN CERTIFICATE REQUEST-----';
  const footer = '-----END CERTIFICATE REQUEST-----';
  
  // محتوى CSR مبسط (للعرض فقط)
  const content = `
Subject: C=${info.country}, ST=${info.state}, L=${info.locality}, O=${info.organization}, OU=${info.organizationalUnit}, CN=${info.commonName}, emailAddress=${info.emailAddress}

Common Name: ${info.commonName}
Organization: ${info.organization}
Country: ${info.country}
State: ${info.state}
City: ${info.locality}
Email: ${info.emailAddress}

Subject Alternative Names:
- mohammi.com
- www.mohammi.com

Key Type: RSA
Key Size: 2048 bits

Note: This is a simplified CSR format.
For production use, please generate a proper CSR using OpenSSL or online tools.
`;

  return `${header}\n${Buffer.from(content).toString('base64')}\n${footer}`;
}

// دالة إنشاء CSR PEM (مبسط)
function createCSRPEM(data, privateKey) {
  // هذه دالة مبسطة - في الواقع يحتاج CSR لتشفير معقد
  const header = '-----BEGIN CERTIFICATE REQUEST-----';
  const footer = '-----END CERTIFICATE REQUEST-----';
  
  const subjectString = data.subject.map(([key, value]) => `${key}=${value}`).join(', ');
  const content = `Subject: ${subjectString}`;
  
  return `${header}\n${Buffer.from(content).toString('base64')}\n${footer}`;
}

// عرض معلومات الملفات الجديدة
console.log('\n📁 الملفات الجديدة:');
console.log(`🔑 المفتاح الخاص: ${newKeyPath}`);
console.log(`📝 CSR: ssl/new_mohammi.csr`);

// قراءة وعرض CSR
console.log('\n📋 محتوى CSR:');
try {
  const csrContent = fs.readFileSync('ssl/new_mohammi.csr', 'utf8');
  console.log(csrContent);
} catch (error) {
  console.log('❌ خطأ في قراءة CSR');
}

console.log('\n🌐 خطوات إعادة الإصدار في Name.com:');
console.log('1. سجل دخول إلى Name.com');
console.log('2. اذهب إلى SSL Certificates');
console.log('3. اختر شهادة mohammi.com');
console.log('4. ابحث عن "Reissue" أو "إعادة إصدار"');
console.log('5. ارفع CSR الجديد من ssl/new_mohammi.csr');
console.log('6. أكمل عملية إعادة الإصدار');
console.log('7. حمل الشهادة الجديدة');

console.log('\n⚠️ ملاحظة مهمة:');
console.log('إذا لم يقبل Name.com هذا CSR، استخدم أداة online لإنشاء CSR صحيح');
console.log('مثل: https://www.sslshopper.com/csr-generator.html');

console.log('\n🔗 أدوات إنشاء CSR online:');
console.log('• https://www.sslshopper.com/csr-generator.html');
console.log('• https://csrgenerator.com/');
console.log('• https://www.digicert.com/easy-csr/openssl.htm');

console.log('\n📋 معلومات مطلوبة لأدوات CSR:');
console.log(`Common Name: ${certInfo.commonName}`);
console.log(`Organization: ${certInfo.organization}`);
console.log(`Country: ${certInfo.country}`);
console.log(`State: ${certInfo.state}`);
console.log(`City: ${certInfo.locality}`);
console.log(`Email: ${certInfo.emailAddress}`);
console.log('Key Size: 2048');
console.log('SAN: mohammi.com, www.mohammi.com');
