import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// PUT - تحديث إشعار العميل (وضع علامة مقروء)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const notificationId = parseInt(params.id)
    const body = await request.json()
    const { is_read } = body

    const clientId = 1 // مؤقت للاختبار

    if (isNaN(notificationId)) {
      return NextResponse.json(
        { success: false, error: 'معرف الإشعار غير صحيح' },
        { status: 400 }
      )
    }

    // التحقق من ملكية الإشعار
    const ownershipCheck = await query(
      'SELECT id FROM client_notifications WHERE id = $1 AND client_id = $2',
      [notificationId, clientId]
    )

    if (ownershipCheck.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الإشعار غير موجود أو غير مملوك لك' },
        { status: 404 }
      )
    }

    // تحديث حالة القراءة
    const updateQuery = `
      UPDATE client_notifications 
      SET is_read = $2, read_at = CASE WHEN $2 = true THEN CURRENT_TIMESTAMP ELSE read_at END
      WHERE id = $1
      RETURNING *
    `

    const result = await query(updateQuery, [notificationId, is_read])

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: is_read ? 'تم وضع علامة مقروء على الإشعار' : 'تم إزالة علامة المقروء من الإشعار'
    })

  } catch (error) {
    console.error('خطأ في تحديث الإشعار:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الإشعار' },
      { status: 500 }
    )
  }
}

// DELETE - حذف إشعار
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const notificationId = parseInt(params.id)
    const clientId = 1 // مؤقت للاختبار

    if (isNaN(notificationId)) {
      return NextResponse.json(
        { success: false, error: 'معرف الإشعار غير صحيح' },
        { status: 400 }
      )
    }

    // التحقق من ملكية الإشعار
    const ownershipCheck = await query(
      'SELECT id FROM client_notifications WHERE id = $1 AND client_id = $2',
      [notificationId, clientId]
    )

    if (ownershipCheck.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الإشعار غير موجود أو غير مملوك لك' },
        { status: 404 }
      )
    }

    // حذف الإشعار
    await query('DELETE FROM client_notifications WHERE id = $1', [notificationId])

    return NextResponse.json({
      success: true,
      message: 'تم حذف الإشعار بنجاح'
    })

  } catch (error) {
    console.error('خطأ في حذف الإشعار:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الإشعار' },
      { status: 500 }
    )
  }
}