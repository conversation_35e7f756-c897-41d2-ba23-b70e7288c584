import { NextResponse } from 'next/server'
import { sendCloudMessage } from '../../../../../lib/whatsapp-cloud'

export async function POST(req: Request) {
  try {
    const body = await req.json()
    const { to, text, template, phoneNumberId, accessToken } = body || {}

    if (!to) {
      return NextResponse.json({ success: false, error: 'Missing to' }, { status: 400 })
    }

    if (!text && !template) {
      return NextResponse.json({ success: false, error: 'Provide text or template' }, { status: 400 })
    }

    const result = await sendCloudMessage({ to, text, template, phoneNumberId, accessToken })

    if (!result.success) {
      return NextResponse.json({ success: false, error: result.error }, { status: 400 })
    }

    return NextResponse.json({ success: true, messageId: result.messageId })
  } catch (error: any) {
    return NextResponse.json({ success: false, error: error?.message || 'Unexpected error' }, { status: 500 })
  }
}
