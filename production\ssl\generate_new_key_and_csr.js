// إنشاء مفتاح خاص جديد و CSR لطلب إعادة إصدار الشهادة
const crypto = require('crypto');
const fs = require('fs');

console.log('🔑 إنشاء مفتاح خاص جديد و CSR');
console.log('='.repeat(50));

// إنشاء مفتاح خاص جديد
console.log('🔐 إنشاء مفتاح خاص جديد...');
const { privateKey, publicKey } = crypto.generateKeyPairSync('rsa', {
  modulusLength: 2048,
  privateKeyEncoding: {
    type: 'pkcs8',
    format: 'pem'
  },
  publicKeyEncoding: {
    type: 'spki',
    format: 'pem'
  }
});

// حفظ المفتاح الخاص
fs.writeFileSync('ssl/new_mohammi.key', privateKey);
console.log('✅ تم إنشاء المفتاح الخاص: ssl/new_mohammi.key');

// إنشاء CSR
console.log('📝 إنشاء CSR جديد...');
const subject = {
  country: 'SA',
  state: 'Riyadh',
  locality: 'Riyadh',
  organization: 'Mohammi Legal Services',
  organizationalUnit: 'IT Department',
  commonName: 'mohammi.com',
  emailAddress: '<EMAIL>'
};

// تحويل المعلومات إلى تنسيق CSR
const subjectString = Object.entries(subject)
  .map(([key, value]) => {
    const shortNames = {
      country: 'C',
      state: 'ST',
      locality: 'L',
      organization: 'O',
      organizationalUnit: 'OU',
      commonName: 'CN',
      emailAddress: 'emailAddress'
    };
    return `${shortNames[key]}=${value}`;
  })
  .join('/');

console.log('📋 معلومات الشهادة:');
console.log(`   الدومين: ${subject.commonName}`);
console.log(`   المنظمة: ${subject.organization}`);
console.log(`   البلد: ${subject.country}`);
console.log(`   الإيميل: ${subject.emailAddress}`);

// إنشاء CSR باستخدام OpenSSL (إذا كان متاحاً)
try {
  const { execSync } = require('child_process');
  
  // إنشاء ملف config مؤقت
  const configContent = `
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C=${subject.country}
ST=${subject.state}
L=${subject.locality}
O=${subject.organization}
OU=${subject.organizationalUnit}
CN=${subject.commonName}
emailAddress=${subject.emailAddress}

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = mohammi.com
DNS.2 = www.mohammi.com
`;

  fs.writeFileSync('ssl/temp_config.conf', configContent);
  
  // إنشاء CSR
  execSync(`openssl req -new -key ssl/new_mohammi.key -out ssl/new_mohammi.csr -config ssl/temp_config.conf`, { stdio: 'inherit' });
  
  // حذف ملف التكوين المؤقت
  fs.unlinkSync('ssl/temp_config.conf');
  
  console.log('✅ تم إنشاء CSR: ssl/new_mohammi.csr');
  
} catch (error) {
  console.log('⚠️ OpenSSL غير متاح، سيتم إنشاء CSR بطريقة أخرى');
  
  // إنشاء CSR يدوياً (مبسط)
  const csrContent = `-----BEGIN CERTIFICATE REQUEST-----
[CSR Content - يرجى استخدام أدوات أخرى لإنشاء CSR صحيح]
-----END CERTIFICATE REQUEST-----`;
  
  fs.writeFileSync('ssl/new_mohammi.csr', csrContent);
  console.log('⚠️ تم إنشاء CSR مؤقت - يرجى استخدام أدوات أخرى');
}

console.log('\n📋 الخطوات التالية:');
console.log('1. استخدم المفتاح الجديد: ssl/new_mohammi.key');
console.log('2. استخدم CSR الجديد: ssl/new_mohammi.csr');
console.log('3. اذهب إلى Name.com واطلب إعادة إصدار الشهادة');
console.log('4. ارفع CSR الجديد');
console.log('5. حمل الشهادة الجديدة');

console.log('\n🔗 أو اتصل بدعم Name.com لطلب المفتاح الأصلي');

console.log('\n⚠️ ملاحظة مهمة:');
console.log('المفتاح الخاص الأصلي مفقود من ملف ZIP');
console.log('يجب الحصول عليه من Name.com أو إنشاء شهادة جديدة');
