import { NextRequest, NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'

export async function POST() {
  try {
    console.log('🔄 إنشاء قاعدة بيانات محلية بديلة...')

    // إنشاء مجلد قاعدة البيانات
    const dbDir = path.join(process.cwd(), 'database')
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true })
      console.log('✅ تم إنشاء مجلد قاعدة البيانات')
    }

    // إنشاء ملف قاعدة بيانات JSON
    const dbFile = path.join(dbDir, 'case_movements.json')

    const sampleData = {
      case_movements: [
        {
          id: 1,
          case_id: 1,
          case_number: '2024/001',
          case_title: 'قضية تجارية - شركة الأمل',
          movement_type: 'case_created',
          description: 'تم إنشاء القضية في النظام',
          created_by_name: 'النظام',
          priority: 'normal',
          client_name: 'شركة الأمل للتجارة',
          case_status: 'نشطة',
          created_at: new Date().toISOString(),
          movement_date: new Date().toISOString().split('T')[0]
        },
        {
          id: 2,
          case_id: 1,
          case_number: '2024/001',
          case_title: 'قضية تجارية - شركة الأمل',
          movement_type: 'case_assigned',
          description: 'تم توزيع القضية على المحامي أحمد محمد',
          created_by_name: 'المدير العام',
          priority: 'high',
          client_name: 'شركة الأمل للتجارة',
          case_status: 'موزعة',
          created_at: new Date(Date.now() - 86400000).toISOString(),
          movement_date: new Date(Date.now() - 86400000).toISOString().split('T')[0]
        },
        {
          id: 3,
          case_id: 2,
          case_number: '2024/002',
          case_title: 'قضية عمالية - محمد علي',
          movement_type: 'case_created',
          description: 'تم إنشاء القضية في النظام',
          created_by_name: 'النظام',
          priority: 'normal',
          client_name: 'محمد علي أحمد',
          case_status: 'نشطة',
          created_at: new Date(Date.now() - 172800000).toISOString(),
          movement_date: new Date(Date.now() - 172800000).toISOString().split('T')[0]
        },
        {
          id: 4,
          case_id: 2,
          case_number: '2024/002',
          case_title: 'قضية عمالية - محمد علي',
          movement_type: 'hearing_scheduled',
          description: 'تم تحديد جلسة للقضية في المحكمة العمالية',
          created_by_name: 'أحمد محمد',
          priority: 'high',
          client_name: 'محمد علي أحمد',
          case_status: 'جلسة مجدولة',
          created_at: new Date(Date.now() - 86400000).toISOString(),
          movement_date: new Date(Date.now() - 86400000).toISOString().split('T')[0]
        },
        {
          id: 5,
          case_id: 3,
          case_number: '2024/003',
          case_title: 'قضية مدنية - فاطمة سالم',
          movement_type: 'case_created',
          description: 'تم إنشاء القضية في النظام',
          created_by_name: 'النظام',
          priority: 'normal',
          client_name: 'فاطمة سالم محمد',
          case_status: 'نشطة',
          created_at: new Date(Date.now() - 259200000).toISOString(),
          movement_date: new Date(Date.now() - 259200000).toISOString().split('T')[0]
        },
        {
          id: 6,
          case_id: 3,
          case_number: '2024/003',
          case_title: 'قضية مدنية - فاطمة سالم',
          movement_type: 'document_uploaded',
          description: 'تم رفع وثيقة "عقد الإيجار الأصلي" للقضية',
          created_by_name: 'سارة أحمد',
          priority: 'normal',
          client_name: 'فاطمة سالم محمد',
          case_status: 'قيد المراجعة',
          created_at: new Date(Date.now() - 172800000).toISOString(),
          movement_date: new Date(Date.now() - 172800000).toISOString().split('T')[0]
        },
        {
          id: 7,
          case_id: 4,
          case_number: '2024/004',
          case_title: 'قضية جنائية - خالد يوسف',
          movement_type: 'case_created',
          description: 'تم إنشاء القضية في النظام',
          created_by_name: 'النظام',
          priority: 'urgent',
          client_name: 'خالد يوسف علي',
          case_status: 'نشطة',
          created_at: new Date(Date.now() - 345600000).toISOString(),
          movement_date: new Date(Date.now() - 345600000).toISOString().split('T')[0]
        },
        {
          id: 8,
          case_id: 4,
          case_number: '2024/004',
          case_title: 'قضية جنائية - خالد يوسف',
          movement_type: 'follow_added',
          description: 'تم إضافة متابعة: تم تقديم الاستئناف للمحكمة العليا',
          created_by_name: 'محمد الحاشدي',
          priority: 'urgent',
          client_name: 'خالد يوسف علي',
          case_status: 'استئناف',
          created_at: new Date(Date.now() - 86400000).toISOString(),
          movement_date: new Date(Date.now() - 86400000).toISOString().split('T')[0]
        },
        {
          id: 9,
          case_id: 5,
          case_number: '2024/005',
          case_title: 'قضية إدارية - شركة النور',
          movement_type: 'case_created',
          description: 'تم إنشاء القضية في النظام',
          created_by_name: 'النظام',
          priority: 'normal',
          client_name: 'شركة النور للمقاولات',
          case_status: 'نشطة',
          created_at: new Date(Date.now() - 432000000).toISOString(),
          movement_date: new Date(Date.now() - 432000000).toISOString().split('T')[0]
        },
        {
          id: 10,
          case_id: 5,
          case_number: '2024/005',
          case_title: 'قضية إدارية - شركة النور',
          movement_type: 'case_status_changed',
          description: 'تم تغيير حالة القضية من "نشطة" إلى "قيد المراجعة"',
          created_by_name: 'أحمد محمد',
          priority: 'normal',
          client_name: 'شركة النور للمقاولات',
          case_status: 'قيد المراجعة',
          created_at: new Date(Date.now() - 259200000).toISOString(),
          movement_date: new Date(Date.now() - 259200000).toISOString().split('T')[0]
        }
      ],
      metadata: {
        total_movements: 10,
        total_cases: 5,
        last_updated: new Date().toISOString(),
        database_type: 'JSON_FALLBACK'
      }
    }

    fs.writeFileSync(dbFile, JSON.stringify(sampleData, null, 2))
    console.log('✅ تم إنشاء قاعدة البيانات المحلية')

    // اختبار قاعدة البيانات
    try {
      const { query } = require('@/lib/db-fallback')
      const result = await query('SELECT * FROM case_movements ORDER BY created_at DESC LIMIT 3')
      
      return NextResponse.json({
        success: true,
        message: 'تم إنشاء قاعدة البيانات البديلة بنجاح',
        details: {
          database_path: dbFile,
          total_movements: sampleData.case_movements.length,
          total_cases: sampleData.metadata.total_cases,
          test_query_results: result.rowCount,
          sample_movements: result.rows.map((row: any) => ({
            case_number: row.case_number,
            movement_type: row.movement_type,
            priority: row.priority
          }))
        },
        next_steps: [
          'قاعدة البيانات البديلة جاهزة للاستخدام',
          'يمكنك الآن الوصول لصفحة /movements',
          'النظام سيعمل بدون PostgreSQL',
          'عند إصلاح PostgreSQL، سيعود النظام لاستخدامه تلقائياً'
        ]
      })
      
    } catch (testError) {
      return NextResponse.json({
        success: true,
        message: 'تم إنشاء قاعدة البيانات ولكن فشل الاختبار',
        details: {
          database_path: dbFile,
          total_movements: sampleData.case_movements.length,
          test_error: testError.message
        }
      })
    }

  } catch (error) {
    console.error('❌ خطأ في إنشاء قاعدة البيانات البديلة:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في إنشاء قاعدة البيانات البديلة',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
