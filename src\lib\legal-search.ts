import fs from 'fs'
import path from 'path'

// مسار الملفات القانونية
const LAWS_DIRECTORY = '/home/<USER>/Downloads/legal-system/laws'

// واجهة نتيجة البحث
interface SearchResult {
  found: boolean
  content: string
  sources: string[]
  confidence: number
}

// كلمات مفتاحية للمواضيع القانونية (محسنة ومتوسعة)
const LEGAL_KEYWORDS = {
  'عمل': ['عمل', 'عامل', 'موظف', 'راتب', 'أجر', 'إجازة', 'نقابة', 'عمالية', 'توظيف', 'استقالة', 'فصل', 'تأمينات'],
  'طلاق': ['طلاق', 'زواج', 'أحوال شخصية', 'زوج', 'زوجة', 'مهر', 'نفقة', 'خلع', 'مبارات', 'عدة', 'حضانة'],
  'ميراث': ['ميراث', 'وراثة', 'تركة', 'وارث', 'موروث', 'فريضة', 'تقسيم', 'إرث', 'ورثة', 'مواريث'],
  'جرائم': ['جريمة', 'عقوبة', 'جنائي', 'جناية', 'مخالفة', 'عقاب', 'سجن', 'غرامة', 'قصاص', 'حدود'],
  'تجارة': ['تجارة', 'تجاري', 'شركة', 'تجار', 'بيع', 'شراء', 'استثمار', 'أعمال', 'تسويق', 'صناعة'],
  'ضرائب': ['ضريبة', 'ضرائب', 'رسوم', 'جمارك', 'زكاة', 'مالية', 'إيرادات', 'خزينة'],
  'بنوك': ['بنك', 'بنوك', 'مصرف', 'مصارف', 'ائتمان', 'قرض', 'فوائد', 'ودائع', 'حسابات'],
  'أراضي': ['أرض', 'أراضي', 'عقار', 'عقارات', 'ملكية', 'سجل', 'طابو', 'مساحة', 'حدود'],
  'دستور': ['دستور', 'دستوري', 'حقوق', 'واجبات', 'مواطن', 'حريات', 'سيادة', 'جمهورية'],
  'مرافعات': ['مرافعة', 'مرافعات', 'دعوى', 'قضية', 'محكمة', 'قاضي', 'حكم', 'استئناف', 'نقض'],
  'إجراءات': ['إجراء', 'إجراءات', 'تقاضي', 'خصومة', 'طعن', 'تنفيذ', 'حجز', 'بيع'],
  'شركات': ['شركة', 'شركات', 'مساهمة', 'تضامن', 'توصية', 'محدودة', 'مسؤولية', 'أسهم'],
  'ضمان': ['ضمان', 'تأمين', 'اجتماعي', 'تقاعد', 'معاش', 'إعانة', 'إصابة', 'مرض'],
  'صحافة': ['صحافة', 'إعلام', 'نشر', 'مطبوعات', 'صحفي', 'حرية', 'رأي', 'تعبير'],
  'بيئة': ['بيئة', 'تلوث', 'حماية', 'طبيعة', 'مياه', 'هواء', 'نظافة', 'صحة'],
  'تعليم': ['تعليم', 'دراسة', 'طالب', 'جامعة', 'مدرسة', 'تعلم', 'تربية', 'أكاديمي', 'علمي', 'بحث'],
  'صحة': ['صحة', 'طب', 'مستشفى', 'علاج', 'دواء', 'طبيب', 'مريض', 'وقاية'],
  'نقل': ['نقل', 'مواصلات', 'سيارة', 'طريق', 'مرور', 'رخصة', 'قيادة', 'حوادث']
}

// دالة تحديد نوع الموضوع القانوني (محسنة)
function identifyLegalTopic(query: string): string[] {
  const topics: { topic: string, score: number }[] = []
  const lowerQuery = query.toLowerCase()
  const queryWords = lowerQuery.split(/\s+/).filter(word => word.length > 2)

  // حساب نقاط لكل موضوع بناءً على عدد الكلمات المطابقة
  for (const [topic, keywords] of Object.entries(LEGAL_KEYWORDS)) {
    let score = 0

    for (const keyword of keywords) {
      if (lowerQuery.includes(keyword)) {
        // نقاط إضافية للمطابقة الكاملة
        score += keyword.length > 4 ? 3 : 2

        // نقاط إضافية إذا كانت الكلمة في بداية السؤال
        if (lowerQuery.startsWith(keyword)) {
          score += 2
        }
      }
    }

    if (score > 0) {
      topics.push({ topic, score })
    }
  }

  // ترتيب المواضيع حسب النقاط
  topics.sort((a, b) => b.score - a.score)

  // إرجاع أفضل موضوعين أو "عام" إذا لم نجد شيء
  const selectedTopics = topics.slice(0, 2).map(t => t.topic)
  return selectedTopics.length > 0 ? selectedTopics : ['عام']
}

// دالة قراءة وفلترة الملفات القانونية
async function getRelevantLawFiles(topics: string[]): Promise<string[]> {
  try {
    const files = await fs.promises.readdir(LAWS_DIRECTORY)
    const relevantFiles: string[] = []

    for (const file of files) {
      if (!file.endsWith('.txt')) continue

      const fileName = file.toLowerCase()

      // فحص إذا كان اسم الملف يحتوي على كلمات مفتاحية
      for (const topic of topics) {
        const keywords = LEGAL_KEYWORDS[topic] || []
        if (keywords.some(keyword => fileName.includes(keyword))) {
          relevantFiles.push(file)
          break
        }
      }
    }

    // إذا لم نجد ملفات محددة، أضف الملفات الأساسية
    if (relevantFiles.length === 0) {
      const basicFiles = [
        'الدستور_اليمني.txt',
        'قانون_الجرائم_والعقوبات.txt',
        'قانون_العمل.txt',
        'قانون_الأحوال_الشخصية.txt',
        'قانون_المرافعات.txt'
      ]

      for (const basicFile of basicFiles) {
        if (files.includes(basicFile)) {
          relevantFiles.push(basicFile)
        }
      }
    }

    // إضافة المزيد من الملفات إذا كان الموضوع عام
    if (topics.includes('عام') && relevantFiles.length < 3) {
      const additionalFiles = files.filter(file =>
        file.endsWith('.txt') && !relevantFiles.includes(file)
      ).slice(0, 3 - relevantFiles.length)

      relevantFiles.push(...additionalFiles)
    }

    return relevantFiles
  } catch (error) {
    console.error('خطأ في قراءة مجلد القوانين:', error)
    return []
  }
}

// دالة البحث في محتوى الملف (محسنة للترميز العربي)
async function searchInFile(filePath: string, query: string): Promise<{content: string, confidence: number}> {
  try {
    // قراءة الملف مع ضمان الترميز الصحيح
    const content = await fs.promises.readFile(filePath, { encoding: 'utf8' })

    // تنظيف المحتوى من الرموز الغريبة
    const cleanContent = content
      .replace(/\r\n/g, '\n')  // توحيد نهايات الأسطر
      .replace(/\r/g, '\n')    // إزالة \r المتبقية
      .replace(/\u00A0/g, ' ') // استبدال المسافات غير المكسورة
      .replace(/\uFEFF/g, '')  // إزالة BOM إذا وجد

    const lines = cleanContent.split('\n')
    const queryWords = query.toLowerCase().split(' ').filter(word => word.length > 2)

    let relevantSections: string[] = []
    let confidence = 0

    // البحث عن الأقسام ذات الصلة
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].toLowerCase()

      // تخطي الأسطر الفارغة أو التي تحتوي على معلومات تقنية فقط
      if (line.trim().length < 3 ||
          line.includes('scraped at:') ||
          line.includes('source:') ||
          line.includes('title:') ||
          line.includes('languge') ||
          line.includes('=====')) {
        continue
      }

      const matchCount = queryWords.filter(word => line.includes(word)).length

      if (matchCount > 0) {
        // أضف السياق (الأسطر المحيطة) مع تنظيف
        const start = Math.max(0, i - 2)
        const end = Math.min(lines.length, i + 3)
        const sectionLines = lines.slice(start, end)
          .filter(l => l.trim().length > 0 && !l.includes('===='))
          .map(l => l.trim())

        if (sectionLines.length > 0) {
          const section = sectionLines.join('\n')
          relevantSections.push(section)
          confidence += matchCount * 10
        }
      }
    }

    // إزالة التكرار وتحديد الثقة
    const uniqueSections = [...new Set(relevantSections)]
    const result = uniqueSections.slice(0, 3).join('\n\n---\n\n') // أفضل 3 أقسام

    return {
      content: result,
      confidence: Math.min(confidence, 100)
    }
  } catch (error) {
    console.error(`خطأ في قراءة الملف ${filePath}:`, error)
    return { content: '', confidence: 0 }
  }
}

// دالة فحص إذا كان السؤال قانوني
function isLegalQuestion(query: string): boolean {
  const legalIndicators = [
    'قانون', 'حق', 'واجب', 'محكمة', 'قاضي', 'دعوى', 'عقد', 'جريمة',
    'ميراث', 'طلاق', 'زواج', 'عمل', 'شركة', 'ضريبة', 'ملكية',
    'استشارة', 'قانوني', 'قضية', 'مخالفة', 'عقوبة', 'حكم', 'تشريع'
  ]

  const lowerQuery = query.toLowerCase()
  return legalIndicators.some(indicator => lowerQuery.includes(indicator)) ||
         query.includes('؟') || query.includes('كيف') || query.includes('ما هي')
}

// الدالة الرئيسية للبحث في القوانين
export async function searchLegalDocuments(query: string): Promise<SearchResult> {
  try {

    // فحص إذا كان السؤال قانوني
    if (!isLegalQuestion(query)) {

      return {
        found: false,
        content: '',
        sources: [],
        confidence: 0
      }
    }

    // تحديد المواضيع ذات الصلة
    const topics = identifyLegalTopic(query)

    // الحصول على الملفات ذات الصلة
    const relevantFiles = await getRelevantLawFiles(topics)

    if (relevantFiles.length === 0) {
      return {
        found: false,
        content: '',
        sources: [],
        confidence: 0
      }
    }

    // البحث في كل ملف
    const searchResults: Array<{content: string, confidence: number, file: string}> = []

    for (const file of relevantFiles) {
      const filePath = path.join(LAWS_DIRECTORY, file)
      const result = await searchInFile(filePath, query)

      if (result.content && result.confidence > 0) {
        searchResults.push({
          ...result,
          file: file.replace('.txt', '').replace(/agoyemen\.net_\d+_/, '')
        })
      }
    }

    // ترتيب النتائج حسب الثقة
    searchResults.sort((a, b) => b.confidence - a.confidence)

    if (searchResults.length === 0) {
      return {
        found: false,
        content: '',
        sources: [],
        confidence: 0
      }
    }

    // تجميع أفضل النتائج
    const bestResults = searchResults.slice(0, 2) // أفضل نتيجتين
    const combinedContent = bestResults.map(result => 
      `**من ${result.file}:**\n${result.content}`
    ).join('\n\n' + '='.repeat(50) + '\n\n')

    const totalConfidence = bestResults.reduce((sum, result) => sum + result.confidence, 0) / bestResults.length

    return {
      found: true,
      content: combinedContent,
      sources: bestResults.map(r => r.file),
      confidence: totalConfidence
    }

  } catch (error) {
    console.error('❌ خطأ في البحث في الملفات القانونية:', error)
    return {
      found: false,
      content: '',
      sources: [],
      confidence: 0
    }
  }
}

// دالة تنسيق الرد النهائي
export function formatLegalResponse(searchResult: SearchResult, aiResponse?: string): string {
  if (!searchResult.found) {
    return aiResponse || 'لم يتم العثور على معلومات في الملفات القانونية المحلية.'
  }

  let response = `📚 **من الملفات القانونية المحلية:**\n\n${searchResult.content}\n\n`

  if (searchResult.sources.length > 0) {
    response += `📖 **المصادر:** ${searchResult.sources.join(', ')}\n\n`
  }

  if (aiResponse && searchResult.confidence < 70) {
    response += `🤖 **معلومات إضافية من الذكاء الاصطناعي:**\n\n${aiResponse}`
  }

  return response
}
