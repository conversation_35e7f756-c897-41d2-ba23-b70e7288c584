'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  MessageSquare, 
  Search, 
  Filter, 
  Trash2, 
  Download,
  Calendar,
  User,
  Bot,
  BarChart3,
  RefreshCw
} from 'lucide-react'
import toast from 'react-hot-toast'

interface ChatLog {
  id: number
  session_id: string
  user_message: string
  bot_response: string
  response_type: string
  user_ip: string
  created_at: string
}

interface ChatStats {
  response_type: string
  count: number
}

export default function ChatLogsPage() {
  const [logs, setLogs] = useState<ChatLog[]>([])
  const [stats, setStats] = useState<ChatStats[]>([])
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedType, setSelectedType] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalLogs, setTotalLogs] = useState(0)

  useEffect(() => {
    loadLogs()
  }, [currentPage, selectedType])

  const loadLogs = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20'
      })

      if (selectedType) {
        params.append('response_type', selectedType)
      }

      const response = await fetch(`/api/chat/logs?${params}`)
      const result = await response.json()

      if (result.success) {
        setLogs(result.data.logs)
        setStats(result.data.stats)
        setTotalPages(result.data.pagination.totalPages)
        setTotalLogs(result.data.pagination.total)
      } else {
        toast.error(result.error || 'خطأ في تحميل السجلات')
      }
    } catch (error) {
      console.error('خطأ في تحميل سجلات المحادثة:', error)
      toast.error('خطأ في الاتصال بالخادم')
    } finally {
      setLoading(false)
    }
  }

  const clearAllLogs = async () => {
    if (!confirm('هل أنت متأكد من حذف جميع سجلات المحادثة؟ هذا الإجراء لا يمكن التراجع عنه.')) {
      return
    }

    try {
      const response = await fetch('/api/chat/logs?action=clear_all', {
        method: 'DELETE'
      })

      const result = await response.json()

      if (result.success) {
        toast.success('تم حذف جميع السجلات بنجاح')
        setLogs([])
        setTotalLogs(0)
        setTotalPages(1)
        setCurrentPage(1)
      } else {
        toast.error(result.error || 'خطأ في حذف السجلات')
      }
    } catch (error) {
      console.error('خطأ في حذف السجلات:', error)
      toast.error('خطأ في الاتصال بالخادم')
    }
  }

  const clearOldLogs = async () => {
    const daysAgo = prompt('حذف السجلات الأقدم من كم يوم؟', '30')
    if (!daysAgo || isNaN(parseInt(daysAgo))) return

    const beforeDate = new Date()
    beforeDate.setDate(beforeDate.getDate() - parseInt(daysAgo))

    try {
      const response = await fetch(`/api/chat/logs?action=clear_old&before_date=${beforeDate.toISOString()}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (result.success) {
        toast.success(result.message)
        loadLogs()
      } else {
        toast.error(result.error || 'خطأ في حذف السجلات')
      }
    } catch (error) {
      console.error('خطأ في حذف السجلات القديمة:', error)
      toast.error('خطأ في الاتصال بالخادم')
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getTypeColor = (type: string) => {
    const colors = {
      greeting: 'bg-green-100 text-green-800',
      services: 'bg-blue-100 text-blue-800',
      contact: 'bg-purple-100 text-purple-800',
      company: 'bg-orange-100 text-orange-800',
      library: 'bg-indigo-100 text-indigo-800',
      default: 'bg-gray-100 text-gray-800',
      error: 'bg-red-100 text-red-800'
    }
    return colors[type] || colors.default
  }

  const getTypeLabel = (type: string) => {
    const labels = {
      greeting: 'ترحيب',
      services: 'خدمات',
      contact: 'تواصل',
      company: 'عن الشركة',
      library: 'مكتبة قانونية',
      default: 'افتراضي',
      error: 'خطأ'
    }
    return labels[type] || type
  }

  const filteredLogs = logs.filter(log => 
    searchQuery === '' || 
    log.user_message.toLowerCase().includes(searchQuery.toLowerCase()) ||
    log.bot_response.toLowerCase().includes(searchQuery.toLowerCase()) ||
    log.session_id.includes(searchQuery)
  )

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* رأس الصفحة */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 space-x-reverse">
            <MessageSquare className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-3xl font-bold text-gray-900">سجلات المحادثة</h1>
              <p className="text-gray-600">إدارة ومراقبة محادثات الذكاء الاصطناعي</p>
            </div>
          </div>

          <div className="flex items-center space-x-2 space-x-reverse">
            <Button
              onClick={loadLogs}
              variant="outline"
              size="sm"
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              تحديث
            </Button>

            <Button
              onClick={clearOldLogs}
              variant="outline"
              size="sm"
            >
              <Calendar className="h-4 w-4 mr-2" />
              حذف القديم
            </Button>

            <Button
              onClick={clearAllLogs}
              variant="destructive"
              size="sm"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              حذف الكل
            </Button>
          </div>
        </div>

        {/* الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">إجمالي المحادثات</p>
                  <p className="text-2xl font-bold text-gray-900">{totalLogs}</p>
                </div>
                <MessageSquare className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          {stats.slice(0, 3).map((stat, index) => (
            <Card key={stat.response_type}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">{getTypeLabel(stat.response_type)}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.count}</p>
                  </div>
                  <BarChart3 className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* أدوات البحث والفلترة */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في الرسائل أو معرف الجلسة..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>

              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="">جميع الأنواع</option>
                {stats.map((stat) => (
                  <option key={stat.response_type} value={stat.response_type}>
                    {getTypeLabel(stat.response_type)} ({stat.count})
                  </option>
                ))}
              </select>
            </div>
          </CardContent>
        </Card>

        {/* قائمة السجلات */}
        <Card>
          <CardHeader>
            <CardTitle>سجلات المحادثة ({filteredLogs.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <RefreshCw className="h-8 w-8 animate-spin mx-auto text-blue-600 mb-2" />
                <p className="text-gray-500">جاري تحميل السجلات...</p>
              </div>
            ) : filteredLogs.length === 0 ? (
              <div className="text-center py-8">
                <MessageSquare className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-500">لا توجد سجلات محادثة</p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredLogs.map((log) => (
                  <div key={log.id} className="border rounded-lg p-4 hover:bg-gray-50">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <Badge className={getTypeColor(log.response_type)}>
                          {getTypeLabel(log.response_type)}
                        </Badge>
                        <span className="text-sm text-gray-500">
                          جلسة: {log.session_id.slice(-8)}
                        </span>
                        {log.user_ip && (
                          <span className="text-sm text-gray-500">
                            IP: {log.user_ip}
                          </span>
                        )}
                      </div>
                      <span className="text-sm text-gray-500">
                        {formatDate(log.created_at)}
                      </span>
                    </div>

                    <div className="space-y-3">
                      {/* رسالة المستخدم */}
                      <div className="flex items-start space-x-2 space-x-reverse">
                        <div className="h-6 w-6 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                          <User className="h-3 w-3 text-white" />
                        </div>
                        <div className="flex-1 bg-blue-50 rounded-lg p-3">
                          <p className="text-sm text-gray-900">{log.user_message}</p>
                        </div>
                      </div>

                      {/* رد البوت */}
                      <div className="flex items-start space-x-2 space-x-reverse">
                        <div className="h-6 w-6 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
                          <Bot className="h-3 w-3 text-white" />
                        </div>
                        <div className="flex-1 bg-gray-50 rounded-lg p-3">
                          <p className="text-sm text-gray-900 whitespace-pre-wrap">{log.bot_response}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* التنقل بين الصفحات */}
            {totalPages > 1 && (
              <div className="flex items-center justify-center space-x-2 space-x-reverse mt-6">
                <Button
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                  variant="outline"
                  size="sm"
                >
                  السابق
                </Button>

                <span className="text-sm text-gray-600">
                  صفحة {currentPage} من {totalPages}
                </span>

                <Button
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                  variant="outline"
                  size="sm"
                >
                  التالي
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
