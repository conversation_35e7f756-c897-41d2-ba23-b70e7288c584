"use client"

import React, { useEffect, useMemo, useState } from "react"

type SendResponse = {
  success: boolean
  messageId?: string
  error?: string
  details?: any
}

const storageKey = {
  phoneNumberId: "wa_cloud_phone_number_id",
  accessToken: "wa_cloud_access_token",
  to: "wa_cloud_to_default",
}

function getEnvHint(name: string) {
  // Only hints for user display; server envs are not readable on client.
  return process.env[name] ? "(موجود من البيئة)" : ""
}

export default function WhatsAppCloudSettingsPage() {
  const [phoneNumberId, setPhoneNumberId] = useState("")
  const [accessToken, setAccessToken] = useState("")
  const [to, setTo] = useState("")
  const [text, setText] = useState("")
  const [documentUrl, setDocumentUrl] = useState("")
  const [filename, setFilename] = useState("")
  const [caption, setCaption] = useState("")
  const [loading, setLoading] = useState<"text" | "doc" | "bundle" | "template" | "status" | null>(null)
  const [result, setResult] = useState<SendResponse | null>(null)
  // Template fields
  const [templateName, setTemplateName] = useState("")
  const [templateLanguage, setTemplateLanguage] = useState("ar")
  const [templateComponentsStr, setTemplateComponentsStr] = useState("")
  // Status field
  const [statusResult, setStatusResult] = useState<any>(null)

  useEffect(() => {
    // Load saved settings
    setPhoneNumberId(localStorage.getItem(storageKey.phoneNumberId) || "")
    setAccessToken(localStorage.getItem(storageKey.accessToken) || "")
    setTo(localStorage.getItem(storageKey.to) || "")
  }, [])

  const canSendText = useMemo(() => !!to && !!(text?.trim()), [to, text])
  const canSendDoc = useMemo(() => !!to && !!(documentUrl?.trim()), [to, documentUrl])
  const canSendBundle = useMemo(() => !!to && (!!(text?.trim()) || !!(documentUrl?.trim())), [to, text, documentUrl])
  const canSendTemplate = useMemo(() => !!to && !!(templateName?.trim()) && !!(templateLanguage?.trim()), [to, templateName, templateLanguage])

  function saveLocally() {
    localStorage.setItem(storageKey.phoneNumberId, phoneNumberId)
    localStorage.setItem(storageKey.accessToken, accessToken)
    localStorage.setItem(storageKey.to, to)
    alert("تم الحفظ محلياً في المتصفح.")
  }

  async function send(endpoint: string, payload: any, kind: typeof loading) {
    setLoading(kind)
    setResult(null)
    try {
      const res = await fetch(endpoint, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      })
      const data = await res.json()
      setResult(data)
    } catch (e: any) {
      setResult({ success: false, error: e?.message || "Network error" })
    } finally {
      setLoading(null)
    }
  }

  function buildAuthPart() {
    // If values provided here, server will use them; otherwise it falls back to .env
    const p: any = {}
    if (phoneNumberId) p.phoneNumberId = phoneNumberId
    if (accessToken) p.accessToken = accessToken
    return p
  }

  function parseTemplateComponents(): any[] | undefined {
    const raw = templateComponentsStr.trim()
    if (!raw) return undefined
    try {
      const parsed = JSON.parse(raw)
      if (Array.isArray(parsed)) return parsed
      alert("صيغة components يجب أن تكون مصفوفة JSON.")
      return undefined
    } catch (e: any) {
      alert("تعذّر قراءة JSON في components: " + (e?.message || ""))
      return undefined
    }
  }

  return (
    <div style={{ maxWidth: 900, margin: "24px auto", padding: 16 }}>
      <h1 style={{ fontSize: 22, fontWeight: 700, marginBottom: 12 }}>إعدادات واختبار WhatsApp Cloud API</h1>
      <p style={{ color: "#555", marginBottom: 16 }}>
        هذه الصفحة تساعدك على اختبار الإرسال النصّي وإرسال ملفات PDF عبر WhatsApp Cloud API لكل نسخة.
        يمكنك إدخال <code>phoneNumberId</code> و<code>accessToken</code> هنا مؤقتاً (يُخزّن محلياً في المتصفح)،
        أو تركهما فارغين والاعتماد على قيم البيئة على الخادم.
      </p>

      <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: 16 }}>
        <div>
          <label>Phone Number ID {getEnvHint("WHATSAPP_PHONE_NUMBER_ID")}</label>
          <input value={phoneNumberId} onChange={e => setPhoneNumberId(e.target.value)} placeholder="مثال: 123456789012345" style={{ width: "100%", padding: 8, marginTop: 4 }} />
        </div>
        <div>
          <label>Access Token {getEnvHint("WHATSAPP_ACCESS_TOKEN")}</label>
          <input value={accessToken} onChange={e => setAccessToken(e.target.value)} placeholder="EAAG..." style={{ width: "100%", padding: 8, marginTop: 4 }} />
        </div>
        <div>
          <label>رقم المستلم (to)</label>
          <input value={to} onChange={e => setTo(e.target.value)} placeholder="777123456 أو 9677xxxxxxx" style={{ width: "100%", padding: 8, marginTop: 4 }} />
        </div>
      </div>

      <div style={{ marginTop: 12 }}>
        <button onClick={saveLocally} style={{ padding: "8px 16px", background: "#0ea5e9", color: "white", border: 0, borderRadius: 6 }}>حفظ الإعدادات محلياً</button>
      </div>

      <hr style={{ margin: "20px 0" }} />

      <h2 style={{ fontSize: 18, fontWeight: 700, marginBottom: 8 }}>إرسال Template (مفيد للإشعارات خارج نافذة 24 ساعة)</h2>
      <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: 16 }}>
        <div>
          <label>Template Name</label>
          <input value={templateName} onChange={e => setTemplateName(e.target.value)} placeholder="مثال: appointment_reminder" style={{ width: "100", padding: 8, marginTop: 4 }} />
        </div>
        <div>
          <label>Language Code</label>
          <input value={templateLanguage} onChange={e => setTemplateLanguage(e.target.value)} placeholder="مثال: ar أو en_US" style={{ width: "100%", padding: 8, marginTop: 4 }} />
        </div>
      </div>
      <div style={{ marginTop: 8 }}>
        <label>Components (JSON Array اختياري)</label>
        <textarea value={templateComponentsStr} onChange={e => setTemplateComponentsStr(e.target.value)} rows={4} placeholder='مثال: [{"type":"body","parameters":[{"type":"text","text":"محمد"}]}]' style={{ width: "100%", padding: 8 }} />
      </div>
      <div style={{ marginTop: 8 }}>
        <button
          disabled={!canSendTemplate || loading !== null}
          onClick={() => {
            const components = parseTemplateComponents()
            const payload: any = { to, template: { name: templateName, language: templateLanguage } , ...buildAuthPart() }
            if (components) payload.template.components = components
            send("/api/whatsapp/cloud/send", payload, "template")
          }}
          style={{ padding: "8px 16px", background: canSendTemplate ? "#22c55e" : "#a3a3a3", color: "white", border: 0, borderRadius: 6 }}
        >
          {loading === "template" ? "جارٍ الإرسال..." : "إرسال Template"}
        </button>
      </div>

      <hr style={{ margin: "20px 0" }} />

      <h2 style={{ fontSize: 18, fontWeight: 700, marginBottom: 8 }}>فحص حالة الرقم (Status)</h2>
      <div>
        <button
          disabled={loading !== null}
          onClick={async () => {
            setLoading("status")
            setStatusResult(null)
            try {
              const res = await fetch("/api/whatsapp/cloud/status", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(buildAuthPart())
              })
              const data = await res.json()
              setStatusResult(data)
            } catch (e: any) {
              setStatusResult({ success: false, error: e?.message || "Network error" })
            } finally {
              setLoading(null)
            }
          }}
          style={{ padding: "8px 16px", background: "#6366f1", color: "white", border: 0, borderRadius: 6 }}
        >
          {loading === "status" ? "جارٍ الفحص..." : "فحص الحالة"}
        </button>
      </div>
      <div style={{ marginTop: 8 }}>
        <pre style={{ background: "#0b1020", color: "#d1d5db", padding: 12, borderRadius: 6, overflowX: "auto" }}>
          {statusResult ? JSON.stringify(statusResult, null, 2) : "— لا توجد نتيجة فحص بعد —"}
        </pre>
      </div>

      <h2 style={{ fontSize: 18, fontWeight: 700, marginBottom: 8 }}>إرسال نص</h2>
      <textarea value={text} onChange={e => setText(e.target.value)} placeholder="نص الرسالة (مثال: تم إضافة مبلغ 50,000 ريال إلى حسابكم)" rows={3} style={{ width: "100%", padding: 8 }} />
      <div style={{ marginTop: 8 }}>
        <button
          disabled={!canSendText || loading !== null}
          onClick={() => send("/api/whatsapp/cloud/send", { to, text, ...buildAuthPart() }, "text")}
          style={{ padding: "8px 16px", background: canSendText ? "#22c55e" : "#a3a3a3", color: "white", border: 0, borderRadius: 6 }}
        >
          {loading === "text" ? "جارٍ الإرسال..." : "إرسال نص"}
        </button>
      </div>

      <hr style={{ margin: "20px 0" }} />

      <h2 style={{ fontSize: 18, fontWeight: 700, marginBottom: 8 }}>إرسال PDF/مستند عبر رابط عام</h2>
      <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: 16 }}>
        <div>
          <label>Document URL</label>
          <input value={documentUrl} onChange={e => setDocumentUrl(e.target.value)} placeholder="https://example.com/path/file.pdf" style={{ width: "100%", padding: 8, marginTop: 4 }} />
        </div>
        <div>
          <label>Filename (اختياري)</label>
          <input value={filename} onChange={e => setFilename(e.target.value)} placeholder="file.pdf" style={{ width: "100%", padding: 8, marginTop: 4 }} />
        </div>
      </div>
      <div style={{ marginTop: 8 }}>
        <label>Caption (اختياري)</label>
        <input value={caption} onChange={e => setCaption(e.target.value)} placeholder="سند قبض رقم 1001" style={{ width: "100%", padding: 8 }} />
      </div>
      <div style={{ marginTop: 8 }}>
        <button
          disabled={!canSendDoc || loading !== null}
          onClick={() => send("/api/whatsapp/cloud/send", { to, documentUrl, filename, caption, ...buildAuthPart() }, "doc")}
          style={{ padding: "8px 16px", background: canSendDoc ? "#22c55e" : "#a3a3a3", color: "white", border: 0, borderRadius: 6 }}
        >
          {loading === "doc" ? "جارٍ الإرسال..." : "إرسال مستند"}
        </button>
      </div>

      <hr style={{ margin: "20px 0" }} />

      <h2 style={{ fontSize: 18, fontWeight: 700, marginBottom: 8 }}>إرسال نص ثم PDF في نفس الطلب</h2>
      <p style={{ color: "#666", marginBottom: 8 }}>مفيد لسيناريوهات: سند قبض/صرف/قيد يومي/كشف حساب مرفق مع رسالة.</p>
      <div>
        <button
          disabled={!canSendBundle || loading !== null}
          onClick={() => send("/api/whatsapp/cloud/send-bundle", { to, text, documentUrl, filename, caption, ...buildAuthPart() }, "bundle")}
          style={{ padding: "8px 16px", background: canSendBundle ? "#22c55e" : "#a3a3a3", color: "white", border: 0, borderRadius: 6 }}
        >
          {loading === "bundle" ? "جارٍ الإرسال..." : "إرسال نص ثم مستند"}
        </button>
      </div>

      <hr style={{ margin: "20px 0" }} />

      <h2 style={{ fontSize: 18, fontWeight: 700, marginBottom: 8 }}>النتيجة</h2>
      <pre style={{ background: "#0b1020", color: "#d1d5db", padding: 12, borderRadius: 6, overflowX: "auto" }}>
        {result ? JSON.stringify(result, null, 2) : "— لا توجد نتيجة بعد —"}
      </pre>

      <div style={{ marginTop: 16, color: "#666" }}>
        <ul>
          <li>إذا كانت الرسائل مبادرة من الشركة وخارج نافذة 24 ساعة، فقد يتطلب الإرسال استخدام قوالب معتمدة من Meta.</li>
          <li>تأكد أن رابط الملف عام ويمكن الوصول إليه مباشرة.</li>
        </ul>
      </div>
    </div>
  )
}
