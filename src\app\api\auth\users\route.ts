import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'
import jwt from 'jsonwebtoken'
import crypto from 'crypto'

// POST - تسجيل دخول المستخدم
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { username, password } = body

    if (!username || !password) {
      return NextResponse.json(
        { success: false, error: 'اسم المستخدم وكلمة المرور مطلوبان' },
        { status: 400 }
      )
    }

    console.log('🔍 محاولة تسجيل دخول المستخدم:', username)

    // البحث في قاعدة البيانات أولاً
    try {
      console.log('📊 البحث في قاعدة البيانات...')
      const userResult = await query(`
        SELECT u.*, e.name as employee_name, ur.display_name as role_display_name
        FROM users u
        LEFT JOIN employees e ON u.employee_id = e.id
        LEFT JOIN user_roles ur ON u.role = ur.role_name
        WHERE u.username = $1 AND u.is_active = true
      `, [username])

      if (userResult.rows.length > 0) {
        const dbUser = userResult.rows[0]
        console.log('✅ تم العثور على المستخدم في قاعدة البيانات:', dbUser.username)

        // التحقق من كلمة المرور المشفرة باستخدام bcrypt
        const bcrypt = require('bcryptjs')
        const isPasswordValid = await bcrypt.compare(password, dbUser.password_hash)

        if (!isPasswordValid) {
          console.log('❌ كلمة المرور غير صحيحة للمستخدم:', username)
          return NextResponse.json(
            { success: false, error: 'كلمة المرور غير صحيحة' },
            { status: 401 }
          )
        }

        console.log('✅ تم التحقق من كلمة المرور بنجاح')

        // جلب صلاحيات المستخدم من قاعدة البيانات
        let userPermissions = []
        try {
          const permissionsResult = await query(`
            SELECT up.permission_key, p.name as permission_name, p.category
            FROM user_permissions up
            LEFT JOIN permissions p ON up.permission_key = p.key
            WHERE up.user_id = $1 AND up.is_active = true
          `, [dbUser.id])

          userPermissions = permissionsResult.rows.map(row => ({
            key: row.permission_key,
            name: row.permission_name || row.permission_key,
            category: row.category || 'عام'
          }))

          console.log(`🔐 تم جلب ${userPermissions.length} صلاحية من قاعدة البيانات`)
        } catch (permError) {
          console.log('⚠️ خطأ في جلب الصلاحيات من قاعدة البيانات:', permError.message)
          // استخدام صلاحيات افتراضية حسب الدور
          userPermissions = getDefaultPermissionsByRole(dbUser.role)
        }

        // إنشاء sessionToken وتسجيل الجلسة في قاعدة البيانات لتتوافق مع middleware
        const sessionToken = crypto.randomBytes(32).toString('hex')

        try {
          await query(`
            INSERT INTO active_sessions (user_id, session_token, is_active, last_activity, created_at, updated_at)
            VALUES ($1, $2, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
          `, [dbUser.id, sessionToken])
        } catch (sessErr) {
          console.error('⚠️ فشل إنشاء سجل الجلسة في active_sessions:', (sessErr as any).message)
        }

        // توليد JWT كما يتوقع middleware.ts
        const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-here'
        const token = jwt.sign({ userId: dbUser.id, sessionToken }, JWT_SECRET, { expiresIn: '24h' })

        const response = NextResponse.json({
          success: true,
          token,
          user: {
            id: dbUser.id,
            username: dbUser.username,
            name: dbUser.name,
            role: dbUser.role,
            role_display_name: dbUser.role_display_name || dbUser.role,
            user_type: dbUser.user_type || 'user',
            status: dbUser.status || 'active',
            is_active: dbUser.is_active !== false,
            employee_name: dbUser.employee_name || dbUser.name,
            permissions: userPermissions
          }
        })

        // تعيين كوكي HttpOnly ليستعمله الـ middleware
        response.cookies.set('auth-token', token, {
          httpOnly: true,
          sameSite: 'lax',
          path: '/',
          maxAge: 60 * 60 * 24 // 24 ساعة
        })

        return response
      }

    } catch (error) {
      console.error('❌ خطأ في قاعدة البيانات:', error)
      console.log('⚠️ فشل الاتصال بقاعدة البيانات، استخدام المستخدمين التجريبيين')
    }

    // إذا لم يتم العثور على المستخدم في قاعدة البيانات
    console.log('❌ المستخدم غير موجود في قاعدة البيانات:', username)
    return NextResponse.json(
      { success: false, error: 'اسم المستخدم غير موجود' },
      { status: 404 }
    )

  } catch (error) {
    console.error('❌ خطأ في تسجيل الدخول:', error)
    return NextResponse.json(
      { success: false, error: 'خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// دالة للحصول على الصلاحيات الافتراضية حسب الدور
function getDefaultPermissionsByRole(role: string) {
  const rolePermissions = {
    admin: [
      'users:view', 'users:create', 'users:update', 'users:delete', 'users:permissions',
      'cases:view', 'cases:create', 'cases:update', 'cases:delete', 'cases:assign', 'cases:status',
      'clients:view', 'clients:create', 'clients:update', 'clients:delete', 'clients:contact',
      'accounting:view', 'accounting:create', 'accounting:update', 'accounting:delete', 'accounting:approve', 'accounting:reports',
      'whatsapp:view', 'whatsapp:manage', 'whatsapp:send', 'whatsapp:templates', 'whatsapp:monitor', 'whatsapp:settings', 'whatsapp:contacts', 'whatsapp:reminders', 'whatsapp:notifications', 'whatsapp:logs', 'whatsapp:statistics',
      'settings:view', 'settings:update', 'settings:company', 'settings:system', 'settings:security',
      'reports:view', 'reports:export', 'reports:create', 'reports:schedule',
      'departments:view', 'departments:create', 'departments:update', 'departments:delete',
      'library:view', 'library:create', 'library:update', 'library:delete', 'library:download',
      'calendar:view', 'calendar:create', 'calendar:update', 'calendar:delete',
      'tasks:view', 'tasks:create', 'tasks:update', 'tasks:delete', 'tasks:assign',
      'notifications:view', 'notifications:create', 'notifications:manage',
      'backup:view', 'backup:create', 'backup:restore',
      'audit:view', 'audit:export',
      'ai:view', 'ai:manage', 'ai:chat'
    ],
    manager: [
      'users:view', 'cases:view', 'cases:create', 'cases:update', 'cases:assign',
      'clients:view', 'clients:create', 'clients:update', 'clients:contact',
      'accounting:view', 'accounting:create', 'accounting:update', 'accounting:reports',
      'whatsapp:view', 'whatsapp:send', 'whatsapp:templates', 'whatsapp:monitor',
      'reports:view', 'reports:export', 'library:view', 'library:download',
      'calendar:view', 'calendar:create', 'calendar:update',
      'tasks:view', 'tasks:create', 'tasks:update', 'tasks:assign'
    ],
    lawyer: [
      'cases:view', 'cases:create', 'cases:update',
      'clients:view', 'clients:create', 'clients:update', 'clients:contact',
      'whatsapp:view', 'whatsapp:send', 'reports:view',
      'library:view', 'library:download', 'calendar:view', 'calendar:create',
      'tasks:view', 'tasks:create', 'tasks:update'
    ]
  }

  return (rolePermissions[role] || rolePermissions.lawyer).map(key => ({
    key,
    name: getPermissionName(key),
    category: getPermissionCategory(key)
  }))
}

// دالة للحصول على اسم الصلاحية
function getPermissionName(key: string): string {
  const names: { [key: string]: string } = {
    'users:view': 'عرض المستخدمين',
    'users:create': 'إضافة مستخدم',
    'users:update': 'تعديل مستخدم',
    'users:delete': 'حذف مستخدم',
    'users:permissions': 'إدارة صلاحيات المستخدمين',
    'cases:view': 'عرض القضايا',
    'cases:create': 'إضافة قضية',
    'cases:update': 'تعديل قضية',
    'cases:delete': 'حذف قضية',
    'cases:assign': 'تعيين القضايا',
    'cases:status': 'تغيير حالة القضية',
    'clients:view': 'عرض العملاء',
    'clients:create': 'إضافة عميل',
    'clients:update': 'تعديل عميل',
    'clients:delete': 'حذف عميل',
    'clients:contact': 'التواصل مع العملاء',
    'whatsapp:view': 'عرض إعدادات WhatsApp',
    'whatsapp:manage': 'إدارة اتصال WhatsApp',
    'whatsapp:send': 'إرسال رسائل WhatsApp',
    'whatsapp:templates': 'إدارة قوالب الرسائل',
    'whatsapp:monitor': 'مراقبة حالة WhatsApp',
    'whatsapp:settings': 'تعديل إعدادات WhatsApp',
    'whatsapp:contacts': 'إدارة جهات اتصال WhatsApp',
    'whatsapp:reminders': 'إدارة تذكيرات WhatsApp',
    'whatsapp:notifications': 'إدارة إشعارات WhatsApp',
    'whatsapp:logs': 'عرض سجلات WhatsApp',
    'whatsapp:statistics': 'عرض إحصائيات WhatsApp'
  }
  return names[key] || key
}

// دالة للحصول على فئة الصلاحية
function getPermissionCategory(key: string): string {
  const categories: { [key: string]: string } = {
    'users:view': 'إدارة المستخدمين',
    'users:create': 'إدارة المستخدمين',
    'users:update': 'إدارة المستخدمين',
    'users:delete': 'إدارة المستخدمين',
    'users:permissions': 'إدارة المستخدمين',
    'cases:view': 'إدارة القضايا',
    'cases:create': 'إدارة القضايا',
    'cases:update': 'إدارة القضايا',
    'cases:delete': 'إدارة القضايا',
    'cases:assign': 'إدارة القضايا',
    'cases:status': 'إدارة القضايا',
    'clients:view': 'إدارة العملاء',
    'clients:create': 'إدارة العملاء',
    'clients:update': 'إدارة العملاء',
    'clients:delete': 'إدارة العملاء',
    'clients:contact': 'إدارة العملاء',
    'whatsapp:view': 'WhatsApp',
    'whatsapp:manage': 'WhatsApp',
    'whatsapp:send': 'WhatsApp',
    'whatsapp:templates': 'WhatsApp',
    'whatsapp:monitor': 'WhatsApp',
    'whatsapp:settings': 'WhatsApp',
    'whatsapp:contacts': 'WhatsApp',
    'whatsapp:reminders': 'WhatsApp',
    'whatsapp:notifications': 'WhatsApp',
    'whatsapp:logs': 'WhatsApp',
    'whatsapp:statistics': 'WhatsApp'
  }
  return categories[key] || 'عام'
}
