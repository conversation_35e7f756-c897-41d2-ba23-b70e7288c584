const fs = require('fs');
const path = require('path');

console.log('🔄 إنشاء قاعدة بيانات محلية بديلة...\n');

// إنشاء مجلد قاعدة البيانات
const dbDir = path.join(__dirname, 'database');
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
  console.log('✅ تم إنشاء مجلد قاعدة البيانات');
}

// إنشاء ملف قاعدة بيانات JSON بسيط
const dbFile = path.join(dbDir, 'case_movements.json');

const sampleData = {
  case_movements: [
    {
      id: 1,
      case_id: 1,
      case_number: '2024/001',
      case_title: 'قضية تجارية - شركة الأمل',
      movement_type: 'case_created',
      description: 'تم إنشاء القضية في النظام',
      created_by_name: 'النظام',
      priority: 'normal',
      client_name: 'شركة الأمل للتجارة',
      case_status: 'نشطة',
      created_at: new Date().toISOString(),
      movement_date: new Date().toISOString().split('T')[0]
    },
    {
      id: 2,
      case_id: 1,
      case_number: '2024/001',
      case_title: 'قضية تجارية - شركة الأمل',
      movement_type: 'case_assigned',
      description: 'تم توزيع القضية على المحامي أحمد محمد',
      created_by_name: 'المدير العام',
      priority: 'high',
      client_name: 'شركة الأمل للتجارة',
      case_status: 'موزعة',
      created_at: new Date(Date.now() - 86400000).toISOString(),
      movement_date: new Date(Date.now() - 86400000).toISOString().split('T')[0]
    },
    {
      id: 3,
      case_id: 2,
      case_number: '2024/002',
      case_title: 'قضية عمالية - محمد علي',
      movement_type: 'case_created',
      description: 'تم إنشاء القضية في النظام',
      created_by_name: 'النظام',
      priority: 'normal',
      client_name: 'محمد علي أحمد',
      case_status: 'نشطة',
      created_at: new Date(Date.now() - 172800000).toISOString(),
      movement_date: new Date(Date.now() - 172800000).toISOString().split('T')[0]
    },
    {
      id: 4,
      case_id: 2,
      case_number: '2024/002',
      case_title: 'قضية عمالية - محمد علي',
      movement_type: 'hearing_scheduled',
      description: 'تم تحديد جلسة للقضية في المحكمة العمالية',
      created_by_name: 'أحمد محمد',
      priority: 'high',
      client_name: 'محمد علي أحمد',
      case_status: 'جلسة مجدولة',
      created_at: new Date(Date.now() - 86400000).toISOString(),
      movement_date: new Date(Date.now() - 86400000).toISOString().split('T')[0]
    },
    {
      id: 5,
      case_id: 3,
      case_number: '2024/003',
      case_title: 'قضية مدنية - فاطمة سالم',
      movement_type: 'case_created',
      description: 'تم إنشاء القضية في النظام',
      created_by_name: 'النظام',
      priority: 'normal',
      client_name: 'فاطمة سالم محمد',
      case_status: 'نشطة',
      created_at: new Date(Date.now() - 259200000).toISOString(),
      movement_date: new Date(Date.now() - 259200000).toISOString().split('T')[0]
    },
    {
      id: 6,
      case_id: 3,
      case_number: '2024/003',
      case_title: 'قضية مدنية - فاطمة سالم',
      movement_type: 'document_uploaded',
      description: 'تم رفع وثيقة "عقد الإيجار الأصلي" للقضية',
      created_by_name: 'سارة أحمد',
      priority: 'normal',
      client_name: 'فاطمة سالم محمد',
      case_status: 'قيد المراجعة',
      created_at: new Date(Date.now() - 172800000).toISOString(),
      movement_date: new Date(Date.now() - 172800000).toISOString().split('T')[0]
    },
    {
      id: 7,
      case_id: 4,
      case_number: '2024/004',
      case_title: 'قضية جنائية - خالد يوسف',
      movement_type: 'case_created',
      description: 'تم إنشاء القضية في النظام',
      created_by_name: 'النظام',
      priority: 'urgent',
      client_name: 'خالد يوسف علي',
      case_status: 'نشطة',
      created_at: new Date(Date.now() - 345600000).toISOString(),
      movement_date: new Date(Date.now() - 345600000).toISOString().split('T')[0]
    },
    {
      id: 8,
      case_id: 4,
      case_number: '2024/004',
      case_title: 'قضية جنائية - خالد يوسف',
      movement_type: 'follow_added',
      description: 'تم إضافة متابعة: تم تقديم الاستئناف للمحكمة العليا',
      created_by_name: 'محمد الحاشدي',
      priority: 'urgent',
      client_name: 'خالد يوسف علي',
      case_status: 'استئناف',
      created_at: new Date(Date.now() - 86400000).toISOString(),
      movement_date: new Date(Date.now() - 86400000).toISOString().split('T')[0]
    },
    {
      id: 9,
      case_id: 5,
      case_number: '2024/005',
      case_title: 'قضية إدارية - شركة النور',
      movement_type: 'case_created',
      description: 'تم إنشاء القضية في النظام',
      created_by_name: 'النظام',
      priority: 'normal',
      client_name: 'شركة النور للمقاولات',
      case_status: 'نشطة',
      created_at: new Date(Date.now() - 432000000).toISOString(),
      movement_date: new Date(Date.now() - 432000000).toISOString().split('T')[0]
    },
    {
      id: 10,
      case_id: 5,
      case_number: '2024/005',
      case_title: 'قضية إدارية - شركة النور',
      movement_type: 'case_status_changed',
      description: 'تم تغيير حالة القضية من "نشطة" إلى "قيد المراجعة"',
      created_by_name: 'أحمد محمد',
      priority: 'normal',
      client_name: 'شركة النور للمقاولات',
      case_status: 'قيد المراجعة',
      created_at: new Date(Date.now() - 259200000).toISOString(),
      movement_date: new Date(Date.now() - 259200000).toISOString().split('T')[0]
    }
  ],
  metadata: {
    total_movements: 10,
    total_cases: 5,
    last_updated: new Date().toISOString(),
    database_type: 'JSON_FALLBACK'
  }
};

fs.writeFileSync(dbFile, JSON.stringify(sampleData, null, 2));
console.log('✅ تم إنشاء قاعدة البيانات المحلية');
console.log(`📁 المسار: ${dbFile}`);
console.log(`📊 البيانات: ${sampleData.case_movements.length} حركة لـ ${sampleData.metadata.total_cases} قضايا`);

console.log('\n🎉 قاعدة البيانات المحلية جاهزة!');
console.log('الآن سأقوم بتحديث النظام لاستخدامها...\n');
