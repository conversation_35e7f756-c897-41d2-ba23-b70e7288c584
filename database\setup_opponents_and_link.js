const { Client } = require('pg')

const DB_NAMES = (process.env.DB_NAMES ? process.env.DB_NAMES.split(',').map(s=>s.trim()).filter(Boolean) : ['mohammidev'])
const base = { host: 'localhost', port: 5432, user: 'postgres', password: 'yemen123' }

async function run(db){
  const client = new Client({ ...base, database: db })
  await client.connect()
  try {
    console.log(`\n=== Database: ${db} (opponents setup) ===`)

    // Create opponents table
    await client.query(`
      CREATE TABLE IF NOT EXISTS opponents (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        phone VARCHAR(50),
        address TEXT,
        notes TEXT,
        created_at TIMESTAMP DEFAULT NOW()
      );
    `)
    console.log('✅ ensured opponents table')

    // Add opponent_id to issues if missing
    const cols = await client.query(`SELECT column_name FROM information_schema.columns WHERE table_name='issues'`)
    const hasCol = cols.rows.some(r=>r.column_name==='opponent_id')
    if(!hasCol){
      await client.query(`ALTER TABLE issues ADD COLUMN opponent_id INTEGER NULL REFERENCES opponents(id)`)
      console.log('✅ added opponent_id to issues')
    } else {
      console.log('ℹ️ opponent_id exists in issues')
    }

  } finally {
    await client.end()
  }
}

;(async () => {
  for(const db of DB_NAMES){
    try{ await run(db) } catch(e){ console.error('❌ error on', db, e.message) }
  }
  console.log('\nDone.')
})()
