// استعادة مبسطة لقاعدة البيانات
const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

async function simpleRestore() {
  console.log('🔄 بدء الاستعادة المبسطة...');
  
  try {
    // 1. الاتصال بقاعدة postgres الافتراضية
    const adminClient = new Client({
      host: 'localhost',
      port: 5432,
      database: 'postgres',
      user: 'postgres',
      password: 'yemen123'
    });
    
    await adminClient.connect();
    console.log('✅ متصل بقاعدة postgres');
    
    // 2. إنهاء الاتصالات الحالية
    try {
      await adminClient.query(`
        SELECT pg_terminate_backend(pid)
        FROM pg_stat_activity
        WHERE datname = 'mohammi' AND pid <> pg_backend_pid()
      `);
      console.log('✅ تم إنهاء الاتصالات الحالية');
    } catch (error) {
      console.log('⚠️ لا توجد اتصالات لإنهائها');
    }
    
    // 3. حذف قاعدة البيانات إذا كانت موجودة
    try {
      await adminClient.query('DROP DATABASE IF EXISTS mohammi');
      console.log('✅ تم حذف قاعدة البيانات القديمة');
    } catch (error) {
      console.log('⚠️ قاعدة البيانات غير موجودة');
    }
    
    // 4. إنشاء قاعدة البيانات الجديدة
    await adminClient.query(`CREATE DATABASE mohammi WITH ENCODING = 'UTF8'`);
    console.log('✅ تم إنشاء قاعدة البيانات الجديدة');
    
    await adminClient.end();
    
    // 5. قراءة ملف النسخة الاحتياطية
    const backupFile = path.join(__dirname, '..', 'mohammi.sql');
    let sqlContent = fs.readFileSync(backupFile, 'utf8');
    
    // إزالة الأوامر التي تسبب مشاكل
    sqlContent = sqlContent.replace(/DROP DATABASE IF EXISTS mohammi;/g, '');
    sqlContent = sqlContent.replace(/CREATE DATABASE mohammi.*?;/g, '');
    sqlContent = sqlContent.replace(/\\connect mohammi/g, '');
    sqlContent = sqlContent.replace(/LOCALE_PROVIDER = libc LOCALE = 'en_US\.UTF-8'/g, '');
    
    console.log('✅ تم تنظيف ملف النسخة الاحتياطية');
    
    // 6. الاتصال بقاعدة mohammi الجديدة
    const client = new Client({
      host: 'localhost',
      port: 5432,
      database: 'mohammi',
      user: 'postgres',
      password: 'yemen123'
    });
    
    await client.connect();
    console.log('✅ متصل بقاعدة mohammi');
    
    // 7. تقسيم الملف إلى أوامر منفصلة
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--') && !stmt.startsWith('SET'));
    
    console.log(`📊 عدد الأوامر: ${statements.length}`);
    
    // 8. تنفيذ الأوامر
    let successCount = 0;
    let errorCount = 0;
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      if (statement.includes('CREATE TABLE') || 
          statement.includes('INSERT INTO') || 
          statement.includes('CREATE INDEX') ||
          statement.includes('ALTER TABLE')) {
        
        try {
          await client.query(statement);
          successCount++;
          
          if (i % 50 === 0) {
            console.log(`📈 تم تنفيذ ${i + 1}/${statements.length} أوامر`);
          }
        } catch (error) {
          errorCount++;
          if (error.message.includes('already exists')) {
            // تجاهل أخطاء الوجود المسبق
          } else {
            console.log(`⚠️ خطأ في الأمر ${i + 1}: ${error.message.substring(0, 100)}`);
          }
        }
      }
    }
    
    console.log(`✅ تم تنفيذ ${successCount} أمر بنجاح`);
    console.log(`⚠️ ${errorCount} خطأ (معظمها طبيعي)`);
    
    // 9. التحقق من النتائج
    console.log('\n🔍 التحقق من النتائج...');
    
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    console.log(`📊 عدد الجداول: ${tablesResult.rows.length}`);
    
    if (tablesResult.rows.length > 0) {
      console.log('📋 الجداول المستعادة:');
      tablesResult.rows.slice(0, 10).forEach((row, index) => {
        console.log(`   ${index + 1}. ${row.table_name}`);
      });
      
      if (tablesResult.rows.length > 10) {
        console.log(`   ... و ${tablesResult.rows.length - 10} جدول آخر`);
      }
    }
    
    // فحص بعض الجداول المهمة
    const importantTables = ['companies', 'users', 'clients', 'cases', 'employees'];
    
    for (const tableName of importantTables) {
      try {
        const result = await client.query(`SELECT COUNT(*) as count FROM ${tableName}`);
        console.log(`📊 ${tableName}: ${result.rows[0].count} سجل`);
      } catch (error) {
        console.log(`❌ جدول ${tableName}: غير موجود`);
      }
    }
    
    await client.end();
    
    console.log('\n🎉 تمت الاستعادة بنجاح!');
    console.log('✅ قاعدة البيانات جاهزة للاستخدام');
    
  } catch (error) {
    console.error('❌ خطأ في الاستعادة:', error.message);
  }
}

simpleRestore();
