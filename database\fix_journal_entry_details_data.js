const { Client } = require('pg');

async function fixJournalEntryDetailsData() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: process.env.PGDATABASE || 'mohammi',
    user: process.env.PGUSER || 'postgres',
    password: process.env.PGPASSWORD || 'yemen123',
  });

  try {
    await client.connect();
    console.log('🔗 متصل بقاعدة البيانات');

    // 1. فحص البيانات الحالية في cost_center_id
    console.log('🔍 فحص البيانات الحالية في cost_center_id...');
    const invalidCostCenters = await client.query(`
      SELECT DISTINCT cost_center_id 
      FROM journal_entry_details 
      WHERE cost_center_id IS NOT NULL 
        AND cost_center_id NOT IN (SELECT id FROM cost_centers)
    `);

    if (invalidCostCenters.rows.length > 0) {
      console.log(`⚠️ وجدت ${invalidCostCenters.rows.length} قيم غير صحيحة في cost_center_id:`);
      invalidCostCenters.rows.forEach(row => {
        console.log(`   - ${row.cost_center_id}`);
      });

      // تعيين قيمة افتراضية للقيم غير الصحيحة
      console.log('🔧 إصلاح القيم غير الصحيحة...');
      await client.query(`
        UPDATE journal_entry_details 
        SET cost_center_id = 1 
        WHERE cost_center_id IS NOT NULL 
          AND cost_center_id NOT IN (SELECT id FROM cost_centers)
      `);
      console.log('✅ تم إصلاح القيم غير الصحيحة');
    } else {
      console.log('✅ جميع قيم cost_center_id صحيحة');
    }

    // 2. فحص البيانات في issues_id
    console.log('🔍 فحص البيانات الحالية في issues_id...');
    const invalidIssues = await client.query(`
      SELECT DISTINCT issues_id 
      FROM journal_entry_details 
      WHERE issues_id IS NOT NULL 
        AND issues_id NOT IN (SELECT id FROM issues)
    `);

    if (invalidIssues.rows.length > 0) {
      console.log(`⚠️ وجدت ${invalidIssues.rows.length} قيم غير صحيحة في issues_id:`);
      invalidIssues.rows.forEach(row => {
        console.log(`   - ${row.issues_id}`);
      });

      // حذف القيم غير الصحيحة
      console.log('🔧 إصلاح القيم غير الصحيحة...');
      await client.query(`
        UPDATE journal_entry_details 
        SET issues_id = NULL 
        WHERE issues_id IS NOT NULL 
          AND issues_id NOT IN (SELECT id FROM issues)
      `);
      console.log('✅ تم إصلاح القيم غير الصحيحة');
    } else {
      console.log('✅ جميع قيم issues_id صحيحة');
    }

    // 3. إضافة القيود المرجعية مرة أخرى
    console.log('🔗 إضافة القيود المرجعية...');
    
    // حذف القيود القديمة إن وجدت
    try {
      await client.query(`
        ALTER TABLE journal_entry_details 
        DROP CONSTRAINT IF EXISTS fk_jed_cost_center_id
      `);
      await client.query(`
        ALTER TABLE journal_entry_details 
        DROP CONSTRAINT IF EXISTS fk_jed_issues_id
      `);
    } catch (error) {
      // تجاهل الأخطاء
    }

    // إضافة القيود الجديدة
    try {
      await client.query(`
        ALTER TABLE journal_entry_details 
        ADD CONSTRAINT fk_jed_cost_center_id 
        FOREIGN KEY (cost_center_id) REFERENCES cost_centers(id)
      `);
      console.log('✅ تم إضافة قيد مرجعي لـ cost_center_id');
    } catch (error) {
      console.log('⚠️ تعذر إضافة قيد مرجعي cost_center_id:', error.message);
    }

    try {
      await client.query(`
        ALTER TABLE journal_entry_details 
        ADD CONSTRAINT fk_jed_issues_id 
        FOREIGN KEY (issues_id) REFERENCES issues(id)
      `);
      console.log('✅ تم إضافة قيد مرجعي لـ issues_id');
    } catch (error) {
      console.log('⚠️ تعذر إضافة قيد مرجعي issues_id:', error.message);
    }

    // 4. عرض إحصائيات البيانات
    console.log('\n📊 إحصائيات البيانات:');
    
    const totalRecords = await client.query(`
      SELECT COUNT(*) as count FROM journal_entry_details
    `);
    console.log(`   📋 إجمالي السجلات: ${totalRecords.rows[0].count}`);

    const withCostCenter = await client.query(`
      SELECT COUNT(*) as count FROM journal_entry_details 
      WHERE cost_center_id IS NOT NULL
    `);
    console.log(`   🏢 السجلات مع مركز تكلفة: ${withCostCenter.rows[0].count}`);

    const withIssues = await client.query(`
      SELECT COUNT(*) as count FROM journal_entry_details 
      WHERE issues_id IS NOT NULL
    `);
    console.log(`   ⚖️ السجلات مع قضية: ${withIssues.rows[0].count}`);

    // 5. عرض مراكز التكلفة المتاحة
    console.log('\n🏢 مراكز التكلفة المتاحة:');
    const costCenters = await client.query(`
      SELECT id, center_code, center_name 
      FROM cost_centers 
      WHERE is_active = true 
      ORDER BY id
    `);
    
    costCenters.rows.forEach(center => {
      console.log(`   ${center.id}. ${center.center_code} - ${center.center_name}`);
    });

    console.log('\n✅ تم إصلاح بيانات جدول journal_entry_details بنجاح');

  } catch (err) {
    console.error('❌ خطأ أثناء الإصلاح:', err.message);
    process.exitCode = 1;
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

if (require.main === module) {
  fixJournalEntryDetailsData();
}

module.exports = { fixJournalEntryDetailsData };
