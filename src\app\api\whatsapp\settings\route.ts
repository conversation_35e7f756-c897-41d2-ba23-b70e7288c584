/**
 * API إعدادات WhatsApp الكامل
 */

import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب إعدادات WhatsApp
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 جلب إعدادات WhatsApp...')

    // جلب بيانات الشركة مع إعدادات WhatsApp
    const companyResult = await query(`
      SELECT 
        id,
        name as company_name,
        phone,
        email,
        address,
        whatsapp_phone,
        whatsapp_business_name,
        whatsapp_enabled,
        whatsapp_session_name,
        whatsapp_auto_reply,
        whatsapp_business_hours_start,
        whatsapp_business_hours_end,
        whatsapp_auto_reply_message
      FROM companies 
      WHERE is_active = true
      LIMIT 1
    `)

    if (companyResult.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'لم يتم العثور على بيانات الشركة'
      }, { status: 404 })
    }

    const company = companyResult.rows[0]

    // جلب جهات الاتصال
    const contactsResult = await query(`
      SELECT 
        contact_type,
        COUNT(*) as count
      FROM whatsapp_contacts 
      WHERE company_id = $1 AND is_active = true
      GROUP BY contact_type
    `, [company.id])

    const contacts = {
      clients: 0,
      employees: 0
    }

    contactsResult.rows.forEach(row => {
      if (row.contact_type === 'client') contacts.clients = parseInt(row.count)
      if (row.contact_type === 'employee') contacts.employees = parseInt(row.count)
    })

    // جلب إحصائيات اليوم
    const today = new Date().toISOString().split('T')[0]
    const statsResult = await query(`
      SELECT 
        messages_sent,
        messages_delivered,
        messages_failed
      FROM whatsapp_daily_stats 
      WHERE company_id = $1 AND stat_date = $2
    `, [company.id, today])

    const stats = statsResult.rows[0] || {
      messages_sent: 0,
      messages_delivered: 0,
      messages_failed: 0
    }

    const settings = {
      company: {
        id: company.id,
        name: company.company_name,
        phone: company.phone,
        email: company.email,
        address: company.address
      },
      whatsapp: {
        enabled: company.whatsapp_enabled || false,
        phone: company.whatsapp_phone || '',
        businessName: company.whatsapp_business_name || '',
        sessionName: company.whatsapp_session_name || 'whatsapp_session',
        autoReply: company.whatsapp_auto_reply || false,
        businessHoursStart: company.whatsapp_business_hours_start || '08:00',
        businessHoursEnd: company.whatsapp_business_hours_end || '17:00',
        autoReplyMessage: company.whatsapp_auto_reply_message || 'مرحباً بك، سيتم الرد عليك قريباً'
      },
      contacts,
      stats,
      status: {
        connected: false,
        lastActivity: null,
        qrCode: null
      }
    }

    console.log('✅ تم جلب إعدادات WhatsApp بنجاح')

    return NextResponse.json({
      success: true,
      data: settings
    })

  } catch (error) {
    console.error('❌ خطأ في جلب إعدادات WhatsApp:', error)
    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم'
    }, { status: 500 })
  }
}

// POST - حفظ إعدادات WhatsApp
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    console.log('💾 حفظ إعدادات WhatsApp:', body)

    const {
      companyName,
      phone,
      email,
      address,
      whatsappPhone,
      whatsappBusinessName,
      whatsappEnabled,
      whatsappAutoReply,
      businessHoursStart,
      businessHoursEnd,
      whatsappAutoReplyMessage
    } = body

    // التحقق من البيانات المطلوبة
    if (!companyName) {
      return NextResponse.json({
        success: false,
        error: 'اسم الشركة مطلوب'
      }, { status: 400 })
    }

    // تحديث بيانات الشركة
    await query(`
      UPDATE companies SET
        name = $1,
        phone = $2,
        email = $3,
        address = $4,
        whatsapp_phone = $5,
        whatsapp_business_name = $6,
        whatsapp_enabled = $7,
        whatsapp_auto_reply = $8,
        whatsapp_business_hours_start = $9,
        whatsapp_business_hours_end = $10,
        whatsapp_auto_reply_message = $11,
        updated_at = CURRENT_TIMESTAMP
      WHERE is_active = true
    `, [
      companyName,
      phone,
      email,
      address,
      whatsappPhone,
      whatsappBusinessName,
      whatsappEnabled,
      whatsappAutoReply,
      businessHoursStart,
      businessHoursEnd,
      whatsappAutoReplyMessage
    ])

    console.log('✅ تم حفظ إعدادات WhatsApp بنجاح')

    return NextResponse.json({
      success: true,
      message: 'تم حفظ الإعدادات بنجاح'
    })

  } catch (error) {
    console.error('❌ خطأ في حفظ إعدادات WhatsApp:', error)
    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم'
    }, { status: 500 })
  }
}
