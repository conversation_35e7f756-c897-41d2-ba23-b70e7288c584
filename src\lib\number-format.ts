export type NumberFormatOptions = {
  minFractionDigits?: number
  maxFractionDigits?: number
  locale?: string
}

// تنسيق عام للأرقام بعدد كسور لا يزيد عن رقمين افتراضيًا
export function formatNumber(value: number | string | null | undefined, opts: NumberFormatOptions = {}): string {
  if (value === null || value === undefined || value === '') return '0'
  const num = typeof value === 'string' ? Number(value) : value
  if (Number.isNaN(num)) return '0'

  const {
    minFractionDigits = 0,
    maxFractionDigits = 2,
    locale = 'ar'
  } = opts

  return new Intl.NumberFormat(locale, {
    minimumFractionDigits: minFractionDigits,
    maximumFractionDigits: maxFractionDigits,
  }).format(num)
}

// تنسيق مبسط للعملات: رقمين بعد الفاصلة كحد أقصى وبالسياق العربي
export function formatCurrency(value: number | string | null | undefined, locale: string = 'ar'): string {
  return formatNumber(value, { minFractionDigits: 0, maxFractionDigits: 2, locale })
}
