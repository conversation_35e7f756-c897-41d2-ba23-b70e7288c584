/**
 * مكون لضمان عرض الأرقام باللاتينية في جميع أنحاء النظام
 */

import React from 'react'
import { convertArabicToLatinNumbers, ensureLatinNumbers } from '@/lib/currency-formatter'

interface LatinNumberProps {
  value: number | string | null | undefined
  className?: string
  style?: React.CSSProperties
  format?: 'currency' | 'number' | 'percentage' | 'raw'
  currency?: string
  decimals?: number
  showZero?: boolean
}

/**
 * مكون لعرض الأرقام باللاتينية
 */
export function LatinNumber({ 
  value, 
  className = '', 
  style = {},
  format = 'number',
  currency = 'ر.س',
  decimals = 2,
  showZero = true
}: LatinNumberProps) {
  
  const formatValue = () => {
    if (value === null || value === undefined) {
      return showZero ? '0.00' : '-'
    }

    const numValue = typeof value === 'string' ? parseFloat(convertArabicToLatinNumbers(value)) : value
    
    if (isNaN(numValue)) {
      return showZero ? '0.00' : '-'
    }

    if (numValue === 0 && !showZero) {
      return '-'
    }

    switch (format) {
      case 'currency':
        return `${numValue.toLocaleString('en-US', {
          minimumFractionDigits: decimals,
          maximumFractionDigits: decimals
        })} ${currency}`
      
      case 'percentage':
        return `${numValue.toFixed(decimals)}%`
      
      case 'number':
        return numValue.toLocaleString('en-US', {
          minimumFractionDigits: decimals,
          maximumFractionDigits: decimals
        })
      
      case 'raw':
        return numValue.toFixed(decimals)
      
      default:
        return numValue.toString()
    }
  }

  const combinedStyle = {
    fontVariantNumeric: 'lining-nums tabular-nums',
    WebkitFontFeatureSettings: '"lnum", "tnum"',
    fontFeatureSettings: '"lnum", "tnum"',
    direction: 'ltr' as const,
    unicodeBidi: 'embed' as const,
    ...style
  }

  return (
    <span 
      className={`latin-numbers ${className}`}
      style={combinedStyle}
    >
      {formatValue()}
    </span>
  )
}

/**
 * مكون خاص للعملة
 */
export function CurrencyDisplay({ 
  amount, 
  currency = 'ر.س', 
  className = '',
  showZero = true 
}: {
  amount: number | string | null | undefined
  currency?: string
  className?: string
  showZero?: boolean
}) {
  return (
    <LatinNumber 
      value={amount}
      format="currency"
      currency={currency}
      className={`currency-text ${className}`}
      showZero={showZero}
    />
  )
}

/**
 * مكون خاص للنسب المئوية
 */
export function PercentageDisplay({ 
  percentage, 
  decimals = 2,
  className = '' 
}: {
  percentage: number | string | null | undefined
  decimals?: number
  className?: string
}) {
  return (
    <LatinNumber 
      value={percentage}
      format="percentage"
      decimals={decimals}
      className={`percentage-text ${className}`}
    />
  )
}

/**
 * مكون خاص للأرقام في الجداول
 */
export function TableNumber({ 
  value, 
  className = '',
  showZero = false 
}: {
  value: number | string | null | undefined
  className?: string
  showZero?: boolean
}) {
  return (
    <LatinNumber 
      value={value}
      format="number"
      className={`number-cell ${className}`}
      showZero={showZero}
    />
  )
}

/**
 * مكون خاص للأرصدة مع الألوان
 */
export function BalanceDisplay({ 
  balance, 
  accountNature = 'مدين',
  currency = 'ر.س',
  className = '' 
}: {
  balance: number | string | null | undefined
  accountNature?: string
  currency?: string
  className?: string
}) {
  const numBalance = typeof balance === 'string' ? parseFloat(convertArabicToLatinNumbers(balance)) : (balance || 0)
  
  let balanceType = ''
  let colorClass = ''
  
  if (accountNature === 'مدين') {
    if (numBalance >= 0) {
      balanceType = 'مدين'
      colorClass = 'text-green-600'
    } else {
      balanceType = 'دائن'
      colorClass = 'text-red-600'
    }
  } else {
    if (numBalance >= 0) {
      balanceType = 'دائن'
      colorClass = 'text-green-600'
    } else {
      balanceType = 'مدين'
      colorClass = 'text-red-600'
    }
  }

  return (
    <span className={`balance-text ${colorClass} ${className}`}>
      <LatinNumber 
        value={Math.abs(numBalance)}
        format="currency"
        currency={currency}
      />
      <span className="mr-1">{balanceType}</span>
    </span>
  )
}

/**
 * مكون لحقول الإدخال المالية
 */
export function FinancialInput({ 
  value, 
  onChange, 
  placeholder = '0.00',
  className = '',
  ...props 
}: {
  value: string | number
  onChange: (value: string) => void
  placeholder?: string
  className?: string
  [key: string]: any
}) {
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let inputValue = e.target.value
    
    // تحويل الأرقام العربية إلى لاتينية
    inputValue = convertArabicToLatinNumbers(inputValue)
    
    // السماح فقط بالأرقام والنقطة والسالب
    inputValue = inputValue.replace(/[^\d.-]/g, '')
    
    onChange(inputValue)
  }

  const inputStyle = {
    fontVariantNumeric: 'lining-nums',
    WebkitFontFeatureSettings: '"lnum"',
    fontFeatureSettings: '"lnum"',
    direction: 'ltr' as const,
    textAlign: 'right' as const
  }

  return (
    <input
      type="text"
      value={ensureLatinNumbers(value)}
      onChange={handleChange}
      placeholder={placeholder}
      className={`financial-input ${className}`}
      style={inputStyle}
      {...props}
    />
  )
}

/**
 * Hook لضمان الأرقام اللاتينية
 */
export function useLatinNumbers() {
  const formatCurrency = (amount: number | string | null | undefined, currency = 'ر.س') => {
    if (!amount) return `0.00 ${currency}`
    const numAmount = typeof amount === 'string' ? parseFloat(convertArabicToLatinNumbers(amount)) : amount
    if (isNaN(numAmount)) return `0.00 ${currency}`
    return `${numAmount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} ${currency}`
  }

  const formatNumber = (amount: number | string | null | undefined) => {
    if (!amount) return '0.00'
    const numAmount = typeof amount === 'string' ? parseFloat(convertArabicToLatinNumbers(amount)) : amount
    if (isNaN(numAmount)) return '0.00'
    return numAmount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
  }

  const parseAmount = (value: string | number) => {
    if (typeof value === 'number') return value
    const cleaned = convertArabicToLatinNumbers(value).replace(/[^\d.-]/g, '')
    const parsed = parseFloat(cleaned)
    return isNaN(parsed) ? 0 : parsed
  }

  return {
    formatCurrency,
    formatNumber,
    parseAmount,
    convertArabicToLatinNumbers,
    ensureLatinNumbers
  }
}
