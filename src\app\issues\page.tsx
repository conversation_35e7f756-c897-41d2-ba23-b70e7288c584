'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ClientSelect } from '@/components/ui/client-select'
import MainLayout from '@/components/layout/main-layout'
import { Scale, Plus, Edit, Eye, Trash2, Search, Save, X } from 'lucide-react'

interface Issue {
  id: number
  case_number: string
  title: string
  description: string
  client_id: number
  client_name: string
  client_phone: string
  court_id: number
  court_name: string
  issue_type: string
  issue_type_id?: number
  status: string
  case_amount: number
  currency_id: number
  currency_symbol: string
  amount_yer: number
  notes: string
  contract_method: string
  contract_date: string
  created_date: string
  opponent_name?: string
  opponent_id?: number
}

interface Court {
  id: number
  name: string
  governorate_name: string
}

interface Currency {
  id: number
  currency_name: string
  symbol: string
  exchange_rate: number
  is_base_currency: boolean
}

interface Status {
  value: string
  label: string
}

interface ContractMethod {
  value: string
  label: string
}

interface IssueType {
  id: number
  name: string
}

export default function IssuesPage() {
  const [issues, setIssues] = useState<Issue[]>([])
  const [opponents, setOpponents] = useState<Array<{id:number; name:string}>>([])
  const [courts, setCourts] = useState<Court[]>([])
  const [currencies, setCurrencies] = useState<Currency[]>([])
  const [statuses, setStatuses] = useState<Status[]>([])
  const [contractMethods, setContractMethods] = useState<ContractMethod[]>([])
  const [issueTypes, setIssueTypes] = useState<IssueType[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalType, setModalType] = useState<'add' | 'edit' | 'view'>('add')
  const [editingIssue, setEditingIssue] = useState<Issue | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  // فلاترة نطاق المبالغ
  const [amountMin, setAmountMin] = useState<string>('')
  const [amountMax, setAmountMax] = useState<string>('')

  // حالات الفلاترة العامة لمنيو الأعمدة
  const [filters, setFilters] = useState({
    case_number: '',
    title: '',
    client_name: '',
    opponent_name: '',
    court_name: ''
  })
  const [sortBy, setSortBy] = useState<keyof Issue>('created_date')
  const [sortDir, setSortDir] = useState<'asc' | 'desc'>('desc')
  const [colMenu, setColMenu] = useState<{open:boolean; key: keyof Issue | null; x:number; y:number}>({open:false, key:null, x:0, y:0})

  const [formData, setFormData] = useState({
    case_number: '',
    title: '',
    description: '',
    client_id: '',
    client_name: '',
    client_phone: '',
    court_id: '',
    court_name: '',
    issue_type: '',
    issue_type_id: '',
    status: 'new',
    case_amount: '',
    currency_id: '1',
    notes: '',
    contract_method: 'بالجلسة',
    contract_date: new Date().toISOString().split('T')[0],
    opponent_name: '',
    opponent_id: ''
  })

  // جلب القضايا
  const fetchIssues = async () => {
    try {
      const response = await fetch('/api/issues')
      const result = await response.json()
      if (result.success) {
        setIssues(result.data || [])
      }
  
    } catch (error) {
      console.error('Error fetching issues:', error)
    }
  }

  // جلب الخصوم
  const fetchOpponents = async () => {
    try {
      const response = await fetch('/api/opponents')
      const result = await response.json()
      if (result.success) {
        setOpponents(result.data || [])
      }
    } catch (error) {
      console.error('Error fetching opponents:', error)
    }
  }

  const fetchCourts = async () => {
    try {
      const response = await fetch('/api/courts')
      const result = await response.json()
      if (result.success) {
        setCourts(result.data || [])
      }
    } catch (error) {
      console.error('Error fetching courts:', error)
    }
  }

  const fetchCurrencies = async () => {
    try {
      const response = await fetch('/api/currencies')
      const result = await response.json()
      if (result.success) {
        setCurrencies(result.data || [])
      }
    } catch (error) {
      console.error('Error fetching currencies:', error)
    }
  }

  const fetchStatuses = async () => {
    try {
      const response = await fetch('/api/issue-statuses')
      const result = await response.json()
      if (result.success) {
        setStatuses(result.data || [])
      }
    } catch (error) {
      console.error('Error fetching statuses:', error)
    }
  }

  const fetchContractMethods = async () => {
    try {
      const response = await fetch('/api/contract-methods')
      const result = await response.json()
      if (result.success) {
        setContractMethods(result.data || [])
      }
    } catch (error) {
      console.error('Error fetching contract methods:', error)
    }
  }

  const fetchIssueTypes = async () => {
    try {
      const response = await fetch('/api/issue-types')
      const result = await response.json()
      if (result.success) {
        setIssueTypes(result.data || [])
      }
    } catch (error) {
      console.error('Error fetching issue types:', error)
    }
  }

  // تصدير النتائج الحالية إلى CSV/Excel (خارج دالة الجلب)
  const exportIssues = (rows: Issue[], type: 'csv'|'excel') => {
    const headers = ['رقم القضية','العنوان','الموكل','الخصم','المحكمة','الحالة','المبلغ','العملة']
    const csvRows = [headers]
    for (const r of rows) {
      const amount = (r as any).case_amount ?? (r as any).amount_yer ?? ''
      const currency = (r as any).currency_symbol ?? ''
      csvRows.push([
        r.case_number || '',
        r.title || '',
        (r as any).client_name || '',
        (r as any).opponent_name || '',
        (r as any).court_name || '',
        (r as any).status || '',
        amount?.toString() || '',
        currency?.toString() || ''
      ])
    }
    const csv = csvRows.map(row => row.map(cell => `"${(cell ?? '').toString().replace(/"/g,'""')}"`).join(',')).join('\n')
    const blob = new Blob([csv], { type: type==='csv' ? 'text/csv;charset=utf-8;' : 'application/vnd.ms-excel' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = type==='csv' ? 'issues.csv' : 'issues.xls'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  // أدوات منيو الفلاترة للأعمدة (خارج أي دالة أخرى)
  const openColMenu = (key: keyof Issue) => (e: React.MouseEvent<HTMLElement>) => {
    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect()
    setColMenu({ open:true, key, x: rect.left + rect.width, y: rect.bottom })
  }
  const getFilterValueForKey = () => {
    if (!colMenu.key) return ''
    switch(colMenu.key){
      case 'case_number': return filters.case_number
      case 'title': return filters.title
      case 'client_name': return filters.client_name
      case 'opponent_name': return filters.opponent_name
      case 'court_name': return filters.court_name
      default: return ''
    }
  }
  const setFilterForKey = (v: string) => {
    if (!colMenu.key) return
    switch(colMenu.key){
      case 'case_number': setFilters(prev=>({...prev, case_number: v})); break
      case 'title': setFilters(prev=>({...prev, title: v})); break
      case 'client_name': setFilters(prev=>({...prev, client_name: v})); break
      case 'opponent_name': setFilters(prev=>({...prev, opponent_name: v})); break
      case 'court_name': setFilters(prev=>({...prev, court_name: v})); break
    }
  }
  const clearFilterForKey = () => {
    if (!colMenu.key) return
    setFilterForKey('')
  }
  const applySortAsc = () => { if (colMenu.key){ setSortBy(colMenu.key as keyof Issue); setSortDir('asc'); setColMenu(prev=>({...prev, open:false})) } }
  const applySortDesc = () => { if (colMenu.key){ setSortBy(colMenu.key as keyof Issue); setSortDir('desc'); setColMenu(prev=>({...prev, open:false})) } }

  // حساب المبلغ بالريال اليمني
  const calculateAmountYer = (amount: string, currencyId: string) => {
    const numAmount = parseFloat(amount) || 0
    const currency = currencies.find(c => c.id.toString() === currencyId)

    if (!currency) return numAmount
    if (currency.is_base_currency) return numAmount

    return numAmount * currency.exchange_rate
  }

  // معالجة تغيير العميل
  const handleClientChange = (clientId: string, clientData?: any) => {
    setFormData({
      ...formData,
      client_id: clientId,
      client_name: clientData?.name || '',
      client_phone: clientData?.phone || ''
    })
  }

  // إضافة قضية جديدة
  const handleAddNew = () => {
    setEditingIssue(null)
    setFormData({
      case_number: '',
      title: '',
      description: '',
      client_id: '',
      client_name: '',
      client_phone: '',
      court_id: '',
      court_name: '',
      issue_type: '',
      issue_type_id: '',
      status: 'new',
      case_amount: '',
      currency_id: '1',
      notes: '',
      contract_method: 'بالجلسة',
      contract_date: new Date().toISOString().split('T')[0],
      opponent_name: '',
      opponent_id: ''
    })
    setModalType('add')
    setIsModalOpen(true)
  }

  // تعديل قضية
  const handleEdit = (issue: Issue) => {
    setEditingIssue(issue)
    setFormData({
      case_number: issue.case_number,
      title: issue.title,
      description: issue.description,
      client_id: issue.client_id.toString(),
      client_name: issue.client_name,
      client_phone: issue.client_phone,
      court_id: issue.court_id?.toString() || '',
      court_name: issue.court_name || '',
      issue_type: issue.issue_type || '',
      issue_type_id: issue.issue_type_id?.toString() || '',
      status: issue.status,
      case_amount: issue.case_amount?.toString() || '',
      currency_id: issue.currency_id?.toString() || '1',
      notes: issue.notes || '',
      contract_method: issue.contract_method || 'بالجلسة',
      contract_date: issue.contract_date || new Date().toISOString().split('T')[0],
      opponent_name: issue.opponent_name || '',
      opponent_id: (issue.opponent_id?.toString() || '')
    })
    setModalType('edit')
    setIsModalOpen(true)
  }

  // عرض قضية
  const handleView = (issue: Issue) => {
    setEditingIssue(issue)
    setFormData({
      case_number: issue.case_number,
      title: issue.title,
      description: issue.description,
      client_id: issue.client_id.toString(),
      client_name: issue.client_name,
      client_phone: issue.client_phone,
      court_id: issue.court_id?.toString() || '',
      court_name: issue.court_name || '',
      issue_type: issue.issue_type || '',
      issue_type_id: issue.issue_type_id?.toString() || '',
      status: issue.status,
      case_amount: issue.case_amount?.toString() || '',
      currency_id: issue.currency_id?.toString() || '1',
      notes: issue.notes || '',
      contract_method: issue.contract_method || 'بالجلسة',
      contract_date: issue.contract_date || new Date().toISOString().split('T')[0],
      opponent_name: issue.opponent_name || '',
      opponent_id: (issue.opponent_id?.toString() || '')
    })
    setModalType('view')
    setIsModalOpen(true)
  }

  // حفظ القضية
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      if (modalType === 'add') {
        const response = await fetch('/api/issues', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ ...formData, opponent_id: formData.opponent_id ? Number(formData.opponent_id) : null, created_by: 1 })
        })

        const result = await response.json()
        if (result.success) {
          alert('تم إضافة القضية بنجاح')
          fetchIssues()
        } else {
          alert(result.error || 'فشل في إضافة القضية')
          return
        }
      } else if (modalType === 'edit' && editingIssue) {
        const response = await fetch(`/api/issues/${editingIssue.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ ...formData, opponent_id: formData.opponent_id ? Number(formData.opponent_id) : null })
        })

        const result = await response.json()
        if (result.success) {
          alert('تم تحديث القضية بنجاح')
          fetchIssues()
        } else {
          alert(result.error || 'فشل في تحديث القضية')
          return
        }
      }

      setIsModalOpen(false)
      setEditingIssue(null)
    } catch (error) {
      console.error('Error submitting form:', error)
      alert('حدث خطأ في الاتصال')
    }
  }

  // حذف قضية
  const handleDelete = async (id: number) => {
    if (confirm('هل أنت متأكد من حذف هذه القضية؟')) {
      try {
        const response = await fetch(`/api/issues/${id}`, {
          method: 'DELETE'
        })

        const result = await response.json()
        if (result.success) {
          alert('تم حذف القضية بنجاح')
          fetchIssues()
        } else {
          alert(result.error || 'فشل في حذف القضية')
        }
      } catch (error) {
        console.error('Error deleting issue:', error)
        alert('حدث خطأ في الاتصال')
      }
    }
  }

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true)
      try {
        await Promise.all([
          fetchIssues(),
          fetchCourts(),
          fetchCurrencies(),
          fetchStatuses(),
          fetchContractMethods(),
          fetchIssueTypes(),
          fetchOpponents()
        ])
      } catch (error) {
        console.error('Error loading data:', error)
      } finally {
        setIsLoading(false)
      }
    }
    loadData()
  }, [])

  // تصفية وترتيب القضايا (حسب الفلاتر ومن ثم الفرز)
  const filteredIssues = issues
    .filter(issue => {
      // البحث العام الموجود مسبقاً
      const matchesSearch =
        issue.case_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
        issue.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        issue.client_name.toLowerCase().includes(searchTerm.toLowerCase())

      const matchesStatus = statusFilter === 'all' || issue.status === statusFilter

      // فلاتر الأعمدة
      const f1 = filters.case_number ? issue.case_number.toLowerCase().includes(filters.case_number.toLowerCase()) : true
      const f2 = filters.title ? issue.title.toLowerCase().includes(filters.title.toLowerCase()) : true
      const f3 = filters.client_name ? (issue.client_name||'').toLowerCase().includes(filters.client_name.toLowerCase()) : true
      const f4 = filters.opponent_name ? (issue.opponent_name||'').toLowerCase().includes(filters.opponent_name.toLowerCase()) : true
      const f5 = filters.court_name ? (issue.court_name||'').toLowerCase().includes(filters.court_name.toLowerCase()) : true

      // فلترة نطاق المبالغ
      const amountVal = (issue as any).case_amount ?? (issue as any).amount_yer ?? 0
      const passMin = amountMin ? Number(amountVal) >= Number(amountMin) : true
      const passMax = amountMax ? Number(amountVal) <= Number(amountMax) : true

      return matchesSearch && matchesStatus && f1 && f2 && f3 && f4 && f5 && passMin && passMax
    })
    .sort((a, b) => {
      const av = (a[sortBy] ?? '').toString().toLowerCase()
      const bv = (b[sortBy] ?? '').toString().toLowerCase()
      if (av < bv) return sortDir === 'asc' ? -1 : 1
      if (av > bv) return sortDir === 'asc' ? 1 : -1
      return 0
    })

  // إحصائيات
  const stats = {
    total: issues.length,
    new: issues.filter(i => i.status === 'new').length,
    pending: issues.filter(i => i.status === 'pending').length,
    in_progress: issues.filter(i => i.status === 'in_progress').length,
    completed: issues.filter(i => i.status === 'completed').length
  }

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-emerald-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">جاري تحميل البيانات...</p>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6 p-6">
        {/* العنوان والزر */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Scale className="h-8 w-8 mr-3 text-emerald-600" />
              إدارة القضايا
            </h1>
            <p className="text-gray-700 mt-1">إدارة ومتابعة جميع القضايا القانونية</p>
          </div>
          <Button onClick={handleAddNew} className="bg-emerald-600 hover:bg-emerald-700 text-emerald-600">
            <Plus className="h-4 w-4 mr-2" />
            إضافة قضية جديدة
          </Button>
        </div>

        {/* الإحصائيات */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-5">
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
                <div className="text-sm text-gray-600">إجمالي القضايا</div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{stats.new}</div>
                <div className="text-sm text-gray-600">جديدة</div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
                <div className="text-sm text-gray-600">معلقة</div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{stats.in_progress}</div>
                <div className="text-sm text-gray-600">قيد المعالجة</div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-emerald-600">{stats.completed}</div>
                <div className="text-sm text-gray-600">مكتملة</div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* البحث والفلترة وزر مسح الفلاتر */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
              <div className="relative flex-1">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-700 h-4 w-4" />
                <Input
                  placeholder="البحث برقم القضية، العنوان، أو اسم الموكل..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10 text-black"
                />
              </div>
              <div className="flex gap-2 flex-wrap items-center">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-black"
                >
                  <option value="all">جميع الحالات</option>
                  {statuses.map(status => (
                    <option key={status.value} value={status.value}>
                      {status.label}
                    </option>
                  ))}
                </select>
                {/* نطاق المبالغ */}
                <div className="flex items-center gap-2">
                  <Input
                    type="number"
                    placeholder="مبلغ من"
                    value={amountMin}
                    onChange={(e)=> setAmountMin(e.target.value)}
                    className="w-28 text-black"
                  />
                  <span className="text-gray-500">-</span>
                  <Input
                    type="number"
                    placeholder="مبلغ إلى"
                    value={amountMax}
                    onChange={(e)=> setAmountMax(e.target.value)}
                    className="w-28 text-black"
                  />
                </div>
                <Button
                  variant="outline"
                  className="border-gray-500 text-gray-700 hover:bg-gray-50"
                  onClick={() => { setFilters({ case_number:'', title:'', client_name:'', opponent_name:'', court_name:'' }); setSortBy('created_date'); setSortDir('desc'); setColMenu({open:false, key:null, x:0, y:0}) }}
                >
                  مسح الفلاتر
                </Button>
                {/* أزرار التصدير */}
                <Button variant="outline" className="border-blue-600 text-blue-600 hover:bg-blue-50" onClick={()=> exportIssues(filteredIssues, 'csv')}>
                  تصدير CSV
                </Button>
                <Button variant="outline" className="border-green-600 text-green-600 hover:bg-green-50" onClick={()=> exportIssues(filteredIssues, 'excel')}>
                  تصدير Excel
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* جدول القضايا */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة القضايا ({filteredIssues.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {filteredIssues.length === 0 ? (
              <div className="text-center py-8">
                <Scale className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">لا توجد قضايا</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-right p-3 font-semibold cursor-pointer" onMouseDown={openColMenu('case_number')}>رقم القضية {sortBy==='case_number' ? (sortDir==='asc' ? '▲' : '▼') : ''}</th>
                      <th className="text-right p-3 font-semibold cursor-pointer" onMouseDown={openColMenu('title')}>العنوان {sortBy==='title' ? (sortDir==='asc' ? '▲' : '▼') : ''}</th>
                      <th className="text-right p-3 font-semibold cursor-pointer" onMouseDown={openColMenu('client_name')}>الموكل {sortBy==='client_name' ? (sortDir==='asc' ? '▲' : '▼') : ''}</th>
                      <th className="text-center p-3 font-semibold cursor-pointer" onMouseDown={openColMenu('opponent_name')}>الخصم {sortBy==='opponent_name' ? (sortDir==='asc' ? '▲' : '▼') : ''}</th>
                      <th className="text-center p-3 font-semibold cursor-pointer" onMouseDown={openColMenu('court_name')}>المحكمة {sortBy==='court_name' ? (sortDir==='asc' ? '▲' : '▼') : ''}</th>
                      <th className="text-center p-3 font-semibold">الحالة</th>
                      <th className="text-center p-3 font-semibold">المبلغ</th>
                      <th className="text-center p-3 font-semibold">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredIssues.map((issue) => (
                      <tr key={issue.id} className="border-b hover:bg-gray-50">
                        <td className="p-3 font-medium text-blue-600">{issue.case_number}</td>
                        <td className="p-3">{issue.title}</td>
                        <td className="p-3">
                          <div>
                            <div className="font-medium">{issue.client_name}</div>
                            <div className="text-sm text-gray-500">{issue.client_phone}</div>
                          </div>
                        </td>
                        <td className="p-3 text-center">{issue.opponent_name || '-'}</td>
                        <td className="p-3 text-center">{issue.court_name || '-'}</td>
                        <td className="p-3 text-center">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            issue.status === 'new' ? 'bg-green-100 text-green-800' :
                            issue.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            issue.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                            issue.status === 'completed' ? 'bg-emerald-100 text-emerald-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {statuses.find(s => s.value === issue.status)?.label || issue.status}
                          </span>
                        </td>
                        <td className="p-3 text-center">
                          <div className="text-sm">
                            <div>{(issue.case_amount || 0).toLocaleString()} {issue.currency_symbol}</div>
                            <div className="text-gray-500">{(issue.amount_yer || 0).toLocaleString()} ر.ي</div>
                          </div>
                        </td>
                        <td className="p-3">
                          <div className="flex justify-center space-x-2 space-x-reverse">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleView(issue)}
                              className="text-blue-600 hover:text-blue-800"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEdit(issue)}
                              className="text-green-600 hover:text-green-800"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDelete(issue.id)}
                              className="text-red-600 hover:text-red-800"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* النافذة المنبثقة */}
        {isModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
              {/* رأس النافذة */}
              <div className="flex items-center justify-between p-4 border-b bg-gray-50">
                <h2 className="text-xl font-bold text-gray-900">
                  {modalType === 'add' && '📋 إضافة قضية جديدة'}
                  {modalType === 'edit' && '✏️ تعديل القضية'}
                  {modalType === 'view' && '👁️ عرض تفاصيل القضية'}
                </h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsModalOpen(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              {/* محتوى النافذة */}
              <form onSubmit={handleSubmit} className="p-6 space-y-4">
                {/* الصف الأول: رقم القضية وعنوان القضية */}
                <div className="grid grid-cols-10 gap-3">
                  <div className="col-span-3">
                    <Label htmlFor="case_number" className="text-sm font-semibold text-blue-700 bg-blue-50 px-3 py-1 rounded-md inline-block mb-2">
                      📋 رقم القضية *
                    </Label>
                    <Input
                      id="case_number"
                      value={formData.case_number}
                      onChange={(e) => setFormData({...formData, case_number: e.target.value})}
                      required={modalType !== 'view'}
                      readOnly={modalType === 'view'}
                      className="h-10 text-black bg-blue-50 border-blue-300 focus:border-blue-500 focus:bg-white transition-colors"
                      placeholder="أدخل رقم القضية..."
                    />
                  </div>
                  <div className="col-span-7">
                    <Label htmlFor="title" className="text-sm font-semibold text-green-700 bg-green-50 px-3 py-1 rounded-md inline-block mb-2">
                      📝 عنوان القضية *
                    </Label>
                    <Input
                      id="title"
                      value={formData.title}
                      onChange={(e) => setFormData({...formData, title: e.target.value})}
                      required={modalType !== 'view'}
                      readOnly={modalType === 'view'}
                      className="h-10 text-black bg-green-50 border-green-300 focus:border-green-500 focus:bg-white transition-colors"
                      placeholder="أدخل عنوان القضية..."
                    />
                  </div>
                </div>

                {/* الصف الثاني: وصف القضية */}
                <div className="grid grid-cols-1 gap-3">
                  <div>
                    <Label htmlFor="description" className="text-sm font-semibold text-gray-700 bg-gray-50 px-3 py-1 rounded-md inline-block mb-2">
                      📄 وصف القضية
                    </Label>
                    <textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData({...formData, description: e.target.value})}
                      readOnly={modalType === 'view'}
                      className="w-full h-12 px-3 py-2 border rounded-md resize-none text-sm text-black bg-gray-50 border-gray-300 focus:border-gray-500 focus:bg-white transition-colors"
                      placeholder="أدخل وصف مختصر للقضية..."
                    />
                  </div>
                </div>

                {/* الصف الثالث: الموكل والمحكمة واسم الخصم */}
                <div className="grid grid-cols-2 lg:grid-cols-3 gap-3">
                  <div>
                    <Label className="text-sm font-semibold text-purple-700 bg-purple-50 px-3 py-1 rounded-md inline-block mb-2">
                      👤 الموكل *
                    </Label>
                    <div className="bg-purple-50 border-purple-300 focus-within:border-purple-500 focus-within:bg-white transition-colors rounded-md">
                      <ClientSelect
                        key="client-select-modal"
                        value={formData.client_id}
                        onChange={handleClientChange}
                        label=""
                        placeholder="اختر الموكل..."
                        required
                        disabled={modalType === 'view'}
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="court_id" className="text-sm font-semibold text-orange-700 bg-orange-50 px-3 py-1 rounded-md inline-block mb-2">
                      🏛️ المحكمة
                    </Label>
                    <select
                      id="court_id"
                      value={formData.court_id || ""}
                      onChange={(e) => {
                        const selectedCourt = courts.find(c => c.id.toString() === e.target.value)
                        setFormData({
                          ...formData,
                          court_id: e.target.value,
                          court_name: selectedCourt?.name || ''
                        })
                      }}
                      className="w-full h-10 px-3 py-2 bg-orange-50 border border-orange-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 focus:bg-white transition-colors text-black"
                      disabled={modalType === 'view'}
                    >
                      <option value="">اختر المحكمة...</option>
                      {courts.length === 0 ? (
                        <option value="" disabled>لا توجد محاكم متاحة</option>
                      ) : (
                        courts.map((court) => (
                          <option key={court.id} value={court.id}>
                            {court.name} - {court.governorate_name}
                          </option>
                        ))
                      )}
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="opponent_id" className="text-sm font-semibold text-rose-700 bg-rose-50 px-3 py-1 rounded-md inline-block mb-2">
                      ⚔️ اسم الخصم
                    </Label>
                    <select
                      id="opponent_id"
                      value={formData.opponent_id || ''}
                      onChange={(e) => {
                        const sel = opponents.find(o => o.id.toString() === e.target.value)
                        setFormData({
                          ...formData,
                          opponent_id: e.target.value,
                          opponent_name: sel?.name || ''
                        })
                      }}
                      className="w-full h-10 px-3 py-2 bg-rose-50 border border-rose-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rose-500 focus:border-rose-500 focus:bg-white transition-colors text-black"
                      disabled={modalType === 'view'}
                    >
                      <option value="">اختر الخصم...</option>
                      {opponents.length === 0 ? (
                        <option value="" disabled>لا توجد أسماء خصوم</option>
                      ) : (
                        opponents.map(op => (
                          <option key={op.id} value={op.id}>{op.name}</option>
                        ))
                      )}
                    </select>
                  </div>
                </div>

                {/* الصف الرابع: الحالة ونوع القضية وطريقة التعاقد وتاريخ التعاقد */}
                <div className="grid grid-cols-4 gap-3">
                  <div>
                    <Label htmlFor="status" className="text-sm font-semibold text-red-700 bg-red-50 px-3 py-1 rounded-md inline-block mb-2">
                      📊 حالة القضية
                    </Label>
                    <select
                      id="status"
                      value={formData.status}
                      onChange={(e) => setFormData({...formData, status: e.target.value})}
                      className="w-full h-10 px-3 py-2 bg-red-50 border border-red-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 focus:bg-white transition-colors text-sm text-black"
                      disabled={modalType === 'view'}
                    >
                      {statuses.map(status => (
                        <option key={status.value} value={status.value}>
                          {status.label}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="issue_type" className="text-sm font-semibold text-indigo-700 bg-indigo-50 px-3 py-1 rounded-md inline-block mb-2">
                      ⚖️ نوع القضية
                    </Label>
                    <select
                      id="issue_type"
                      value={formData.issue_type_id || ""}
                      onChange={(e) => {
                        const selectedType = issueTypes.find(t => t.id.toString() === e.target.value)
                        setFormData({
                          ...formData,
                          issue_type_id: e.target.value,
                          issue_type: selectedType?.name || ''
                        })
                      }}
                      className="w-full h-10 px-3 py-2 bg-indigo-50 border border-indigo-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 focus:bg-white transition-colors text-sm text-black"
                      disabled={modalType === 'view'}
                    >
                      <option value="">اختر نوع القضية...</option>
                      {issueTypes.map(type => (
                        <option key={type.id} value={type.id}>
                          {type.name}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="contract_method" className="text-sm font-semibold text-teal-700 bg-teal-50 px-3 py-1 rounded-md inline-block mb-2">
                      📝 طريقة التعاقد
                    </Label>
                    <select
                      id="contract_method"
                      value={formData.contract_method}
                      onChange={(e) => setFormData({...formData, contract_method: e.target.value})}
                      className="w-full h-10 px-3 py-2 bg-teal-50 border border-teal-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 focus:bg-white transition-colors text-sm text-black"
                      disabled={modalType === 'view'}
                    >
                      {contractMethods.map(method => (
                        <option key={method.value} value={method.value}>
                          {method.label}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="contract_date" className="text-sm font-semibold text-pink-700 bg-pink-50 px-3 py-1 rounded-md inline-block mb-2">
                      📅 تاريخ التعاقد
                    </Label>
                    <Input
                      id="contract_date"
                      type="date"
                      value={formData.contract_date}
                      onChange={(e) => setFormData({...formData, contract_date: e.target.value})}
                      readOnly={modalType === 'view'}
                      className="h-10 text-black bg-pink-50 border-pink-300 focus:border-pink-500 focus:bg-white transition-colors text-sm"
                    />
                  </div>
                </div>

                {/* الصف الخامس: العملة والمبالغ */}
                <div className="grid grid-cols-6 gap-3">
                  {/* العملة - مصغرة إلى 50% */}
                  <div className="col-span-1">
                    <Label htmlFor="currency_id" className="text-sm font-semibold text-yellow-700 bg-yellow-50 px-3 py-1 rounded-md inline-block mb-2">
                      💱 العملة
                    </Label>
                    <select
                      id="currency_id"
                      value={formData.currency_id || '1'}
                      onChange={(e) => setFormData({...formData, currency_id: e.target.value})}
                      className="w-full h-10 px-2 py-2 bg-yellow-50 border border-yellow-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 focus:bg-white transition-colors text-xs text-black"
                      disabled={modalType === 'view'}
                    >
                      {currencies.map(currency => (
                        <option key={currency.id} value={currency.id}>
                          {currency.symbol}
                          {currency.is_base_currency && ' - أساسية'}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* قيمة القضية والريال اليمني - تظهر فقط للتعاقد بالعقد */}
                  {formData.contract_method === 'بالعقد' && (
                    <>
                      {/* قيمة القضية - مصغرة إلى 50% */}
                      <div className="col-span-2">
                        <Label htmlFor="case_amount" className="text-sm font-semibold text-emerald-700 bg-emerald-50 px-3 py-1 rounded-md inline-block mb-2">
                          💰 قيمة القضية
                        </Label>
                        <div className="relative">
                          <Input
                            id="case_amount"
                            type="number"
                            step="0.01"
                            min="0"
                            value={formData.case_amount || ''}
                            onChange={(e) => setFormData({...formData, case_amount: e.target.value})}
                            readOnly={modalType === 'view'}
                            className="h-10 pr-10 text-black bg-emerald-50 border-emerald-300 focus:border-emerald-500 focus:bg-white transition-colors text-xs"
                            placeholder="0.00"
                          />
                          <div className="absolute inset-y-0 right-0 pr-2 flex items-center pointer-events-none">
                            <span className="text-gray-500 text-xs">
                              {currencies.find(c => c.id.toString() === (formData.currency_id || '1'))?.symbol || 'ر.ي'}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* بالريال اليمني - مصغرة إلى 50% */}
                      <div className="col-span-2">
                        <Label htmlFor="amount_yer" className="text-sm font-semibold text-green-700 bg-green-50 px-3 py-1 rounded-md inline-block mb-2">
                          💵 بالريال اليمني
                        </Label>
                        <div className="relative">
                          <Input
                            id="amount_yer"
                            type="text"
                            value={Math.round(calculateAmountYer(formData.case_amount || '0', formData.currency_id || '1')).toLocaleString('en-US')}
                            readOnly
                            className="h-10 pr-8 bg-gray-50 text-black text-xs"
                          />
                          <div className="absolute inset-y-0 right-0 pr-2 flex items-center pointer-events-none">
                            <span className="text-gray-500 text-xs">ر.ي</span>
                          </div>
                        </div>
                      </div>
                    </>
                  )}

                  {/* رسالة توضيحية للتعاقد بالجلسة */}
                  {formData.contract_method === 'بالجلسة' && (
                    <div className="col-span-5">
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mt-6">
                        <div className="flex items-center">
                          <div className="text-blue-600 text-lg mr-2">⚖️</div>
                          <div>
                            <h4 className="text-sm font-semibold text-blue-800">تعاقد بالجلسة</h4>
                            <p className="text-xs text-blue-600 mt-1">
                              لا يتم تحديد قيمة ثابتة للقضية في التعاقد بالجلسة، حيث يتم الدفع حسب عدد الجلسات المحضورة.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* مساحة فارغة للتوازن */}
                  <div className="col-span-1"></div>
                </div>

                {/* الصف السادس: الملاحظات */}
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label htmlFor="notes" className="text-sm font-semibold text-gray-700 bg-gray-50 px-3 py-1 rounded-md inline-block mb-2">
                      📝 ملاحظات إضافية
                    </Label>
                    <textarea
                      id="notes"
                      value={formData.notes}
                      onChange={(e) => setFormData({...formData, notes: e.target.value})}
                      readOnly={modalType === 'view'}
                      className="w-full h-12 px-3 py-2 border rounded-md resize-none text-sm text-black bg-gray-50 border-gray-300 focus:border-gray-500 focus:bg-white transition-colors"
                      placeholder="أدخل أي ملاحظات إضافية..."
                    />
                  </div>
                  <div></div>
                </div>

                {/* أزرار الإجراءات */}
                <div className="flex space-x-4 space-x-reverse pt-6 border-t border-gray-200">
                  {modalType !== 'view' && (
                    <Button type="submit" className="flex-1 bg-green-600 hover:bg-green-700 text-white h-12 text-lg font-semibold">
                      <Save className="h-5 w-5 mr-2" />
                      {modalType === 'add' ? '💾 إضافة القضية' : '💾 حفظ التغييرات'}
                    </Button>
                  )}
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsModalOpen(false)}
                    className={`${modalType === 'view' ? 'w-full' : 'flex-1'} h-12 text-lg font-semibold border-gray-300 hover:bg-gray-50`}
                  >
                    {modalType === 'view' ? '❌ إغلاق' : '🚫 إلغاء'}
                  </Button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}
