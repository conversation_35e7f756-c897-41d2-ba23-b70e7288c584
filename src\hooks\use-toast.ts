'use client'

// دالة toast مبسطة للاستخدام السريع
export const toast = (props: {
  title?: string
  description?: string
  variant?: 'default' | 'destructive'
  duration?: number
}) => {
  // عرض الرسالة في console للتطوير
  const message = `${props.title || ''} ${props.description || ''}`.trim()
  const emoji = props.variant === 'destructive' ? '❌' : '✅'

  console.log(`${emoji} ${message}`)

  // يمكن إضافة منطق عرض Toast هنا لاحقاً
  return {
    id: Date.now().toString(),
    dismiss: () => {},
  }
}

// Hook بسيط للتوافق
export function useToast() {
  return {
    toast,
    toasts: [],
    dismiss: () => {},
  }
}
