/**
 * API لإرسال رسائل جماعية عبر WhatsApp
 */

import { NextRequest, NextResponse } from 'next/server'
import { unifiedWhatsAppService } from '@/lib/unified-whatsapp-service'
import { query } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { contactIds, message, includeVoucher = false, voucherPath } = body

    if (!contactIds || !Array.isArray(contactIds) || contactIds.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'قائمة جهات الاتصال مطلوبة'
      }, { status: 400 })
    }

    if (!message) {
      return NextResponse.json({
        success: false,
        error: 'نص الرسالة مطلوب'
      }, { status: 400 })
    }

    // جلب بيانات جهات الاتصال
    const contactsQuery = `
      SELECT
        c.id,
        c.name,
        c.phone,
        'client' as type
      FROM clients c
      WHERE c.id = ANY($1) AND c.phone IS NOT NULL AND c.phone != ''

      UNION ALL

      SELECT
        e.id,
        e.name,
        e.phone,
        'employee' as type
      FROM employees e
      WHERE e.id = ANY($1) AND e.phone IS NOT NULL AND e.phone != ''
    `

    const contactsResult = await query(contactsQuery, [contactIds])
    const contacts = contactsResult.rows

    if (contacts.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'لم يتم العثور على جهات اتصال صالحة'
      }, { status: 400 })
    }

    const results = []
    let successCount = 0
    let failureCount = 0

    // إرسال الرسائل
    for (const contact of contacts) {
      try {
        let result

        if (includeVoucher && voucherPath) {
          // إرسال صورة السند
          result = await unifiedWhatsAppService.sendVoucherImage(
            contact.phone,
            voucherPath,
            message,
            contact.name
          )
        } else {
          // إرسال رسالة نصية
          result = await unifiedWhatsAppService.sendTextMessage(
            contact.phone,
            message,
            contact.name
          )
        }

        if (result.success) {
          successCount++
        } else {
          failureCount++
        }

        results.push({
          contactId: contact.id,
          contactName: contact.name,
          contactPhone: contact.phone,
          success: result.success,
          messageId: result.messageId,
          error: result.error
        })

        // تأخير بسيط بين الرسائل لتجنب الحظر
        await new Promise(resolve => setTimeout(resolve, 2000))

      } catch (error) {
        failureCount++
        results.push({
          contactId: contact.id,
          contactName: contact.name,
          contactPhone: contact.phone,
          success: false,
          error: error instanceof Error ? error.message : 'خطأ غير معروف'
        })
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        totalSent: contacts.length,
        successCount,
        failureCount,
        results
      }
    })

  } catch (error) {
    console.error('خطأ في API الإرسال الجماعي:', error)
    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم'
    }, { status: 500 })
  }
}
