import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب جميع القيود اليومية
export async function GET() {
  try {
    // جلب القيود من جدول vouchers
    const result = await query(`
      SELECT 
        v.*,
        u.username as created_by_username
      FROM vouchers v
      LEFT JOIN users u ON v.user_id = u.id
      WHERE v.voucher_type = 'journal'
      ORDER BY v.created_at DESC
    `)

    // جلب تفاصيل القيود لكل سند
    const vouchersWithEntries = await Promise.all(
      result.rows.map(async (voucher) => {
        const entries = await query(`
          SELECT 
            ve.*,
            da.account_name as debit_account_name,
            da.account_code as debit_account_code,
            ca.account_name as credit_account_name,
            ca.account_code as credit_account_code
          FROM voucher_entries ve
          LEFT JOIN chart_of_accounts da ON ve.debit_account_id = da.id
          LEFT JOIN chart_of_accounts ca ON ve.credit_account_id = ca.id
          WHERE ve.voucher_id = $1
          ORDER BY ve.line_number
        `, [voucher.id])

        return {
          id: voucher.id,
          voucher_number: voucher.voucher_number,
          voucher_date: voucher.voucher_date,
          description: voucher.description,
          total_amount: voucher.amount,
          reference: voucher.payee_name || '',
          status: voucher.status,
          created_by: voucher.created_by_username || 'غير محدد',
          created_at: voucher.created_at,
          entries: entries.rows
        }
      })
    )

    return NextResponse.json({
      success: true,
      data: vouchersWithEntries
    })

  } catch (error) {
    console.error('Error fetching journal entries:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في جلب القيود اليومية' },
      { status: 500 }
    )
  }
}

// POST - إضافة قيد يومي جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      voucher_number,
      voucher_date,
      description,
      reference,
      status = 'pending',
      entries,
      user_id
    } = body

    // التحقق من البيانات المطلوبة
    if (!voucher_date || !description || !entries || entries.length === 0) {
      return NextResponse.json(
        { success: false, error: 'البيانات المطلوبة مفقودة' },
        { status: 400 }
      )
    }

    // حساب المبلغ الإجمالي
    const total_amount = entries.reduce((sum: number, entry: any) => sum + (entry.amount || 0), 0)

    // التحقق من توازن القيود
    const totalDebit = entries.reduce((sum: number, entry: any) => sum + (entry.amount || 0), 0)
    const totalCredit = totalDebit // في النظام المبسط

    if (totalDebit !== totalCredit) {
      return NextResponse.json(
        { success: false, error: 'القيود غير متوازنة - مجموع المدين يجب أن يساوي مجموع الدائن' },
        { status: 400 }
      )
    }

    // توليد رقم القيد إذا لم يكن موجوداً
    let finalVoucherNumber = voucher_number
    if (!finalVoucherNumber) {
      const lastVoucher = await query(`
        SELECT voucher_number 
        FROM vouchers 
        WHERE voucher_type = 'journal' 
        ORDER BY id DESC 
        LIMIT 1
      `)
      
      const lastNumber = lastVoucher.rows.length > 0 
        ? parseInt(lastVoucher.rows[0].voucher_number.split('-')[1]) || 0
        : 0
      
      finalVoucherNumber = `JE-${String(lastNumber + 1).padStart(4, '0')}`
    }

    // إدراج السند الرئيسي
    const voucherResult = await query(`
      INSERT INTO vouchers (
        voucher_number, voucher_type, voucher_date, amount, description,
        payee_name, account_id, status, user_id, created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, CURRENT_TIMESTAMP)
      RETURNING id
    `, [
      finalVoucherNumber,
      'journal',
      voucher_date,
      total_amount,
      description,
      reference || '',
      entries[0]?.credit_account_id || 1, // الحساب الرئيسي
      status,
      user_id
    ])

    const voucherId = voucherResult.rows[0].id

    // إدراج قيود السند
    for (let i = 0; i < entries.length; i++) {
      const entry = entries[i]
      
      // قيد مدين
      await query(`
        INSERT INTO voucher_entries (
          voucher_id, account_id, debit_amount, credit_amount,
          description, line_number, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)
      `, [
        voucherId,
        entry.debit_account_id,
        entry.amount,
        0,
        entry.description || description,
        (i * 2) + 1
      ])

      // قيد دائن
      await query(`
        INSERT INTO voucher_entries (
          voucher_id, account_id, debit_amount, credit_amount,
          description, line_number, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)
      `, [
        voucherId,
        entry.credit_account_id,
        0,
        entry.amount,
        entry.description || description,
        (i * 2) + 2
      ])
    }

    return NextResponse.json({
      success: true,
      message: 'تم إضافة القيد اليومي بنجاح',
      data: { id: voucherId, voucher_number: finalVoucherNumber }
    })

  } catch (error) {
    console.error('Error creating journal entry:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في إضافة القيد اليومي' },
      { status: 500 }
    )
  }
}
