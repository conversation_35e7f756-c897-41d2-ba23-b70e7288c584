const { Client } = require('pg');

async function testPostgreSQLConnection() {
  console.log('🔍 اختبار اتصال PostgreSQL...\n');

  // إعدادات مختلفة للاختبار
  const configs = [
    // الإعدادات الحالية
    { host: 'localhost', port: 5432, user: 'postgres', password: 'yemen123', database: 'postgres' },
    { host: 'localhost', port: 5432, user: 'postgres', password: 'postgres', database: 'postgres' },
    { host: 'localhost', port: 5432, user: 'postgres', password: '', database: 'postgres' },
    { host: 'localhost', port: 5432, user: 'postgres', password: 'admin', database: 'postgres' },
    { host: 'localhost', port: 5432, user: 'postgres', password: '123456', database: 'postgres' },
    { host: 'localhost', port: 5432, user: 'postgres', password: 'root', database: 'postgres' },
    
    // منافذ أخرى محتملة
    { host: 'localhost', port: 5433, user: 'postgres', password: 'yemen123', database: 'postgres' },
    { host: 'localhost', port: 5434, user: 'postgres', password: 'yemen123', database: 'postgres' },
    
    // عناوين IP مختلفة
    { host: '127.0.0.1', port: 5432, user: 'postgres', password: 'yemen123', database: 'postgres' },
    
    // مستخدمين آخرين محتملين
    { host: 'localhost', port: 5432, user: 'admin', password: 'yemen123', database: 'postgres' },
    { host: 'localhost', port: 5432, user: 'root', password: 'yemen123', database: 'postgres' },
  ];

  let successfulConnection = null;

  for (let i = 0; i < configs.length; i++) {
    const config = configs[i];
    console.log(`${i + 1}. اختبار: ${config.user}@${config.host}:${config.port} (كلمة المرور: ${config.password || 'فارغة'})`);
    
    const client = new Client({
      ...config,
      connectTimeoutMillis: 5000,
      connectionTimeoutMillis: 5000
    });

    try {
      await client.connect();
      console.log('   ✅ نجح الاتصال!');
      
      // جلب معلومات الخادم
      try {
        const versionResult = await client.query('SELECT version()');
        const version = versionResult.rows[0].version;
        console.log(`   📋 إصدار PostgreSQL: ${version.split(' ')[1]}`);
      } catch (e) {
        console.log('   📋 متصل ولكن لا يمكن جلب معلومات الإصدار');
      }

      // جلب قائمة قواعد البيانات
      try {
        const dbResult = await client.query(`
          SELECT datname, pg_size_pretty(pg_database_size(datname)) as size
          FROM pg_database 
          WHERE datistemplate = false 
          ORDER BY datname
        `);
        
        console.log(`   📊 قواعد البيانات المتاحة (${dbResult.rows.length}):`);
        dbResult.rows.forEach(row => {
          console.log(`      - ${row.datname} (${row.size})`);
        });

        // البحث عن قواعد البيانات المطلوبة
        const targetDatabases = ['mohammi', 'mohammidev'];
        const foundDatabases = [];
        
        for (const targetDb of targetDatabases) {
          const found = dbResult.rows.find(row => row.datname === targetDb);
          if (found) {
            foundDatabases.push(targetDb);
            console.log(`   🎯 تم العثور على قاعدة البيانات: ${targetDb}`);
            
            // فحص الجداول في قاعدة البيانات
            const dbClient = new Client({
              ...config,
              database: targetDb,
              connectTimeoutMillis: 5000
            });

            try {
              await dbClient.connect();
              
              const tablesResult = await dbClient.query(`
                SELECT table_name, 
                       (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) as column_count
                FROM information_schema.tables t
                WHERE table_schema = 'public' 
                ORDER BY table_name
              `);

              console.log(`      📋 الجداول في ${targetDb} (${tablesResult.rows.length}):`);
              tablesResult.rows.slice(0, 10).forEach(table => {
                console.log(`         - ${table.table_name} (${table.column_count} عمود)`);
              });
              
              if (tablesResult.rows.length > 10) {
                console.log(`         ... و ${tablesResult.rows.length - 10} جدول آخر`);
              }

              // فحص الجداول المهمة
              const importantTables = ['issues', 'case_movements', 'clients', 'employees', 'users', 'documents'];
              for (const tableName of importantTables) {
                const tableExists = tablesResult.rows.find(t => t.table_name === tableName);
                if (tableExists) {
                  try {
                    const countResult = await dbClient.query(`SELECT COUNT(*) FROM ${tableName}`);
                    console.log(`      🎯 ${tableName}: ${countResult.rows[0].count} سجل`);
                  } catch (e) {
                    console.log(`      ⚠️ ${tableName}: موجود ولكن لا يمكن عد السجلات`);
                  }
                }
              }
              
              await dbClient.end();
            } catch (dbError) {
              console.log(`      ❌ خطأ في الوصول لقاعدة البيانات ${targetDb}: ${dbError.message}`);
            }
          }
        }
        
        if (foundDatabases.length === 0) {
          console.log('   ⚠️ لم يتم العثور على قواعد البيانات mohammi أو mohammidev');
        }

        successfulConnection = {
          ...config,
          availableDatabases: dbResult.rows.map(row => row.datname),
          targetDatabases: foundDatabases
        };
        
      } catch (e) {
        console.log('   ⚠️ لا يمكن جلب قائمة قواعد البيانات:', e.message);
        successfulConnection = config;
      }

      await client.end();
      break; // توقف عند أول اتصال ناجح
      
    } catch (error) {
      console.log(`   ❌ فشل: ${error.message}`);
      
      // تحليل نوع الخطأ
      if (error.message.includes('ECONNREFUSED')) {
        console.log('      السبب: الخادم غير مشغل أو المنفذ مغلق');
      } else if (error.message.includes('authentication failed')) {
        console.log('      السبب: كلمة المرور أو اسم المستخدم خاطئ');
      } else if (error.message.includes('database') && error.message.includes('does not exist')) {
        console.log('      السبب: قاعدة البيانات غير موجودة');
      } else if (error.message.includes('timeout')) {
        console.log('      السبب: انتهت مهلة الاتصال');
      }
    }
  }

  console.log('\n' + '='.repeat(50));
  
  if (successfulConnection) {
    console.log('🎉 تم العثور على اتصال صحيح!');
    console.log('\n📋 الإعدادات الصحيحة:');
    console.log(`   المضيف: ${successfulConnection.host}`);
    console.log(`   المنفذ: ${successfulConnection.port}`);
    console.log(`   المستخدم: ${successfulConnection.user}`);
    console.log(`   كلمة المرور: ${successfulConnection.password || 'فارغة'}`);
    
    if (successfulConnection.availableDatabases) {
      console.log(`   قواعد البيانات المتاحة: ${successfulConnection.availableDatabases.join(', ')}`);
    }
    
    if (successfulConnection.targetDatabases && successfulConnection.targetDatabases.length > 0) {
      console.log(`   قواعد البيانات المطلوبة الموجودة: ${successfulConnection.targetDatabases.join(', ')}`);
    }
    
    console.log('\n🔧 الخطوات التالية:');
    console.log('1. سأقوم بتحديث ملف routing.config.json بهذه الإعدادات');
    console.log('2. سأقوم بإنشاء جدول case_movements في قاعدة البيانات المناسبة');
    console.log('3. سأقوم بإعادة تشغيل الخادم');
    
    return successfulConnection;
    
  } else {
    console.log('❌ لم يتم العثور على أي اتصال صحيح');
    console.log('\n🛠️ الحلول المقترحة:');
    console.log('1. تحقق من تشغيل خدمة PostgreSQL:');
    console.log('   - افتح Services (services.msc)');
    console.log('   - ابحث عن postgresql وتأكد من تشغيلها');
    console.log('2. تحقق من إعدادات الجدار الناري');
    console.log('3. تحقق من ملف pg_hba.conf للصلاحيات');
    console.log('4. تحقق من ملف postgresql.conf للمنفذ والاستماع');
    
    return null;
  }
}

// تشغيل الاختبار
testPostgreSQLConnection()
  .then(result => {
    if (result) {
      console.log('\n✅ الاختبار مكتمل بنجاح');
    } else {
      console.log('\n❌ فشل في العثور على اتصال صحيح');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('\n💥 خطأ في تشغيل الاختبار:', error.message);
    process.exit(1);
  });
