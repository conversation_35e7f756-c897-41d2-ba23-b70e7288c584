'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import {
  Upload,
  FileText,
  Image,
  File,
  X,
  Plus,
  AlertCircle,
  CheckCircle,
  Loader2,
  ArrowLeft
} from 'lucide-react'

interface UploadFile {
  file: File
  preview?: string
  progress: number
  status: 'pending' | 'uploading' | 'success' | 'error'
  error?: string
}

export default function DocumentUploadPage() {
  const router = useRouter()
  const [files, setFiles] = useState<UploadFile[]>([])
  const [dragActive, setDragActive] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: 'general',
    subcategory: '',
    tags: '',
    access_level: 'private',
    is_confidential: false
  })

  // معالجة سحب وإفلات الملفات
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(Array.from(e.dataTransfer.files))
    }
  }

  // معالجة اختيار الملفات
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(Array.from(e.target.files))
    }
  }

  const handleFiles = (selectedFiles: File[]) => {
    const newFiles: UploadFile[] = selectedFiles.map(file => ({
      file,
      progress: 0,
      status: 'pending',
      preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : undefined
    }))

    setFiles(prev => [...prev, ...newFiles])
  }

  // إزالة ملف
  const removeFile = (index: number) => {
    setFiles(prev => {
      const newFiles = [...prev]
      if (newFiles[index].preview) {
        URL.revokeObjectURL(newFiles[index].preview!)
      }
      newFiles.splice(index, 1)
      return newFiles
    })
  }

  // تحديد أيقونة الملف
  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) {
      return <Image className="h-8 w-8 text-blue-600" />
    } else if (file.type.includes('pdf')) {
      return <FileText className="h-8 w-8 text-red-600" />
    } else {
      return <File className="h-8 w-8 text-gray-600" />
    }
  }

  // تنسيق حجم الملف
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // رفع الملفات
  const uploadFiles = async () => {
    if (files.length === 0) {
      alert('يرجى اختيار ملف واحد على الأقل')
      return
    }

    if (!formData.title.trim()) {
      alert('يرجى إدخال عنوان للوثيقة')
      return
    }

    setUploading(true)

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i]
        
        // تحديث حالة الملف إلى "جاري الرفع"
        setFiles(prev => {
          const newFiles = [...prev]
          newFiles[i] = { ...newFiles[i], status: 'uploading', progress: 0 }
          return newFiles
        })

        const uploadFormData = new FormData()
        uploadFormData.append('file', file.file)
        uploadFormData.append('title', formData.title)
        uploadFormData.append('description', formData.description)
        uploadFormData.append('category', formData.category)
        uploadFormData.append('subcategory', formData.subcategory)
        uploadFormData.append('tags', formData.tags)
        uploadFormData.append('access_level', formData.access_level)
        uploadFormData.append('is_confidential', formData.is_confidential.toString())

        try {
          const response = await fetch('/api/documents/upload', {
            method: 'POST',
            body: uploadFormData
          })

          const result = await response.json()

          if (result.success) {
            // تحديث حالة الملف إلى "نجح"
            setFiles(prev => {
              const newFiles = [...prev]
              newFiles[i] = { ...newFiles[i], status: 'success', progress: 100 }
              return newFiles
            })
          } else {
            // تحديث حالة الملف إلى "خطأ"
            setFiles(prev => {
              const newFiles = [...prev]
              newFiles[i] = { 
                ...newFiles[i], 
                status: 'error', 
                error: result.error || 'فشل في رفع الملف'
              }
              return newFiles
            })
          }
        } catch (error) {
          // تحديث حالة الملف إلى "خطأ"
          setFiles(prev => {
            const newFiles = [...prev]
            newFiles[i] = { 
              ...newFiles[i], 
              status: 'error', 
              error: 'خطأ في الشبكة'
            }
            return newFiles
          })
        }
      }

      // التحقق من نجاح جميع الملفات
      const allSuccess = files.every(f => f.status === 'success')
      if (allSuccess) {
        alert('تم رفع جميع الملفات بنجاح!')
        router.push('/documents')
      }

    } catch (error) {
      console.error('خطأ في رفع الملفات:', error)
      alert('فشل في رفع الملفات')
    } finally {
      setUploading(false)
    }
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">رفع الوثائق</h1>
            <p className="text-gray-600 mt-1">رفع وثائق جديدة إلى النظام</p>
          </div>
          <Button
            variant="outline"
            onClick={() => router.push('/documents')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            العودة للوثائق
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* منطقة رفع الملفات */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Upload className="h-5 w-5 mr-2" />
                  اختيار الملفات
                </CardTitle>
              </CardHeader>
              <CardContent>
                {/* منطقة السحب والإفلات */}
                <div
                  className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                    dragActive
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-300 hover:border-gray-400'
                  }`}
                  onDragEnter={handleDrag}
                  onDragLeave={handleDrag}
                  onDragOver={handleDrag}
                  onDrop={handleDrop}
                >
                  <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    اسحب الملفات هنا أو انقر للاختيار
                  </h3>
                  <p className="text-gray-600 mb-4">
                    يدعم النظام: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG
                  </p>
                  <input
                    type="file"
                    multiple
                    accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png"
                    onChange={handleFileSelect}
                    className="hidden"
                    id="file-upload"
                  />
                  <label htmlFor="file-upload">
                    <Button variant="outline" className="cursor-pointer">
                      <Plus className="h-4 w-4 mr-2" />
                      اختيار الملفات
                    </Button>
                  </label>
                </div>

                {/* قائمة الملفات المختارة */}
                {files.length > 0 && (
                  <div className="mt-6 space-y-3">
                    <h4 className="font-medium text-gray-900">الملفات المختارة:</h4>
                    {files.map((uploadFile, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 border rounded-lg"
                      >
                        <div className="flex items-center gap-3">
                          {uploadFile.preview ? (
                            <img
                              src={uploadFile.preview}
                              alt="معاينة"
                              className="h-10 w-10 object-cover rounded"
                            />
                          ) : (
                            getFileIcon(uploadFile.file)
                          )}
                          <div>
                            <p className="font-medium text-sm">{uploadFile.file.name}</p>
                            <p className="text-xs text-gray-600">
                              {formatFileSize(uploadFile.file.size)}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          {/* حالة الملف */}
                          {uploadFile.status === 'pending' && (
                            <Badge variant="outline">في الانتظار</Badge>
                          )}
                          {uploadFile.status === 'uploading' && (
                            <div className="flex items-center gap-2">
                              <Loader2 className="h-4 w-4 animate-spin" />
                              <Badge className="bg-blue-100 text-blue-800">جاري الرفع</Badge>
                            </div>
                          )}
                          {uploadFile.status === 'success' && (
                            <div className="flex items-center gap-2">
                              <CheckCircle className="h-4 w-4 text-green-600" />
                              <Badge className="bg-green-100 text-green-800">تم بنجاح</Badge>
                            </div>
                          )}
                          {uploadFile.status === 'error' && (
                            <div className="flex items-center gap-2">
                              <AlertCircle className="h-4 w-4 text-red-600" />
                              <Badge className="bg-red-100 text-red-800">فشل</Badge>
                            </div>
                          )}

                          {/* زر الحذف */}
                          {uploadFile.status !== 'uploading' && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => removeFile(index)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* معلومات الوثيقة */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>معلومات الوثيقة</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">العنوان *</label>
                  <Input
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="أدخل عنوان الوثيقة"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">الوصف</label>
                  <Textarea
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="وصف مختصر للوثيقة"
                    rows={3}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">الفئة</label>
                  <select
                    value={formData.category}
                    onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                    className="w-full p-2 border border-gray-300 rounded-md"
                  >
                    <option value="general">عام</option>
                    <option value="legal">قانوني</option>
                    <option value="financial">مالي</option>
                    <option value="contract">عقود</option>
                    <option value="template">نماذج</option>
                    <option value="report">تقارير</option>
                    <option value="correspondence">مراسلات</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">الفئة الفرعية</label>
                  <Input
                    value={formData.subcategory}
                    onChange={(e) => setFormData(prev => ({ ...prev, subcategory: e.target.value }))}
                    placeholder="الفئة الفرعية (اختياري)"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">العلامات</label>
                  <Input
                    value={formData.tags}
                    onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
                    placeholder="علامات مفصولة بفواصل"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">مستوى الوصول</label>
                  <select
                    value={formData.access_level}
                    onChange={(e) => setFormData(prev => ({ ...prev, access_level: e.target.value }))}
                    className="w-full p-2 border border-gray-300 rounded-md"
                  >
                    <option value="public">عام</option>
                    <option value="private">خاص</option>
                    <option value="restricted">مقيد</option>
                  </select>
                </div>

                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="confidential"
                    checked={formData.is_confidential}
                    onChange={(e) => setFormData(prev => ({ ...prev, is_confidential: e.target.checked }))}
                    className="rounded"
                  />
                  <label htmlFor="confidential" className="text-sm font-medium">
                    وثيقة سرية
                  </label>
                </div>

                <Button
                  onClick={uploadFiles}
                  disabled={uploading || files.length === 0}
                  className="w-full"
                >
                  {uploading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      جاري الرفع...
                    </>
                  ) : (
                    <>
                      <Upload className="h-4 w-4 mr-2" />
                      رفع الملفات ({files.length})
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}