import { Client, LocalAuth, MessageMedia } from 'whatsapp-web.js'
import QRCode from 'qrcode'
import fs from 'fs'
import path from 'path'

export interface WhatsAppMessage {
  id: string
  from: string
  to: string
  body: string
  timestamp: Date
  type: 'text' | 'image' | 'document' | 'audio' | 'video'
  mediaUrl?: string
  status: 'pending' | 'sent' | 'delivered' | 'read' | 'failed'
}

export interface WhatsAppContact {
  id: string
  name: string
  number: string
  isRegistered: boolean
  profilePicUrl?: string
}

class WhatsAppClient {
  private client: Client | null = null
  private isReady = false
  private qrCode: string | null = null
  private sessionPath: string
  private eventListeners: Map<string, Function[]> = new Map()

  constructor() {
    this.sessionPath = process.env.WHATSAPP_SESSION_PATH || './whatsapp-session'
    this.initializeClient()
  }

  private initializeClient() {
    try {
      this.client = new Client({
        authStrategy: new LocalAuth({
          clientId: 'legal-system',
          dataPath: this.sessionPath
        }),
        puppeteer: {
          headless: true,
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--single-process',
            '--disable-gpu'
          ]
        }
      })

      this.setupEventListeners()
    } catch (error) {
      console.error('خطأ في تهيئة عميل WhatsApp:', error)
      throw error
    }
  }

  private setupEventListeners() {
    if (!this.client) return

    // عند توليد QR Code
    this.client.on('qr', async (qr) => {
      console.log('📱 QR Code تم توليده')
      try {
        this.qrCode = await QRCode.toDataURL(qr)
        this.emit('qr', this.qrCode)
      } catch (error) {
        console.error('خطأ في توليد QR Code:', error)
      }
    })

    // عند الاتصال بنجاح
    this.client.on('ready', () => {
      console.log('✅ WhatsApp متصل وجاهز!')
      this.isReady = true
      this.qrCode = null
      this.emit('ready')
    })

    // عند قطع الاتصال
    this.client.on('disconnected', (reason) => {
      console.log('❌ WhatsApp منقطع:', reason)
      this.isReady = false
      this.emit('disconnected', reason)
    })

    // عند استلام رسالة
    this.client.on('message', async (message) => {
      try {
        const contact = await message.getContact()
        const chat = await message.getChat()
        
        const whatsappMessage: WhatsAppMessage = {
          id: message.id.id,
          from: contact.number,
          to: chat.id.user,
          body: message.body,
          timestamp: new Date(message.timestamp * 1000),
          type: message.type as any,
          status: 'delivered'
        }

        this.emit('message', whatsappMessage)
      } catch (error) {
        console.error('خطأ في معالجة الرسالة:', error)
      }
    })

    // عند تغيير حالة الرسالة
    this.client.on('message_ack', (message, ack) => {
      const status = this.getMessageStatus(ack)
      this.emit('message_status', { messageId: message.id.id, status })
    })

    // عند حدوث خطأ
    this.client.on('auth_failure', (message) => {
      console.error('❌ فشل المصادقة:', message)
      this.emit('auth_failure', message)
    })
  }

  private getMessageStatus(ack: number): string {
    switch (ack) {
      case 1: return 'sent'
      case 2: return 'delivered'
      case 3: return 'read'
      default: return 'pending'
    }
  }

  // إدارة الأحداث
  on(event: string, callback: Function) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event)!.push(callback)
  }

  private emit(event: string, data?: any) {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.forEach(callback => callback(data))
    }
  }

  // بدء الاتصال
  async initialize(): Promise<void> {
    if (!this.client) {
      throw new Error('عميل WhatsApp غير مهيأ')
    }

    try {
      console.log('🔄 بدء اتصال WhatsApp...')
      await this.client.initialize()
    } catch (error) {
      console.error('خطأ في بدء WhatsApp:', error)
      throw error
    }
  }

  // إرسال رسالة نصية
  async sendMessage(to: string, message: string): Promise<WhatsAppMessage> {
    if (!this.isReady || !this.client) {
      throw new Error('WhatsApp غير متصل')
    }

    try {
      const chatId = to.includes('@') ? to : `${to}@c.us`
      const sentMessage = await this.client.sendMessage(chatId, message)
      
      return {
        id: sentMessage.id.id,
        from: sentMessage.from,
        to: sentMessage.to,
        body: message,
        timestamp: new Date(),
        type: 'text',
        status: 'sent'
      }
    } catch (error) {
      console.error('خطأ في إرسال الرسالة:', error)
      throw error
    }
  }

  // إرسال صورة
  async sendImage(to: string, imagePath: string, caption?: string): Promise<WhatsAppMessage> {
    if (!this.isReady || !this.client) {
      throw new Error('WhatsApp غير متصل')
    }

    try {
      const media = MessageMedia.fromFilePath(imagePath)
      const chatId = to.includes('@') ? to : `${to}@c.us`
      const sentMessage = await this.client.sendMessage(chatId, media, { caption })
      
      return {
        id: sentMessage.id.id,
        from: sentMessage.from,
        to: sentMessage.to,
        body: caption || '',
        timestamp: new Date(),
        type: 'image',
        mediaUrl: imagePath,
        status: 'sent'
      }
    } catch (error) {
      console.error('خطأ في إرسال الصورة:', error)
      throw error
    }
  }

  // إرسال مستند
  async sendDocument(to: string, documentPath: string, filename?: string): Promise<WhatsAppMessage> {
    if (!this.isReady || !this.client) {
      throw new Error('WhatsApp غير متصل')
    }

    try {
      const media = MessageMedia.fromFilePath(documentPath)
      const chatId = to.includes('@') ? to : `${to}@c.us`
      const sentMessage = await this.client.sendMessage(chatId, media, { 
        sendMediaAsDocument: true,
        caption: filename 
      })
      
      return {
        id: sentMessage.id.id,
        from: sentMessage.from,
        to: sentMessage.to,
        body: filename || '',
        timestamp: new Date(),
        type: 'document',
        mediaUrl: documentPath,
        status: 'sent'
      }
    } catch (error) {
      console.error('خطأ في إرسال المستند:', error)
      throw error
    }
  }

  // التحقق من تسجيل الرقم
  async isRegistered(number: string): Promise<boolean> {
    if (!this.isReady || !this.client) {
      throw new Error('WhatsApp غير متصل')
    }

    try {
      const chatId = number.includes('@') ? number : `${number}@c.us`
      const isRegistered = await this.client.isRegisteredUser(chatId)
      return isRegistered
    } catch (error) {
      console.error('خطأ في التحقق من التسجيل:', error)
      return false
    }
  }

  // جلب معلومات جهة الاتصال
  async getContact(number: string): Promise<WhatsAppContact | null> {
    if (!this.isReady || !this.client) {
      throw new Error('WhatsApp غير متصل')
    }

    try {
      const chatId = number.includes('@') ? number : `${number}@c.us`
      const contact = await this.client.getContactById(chatId)
      
      return {
        id: contact.id.user,
        name: contact.name || contact.pushname || 'غير معروف',
        number: contact.number,
        isRegistered: contact.isWAContact,
        profilePicUrl: await contact.getProfilePicUrl().catch(() => undefined)
      }
    } catch (error) {
      console.error('خطأ في جلب معلومات جهة الاتصال:', error)
      return null
    }
  }

  // الحصول على حالة الاتصال
  getStatus() {
    return {
      isReady: this.isReady,
      hasQR: !!this.qrCode,
      qrCode: this.qrCode,
      client: !!this.client
    }
  }

  // قطع الاتصال
  async disconnect(): Promise<void> {
    if (this.client) {
      await this.client.destroy()
      this.client = null
      this.isReady = false
      this.qrCode = null
    }
  }

  // إعادة الاتصال
  async reconnect(): Promise<void> {
    await this.disconnect()
    this.initializeClient()
    await this.initialize()
  }
}

// إنشاء مثيل واحد للاستخدام العام
export const whatsappClient = new WhatsAppClient()
export default WhatsAppClient
