/**
 * سكريبت لتحديث الربط التلقائي للموظفين والعملاء بدليل الحسابات
 * وإنشاء أرقام الموظفين التلقائية
 */

const { Client } = require('pg');

// إعدادات قاعدة البيانات mohammidev
const dbConfig = {
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'mohammidev'
};

/**
 * دالة لإنشاء رمز المنصب
 */
function getPositionCode(position) {
  if (!position) return 'EMP'
  
  const positionCodes = {
    'محامي رسمي': 'LAW',
    'محامي متدرب': 'TRN',
    'مساعد قانوني': 'AST',
    'سكرتير': 'SEC',
    'محاسب': 'ACC',
    'مدير': 'MGR',
    'موظف إداري': 'ADM',
    'مستشار قانوني': 'CON',
    'رئيس قسم': 'HOD',
    'مدير عام': 'GM'
  }

  return positionCodes[position] || 'EMP'
}

async function updateAutomaticLinking() {
  const client = new Client(dbConfig);

  try {
    await client.connect();
    console.log('✅ متصل بقاعدة البيانات mohammidev');

    // 1. الحصول على الحسابات الأب
    console.log('\n🔍 البحث عن الحسابات الأب...');
    
    const employeesParentAccount = await client.query(`
      SELECT id, account_code, account_name FROM chart_of_accounts 
      WHERE account_name_en = 'Employees' OR account_code = '1151'
      LIMIT 1
    `);
    
    const clientsParentAccount = await client.query(`
      SELECT id, account_code, account_name FROM chart_of_accounts 
      WHERE account_code = '1121' OR account_name LIKE '%عملاء%'
      LIMIT 1
    `);

    const employeesAccountId = employeesParentAccount.rows.length > 0 ? employeesParentAccount.rows[0].id : null;
    const clientsAccountId = clientsParentAccount.rows.length > 0 ? clientsParentAccount.rows[0].id : null;

    console.log(`📊 حساب الموظفين الأب: ${employeesAccountId} (${employeesParentAccount.rows[0]?.account_code}: ${employeesParentAccount.rows[0]?.account_name})`);
    console.log(`📊 حساب العملاء الأب: ${clientsAccountId} (${clientsParentAccount.rows[0]?.account_code}: ${clientsParentAccount.rows[0]?.account_name})`);

    // 2. تحديث الموظفين
    console.log('\n👥 تحديث الموظفين...');
    
    const employees = await client.query(`
      SELECT id, name, position, employee_number, account_id
      FROM employees
      ORDER BY id
    `);

    console.log(`📊 عدد الموظفين: ${employees.rows.length}`);

    let employeesUpdated = 0;
    let employeeNumbersGenerated = 0;

    for (const employee of employees.rows) {
      let updates = [];
      let values = [employee.id];
      let paramIndex = 2;

      // إنشاء رقم الموظف إذا لم يكن موجوداً
      if (!employee.employee_number) {
        const positionCode = getPositionCode(employee.position);
        const employeeNumber = `${positionCode}${String(employee.id).padStart(3, '0')}`;
        updates.push(`employee_number = $${paramIndex}`);
        values.push(employeeNumber);
        paramIndex++;
        employeeNumbersGenerated++;
        console.log(`   📝 رقم موظف جديد: ${employee.name} → ${employeeNumber}`);
      }

      // تحديث الحساب الأب
      if (employee.account_id !== employeesAccountId && employeesAccountId) {
        updates.push(`account_id = $${paramIndex}`);
        values.push(employeesAccountId);
        paramIndex++;
        console.log(`   💰 تحديث حساب: ${employee.name} → ${employeesAccountId}`);
      }

      // تنفيذ التحديث إذا كان هناك تغييرات
      if (updates.length > 0) {
        const updateQuery = `UPDATE employees SET ${updates.join(', ')} WHERE id = $1`;
        await client.query(updateQuery, values);
        employeesUpdated++;
        console.log(`   ✅ تم تحديث: ${employee.name}`);
      } else {
        console.log(`   ℹ️ لا يحتاج تحديث: ${employee.name}`);
      }
    }

    // 3. تحديث العملاء
    console.log('\n👤 تحديث العملاء...');
    
    const clients = await client.query(`
      SELECT id, name, account_id
      FROM clients
      ORDER BY id
    `);

    console.log(`📊 عدد العملاء: ${clients.rows.length}`);

    let clientsUpdated = 0;

    for (const clientData of clients.rows) {
      // تحديث الحساب الأب للعملاء
      if (clientData.account_id !== clientsAccountId && clientsAccountId) {
        await client.query(
          'UPDATE clients SET account_id = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
          [clientsAccountId, clientData.id]
        );
        clientsUpdated++;
        console.log(`   💰 تحديث حساب العميل: ${clientData.name} → ${clientsAccountId}`);
      } else {
        console.log(`   ℹ️ العميل لديه حساب صحيح: ${clientData.name}`);
      }
    }

    // 4. التحقق النهائي
    console.log('\n🔍 التحقق النهائي...');
    
    // فحص الموظفين
    const employeesCheck = await client.query(`
      SELECT 
        COUNT(*) as total_employees,
        COUNT(employee_number) as employees_with_number,
        COUNT(CASE WHEN account_id = $1 THEN 1 END) as employees_with_correct_account
      FROM employees
    `, [employeesAccountId]);

    const empStats = employeesCheck.rows[0];
    console.log('\n📊 إحصائيات الموظفين:');
    console.log(`   - إجمالي الموظفين: ${empStats.total_employees}`);
    console.log(`   - موظفين لديهم رقم موظف: ${empStats.employees_with_number}`);
    console.log(`   - موظفين مرتبطين بالحساب الصحيح: ${empStats.employees_with_correct_account}`);

    // فحص العملاء
    const clientsCheck = await client.query(`
      SELECT 
        COUNT(*) as total_clients,
        COUNT(CASE WHEN account_id = $1 THEN 1 END) as clients_with_correct_account
      FROM clients
    `, [clientsAccountId]);

    const clientStats = clientsCheck.rows[0];
    console.log('\n📊 إحصائيات العملاء:');
    console.log(`   - إجمالي العملاء: ${clientStats.total_clients}`);
    console.log(`   - عملاء مرتبطين بالحساب الصحيح: ${clientStats.clients_with_correct_account}`);

    // 5. عرض عينة من البيانات المحدثة
    console.log('\n📋 عينة من الموظفين المحدثين:');
    const employeesSample = await client.query(`
      SELECT e.name, e.employee_number, e.account_id, c.account_name
      FROM employees e
      LEFT JOIN chart_of_accounts c ON e.account_id = c.id
      ORDER BY e.id
      LIMIT 5
    `);

    employeesSample.rows.forEach(emp => {
      console.log(`   - ${emp.name}: رقم=${emp.employee_number}, حساب=${emp.account_id} (${emp.account_name || 'غير موجود'})`);
    });

    console.log('\n📋 عينة من العملاء المحدثين:');
    const clientsSample = await client.query(`
      SELECT c.name, c.account_id, coa.account_name
      FROM clients c
      LEFT JOIN chart_of_accounts coa ON c.account_id = coa.id
      ORDER BY c.id
      LIMIT 5
    `);

    clientsSample.rows.forEach(client => {
      console.log(`   - ${client.name}: حساب=${client.account_id} (${client.account_name || 'غير موجود'})`);
    });

    console.log('\n📈 ملخص التحديثات:');
    console.log(`   - موظفين تم تحديثهم: ${employeesUpdated}`);
    console.log(`   - أرقام موظفين تم إنشاؤها: ${employeeNumbersGenerated}`);
    console.log(`   - عملاء تم تحديثهم: ${clientsUpdated}`);

    console.log('\n🎉 تم تحديث الربط التلقائي بنجاح!');
    console.log('💡 الآن جميع الموظفين والعملاء مرتبطين بالحسابات الأب الصحيحة');
    console.log('🔄 عند إضافة موظف أو عميل جديد، سيتم ربطه تلقائياً بالحساب الأب');

  } catch (error) {
    console.error('❌ خطأ في تحديث الربط التلقائي:', error.message);
    throw error;
  } finally {
    await client.end();
  }
}

// تشغيل السكريبت
if (require.main === module) {
  updateAutomaticLinking().catch(console.error);
}

module.exports = { updateAutomaticLinking, getPositionCode };
