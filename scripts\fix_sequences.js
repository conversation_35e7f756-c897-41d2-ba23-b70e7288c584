// سكربت لإصلاح تزامن تسلسلات المفاتيح الأساسية بعد الاستيراد أو النقل
// يصلح مشكلة: duplicate key value violates unique constraint "journal_entries_pkey"

const { Pool } = require('pg')
const fs = require('fs')
const path = require('path')

// تحميل ملف التوجيه (إن وُجد)
let routingConfig = null
try {
  const configPath = path.join(process.cwd(), 'routing.config.json')
  const configData = fs.readFileSync(configPath, 'utf8')
  routingConfig = JSON.parse(configData)
} catch (error) {
  console.warn('⚠️ لم يُعثر على routing.config.json، سيتم استخدام الإعدادات الافتراضية')
}

// تحديد قاعدة البيانات بناءً على المنفذ (متوافق مع src/lib/db.ts)
const getDatabaseConfig = () => {
  const port = process.env.PORT || '7443'

  if (routingConfig && routingConfig.routes && routingConfig.routes[port]) {
    const route = routingConfig.routes[port]
    const defaultConfig = routingConfig.default_config || {}
    return {
      database: route.database,
      user: defaultConfig.db_user || 'postgres',
      host: defaultConfig.db_host || 'localhost',
      password: process.env.DB_PASSWORD || defaultConfig.db_password || 'yemen123',
      port: defaultConfig.db_port || 5432,
    }
  }

  console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${port}، سيتم استخدام الإعدادات الافتراضية`)
  return {
    database: 'mohammi',
    user: 'postgres',
    host: 'localhost',
    password: process.env.DB_PASSWORD || 'yemen123',
    port: 5432,
  }
}

const dbConfig = getDatabaseConfig()
const pool = new Pool({
  ...dbConfig,
  ssl: false,
  max: 5,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 5000,
})

async function query(text, params) {
  const client = await pool.connect()
  try {
    await client.query('SET client_encoding TO UTF8')
    return await client.query(text, params)
  } finally {
    client.release()
  }
}

async function fixSequence(table, idCol = 'id') {
  const seqSql = `SELECT pg_get_serial_sequence($1, $2) as seq`;
  const seqRes = await query(seqSql, [table, idCol]);
  const seqName = seqRes.rows[0]?.seq;
  if (!seqName) {
    console.log(`⚠️ لا يوجد تسلسل مرتبط بالعمود ${idCol} في الجدول ${table}`);
    return;
  }
  const maxIdRes = await query(`SELECT COALESCE(MAX(${idCol}), 0) AS max_id FROM ${table}`);
  const maxId = parseInt(maxIdRes.rows[0].max_id || 0, 10);
  // setval(seq, new_value, is_called)
  // نضبط is_called = true بحيث يكون الاستدعاء التالي nextval() > max
  await query(`SELECT setval($1, $2, true)`, [seqName, Math.max(maxId, 1)]);
  console.log(`✅ تم ضبط التسلسل ${seqName} إلى ${Math.max(maxId, 1)} (table=${table})`);
}

async function main() {
  try {
    await fixSequence('journal_entries', 'id');
    await fixSequence('journal_entry_details', 'id');
    console.log('🎉 اكتمل إصلاح التسلسلات بنجاح');
    process.exit(0);
  } catch (err) {
    console.error('❌ فشل إصلاح التسلسلات:', err);
    process.exit(1);
  }
}

main();
