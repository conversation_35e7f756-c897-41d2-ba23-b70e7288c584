'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Archive,
  Search,
  Filter,
  Download,
  Eye,
  RotateCcw,
  Calendar,
  FileText,
  User,
  ArrowLeft,
  Trash2,
  AlertTriangle
} from 'lucide-react'

interface ArchivedDocument {
  id: number
  title: string
  file_name: string
  file_size: number
  category: string
  subcategory: string
  access_level: string
  is_confidential: boolean
  archived_date: string
  archived_by: string
  archive_reason: string
  created_date: string
  tags: string[]
}

export default function DocumentArchivePage() {
  const [documents, setDocuments] = useState<ArchivedDocument[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [selectedAccessLevel, setSelectedAccessLevel] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  // جلب الوثائق المؤرشفة
  const fetchArchivedDocuments = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
        search: searchTerm,
        category: selectedCategory,
        access_level: selectedAccessLevel,
        archived: 'true'
      })

      const response = await fetch(`/api/documents?${params}`)
      const result = await response.json()

      if (result.success) {
        setDocuments(result.data.documents)
        setTotalPages(result.data.totalPages)
      }
    } catch (error) {
      console.error('خطأ في جلب الوثائق المؤرشفة:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchArchivedDocuments()
  }, [currentPage, searchTerm, selectedCategory, selectedAccessLevel])

  // استعادة وثيقة من الأرشيف
  const restoreDocument = async (documentId: number) => {
    if (!confirm('هل أنت متأكد من استعادة هذه الوثيقة من الأرشيف؟')) {
      return
    }

    try {
      const response = await fetch(`/api/documents/${documentId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ is_archived: false })
      })

      const result = await response.json()
      if (result.success) {
        alert('تم استعادة الوثيقة بنجاح')
        fetchArchivedDocuments()
      } else {
        alert(result.error || 'فشل في استعادة الوثيقة')
      }
    } catch (error) {
      console.error('خطأ في استعادة الوثيقة:', error)
      alert('خطأ في استعادة الوثيقة')
    }
  }

  // حذف وثيقة نهائياً
  const deleteDocumentPermanently = async (documentId: number) => {
    if (!confirm('تحذير: سيتم حذف الوثيقة نهائياً ولا يمكن استعادتها. هل أنت متأكد؟')) {
      return
    }

    try {
      const response = await fetch(`/api/documents/${documentId}`, {
        method: 'DELETE'
      })

      const result = await response.json()
      if (result.success) {
        alert('تم حذف الوثيقة نهائياً')
        fetchArchivedDocuments()
      } else {
        alert(result.error || 'فشل في حذف الوثيقة')
      }
    } catch (error) {
      console.error('خطأ في حذف الوثيقة:', error)
      alert('خطأ في حذف الوثيقة')
    }
  }

  // تنسيق حجم الملف
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // تنسيق التاريخ
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // تحديد لون مستوى الوصول
  const getAccessLevelColor = (level: string) => {
    switch (level) {
      case 'public': return 'bg-green-100 text-green-800'
      case 'private': return 'bg-blue-100 text-blue-800'
      case 'restricted': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // تحديد لون الفئة
  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      'legal': 'bg-purple-100 text-purple-800',
      'financial': 'bg-yellow-100 text-yellow-800',
      'contract': 'bg-orange-100 text-orange-800',
      'template': 'bg-cyan-100 text-cyan-800',
      'report': 'bg-pink-100 text-pink-800',
      'correspondence': 'bg-indigo-100 text-indigo-800'
    }
    return colors[category] || 'bg-gray-100 text-gray-800'
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Archive className="h-8 w-8 mr-3 text-gray-600" />
              أرشيف الوثائق
            </h1>
            <p className="text-gray-600 mt-1">إدارة الوثائق المؤرشفة والمحذوفة</p>
          </div>
          <Button
            variant="outline"
            onClick={() => window.history.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            العودة
          </Button>
        </div>

        {/* أدوات البحث والتصفية */}
        <Card>
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في الوثائق المؤرشفة..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
              
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="p-2 border border-gray-300 rounded-md"
              >
                <option value="">جميع الفئات</option>
                <option value="legal">قانوني</option>
                <option value="financial">مالي</option>
                <option value="contract">عقود</option>
                <option value="template">نماذج</option>
                <option value="report">تقارير</option>
                <option value="correspondence">مراسلات</option>
              </select>
              
              <select
                value={selectedAccessLevel}
                onChange={(e) => setSelectedAccessLevel(e.target.value)}
                className="p-2 border border-gray-300 rounded-md"
              >
                <option value="">جميع مستويات الوصول</option>
                <option value="public">عام</option>
                <option value="private">خاص</option>
                <option value="restricted">مقيد</option>
              </select>
              
              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm('')
                  setSelectedCategory('')
                  setSelectedAccessLevel('')
                  setCurrentPage(1)
                }}
              >
                <Filter className="h-4 w-4 mr-2" />
                إعادة تعيين
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* قائمة الوثائق المؤرشفة */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>الوثائق المؤرشفة ({documents.length})</span>
              {documents.length > 0 && (
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <AlertTriangle className="h-4 w-4" />
                  <span>الوثائق المؤرشفة غير مرئية للمستخدمين العاديين</span>
                </div>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-4 text-gray-600">جاري تحميل الوثائق المؤرشفة...</p>
              </div>
            ) : documents.length === 0 ? (
              <div className="text-center py-12">
                <Archive className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">لا توجد وثائق مؤرشفة</p>
              </div>
            ) : (
              <div className="space-y-4">
                {documents.map((document) => (
                  <div key={document.id} className="border rounded-lg p-4 hover:bg-gray-50">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-4 flex-1">
                        <FileText className="h-8 w-8 text-gray-600 mt-1" />
                        
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-semibold text-lg">{document.title}</h3>
                            {document.is_confidential && (
                              <Badge className="bg-red-100 text-red-800 text-xs">سري</Badge>
                            )}
                          </div>
                          
                          <div className="flex flex-wrap gap-2 mb-3">
                            <Badge className={getCategoryColor(document.category)}>
                              {document.category}
                            </Badge>
                            <Badge className={getAccessLevelColor(document.access_level)}>
                              {document.access_level === 'public' ? 'عام' : 
                               document.access_level === 'private' ? 'خاص' : 'مقيد'}
                            </Badge>
                            {document.subcategory && (
                              <Badge variant="outline">{document.subcategory}</Badge>
                            )}
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                            <div>
                              <p><strong>اسم الملف:</strong> {document.file_name}</p>
                              <p><strong>الحجم:</strong> {formatFileSize(document.file_size)}</p>
                              <p><strong>تاريخ الإنشاء:</strong> {formatDate(document.created_date)}</p>
                            </div>
                            <div>
                              <p><strong>تاريخ الأرشفة:</strong> {formatDate(document.archived_date)}</p>
                              <p><strong>أرشف بواسطة:</strong> {document.archived_by}</p>
                              {document.archive_reason && (
                                <p><strong>سبب الأرشفة:</strong> {document.archive_reason}</p>
                              )}
                            </div>
                          </div>
                          
                          {document.tags && document.tags.length > 0 && (
                            <div className="mt-3">
                              <div className="flex flex-wrap gap-1">
                                {document.tags.map((tag, index) => (
                                  <Badge key={index} variant="outline" className="text-xs">
                                    {tag}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex flex-col gap-2 ml-4">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => window.open(`/api/documents/download/${document.id}`, '_blank')}
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                        
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => restoreDocument(document.id)}
                          className="text-green-600 hover:text-green-700"
                        >
                          <RotateCcw className="h-4 w-4" />
                        </Button>
                        
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => deleteDocumentPermanently(document.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* التنقل بين الصفحات */}
        {totalPages > 1 && (
          <div className="flex justify-center gap-2">
            <Button
              variant="outline"
              disabled={currentPage === 1}
              onClick={() => setCurrentPage(prev => prev - 1)}
            >
              السابق
            </Button>
            
            <span className="flex items-center px-4 py-2 text-sm text-gray-600">
              صفحة {currentPage} من {totalPages}
            </span>
            
            <Button
              variant="outline"
              disabled={currentPage === totalPages}
              onClick={() => setCurrentPage(prev => prev + 1)}
            >
              التالي
            </Button>
          </div>
        )}
      </div>
    </MainLayout>
  )
}