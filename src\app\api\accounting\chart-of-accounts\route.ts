import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب جميع الحسابات
export async function GET(request: NextRequest) {
  try {

    const { searchParams } = new URL(request.url)
    const level = searchParams.get('level')
    const includeLinked = searchParams.get('include_linked') === 'true'
    const excludeLinkedTables = searchParams.get('exclude_linked_tables') === 'true'
    const onlyTransactional = searchParams.get('only_transactional') === 'true'

    let sql = `
      SELECT
        coa.id,
        coa.account_code,
        coa.account_name,
        coa.account_name_en,
        coa.level_1_code,
        coa.level_2_code,
        coa.level_3_code,
        coa.level_4_code,
        coa.account_level,
        coa.parent_id,
        coa.account_type,
        coa.account_nature,
        coa.is_active,
        coa.allow_transactions,
        coa.linked_table,
        coa.auto_create_sub_accounts,
        coa.opening_balance,
        coa.current_balance,
        coa.description,
        coa.created_date,
        coa.is_linked_record,
        coa.original_table,
        coa.linked_record_id
      FROM chart_of_accounts coa
      WHERE coa.is_active = true
    `

    // إخفاء الجداول المربوطة مباشرة (العملاء، الموظفين، الموردين)
    if (excludeLinkedTables) {
      sql += ` AND (coa.is_linked_record IS NULL OR coa.is_linked_record = false)`
    }

    const params: any[] = []
    let paramIndex = 1

    // تصفية حسب المستوى
    if (level && level !== 'all') {
      sql += ` AND coa.account_level = $${paramIndex}`
      params.push(parseInt(level))
      paramIndex++
    }

    // تصفية للحسابات التي تقبل معاملات فقط
    if (onlyTransactional) {
      sql += ` AND coa.allow_transactions = true`
    }

    sql += ` ORDER BY coa.account_code`

    const result = await query(sql, params)
    let accounts = result.rows

    // ملاحظة: تم إزالة عرض العملاء والموظفين والموردين من دليل الحسابات
    // سيتم ربطهم يدوياً من خلال تعديل الحسابات

    return NextResponse.json({
      success: true,
      accounts,
      total: accounts.length,
      message: 'تم جلب الحسابات بنجاح'
    })

  } catch (error) {
    console.error('خطأ في جلب الحسابات:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب الحسابات',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// POST - إضافة حساب جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      account_code,
      account_name,
      account_name_en,
      account_level,
      parent_id,
      account_type,
      account_nature = 'مدين',
      linked_table,
      auto_create_sub_accounts = false,
      allow_transactions = false,
      opening_balance = 0,
      description
    } = body

    // التحقق من صحة البيانات
    if (!account_code || !account_name || !account_level || !account_type) {
      return NextResponse.json({
        success: false,
        error: 'البيانات المطلوبة مفقودة'
      }, { status: 400 })
    }

    // منع تفعيل المعاملات على حسابات التحكم
    if (allow_transactions && linked_table && linked_table !== 'none') {
      return NextResponse.json({
        success: false,
        error: 'لا يمكن تفعيل المعاملات على حسابات التحكم المرتبطة بجداول خارجية'
      }, { status: 400 })
    }

    // التحقق من عدم تكرار رمز الحساب
    const existingAccount = await query(
      'SELECT id FROM chart_of_accounts WHERE account_code = $1',
      [account_code]
    )

    if (existingAccount.rows.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'رمز الحساب موجود مسبقاً'
      }, { status: 400 })
    }

    // تحديد أكواد المستويات
    let level_1_code = null, level_2_code = null, level_3_code = null, level_4_code = null

    if (account_level === 1) {
      level_1_code = account_code
    } else if (account_level === 2) {
      level_1_code = account_code.substring(0, 2)
      level_2_code = account_code
    } else if (account_level === 3) {
      level_1_code = account_code.substring(0, 2)
      level_2_code = account_code.substring(0, 4)
      level_3_code = account_code
    } else if (account_level === 4) {
      level_1_code = account_code.substring(0, 2)
      level_2_code = account_code.substring(0, 4)
      level_3_code = account_code.substring(0, 6)
      level_4_code = account_code
    }

    // استخدام القيمة المرسلة من النموذج أو افتراضياً حسب المستوى
    const finalAllowTransactions = allow_transactions !== undefined ? allow_transactions : (account_level === 4)

    // إدراج الحساب الجديد
    const result = await query(`
      INSERT INTO chart_of_accounts (
        account_code, account_name, account_name_en,
        level_1_code, level_2_code, level_3_code, level_4_code,
        account_level, parent_id, account_type, account_nature,
        allow_transactions, linked_table, auto_create_sub_accounts,
        opening_balance, current_balance, description
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
      RETURNING *
    `, [
      account_code, account_name, account_name_en,
      level_1_code, level_2_code, level_3_code, level_4_code,
      account_level, parent_id, account_type, account_nature,
      finalAllowTransactions, linked_table, auto_create_sub_accounts,
      opening_balance, opening_balance, description
    ])

    return NextResponse.json({
      success: true,
      account: result.rows[0],
      message: 'تم إضافة الحساب بنجاح'
    })

  } catch (error) {
    console.error('خطأ في إضافة الحساب:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في إضافة الحساب',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// PUT - تحديث حساب
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      account_name,
      account_name_en,
      account_type,
      account_nature,
      linked_table,
      auto_create_sub_accounts,
      description,
      is_active = true
    } = body

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف الحساب مطلوب'
      }, { status: 400 })
    }

    const result = await query(`
      UPDATE chart_of_accounts
      SET
        account_name = $2,
        account_name_en = $3,
        account_type = $4,
        account_nature = $5,
        linked_table = $6,
        auto_create_sub_accounts = $7,
        description = $8,
        is_active = $9,
        updated_date = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `, [
      id, account_name, account_name_en, account_type, account_nature,
      linked_table, auto_create_sub_accounts, description, is_active
    ])

    if (result.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الحساب غير موجود'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      account: result.rows[0],
      message: 'تم تحديث الحساب بنجاح'
    })

  } catch (error) {
    console.error('خطأ في تحديث الحساب:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث الحساب',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
