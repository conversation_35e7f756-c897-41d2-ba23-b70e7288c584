'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import {
  Scale,
  Users,
  FileText,
  Shield,
  Building,
  Gavel,
  BookOpen,
  Award,
  Phone,
  Mail,
  MapPin,
  Star,
  TrendingUp,
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  MessageCircle,
  LogIn,
  Download,
  Home
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

interface Service {
  id: number
  title: string
  slug: string
  description: string
  content: string
  icon_name: string
  icon_color: string
  image_url?: string
  meta_title?: string
  meta_description?: string
}

interface CompanyData {
  id: number
  name: string
  logo_url?: string
  logo_image_url?: string
  phone?: string
  email?: string
  address?: string
  city?: string
  country?: string
  description?: string
}

// خريطة الأيقونات
const iconMap: { [key: string]: any } = {
  Scale,
  Users,
  FileText,
  Shield,
  Building,
  Gavel,
  BookO<PERSON>,
  Award,
  Phone,
  Mail,
  MapPin,
  Star,
  TrendingUp,
  CheckCircle,
  ArrowRight,
  MessageCircle,
  LogIn,
  Download
}

export default function ServicePage() {
  const params = useParams()
  const router = useRouter()
  const [service, setService] = useState<Service | null>(null)
  const [companyData, setCompanyData] = useState<CompanyData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (params.slug) {
      fetchData()
    }
  }, [params.slug])

  const fetchData = async () => {
    try {
      setLoading(true)
      setError(null)

      // جلب بيانات الخدمة من serviceslow
      const serviceResponse = await fetch(`/api/serviceslow/slug/${params.slug}`)
      const serviceResult = await serviceResponse.json()
      
      if (!serviceResult.success) {
        setError('الخدمة غير موجودة')
        return
      }
      
      setService(serviceResult.data)

      // جلب بيانات الشركة
      const companyResponse = await fetch('/api/company')
      const companyResult = await companyResponse.json()
      if (companyResult.success && companyResult.data.length > 0) {
        setCompanyData(companyResult.data[0])
      }

    } catch (error) {
      console.error('Error fetching data:', error)
      setError('حدث خطأ في تحميل البيانات')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <div className="animate-pulse">
          <div className="h-16 bg-gray-200"></div>
          <div className="container mx-auto px-4 py-8">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
            <div className="space-y-4">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
              <div className="h-4 bg-gray-200 rounded w-4/6"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !service) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Scale className="h-12 w-12 text-red-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">الخدمة غير موجودة</h1>
          <p className="text-gray-600 mb-6">{error || 'لم يتم العثور على الخدمة المطلوبة'}</p>
          <Link href="/home">
            <Button className="bg-blue-600 hover:bg-blue-700 text-white">
              <Home className="h-4 w-4 mr-2" />
              العودة للرئيسية
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  const IconComponent = iconMap[service.icon_name] || Scale

  return (
    <div className="min-h-screen" style={{ background: 'linear-gradient(135deg, #222222 0%, #**********%)' }}>
      {/* Header */}
      <header className="shadow-sm border-b sticky top-0 z-50" style={{ background: 'linear-gradient(135deg, #222222 0%, #**********%)', borderColor: 'rgba(204, 169, 103, 0.2)' }}>
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              {companyData?.logo_image_url ? (
                <Image
                  src={companyData.logo_image_url}
                  alt="شعار الشركة"
                  width={60}
                  height={60}
                  className="rounded-lg mr-3 object-contain"
                />
              ) : companyData?.logo_url ? (
                <Image
                  src={companyData.logo_url}
                  alt="شعار الشركة"
                  width={60}
                  height={60}
                  className="rounded-lg mr-3 object-contain"
                />
              ) : (
                <div className="w-12 h-12 rounded-lg flex items-center justify-center mr-3 shadow-lg" style={{ background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)' }}>
                  <Scale className="h-6 w-6 text-gray-900" />
                </div>
              )}
              <div>
                <h1 className="text-xl font-bold" style={{ color: '#cca967' }}>
                  {companyData?.name || 'مؤسسة المحاماة والاستشارات القانونية'}
                </h1>
                <p className="text-sm text-gray-300">Excellence in Legal Services</p>
              </div>
            </div>

            <nav className="hidden md:flex items-center space-x-8 space-x-reverse">
              <Link href="/home" className="text-gray-300 hover:text-yellow-400 font-medium transition-colors">الرئيسية</Link>
              <Link href="/home#services" className="text-gray-300 hover:text-yellow-400 font-medium transition-colors">خدماتنا</Link>
              <Link href="/home#about" className="text-gray-300 hover:text-yellow-400 font-medium transition-colors">من نحن</Link>
              <Link href="/home#library" className="text-gray-300 hover:text-yellow-400 font-medium transition-colors">المكتبة القانونية</Link>
            </nav>

            <Link href="/dashboard">
              <Button className="text-gray-900 hover:text-gray-800 shadow-lg" style={{ background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)' }}>
                <LogIn className="h-4 w-4 mr-2" />
                دخول النظام
              </Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Breadcrumb */}
      <div className="py-4" style={{ background: 'rgba(204, 169, 103, 0.1)' }}>
        <div className="container mx-auto px-4">
          <nav className="flex items-center space-x-2 space-x-reverse text-sm">
            <Link href="/home" className="text-yellow-400 hover:text-yellow-300 transition-colors">الرئيسية</Link>
            <ArrowLeft className="h-4 w-4 text-gray-500" />
            <Link href="/home#services" className="text-yellow-400 hover:text-yellow-300 transition-colors">الخدمات</Link>
            <ArrowLeft className="h-4 w-4 text-gray-500" />
            <span className="text-gray-300">{service.title}</span>
          </nav>
        </div>
      </div>

      {/* Hero Section */}
      <section className="py-16" style={{ background: 'linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%)' }}>
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex justify-center mb-6">
              <div
                className="w-20 h-20 rounded-2xl flex items-center justify-center shadow-2xl"
                style={{ background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)' }}
              >
                <IconComponent className="h-10 w-10 text-gray-900" />
              </div>
            </div>

            <h1 className="text-4xl lg:text-5xl font-bold mb-6" style={{ color: '#cca967' }}>{service.title}</h1>
            <p className="text-xl text-gray-300 leading-relaxed max-w-3xl mx-auto">
              {service.description}
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mt-8">
              <Button size="lg" className="text-gray-900 hover:text-gray-800 px-8 py-4 text-lg font-semibold shadow-lg" style={{ background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)' }}>
                <MessageCircle className="h-5 w-5 mr-2" />
                استشارة مجانية
              </Button>
              <Button size="lg" variant="outline" className="border-2 text-yellow-400 hover:bg-yellow-400/10 backdrop-blur-sm px-8 py-4 text-lg font-semibold" style={{ borderColor: '#cca967' }}>
                <Phone className="h-5 w-5 mr-2" />
                {companyData?.phone || '+967-1-234567'}
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Content Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <Card className="shadow-xl border-0 rounded-2xl overflow-hidden" style={{ background: 'linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%)', borderColor: 'rgba(204, 169, 103, 0.2)' }}>
              <CardContent className="p-8 lg:p-12">
                <div
                  className="prose prose-lg max-w-none text-gray-300"
                  style={{
                    '--tw-prose-headings': '#cca967',
                    '--tw-prose-links': '#d4b876',
                    '--tw-prose-bold': '#cca967',
                    '--tw-prose-bullets': '#cca967',
                    '--tw-prose-counters': '#cca967'
                  } as React.CSSProperties}
                  dangerouslySetInnerHTML={{ __html: service.content }}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16" style={{ background: 'rgba(204, 169, 103, 0.1)' }}>
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-4" style={{ color: '#cca967' }}>هل تحتاج لاستشارة قانونية؟</h2>
            <p className="text-xl text-gray-300 mb-8">
              تواصل معنا الآن للحصول على استشارة مجانية من فريقنا المتخصص
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="p-6 rounded-xl shadow-lg" style={{ background: 'linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%)' }}>
                <Phone className="h-8 w-8 mx-auto mb-4" style={{ color: '#cca967' }} />
                <h3 className="font-semibold mb-2" style={{ color: '#cca967' }}>اتصل بنا</h3>
                <p className="text-gray-300">{companyData?.phone || '+967-1-234567'}</p>
              </div>

              <div className="p-6 rounded-xl shadow-lg" style={{ background: 'linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%)' }}>
                <Mail className="h-8 w-8 mx-auto mb-4" style={{ color: '#cca967' }} />
                <h3 className="font-semibold mb-2" style={{ color: '#cca967' }}>راسلنا</h3>
                <p className="text-gray-300">{companyData?.email || '<EMAIL>'}</p>
              </div>

              <div className="p-6 rounded-xl shadow-lg" style={{ background: 'linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%)' }}>
                <MapPin className="h-8 w-8 mx-auto mb-4" style={{ color: '#cca967' }} />
                <h3 className="font-semibold mb-2" style={{ color: '#cca967' }}>زورنا</h3>
                <p className="text-gray-300">{companyData?.address || 'العنوان غير محدد'}</p>
              </div>
            </div>

            <Button size="lg" className="text-gray-900 hover:text-gray-800 px-8 py-4 text-lg font-semibold shadow-lg" style={{ background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)' }}>
              <MessageCircle className="h-5 w-5 mr-2" />
              احجز استشارة مجانية
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12" style={{ background: 'linear-gradient(135deg, #1a1a1a 0%, #0f0f0f 100%)' }}>
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="flex items-center justify-center mb-4">
              {companyData?.logo_image_url ? (
                <Image
                  src={companyData.logo_image_url}
                  alt="شعار الشركة"
                  width={40}
                  height={40}
                  className="rounded-lg mr-3 object-contain"
                />
              ) : companyData?.logo_url ? (
                <Image
                  src={companyData.logo_url}
                  alt="شعار الشركة"
                  width={40}
                  height={40}
                  className="rounded-lg mr-3 object-contain"
                />
              ) : (
                <div className="w-10 h-10 rounded-lg flex items-center justify-center mr-3 shadow-lg" style={{ background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)' }}>
                  <Scale className="h-5 w-5 text-gray-900" />
                </div>
              )}
              <h3 className="text-xl font-bold" style={{ color: '#cca967' }}>
                {companyData?.name || 'مؤسسة المحاماة والاستشارات القانونية'}
              </h3>
            </div>
            <p className="text-gray-400 mb-4">
              &copy; {new Date().getFullYear()} <span style={{ color: '#cca967' }}>{companyData?.name || 'مؤسسة المحاماة والاستشارات القانونية'}</span>. جميع الحقوق محفوظة.
            </p>
            <Link href="/home" className="text-yellow-400 hover:text-yellow-300 transition-colors">
              العودة للرئيسية
            </Link>
          </div>
        </div>
      </footer>
    </div>
  )
}
