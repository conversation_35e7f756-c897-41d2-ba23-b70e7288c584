import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

export async function GET() {
  try {
    const result = await query(`
      SELECT 
        sd.*,
        s.name as service_name,
        l.name as lineage_name
      FROM service_distributions sd
      LEFT JOIN services s ON sd.service_id = s.id
      LEFT JOIN lineages l ON sd.lineage_id = l.id
      ORDER BY sd.created_date DESC
    `)
    
    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching service distributions:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات توزيع الخدمات' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { service_id, lineage_id, percentage, amount, notes } = body

    if (!service_id || !lineage_id) {
      return NextResponse.json(
        { success: false, error: 'معرف الخدمة ومعرف النسبة مطلوبان' },
        { status: 400 }
      )
    }

    const result = await query(`
      INSERT INTO service_distributions (service_id, lineage_id, percentage, amount, notes)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `, [service_id, lineage_id, percentage || 0, amount || 0, notes || ''])

    return NextResponse.json({
      success: true,
      message: 'تم إضافة توزيع الخدمة بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error creating service distribution:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة توزيع الخدمة' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, service_id, lineage_id, percentage, amount, notes } = body

    if (!id || !service_id || !lineage_id) {
      return NextResponse.json(
        { success: false, error: 'المعرف ومعرف الخدمة ومعرف النسبة مطلوبان' },
        { status: 400 }
      )
    }

    const result = await query(`
      UPDATE service_distributions 
      SET service_id = $1, lineage_id = $2, percentage = $3, amount = $4, 
          notes = $5, updated_at = CURRENT_TIMESTAMP
      WHERE id = $6
      RETURNING *
    `, [service_id, lineage_id, percentage || 0, amount || 0, notes || '', id])

    return NextResponse.json({
      success: true,
      message: 'تم تحديث توزيع الخدمة بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error updating service distribution:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث توزيع الخدمة' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف التوزيع مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(
      'DELETE FROM service_distributions WHERE id = $1 RETURNING *',
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'توزيع الخدمة غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف توزيع الخدمة بنجاح'
    })
  } catch (error) {
    console.error('Error deleting service distribution:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف توزيع الخدمة' },
      { status: 500 }
    )
  }
}
