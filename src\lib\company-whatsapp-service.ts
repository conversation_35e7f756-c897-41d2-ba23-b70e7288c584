/**
 * خدمة WhatsApp المخصصة للشركة
 * تستخدم رقم الهاتف المسجل في بيانات الشركة
 */

import { Client, LocalAuth, MessageMedia } from 'whatsapp-web.js'
import { query } from '@/lib/db'
import * as fs from 'fs'
import * as path from 'path'

// أنواع البيانات
interface CompanyWhatsAppConfig {
  companyId: number
  companyName: string
  whatsappPhone: string
  businessName: string
  sessionName: string
  enabled: boolean
  autoReply: boolean
  autoReplyMessage: string
  businessHoursStart: string
  businessHoursEnd: string
}

interface ContactInfo {
  id: number
  name: string
  phone: string
  type: 'client' | 'employee'
  isActive: boolean
}

interface MessageResult {
  success: boolean
  messageId?: string
  error?: string
  timestamp: Date
  recipientPhone: string
}

// فئة خدمة WhatsApp للشركة
export class CompanyWhatsAppService {
  private client: Client | null = null
  private config: CompanyWhatsAppConfig | null = null
  private isReady: boolean = false
  private qrCode: string | null = null
  private connectionStatus: 'disconnected' | 'connecting' | 'connected' | 'ready' = 'disconnected'
  private messageQueue: Array<{ phone: string; message: string; type: string; mediaPath?: string }> = []
  private isProcessingQueue: boolean = false

  constructor() {
    this.loadCompanyConfig()
  }

  // تحميل إعدادات الشركة - متوافق مع النظام الموحد
  private async loadCompanyConfig(): Promise<void> {
    try {
      // جلب معلومات الشركة من headers المرسلة من الخادم الموحد
      const companyHeader = process.env.COMPANY_NAME || 'نظام إدارة المحاماة'
      const databaseHeader = process.env.DATABASE_NAME || 'legal_system'

      const result = await query(`
        SELECT
          id, company_name, whatsapp_phone, whatsapp_business_name,
          whatsapp_enabled, whatsapp_session_name, whatsapp_auto_reply,
          whatsapp_business_hours_start, whatsapp_business_hours_end,
          whatsapp_auto_reply_message
        FROM company_info
        WHERE whatsapp_enabled = true
        LIMIT 1
      `)

      if (result.rows.length > 0) {
        const row = result.rows[0]
        this.config = {
          companyId: row.id,
          companyName: row.company_name,
          whatsappPhone: row.whatsapp_phone,
          businessName: row.whatsapp_business_name || row.company_name,
          sessionName: row.whatsapp_session_name || `company_${row.id}_whatsapp`,
          enabled: row.whatsapp_enabled,
          autoReply: row.whatsapp_auto_reply,
          autoReplyMessage: row.whatsapp_auto_reply_message,
          businessHoursStart: row.whatsapp_business_hours_start,
          businessHoursEnd: row.whatsapp_business_hours_end
        }

        console.log(`📱 تم تحميل إعدادات WhatsApp للشركة: ${this.config.companyName}`)
        console.log(`📞 رقم WhatsApp: ${this.config.whatsappPhone}`)
      } else {
        console.warn('⚠️ لم يتم العثور على إعدادات WhatsApp مفعلة للشركة')
      }
    } catch (error) {
      console.error('خطأ في تحميل إعدادات الشركة:', error)
    }
  }

  // تهيئة عميل WhatsApp
  private async initializeClient(): Promise<void> {
    if (!this.config) {
      throw new Error('إعدادات الشركة غير متوفرة')
    }

    try {
      // إنشاء مجلد الجلسة المخصص للشركة
      const sessionPath = `./whatsapp-sessions/${this.config.sessionName}`

      this.client = new Client({
        authStrategy: new LocalAuth({
          name: this.config.sessionName,
          dataPath: sessionPath
        }),
        puppeteer: {
          headless: true,
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--single-process',
            '--disable-gpu'
          ]
        }
      })

      this.setupEventHandlers()
      console.log(`🔧 تم تهيئة عميل WhatsApp للشركة: ${this.config.companyName}`)
    } catch (error) {
      console.error('خطأ في تهيئة عميل WhatsApp:', error)
      throw error
    }
  }

  // إعداد معالجات الأحداث
  private setupEventHandlers(): void {
    if (!this.client || !this.config) return

    // عند توليد QR Code
    this.client.on('qr', (qr) => {
      console.log(`📱 QR Code جديد للشركة ${this.config!.companyName}`)
      this.qrCode = qr
      this.connectionStatus = 'connecting'

      // حفظ QR Code في قاعدة البيانات للعرض في الواجهة
      this.saveQRCode(qr)
    })

    // عند الاتصال بنجاح
    this.client.on('ready', async () => {
      console.log(`✅ WhatsApp جاهز للشركة: ${this.config!.companyName}`)
      this.isReady = true
      this.connectionStatus = 'ready'
      this.qrCode = null

      // تحديث حالة الاتصال في قاعدة البيانات
      await this.updateConnectionStatus('connected')

      // بدء معالجة قائمة الانتظار
      this.startQueueProcessor()
    })

    // عند قطع الاتصال
    this.client.on('disconnected', async (reason) => {
      console.log(`❌ تم قطع اتصال WhatsApp للشركة ${this.config!.companyName}:`, reason)
      this.isReady = false
      this.connectionStatus = 'disconnected'

      await this.updateConnectionStatus('disconnected', reason)
    })

    // عند استقبال رسالة
    this.client.on('message', async (message) => {
      await this.handleIncomingMessage(message)
    })

    // معالجة الأخطاء
    this.client.on('auth_failure', async (message) => {
      console.error(`❌ فشل مصادقة WhatsApp للشركة ${this.config!.companyName}:`, message)
      this.connectionStatus = 'disconnected'

      await this.updateConnectionStatus('auth_failed', message)
    })
  }

  // بدء تشغيل الخدمة
  async start(): Promise<void> {
    try {
      if (!this.config) {
        await this.loadCompanyConfig()
        if (!this.config) {
          throw new Error('لم يتم العثور على إعدادات WhatsApp للشركة')
        }
      }

      if (!this.config.enabled) {
        throw new Error('خدمة WhatsApp غير مفعلة للشركة')
      }

      console.log(`🔄 بدء تشغيل WhatsApp للشركة: ${this.config.companyName}`)

      await this.initializeClient()
      await this.client!.initialize()

    } catch (error) {
      console.error('خطأ في بدء تشغيل WhatsApp:', error)
      throw error
    }
  }

  // إيقاف الخدمة
  async stop(): Promise<void> {
    try {
      if (this.client) {
        await this.client.destroy()
        this.client = null
        this.isReady = false
        this.connectionStatus = 'disconnected'

        if (this.config) {
          await this.updateConnectionStatus('stopped')
          console.log(`🛑 تم إيقاف WhatsApp للشركة: ${this.config.companyName}`)
        }
      }
    } catch (error) {
      console.error('خطأ في إيقاف WhatsApp:', error)
    }
  }

  // الحصول على حالة الاتصال
  getStatus(): {
    isReady: boolean
    status: string
    qrCode: string | null
    companyName: string | null
    whatsappPhone: string | null
  } {
    return {
      isReady: this.isReady,
      status: this.connectionStatus,
      qrCode: this.qrCode,
      companyName: this.config?.companyName || null,
      whatsappPhone: this.config?.whatsappPhone || null
    }
  }

  // تنسيق رقم الهاتف اليمني
  private formatYemenPhone(phone: string): string {
    let cleanPhone = phone.replace(/[\s\-\(\)]/g, '')

    if (cleanPhone.startsWith('0')) {
      cleanPhone = cleanPhone.substring(1)
    }

    if (!cleanPhone.startsWith('967')) {
      cleanPhone = '967' + cleanPhone
    }

    return '+' + cleanPhone
  }

  // إرسال رسالة نصية
  async sendTextMessage(contactPhone: string, message: string, recipientName?: string): Promise<MessageResult> {
    try {
      if (!this.isReady || !this.client || !this.config) {
        // إضافة إلى قائمة الانتظار إذا لم يكن جاهز
        this.messageQueue.push({ phone: contactPhone, message, type: 'text' })
        throw new Error('WhatsApp غير جاهز - تم إضافة الرسالة لقائمة الانتظار')
      }

      const formattedPhone = this.formatYemenPhone(contactPhone)
      const chatId = formattedPhone.replace('+', '') + '@c.us'

      // التحقق من الحد اليومي
      const canSend = await this.checkDailyLimit()
      if (!canSend) {
        throw new Error('تم تجاوز الحد الأقصى للرسائل اليومية')
      }

      // التحقق من وجود الرقم على WhatsApp
      const isRegistered = await this.client.isRegisteredUser(chatId)
      if (!isRegistered) {
        throw new Error('الرقم غير مسجل على WhatsApp')
      }

      // إرسال الرسالة
      const sentMessage = await this.client.sendMessage(chatId, message)

      // حفظ سجل الرسالة
      await this.saveMessageLog({
        phone: formattedPhone,
        recipientName,
        message,
        type: 'text',
        status: 'sent',
        messageId: sentMessage.id.id
      })

      return {
        success: true,
        messageId: sentMessage.id.id,
        timestamp: new Date(),
        recipientPhone: formattedPhone
      }

    } catch (error) {
      console.error('خطأ في إرسال الرسالة:', error)

      await this.saveMessageLog({
        phone: contactPhone,
        recipientName,
        message,
        type: 'text',
        status: 'failed',
        error: error instanceof Error ? error.message : 'خطأ غير معروف'
      })

      return {
        success: false,
        error: error instanceof Error ? error.message : 'خطأ غير معروف',
        timestamp: new Date(),
        recipientPhone: contactPhone
      }
    }
  }

  // إرسال صورة السند
  async sendVoucherImage(contactPhone: string, imagePath: string, voucherInfo: string, recipientName?: string): Promise<MessageResult> {
    try {
      if (!this.isReady || !this.client || !this.config) {
        this.messageQueue.push({ phone: contactPhone, message: voucherInfo, type: 'image', mediaPath: imagePath })
        throw new Error('WhatsApp غير جاهز - تم إضافة الرسالة لقائمة الانتظار')
      }

      const formattedPhone = this.formatYemenPhone(contactPhone)
      const chatId = formattedPhone.replace('+', '') + '@c.us'

      // التحقق من وجود الملف
      if (!fs.existsSync(imagePath)) {
        throw new Error('ملف الصورة غير موجود')
      }

      // إنشاء كائن الوسائط
      const media = MessageMedia.fromFilePath(imagePath)

      // تحضير نص مرفق مع الصورة
      const caption = `🧾 ${voucherInfo}\n\n📞 ${this.config.businessName}\n${this.config.whatsappPhone}\n\nشكراً لتعاملكم معنا`

      // إرسال الصورة
      const sentMessage = await this.client.sendMessage(chatId, media, { caption })

      // حفظ سجل الرسالة
      await this.saveMessageLog({
        phone: formattedPhone,
        recipientName,
        message: caption,
        type: 'image',
        status: 'sent',
        messageId: sentMessage.id.id,
        mediaPath: imagePath
      })

      return {
        success: true,
        messageId: sentMessage.id.id,
        timestamp: new Date(),
        recipientPhone: formattedPhone
      }

    } catch (error) {
      console.error('خطأ في إرسال صورة السند:', error)

      await this.saveMessageLog({
        phone: contactPhone,
        recipientName,
        message: voucherInfo,
        type: 'image',
        status: 'failed',
        error: error instanceof Error ? error.message : 'خطأ غير معروف',
        mediaPath: imagePath
      })

      return {
        success: false,
        error: error instanceof Error ? error.message : 'خطأ غير معروف',
        timestamp: new Date(),
        recipientPhone: contactPhone
      }
    }
  }

  // جلب جهات الاتصال من قاعدة البيانات
  async getCompanyContacts(type?: 'client' | 'employee'): Promise<ContactInfo[]> {
    try {
      let queryText = ''

      if (type === 'client' || !type) {
        queryText = `
          SELECT
            c.id,
            c.name,
            c.phone,
            'client' as type,
            c.is_active
          FROM clients c
          WHERE c.phone IS NOT NULL AND c.phone != '' AND c.is_active = true
        `
      }

      if (type === 'employee' || !type) {
        if (queryText) queryText += ' UNION ALL '
        queryText += `
          SELECT
            e.id,
            e.name,
            e.phone,
            'employee' as type,
            e.is_active
          FROM employees e
          WHERE e.phone IS NOT NULL AND e.phone != '' AND e.is_active = true
        `
      }

      queryText += ` ORDER BY name`

      const result = await query(queryText)

      return result.rows.map(row => ({
        id: row.id,
        name: row.name,
        phone: this.formatYemenPhone(row.phone),
        type: row.type,
        isActive: row.is_active
      }))

    } catch (error) {
      console.error('خطأ في جلب جهات الاتصال:', error)
      return []
    }
  }

  // التحقق من الحد اليومي للرسائل
  private async checkDailyLimit(): Promise<boolean> {
    try {
      if (!this.config) return false

      const result = await query(`
        SELECT COALESCE(messages_sent, 0) as sent_today
        FROM whatsapp_daily_stats
        WHERE company_id = $1 AND stat_date = CURRENT_DATE
      `, [this.config.companyId])

      const sentToday = result.rows[0]?.sent_today || 0

      // جلب الحد الأقصى من الإعدادات
      const limitResult = await query(`
        SELECT setting_value::integer as max_limit
        FROM whatsapp_company_settings
        WHERE company_id = $1 AND setting_key = 'max_daily_messages'
      `, [this.config.companyId])

      const maxLimit = limitResult.rows[0]?.max_limit || 500

      return sentToday < maxLimit
    } catch (error) {
      console.error('خطأ في فحص الحد اليومي:', error)
      return true // السماح بالإرسال في حالة الخطأ
    }
  }

  // حفظ سجل الرسائل
  private async saveMessageLog(logData: {
    phone: string
    recipientName?: string
    message: string
    type: string
    status: string
    messageId?: string
    error?: string
    mediaPath?: string
  }): Promise<void> {
    try {
      if (!this.config) return

      await query(`
        INSERT INTO whatsapp_local_messages (
          company_id, phone_number, recipient_name, message_content,
          message_type, media_path, status, whatsapp_message_id,
          error_message, sent_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      `, [
        this.config.companyId,
        logData.phone,
        logData.recipientName || null,
        logData.message,
        logData.type,
        logData.mediaPath || null,
        logData.status,
        logData.messageId || null,
        logData.error || null,
        logData.status === 'sent' ? new Date() : null
      ])
    } catch (error) {
      console.error('خطأ في حفظ سجل الرسالة:', error)
    }
  }

  // حفظ QR Code
  private async saveQRCode(qrCode: string): Promise<void> {
    try {
      if (!this.config) return

      await query(`
        INSERT INTO whatsapp_company_settings (company_id, setting_key, setting_value, setting_type)
        VALUES ($1, 'current_qr_code', $2, 'string')
        ON CONFLICT (company_id, setting_key)
        DO UPDATE SET setting_value = $2, updated_at = CURRENT_TIMESTAMP
      `, [this.config.companyId, qrCode])
    } catch (error) {
      console.error('خطأ في حفظ QR Code:', error)
    }
  }

  // تحديث حالة الاتصال
  private async updateConnectionStatus(status: string, details?: string): Promise<void> {
    try {
      if (!this.config) return

      await query(`
        INSERT INTO whatsapp_company_settings (company_id, setting_key, setting_value, setting_type)
        VALUES ($1, 'connection_status', $2, 'string')
        ON CONFLICT (company_id, setting_key)
        DO UPDATE SET setting_value = $2, updated_at = CURRENT_TIMESTAMP
      `, [this.config.companyId, JSON.stringify({ status, details, timestamp: new Date() })])
    } catch (error) {
      console.error('خطأ في تحديث حالة الاتصال:', error)
    }
  }

  // معالجة الرسائل الواردة
  private async handleIncomingMessage(message: any): Promise<void> {
    try {
      if (!this.config) return

      const contact = await message.getContact()
      const chat = await message.getChat()

      console.log(`📨 رسالة واردة للشركة ${this.config.companyName} من ${contact.name || contact.number}: ${message.body}`)

      // حفظ الرسالة الواردة
      await query(`
        INSERT INTO whatsapp_incoming_messages (
          company_id, phone_number, sender_name, message_content,
          message_type, received_at
        ) VALUES ($1, $2, $3, $4, $5, $6)
      `, [
        this.config.companyId,
        contact.number,
        contact.name || 'غير معروف',
        message.body,
        message.type,
        new Date()
      ])

      // الرد التلقائي إذا كان مفعل
      if (this.config.autoReply && this.isBusinessHours()) {
        const autoReplyText = this.config.autoReplyMessage.replace('{{company_name}}', this.config.businessName)

        // تأخير بسيط قبل الرد
        setTimeout(async () => {
          try {
            await message.reply(autoReplyText)
          } catch (error) {
            console.error('خطأ في الرد التلقائي:', error)
          }
        }, 2000)
      }

    } catch (error) {
      console.error('خطأ في معالجة الرسالة الواردة:', error)
    }
  }

  // فحص ساعات العمل
  private isBusinessHours(): boolean {
    if (!this.config) return false

    const now = new Date()
    const currentTime = now.getHours() * 60 + now.getMinutes()

    const startTime = this.parseTime(this.config.businessHoursStart)
    const endTime = this.parseTime(this.config.businessHoursEnd)

    return currentTime >= startTime && currentTime <= endTime
  }

  // تحليل الوقت
  private parseTime(timeString: string): number {
    const [hours, minutes] = timeString.split(':').map(Number)
    return hours * 60 + minutes
  }

  // معالج قائمة الانتظار
  private async startQueueProcessor(): Promise<void> {
    if (this.isProcessingQueue) return

    this.isProcessingQueue = true

    while (this.messageQueue.length > 0 && this.isReady) {
      const message = this.messageQueue.shift()
      if (!message) continue

      try {
        if (message.type === 'text') {
          await this.sendTextMessage(message.phone, message.message)
        } else if (message.type === 'image' && message.mediaPath) {
          await this.sendVoucherImage(message.phone, message.mediaPath, message.message)
        }

        // تأخير بين الرسائل
        await new Promise(resolve => setTimeout(resolve, 2000))
      } catch (error) {
        console.error('خطأ في معالجة رسالة من قائمة الانتظار:', error)
      }
    }

    this.isProcessingQueue = false
  }
}

// إنشاء مثيل واحد من الخدمة
export const companyWhatsAppService = new CompanyWhatsAppService()

// دوال مساعدة للاستخدام السريع
export async function sendCompanyWhatsApp(phone: string, message: string, recipientName?: string): Promise<MessageResult> {
  return await companyWhatsAppService.sendTextMessage(phone, message, recipientName)
}

export async function sendCompanyVoucher(phone: string, imagePath: string, voucherInfo: string, recipientName?: string): Promise<MessageResult> {
  return await companyWhatsAppService.sendVoucherImage(phone, imagePath, voucherInfo, recipientName)
}

export async function getCompanyWhatsAppStatus() {
  return companyWhatsAppService.getStatus()
}
