'use client'

import { useState, useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { ChevronDown, Search, BookOpen, User, Users } from 'lucide-react'

interface Account {
  id: number
  name: string
  code?: string
  type: 'client' | 'employee' | 'financial'
  account_type?: string
  phone?: string
  email?: string
  national_id?: string
}

interface AccountSelectProps {
  value: string
  onChange: (accountId: string, accountData: Account | null) => void
  label?: string
  placeholder?: string
  required?: boolean
  includeClients?: boolean
  includeEmployees?: boolean
  includeFinancial?: boolean
}

export function AccountSelect({ 
  value, 
  onChange, 
  label = "الحساب", 
  placeholder = "اختر الحساب", 
  required = false,
  includeClients = true,
  includeEmployees = true,
  includeFinancial = true
}: AccountSelectProps) {
  const [accounts, setAccounts] = useState<Account[]>([])
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedAccount, setSelectedAccount] = useState<Account | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  const fetchAccounts = async () => {
    setIsLoading(true)
    try {
      const allAccounts: Account[] = []

      // جلب الموكلين
      if (includeClients) {
        try {
          const clientsResponse = await fetch('/api/clients')
          const clientsResult = await clientsResponse.json()
          if (clientsResult.success) {
            const clientAccounts = clientsResult.data.map((client: any) => ({
              id: `client_${client.id}`,
              name: client.name,
              type: 'client' as const,
              phone: client.phone,
              email: client.email,
              national_id: client.national_id
            }))
            allAccounts.push(...clientAccounts)
          }
        } catch (error) {
          console.error('Error fetching clients:', error)
        }
      }

      // جلب الموظفين
      if (includeEmployees) {
        try {
          const employeesResponse = await fetch('/api/employees')
          const employeesResult = await employeesResponse.json()
          if (employeesResult.success) {
            const employeeAccounts = employeesResult.data.map((employee: any) => ({
              id: `employee_${employee.id}`,
              name: employee.name,
              type: 'employee' as const,
              phone: employee.phone,
              email: employee.email,
              national_id: employee.national_id
            }))
            allAccounts.push(...employeeAccounts)
          }
        } catch (error) {
          console.error('Error fetching employees:', error)
        }
      }

      // جلب الحسابات المالية
      if (includeFinancial) {
        try {
          // محاكاة حسابات مالية
          const financialAccounts = [
            { id: 'fin_1001001', name: 'النقدية في الصندوق', code: '1001001', type: 'financial' as const, account_type: 'أصول' },
            { id: 'fin_1001002', name: 'البنك - الحساب الجاري', code: '1001002', type: 'financial' as const, account_type: 'أصول' },
            { id: 'fin_1101001', name: 'العملاء', code: '1101001', type: 'financial' as const, account_type: 'أصول' },
            { id: 'fin_2001001', name: 'الموردون', code: '2001001', type: 'financial' as const, account_type: 'خصوم' },
            { id: 'fin_3001', name: 'رأس المال', code: '3001', type: 'financial' as const, account_type: 'حقوق ملكية' },
            { id: 'fin_4001', name: 'إيرادات الخدمات القانونية', code: '4001', type: 'financial' as const, account_type: 'إيرادات' },
            { id: 'fin_5001', name: 'مصروفات الرواتب', code: '5001', type: 'financial' as const, account_type: 'مصروفات' },
            { id: 'fin_5002', name: 'مصروفات الإيجار', code: '5002', type: 'financial' as const, account_type: 'مصروفات' },
            { id: 'fin_5003', name: 'مصروفات الكهرباء', code: '5003', type: 'financial' as const, account_type: 'مصروفات' },
            { id: 'fin_5004', name: 'مصروفات الاتصالات', code: '5004', type: 'financial' as const, account_type: 'مصروفات' }
          ]
          allAccounts.push(...financialAccounts)
        } catch (error) {
          console.error('Error loading financial accounts:', error)
        }
      }

      setAccounts(allAccounts)
    } catch (error) {
      console.error('Error fetching accounts:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchAccounts()
  }, [includeClients, includeEmployees, includeFinancial])

  useEffect(() => {
    if (value && accounts.length > 0) {
      const account = accounts.find(a => a.id.toString() === value)
      setSelectedAccount(account || null)
    }
  }, [value, accounts])

  const filteredAccounts = accounts.filter(account =>
    account.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (account.code && account.code.includes(searchTerm)) ||
    (account.phone && account.phone.includes(searchTerm)) ||
    (account.national_id && account.national_id.includes(searchTerm))
  )

  const handleSelect = (account: Account) => {
    setSelectedAccount(account)
    onChange(account.id.toString(), account)
    setIsOpen(false)
    setSearchTerm('')
  }

  const handleClear = () => {
    setSelectedAccount(null)
    onChange('', null)
    setSearchTerm('')
  }

  const getAccountIcon = (type: string) => {
    switch (type) {
      case 'client': return <User className="h-4 w-4 text-blue-500" />
      case 'employee': return <Users className="h-4 w-4 text-green-500" />
      case 'financial': return <BookOpen className="h-4 w-4 text-purple-500" />
      default: return <BookOpen className="h-4 w-4 text-gray-400" />
    }
  }

  const getAccountTypeText = (type: string) => {
    switch (type) {
      case 'client': return 'موكل'
      case 'employee': return 'موظف'
      case 'financial': return 'حساب مالي'
      default: return 'حساب'
    }
  }

  const getAccountTypeColor = (type: string) => {
    switch (type) {
      case 'client': return 'bg-teal-100 text-teal-800'
      case 'employee': return 'bg-green-100 text-green-800'
      case 'financial': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="relative">
      <Label className="block text-sm font-medium text-gray-700 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </Label>
      
      <div className="relative">
        <div
          className="w-full px-3 py-2 border border-gray-300 rounded-md cursor-pointer bg-white flex items-center justify-between"
          onClick={() => setIsOpen(!isOpen)}
        >
          <div className="flex items-center">
            {selectedAccount ? getAccountIcon(selectedAccount.type) : <BookOpen className="h-4 w-4 mr-2 text-gray-400" />}
            <span className={selectedAccount ? 'text-gray-900 mr-2' : 'text-gray-500 mr-2'}>
              {selectedAccount ? selectedAccount.name : placeholder}
            </span>
            {selectedAccount && (
              <Badge className={getAccountTypeColor(selectedAccount.type)} size="sm">
                {getAccountTypeText(selectedAccount.type)}
              </Badge>
            )}
          </div>
          <ChevronDown className={`h-4 w-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </div>

        {isOpen && (
          <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-80 overflow-hidden">
            {/* شريط البحث */}
            <div className="p-2 border-b">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في الحسابات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                  autoFocus
                />
              </div>
            </div>

            {/* قائمة الحسابات */}
            <div className="max-h-64 overflow-y-auto">
              {isLoading ? (
                <div className="p-3 text-center text-gray-500">جاري التحميل...</div>
              ) : filteredAccounts.length > 0 ? (
                <>
                  {selectedAccount && (
                    <div
                      className="p-2 hover:bg-gray-100 cursor-pointer border-b text-red-600"
                      onClick={handleClear}
                    >
                      <div className="flex items-center">
                        <span className="text-sm">✕ إلغاء الاختيار</span>
                      </div>
                    </div>
                  )}
                  {filteredAccounts.map((account) => (
                    <div
                      key={account.id}
                      className="p-3 hover:bg-gray-100 cursor-pointer border-b last:border-b-0"
                      onClick={() => handleSelect(account)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center">
                            {getAccountIcon(account.type)}
                            <span className="font-medium text-gray-900 mr-2">{account.name}</span>
                            <Badge className={getAccountTypeColor(account.type)} size="sm">
                              {getAccountTypeText(account.type)}
                            </Badge>
                          </div>
                          <div className="text-sm text-gray-500 mt-1">
                            {account.code && <span>الرمز: {account.code}</span>}
                            {account.phone && <span> • الهاتف: {account.phone}</span>}
                            {account.account_type && <span> • النوع: {account.account_type}</span>}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </>
              ) : (
                <div className="p-3 text-center text-gray-500">
                  {searchTerm ? 'لا توجد نتائج للبحث' : 'لا توجد حسابات'}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* عرض تفاصيل الحساب المختار */}
      {selectedAccount && (
        <div className="mt-2 p-3 bg-gray-50 border border-gray-200 rounded-md">
          <div className="text-sm text-gray-700">
            <div className="grid grid-cols-2 gap-2">
              {selectedAccount.code && <div><strong>الرمز:</strong> {selectedAccount.code}</div>}
              {selectedAccount.phone && <div><strong>الهاتف:</strong> {selectedAccount.phone}</div>}
              {selectedAccount.email && <div><strong>البريد:</strong> {selectedAccount.email}</div>}
              {selectedAccount.national_id && <div><strong>الهوية:</strong> {selectedAccount.national_id}</div>}
              {selectedAccount.account_type && <div><strong>نوع الحساب:</strong> {selectedAccount.account_type}</div>}
            </div>
          </div>
        </div>
      )}

      {/* حقل مخفي للقيمة */}
      <input type="hidden" value={value} name="account_id" />
    </div>
  )
}
