import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع الحركات من قاعدة البيانات
export async function GET() {
  try {
    const result = await query('SELECT * FROM movements ORDER BY id')
    
    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching الحركات:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'فشل في جلب بيانات الحركات',
        message: 'تأكد من وجود الجدول في قاعدة البيانات'
      },
      { status: 500 }
    )
  }
}

// POST - إضافة الحركات جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // هنا يجب إضافة منطق الإدراج حسب كل جدول
    // سيتم تحديثه لاحقاً حسب هيكل كل جدول
    
    return NextResponse.json({
      success: true,
      message: 'تم إضافة الحركات بنجاح'
    })
  } catch (error) {
    console.error('Error creating الحركات:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة الحركات' },
      { status: 500 }
    )
  }
}

// PUT - تحديث الحركات
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    
    // هنا يجب إضافة منطق التحديث حسب كل جدول
    // سيتم تحديثه لاحقاً حسب هيكل كل جدول
    
    return NextResponse.json({
      success: true,
      message: 'تم تحديث الحركات بنجاح'
    })
  } catch (error) {
    console.error('Error updating الحركات:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الحركات' },
      { status: 500 }
    )
  }
}

// DELETE - حذف الحركات
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الحركات مطلوب' },
        { status: 400 }
      )
    }

    await query('DELETE FROM movements WHERE id = $1', [id])

    return NextResponse.json({
      success: true,
      message: 'تم حذف الحركات بنجاح'
    })
  } catch (error) {
    console.error('Error deleting الحركات:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الحركات' },
      { status: 500 }
    )
  }
}