'use client'

import { useState, useEffect, useRef } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { ChevronDown, Search, FileText } from 'lucide-react'

interface Issue {
  id: number
  case_number: string
  title: string
  client_name: string
  court_name: string
  status: string
  issue_type: string
  case_amount: number
  amount?: number // للتوافق مع النسخة القديمة
  contract_method: string
}

interface IssueSelectProps {
  value: string
  onChange: (issueId: string, issueData: Issue | null) => void
  label?: string
  placeholder?: string
  required?: boolean
  excludeDistributed?: boolean // خيار لاستبعاد القضايا الموزعة
}

export function IssueSelect({ value, onChange, label = "القضية", placeholder = "اختر القضية", required = false, excludeDistributed = false }: IssueSelectProps) {
  const [issues, setIssues] = useState<Issue[]>([])
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedIssue, setSelectedIssue] = useState<Issue | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const isMountedRef = useRef(true)

  const fetchIssues = async () => {
    if (!isMountedRef.current) return

    setIsLoading(true)
    try {
      // استخدام معامل undistributed لجلب القضايا غير الموزعة
      const apiUrl = excludeDistributed ? '/api/issues?undistributed=true' : '/api/issues'
      const separator = apiUrl.includes('?') ? '&' : '?'
      const urlWithTimestamp = `${apiUrl}${separator}t=${Date.now()}` // منع cache

      console.log('IssueSelect: Fetching issues with URL:', urlWithTimestamp)
      console.log('IssueSelect: excludeDistributed =', excludeDistributed)

      const response = await fetch(urlWithTimestamp, {
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      if (!isMountedRef.current) return

      if (result.success && Array.isArray(result.data)) {
        console.log('IssueSelect: Received issues count:', result.data.length)
        console.log('IssueSelect: Full API response:', result)
        if (excludeDistributed) {
          console.log('IssueSelect: Undistributed issues:', result.data.map(i => `${i.id}: ${i.case_number || 'بدون رقم'} - ${i.title || 'بدون عنوان'}`))
        }
        setIssues(result.data)

        // تسجيل إضافي للتشخيص
        if (result.data.length === 0) {
          console.warn('⚠️ IssueSelect: لا توجد قضايا متاحة!')
          if (excludeDistributed) {
            console.warn('⚠️ IssueSelect: جميع القضايا موزعة أو لا توجد قضايا')
          }
        }
      } else {
        console.error('IssueSelect: API error:', result.error || 'البيانات غير صحيحة')
        console.error('IssueSelect: Full error response:', result)
        setIssues([])
      }
    } catch (error) {
      console.error('Error fetching issues:', error)
      if (isMountedRef.current) {
        setIssues([])
      }
    } finally {
      if (isMountedRef.current) {
        setIsLoading(false)
      }
    }
  }

  useEffect(() => {
    console.log('🔄 IssueSelect: excludeDistributed changed to:', excludeDistributed)
    console.log('🔄 IssueSelect: Component mounted, fetching issues...')
    fetchIssues()

    return () => {
      isMountedRef.current = false
    }
  }, [excludeDistributed])

  // إعادة جلب البيانات عند فتح القائمة
  useEffect(() => {
    if (isOpen) {
      console.log('📋 IssueSelect: Dropdown opened, refetching issues')
      console.log('📋 IssueSelect: excludeDistributed =', excludeDistributed)
      fetchIssues()
    }
  }, [isOpen, excludeDistributed])

  // إعادة تعيين القضية المختارة عند تغيير المعامل
  useEffect(() => {
    if (excludeDistributed && selectedIssue && issues.length > 0) {
      // التحقق من أن القضية المختارة لا تزال في القائمة الجديدة
      const isStillValid = issues.some(issue => issue.id === selectedIssue.id)
      if (!isStillValid) {
        console.log('IssueSelect: Selected issue is no longer valid, clearing selection')
        setSelectedIssue(null)
        onChange('', null)
      }
    }
  }, [issues, excludeDistributed, selectedIssue, onChange])

  useEffect(() => {
    if (value && Array.isArray(issues) && issues.length > 0) {
      const issue = issues.find(i => i && i.id && i.id.toString() === value)
      if (issue) {
        setSelectedIssue(issue)
      } else if (!value) {
        setSelectedIssue(null)
      }
    } else if (!value) {
      setSelectedIssue(null)
    }
  }, [value, issues])

  const filteredIssues = Array.isArray(issues) ? issues.filter(issue => {
    if (!issue || typeof issue !== 'object') return false
    return (
      (issue.case_number || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (issue.title || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (issue.client_name || '').toLowerCase().includes(searchTerm.toLowerCase())
    )
  }) : []



  const handleSelect = (issue: Issue) => {
    setSelectedIssue(issue)
    onChange(issue.id.toString(), issue)
    setIsOpen(false)
    setSearchTerm('')
  }

  const handleClear = () => {
    setSelectedIssue(null)
    onChange('', null)
    setSearchTerm('')
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'in_progress': return 'bg-blue-100 text-blue-800'
      case 'completed': return 'bg-green-100 text-green-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'معلقة'
      case 'in_progress': return 'قيد المعالجة'
      case 'completed': return 'مكتملة'
      case 'cancelled': return 'ملغية'
      default: return 'غير محدد'
    }
  }

  return (
    <div className="relative">
      <Label className="block text-sm font-medium text-gray-700 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </Label>

      <div className="relative">
        <div
          className="w-full px-3 py-2 border border-gray-300 rounded-md cursor-pointer bg-white flex items-center justify-between"
          onClick={() => {

            setIsOpen(!isOpen)
          }}
        >
          <div className="flex items-center">
            <FileText className="h-4 w-4 mr-2 text-gray-400" />
            <span className={selectedIssue ? 'text-gray-900' : 'text-gray-500'}>
              {selectedIssue ? `${selectedIssue.case_number} - ${selectedIssue.title}` : placeholder}
            </span>
          </div>
          <ChevronDown className={`h-4 w-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </div>

        {isOpen && (
          <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-xl max-h-96 overflow-hidden">
            {/* شريط البحث */}
            <div className="p-3 border-b bg-gray-50">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في القضايا..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10 text-sm h-8 border-gray-200 focus:border-blue-500"
                  autoFocus
                />
              </div>
            </div>

            {/* قائمة القضايا */}
            <div className="max-h-80 overflow-y-auto">
              {isLoading ? (
                <div className="p-4 text-center text-gray-500 text-sm">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mx-auto mb-2"></div>
                  جاري التحميل...
                </div>
              ) : filteredIssues.length > 0 ? (
                <>
                  {selectedIssue && (
                    <div
                      className="p-3 hover:bg-red-50 cursor-pointer border-b text-red-600 transition-colors"
                      onClick={handleClear}
                    >
                      <div className="flex items-center">
                        <span className="text-sm font-medium">✕ إلغاء الاختيار</span>
                      </div>
                    </div>
                  )}
                  {filteredIssues.map((issue) => (
                    <div
                      key={issue.id}
                      className="p-4 hover:bg-blue-50 cursor-pointer border-b last:border-b-0 transition-colors"
                      onClick={() => handleSelect(issue)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center">
                            <FileText className="h-4 w-4 mr-2 text-gray-400" />
                            <span className="font-medium text-gray-900">{issue.case_number}</span>
                            <Badge className={`${getStatusColor(issue.status)} mr-2 text-xs`}>
                              {getStatusText(issue.status)}
                            </Badge>
                          </div>
                          <div className="text-sm text-gray-600 mt-1">{issue.title}</div>
                          <div className="text-sm text-gray-500 mt-1">
                            الموكل: {issue.client_name || 'غير محدد'} • المحكمة: {issue.court_name || 'غير محدد'}
                            {issue.case_amount > 0 && ` • القيمة: ${parseFloat(issue.case_amount).toLocaleString()} ريال`}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </>
              ) : (
                <div className="p-3 text-center text-gray-500">
                  {searchTerm ? 'لا توجد نتائج للبحث' : 'لا توجد قضايا'}
                </div>
              )}
            </div>
          </div>
        )}
      </div>



      {/* حقل مخفي للقيمة */}
      <input type="hidden" value={value} name="issue_id" />
    </div>
  )
}
