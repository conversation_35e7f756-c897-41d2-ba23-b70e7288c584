import { NextRequest, NextResponse } from 'next/server'
import { query, getDatabaseName } from '@/lib/database-router'

// GET - جلب جميع الإعلانات العامة
export async function GET() {
  try {

    const result = await query(`
      SELECT id, title, content, type, is_active, created_date
      FROM public_announcements 
      ORDER BY created_date DESC
    `)

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching public announcements:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الإعلانات' },
      { status: 500 }
    )
  }
}