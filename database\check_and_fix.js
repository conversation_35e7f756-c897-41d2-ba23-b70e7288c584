// فحص وإصلاح بنية الجداول
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function checkAndFix() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔄 جاري الاتصال بقاعدة البيانات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // فحص بنية جدول lineages
    console.log('🔍 فحص بنية جدول lineages...');
    const lineagesColumns = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'lineages' AND table_schema = 'public'
      ORDER BY ordinal_position
    `);
    
    console.log('📋 أعمدة جدول lineages الحالية:');
    lineagesColumns.rows.forEach(col => {
      console.log(`   - ${col.column_name}: ${col.data_type}`);
    });

    // إضافة الأعمدة المفقودة لجدول lineages
    console.log('🔧 إضافة الأعمدة المفقودة لجدول lineages...');
    const lineageAlters = [
      'ALTER TABLE lineages ADD COLUMN IF NOT EXISTS description TEXT',
      'ALTER TABLE lineages ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true',
      'ALTER TABLE lineages ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
    ];

    for (const sql of lineageAlters) {
      try {
        await client.query(sql);
        console.log(`   ✅ ${sql.split(' ')[5]} تم إضافة العمود`);
      } catch (error) {
        console.log(`   ⚠️ ${error.message}`);
      }
    }

    // فحص بنية جدول services
    console.log('🔍 فحص بنية جدول services...');
    const servicesColumns = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'services' AND table_schema = 'public'
      ORDER BY ordinal_position
    `);
    
    console.log('📋 أعمدة جدول services الحالية:');
    servicesColumns.rows.forEach(col => {
      console.log(`   - ${col.column_name}: ${col.data_type}`);
    });

    // إضافة الأعمدة المفقودة لجدول services
    console.log('🔧 إضافة الأعمدة المفقودة لجدول services...');
    const serviceAlters = [
      'ALTER TABLE services ADD COLUMN IF NOT EXISTS description TEXT',
      'ALTER TABLE services ADD COLUMN IF NOT EXISTS price DECIMAL(10,2) DEFAULT 0',
      'ALTER TABLE services ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true',
      'ALTER TABLE services ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
    ];

    for (const sql of serviceAlters) {
      try {
        await client.query(sql);
        console.log(`   ✅ ${sql.split(' ')[5]} تم إضافة العمود`);
      } catch (error) {
        console.log(`   ⚠️ ${error.message}`);
      }
    }

    // إدراج بيانات تجريبية للنسب المالية
    console.log('🔄 جاري إدراج بيانات النسب المالية...');
    const lineagesData = [
      { name: 'نسبة الإدارة', admin_percentage: 30.00, description: 'نسبة الإدارة من الأتعاب' },
      { name: 'نسبة المحكمة', admin_percentage: 20.00, description: 'نسبة رسوم المحكمة' },
      { name: 'نسبة العمولة', admin_percentage: 15.00, description: 'نسبة العمولة للوسطاء' },
      { name: 'نسبة أخرى', admin_percentage: 10.00, description: 'نسب أخرى متنوعة' }
    ];

    for (const lineage of lineagesData) {
      try {
        await client.query(`
          INSERT INTO lineages (name, admin_percentage, description)
          SELECT $1, $2, $3
          WHERE NOT EXISTS (SELECT 1 FROM lineages WHERE name = $1)
        `, [lineage.name, lineage.admin_percentage, lineage.description]);
        console.log(`   ✅ تم إدراج: ${lineage.name}`);
      } catch (error) {
        console.log(`   ⚠️ خطأ في إدراج ${lineage.name}: ${error.message}`);
      }
    }

    // إدراج بيانات تجريبية للخدمات
    console.log('🔄 جاري إدراج بيانات الخدمات...');
    const servicesData = [
      { name: 'استشارة قانونية', description: 'استشارة قانونية عامة', price: 500.00 },
      { name: 'صياغة عقد', description: 'صياغة العقود القانونية', price: 1000.00 },
      { name: 'تمثيل قضائي', description: 'التمثيل أمام المحاكم', price: 2000.00 },
      { name: 'مراجعة قانونية', description: 'مراجعة الوثائق القانونية', price: 750.00 }
    ];

    for (const service of servicesData) {
      try {
        await client.query(`
          INSERT INTO services (name, description, price)
          SELECT $1, $2, $3
          WHERE NOT EXISTS (SELECT 1 FROM services WHERE name = $1)
        `, [service.name, service.description, service.price]);
        console.log(`   ✅ تم إدراج: ${service.name}`);
      } catch (error) {
        console.log(`   ⚠️ خطأ في إدراج ${service.name}: ${error.message}`);
      }
    }

    // التحقق من النتائج النهائية
    console.log('🔄 جاري التحقق من النتائج النهائية...');
    
    const results = await Promise.all([
      client.query('SELECT COUNT(*) FROM lineages'),
      client.query('SELECT COUNT(*) FROM services'),
      client.query('SELECT COUNT(*) FROM case_distribution'),
      client.query('SELECT COUNT(*) FROM service_distributions')
    ]);

    console.log('📊 ملخص الجداول النهائي:');
    console.log(`   - النسب المالية: ${results[0].rows[0].count} سجل`);
    console.log(`   - الخدمات: ${results[1].rows[0].count} سجل`);
    console.log(`   - توزيع القضايا: ${results[2].rows[0].count} سجل`);
    console.log(`   - تفاصيل توزيع الخدمات: ${results[3].rows[0].count} سجل`);

    console.log('✅ تم إصلاح وتحديث قاعدة البيانات بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في إصلاح قاعدة البيانات:', error.message);
  } finally {
    await client.end();
    console.log('🔄 تم قطع الاتصال بقاعدة البيانات');
  }
}

checkAndFix();
