# سكريبت لإنشاء شهادة SSL لـ mohammi.com
# PowerShell script to generate SSL certificate for mohammi.com

Write-Host "🔐 إنشاء شهادة SSL لـ mohammi.com" -ForegroundColor Green
Write-Host "=" * 50

# إنشاء مجلد SSL إذا لم يكن موجوداً
$sslDir = "ssl"
if (!(Test-Path $sslDir)) {
    New-Item -ItemType Directory -Path $sslDir
    Write-Host "✅ تم إنشاء مجلد SSL" -ForegroundColor Green
}

# الانتقال إلى مجلد SSL
Set-Location $sslDir

# إنشاء ملف التكوين للشهادة
$configContent = @"
[req]
default_bits = 2048
prompt = no
default_md = sha256
distinguished_name = dn
req_extensions = v3_req

[dn]
C=YE
ST=Sanaa
L=Sanaa
O=Mohammi Legal Services
OU=IT Department
CN=mohammi.com

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = mohammi.com
DNS.2 = www.mohammi.com
DNS.3 = api.mohammi.com
DNS.4 = admin.mohammi.com
"@

# حفظ ملف التكوين
$configContent | Out-File -FilePath "mohammi.conf" -Encoding UTF8
Write-Host "✅ تم إنشاء ملف التكوين: mohammi.conf" -ForegroundColor Green

# التحقق من وجود OpenSSL
$opensslPath = Get-Command openssl -ErrorAction SilentlyContinue
if (-not $opensslPath) {
    Write-Host "❌ OpenSSL غير مثبت. يرجى تثبيت OpenSSL أولاً" -ForegroundColor Red
    Write-Host "يمكنك تحميله من: https://slproweb.com/products/Win32OpenSSL.html" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ تم العثور على OpenSSL: $($opensslPath.Source)" -ForegroundColor Green

# إنشاء المفتاح الخاص
Write-Host "🔑 إنشاء المفتاح الخاص..." -ForegroundColor Yellow
& openssl genrsa -out mohammi.key 2048
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ تم إنشاء المفتاح الخاص: mohammi.key" -ForegroundColor Green
} else {
    Write-Host "❌ فشل في إنشاء المفتاح الخاص" -ForegroundColor Red
    exit 1
}

# إنشاء طلب الشهادة (CSR)
Write-Host "📄 إنشاء طلب الشهادة (CSR)..." -ForegroundColor Yellow
& openssl req -new -key mohammi.key -out mohammi.csr -config mohammi.conf
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ تم إنشاء طلب الشهادة: mohammi.csr" -ForegroundColor Green
} else {
    Write-Host "❌ فشل في إنشاء طلب الشهادة" -ForegroundColor Red
    exit 1
}

# عرض محتوى CSR
Write-Host "`n📋 محتوى طلب الشهادة (CSR):" -ForegroundColor Cyan
Write-Host "=" * 50
Get-Content mohammi.csr
Write-Host "=" * 50

# إنشاء شهادة موقعة ذاتياً للاختبار
Write-Host "`n🧪 إنشاء شهادة موقعة ذاتياً للاختبار..." -ForegroundColor Yellow
& openssl x509 -req -days 365 -in mohammi.csr -signkey mohammi.key -out mohammi.crt -extensions v3_req -extfile mohammi.conf
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ تم إنشاء الشهادة التجريبية: mohammi.crt" -ForegroundColor Green
} else {
    Write-Host "❌ فشل في إنشاء الشهادة التجريبية" -ForegroundColor Red
}

# عرض معلومات الشهادة
Write-Host "`n📊 معلومات الشهادة:" -ForegroundColor Cyan
& openssl x509 -in mohammi.crt -text -noout | Select-String "Subject:", "DNS:", "Not Before:", "Not After:"

# إنشاء ملف PEM مجمع
Write-Host "`n📦 إنشاء ملف PEM مجمع..." -ForegroundColor Yellow
$certContent = Get-Content mohammi.crt -Raw
$keyContent = Get-Content mohammi.key -Raw
$pemContent = $certContent + "`n" + $keyContent
$pemContent | Out-File -FilePath "mohammi.pem" -Encoding UTF8 -NoNewline
Write-Host "✅ تم إنشاء ملف PEM مجمع: mohammi.pem" -ForegroundColor Green

# عرض ملخص الملفات المنشأة
Write-Host "`n📁 الملفات المنشأة:" -ForegroundColor Cyan
Write-Host "=" * 50
Get-ChildItem -Name "mohammi.*" | ForEach-Object {
    $size = (Get-Item $_).Length
    Write-Host "📄 $_ ($size bytes)" -ForegroundColor White
}

Write-Host "`n🎯 الخطوات التالية:" -ForegroundColor Yellow
Write-Host "1. انسخ محتوى ملف mohammi.csr والصقه في Name.com" -ForegroundColor White
Write-Host "2. بعد الحصول على الشهادة من Name.com، احفظها في ملف mohammi_signed.crt" -ForegroundColor White
Write-Host "3. استخدم mohammi.key و mohammi_signed.crt في إعداد الخادم" -ForegroundColor White

Write-Host "`n✅ تم الانتهاء من إنشاء ملفات SSL!" -ForegroundColor Green
