import { NextRequest, NextResponse } from 'next/server';
import { Client } from 'pg';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const {
      dbName,
      dbHost = 'localhost',
      dbPort = 5432,
      dbUser = 'postgres',
      dbPassword = 'postgres',
      timeoutMs = 5000,
    } = body || {};

    if (!dbName) {
      return NextResponse.json({ ok: false, error: 'dbName is required' }, { status: 400 });
    }

    const client = new Client({
      host: dbHost,
      port: Number(dbPort),
      database: dbName,
      user: dbUser,
      password: dbPassword,
      connectionTimeoutMillis: Number(timeoutMs) || 5000,
    });

    try {
      await client.connect();
      const result = await client.query('SELECT NOW() as now');
      await client.end();
      return NextResponse.json({ ok: true, now: result.rows?.[0]?.now });
    } catch (err: any) {
      try { await client.end(); } catch {}
      return NextResponse.json({ ok: false, error: err?.message || 'Connection failed' }, { status: 200 });
    }
  } catch (error: any) {
    return NextResponse.json({ ok: false, error: error?.message || 'Unknown error' }, { status: 500 });
  }
}
