// فحص تطابق الشهادة والمفتاح الخاص حسب دليل Name.com
const fs = require('fs');
const { execSync } = require('child_process');
const crypto = require('crypto');

console.log('🔍 فحص تطابق الشهادة والمفتاح الخاص');
console.log('حسب دليل Name.com - Solution ID: SO29559');
console.log('='.repeat(60));

// مسارات الملفات
const files = {
  cert: 'ssl/mohammi_com.crt',
  key: 'ssl/mohammi.key', // المفتاح الحالي
  correctKey: 'ssl/correct_mohammi.key' // المفتاح الصحيح إذا وجد
};

// التحقق من وجود الملفات
console.log('📁 فحص وجود الملفات:');
let keyToTest = null;

if (fs.existsSync(files.correctKey)) {
  keyToTest = files.correctKey;
  console.log(`✅ سيتم اختبار المفتاح الصحيح: ${files.correctKey}`);
} else if (fs.existsSync(files.key)) {
  keyToTest = files.key;
  console.log(`⚠️ سيتم اختبار المفتاح الحالي: ${files.key}`);
} else {
  console.log('❌ لم يتم العثور على أي مفتاح خاص');
  console.log('💡 يرجى وضع المفتاح الصحيح في: ssl/correct_mohammi.key');
  process.exit(1);
}

if (!fs.existsSync(files.cert)) {
  console.log('❌ ملف الشهادة غير موجود:', files.cert);
  process.exit(1);
}

console.log(`✅ الشهادة: ${files.cert}`);
console.log(`✅ المفتاح: ${keyToTest}`);

// الاختبار الأول: فحص سلامة المفتاح الخاص
console.log('\n🔐 الاختبار الأول: فحص سلامة المفتاح الخاص');
console.log('-'.repeat(50));

try {
  console.log('🔍 تشغيل: openssl rsa -in [key] -check -noout');
  const keyCheckResult = execSync(`openssl rsa -in "${keyToTest}" -check -noout`, { 
    encoding: 'utf8',
    stdio: ['pipe', 'pipe', 'pipe']
  });
  
  console.log('✅ نتيجة فحص المفتاح:');
  console.log(keyCheckResult || 'RSA key ok');
  
} catch (error) {
  console.log('❌ خطأ في فحص المفتاح:');
  console.log(error.stderr || error.message);
  
  // فحص أنواع الأخطاء المحددة
  const errorMsg = error.stderr || error.message;
  if (errorMsg.includes('p not prime')) {
    console.log('🚨 خطأ: p not prime - المفتاح تالف');
  } else if (errorMsg.includes('n does not equal p q')) {
    console.log('🚨 خطأ: n does not equal p q - المفتاح تالف');
  } else if (errorMsg.includes('d e not congruent to 1')) {
    console.log('🚨 خطأ: d e not congruent to 1 - المفتاح تالف');
  }
  
  console.log('💡 المفتاح قد يكون تالفاً أو غير صحيح');
}

// الاختبار الثاني: مقارنة Modulus
console.log('\n🔢 الاختبار الثاني: مقارنة Modulus');
console.log('-'.repeat(50));

try {
  console.log('🔍 استخراج modulus من الشهادة...');
  const certModulus = execSync(`openssl x509 -noout -modulus -in "${files.cert}"`, { 
    encoding: 'utf8' 
  }).trim();
  
  console.log('🔍 استخراج modulus من المفتاح الخاص...');
  const keyModulus = execSync(`openssl rsa -noout -modulus -in "${keyToTest}"`, { 
    encoding: 'utf8' 
  }).trim();
  
  console.log('📊 Modulus الشهادة:');
  console.log(certModulus.substring(0, 100) + '...');
  
  console.log('📊 Modulus المفتاح:');
  console.log(keyModulus.substring(0, 100) + '...');
  
  if (certModulus === keyModulus) {
    console.log('✅ Modulus متطابق! الشهادة والمفتاح متوافقان');
  } else {
    console.log('❌ Modulus غير متطابق! الشهادة والمفتاح غير متوافقين');
    console.log('💡 هذا يؤكد أن المفتاح الحالي خاطئ');
  }
  
} catch (error) {
  console.log('❌ خطأ في مقارنة Modulus:', error.message);
}

// الاختبار الثالث: اختبار التشفير وفك التشفير
console.log('\n🔒 الاختبار الثالث: اختبار التشفير وفك التشفير');
console.log('-'.repeat(50));

try {
  // استخراج المفتاح العام من الشهادة
  console.log('🔍 استخراج المفتاح العام من الشهادة...');
  execSync(`openssl x509 -in "${files.cert}" -noout -pubkey > ssl/public_key.pem`);
  
  // إنشاء ملف اختبار
  const testMessage = 'message test';
  fs.writeFileSync('ssl/test.txt', testMessage);
  console.log('📝 تم إنشاء ملف الاختبار: ssl/test.txt');
  
  // تشفير الملف بالمفتاح العام
  console.log('🔐 تشفير الملف بالمفتاح العام...');
  execSync(`openssl pkeyutl -encrypt -in ssl/test.txt -pubin -inkey ssl/public_key.pem -out ssl/cipher.txt`);
  
  // فك التشفير بالمفتاح الخاص
  console.log('🔓 فك التشفير بالمفتاح الخاص...');
  const decryptedMessage = execSync(`openssl pkeyutl -decrypt -in ssl/cipher.txt -inkey "${keyToTest}"`, { 
    encoding: 'utf8' 
  });
  
  console.log('📤 الرسالة الأصلية:', testMessage);
  console.log('📥 الرسالة المفكوكة:', decryptedMessage.trim());
  
  if (testMessage === decryptedMessage.trim()) {
    console.log('✅ اختبار التشفير/فك التشفير نجح!');
  } else {
    console.log('❌ اختبار التشفير/فك التشفير فشل!');
  }
  
} catch (error) {
  console.log('❌ خطأ في اختبار التشفير:', error.message);
  console.log('💡 هذا يؤكد أن المفتاح والشهادة غير متوافقين');
}

// الاختبار الرابع: اختبار التوقيع الرقمي
console.log('\n✍️ الاختبار الرابع: اختبار التوقيع الرقمي');
console.log('-'.repeat(50));

try {
  // توقيع الملف بالمفتاح الخاص
  console.log('🔍 توقيع الملف بالمفتاح الخاص...');
  execSync(`openssl dgst -sha256 -sign "${keyToTest}" -out ssl/test.sig ssl/test.txt`);
  
  // التحقق من التوقيع بالمفتاح العام
  console.log('🔍 التحقق من التوقيع بالمفتاح العام...');
  const verifyResult = execSync(`openssl dgst -sha256 -verify ssl/public_key.pem -signature ssl/test.sig ssl/test.txt`, { 
    encoding: 'utf8' 
  }).trim();
  
  console.log('📋 نتيجة التحقق من التوقيع:', verifyResult);
  
  if (verifyResult.includes('Verified OK')) {
    console.log('✅ اختبار التوقيع الرقمي نجح!');
  } else {
    console.log('❌ اختبار التوقيع الرقمي فشل!');
  }
  
} catch (error) {
  console.log('❌ خطأ في اختبار التوقيع:', error.message);
}

// تنظيف الملفات المؤقتة
console.log('\n🧹 تنظيف الملفات المؤقتة...');
try {
  ['ssl/public_key.pem', 'ssl/test.txt', 'ssl/cipher.txt', 'ssl/test.sig'].forEach(file => {
    if (fs.existsSync(file)) {
      fs.unlinkSync(file);
    }
  });
  console.log('✅ تم تنظيف الملفات المؤقتة');
} catch (error) {
  console.log('⚠️ خطأ في تنظيف الملفات المؤقتة');
}

// النتيجة النهائية
console.log('\n📊 النتيجة النهائية:');
console.log('='.repeat(60));

if (keyToTest === files.correctKey) {
  console.log('🎯 تم اختبار المفتاح الصحيح');
} else {
  console.log('⚠️ تم اختبار المفتاح الحالي (قد يكون خاطئ)');
}

console.log('\n💡 التوصيات:');
console.log('1. إذا فشلت الاختبارات، المفتاح غير صحيح');
console.log('2. اتصل بـ Name.com لطلب المفتاح الصحيح');
console.log('3. أو أنشئ مفتاح جديد واطلب إعادة إصدار الشهادة');
console.log('4. إذا نجحت الاختبارات، يمكن تشغيل SSL');

console.log('\n🚀 للمتابعة مع SSL (إذا نجحت الاختبارات):');
console.log('node ssl/start_ssl_correct.js');
