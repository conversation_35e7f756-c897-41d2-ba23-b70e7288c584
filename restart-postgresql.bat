@echo off
chcp 65001 >nul
echo ========================================
echo   Restarting PostgreSQL Service
echo ========================================
echo.

echo 1. Stopping all PostgreSQL services...
echo.

REM Stop all possible PostgreSQL services
net stop postgresql-x64-15 2>nul
net stop postgresql-x64-14 2>nul
net stop postgresql-x64-13 2>nul
net stop postgresql-x64-12 2>nul
net stop postgresql 2>nul

echo.
echo 2. Waiting 5 seconds...
timeout /t 5 /nobreak >nul

echo.
echo 3. Starting PostgreSQL services...
echo.

REM Try to start services
echo Trying postgresql-x64-15...
net start postgresql-x64-15
if %errorlevel% equ 0 (
    echo SUCCESS: postgresql-x64-15 started
    goto :test
)

echo Trying postgresql-x64-14...
net start postgresql-x64-14
if %errorlevel% equ 0 (
    echo SUCCESS: postgresql-x64-14 started
    goto :test
)

echo Trying postgresql-x64-13...
net start postgresql-x64-13
if %errorlevel% equ 0 (
    echo SUCCESS: postgresql-x64-13 started
    goto :test
)

echo Trying postgresql...
net start postgresql
if %errorlevel% equ 0 (
    echo SUCCESS: postgresql started
    goto :test
)

echo.
echo No PostgreSQL service found or failed to start.
echo Please check Services.msc manually.
goto :end

:test
echo.
echo ========================================
echo   Testing Connection
echo ========================================
echo.
echo Testing with password: yemen123
node -e "
const { Client } = require('pg');
const client = new Client({
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'postgres',
  connectTimeoutMillis: 5000
});

client.connect()
  .then(() => {
    console.log('✅ Connection successful!');
    return client.query('SELECT version()');
  })
  .then(result => {
    console.log('📋 PostgreSQL version:', result.rows[0].version.split(' ')[1]);
    return client.query('SELECT datname FROM pg_database WHERE datistemplate = false ORDER BY datname');
  })
  .then(result => {
    console.log('📊 Available databases:');
    result.rows.forEach(row => console.log('   -', row.datname));
    
    const targetDbs = ['mohammi', 'mohammidev'];
    const found = result.rows.filter(row => targetDbs.includes(row.datname));
    if (found.length > 0) {
      console.log('🎯 Target databases found:', found.map(row => row.datname).join(', '));
    } else {
      console.log('⚠️ Target databases (mohammi, mohammidev) not found');
    }
    
    client.end();
  })
  .catch(error => {
    console.log('❌ Connection failed:', error.message);
    client.end();
  });
"

:end
echo.
pause
