/**
 * سكريبت للتحقق من بنية جدول دليل الحسابات
 */

const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'mohammidev'
};

async function checkChartStructure() {
  const client = new Client(dbConfig);

  try {
    await client.connect();
    console.log('✅ متصل بقاعدة البيانات mohammidev');

    // فحص بنية جدول دليل الحسابات
    const structure = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_name = 'chart_of_accounts' AND table_schema = 'public'
      ORDER BY ordinal_position
    `);

    console.log('\n📋 بنية جدول chart_of_accounts:');
    structure.rows.forEach(col => {
      console.log(`   - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? '(مطلوب)' : '(اختياري)'} ${col.column_default ? `[افتراضي: ${col.column_default}]` : ''}`);
    });

    // فحص عينة من البيانات
    const sample = await client.query('SELECT * FROM chart_of_accounts LIMIT 3');
    console.log('\n📊 عينة من البيانات:');
    sample.rows.forEach(row => {
      console.log(`   - ID: ${row.id}, Code: ${row.account_code}, Name: ${row.account_name}, Level: ${row.account_level || 'غير محدد'}`);
    });

  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await client.end();
  }
}

checkChartStructure().catch(console.error);
