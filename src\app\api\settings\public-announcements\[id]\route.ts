import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// DELETE - حذف إعلان عام
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    
    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, error: 'معرف الإعلان غير صحيح' },
        { status: 400 }
      )
    }

    await query('DELETE FROM public_announcements WHERE id = $1', [id])

    return NextResponse.json({
      success: true,
      message: 'تم حذف الإعلان بنجاح'
    })
  } catch (error) {
    console.error('Error deleting public announcement:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الإعلان' },
      { status: 500 }
    )
  }
}