import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب جميع الجلسات أو جلسات قضية محددة
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const issueId = searchParams.get('issue_id')
    const status = searchParams.get('status')
    const upcoming = searchParams.get('upcoming')

    let queryStr = `
      SELECT
        h.*,
        i.case_number,
        i.title as issue_title
      FROM hearings h
      LEFT JOIN issues i ON h.issue_id = i.id
    `
    const queryParams = []
    const conditions = []

    // فلترة حسب القضية
    if (issueId) {
      conditions.push(`h.issue_id = $${queryParams.length + 1}`)
      queryParams.push(issueId)
    }

    // فلترة حسب الحالة
    if (status) {
      conditions.push(`h.status = $${queryParams.length + 1}`)
      queryParams.push(status)
    }

    // فلترة الجلسات القادمة فقط
    if (upcoming === 'true') {
      conditions.push(`h.hearing_date > CURRENT_TIMESTAMP`)
      conditions.push(`h.status IN ('scheduled', 'postponed')`)
    }

    if (conditions.length > 0) {
      queryStr += ` WHERE ${conditions.join(' AND ')}`
    }

    queryStr += ` ORDER BY h.hearing_date ASC`

    const result = await query(queryStr, queryParams)

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching hearings:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات الجلسات' },
      { status: 500 }
    )
  }
}

// POST - إضافة جلسة جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      issue_id,
      hearing_date,
      hearing_time,
      court_name,
      hearing_type,
      notes
    } = body

    if (!issue_id || !hearing_date) {
      return NextResponse.json(
        { success: false, error: 'معرف القضية وتاريخ الجلسة مطلوبان' },
        { status: 400 }
      )
    }

    const result = await query(`
      INSERT INTO hearings (issue_id, hearing_date, hearing_time, court_name, hearing_type, notes)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `, [issue_id, hearing_date, hearing_time, court_name, hearing_type, notes])

    return NextResponse.json({
      success: true,
      message: 'تم إضافة الجلسة بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error creating hearing:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة الجلسة' },
      { status: 500 }
    )
  }
}
