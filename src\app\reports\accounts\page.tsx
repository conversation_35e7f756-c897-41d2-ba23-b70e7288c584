"use client"

import React, { useState } from 'react'

function qs(params: Record<string, any>) {
  const sp = new URLSearchParams()
  Object.entries(params).forEach(([k, v]) => {
    if (v !== undefined && v !== null && v !== '' && v !== 'all') sp.append(k, String(v))
  })
  return sp.toString()
}

export default function AccountsReportsPage() {
  const [report, setReport] = useState<'statement' | 'daily'>('statement')
  const [accountId, setAccountId] = useState('')
  const [clientId, setClientId] = useState('')
  const [employeeId, setEmployeeId] = useState('')
  const [dateFrom, setDateFrom] = useState('')
  const [dateTo, setDateTo] = useState('')
  const [status, setStatus] = useState<'approved' | 'draft' | 'all'>('approved')
  const [summary, setSummary] = useState(true)

  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [data, setData] = useState<any>(null)

  const search = async () => {
    try {
      setLoading(true)
      setError(null)
      setData(null)
      const query = qs({
        report,
        account_id: accountId || undefined,
        client_id: clientId || undefined,
        employee_id: employeeId || undefined,
        date_from: dateFrom || undefined,
        date_to: dateTo || undefined,
        status,
        summary: summary ? 'true' : 'false',
      })
      const res = await fetch(`/api/reports/accounts?${query}`)
      const json = await res.json()
      if (!res.ok || json.success === false) throw new Error(json.error || 'فشل الجلب')
      setData(json)
    } catch (e: any) {
      setError(e.message || 'خطأ غير معروف')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-6 space-y-6">
      <div>
        <h1 className="text-2xl font-bold">تقارير الحسابات</h1>
        <p className="text-gray-600">كشف حساب أو حركة يومية مع فلاتر مرنة</p>
      </div>

      <div className="space-y-4 bg-white rounded border p-4">
        <h2 className="font-semibold">المرشحات</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm text-gray-700 mb-1">نوع التقرير</label>
            <select value={report} onChange={(e)=>setReport(e.target.value as any)} className="w-full border rounded p-2">
              <option value="statement">كشف حساب</option>
              <option value="daily">الحركة اليومية</option>
            </select>
          </div>
          <div>
            <label className="block text-sm text-gray-700 mb-1">معرف الحساب</label>
            <input value={accountId} onChange={(e)=>setAccountId(e.target.value)} className="w-full border rounded p-2" placeholder="coa.id" />
          </div>
          <div>
            <label className="block text-sm text-gray-700 mb-1">معرف العميل</label>
            <input value={clientId} onChange={(e)=>setClientId(e.target.value)} className="w-full border rounded p-2" placeholder="clients.id" />
          </div>
          <div>
            <label className="block text-sm text-gray-700 mb-1">معرف الموظف</label>
            <input value={employeeId} onChange={(e)=>setEmployeeId(e.target.value)} className="w-full border rounded p-2" placeholder="employees.id" />
          </div>
          <div>
            <label className="block text-sm text-gray-700 mb-1">الحالة</label>
            <select value={status} onChange={(e)=>setStatus(e.target.value as any)} className="w-full border rounded p-2">
              <option value="approved">معتمد</option>
              <option value="draft">مسودة</option>
              <option value="all">الكل</option>
            </select>
          </div>
          <div>
            <label className="block text-sm text-gray-700 mb-1">من تاريخ</label>
            <input type="date" value={dateFrom} onChange={(e)=>setDateFrom(e.target.value)} className="w-full border rounded p-2" />
          </div>
          <div>
            <label className="block text-sm text-gray-700 mb-1">إلى تاريخ</label>
            <input type="date" value={dateTo} onChange={(e)=>setDateTo(e.target.value)} className="w-full border rounded p-2" />
          </div>
          <div className="flex items-center gap-2">
            <input id="summary" type="checkbox" checked={summary} onChange={(e)=>setSummary(e.target.checked)} />
            <label htmlFor="summary" className="text-sm text-gray-700">إظهار ملخص</label>
          </div>
        </div>
        <div className="flex gap-3">
          <button onClick={search} className="px-4 py-2 rounded bg-blue-600 text-white">بحث</button>
          <button onClick={()=>{setData(null); setError(null)}} className="px-4 py-2 rounded bg-gray-100">مسح النتائج</button>
        </div>
      </div>

      <div className="bg-white rounded border p-4">
        <h2 className="font-semibold mb-3">النتائج</h2>
        {loading && <div className="text-gray-600">جاري التحميل...</div>}
        {error && <div className="text-red-600">خطأ: {error}</div>}

        {!loading && !error && data && report === 'daily' && (
          <div className="overflow-x-auto">
            <table className="min-w-full text-sm">
              <thead className="bg-gray-100">
                <tr>
                  <th className="p-2 text-right">التاريخ</th>
                  <th className="p-2 text-right">عدد القيود</th>
                  <th className="p-2 text-right">إجمالي مدين</th>
                  <th className="p-2 text-right">إجمالي دائن</th>
                </tr>
              </thead>
              <tbody>
                {data.data?.map((r: any, idx: number) => (
                  <tr key={idx} className="border-b">
                    <td className="p-2">{r.entry_date?.slice(0,10)}</td>
                    <td className="p-2">{r.entries_count}</td>
                    <td className="p-2">{r.total_debit}</td>
                    <td className="p-2">{r.total_credit}</td>
                  </tr>
                ))}
                {(!data.data || data.data.length === 0) && (
                  <tr><td className="p-3 text-center text-gray-500" colSpan={4}>لا توجد نتائج</td></tr>
                )}
              </tbody>
            </table>
          </div>
        )}

        {!loading && !error && data && report === 'statement' && (
          <div className="overflow-x-auto">
            {data.summary && (
              <div className="mb-4 p-3 bg-gray-50 rounded border text-sm flex flex-wrap gap-6">
                <div>رصيد افتتاحي: د {data.opening?.debit || 0} | دائن {data.opening?.credit || 0}</div>
                <div>إجمالي الفترة: مدين {data.totals?.period_debit || 0} | دائن {data.totals?.period_credit || 0}</div>
                <div>رصيد ختامي: {data.closing?.balance_type} - د {data.closing?.debit || 0} | دائن {data.closing?.credit || 0}</div>
              </div>
            )}
            <table className="min-w-full text-sm">
              <thead className="bg-gray-100">
                <tr>
                  <th className="p-2 text-right">رقم القيد</th>
                  <th className="p-2 text-right">التاريخ</th>
                  <th className="p-2 text-right">الوصف</th>
                  <th className="p-2 text-right">مدين</th>
                  <th className="p-2 text-right">دائن</th>
                  <th className="p-2 text-right">الحساب</th>
                  <th className="p-2 text-right">الرمز</th>
                </tr>
              </thead>
              <tbody>
                {data.lines?.map((l: any) => (
                  <tr key={l.line_id} className="border-b">
                    <td className="p-2">{l.entry_number}</td>
                    <td className="p-2">{l.entry_date?.slice(0,10)}</td>
                    <td className="p-2">{l.entry_description || l.line_description}</td>
                    <td className="p-2">{l.debit}</td>
                    <td className="p-2">{l.credit}</td>
                    <td className="p-2">{l.account_name}</td>
                    <td className="p-2">{l.account_code}</td>
                  </tr>
                ))}
                {(!data.lines || data.lines.length === 0) && (
                  <tr><td className="p-3 text-center text-gray-500" colSpan={7}>لا توجد نتائج</td></tr>
                )}
              </tbody>
            </table>
          </div>
        )}

        {!loading && !error && !data && <div className="text-gray-500">اضغط على زر بحث لعرض النتائج</div>}
      </div>
    </div>
  )
}
