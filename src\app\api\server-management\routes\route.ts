import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

function getConfigPath() {
  return path.resolve(process.cwd(), 'production', 'routing.config.json');
}

export async function GET() {
  try {
    const configPath = getConfigPath();
    if (!fs.existsSync(configPath)) {
      return NextResponse.json({ error: 'routing.config.json not found' }, { status: 404 });
    }
    const content = fs.readFileSync(configPath, 'utf8');
    const data = JSON.parse(content);
    return NextResponse.json(data);
  } catch (error: any) {
    return NextResponse.json({ error: error?.message || 'Unknown error' }, { status: 500 });
  }
}
