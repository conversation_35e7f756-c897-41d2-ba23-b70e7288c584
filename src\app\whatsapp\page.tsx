'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  MessageSquare,
  Send,
  Users,
  Settings,
  BarChart3,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Phone,
  FileText,
  Zap
} from 'lucide-react'
import MainLayout from '@/components/layout/main-layout'

interface WhatsAppTemplate {
  id: number
  templateName: string
  templateCategory: string
  bodyText: string
  status: string
  variables?: string[]
}

interface WhatsAppContact {
  id: number
  phoneNumber: string
  fullPhone: string
  isVerified: boolean
  optIn: boolean
  userName?: string
  userType: 'client' | 'employee'
}

interface NotificationType {
  id: number
  typeCode: string
  typeName: string
  description: string
  templateId?: number
  sendToClients: boolean
  sendToEmployees: boolean
  priorityLevel: number
}

export default function WhatsAppManagement() {
  const [templates, setTemplates] = useState<WhatsAppTemplate[]>([])
  const [contacts, setContacts] = useState<WhatsAppContact[]>([])
  const [notificationTypes, setNotificationTypes] = useState<NotificationType[]>([])
  const [selectedTemplate, setSelectedTemplate] = useState<number | null>(null)
  const [selectedContacts, setSelectedContacts] = useState<number[]>([])
  const [messageVariables, setMessageVariables] = useState<Record<string, string>>({})
  const [isLoading, setIsLoading] = useState(false)
  const [sendResults, setSendResults] = useState<any>(null)

  // تحميل البيانات عند بدء التشغيل
  useEffect(() => {
    loadTemplates()
    loadContacts()
    loadNotificationTypes()
  }, [])

  const loadTemplates = async () => {
    try {
      const response = await fetch('/api/whatsapp/templates')
      const data = await response.json()
      if (data.success) {
        setTemplates(data.data)
      }
    } catch (error) {
      console.error('خطأ في تحميل القوالب:', error)
    }
  }

  const loadContacts = async () => {
    try {
      const response = await fetch('/api/whatsapp/contacts')
      const data = await response.json()
      if (data.success) {
        setContacts(data.data)
      }
    } catch (error) {
      console.error('خطأ في تحميل جهات الاتصال:', error)
    }
  }

  const loadNotificationTypes = async () => {
    try {
      const response = await fetch('/api/whatsapp/notification-types')
      const data = await response.json()
      if (data.success) {
        setNotificationTypes(data.data)
      }
    } catch (error) {
      console.error('خطأ في تحميل أنواع الإشعارات:', error)
    }
  }

  const handleSendMessage = async () => {
    if (!selectedTemplate || selectedContacts.length === 0) {
      alert('يرجى اختيار قالب وجهات اتصال')
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch('/api/whatsapp/send', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          contactIds: selectedContacts,
          templateId: selectedTemplate,
          variables: messageVariables
        })
      })

      const data = await response.json()
      setSendResults(data)

      if (data.success) {
        alert(`تم إرسال ${data.data.successCount} رسالة بنجاح من أصل ${data.data.totalSent}`)
      }
    } catch (error) {
      console.error('خطأ في الإرسال:', error)
      alert('حدث خطأ في الإرسال')
    } finally {
      setIsLoading(false)
    }
  }

  const handleContactSelection = (contactId: number) => {
    setSelectedContacts(prev =>
      prev.includes(contactId)
        ? prev.filter(id => id !== contactId)
        : [...prev, contactId]
    )
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return <Badge className="bg-green-100 text-green-800">معتمد</Badge>
      case 'PENDING':
        return <Badge className="bg-yellow-100 text-yellow-800">في الانتظار</Badge>
      case 'REJECTED':
        return <Badge className="bg-red-100 text-red-800">مرفوض</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>
    }
  }

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50">
        <div className="space-y-6 p-6">
          {/* العنوان الرئيسي */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3 space-x-reverse">
              <MessageSquare className="h-8 w-8 text-green-600" />
              <h1 className="text-2xl font-bold text-gray-900">إدارة إشعارات WhatsApp</h1>
            </div>
            <div className="flex space-x-2 space-x-reverse">
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 ml-2" />
                الإعدادات
              </Button>
              <Button variant="outline" size="sm">
                <BarChart3 className="h-4 w-4 ml-2" />
                الإحصائيات
              </Button>
            </div>
          </div>

          {/* إحصائيات سريعة */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">القوالب المعتمدة</p>
                    <p className="text-2xl font-bold text-green-600">
                      {templates.filter(t => t.status === 'APPROVED').length}
                    </p>
                  </div>
                  <FileText className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">جهات الاتصال النشطة</p>
                    <p className="text-2xl font-bold text-blue-600">
                      {contacts.filter(c => c.optIn).length}
                    </p>
                  </div>
                  <Users className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">الرسائل اليوم</p>
                    <p className="text-2xl font-bold text-purple-600">0</p>
                  </div>
                  <Send className="h-8 w-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">معدل التسليم</p>
                    <p className="text-2xl font-bold text-orange-600">0%</p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* التبويبات الرئيسية */}
          <Tabs defaultValue="send" className="space-y-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="send">إرسال رسائل</TabsTrigger>
              <TabsTrigger value="templates">القوالب</TabsTrigger>
              <TabsTrigger value="contacts">جهات الاتصال</TabsTrigger>
              <TabsTrigger value="history">السجل</TabsTrigger>
            </TabsList>

            {/* تبويب إرسال الرسائل */}
            <TabsContent value="send" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* اختيار القالب */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2 space-x-reverse">
                      <FileText className="h-5 w-5" />
                      <span>اختيار القالب</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <Select value={selectedTemplate?.toString()} onValueChange={(value) => setSelectedTemplate(Number(value))}>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر قالب الرسالة" />
                      </SelectTrigger>
                      <SelectContent>
                        {templates.filter(t => t.status === 'APPROVED').map(template => (
                          <SelectItem key={template.id} value={template.id.toString()}>
                            {template.templateName}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    {selectedTemplate && (
                      <div className="space-y-3">
                        <div className="p-3 bg-gray-50 rounded-lg">
                          <p className="text-sm text-gray-600 mb-2">معاينة القالب:</p>
                          <p className="text-sm">
                            {templates.find(t => t.id === selectedTemplate)?.bodyText}
                          </p>
                        </div>

                        {/* متغيرات القالب */}
                        {templates.find(t => t.id === selectedTemplate)?.variables?.map(variable => (
                          <div key={variable}>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              {variable}
                            </label>
                            <Input
                              placeholder={`أدخل قيمة ${variable}`}
                              value={messageVariables[variable] || ''}
                              onChange={(e) => setMessageVariables(prev => ({
                                ...prev,
                                [variable]: e.target.value
                              }))}
                            />
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* اختيار جهات الاتصال */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2 space-x-reverse">
                      <Users className="h-5 w-5" />
                      <span>جهات الاتصال ({selectedContacts.length})</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 max-h-64 overflow-y-auto">
                      {contacts.filter(c => c.optIn).map(contact => (
                        <div key={contact.id} className="flex items-center space-x-3 space-x-reverse p-2 hover:bg-gray-50 rounded">
                          <input
                            type="checkbox"
                            checked={selectedContacts.includes(contact.id)}
                            onChange={() => handleContactSelection(contact.id)}
                            className="rounded"
                          />
                          <div className="flex-1">
                            <p className="text-sm font-medium">{contact.userName || contact.fullPhone}</p>
                            <p className="text-xs text-gray-500">{contact.fullPhone}</p>
                          </div>
                          <Badge variant={contact.userType === 'client' ? 'default' : 'secondary'}>
                            {contact.userType === 'client' ? 'عميل' : 'موظف'}
                          </Badge>
                        </div>
                      ))}
                    </div>

                    <div className="mt-4 pt-4 border-t">
                      <Button
                        onClick={handleSendMessage}
                        disabled={isLoading || !selectedTemplate || selectedContacts.length === 0}
                        className="w-full"
                      >
                        {isLoading ? (
                          <>
                            <Clock className="h-4 w-4 ml-2 animate-spin" />
                            جاري الإرسال...
                          </>
                        ) : (
                          <>
                            <Send className="h-4 w-4 ml-2" />
                            إرسال الرسائل ({selectedContacts.length})
                          </>
                        )}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* نتائج الإرسال */}
              {sendResults && (
                <Card>
                  <CardHeader>
                    <CardTitle>نتائج الإرسال</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-3 gap-4 mb-4">
                      <div className="text-center">
                        <p className="text-2xl font-bold text-blue-600">{sendResults.data.totalSent}</p>
                        <p className="text-sm text-gray-600">إجمالي المرسل</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-green-600">{sendResults.data.successCount}</p>
                        <p className="text-sm text-gray-600">نجح</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-red-600">{sendResults.data.failureCount}</p>
                        <p className="text-sm text-gray-600">فشل</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* تبويب القوالب */}
            <TabsContent value="templates">
              <Card>
                <CardHeader>
                  <CardTitle>قوالب الرسائل</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {templates.map(template => (
                      <div key={template.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="font-medium">{template.templateName}</h3>
                          {getStatusBadge(template.status)}
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{template.bodyText}</p>
                        <p className="text-xs text-gray-500">الفئة: {template.templateCategory}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* تبويب جهات الاتصال */}
            <TabsContent value="contacts">
              <Card>
                <CardHeader>
                  <CardTitle>جهات الاتصال</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {contacts.map(contact => (
                      <div key={contact.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3 space-x-reverse">
                          <Phone className="h-4 w-4 text-gray-400" />
                          <div>
                            <p className="font-medium">{contact.userName || contact.fullPhone}</p>
                            <p className="text-sm text-gray-500">{contact.fullPhone}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <Badge variant={contact.userType === 'client' ? 'default' : 'secondary'}>
                            {contact.userType === 'client' ? 'عميل' : 'موظف'}
                          </Badge>
                          {contact.isVerified && <CheckCircle className="h-4 w-4 text-green-500" />}
                          {contact.optIn ? (
                            <Badge className="bg-green-100 text-green-800">مفعل</Badge>
                          ) : (
                            <Badge className="bg-red-100 text-red-800">معطل</Badge>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* تبويب السجل */}
            <TabsContent value="history">
              <Card>
                <CardHeader>
                  <CardTitle>سجل الرسائل</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-500 text-center py-8">سيتم إضافة سجل الرسائل قريباً</p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </MainLayout>
  )
}
