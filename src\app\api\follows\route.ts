import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

async function ensureSchema() {
  // إنشاء الجداول المطلوبة إن لم تكن موجودة
  await query(`
    CREATE TABLE IF NOT EXISTS follows (
      id SERIAL PRIMARY KEY,
      case_id INTEGER NOT NULL,
      service_id INTEGER NOT NULL,
      user_id INTEGER NOT NULL,
      report TEXT,
      date_field DATE NOT NULL DEFAULT CURRENT_DATE,
      status TEXT NOT NULL DEFAULT 'pending',
      next_hearing_id INTEGER NULL,
      judge_id INTEGER NULL REFERENCES judges(id),
      created_at TIMESTAMP DEFAULT NOW(),
      updated_at TIMESTAMP DEFAULT NOW()
    );
  `)
  await query(`
    CREATE TABLE IF NOT EXISTS follow_details (
      id SERIAL PRIMARY KEY,
      follow_id INTEGER REFERENCES follows(id) ON DELETE CASCADE,
      client_amount NUMERIC(18,2) DEFAULT 0,
      employee_amount NUMERIC(18,2) DEFAULT 0,
      created_at TIMESTAMP DEFAULT NOW()
    );
  `)
}

// GET - جلب جميع المتابعات من قاعدة البيانات
export async function GET(request: NextRequest) {
  try {
    await ensureSchema()
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id')
    const where = userId ? 'WHERE f.user_id = $1' : ''
    const params = userId ? [userId] : []
    const sql = `
      SELECT 
        f.id,
        f.case_id,
        i.case_number,
        i.title as case_title,
        i.client_name,
        f.service_id,
        s.name as service_name,
        f.report,
        f.date_field,
        f.status,
        f.user_id,
        u.name as user_name,
        h.hearing_date as next_hearing_date,
        h.hearing_time as next_hearing_time,
        f.judge_id,
        j.name as judge_name,
        fd.client_amount,
        fd.employee_amount
      FROM follows f
      LEFT JOIN issues i ON i.id = f.case_id
      LEFT JOIN services s ON s.id = f.service_id
      LEFT JOIN employees u ON u.id = f.user_id
      LEFT JOIN hearings h ON h.id = f.next_hearing_id
      LEFT JOIN judges j ON j.id = f.judge_id
      LEFT JOIN LATERAL (
        SELECT client_amount, employee_amount
        FROM follow_details fd
        WHERE fd.follow_id = f.id
        ORDER BY fd.id DESC
        LIMIT 1
      ) fd ON true
      ${where}
      ORDER BY f.id DESC`;
    const result = await query(sql, params)
    return NextResponse.json({ success: true, data: result.rows })
  } catch (error) {
    console.error('Error fetching المتابعات:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات المتابعات' },
      { status: 500 }
    )
  }
}

// POST - إضافة المتابعات جديد
export async function POST(request: NextRequest) {
  try {
    await ensureSchema()
    const body = await request.json()
    const {
      case_id,
      service_id,
      user_id,
      report,
      date_field,
      status = 'pending',
      next_hearing_id,
      judge_id,
      client_amount = 0,
      employee_amount = 0
    } = body

    if (!case_id || !service_id || !user_id) {
      return NextResponse.json(
        { success: false, error: 'case_id و service_id و user_id مطلوبة' },
        { status: 400 }
      )
    }

    const insertFollow = await query(
      `INSERT INTO follows (case_id, service_id, user_id, report, date_field, status, next_hearing_id, judge_id)
       VALUES ($1,$2,$3,$4,COALESCE($5, CURRENT_DATE),$6,$7,$8)
       RETURNING id`,
      [case_id, service_id, user_id, report || null, date_field || null, status, next_hearing_id || null, judge_id || null]
    )
    const followId = insertFollow.rows[0].id

    await query(
      `INSERT INTO follow_details (follow_id, client_amount, employee_amount)
       VALUES ($1,$2,$3)`,
      [followId, client_amount, employee_amount]
    )

    return NextResponse.json({ success: true, data: { id: followId } })
  } catch (error) {
    console.error('Error creating المتابعات:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة المتابعة' },
      { status: 500 }
    )
  }
}

// PUT - تحديث المتابعات
export async function PUT(request: NextRequest) {
  try {
    await ensureSchema()
    const body = await request.json()
    const { id, report, date_field, status, next_hearing_id, judge_id, client_amount, employee_amount, action } = body
    if (!id) return NextResponse.json({ success: false, error: 'id مطلوب' }, { status: 400 })

    if (action === 'approve') {
      await query(`UPDATE follows SET status='ready', updated_at=NOW() WHERE id=$1`, [id])
      return NextResponse.json({ success: true, message: 'تم تعميد المتابعة' })
    }

    await query(
      `UPDATE follows SET report=COALESCE($2, report), date_field=COALESCE($3, date_field), status=COALESCE($4, status), next_hearing_id=COALESCE($5, next_hearing_id), judge_id=COALESCE($6, judge_id), updated_at=NOW() WHERE id=$1`,
      [id, report || null, date_field || null, status || null, next_hearing_id || null, judge_id || null]
    )
    if (client_amount != null || employee_amount != null) {
      await query(
        `INSERT INTO follow_details (follow_id, client_amount, employee_amount) VALUES ($1,$2,$3)`,
        [id, client_amount ?? 0, employee_amount ?? 0]
      )
    }
    return NextResponse.json({ success: true, message: 'تم تحديث المتابعة' })
  } catch (error) {
    console.error('Error updating المتابعات:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث المتابعة' },
      { status: 500 }
    )
  }
}

// DELETE - حذف المتابعات
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف المتابعات مطلوب' },
        { status: 400 }
      )
    }

    await query('DELETE FROM follows WHERE id = $1', [id])

    return NextResponse.json({
      success: true,
      message: 'تم حذف المتابعات بنجاح'
    })
  } catch (error) {
    console.error('Error deleting المتابعات:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف المتابعات' },
      { status: 500 }
    )
  }
}