/**
 * مكتبة الربط التلقائي للحسابات
 * تحتوي على دوال لربط السجلات الجديدة بالحسابات المحاسبية تلقائياً
 */

interface AutoLinkResult {
  success: boolean
  message: string
  link_id?: number
  sub_account_code?: string
  sub_account_name?: string
}

/**
 * ربط سجل جديد بحساب محاسبي تلقائياً
 * @param tableName اسم الجدول
 * @param recordId معرف السجل
 * @param recordName اسم السجل (اختياري)
 * @returns نتيجة عملية الربط
 */
export async function autoLinkAccount(
  tableName: string, 
  recordId: number, 
  recordName?: string
): Promise<AutoLinkResult> {
  try {
    const response = await fetch('/api/auto-link-account', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        table_name: tableName,
        record_id: recordId,
        record_name: recordName
      })
    })

    const data = await response.json()

    if (data.success) {
      return {
        success: true,
        message: data.message,
        link_id: data.data?.link?.id,
        sub_account_code: data.data?.link?.sub_account_code,
        sub_account_name: data.data?.link?.sub_account_name
      }
    } else {
      return {
        success: false,
        message: data.error || 'فشل في الربط التلقائي'
      }
    }
  } catch (error) {
    console.error('Error in auto-linking account:', error)
    return {
      success: false,
      message: 'حدث خطأ في الربط التلقائي'
    }
  }
}

/**
 * التحقق من إمكانية الربط التلقائي لجدول معين
 * @param tableName اسم الجدول
 * @returns هل الربط التلقائي مفعل أم لا
 */
export async function checkAutoLinkEnabled(tableName: string): Promise<boolean> {
  try {
    const response = await fetch(`/api/auto-link-account?table_name=${tableName}`)
    const data = await response.json()

    return data.success && data.data?.is_enabled
  } catch (error) {
    console.error('Error checking auto-link settings:', error)
    return false
  }
}

/**
 * ربط عدة سجلات دفعة واحدة
 * @param tableName اسم الجدول
 * @param records قائمة السجلات للربط
 * @returns نتائج عمليات الربط
 */
export async function autoLinkMultipleRecords(
  tableName: string,
  records: Array<{ id: number; name?: string }>
): Promise<AutoLinkResult[]> {
  const results: AutoLinkResult[] = []

  for (const record of records) {
    const result = await autoLinkAccount(tableName, record.id, record.name)
    results.push(result)
  }

  return results
}

/**
 * دالة مساعدة لعرض رسالة نتيجة الربط التلقائي
 * @param result نتيجة عملية الربط
 * @param showAlert هل تظهر تنبيه أم لا
 */
export function handleAutoLinkResult(result: AutoLinkResult, showAlert: boolean = true): void {
  if (showAlert) {
    if (result.success) {
      alert(`✅ ${result.message}\nكود الحساب: ${result.sub_account_code}`)
    } else {
      // لا نظهر خطأ إذا كان الربط التلقائي غير مفعل
      if (!result.message.includes('لا توجد إعدادات ربط')) {
        alert(`❌ ${result.message}`)
      }
    }
  }

}

/**
 * دالة لربط السجلات الموجودة مسبقاً عند تفعيل الربط التلقائي
 * @param tableName اسم الجدول
 * @returns عدد السجلات التي تم ربطها
 */
export async function linkExistingRecords(tableName: string): Promise<number> {
  try {
    // جلب السجلات الموجودة من الجدول
    const response = await fetch(`/api/${tableName}`)
    const data = await response.json()

    if (!data.success || !data.data) {
      return 0
    }

    let linkedCount = 0

    // ربط كل سجل
    for (const record of data.data) {
      const result = await autoLinkAccount(tableName, record.id, record.name)
      if (result.success) {
        linkedCount++
      }
    }

    return linkedCount
  } catch (error) {
    console.error('Error linking existing records:', error)
    return 0
  }
}

/**
 * دالة للحصول على معلومات الحساب المربوط لسجل معين
 * @param tableName اسم الجدول
 * @param recordId معرف السجل
 * @returns معلومات الحساب المربوط
 */
export async function getLinkedAccountInfo(tableName: string, recordId: number) {
  try {
    const response = await fetch(`/api/account-sub-links?table_name=${tableName}&record_id=${recordId}`)
    const data = await response.json()

    if (data.success && data.data.length > 0) {
      return data.data[0]
    }

    return null
  } catch (error) {
    console.error('Error getting linked account info:', error)
    return null
  }
}

/**
 * دالة لحذف رابط حساب
 * @param linkId معرف الرابط
 * @returns نتيجة عملية الحذف
 */
export async function deleteAccountLink(linkId: number): Promise<AutoLinkResult> {
  try {
    const response = await fetch('/api/auto-link-account', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ link_id: linkId })
    })

    const data = await response.json()

    return {
      success: data.success,
      message: data.message || (data.success ? 'تم حذف الرابط بنجاح' : 'فشل في حذف الرابط')
    }
  } catch (error) {
    console.error('Error deleting account link:', error)
    return {
      success: false,
      message: 'حدث خطأ في حذف الرابط'
    }
  }
}

/**
 * دالة لتحديث رابط حساب
 * @param linkId معرف الرابط
 * @param updates التحديثات المطلوبة
 * @returns نتيجة عملية التحديث
 */
export async function updateAccountLink(
  linkId: number, 
  updates: { sub_account_name?: string; notes?: string }
): Promise<AutoLinkResult> {
  try {
    const response = await fetch('/api/auto-link-account', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ link_id: linkId, ...updates })
    })

    const data = await response.json()

    return {
      success: data.success,
      message: data.message || (data.success ? 'تم تحديث الرابط بنجاح' : 'فشل في تحديث الرابط')
    }
  } catch (error) {
    console.error('Error updating account link:', error)
    return {
      success: false,
      message: 'حدث خطأ في تحديث الرابط'
    }
  }
}

/**
 * دالة لجلب إحصائيات الربط التلقائي
 * @returns إحصائيات الربط
 */
export async function getAutoLinkStats() {
  try {
    const response = await fetch('/api/account-linking-settings')
    const data = await response.json()

    if (data.success) {
      const stats = {
        total_settings: data.data.length,
        enabled_settings: data.data.filter((s: any) => s.is_enabled).length,
        total_linked_records: data.data.reduce((sum: number, s: any) => sum + s.linked_records_count, 0)
      }

      return stats
    }

    return null
  } catch (error) {
    console.error('Error getting auto-link stats:', error)
    return null
  }
}
