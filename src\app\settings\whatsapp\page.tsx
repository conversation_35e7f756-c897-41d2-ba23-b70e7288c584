'use client'

import { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import {
  MessageSquare,
  Settings,
  Play,
  Square,
  Wifi,
  WifiOff,
  Send,
  Users,
  BarChart3,
  CheckCircle,
  AlertCircle,
  Clock,
  RefreshCw
} from 'lucide-react'

interface WhatsAppSettings {
  company: {
    id: number
    name: string
    phone: string
    email: string
    address: string
  }
  whatsapp: {
    enabled: boolean
    phone: string
    businessName: string
    sessionName: string
    autoReply: boolean
    businessHoursStart: string
    businessHoursEnd: string
    autoReplyMessage: string
  }
  contacts: {
    clients: number
    employees: number
  }
  stats: {
    messages_sent: number
    messages_delivered: number
    messages_failed: number
  }
}

export default function WhatsAppPage() {
  const [settings, setSettings] = useState<WhatsAppSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [message, setMessage] = useState('')
  const [whatsappStatus, setWhatsappStatus] = useState({
    connected: false,
    qrCode: null as string | null,
    status: 'disconnected',
    uptime: 0,
    lastActivity: null as string | null,
    isStarting: false,
    failureCount: 0,
    maxRetries: 3,
    lastError: null as string | null,
    canRetry: true
  })

  // رسالة اختبار
  const [testMessage, setTestMessage] = useState({
    phoneNumber: '',
    message: 'مرحباً، هذه رسالة حقيقية من نظام إدارة المحاماة - تم إرسالها عبر WhatsApp الفعلي (محسن بدون تكرار)',
    recipientName: ''
  })

  // حالة الإرسال
  const [sending, setSending] = useState(false)

  // مراجع للتحكم في التحديث
  const updateIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // دالة عرض الرسائل
  const showMessage = (text: string, type: 'success' | 'error' | 'info' = 'success') => {
    setMessage(text)
    setTimeout(() => setMessage(''), 5000)
  }

  // دالة إيقاف التحديث
  const stopUpdating = () => {
    if (updateIntervalRef.current) {
      clearInterval(updateIntervalRef.current)
      updateIntervalRef.current = null
      console.log('🛑 تم إيقاف التحديث الدوري')
    }
    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current)
      updateTimeoutRef.current = null
      console.log('🛑 تم إيقاف timeout التحديث')
    }
  }

  // جلب الإعدادات
  const fetchSettings = async () => {
    try {
      const response = await fetch('/api/whatsapp/settings')
      const result = await response.json()

      if (result.success) {
        setSettings(result.data)
        showMessage('تم تحميل الإعدادات بنجاح')
      } else {
        showMessage(result.error || 'فشل في جلب الإعدادات', 'error')
      }
    } catch (error) {
      console.error('خطأ في جلب الإعدادات:', error)
      showMessage('فشل في الاتصال بالخادم', 'error')
    } finally {
      setLoading(false)
    }
  }

  // حفظ الإعدادات
  const saveSettings = async () => {
    if (!settings) return

    setSaving(true)
    try {
      const response = await fetch('/api/whatsapp/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          companyName: settings.company.name,
          phone: settings.company.phone,
          email: settings.company.email,
          address: settings.company.address,
          whatsappPhone: settings.whatsapp.phone,
          whatsappBusinessName: settings.whatsapp.businessName,
          whatsappEnabled: settings.whatsapp.enabled,
          whatsappAutoReply: settings.whatsapp.autoReply,
          businessHoursStart: settings.whatsapp.businessHoursStart,
          businessHoursEnd: settings.whatsapp.businessHoursEnd,
          whatsappAutoReplyMessage: settings.whatsapp.autoReplyMessage
        })
      })

      const result = await response.json()

      if (result.success) {
        showMessage('تم حفظ الإعدادات بنجاح')
      } else {
        showMessage(result.error || 'فشل في حفظ الإعدادات', 'error')
      }
    } catch (error) {
      console.error('خطأ في حفظ الإعدادات:', error)
      showMessage('فشل في الاتصال بالخادم', 'error')
    } finally {
      setSaving(false)
    }
  }

  // بدء WhatsApp
  const startWhatsApp = async () => {
    try {
      showMessage('جاري بدء خدمة WhatsApp...', 'success')

      const response = await fetch('/api/whatsapp/control', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action: 'start' })
      })

      const result = await response.json()

      if (result.success) {
        showMessage('تم بدء خدمة WhatsApp بنجاح')
        // تفعيل التحديث السريع محلياً حتى تصل حالة الخادم
        setWhatsappStatus((prev) => ({
          ...prev,
          isStarting: true,
          status: 'connecting'
        }))
        // تحديث الحالة فوراً للحصول على QR Code إن وُجد
        updateWhatsAppStatus()
      } else {
        showMessage(result.error || 'فشل في بدء WhatsApp', 'error')
      }
    } catch (error) {
      console.error('خطأ في بدء WhatsApp:', error)
      showMessage('فشل في الاتصال بالخادم', 'error')
    }
  }

  // إيقاف WhatsApp
  const stopWhatsApp = async () => {
    try {
      const response = await fetch('/api/whatsapp/control', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action: 'stop' })
      })

      const result = await response.json()

      if (result.success) {
        // إعادة تعيين الحالة بالكامل لتكون متسقة مع القيم الافتراضية
        setWhatsappStatus({
          connected: false,
          qrCode: null,
          status: 'disconnected',
          uptime: 0,
          lastActivity: null,
          isStarting: false,
          failureCount: 0,
          maxRetries: 3,
          lastError: null,
          canRetry: true
        })
        showMessage('تم إيقاف خدمة WhatsApp')
      }
    } catch (error) {
      console.error('خطأ في إيقاف WhatsApp:', error)
    }
  }

  // إعادة تشغيل WhatsApp (إصلاح المشاكل)
  const restartWhatsApp = async () => {
    try {
      showMessage('جاري إعادة تشغيل WhatsApp...', 'success')

      // إيقاف أولاً
      await fetch('/api/whatsapp/control', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action: 'stop' })
      })

      // انتظار ثانيتين
      await new Promise(resolve => setTimeout(resolve, 2000))

      // بدء مرة أخرى
      const response = await fetch('/api/whatsapp/control', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action: 'start' })
      })

      const result = await response.json()

      if (result.success) {
        showMessage('تم إعادة تشغيل WhatsApp بنجاح')
        // تفعيل التحديث السريع محلياً حتى تصل حالة الخادم
        setWhatsappStatus((prev) => ({
          ...prev,
          isStarting: true,
          status: 'connecting'
        }))
        // تحديث الحالة فوراً للحصول على QR Code إن وُجد
        updateWhatsAppStatus()
      } else {
        showMessage(result.error || 'فشل في إعادة تشغيل WhatsApp', 'error')
      }
    } catch (error) {
      console.error('خطأ في إعادة تشغيل WhatsApp:', error)
      showMessage('فشل في إعادة تشغيل WhatsApp', 'error')
    }
  }

  // إعادة تعيين حالة الفشل
  const resetFailureState = async () => {
    try {
      showMessage('جاري إعادة تعيين حالة الفشل...')

      const response = await fetch('/api/whatsapp/control', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'reset' })
      })

      const result = await response.json()

      if (result.success) {
        showMessage('تم إعادة تعيين حالة الفشل بنجاح')
        updateWhatsAppStatus() // تحديث الحالة
      } else {
        showMessage(result.error || 'فشل في إعادة تعيين الحالة', 'error')
      }

    } catch (error) {
      showMessage('خطأ في إعادة تعيين الحالة', 'error')
    }
  }

  // إرسال رسالة اختبار
  const sendTestMessage = async () => {
    if (!testMessage.phoneNumber || !testMessage.message) {
      showMessage('يرجى إدخال رقم الهاتف والرسالة', 'error')
      return
    }

    setSending(true)
    try {
      const response = await fetch('/api/whatsapp/send-simple', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testMessage)
      })

      const result = await response.json()

      if (result.success) {
        showMessage('تم إرسال الرسالة الحقيقية بنجاح!')
        setTestMessage({
          phoneNumber: '',
          message: 'مرحباً، هذه رسالة حقيقية من نظام إدارة المحاماة - تم إرسالها عبر WhatsApp الفعلي (محسن بدون تكرار)',
          recipientName: ''
        })
      } else {
        showMessage(result.error || 'فشل في إرسال الرسالة', 'error')
      }
    } catch (error) {
      console.error('خطأ في إرسال الرسالة:', error)
      showMessage('فشل في الاتصال بالخادم', 'error')
    } finally {
      setSending(false)
    }
  }

  // تحديث حالة WhatsApp
  const updateWhatsAppStatus = async () => {
    try {
      const response = await fetch('/api/whatsapp/control')
      const result = await response.json()

      if (result.success) {
        const newStatus = {
          connected: result.data.connected,
          qrCode: result.data.qrCode,
          status: result.data.status,
          uptime: result.data.uptime || 0,
          lastActivity: result.data.lastActivity,
          isStarting: result.data.isStarting || false,
          failureCount: result.data.failureCount || 0,
          maxRetries: result.data.maxRetries || 3,
          lastError: result.data.lastError || null,
          canRetry: result.data.canRetry !== false
        }

        // تحديث الحالة
        setWhatsappStatus(newStatus)

        // إشعار في الكونسول للمتابعة
        console.log('🔄 تحديث حالة WhatsApp:', {
          connected: newStatus.connected,
          status: newStatus.status,
          hasQR: !!newStatus.qrCode
        })

        // إشعار المستخدم عند الاتصال
        if (newStatus.connected && !whatsappStatus.connected) {
          showMessage('تم الاتصال بـ WhatsApp بنجاح! 🎉')
          console.log('🎉 تم تأكيد الاتصال في الواجهة الأمامية!')
        }

        // إشعار عند إزالة QR Code
        if (!newStatus.qrCode && whatsappStatus.qrCode) {
          console.log('🔄 تم إزالة QR Code - تحديث الواجهة')
          showMessage('تم مسح QR Code - جاري الاتصال...', 'info')

          // تحديث سريع كل ثانية لمدة 15 ثانية للتأكد من الاتصال
          let quickCheckCount = 0;
          const quickCheck = setInterval(() => {
            updateWhatsAppStatus();
            quickCheckCount++;
            if (quickCheckCount >= 15 || newStatus.connected) {
              clearInterval(quickCheck);
            }
          }, 1000);
        }
      }
    } catch (error) {
      console.error('خطأ في تحديث حالة WhatsApp:', error)
    }
  }

  // تنسيق وقت التشغيل
  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }

  useEffect(() => {
    fetchSettings()

    // تحديث فوري
    updateWhatsAppStatus()

    return () => {}
  }, [])

  // تحديث ذكي بناءً على الحالة
  useEffect(() => {
    // إيقاف أي تحديث سابق
    stopUpdating()

    if (whatsappStatus.isStarting) {
      // تحديث كل ثانية أثناء التشغيل (لمدة دقيقة واحدة فقط)
      updateIntervalRef.current = setInterval(updateWhatsAppStatus, 1000)
      console.log('🔄 تحديث كل ثانية - جاري التشغيل')

      // إيقاف التحديث بعد دقيقة واحدة إذا لم يظهر QR Code أو يتصل
      updateTimeoutRef.current = setTimeout(() => {
        if (!whatsappStatus.connected && !whatsappStatus.qrCode) {
          stopUpdating()
          console.log('⏰ تم إيقاف التحديث - انتهت مهلة التشغيل (60 ثانية)')
          showMessage('انتهت مهلة التشغيل - لم يظهر QR Code', 'error')
        }
      }, 60000) // دقيقة واحدة

    } else if (whatsappStatus.qrCode && !whatsappStatus.connected) {
      // تحديث كل نصف ثانية عند وجود QR Code (لمدة 5 دقائق فقط)
      updateIntervalRef.current = setInterval(updateWhatsAppStatus, 500)
      console.log('🔄 تحديث سريع كل نصف ثانية - انتظار مسح QR Code')

      // إيقاف التحديث بعد 5 دقائق إذا لم يتم المسح
      updateTimeoutRef.current = setTimeout(() => {
        if (whatsappStatus.qrCode && !whatsappStatus.connected) {
          stopUpdating()
          console.log('⏰ تم إيقاف التحديث - انتهت مهلة QR Code (5 دقائق)')
          showMessage('انتهت مهلة QR Code - يرجى إعادة المحاولة', 'error')
        }
      }, 300000) // 5 دقائق

    } else if (whatsappStatus.connected) {
      // تحديث كل 30 ثانية عند الاتصال
      updateIntervalRef.current = setInterval(updateWhatsAppStatus, 30000)
      console.log('🔄 تحديث كل 30 ثانية - متصل')

    } else if (whatsappStatus.status === 'failed') {
      // لا تحديث عند الفشل
      console.log('🛑 لا تحديث - حالة فشل')
      stopUpdating()

    } else {
      // لا تحديث في الحالات الأخرى
      console.log('🛑 لا تحديث - حالة غير نشطة')
      stopUpdating()
    }

    return () => stopUpdating()
  }, [whatsappStatus.qrCode, whatsappStatus.connected, whatsappStatus.isStarting, whatsappStatus.status])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!settings) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-6">
            <p className="text-center text-gray-500">فشل في تحميل الإعدادات</p>
            <Button onClick={fetchSettings} className="mt-4 mx-auto block">
              إعادة المحاولة
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6" dir="rtl">
      {/* رسائل النظام */}
      {message && (
        <div className={`p-4 rounded-lg flex items-center gap-2 ${
          message.includes('فشل') || message.includes('خطأ')
            ? 'bg-red-50 text-red-800 border border-red-200'
            : 'bg-green-50 text-green-800 border border-green-200'
        }`}>
          {message.includes('فشل') || message.includes('خطأ') ? (
            <AlertCircle className="h-5 w-5" />
          ) : (
            <CheckCircle className="h-5 w-5" />
          )}
          {message}
        </div>
      )}

      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">إعدادات WhatsApp الحقيقي</h1>
          <p className="text-gray-600">إدارة خدمة WhatsApp الحقيقية - يرسل رسائل فعلية بدون تكرار</p>
        </div>

        <div className="flex items-center gap-4">
          <Badge variant={whatsappStatus.connected ? "default" : whatsappStatus.qrCode ? "secondary" : "outline"}>
            {whatsappStatus.connected ? (
              <>
                <Wifi className="h-4 w-4 ml-1" />
                متصل ✅
              </>
            ) : whatsappStatus.qrCode ? (
              <>
                <Clock className="h-4 w-4 ml-1" />
                انتظار المسح
              </>
            ) : (
              <>
                <WifiOff className="h-4 w-4 ml-1" />
                غير متصل
              </>
            )}
          </Badge>

          {whatsappStatus.uptime > 0 && (
            <Badge variant="outline">
              <Clock className="h-4 w-4 ml-1" />
              {formatUptime(whatsappStatus.uptime)}
            </Badge>
          )}

          <div className="flex gap-2">
            {whatsappStatus.connected ? (
              <Button onClick={stopWhatsApp} variant="destructive">
                <Square className="h-4 w-4 ml-2" />
                إيقاف
              </Button>
            ) : whatsappStatus.status === 'failed' ? (
              <Button onClick={resetFailureState} variant="outline">
                <RefreshCw className="h-4 w-4 ml-2" />
                إعادة تعيين
              </Button>
            ) : (
              <Button
                onClick={startWhatsApp}
                disabled={whatsappStatus.isStarting || !whatsappStatus.canRetry}
              >
                <Play className="h-4 w-4 ml-2" />
                {whatsappStatus.isStarting ? 'جاري البدء...' : 'بدء'}
              </Button>
            )}

            {whatsappStatus.status !== 'failed' && (
              <Button onClick={restartWhatsApp} variant="outline">
                <RefreshCw className="h-4 w-4 ml-2" />
                إعادة تشغيل
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* معلومات النظام الحقيقي */}
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center gap-2 mb-2">
          <CheckCircle className="h-5 w-5 text-green-600" />
          <h3 className="font-semibold text-green-800">نظام WhatsApp حقيقي 100% - محسن بدون تكرار</h3>
        </div>
        <p className="text-green-700 text-sm">
          📱 يستخدم whatsapp-web.js الحقيقي • ✅ QR Code من WhatsApp فعلياً • 📤 إرسال رسائل حقيقية • 🛡️ حماية من التكرار
        </p>
      </div>

      {/* عرض حالة الفشل */}
      {whatsappStatus.status === 'failed' && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <AlertCircle className="h-5 w-5 text-red-600" />
            <h3 className="font-semibold text-red-800">فشل في تشغيل WhatsApp</h3>
          </div>
          <div className="space-y-2">
            <p className="text-red-700 text-sm">
              تم الوصول للحد الأقصى من المحاولات ({whatsappStatus.failureCount}/{whatsappStatus.maxRetries})
            </p>
            {whatsappStatus.lastError && (
              <p className="text-red-600 text-xs bg-red-100 p-2 rounded">
                آخر خطأ: {whatsappStatus.lastError}
              </p>
            )}
            <p className="text-red-700 text-sm">
              اضغط "إعادة تعيين" لمحاولة التشغيل مرة أخرى
            </p>
          </div>
        </div>
      )}

      {/* عرض حالة التشغيل */}
      {whatsappStatus.isStarting && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center gap-2">
            <RefreshCw className="h-5 w-5 text-blue-600 animate-spin" />
            <p className="text-blue-800 font-medium">
              جاري تشغيل WhatsApp... (المحاولة {whatsappStatus.failureCount + 1}/{whatsappStatus.maxRetries})
            </p>
          </div>
        </div>
      )}

      <Tabs defaultValue="connection" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="connection">
            <MessageSquare className="h-4 w-4 ml-2" />
            الاتصال
          </TabsTrigger>
          <TabsTrigger value="send">
            <Send className="h-4 w-4 ml-2" />
            إرسال
          </TabsTrigger>
          <TabsTrigger value="settings">
            <Settings className="h-4 w-4 ml-2" />
            الإعدادات
          </TabsTrigger>
          <TabsTrigger value="stats">
            <BarChart3 className="h-4 w-4 ml-2" />
            الإحصائيات
          </TabsTrigger>
        </TabsList>

        <TabsContent value="connection">
          <Card>
            <CardHeader>
              <CardTitle>حالة الاتصال</CardTitle>
            </CardHeader>
            <CardContent>
              {whatsappStatus.qrCode ? (
                <div className="text-center space-y-4">
                  <p className="text-lg font-semibold">امسح هذا الكود من تطبيق WhatsApp على هاتفك:</p>
                  <img
                    src={whatsappStatus.qrCode}
                    alt="QR Code"
                    className="mx-auto border rounded-lg shadow-lg"
                  />
                  <p className="text-sm text-gray-600">
                    افتح WhatsApp → الإعدادات → الأجهزة المرتبطة → ربط جهاز
                  </p>
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mt-4">
                    <p className="text-blue-700 text-sm mb-2">
                      🔄 بعد مسح الكود من هاتفك:
                    </p>
                    <div className="flex gap-2">
                      <Button
                        onClick={() => {
                          updateWhatsAppStatus()
                          showMessage('جاري تحديث الحالة...')
                        }}
                        variant="outline"
                        className="bg-blue-50 hover:bg-blue-100 border-blue-200"
                      >
                        <RefreshCw className="h-4 w-4 ml-2" />
                        تحديث الحالة فوراً
                      </Button>
                      <Button
                        onClick={() => {
                          // تحديث متكرر كل ثانية لمدة 10 ثوان
                          let count = 0;
                          const quickUpdate = setInterval(() => {
                            updateWhatsAppStatus();
                            count++;
                            if (count >= 10 || whatsappStatus.connected) {
                              clearInterval(quickUpdate);
                            }
                          }, 1000);
                          showMessage('جاري المراقبة السريعة للاتصال...')
                        }}
                        variant="outline"
                        className="bg-green-50 hover:bg-green-100 border-green-200"
                      >
                        <Clock className="h-4 w-4 ml-2" />
                        مراقبة سريعة
                      </Button>
                    </div>
                  </div>
                </div>
              ) : whatsappStatus.connected ? (
                <div className="text-center space-y-4">
                  <div className="text-green-600">
                    <Wifi className="h-16 w-16 mx-auto mb-4" />
                    <p className="text-xl font-bold">WhatsApp متصل وجاهز! 🎉</p>
                    <p className="text-sm text-gray-600">يمكنك الآن إرسال الرسائل الحقيقية</p>
                    <p className="text-xs text-green-600 font-medium">
                      ✅ تم الاتصال بنجاح - النظام جاهز للاستخدام
                    </p>
                    {whatsappStatus.uptime > 0 && (
                      <p className="text-xs text-gray-500 mt-2">
                        مدة الاتصال: {formatUptime(whatsappStatus.uptime)}
                      </p>
                    )}
                    {whatsappStatus.lastActivity && (
                      <p className="text-xs text-gray-500">
                        آخر نشاط: {new Date(whatsappStatus.lastActivity).toLocaleString('ar-SA')}
                      </p>
                    )}
                  </div>
                  <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                    <p className="text-green-700 text-sm">
                      ✅ الاتصال مؤكد - يمكنك إرسال رسائل حقيقية الآن
                    </p>
                  </div>
                </div>
              ) : (
                <div className="text-center space-y-4">
                  <div className="text-gray-500">
                    <WifiOff className="h-16 w-16 mx-auto mb-4" />
                    <p className="text-xl">WhatsApp غير متصل</p>
                    <p className="text-sm">اضغط "بدء" للاتصال أو "إعادة تشغيل" لحل المشاكل</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="send">
          <Card>
            <CardHeader>
              <CardTitle>إرسال رسالة حقيقية (WhatsApp فعلي)</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="testPhone">رقم الهاتف</Label>
                <Input
                  id="testPhone"
                  placeholder="+967771234567"
                  value={testMessage.phoneNumber}
                  onChange={(e) => setTestMessage({
                    ...testMessage,
                    phoneNumber: e.target.value
                  })}
                />
              </div>
              <div>
                <Label htmlFor="testName">اسم المستلم (اختياري)</Label>
                <Input
                  id="testName"
                  placeholder="اسم المستلم"
                  value={testMessage.recipientName}
                  onChange={(e) => setTestMessage({
                    ...testMessage,
                    recipientName: e.target.value
                  })}
                />
              </div>
              <div>
                <Label htmlFor="testMessage">الرسالة</Label>
                <Textarea
                  id="testMessage"
                  value={testMessage.message}
                  onChange={(e) => setTestMessage({
                    ...testMessage,
                    message: e.target.value
                  })}
                />
              </div>

              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <p className="text-red-700 text-sm font-semibold">
                  ⚠️ تحذير: هذه رسالة حقيقية ستصل للرقم المحدد فعلياً عبر WhatsApp
                </p>
              </div>

              <Button
                onClick={sendTestMessage}
                disabled={sending || !whatsappStatus.connected}
                className="w-full bg-green-600 hover:bg-green-700"
              >
                <Send className="h-4 w-4 ml-2" />
                {sending ? 'جاري الإرسال الحقيقي...' : 'إرسال رسالة حقيقية (WhatsApp فعلي)'}
              </Button>
              {!whatsappStatus.connected && (
                <p className="text-sm text-amber-600 text-center">
                  يجب الاتصال بـ WhatsApp أولاً لإرسال الرسائل
                </p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>إعدادات الشركة</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="companyName">اسم الشركة</Label>
                  <Input
                    id="companyName"
                    value={settings.company.name}
                    onChange={(e) => setSettings({
                      ...settings,
                      company: { ...settings.company, name: e.target.value }
                    })}
                  />
                </div>
                <div>
                  <Label htmlFor="phone">رقم الهاتف</Label>
                  <Input
                    id="phone"
                    value={settings.company.phone}
                    onChange={(e) => setSettings({
                      ...settings,
                      company: { ...settings.company, phone: e.target.value }
                    })}
                  />
                </div>
                <div>
                  <Label htmlFor="email">البريد الإلكتروني</Label>
                  <Input
                    id="email"
                    type="email"
                    value={settings.company.email}
                    onChange={(e) => setSettings({
                      ...settings,
                      company: { ...settings.company, email: e.target.value }
                    })}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>إعدادات WhatsApp</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="whatsappEnabled">تفعيل خدمة WhatsApp</Label>
                  <Switch
                    id="whatsappEnabled"
                    checked={settings.whatsapp.enabled}
                    onCheckedChange={(checked) => setSettings({
                      ...settings,
                      whatsapp: { ...settings.whatsapp, enabled: checked }
                    })}
                  />
                </div>
                <div>
                  <Label htmlFor="whatsappPhone">رقم WhatsApp</Label>
                  <Input
                    id="whatsappPhone"
                    value={settings.whatsapp.phone}
                    onChange={(e) => setSettings({
                      ...settings,
                      whatsapp: { ...settings.whatsapp, phone: e.target.value }
                    })}
                  />
                </div>
                <div>
                  <Label htmlFor="businessName">اسم العمل</Label>
                  <Input
                    id="businessName"
                    value={settings.whatsapp.businessName}
                    onChange={(e) => setSettings({
                      ...settings,
                      whatsapp: { ...settings.whatsapp, businessName: e.target.value }
                    })}
                  />
                </div>
                <div>
                  <Label htmlFor="autoReplyMessage">رسالة الرد التلقائي</Label>
                  <Textarea
                    id="autoReplyMessage"
                    value={settings.whatsapp.autoReplyMessage}
                    onChange={(e) => setSettings({
                      ...settings,
                      whatsapp: { ...settings.whatsapp, autoReplyMessage: e.target.value }
                    })}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="flex justify-end">
            <Button onClick={saveSettings} disabled={saving}>
              {saving ? 'جاري الحفظ...' : 'حفظ الإعدادات'}
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="stats">
          <div className="grid gap-6 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="h-5 w-5 ml-2" />
                  جهات الاتصال
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>العملاء:</span>
                    <span className="font-semibold">{settings.contacts.clients}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>الموظفين:</span>
                    <span className="font-semibold">{settings.contacts.employees}</span>
                  </div>
                  <div className="flex justify-between border-t pt-2">
                    <span>الإجمالي:</span>
                    <span className="font-semibold">
                      {settings.contacts.clients + settings.contacts.employees}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Send className="h-5 w-5 ml-2" />
                  الرسائل اليوم
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>مرسلة:</span>
                    <span className="font-semibold text-green-600">
                      {settings.stats.messages_sent}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>مسلمة:</span>
                    <span className="font-semibold text-blue-600">
                      {settings.stats.messages_delivered}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>فاشلة:</span>
                    <span className="font-semibold text-red-600">
                      {settings.stats.messages_failed}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="h-5 w-5 ml-2" />
                  معدل النجاح
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">
                    {settings.stats.messages_sent > 0
                      ? Math.round((settings.stats.messages_delivered / settings.stats.messages_sent) * 100)
                      : 0
                    }%
                  </div>
                  <p className="text-sm text-gray-600">معدل التسليم</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
