/**
 * سكريبت لتحديث الموظفين الموجودين وإضافة البيانات المفقودة
 * employee_number و account_id للموظفين الذين لا يملكون هذه البيانات
 */

const { Client } = require('pg');

// إعدادات قاعدة البيانات mohammidev
const dbConfig = {
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'mohammidev'
};

async function updateExistingEmployees() {
  const client = new Client(dbConfig);

  try {
    await client.connect();
    console.log('✅ متصل بقاعدة البيانات mohammidev');

    // 1. فحص الموظفين الموجودين
    console.log('\n🔍 فحص الموظفين الموجودين...');
    const employeesResult = await client.query(`
      SELECT id, name, employee_number, account_id, position, department_id
      FROM employees
      ORDER BY id
    `);

    const employees = employeesResult.rows;
    console.log(`📊 عدد الموظفين الموجودين: ${employees.length}`);

    if (employees.length === 0) {
      console.log('ℹ️ لا يوجد موظفين في قاعدة البيانات');
      return;
    }

    // 2. تحليل البيانات المفقودة
    const missingEmployeeNumber = employees.filter(emp => !emp.employee_number);
    const missingAccountId = employees.filter(emp => !emp.account_id);

    console.log(`\n📋 تحليل البيانات المفقودة:`);
    console.log(`   - موظفين بدون رقم موظف: ${missingEmployeeNumber.length}`);
    console.log(`   - موظفين بدون رقم حساب: ${missingAccountId.length}`);

    // 3. تحديث أرقام الموظفين المفقودة
    if (missingEmployeeNumber.length > 0) {
      console.log('\n🔢 تحديث أرقام الموظفين المفقودة...');
      
      for (const employee of missingEmployeeNumber) {
        // إنشاء رقم موظف تلقائي بناءً على المعرف والمنصب
        let employeeNumber;
        
        if (employee.position) {
          // إنشاء رقم بناءً على المنصب
          const positionCode = getPositionCode(employee.position);
          employeeNumber = `${positionCode}${String(employee.id).padStart(3, '0')}`;
        } else {
          // رقم افتراضي
          employeeNumber = `EMP${String(employee.id).padStart(3, '0')}`;
        }

        await client.query(
          'UPDATE employees SET employee_number = $1 WHERE id = $2',
          [employeeNumber, employee.id]
        );

        console.log(`   ✅ تم تحديث الموظف ${employee.name}: ${employeeNumber}`);
      }
    }

    // 4. تحديث أرقام الحسابات المفقودة
    if (missingAccountId.length > 0) {
      console.log('\n💰 تحديث أرقام الحسابات المفقودة...');
      
      for (const employee of missingAccountId) {
        // إنشاء رقم حساب تلقائي بناءً على القسم والمعرف
        let accountId;
        
        if (employee.department_id) {
          // رقم حساب بناءً على القسم (مثال: قسم 1 = حسابات 5100-5199)
          const baseAccount = 5100 + (employee.department_id * 10);
          accountId = baseAccount + (employee.id % 10);
        } else {
          // رقم حساب افتراضي للموظفين العامين
          accountId = 5000 + employee.id;
        }

        await client.query(
          'UPDATE employees SET account_id = $1 WHERE id = $2',
          [accountId, employee.id]
        );

        console.log(`   ✅ تم تحديث حساب الموظف ${employee.name}: ${accountId}`);
      }
    }

    // 5. التحقق النهائي
    console.log('\n🔍 التحقق النهائي من التحديثات...');
    const finalCheck = await client.query(`
      SELECT 
        COUNT(*) as total_employees,
        COUNT(employee_number) as employees_with_number,
        COUNT(account_id) as employees_with_account,
        COUNT(*) - COUNT(employee_number) as missing_numbers,
        COUNT(*) - COUNT(account_id) as missing_accounts
      FROM employees
    `);

    const stats = finalCheck.rows[0];
    console.log('\n📊 إحصائيات نهائية:');
    console.log(`   - إجمالي الموظفين: ${stats.total_employees}`);
    console.log(`   - موظفين لديهم رقم موظف: ${stats.employees_with_number}`);
    console.log(`   - موظفين لديهم رقم حساب: ${stats.employees_with_account}`);
    console.log(`   - موظفين بدون رقم موظف: ${stats.missing_numbers}`);
    console.log(`   - موظفين بدون رقم حساب: ${stats.missing_accounts}`);

    // 6. عرض عينة من البيانات المحدثة
    console.log('\n📋 عينة من البيانات المحدثة:');
    const sampleResult = await client.query(`
      SELECT id, name, employee_number, account_id, position
      FROM employees
      ORDER BY id
      LIMIT 5
    `);

    sampleResult.rows.forEach(emp => {
      console.log(`   - ${emp.name}: رقم الموظف=${emp.employee_number}, رقم الحساب=${emp.account_id}`);
    });

    console.log('\n🎉 تم تحديث بيانات الموظفين الموجودين بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في تحديث بيانات الموظفين:', error.message);
    throw error;
  } finally {
    await client.end();
  }
}

/**
 * دالة لإنشاء رمز المنصب
 */
function getPositionCode(position) {
  const positionCodes = {
    'محامي رسمي': 'LAW',
    'محامي متدرب': 'TRN',
    'مساعد قانوني': 'AST',
    'سكرتير': 'SEC',
    'محاسب': 'ACC',
    'مدير': 'MGR',
    'موظف إداري': 'ADM',
    'مستشار قانوني': 'CON',
    'رئيس قسم': 'HOD',
    'مدير عام': 'GM'
  };

  return positionCodes[position] || 'EMP';
}

/**
 * دالة لإنشاء سكريبت تحديث مخصص
 */
async function createCustomUpdateScript() {
  console.log('\n📝 إنشاء سكريبت تحديث مخصص...');
  
  const customScript = `
-- سكريبت SQL لتحديث الموظفين الموجودين
-- يمكن تشغيله مباشرة في قاعدة البيانات

-- تحديث أرقام الموظفين المفقودة
UPDATE employees 
SET employee_number = CASE 
  WHEN position = 'محامي رسمي' THEN 'LAW' || LPAD(id::text, 3, '0')
  WHEN position = 'محامي متدرب' THEN 'TRN' || LPAD(id::text, 3, '0')
  WHEN position = 'مساعد قانوني' THEN 'AST' || LPAD(id::text, 3, '0')
  WHEN position = 'سكرتير' THEN 'SEC' || LPAD(id::text, 3, '0')
  WHEN position = 'محاسب' THEN 'ACC' || LPAD(id::text, 3, '0')
  WHEN position = 'مدير' THEN 'MGR' || LPAD(id::text, 3, '0')
  ELSE 'EMP' || LPAD(id::text, 3, '0')
END
WHERE employee_number IS NULL OR employee_number = '';

-- تحديث أرقام الحسابات المفقودة
UPDATE employees 
SET account_id = CASE 
  WHEN department_id IS NOT NULL THEN 5100 + (department_id * 10) + (id % 10)
  ELSE 5000 + id
END
WHERE account_id IS NULL;

-- التحقق من النتائج
SELECT 
  COUNT(*) as total_employees,
  COUNT(employee_number) as employees_with_number,
  COUNT(account_id) as employees_with_account
FROM employees;
`;

  require('fs').writeFileSync('database/update_employees_manual.sql', customScript);
  console.log('✅ تم إنشاء ملف database/update_employees_manual.sql');
}

// تشغيل السكريبت
if (require.main === module) {
  updateExistingEmployees()
    .then(() => createCustomUpdateScript())
    .catch(console.error);
}

module.exports = { updateExistingEmployees, getPositionCode };
