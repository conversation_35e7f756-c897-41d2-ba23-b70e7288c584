// سكربت ترحيل لإزالة case_id و case_number والاعتماد على cost_center_id في journal_entry_details
// يعمل وفق إعدادات routing.config.json ويختار قاعدة البيانات حسب PORT

const { Pool } = require('pg')
const fs = require('fs')
const path = require('path')

let routingConfig = null
try {
  const configPath = path.join(process.cwd(), 'routing.config.json')
  const configData = fs.readFileSync(configPath, 'utf8')
  routingConfig = JSON.parse(configData)
} catch (error) {
  console.warn('⚠️ لم يُعثر على routing.config.json، سيتم استخدام الإعدادات الافتراضية')
}

const getDatabaseConfig = () => {
  const port = process.env.PORT || '7443'

  if (routingConfig && routingConfig.routes && routingConfig.routes[port]) {
    const route = routingConfig.routes[port]
    const defaultConfig = routingConfig.default_config || {}
    return {
      database: route.database,
      user: defaultConfig.db_user || 'postgres',
      host: defaultConfig.db_host || 'localhost',
      password: process.env.DB_PASSWORD || defaultConfig.db_password || 'yemen123',
      port: defaultConfig.db_port || 5432,
    }
  }

  console.warn(`⚠️ لم يتم العثور على توجيه للمنفذ ${port}، سيتم استخدام الإعدادات الافتراضية`)
  return {
    database: 'mohammi',
    user: 'postgres',
    host: 'localhost',
    password: process.env.DB_PASSWORD || 'yemen123',
    port: 5432,
  }
}

const dbConfig = getDatabaseConfig()
const pool = new Pool({
  ...dbConfig,
  ssl: false,
  max: 5,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 5000,
})

async function query(text, params) {
  const client = await pool.connect()
  try {
    await client.query('SET client_encoding TO UTF8')
    return await client.query(text, params)
  } finally {
    client.release()
  }
}

async function columnExists(table, column) {
  const res = await query(
    `SELECT 1 FROM information_schema.columns WHERE table_name = $1 AND column_name = $2`,
    [table, column]
  )
  return res.rowCount > 0
}

async function tableExists(table) {
  const res = await query(
    `SELECT 1 FROM information_schema.tables WHERE table_name = $1 AND table_schema = 'public'`,
    [table]
  )
  return res.rowCount > 0
}

async function indexExists(indexName) {
  const res = await query(
    `SELECT 1 FROM pg_indexes WHERE schemaname = 'public' AND indexname = $1`,
    [indexName]
  )
  return res.rowCount > 0
}

async function fkExists(table, constraint) {
  const res = await query(
    `SELECT 1 FROM information_schema.table_constraints WHERE table_name = $1 AND constraint_name = $2 AND constraint_type = 'FOREIGN KEY'`,
    [table, constraint]
  )
  return res.rowCount > 0
}

async function migrate() {
  // تأكد من وجود الجدول
  if (!(await tableExists('journal_entry_details'))) {
    console.log('⚠️ جدول journal_entry_details غير موجود. لا حاجة للترحيل.')
    return
  }

  // إضافة عمود cost_center_id إن لم يكن موجودًا
  if (!(await columnExists('journal_entry_details', 'cost_center_id'))) {
    await query(`ALTER TABLE journal_entry_details ADD COLUMN cost_center_id INTEGER`)
    console.log('✅ تم إضافة العمود cost_center_id')
  } else {
    console.log('ℹ️ العمود cost_center_id موجود مسبقًا')
  }

  // حذف الأعمدة القديمة إن وجدت
  const removableColumns = ['case_id', 'case_number']
  for (const col of removableColumns) {
    if (await columnExists('journal_entry_details', col)) {
      try {
        await query(`ALTER TABLE journal_entry_details DROP COLUMN ${col}`)
        console.log(`✅ تم حذف العمود ${col}`)
      } catch (e) {
        console.warn(`⚠️ تعذر حذف العمود ${col}:`, e.message)
      }
    } else {
      console.log(`ℹ️ العمود ${col} غير موجود، تم تجاوزه`)
    }
  }

  // إنشاء فهرس على cost_center_id إن لم يكن موجودًا
  if (!(await indexExists('idx_jed_cost_center_id'))) {
    await query(`CREATE INDEX idx_jed_cost_center_id ON journal_entry_details(cost_center_id)`) 
    console.log('✅ تم إنشاء الفهرس idx_jed_cost_center_id')
  } else {
    console.log('ℹ️ الفهرس idx_jed_cost_center_id موجود مسبقًا')
  }

  // إضافة FK اختياري لجدول cost_centers إن كان موجودًا وليس مضافًا مسبقًا
  const hasCostCenters = await tableExists('cost_centers')
  if (hasCostCenters) {
    // إزالة أي FK قديم باسم مختلف يخص case_id لتجنب التعارض (تجاهل الخطأ إن وجد)
    try { await query(`ALTER TABLE journal_entry_details DROP CONSTRAINT IF EXISTS journal_entry_details_case_id_fkey`) } catch {}

    if (!(await fkExists('journal_entry_details', 'journal_entry_details_cost_center_id_fkey'))) {
      try {
        await query(`ALTER TABLE journal_entry_details ADD CONSTRAINT journal_entry_details_cost_center_id_fkey FOREIGN KEY (cost_center_id) REFERENCES cost_centers(id) ON DELETE SET NULL`)
        console.log('✅ تم إضافة الربط الخارجي مع cost_centers')
      } catch (e) {
        console.warn('⚠️ تعذر إضافة FK لـ cost_center_id:', e.message)
      }
    } else {
      console.log('ℹ️ FK cost_center_id موجود مسبقًا')
    }
  } else {
    console.log('ℹ️ جدول cost_centers غير موجود. تم تجاوز إضافة FK')
  }
}

async function main() {
  try {
    await migrate()
    console.log('🎉 اكتمل ترحيل cost_center_id بنجاح')
    process.exit(0)
  } catch (err) {
    console.error('❌ فشل تنفيذ الترحيل:', err)
    process.exit(1)
  }
}

main()
