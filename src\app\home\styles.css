/* تصميم موحد للصفحة الرئيسية */
.tafahum-page {
  background: linear-gradient(135deg, #333333 0%, #171717 100%);
  color: white;
  min-height: 100vh;
}

/* تصميم الهيدر */
.tafahum-header {
  background: linear-gradient(135deg, #333333 0%, #171717 100%) !important;
  border-bottom: 1px solid rgba(234, 179, 8, 0.2);
}

/* تصميم الأزرار */
.tafahum-btn {
  background: linear-gradient(to right, #eab308, #f59e0b);
  color: #1f2937;
  border: none;
  border-radius: 0.75rem;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.tafahum-btn:hover {
  background: linear-gradient(to right, #f59e0b, #d97706);
  transform: translateY(-2px);
  box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.2);
}

.tafahum-btn-outline {
  background: transparent;
  color: #eab308;
  border: 2px solid #eab308;
  border-radius: 0.75rem;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.tafahum-btn-outline:hover {
  background: #eab308;
  color: #1f2937;
  transform: translateY(-2px);
}

/* تصميم روابط التنقل */
.tafahum-nav-link {
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
}

.tafahum-nav-link:hover {
  color: #fbbf24;
  transform: scale(1.05);
}

.tafahum-nav-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(to right, #eab308, #f59e0b);
  transition: width 0.3s ease;
}

.tafahum-nav-link:hover::after {
  width: 100%;
}

/* Hero Section */
.hero {
  position: relative;
  background: linear-gradient(135deg, #333333 0%, #171717 100%);
  color: white;
  overflow: hidden;
}

.hero-pattern {
  position: absolute;
  inset: 0;
  opacity: 0.1;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.4' fill-rule='evenodd'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/svg%3E");
}

.hero-content {
  position: relative;
  z-index: 10;
}

.hero-badge {
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

/* Stats */
.stat-item {
  transition: transform 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
}

/* Image Frame */
.image-frame {
  position: relative;
  z-index: 1;
}

.image-frame::before {
  content: '';
  position: absolute;
  top: -20px;
  right: -20px;
  width: 100%;
  height: 100%;
  border: 2px solid rgba(59, 130, 246, 0.5);
  border-radius: 1rem;
  z-index: -1;
  transition: all 0.3s ease;
}

.image-frame:hover::before {
  top: -15px;
  right: -15px;
}

/* Experience Badge */
.experience-badge {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.experience-badge:hover {
  transform: scale(1.05);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Wave Divider */
.wave-divider {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  overflow: hidden;
  line-height: 0;
}

.wave-divider svg {
  position: relative;
  display: block;
  width: calc(100% + 1.3px);
  height: 150px;
}

.wave-divider .shape-fill {
  fill: #FFFFFF;
}

/* Responsive Adjustments */
@media (max-width: 1024px) {
  .hero-content {
    text-align: center;
  }
  
  .hero-buttons {
    justify-content: center;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 640px) {
  .hero {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }
  
  .hero h1 {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .experience-badge {
    position: static !important;
    margin-top: 2rem;
    margin-left: auto;
    margin-right: auto;
  }
}
