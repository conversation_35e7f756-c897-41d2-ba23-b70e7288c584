"use client";

import { useEffect, useState } from 'react';
import { ChatWidget } from '@/components/ChatWidget';
import { LegalLibrarySectionNew } from './components/legal-library-section-new';
import { AnnouncementsBar } from './components/announcements-bar';
import { MapSection } from './components/map-section';
import { ToasterClient } from './components/toaster-client';
import './styles.css';

// Interfaces
export interface CompanyData {
  id: number;
  name: string;
  legal_name: string;
  description: string;
  address: string;
  city: string;
  country: string;
  phone: string;
  email: string;
  website: string;
  logo_url: string;
  logo_image_url: string;
  established_date: string;
  registration_number: string;
  legal_form: string;
  capital: number;
  tax_number: string;
  is_active: boolean;
  working_hours?: string;
  latitude?: number;
  longitude?: number;
}

type Stats = {
  clients: number;
  issues: number;
  employees: number;
  completedIssues: number;
  newIssues: number;
  courts: number;
  successRate: number;
  experienceYears: number;
  legalDocuments?: number;
} | null;

// Import client components directly
import { Header as HeaderComponent } from './components/header';
import { HeroSection } from './components/hero-section';
import { ServicesSection } from './components/services-section';
import { TestimonialsSection } from './components/testimonials-section';
import { Footer } from './components/footer';

export default function HomePage() {
  const [companyData, setCompanyData] = useState<CompanyData | null>(null);
  const [stats, setStats] = useState<Stats>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    let cancelled = false;
    async function load() {
      try {
        const [companyRes, statsRes] = await Promise.all([
          fetch('/api/company', { cache: 'no-store' }),
          fetch('/api/stats', { cache: 'no-store' }),
        ]);

        if (!cancelled) {
          if (companyRes.ok) {
            const cj = await companyRes.json();
            if (cj.success && Array.isArray(cj.data) && cj.data.length > 0) {
              setCompanyData(cj.data[0] as CompanyData);
            }
          }
          if (statsRes.ok) {
            const sj = await statsRes.json();
            if (sj.success) setStats(sj.data as Stats);
          }
        }
      } catch {
        // تجاهل الأخطاء لإبقاء الصفحة تعمل
      } finally {
        if (!cancelled) setLoading(false);
      }
    }
    load();
    return () => { cancelled = true; };
  }, []);

  const safeCompanyData: CompanyData = companyData ?? {
    id: 0,
    name: 'مؤسستنا القانونية',
    legal_name: 'المؤسسة القانونية',
    description: 'خدمات قانونية متكاملة.',
    address: 'اليمن - صنعاء',
    city: 'صنعاء',
    country: 'اليمن',
    phone: '+967-*********',
    email: '<EMAIL>',
    website: 'https://example.com',
    logo_url: '/logo.png',
    logo_image_url: '/logo.png',
    established_date: new Date().toISOString(),
    registration_number: '',
    legal_form: '',
    capital: 0,
    tax_number: '',
    is_active: true,
  };

  if (loading && !companyData) {
    return (
      <div dir="rtl" className="min-h-screen flex items-center justify-center text-white" style={{ background: 'linear-gradient(135deg, #333333 0%, #**********%)' }}>
        جاري التحميل...
      </div>
    );
  }

  return (
    <div
      dir="rtl"
      className="min-h-screen text-white"
      style={{
        background: 'linear-gradient(135deg, #333333 0%, #**********%)',
        minHeight: '100vh'
      }}
    >
      <ToasterClient />

      {/* يمكن إضافة شريط تنبيه لاحقاً عند الحاجة */}
      {/* Announcements Bar */}
      <AnnouncementsBar />

      {/* Header */}
      <HeaderComponent companyData={safeCompanyData as any} />

      <main>
        {/* Hero Section */}
        <HeroSection companyData={safeCompanyData as any} stats={stats as any} />

        {/* Services Section */}
        <section id="services" className="py-16" style={{ background: 'linear-gradient(135deg, #333333 0%, #**********%)' }}>
          <ServicesSection />
        </section>

        {/* Legal Library Section */}
        <LegalLibrarySectionNew />

        {/* Testimonials Section */}
        <section className="py-16" style={{ background: 'linear-gradient(135deg, #333333 0%, #**********%)' }}>
          <TestimonialsSection />
        </section>

        {/* Map Section */}
        <MapSection companyData={safeCompanyData as any} />

      </main>

      {/* Footer */}
      <Footer companyData={safeCompanyData as any} />

      {/* Chat Widget */}
      <ChatWidget />
    </div>
  );
}