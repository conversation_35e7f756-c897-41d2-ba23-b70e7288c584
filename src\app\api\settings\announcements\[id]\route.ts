import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// DELETE - حذف إعلان
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    
    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, error: 'معرف الإعلان غير صحيح' },
        { status: 400 }
      )
    }

    await query('DELETE FROM announcements WHERE id = $1', [id])

    return NextResponse.json({
      success: true,
      message: 'تم حذف الإعلان بنجاح'
    })
  } catch (error) {
    console.error('Error deleting announcement:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الإعلان' },
      { status: 500 }
    )
  }
}

// PUT - تحديث إعلان
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    const body = await request.json()
    const { title, content, is_active } = body
    
    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, error: 'معرف الإعلان غير صحيح' },
        { status: 400 }
      )
    }

    const result = await query(`
      UPDATE announcements 
      SET title = $1, content = $2, is_active = $3, updated_date = NOW()
      WHERE id = $4
      RETURNING *
    `, [title, content, is_active, id])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الإعلان غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم تحديث الإعلان بنجاح'
    })
  } catch (error) {
    console.error('Error updating announcement:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الإعلان' },
      { status: 500 }
    )
  }
}