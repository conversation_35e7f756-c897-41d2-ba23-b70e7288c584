import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET /api/follows/services-for-case?case_id=.. : جلب الخدمات المخصصة للقضية من صفحة توزيع القضايا
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const caseId = searchParams.get('case_id')
    if (!caseId) {
      return NextResponse.json({ success: false, error: 'case_id مطلوب' }, { status: 400 })
    }

    const sql = `
      SELECT DISTINCT sd.service_id AS id, s.name
      FROM case_distribution cd
      JOIN service_distributions sd ON cd.id = sd.case_distribution_id
      JOIN services s ON s.id = sd.service_id
      WHERE cd.issue_id = $1
      ORDER BY s.name
    `
    const result = await query(sql, [caseId])

    return NextResponse.json({ success: true, data: result.rows })
  } catch (error) {
    console.error('Error fetching services for case:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب خدمات القضية' },
      { status: 500 }
    )
  }
}
