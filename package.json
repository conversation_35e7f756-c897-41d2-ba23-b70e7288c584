{"name": "legal-system", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev -p 3300", "dev:mohammi": "next dev -p 3000", "dev:rubaie": "next dev -p 3001", "start": "next start -p 3300", "advanced": "node advanced-unified-server.js"}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-toast": "^1.2.15", "bcryptjs": "^3.0.2", "chardet": "^2.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "iconv-lite": "^0.7.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.468.0", "mammoth": "^1.10.0", "next": "15.1.3", "pdf-parse": "^1.1.1", "pg": "^8.16.3", "qrcode": "^1.5.4", "qrcode-terminal": "^0.12.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.6.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "whatsapp-web.js": "^1.34.0"}, "devDependencies": {"@types/node": "^22.10.2", "@types/pg": "^8.11.10", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-config-next": "15.1.3", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "^5.7.2"}}