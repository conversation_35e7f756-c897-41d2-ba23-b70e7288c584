'use client'

import { useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { MessageSquare, RefreshCw, Settings, AlertTriangle } from 'lucide-react'
import MainLayout from '@/components/layout/main-layout'

export default function WhatsAppError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    console.error('خطأ في إعدادات WhatsApp:', error)
  }, [error])

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-xl text-gray-900 flex items-center justify-center">
              <MessageSquare className="h-5 w-5 ml-2" />
              خطأ في إعدادات WhatsApp
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-600 text-center">
              حدث خطأ أثناء تحميل إعدادات WhatsApp. قد يكون السبب:
            </p>
            
            <ul className="text-sm text-gray-600 space-y-1 text-right">
              <li>• مشكلة في الاتصال بالخادم</li>
              <li>• خطأ في تحميل البيانات</li>
              <li>• مشكلة في صلاحيات الوصول</li>
            </ul>
            
            {error.digest && (
              <div className="bg-gray-100 p-3 rounded-lg">
                <p className="text-xs text-gray-500">
                  معرف الخطأ: {error.digest}
                </p>
              </div>
            )}

            <div className="flex space-x-2 space-x-reverse">
              <Button 
                onClick={reset}
                className="flex-1"
              >
                <RefreshCw className="h-4 w-4 ml-2" />
                إعادة المحاولة
              </Button>
              <Button 
                variant="outline"
                onClick={() => window.location.href = '/settings'}
                className="flex-1"
              >
                <Settings className="h-4 w-4 ml-2" />
                الإعدادات العامة
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
