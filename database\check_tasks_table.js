const { Client } = require('pg')

const TARGET_DATABASES = (process.env.DB_NAMES
  ? process.env.DB_NAMES.split(',').map(s => s.trim()).filter(Boolean)
  : ['mohammidev', 'mohammi'])

const baseConfig = {
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
}

async function checkDb(db) {
  const client = new Client({ ...baseConfig, database: db })
  try {
    console.log(`\n=== Checking database: ${db} ===`)
    await client.connect()

    const res = await client.query(
      `SELECT to_regclass('public.tasks') AS tasks, to_regclass('public.task') AS task`
    )
    const row = res.rows[0] || {}
    const hasTasks = !!row.tasks
    const hasTask = !!row.task

    console.log(`- Table tasks exists: ${hasTasks}`)
    console.log(`- Table task exists: ${hasTask}`)

    if (hasTasks || hasTask) {
      const tableName = hasTasks ? 'tasks' : 'task'
      const cols = await client.query(
        `SELECT column_name, data_type FROM information_schema.columns WHERE table_name = $1 ORDER BY ordinal_position`,
        [tableName]
      )
      console.log(`Columns in ${tableName}:`)
      cols.rows.forEach(c => console.log(`  - ${c.column_name} (${c.data_type})`))
      const count = await client.query(`SELECT COUNT(*)::int AS count FROM ${tableName}`)
      console.log(`Rows in ${tableName}: ${count.rows[0].count}`)
    }
  } catch (e) {
    console.error('Error:', e.message)
  } finally {
    await client.end().catch(()=>{})
  }
}

async function main(){
  for (const db of TARGET_DATABASES) {
    await checkDb(db)
  }
}

main().catch(err=>{console.error(err); process.exit(1)})
