/**
 * دوال الإشعارات التلقائية عبر WhatsApp
 * تستخدم رقم الشركة المسجل في النظام
 */

import { companyWhatsAppService } from './company-whatsapp-service'
import { query } from './db'

// أنواع الإشعارات
export enum NotificationType {
  CASE_CREATED = 'case_created',
  CASE_UPDATED = 'case_updated',
  CASE_FOLLOW_UP = 'case_follow_up',
  HEARING_SCHEDULED = 'hearing_scheduled',
  HEARING_REMINDER = 'hearing_reminder',
  PAYMENT_DUE = 'payment_due',
  PAYMENT_RECEIVED = 'payment_received',
  RECEIPT_VOUCHER = 'receipt_voucher',
  PAYMENT_VOUCHER = 'payment_voucher',
  JOURNAL_ENTRY = 'journal_entry',
  DOCUMENT_READY = 'document_ready',
  APPOINTMENT_SCHEDULED = 'appointment_scheduled',
  TASK_ASSIGNED = 'task_assigned'
}

// قوالب الرسائل
const MESSAGE_TEMPLATES = {
  [NotificationType.CASE_CREATED]: `🆕 تم إنشاء قضية جديدة

📋 رقم القضية: {{case_number}}
📝 عنوان القضية: {{case_title}}
👤 العميل: {{client_name}}
📅 تاريخ الإنشاء: {{created_date}}

سيتم التواصل معكم لاستكمال الإجراءات المطلوبة.

{{company_name}}
{{company_phone}}`,

  [NotificationType.HEARING_SCHEDULED]: `⚖️ تذكير بجلسة محكمة

📋 القضية: {{case_number}} - {{case_title}}
📅 تاريخ الجلسة: {{hearing_date}}
🕐 وقت الجلسة: {{hearing_time}}
🏛️ المحكمة: {{court_name}}
📍 العنوان: {{court_address}}

يرجى الحضور في الموعد المحدد.

{{company_name}}
{{company_phone}}`,

  [NotificationType.PAYMENT_DUE]: `💰 تذكير بدفعة مستحقة

📋 القضية: {{case_number}}
💵 المبلغ المستحق: {{amount}} ريال
📅 تاريخ الاستحقاق: {{due_date}}
📝 البيان: {{description}}

يرجى سداد المبلغ في أقرب وقت ممكن.

{{company_name}}
{{company_phone}}`,

  [NotificationType.PAYMENT_RECEIVED]: `✅ تأكيد استلام دفعة

📋 القضية: {{case_number}}
💵 المبلغ المستلم: {{amount}} ريال
📅 تاريخ الاستلام: {{payment_date}}
🧾 رقم الإيصال: {{receipt_number}}

شكراً لكم على سداد المبلغ.

{{company_name}}
{{company_phone}}`,

  [NotificationType.DOCUMENT_READY]: `📄 وثيقة جاهزة للاستلام

📋 القضية: {{case_number}}
📝 نوع الوثيقة: {{document_type}}
📄 اسم الوثيقة: {{document_name}}
📅 تاريخ الجاهزية: {{ready_date}}

يمكنكم استلام الوثيقة من مكتبنا أو سنقوم بإرسالها لكم.

{{company_name}}
{{company_phone}}`,

  [NotificationType.APPOINTMENT_SCHEDULED]: `📅 موعد مع المحامي

👤 العميل: {{client_name}}
📅 تاريخ الموعد: {{appointment_date}}
🕐 وقت الموعد: {{appointment_time}}
📍 المكان: {{location}}
📝 الغرض: {{purpose}}

يرجى الحضور في الموعد المحدد.

{{company_name}}
{{company_phone}}`,

  [NotificationType.TASK_ASSIGNED]: `📋 مهمة جديدة

👤 الموظف: {{employee_name}}
📝 عنوان المهمة: {{task_title}}
📄 الوصف: {{task_description}}
📅 الموعد النهائي: {{deadline}}
⚡ الأولوية: {{priority}}

يرجى البدء في تنفيذ المهمة.

{{company_name}}
{{company_phone}}`,

  [NotificationType.CASE_FOLLOW_UP]: `📝 متابعة جديدة للقضية

📋 رقم القضية: {{case_number}}
📝 عنوان القضية: {{case_title}}
📅 تاريخ المتابعة: {{follow_up_date}}
👤 المتابع: {{follow_up_by}}

📄 تفاصيل المتابعة:
{{follow_up_details}}

{{company_name}}
{{company_phone}}`,

  [NotificationType.HEARING_REMINDER]: `⏰ تذكير بجلسة محكمة قادمة

📋 القضية: {{case_number}} - {{case_title}}
📅 تاريخ الجلسة: {{hearing_date}}
🕐 وقت الجلسة: {{hearing_time}}
🏛️ المحكمة: {{court_name}}
📍 العنوان: {{court_address}}

⚠️ باقي {{days_remaining}} أيام على موعد الجلسة

يرجى الاستعداد والحضور في الموعد المحدد.

{{company_name}}
{{company_phone}}`,

  [NotificationType.RECEIPT_VOUCHER]: `🧾 سند قبض جديد

📋 رقم السند: {{voucher_number}}
📅 التاريخ: {{voucher_date}}
💰 المبلغ: {{amount}} ريال
👤 المستلم من: {{received_from}}
📝 البيان: {{description}}

{{#if case_number}}
📋 القضية المرتبطة: {{case_number}}
{{/if}}

تم استلام المبلغ بنجاح.

{{company_name}}
{{company_phone}}`,

  [NotificationType.PAYMENT_VOUCHER]: `💸 سند صرف جديد

📋 رقم السند: {{voucher_number}}
📅 التاريخ: {{voucher_date}}
💰 المبلغ: {{amount}} ريال
👤 المدفوع إلى: {{paid_to}}
📝 البيان: {{description}}

{{#if case_number}}
📋 القضية المرتبطة: {{case_number}}
{{/if}}

تم صرف المبلغ بنجاح.

{{company_name}}
{{company_phone}}`,

  [NotificationType.JOURNAL_ENTRY]: `📊 قيد يومي جديد

📋 رقم القيد: {{entry_number}}
📅 التاريخ: {{entry_date}}
📝 البيان: {{description}}
💰 إجمالي المبلغ: {{total_amount}} ريال

{{#if case_number}}
📋 القضية المرتبطة: {{case_number}}
{{/if}}

تم إدراج القيد في الدفاتر المحاسبية.

{{company_name}}
{{company_phone}}`
}

// دالة عامة لإرسال الإشعارات
async function sendNotification(
  type: NotificationType,
  recipientPhone: string,
  variables: Record<string, string>,
  recipientName?: string
): Promise<boolean> {
  try {
    // جلب بيانات الشركة
    const companyData = await getCompanyData()
    if (!companyData) {
      console.error('بيانات الشركة غير متوفرة')
      return false
    }

    // تحضير المتغيرات مع بيانات الشركة
    const allVariables = {
      ...variables,
      company_name: companyData.company_name,
      company_phone: companyData.whatsapp_phone || companyData.phone,
      current_date: new Date().toLocaleDateString('ar-SA'),
      current_time: new Date().toLocaleTimeString('ar-SA')
    }

    // الحصول على قالب الرسالة
    let message = MESSAGE_TEMPLATES[type]
    if (!message) {
      console.error(`قالب الرسالة غير موجود للنوع: ${type}`)
      return false
    }

    // استبدال المتغيرات في القالب
    Object.entries(allVariables).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g')
      message = message.replace(regex, value || '')
    })

    // إرسال الرسالة
    const result = await companyWhatsAppService.sendTextMessage(
      recipientPhone,
      message,
      recipientName
    )

    return result.success
  } catch (error) {
    console.error('خطأ في إرسال الإشعار:', error)
    return false
  }
}

// جلب بيانات الشركة
async function getCompanyData(): Promise<any> {
  try {
    const result = await query(`
      SELECT company_name, phone, whatsapp_phone, whatsapp_business_name
      FROM company_info
      WHERE whatsapp_enabled = true
      LIMIT 1
    `)

    return result.rows[0] || null
  } catch (error) {
    console.error('خطأ في جلب بيانات الشركة:', error)
    return null
  }
}

// إشعار إنشاء قضية جديدة
export async function notifyNewCase(caseId: number): Promise<boolean> {
  try {
    // جلب بيانات القضية والعميل
    const result = await query(`
      SELECT
        c.case_number,
        c.case_title,
        c.created_at,
        cl.name as client_name,
        cl.phone as client_phone
      FROM cases c
      INNER JOIN clients cl ON cl.id = c.client_id
      WHERE c.id = $1 AND cl.phone IS NOT NULL
    `, [caseId])

    if (result.rows.length === 0) {
      console.warn('لم يتم العثور على بيانات القضية أو رقم هاتف العميل')
      return false
    }

    const caseData = result.rows[0]

    const variables = {
      case_number: caseData.case_number,
      case_title: caseData.case_title,
      client_name: caseData.client_name,
      created_date: new Date(caseData.created_at).toLocaleDateString('ar-SA')
    }

    return await sendNotification(
      NotificationType.CASE_CREATED,
      caseData.client_phone,
      variables,
      caseData.client_name
    )
  } catch (error) {
    console.error('خطأ في إشعار القضية الجديدة:', error)
    return false
  }
}

// إشعار جدولة جلسة محكمة
export async function notifyHearingScheduled(hearingId: number): Promise<boolean> {
  try {
    const result = await query(`
      SELECT
        h.hearing_date,
        h.hearing_time,
        h.court_name,
        h.court_address,
        c.case_number,
        c.case_title,
        cl.name as client_name,
        cl.phone as client_phone
      FROM hearings h
      INNER JOIN cases c ON c.id = h.case_id
      INNER JOIN clients cl ON cl.id = c.client_id
      WHERE h.id = $1 AND cl.phone IS NOT NULL
    `, [hearingId])

    if (result.rows.length === 0) return false

    const hearingData = result.rows[0]

    const variables = {
      case_number: hearingData.case_number,
      case_title: hearingData.case_title,
      hearing_date: new Date(hearingData.hearing_date).toLocaleDateString('ar-SA'),
      hearing_time: hearingData.hearing_time,
      court_name: hearingData.court_name,
      court_address: hearingData.court_address || 'غير محدد'
    }

    return await sendNotification(
      NotificationType.HEARING_SCHEDULED,
      hearingData.client_phone,
      variables,
      hearingData.client_name
    )
  } catch (error) {
    console.error('خطأ في إشعار جلسة المحكمة:', error)
    return false
  }
}

// إشعار دفعة مستحقة
export async function notifyPaymentDue(paymentId: number): Promise<boolean> {
  try {
    const result = await query(`
      SELECT
        p.amount,
        p.due_date,
        p.description,
        c.case_number,
        cl.name as client_name,
        cl.phone as client_phone
      FROM payments p
      INNER JOIN cases c ON c.id = p.case_id
      INNER JOIN clients cl ON cl.id = c.client_id
      WHERE p.id = $1 AND cl.phone IS NOT NULL
    `, [paymentId])

    if (result.rows.length === 0) return false

    const paymentData = result.rows[0]

    const variables = {
      case_number: paymentData.case_number,
      amount: paymentData.amount.toString(),
      due_date: new Date(paymentData.due_date).toLocaleDateString('ar-SA'),
      description: paymentData.description
    }

    return await sendNotification(
      NotificationType.PAYMENT_DUE,
      paymentData.client_phone,
      variables,
      paymentData.client_name
    )
  } catch (error) {
    console.error('خطأ في إشعار الدفعة المستحقة:', error)
    return false
  }
}

// إشعار استلام دفعة
export async function notifyPaymentReceived(paymentId: number, receiptNumber: string): Promise<boolean> {
  try {
    const result = await query(`
      SELECT
        p.amount,
        p.payment_date,
        c.case_number,
        cl.name as client_name,
        cl.phone as client_phone
      FROM payments p
      INNER JOIN cases c ON c.id = p.case_id
      INNER JOIN clients cl ON cl.id = c.client_id
      WHERE p.id = $1 AND cl.phone IS NOT NULL
    `, [paymentId])

    if (result.rows.length === 0) return false

    const paymentData = result.rows[0]

    const variables = {
      case_number: paymentData.case_number,
      amount: paymentData.amount.toString(),
      payment_date: new Date(paymentData.payment_date).toLocaleDateString('ar-SA'),
      receipt_number: receiptNumber
    }

    return await sendNotification(
      NotificationType.PAYMENT_RECEIVED,
      paymentData.client_phone,
      variables,
      paymentData.client_name
    )
  } catch (error) {
    console.error('خطأ في إشعار استلام الدفعة:', error)
    return false
  }
}

// إشعار جاهزية وثيقة
export async function notifyDocumentReady(documentId: number): Promise<boolean> {
  try {
    const result = await query(`
      SELECT
        d.document_name,
        d.document_type,
        d.created_at,
        c.case_number,
        cl.name as client_name,
        cl.phone as client_phone
      FROM documents d
      INNER JOIN cases c ON c.id = d.case_id
      INNER JOIN clients cl ON cl.id = c.client_id
      WHERE d.id = $1 AND cl.phone IS NOT NULL
    `, [documentId])

    if (result.rows.length === 0) return false

    const documentData = result.rows[0]

    const variables = {
      case_number: documentData.case_number,
      document_name: documentData.document_name,
      document_type: documentData.document_type,
      ready_date: new Date(documentData.created_at).toLocaleDateString('ar-SA')
    }

    return await sendNotification(
      NotificationType.DOCUMENT_READY,
      documentData.client_phone,
      variables,
      documentData.client_name
    )
  } catch (error) {
    console.error('خطأ في إشعار جاهزية الوثيقة:', error)
    return false
  }
}

// إشعار موعد مع المحامي
export async function notifyAppointmentScheduled(appointmentId: number): Promise<boolean> {
  try {
    const result = await query(`
      SELECT
        a.appointment_date,
        a.appointment_time,
        a.location,
        a.purpose,
        cl.name as client_name,
        cl.phone as client_phone
      FROM appointments a
      INNER JOIN clients cl ON cl.id = a.client_id
      WHERE a.id = $1 AND cl.phone IS NOT NULL
    `, [appointmentId])

    if (result.rows.length === 0) return false

    const appointmentData = result.rows[0]

    const variables = {
      client_name: appointmentData.client_name,
      appointment_date: new Date(appointmentData.appointment_date).toLocaleDateString('ar-SA'),
      appointment_time: appointmentData.appointment_time,
      location: appointmentData.location,
      purpose: appointmentData.purpose
    }

    return await sendNotification(
      NotificationType.APPOINTMENT_SCHEDULED,
      appointmentData.client_phone,
      variables,
      appointmentData.client_name
    )
  } catch (error) {
    console.error('خطأ في إشعار الموعد:', error)
    return false
  }
}

// إشعار تكليف مهمة للموظف
export async function notifyTaskAssigned(taskId: number): Promise<boolean> {
  try {
    const result = await query(`
      SELECT
        t.task_title,
        t.task_description,
        t.deadline,
        t.priority,
        e.name as employee_name,
        e.phone as employee_phone
      FROM tasks t
      INNER JOIN employees e ON e.id = t.assigned_to
      WHERE t.id = $1 AND e.phone IS NOT NULL
    `, [taskId])

    if (result.rows.length === 0) return false

    const taskData = result.rows[0]

    const variables = {
      employee_name: taskData.employee_name,
      task_title: taskData.task_title,
      task_description: taskData.task_description,
      deadline: new Date(taskData.deadline).toLocaleDateString('ar-SA'),
      priority: taskData.priority === 'high' ? 'عالية' : taskData.priority === 'medium' ? 'متوسطة' : 'منخفضة'
    }

    return await sendNotification(
      NotificationType.TASK_ASSIGNED,
      taskData.employee_phone,
      variables,
      taskData.employee_name
    )
  } catch (error) {
    console.error('خطأ في إشعار تكليف المهمة:', error)
    return false
  }
}

// إرسال صورة السند للعميل
export async function sendVoucherToClient(clientId: number, voucherImagePath: string, voucherInfo: string): Promise<boolean> {
  try {
    const result = await query(`
      SELECT name, phone
      FROM clients
      WHERE id = $1 AND phone IS NOT NULL
    `, [clientId])

    if (result.rows.length === 0) return false

    const client = result.rows[0]

    const voucherResult = await companyWhatsAppService.sendVoucherImage(
      client.phone,
      voucherImagePath,
      voucherInfo,
      client.name
    )

    return voucherResult.success
  } catch (error) {
    console.error('خطأ في إرسال صورة السند:', error)
    return false
  }
}

// إشعار إضافة متابعة جديدة للقضية
export async function notifyCaseFollowUp(followUpId: number): Promise<boolean> {
  try {
    const result = await query(`
      SELECT
        cf.follow_up_date,
        cf.follow_up_details,
        cf.follow_up_by,
        c.case_number,
        c.case_title,
        cl.name as client_name,
        cl.phone as client_phone,
        e.name as employee_name
      FROM case_follow_ups cf
      INNER JOIN cases c ON c.id = cf.case_id
      INNER JOIN clients cl ON cl.id = c.client_id
      LEFT JOIN employees e ON e.id = cf.created_by
      WHERE cf.id = $1 AND cl.phone IS NOT NULL
    `, [followUpId])

    if (result.rows.length === 0) return false

    const followUpData = result.rows[0]

    const variables = {
      case_number: followUpData.case_number,
      case_title: followUpData.case_title,
      follow_up_date: new Date(followUpData.follow_up_date).toLocaleDateString('ar-SA'),
      follow_up_by: followUpData.employee_name || followUpData.follow_up_by || 'المكتب',
      follow_up_details: followUpData.follow_up_details
    }

    return await sendNotification(
      NotificationType.CASE_FOLLOW_UP,
      followUpData.client_phone,
      variables,
      followUpData.client_name
    )
  } catch (error) {
    console.error('خطأ في إشعار متابعة القضية:', error)
    return false
  }
}

// إشعار سند قبض جديد
export async function notifyReceiptVoucher(voucherId: number, clientId?: number): Promise<boolean> {
  try {
    let queryText = `
      SELECT
        rv.voucher_number,
        rv.voucher_date,
        rv.amount,
        rv.received_from,
        rv.description,
        c.case_number
      FROM receipt_vouchers rv
      LEFT JOIN cases c ON c.id = rv.case_id
      WHERE rv.id = $1
    `

    const voucherResult = await query(queryText, [voucherId])
    if (voucherResult.rows.length === 0) return false

    const voucherData = voucherResult.rows[0]

    // إذا كان هناك عميل محدد، إرسال له
    if (clientId) {
      const clientResult = await query(`
        SELECT name, phone FROM clients WHERE id = $1 AND phone IS NOT NULL
      `, [clientId])

      if (clientResult.rows.length === 0) return false

      const client = clientResult.rows[0]

      const variables = {
        voucher_number: voucherData.voucher_number,
        voucher_date: new Date(voucherData.voucher_date).toLocaleDateString('ar-SA'),
        amount: voucherData.amount.toString(),
        received_from: voucherData.received_from,
        description: voucherData.description,
        case_number: voucherData.case_number || ''
      }

      return await sendNotification(
        NotificationType.RECEIPT_VOUCHER,
        client.phone,
        variables,
        client.name
      )
    }

    return true
  } catch (error) {
    console.error('خطأ في إشعار سند القبض:', error)
    return false
  }
}

// إشعار سند صرف جديد
export async function notifyPaymentVoucher(voucherId: number, clientId?: number): Promise<boolean> {
  try {
    let queryText = `
      SELECT
        pv.voucher_number,
        pv.voucher_date,
        pv.amount,
        pv.paid_to,
        pv.description,
        c.case_number
      FROM payment_vouchers pv
      LEFT JOIN cases c ON c.id = pv.case_id
      WHERE pv.id = $1
    `

    const voucherResult = await query(queryText, [voucherId])
    if (voucherResult.rows.length === 0) return false

    const voucherData = voucherResult.rows[0]

    // إذا كان هناك عميل محدد، إرسال له
    if (clientId) {
      const clientResult = await query(`
        SELECT name, phone FROM clients WHERE id = $1 AND phone IS NOT NULL
      `, [clientId])

      if (clientResult.rows.length === 0) return false

      const client = clientResult.rows[0]

      const variables = {
        voucher_number: voucherData.voucher_number,
        voucher_date: new Date(voucherData.voucher_date).toLocaleDateString('ar-SA'),
        amount: voucherData.amount.toString(),
        paid_to: voucherData.paid_to,
        description: voucherData.description,
        case_number: voucherData.case_number || ''
      }

      return await sendNotification(
        NotificationType.PAYMENT_VOUCHER,
        client.phone,
        variables,
        client.name
      )
    }

    return true
  } catch (error) {
    console.error('خطأ في إشعار سند الصرف:', error)
    return false
  }
}

// إشعار قيد يومي جديد
export async function notifyJournalEntry(entryId: number, clientId?: number): Promise<boolean> {
  try {
    const entryResult = await query(`
      SELECT
        je.entry_number,
        je.entry_date,
        je.description,
        c.case_number,
        (SELECT SUM(COALESCE(jed.debit_amount, 0)) FROM journal_entry_details jed WHERE jed.journal_entry_id = je.id) as total_amount
      FROM journal_entries je
      LEFT JOIN cases c ON c.id = je.case_id
      WHERE je.id = $1
    `, [entryId])

    if (entryResult.rows.length === 0) return false

    const entryData = entryResult.rows[0]

    // إذا كان هناك عميل محدد، إرسال له
    if (clientId) {
      const clientResult = await query(`
        SELECT name, phone FROM clients WHERE id = $1 AND phone IS NOT NULL
      `, [clientId])

      if (clientResult.rows.length === 0) return false

      const client = clientResult.rows[0]

      const variables = {
        entry_number: entryData.entry_number,
        entry_date: new Date(entryData.entry_date).toLocaleDateString('ar-SA'),
        description: entryData.description,
        total_amount: entryData.total_amount?.toString() || '0',
        case_number: entryData.case_number || ''
      }

      return await sendNotification(
        NotificationType.JOURNAL_ENTRY,
        client.phone,
        variables,
        client.name
      )
    }

    return true
  } catch (error) {
    console.error('خطأ في إشعار القيد اليومي:', error)
    return false
  }
}

// تذكير بجلسة محكمة قادمة (للعملاء والموظفين)
export async function sendHearingReminders(daysBeforeHearing: number = 3): Promise<{ clientsNotified: number; employeesNotified: number }> {
  try {
    const targetDate = new Date()
    targetDate.setDate(targetDate.getDate() + daysBeforeHearing)

    // جلب الجلسات القادمة
    const hearingsResult = await query(`
      SELECT
        h.id,
        h.hearing_date,
        h.hearing_time,
        h.court_name,
        h.court_address,
        c.case_number,
        c.case_title,
        cl.name as client_name,
        cl.phone as client_phone,
        c.assigned_lawyer
      FROM hearings h
      INNER JOIN cases c ON c.id = h.case_id
      INNER JOIN clients cl ON cl.id = c.client_id
      WHERE h.hearing_date = $1
        AND h.status = 'scheduled'
        AND cl.phone IS NOT NULL
    `, [targetDate.toISOString().split('T')[0]])

    let clientsNotified = 0
    let employeesNotified = 0

    for (const hearing of hearingsResult.rows) {
      const variables = {
        case_number: hearing.case_number,
        case_title: hearing.case_title,
        hearing_date: new Date(hearing.hearing_date).toLocaleDateString('ar-SA'),
        hearing_time: hearing.hearing_time,
        court_name: hearing.court_name,
        court_address: hearing.court_address || 'غير محدد',
        days_remaining: daysBeforeHearing.toString()
      }

      // إشعار العميل
      const clientNotified = await sendNotification(
        NotificationType.HEARING_REMINDER,
        hearing.client_phone,
        variables,
        hearing.client_name
      )

      if (clientNotified) clientsNotified++

      // إشعار المحامي المكلف
      if (hearing.assigned_lawyer) {
        const lawyerResult = await query(`
          SELECT name, phone FROM employees WHERE id = $1 AND phone IS NOT NULL
        `, [hearing.assigned_lawyer])

        if (lawyerResult.rows.length > 0) {
          const lawyer = lawyerResult.rows[0]
          const employeeNotified = await sendNotification(
            NotificationType.HEARING_REMINDER,
            lawyer.phone,
            variables,
            lawyer.name
          )

          if (employeeNotified) employeesNotified++
        }
      }

      // تأخير بسيط بين الرسائل
      await new Promise(resolve => setTimeout(resolve, 2000))
    }

    return { clientsNotified, employeesNotified }
  } catch (error) {
    console.error('خطأ في إرسال تذكيرات الجلسات:', error)
    return { clientsNotified: 0, employeesNotified: 0 }
  }
}
