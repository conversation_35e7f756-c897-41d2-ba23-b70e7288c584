'use client';

import { useState, useEffect } from 'react';
import { Search, FileText, Download, BookOpen, Eye, ExternalLink } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

type LegalFile = {
  id: string;
  name: string;
  originalName: string;
  path: string;
  category: string;
  type: 'pdf' | 'txt';
  size: number;
};

type DocumentCategory = {
  id: string;
  name: string;
  count: number;
  icon: any;
};

export function LegalLibrarySection() {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');
  const [showAllDocuments, setShowAllDocuments] = useState(false);
  const [files, setFiles] = useState<LegalFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // جلب الملفات من API
  useEffect(() => {
    fetchFiles();
  }, []);

  const fetchFiles = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/legal-library');
      const result = await response.json();

      if (result.success) {
        setFiles(result.data);
      } else {
        setError('فشل في جلب الملفات');
      }
    } catch (error) {
      console.error('Error fetching files:', error);
      setError('حدث خطأ في جلب الملفات');
    } finally {
      setLoading(false);
    }
  };

  // إنشاء الفئات بناءً على الملفات المتاحة
  const categories: DocumentCategory[] = [
    { id: 'all', name: 'الكل', count: files.length, icon: BookOpen },
    ...Array.from(new Set(files.map(f => f.category))).map(category => ({
      id: category,
      name: category,
      count: files.filter(f => f.category === category).length,
      icon: FileText
    }))
  ];

  // لا توجد وثائق افتراضية - يجب قراءة الوثائق من قاعدة البيانات فقط

  // تصفية الملفات
  const filteredFiles = files.filter(file => {
    const matchesSearch = file.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         file.category.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = activeCategory === 'all' || file.category === activeCategory;
    return matchesSearch && matchesCategory;
  });

  const displayedFiles = showAllDocuments
    ? filteredFiles
    : filteredFiles.slice(0, 8);

  const getFileIcon = (fileType: string) => {
    switch (fileType.toLowerCase()) {
      case 'pdf':
        return <FileText className="w-5 h-5 text-red-500" />;
      case 'txt':
        return <FileText className="w-5 h-5 text-blue-500" />;
      default:
        return <FileText className="w-5 h-5 text-gray-500" />;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <section id="library" className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <span className="inline-block bg-blue-100 text-blue-600 text-sm font-semibold px-4 py-1 rounded-full mb-4">
            المكتبة القانونية
          </span>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">وثائق قانونية للتحميل</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            تصفح وتحميل أحدث الوثائق القانونية والأنظمة واللوائح
          </p>
          <div className="w-20 h-1 bg-blue-600 mx-auto mt-6"></div>
        </div>

        {/* Search and Filter */}
        <div className="mb-8">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
            {/* Categories */}
            <div className="flex flex-wrap gap-2 w-full md:w-auto">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setActiveCategory(category.id)}
                  className={`px-4 py-2 rounded-full text-sm font-medium flex items-center gap-2 transition-colors ${
                    activeCategory === category.id
                      ? 'bg-blue-600 text-white'
                      : 'bg-white text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <category.icon className="w-4 h-4" />
                  {category.name}
                  <span className="bg-white/20 text-xs px-2 py-0.5 rounded-full">
                    {category.count}
                  </span>
                </button>
              ))}
            </div>

            {/* Search */}
            <div className="relative w-full md:w-80">
              <Input
                type="text"
                placeholder="ابحث في الوثائق..."
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-right"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
                <Search className="w-4 h-4" />
              </div>
            </div>
          </div>
        </div>

        {/* Documents Grid */}
        {filteredDocuments.length > 0 ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
              {displayedDocuments.map((doc) => (
                <div
                  key={doc.id}
                  className="bg-white rounded-xl border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow duration-300 flex flex-col h-full"
                >
                  <div className="p-5 flex-1">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center">
                        {getFileIcon(doc.fileType)}
                        <span className="text-xs text-gray-500 mr-2">.{doc.fileType}</span>
                      </div>
                      <span className="text-xs text-gray-500">{doc.fileSize}</span>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                      {doc.title}
                    </h3>
                    <div className="flex items-center justify-between mt-4">
                      <span className="text-xs px-3 py-1 bg-blue-50 text-blue-600 rounded-full">
                        {categories.find(cat => cat.id === doc.category)?.name}
                      </span>
                      <span className="text-xs text-gray-500">
                        {new Date(doc.date).toLocaleDateString('ar-EG')}
                      </span>
                    </div>
                  </div>
                  <div className="border-t border-gray-100 p-4">
                    <Button variant="outline" size="sm" className="w-full">
                      <Download className="w-4 h-4 ml-2" />
                      تحميل الملف
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            {filteredDocuments.length > 4 && !showAllDocuments && (
              <div className="text-center mt-8">
                <Button
                  variant="outline"
                  onClick={() => setShowAllDocuments(true)}
                  className="px-8 py-6 text-base"
                >
                  عرض المزيد من الوثائق
                </Button>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-12 bg-white rounded-xl border border-gray-200">
            <File className="w-12 h-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد وثائق متطابقة</h3>
            <p className="text-gray-500 max-w-md mx-auto">
              لم نتمكن من العثور على وثائق تطابق بحثك. حاول استخدام كلمات بحث مختلفة.
            </p>
          </div>
        )}
      </div>
    </section>
  );
}
