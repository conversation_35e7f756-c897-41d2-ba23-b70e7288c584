'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'

interface AuthGuardProps {
  children: React.ReactNode
  requiredPermissions?: string[]
  requiredRole?: string
  fallbackUrl?: string
}

export default function AuthGuard({ 
  children, 
  requiredPermissions = [], 
  requiredRole,
  fallbackUrl = '/login' 
}: AuthGuardProps) {
  const { user, loading, hasPermission, hasAnyPermission } = useAuth()
  const router = useRouter()
  const [isAuthorized, setIsAuthorized] = useState(false)

  useEffect(() => {
    if (loading) return

    // التحقق من وجود مستخدم
    if (!user) {
      console.warn('🚫 لا يوجد مستخدم مسجل دخول، توجيه لصفحة تسجيل الدخول')
      router.push(fallbackUrl)
      return
    }

    // التحقق من صحة بيانات المستخدم
    if (!user.username || !user.role) {
      console.warn('🚫 بيانات المستخدم غير مكتملة، إجبار تسجيل الخروج')
      localStorage.removeItem('userSession')
      localStorage.removeItem('clientToken')
      document.cookie = 'userSession=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT'
      router.push(fallbackUrl)
      return
    }

    // التحقق من أن المستخدم نشط
    if (user.is_active === false || user.status !== 'active') {
      console.warn('🚫 المستخدم غير نشط، إجبار تسجيل الخروج')
      localStorage.removeItem('userSession')
      localStorage.removeItem('clientToken')
      document.cookie = 'userSession=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT'
      router.push(fallbackUrl)
      return
    }

    // التحقق من الدور المطلوب
    if (requiredRole && user.role !== requiredRole) {
      console.warn(`🚫 المستخدم لا يملك الدور المطلوب: ${requiredRole}، الدور الحالي: ${user.role}`)
      router.push('/unauthorized')
      return
    }

    // التحقق من الصلاحيات المطلوبة
    if (requiredPermissions.length > 0) {
      const hasRequiredPermissions = hasAnyPermission(requiredPermissions)
      if (!hasRequiredPermissions) {
        console.warn(`🚫 المستخدم لا يملك الصلاحيات المطلوبة: ${requiredPermissions.join(', ')}`)
        router.push('/unauthorized')
        return
      }
    }

    // التحقق من انتهاء صلاحية الجلسة
    if (user.loginTime) {
      const loginTime = new Date(user.loginTime)
      const now = new Date()
      const hoursDiff = (now.getTime() - loginTime.getTime()) / (1000 * 60 * 60)
      
      if (hoursDiff > 24) {
        console.warn('🚫 انتهت صلاحية الجلسة، إجبار تسجيل الخروج')
        localStorage.removeItem('userSession')
        localStorage.removeItem('clientToken')
        document.cookie = 'userSession=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT'
        router.push(fallbackUrl)
        return
      }
    }

    // إذا تم اجتياز جميع الفحوصات
    setIsAuthorized(true)
    console.log('✅ تم التحقق من صلاحية المستخدم:', {
      username: user.username,
      role: user.role,
      permissions: user.permissions?.length || 0,
      sessionValid: true
    })

  }, [user, loading, requiredPermissions, requiredRole, router, fallbackUrl, hasAnyPermission])

  // عرض شاشة التحميل أثناء التحقق
  if (loading || !isAuthorized) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري التحقق من الصلاحيات...</p>
        </div>
      </div>
    )
  }

  // عرض المحتوى إذا كان المستخدم مخول
  return <>{children}</>
}

// مكون مخصص للصفحات التي تتطلب صلاحيات إدارية
export function AdminGuard({ children }: { children: React.ReactNode }) {
  return (
    <AuthGuard requiredRole="admin">
      {children}
    </AuthGuard>
  )
}

// مكون مخصص للصفحات التي تتطلب صلاحيات إدارية أو مدير
export function ManagerGuard({ children }: { children: React.ReactNode }) {
  return (
    <AuthGuard requiredPermissions={['manage_users', 'system_admin']}>
      {children}
    </AuthGuard>
  )
}

// مكون مخصص للصفحات المحاسبية
export function AccountingGuard({ children }: { children: React.ReactNode }) {
  return (
    <AuthGuard requiredPermissions={['manage_accounting', 'view_financial_reports']}>
      {children}
    </AuthGuard>
  )
}
