'use client'

import React from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useRouter } from 'next/navigation'
import { 
  <PERSON>ert<PERSON>riangle, 
  RefreshCw, 
  Home, 
  ArrowRight,
  Bug,
  Shield
} from 'lucide-react'

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
  errorInfo?: React.ErrorInfo
}

interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<{ error?: Error; resetError: () => void }>
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo)
    this.setState({ error, errorInfo })
  }

  resetError = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback
        return <FallbackComponent error={this.state.error} resetError={this.resetError} />
      }
      
      return <DefaultErrorFallback error={this.state.error} resetError={this.resetError} />
    }

    return this.props.children
  }
}

function DefaultErrorFallback({ error, resetError }: { error?: Error; resetError: () => void }) {
  const router = useRouter()

  const handleGoHome = () => {
    resetError()
    router.push('/dashboard')
  }

  const handleGoBack = () => {
    resetError()
    if (window.history.length > 1) {
      router.back()
    } else {
      router.push('/dashboard')
    }
  }

  const handleRefresh = () => {
    resetError()
    window.location.reload()
  }

  return (
    <MainLayout>
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-orange-100 p-4">
        <Card className="w-full max-w-2xl shadow-2xl border-0">
          <CardHeader className="text-center pb-2">
            <div className="flex justify-center mb-4">
              <div className="relative">
                <Shield className="h-24 w-24 text-red-500" />
                <div className="absolute -top-2 -right-2">
                  <AlertTriangle className="h-8 w-8 text-orange-600 animate-pulse" />
                </div>
              </div>
            </div>
            <CardTitle className="text-3xl font-bold text-gray-800 mb-2">
              ⚠️ حدث خطأ غير متوقع
            </CardTitle>
            <p className="text-lg text-gray-600">
              نعتذر، حدث خطأ أثناء تحميل هذه الصفحة
            </p>
          </CardHeader>
          
          <CardContent className="text-center space-y-6">
            {/* رسالة الخطأ */}
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center justify-center mb-2">
                <Bug className="h-5 w-5 text-red-600 mr-2" />
                <span className="font-semibold text-red-800">تفاصيل الخطأ</span>
              </div>
              <p className="text-red-700 text-sm font-mono bg-red-100 p-2 rounded">
                {error?.message || 'خطأ غير محدد'}
              </p>
            </div>

            {/* نصائح للحل */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-semibold text-blue-800 mb-2">💡 نصائح لحل المشكلة:</h3>
              <ul className="text-blue-700 text-sm space-y-1 text-right">
                <li>• تحديث الصفحة</li>
                <li>• التأكد من اتصال الإنترنت</li>
                <li>• مسح ذاكرة التخزين المؤقت للمتصفح</li>
                <li>• المحاولة مرة أخرى بعد قليل</li>
              </ul>
            </div>

            {/* أزرار الإجراءات */}
            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <Button 
                onClick={handleRefresh}
                className="flex-1 bg-green-600 hover:bg-green-700 text-white"
                size="lg"
              >
                <RefreshCw className="h-5 w-5 mr-2" />
                تحديث الصفحة
              </Button>
              <Button 
                onClick={handleGoBack}
                className="flex-1 bg-gray-600 hover:bg-gray-700 text-white"
                size="lg"
              >
                <ArrowRight className="h-5 w-5 mr-2 rotate-180" />
                العودة للخلف
              </Button>
              <Button 
                onClick={handleGoHome}
                className="flex-1 bg-purple-600 hover:bg-purple-700 text-white"
                size="lg"
              >
                <Home className="h-5 w-5 mr-2" />
                لوحة التحكم
              </Button>
            </div>

            {/* معلومات إضافية */}
            <div className="mt-6 p-4 bg-gradient-to-r from-gray-50 to-blue-50 rounded-lg border border-gray-200">
              <p className="text-gray-700 text-sm">
                إذا استمرت المشكلة، يرجى التواصل مع فريق الدعم الفني
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}

export default ErrorBoundary
export { DefaultErrorFallback }
