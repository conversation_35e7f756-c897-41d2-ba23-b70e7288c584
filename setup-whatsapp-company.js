/**
 * سكريبت لإعداد بيانات الشركة وتفعيل WhatsApp
 * يجب تشغيله لإعداد البيانات الأساسية في قاعدة البيانات rubaie
 */

const { Client } = require('pg')

// إعدادات قاعدة البيانات rubaie
const dbConfig = {
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'rubaie'
}

async function setupWhatsAppCompany() {
  const client = new Client(dbConfig)
  
  try {
    await client.connect()
    console.log('🔗 تم الاتصال بقاعدة البيانات rubaie')

    // 1. إنشاء جدول company_info إذا لم يكن موجوداً
    console.log('📋 إنشاء جدول company_info...')
    await client.query(`
      CREATE TABLE IF NOT EXISTS company_info (
        id SERIAL PRIMARY KEY,
        company_name VARCHAR(255) NOT NULL,
        phone VARCHAR(20),
        email VARCHAR(100),
        address TEXT,
        whatsapp_phone VARCHAR(20),
        whatsapp_business_name VARCHAR(100),
        whatsapp_enabled BOOLEAN DEFAULT false,
        whatsapp_session_name VARCHAR(50),
        whatsapp_auto_reply BOOLEAN DEFAULT true,
        whatsapp_business_hours_start TIME DEFAULT '08:00',
        whatsapp_business_hours_end TIME DEFAULT '17:00',
        whatsapp_auto_reply_message TEXT DEFAULT 'مرحباً بك في {{company_name}}. سيتم الرد عليك خلال ساعات العمل.',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // 2. التحقق من وجود بيانات الشركة
    const existingCompany = await client.query('SELECT * FROM company_info LIMIT 1')
    
    if (existingCompany.rows.length === 0) {
      // إدراج بيانات الشركة الافتراضية
      console.log('🏢 إدراج بيانات الشركة...')
      await client.query(`
        INSERT INTO company_info (
          company_name,
          phone,
          email,
          address,
          whatsapp_phone,
          whatsapp_business_name,
          whatsapp_enabled,
          whatsapp_session_name,
          whatsapp_auto_reply,
          whatsapp_auto_reply_message
        ) VALUES (
          'نظام إدارة المحاماة - الربعي',
          '+967771234568',
          '<EMAIL>',
          'صنعاء، اليمن',
          '+967771234568',
          'مكتب الربعي للمحاماة',
          true,
          'rubaie_whatsapp',
          true,
          'مرحباً بك في مكتب الربعي للمحاماة. سيتم الرد عليك خلال ساعات العمل من 8 صباحاً إلى 5 مساءً.'
        )
      `)
    } else {
      // تحديث البيانات الموجودة لتفعيل WhatsApp
      console.log('🔄 تحديث بيانات الشركة الموجودة...')
      await client.query(`
        UPDATE company_info SET
          whatsapp_phone = COALESCE(whatsapp_phone, '+967771234568'),
          whatsapp_business_name = COALESCE(whatsapp_business_name, 'مكتب الربعي للمحاماة'),
          whatsapp_enabled = true,
          whatsapp_session_name = COALESCE(whatsapp_session_name, 'rubaie_whatsapp'),
          whatsapp_auto_reply = true,
          whatsapp_auto_reply_message = COALESCE(
            whatsapp_auto_reply_message, 
            'مرحباً بك في مكتب الربعي للمحاماة. سيتم الرد عليك خلال ساعات العمل من 8 صباحاً إلى 5 مساءً.'
          ),
          updated_at = CURRENT_TIMESTAMP
        WHERE id = 1
      `)
    }

    // 3. إنشاء جدول إعدادات WhatsApp المتقدمة
    console.log('⚙️ إنشاء جدول إعدادات WhatsApp...')
    await client.query(`
      CREATE TABLE IF NOT EXISTS whatsapp_company_settings (
        id SERIAL PRIMARY KEY,
        company_id INTEGER REFERENCES company_info(id) ON DELETE CASCADE,
        setting_key VARCHAR(50) NOT NULL,
        setting_value TEXT,
        setting_type VARCHAR(20) DEFAULT 'string',
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT unique_company_setting UNIQUE (company_id, setting_key)
      )
    `)

    // 4. إدراج الإعدادات الافتراضية
    console.log('📝 إدراج الإعدادات الافتراضية...')
    const defaultSettings = [
      ['max_daily_messages', '500', 'number', 'الحد الأقصى للرسائل اليومية'],
      ['message_delay_seconds', '2', 'number', 'التأخير بين الرسائل (بالثواني)'],
      ['notification_templates', JSON.stringify({
        case_created: 'تم إنشاء قضية جديدة برقم {{case_number}}',
        hearing_reminder: 'تذكير: لديك جلسة محكمة يوم {{date}} في {{court}}',
        payment_reminder: 'تذكير: استحقاق دفعة بمبلغ {{amount}} في {{date}}',
        document_ready: 'المستند {{document_name}} جاهز للاستلام'
      }), 'json', 'قوالب الإشعارات']
    ]

    for (const [key, value, type, description] of defaultSettings) {
      await client.query(`
        INSERT INTO whatsapp_company_settings (company_id, setting_key, setting_value, setting_type, description)
        VALUES (1, $1, $2, $3, $4)
        ON CONFLICT (company_id, setting_key) DO NOTHING
      `, [key, value, type, description])
    }

    // 5. إنشاء جدول إعدادات التذكيرات
    console.log('⏰ إنشاء جدول إعدادات التذكيرات...')
    await client.query(`
      CREATE TABLE IF NOT EXISTS whatsapp_reminder_settings (
        id SERIAL PRIMARY KEY,
        company_id INTEGER REFERENCES company_info(id) ON DELETE CASCADE,
        reminder_type VARCHAR(50) NOT NULL,
        days_before INTEGER NOT NULL,
        enabled BOOLEAN DEFAULT true,
        target_audience VARCHAR(20) DEFAULT 'both',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT unique_company_reminder UNIQUE (company_id, reminder_type, days_before)
      )
    `)

    // 6. إدراج إعدادات التذكيرات الافتراضية
    console.log('📅 إدراج إعدادات التذكيرات...')
    const reminderSettings = [
      ['hearing', 7, true, 'both'],
      ['hearing', 3, true, 'both'],
      ['hearing', 1, true, 'both'],
      ['payment', 7, true, 'clients'],
      ['payment', 1, true, 'clients'],
      ['task', 3, true, 'employees'],
      ['task', 1, true, 'employees']
    ]

    for (const [type, days, enabled, audience] of reminderSettings) {
      await client.query(`
        INSERT INTO whatsapp_reminder_settings (company_id, reminder_type, days_before, enabled, target_audience)
        VALUES (1, $1, $2, $3, $4)
        ON CONFLICT (company_id, reminder_type, days_before) DO NOTHING
      `, [type, days, enabled, audience])
    }

    // 7. إنشاء جداول السجلات
    console.log('📊 إنشاء جداول السجلات...')
    await client.query(`
      CREATE TABLE IF NOT EXISTS whatsapp_local_messages (
        id SERIAL PRIMARY KEY,
        company_id INTEGER REFERENCES company_info(id) ON DELETE CASCADE,
        phone_number VARCHAR(20) NOT NULL,
        recipient_name VARCHAR(100),
        recipient_type VARCHAR(20),
        message_content TEXT NOT NULL,
        message_type VARCHAR(20) DEFAULT 'text',
        media_path TEXT,
        status VARCHAR(20) DEFAULT 'pending',
        whatsapp_message_id VARCHAR(100),
        error_message TEXT,
        sent_at TIMESTAMP,
        delivered_at TIMESTAMP,
        read_at TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    await client.query(`
      CREATE TABLE IF NOT EXISTS whatsapp_daily_stats (
        id SERIAL PRIMARY KEY,
        company_id INTEGER REFERENCES company_info(id) ON DELETE CASCADE,
        stat_date DATE NOT NULL,
        messages_sent INTEGER DEFAULT 0,
        messages_delivered INTEGER DEFAULT 0,
        messages_read INTEGER DEFAULT 0,
        messages_failed INTEGER DEFAULT 0,
        messages_received INTEGER DEFAULT 0,
        unique_contacts INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT unique_company_date UNIQUE (company_id, stat_date)
      )
    `)

    // 8. التحقق من النتائج
    const companyData = await client.query('SELECT * FROM company_info WHERE id = 1')
    const settingsCount = await client.query('SELECT COUNT(*) FROM whatsapp_company_settings WHERE company_id = 1')
    const remindersCount = await client.query('SELECT COUNT(*) FROM whatsapp_reminder_settings WHERE company_id = 1')

    console.log('\n✅ تم إعداد WhatsApp بنجاح!')
    console.log('📊 النتائج:')
    console.log(`   🏢 الشركة: ${companyData.rows[0]?.company_name}`)
    console.log(`   📱 رقم WhatsApp: ${companyData.rows[0]?.whatsapp_phone}`)
    console.log(`   🔧 WhatsApp مفعل: ${companyData.rows[0]?.whatsapp_enabled ? 'نعم' : 'لا'}`)
    console.log(`   ⚙️ عدد الإعدادات: ${settingsCount.rows[0]?.count}`)
    console.log(`   ⏰ عدد إعدادات التذكيرات: ${remindersCount.rows[0]?.count}`)

    console.log('\n🎉 يمكنك الآن الذهاب إلى http://localhost:3001/settings/whatsapp')
    console.log('   وستجد أن جميع الإعدادات مفعلة وجاهزة للاستخدام!')

  } catch (error) {
    console.error('❌ خطأ في إعداد WhatsApp:', error)
    throw error
  } finally {
    await client.end()
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات')
  }
}

// تشغيل السكريبت
if (require.main === module) {
  setupWhatsAppCompany()
    .then(() => {
      console.log('\n🎯 تم الانتهاء من إعداد WhatsApp بنجاح!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('\n💥 فشل في إعداد WhatsApp:', error.message)
      process.exit(1)
    })
}

module.exports = { setupWhatsAppCompany }
