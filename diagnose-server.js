// تشخيص شامل للخادم
const os = require('os');
const { execSync } = require('child_process');

console.log('🔍 تشخيص شامل للخادم');
console.log('='.repeat(50));

// 1. معلومات النظام
console.log('\n📊 معلومات النظام:');
console.log(`   - نظام التشغيل: ${os.type()} ${os.release()}`);
console.log(`   - المعمارية: ${os.arch()}`);
console.log(`   - الذاكرة: ${Math.round(os.totalmem() / 1024 / 1024 / 1024)} GB`);
console.log(`   - المعالج: ${os.cpus()[0].model}`);

// 2. فحص Node.js
console.log('\n🟢 Node.js:');
console.log(`   - الإصدار: ${process.version}`);
console.log(`   - المجلد: ${process.cwd()}`);

// 3. فحص المنافذ
console.log('\n🌐 فحص المنافذ:');
try {
  const netstat = execSync('netstat -an | findstr "LISTENING"', { encoding: 'utf8' });
  const ports = netstat.split('\n').filter(line => 
    line.includes(':7443') || line.includes(':8914') || line.includes(':80') || line.includes(':443')
  );
  
  ports.forEach(port => {
    if (port.includes(':7443')) console.log('   ✅ 7443: نشط');
    if (port.includes(':8914')) console.log('   ✅ 8914: نشط');
    if (port.includes(':80')) console.log('   ✅ 80: نشط');
    if (port.includes(':443')) console.log('   ✅ 443: نشط');
  });
} catch (error) {
  console.log('   ❌ خطأ في فحص المنافذ');
}

// 4. فحص جدار الحماية
console.log('\n🛡️ فحص جدار الحماية:');
try {
  const firewall = execSync('netsh advfirewall show allprofiles state', { encoding: 'utf8' });
  console.log('   - حالة جدار الحماية:', firewall.includes('ON') ? '🔴 مفعل' : '🟢 معطل');
} catch (error) {
  console.log('   ❌ لا يمكن فحص جدار الحماية');
}

// 5. فحص الشبكة
console.log('\n🌍 فحص الشبكة:');
try {
  const ipconfig = execSync('ipconfig', { encoding: 'utf8' });
  const lines = ipconfig.split('\n');
  lines.forEach(line => {
    if (line.includes('IPv4') && !line.includes('127.0.0.1')) {
      const ip = line.split(':')[1]?.trim();
      if (ip) console.log(`   - IP الداخلي: ${ip}`);
    }
  });
} catch (error) {
  console.log('   ❌ خطأ في فحص الشبكة');
}

// 6. اختبار الاتصال الخارجي
console.log('\n🔗 اختبار الاتصال:');
const testUrls = [
  'http://localhost:7443',
  'http://localhost:8914'
];

testUrls.forEach(url => {
  try {
    const http = require('http');
    const urlObj = new URL(url);
    
    const req = http.request({
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: '/',
      timeout: 5000
    }, (res) => {
      console.log(`   ✅ ${url}: يستجيب (${res.statusCode})`);
    });
    
    req.on('error', () => {
      console.log(`   ❌ ${url}: لا يستجيب`);
    });
    
    req.on('timeout', () => {
      console.log(`   ⏰ ${url}: انتهت المهلة`);
    });
    
    req.end();
  } catch (error) {
    console.log(`   ❌ ${url}: خطأ في الاختبار`);
  }
});

console.log('\n' + '='.repeat(50));
console.log('انتهاء التشخيص');
