import { Pool } from 'pg'
import fs from 'fs'
import path from 'path'

// تحميل ملف التوجيه
let routingConfig: any = null
try {
  const configPath = path.join(process.cwd(), 'routing.config.json')
  const configData = fs.readFileSync(configPath, 'utf8')
  routingConfig = JSON.parse(configData)
} catch (error) {
  console.error('❌ خطأ في تحميل ملف التوجيه:', error)
}

// ملاحظة: لم نعد نعتمد على المنفذ الداخلي لتحديد قاعدة البيانات.
// الأولوية الآن لـ DATABASE_URL ثم X_DATABASE التي يمررها الخادم الموحد لكل عملية Next.js.

// تهيئة إعدادات الاتصال وفق الأولوية: DATABASE_URL -> X_DATABASE -> routing.config.json -> افتراضي
const buildPool = () => {
  // 1) إن وُجدت DATABASE_URL نستخدمها مباشرة
  if (process.env.DATABASE_URL) {
    return new Pool({
      connectionString: process.env.DATABASE_URL,
      ssl: false,
      max: 20,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    })
  }

  // 2) إن وُجد X_DATABASE نستخدم إعدادات default_config مع اسم القاعدة المحدد
  if (process.env.X_DATABASE) {
    const dc = routingConfig?.default_config || {}
    const user = process.env.DB_USER || dc.db_user || 'postgres'
    const host = process.env.DB_HOST || dc.db_host || 'localhost'
    const password = process.env.DB_PASSWORD || dc.db_password || 'yemen123'
    const port = Number(process.env.DB_PORT || dc.db_port || 5432)
    const database = process.env.X_DATABASE
    const connectionString = `postgresql://${user}:${password}@${host}:${port}/${database}?client_encoding=UTF8`
    return new Pool({
      connectionString,
      ssl: false,
      max: 20,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    })
  }

  // 3) محاولة القراءة من routing.config.json عبر المنفذ الخارجي إن تم تمريره صراحة
  // ملاحظة: المنفذ الداخلي لعملية Next.js (PORT=3000/3001) لا يعكس المنفذ الخارجي.
  // لذا نسمح بتمرير EXTERNAL_PORT عبر البيئة إن كان هناك استخدام مباشر بدون الخادم الموحد.
  const externalPort = process.env.EXTERNAL_PORT
  if (externalPort && routingConfig && routingConfig.routes[externalPort]) {
    const route = routingConfig.routes[externalPort]
    const dc = routingConfig.default_config
    const user = process.env.DB_USER || dc.db_user
    const host = process.env.DB_HOST || dc.db_host
    const password = process.env.DB_PASSWORD || dc.db_password
    const port = Number(process.env.DB_PORT || dc.db_port)
    const database = route.database
    const connectionString = `postgresql://${user}:${password}@${host}:${port}/${database}?client_encoding=UTF8`
    return new Pool({
      connectionString,
      ssl: false,
      max: 20,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    })
  }

  // 4) افتراضي آمن: قاعدة mohammi محلياً
  console.warn('⚠️ لم تتوفر DATABASE_URL أو X_DATABASE أو EXTERNAL_PORT صالحة — سيتم استخدام الإعدادات الافتراضية (mohammi)')
  const user = process.env.DB_USER || 'postgres'
  const host = process.env.DB_HOST || 'localhost'
  const password = process.env.DB_PASSWORD || 'yemen123'
  const port = Number(process.env.DB_PORT || 5432)
  const database = process.env.DB_NAME || 'mohammi'
  const connectionString = `postgresql://${user}:${password}@${host}:${port}/${database}?client_encoding=UTF8`
  return new Pool({
    connectionString,
    ssl: false,
    max: 20,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
  })
}

const pool = buildPool()

// إعدادات قياس الأداء
const SLOW_QUERY_MS = Number(process.env.SLOW_QUERY_MS || 200)
const LOG_DIR = path.join(process.cwd(), 'logs')
const SLOW_LOG_FILE = path.join(LOG_DIR, 'slow-queries.log')

function ensureLogDir() {
  try {
    if (!fs.existsSync(LOG_DIR)) fs.mkdirSync(LOG_DIR, { recursive: true })
  } catch {}
}

export async function query(text: string, params?: any[]) {
  const client = await pool.connect()
  const startedAt = Date.now()
  try {
    // تعيين ترميز UTF8 للجلسة
    await client.query('SET client_encoding TO UTF8')
    const result = await client.query(text, params)
    const duration = Date.now() - startedAt
    if (duration >= SLOW_QUERY_MS) {
      const preview = (text || '').replace(/\s+/g, ' ').trim().slice(0, 500)
      const line = `[${new Date().toISOString()}] ${duration}ms rows=${result?.rowCount ?? 'n/a'} sql="${preview}" params=${JSON.stringify(params || [])}\n`
      // طباعة تحذير في الخادم
      console.warn(`⏱️ استعلام بطيء: ${duration}ms | rows=${result?.rowCount ?? 'n/a'} | ${preview}`)
      // حفظ في ملف
      try { ensureLogDir(); fs.appendFileSync(SLOW_LOG_FILE, line, { encoding: 'utf8' }) } catch {}
    }
    return result
  } catch (err) {
    const duration = Date.now() - startedAt
    const preview = (text || '').replace(/\s+/g, ' ').trim().slice(0, 500)
    console.error(`❌ خطأ استعلام بعد ${duration}ms: ${preview}`, err)
    throw err
  } finally {
    client.release()
  }
}

export { pool }
