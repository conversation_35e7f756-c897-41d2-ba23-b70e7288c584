'use client'

import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertTriangle, ArrowLeft, Home, LogOut } from 'lucide-react'

export default function UnauthorizedPage() {
  const router = useRouter()
  const { user, logout } = useAuth()

  const handleGoBack = () => {
    router.back()
  }

  const handleGoHome = () => {
    if (user?.type === 'client') {
      router.push('/client-portal')
    } else {
      router.push('/dashboard')
    }
  }

  const handleLogout = () => {
    logout()
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-100 flex items-center justify-center p-4" dir="rtl">
      <div className="w-full max-w-md">
        <Card className="shadow-lg border-red-200">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <div className="bg-red-100 p-3 rounded-full">
                <AlertTriangle className="h-12 w-12 text-red-600" />
              </div>
            </div>
            <CardTitle className="text-2xl font-bold text-red-800">
              غير مخول للوصول
            </CardTitle>
          </CardHeader>
          
          <CardContent className="space-y-6">
            <div className="text-center space-y-3">
              <p className="text-gray-700 text-lg">
                عذراً، لا تملك الصلاحيات اللازمة للوصول إلى هذه الصفحة
              </p>
              
              {user && (
                <div className="bg-gray-50 p-4 rounded-lg text-sm">
                  <p><strong>المستخدم:</strong> {user.name || user.username}</p>
                  <p><strong>الدور:</strong> {user.role_display_name || user.role}</p>
                  <p><strong>عدد الصلاحيات:</strong> {user.permissions?.length || 0}</p>
                </div>
              )}
              
              <p className="text-gray-600">
                إذا كنت تعتقد أن هذا خطأ، يرجى التواصل مع مدير النظام
              </p>
            </div>

            <div className="space-y-3">
              <Button 
                onClick={handleGoBack}
                variant="outline" 
                className="w-full"
              >
                <ArrowLeft className="h-4 w-4 ml-2" />
                العودة للصفحة السابقة
              </Button>
              
              <Button 
                onClick={handleGoHome}
                className="w-full bg-blue-600 hover:bg-blue-700"
              >
                <Home className="h-4 w-4 ml-2" />
                الذهاب للصفحة الرئيسية
              </Button>
              
              <Button 
                onClick={handleLogout}
                variant="destructive" 
                className="w-full"
              >
                <LogOut className="h-4 w-4 ml-2" />
                تسجيل الخروج
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
