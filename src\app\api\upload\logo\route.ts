import { NextRequest, NextResponse } from 'next/server'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { existsSync } from 'fs'

export async function POST(request: NextRequest) {
  try {

    const formData = await request.formData()
    const file = formData.get('logo') as File

    if (!file) {

      return NextResponse.json(
        { success: false, error: 'لم يتم العثور على ملف' },
        { status: 400 }
      )
    }

    // التحقق من نوع الملف
    if (!file.type.startsWith('image/')) {

      return NextResponse.json(
        { success: false, error: 'يرجى اختيار ملف صورة صالح' },
        { status: 400 }
      )
    }

    // التحقق من حجم الملف (أقل من 5MB)
    if (file.size > 5 * 1024 * 1024) {

      return NextResponse.json(
        { success: false, error: 'حجم الملف يجب أن يكون أقل من 5 ميجابايت' },
        { status: 400 }
      )
    }

    // إنشاء مجلد uploads إذا لم يكن موجوداً
    const uploadsDir = join(process.cwd(), 'public', 'uploads', 'logos')
    if (!existsSync(uploadsDir)) {
      await mkdir(uploadsDir, { recursive: true })

    }

    // إنشاء اسم ملف فريد
    const timestamp = Date.now()
    const fileExtension = file.name.split('.').pop()
    const fileName = `logo_${timestamp}.${fileExtension}`
    const filePath = join(uploadsDir, fileName)

    // تحويل الملف إلى buffer وحفظه
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    await writeFile(filePath, buffer)

    // إنشاء URL للملف
    const logoUrl = `/uploads/logos/${fileName}`

    return NextResponse.json({
      success: true,
      logoUrl: logoUrl,
      message: 'تم رفع الشعار بنجاح'
    })

  } catch (error) {
    console.error('❌ خطأ في رفع الشعار:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'حدث خطأ أثناء رفع الشعار',
        details: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    )
  }
}

// السماح بـ GET للتحقق من حالة API
export async function GET() {
  return NextResponse.json({
    success: true,
    message: 'API رفع الشعار يعمل بشكل صحيح',
    maxFileSize: '5MB',
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
  })
}
