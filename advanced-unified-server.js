/**
 * الخادم الموحد المتقدم - يدعم توجيه متعدد المنافذ لقواعد بيانات مختلفة
 *
 * المميزات:
 * - يعتمد على ملف توجيه خارجي لتحديد العلاقة بين المنافذ وقواعد البيانات
 * - يمكن إضافة منافذ وقواعد بيانات جديدة بدون تعديل الكود
 * - يتحقق من اتصال قواعد البيانات قبل بدء الخادم
 * - يدعم إعادة تشغيل تلقائي في حالة الأخطاء
 * - يدعم تخصيص رسائل الترحيب والإشعارات لكل قاعدة بيانات
 * - يدعم تسجيل الطلبات والأخطاء
 * - يدعم تشغيل خادم HTTPS اختياري باستخدام شهادات من default_config.ssl في routing.config.json
 */

const http = require('http');
const https = require('https');

const fs = require('fs');
const path = require('path');
const { Client } = require('pg');
const { execSync } = require('child_process');

// ======== تهيئة النظام ========
console.log('\n🏛️ الخادم الموحد المتقدم - الإصدار 2.0');
console.log('📋 يدعم توجيه متعدد المنافذ لقواعد بيانات مختلفة');
console.log('🔧 يعتمد على ملف التوجيه للإعدادات');
console.log('='.repeat(60));

// ======== تحميل ملف التوجيه ========
let routingConfig;
const configPath = path.join(__dirname, 'routing.config.json');

try {
  if (fs.existsSync(configPath)) {
    const configData = fs.readFileSync(configPath, 'utf8');
    routingConfig = JSON.parse(configData);
    console.log('✅ تم تحميل ملف التوجيه بنجاح');
    console.log(`📋 عدد المنافذ المكونة: ${Object.keys(routingConfig.routes).length}`);
  } else {
    throw new Error('ملف التوجيه غير موجود');
  }

// ======== خادم إدارة التوجيه (المنفذ 8000) ========
function createAdminServer() {
  const admin = http.createServer(async (req, res) => {
    try {
      // السماح فقط بالوصول المحلي
      if (!isLocalRequest(req)) {
        res.statusCode = 403;
        res.setHeader('Content-Type', 'application/json; charset=utf-8');
        return res.end(JSON.stringify({ ok: false, error: 'forbidden' }));
      }

      const url = new URL(req.url, `http://localhost:8000`);

      // GET /admin/status
      if (req.method === 'GET' && url.pathname === '/admin/status') {
        res.setHeader('Content-Type', 'application/json; charset=utf-8');
        return res.end(JSON.stringify(getStatusSnapshot(), null, 2));
      }

      // POST /admin/reload -> إعادة تحميل ملف التوجيه من القرص
      if (req.method === 'POST' && url.pathname === '/admin/reload') {
        reloadRoutingConfigFromDisk();
        res.setHeader('Content-Type', 'application/json; charset=utf-8');
        return res.end(JSON.stringify({ ok: true }));
      }

      // POST /admin/routes -> إضافة مسار جديد وتشغيله
      if (req.method === 'POST' && url.pathname === '/admin/routes') {
        const body = await readJsonBody(req);
        const result = await addRouteAndStart(body);
        res.setHeader('Content-Type', 'application/json; charset=utf-8');
        return res.end(JSON.stringify({ ok: true, result }, null, 2));
      }

      // PUT /admin/routes/:port -> تعديل مسار موجود (تحديث معلومات فقط و/أو internal_next_port)
      if (req.method === 'PUT' && url.pathname.startsWith('/admin/routes/')) {
        const portStr = url.pathname.split('/').pop();
        if (!portStr || !routingConfig.routes[portStr]) {
          res.statusCode = 404;
          res.setHeader('Content-Type', 'application/json; charset=utf-8');
          return res.end(JSON.stringify({ ok: false, error: 'route_not_found' }));
        }
        const body = await readJsonBody(req);
        const newCfg = JSON.parse(JSON.stringify(routingConfig));
        newCfg.routes[portStr] = { ...routingConfig.routes[portStr], ...body };
        writeRoutingConfigWithBackup(newCfg);
        reloadRoutingConfigFromDisk();
        res.setHeader('Content-Type', 'application/json; charset=utf-8');
        return res.end(JSON.stringify({ ok: true, route: routingConfig.routes[portStr] }, null, 2));
      }

      // DELETE /admin/routes/:port -> تعطيل مسار موجود (enabled=false)
      if (req.method === 'DELETE' && url.pathname.startsWith('/admin/routes/')) {
        const portStr = url.pathname.split('/').pop();
        if (!portStr || !routingConfig.routes[portStr]) {
          res.statusCode = 404;
          res.setHeader('Content-Type', 'application/json; charset=utf-8');
          return res.end(JSON.stringify({ ok: false, error: 'route_not_found' }));
        }
        const newCfg = JSON.parse(JSON.stringify(routingConfig));
        newCfg.routes[portStr].enabled = false;
        writeRoutingConfigWithBackup(newCfg);
        reloadRoutingConfigFromDisk();
        res.setHeader('Content-Type', 'application/json; charset=utf-8');
        return res.end(JSON.stringify({ ok: true }));
      }

      // 404
      res.statusCode = 404;
      res.setHeader('Content-Type', 'application/json; charset=utf-8');
      return res.end(JSON.stringify({ ok: false, error: 'not_found' }));
    } catch (e) {
      res.statusCode = 500;
      res.setHeader('Content-Type', 'application/json; charset=utf-8');
      return res.end(JSON.stringify({ ok: false, error: e.message }));
    }
  });

  admin.listen(8000, '127.0.0.1', () => {
    console.log('🛠️ خادم إدارة التوجيه يعمل محلياً على http://127.0.0.1:8000');
    console.log('   • GET  /admin/status');
    console.log('   • POST /admin/reload');
    console.log('   • POST /admin/routes');
    console.log('   • PUT  /admin/routes/:port');
    console.log('   • DELETE /admin/routes/:port');
  });
}
} catch (error) {
  console.error('❌ خطأ في تحميل ملف التوجيه:', error.message);
  process.exit(1);
}

// ======== متغيرات النظام ========
const servers = [];
const activeConnections = {};
const systemSettings = routingConfig.system_settings || {
  auto_create_missing_databases: true,
  validate_database_connection: true,
  log_all_requests: true,
  show_detailed_errors: true,
  auto_restart_on_error: true,
  connection_timeout: 5000,
  max_request_size: "50mb"
};
const defaultConfig = routingConfig.default_config || {
  db_host: "localhost",
  db_port: 5432,
  db_user: "postgres",
  db_password: "yemen123",
  next_port: 3300,
  default_theme: "#1e40af",
  default_welcome: "مرحباً بكم في نظام إدارة المحاماة",
  default_notification_prefix: "إشعار: "
};

// خرائط تتبع الخوادم والعمليات النشطة
const serverByPort = {};
const nextProcessByPort = {};

// ======== دوال مساعدة ========

/**
 * تعقيم قيمة رأس HTTP لإزالة أي محارف غير مسموح بها (غير ASCII القابلة للطباعة)
 * Node.js يرفض رؤوس تحتوي محارف خارج النطاق \x20-\x7E
 * @param {any} value
 * @returns {string}
 */
function sanitizeHeaderValue(value) {
  let str = '';
  if (value === undefined || value === null) return '';
  if (typeof value !== 'string') str = String(value);
  else str = value;
  // السماح فقط بمحارف ASCII القابلة للطباعة واستبدال الباقي بـ '?'
  return str.replace(/[^\x20-\x7E]/g, '?');
}

/**
 * ترميز نص UTF-8 إلى Base64 لإرساله بأمان عبر الرؤوس كمقابل كامل للغة العربية
 * @param {any} value
 * @returns {string}
 */
function encodeBase64Utf8(value) {
  const str = value == null ? '' : String(value);
  return Buffer.from(str, 'utf8').toString('base64');
}

/**
 * تسجيل الأحداث في ملف
 * @param {string} message - الرسالة المراد تسجيلها
 * @param {string} type - نوع الرسالة (info, error, warning)
 */
function logToFile(message, type = 'info') {
  const logDir = path.join(__dirname, 'logs');
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }

  const date = new Date();
  const timestamp = date.toISOString().replace(/T/, ' ').replace(/\..+/, '');
  const logFile = path.join(logDir, `unified-server-${date.toISOString().split('T')[0]}.log`);

  const logMessage = `[${timestamp}] [${type.toUpperCase()}] ${message}\n`;

  fs.appendFileSync(logFile, logMessage);
}

/**
 * فحص أن الطلب محلي فقط للوصول لنقاط إدارة الخادم
 * @param {import('http').IncomingMessage} req
 */
function isLocalRequest(req) {
  try {
    const ip = (req.socket && (req.socket.remoteAddress || '')) || '';
    return ip.includes('127.0.0.1') || ip.includes('::1') || ip === '::ffff:127.0.0.1';
  } catch {
    return false;
  }
}

/**
 * قراءة جسم الطلب JSON
 * @param {import('http').IncomingMessage} req
 * @returns {Promise<any>}
 */
function readJsonBody(req) {
  return new Promise((resolve, reject) => {
    let data = '';
    req.on('data', (chunk) => {
      data += chunk;
      // حماية من الأجسام الكبيرة جداً
      if (data.length > 1e6) {
        req.destroy();
        reject(new Error('Payload too large'));
      }
    });
    req.on('end', () => {
      if (!data) return resolve({});
      try { resolve(JSON.parse(data)); } catch (e) { reject(new Error('Invalid JSON')); }
    });
    req.on('error', reject);
  });
}

/**
 * إعداد رؤوس CORS لواجهات الإدارة
 * @param {import('http').ServerResponse} res
 */
// تمت إزالة دعم CORS الخاص بواجهات الإدارة بعد فصل لوحة التحكم

/**
 * إعادة تحميل ملف التوجيه من القرص
 */
function reloadRoutingConfigFromDisk() {
  const configData = fs.readFileSync(configPath, 'utf8');
  const parsed = JSON.parse(configData);
  routingConfig = parsed;
}

/**
 * كتابة ملف التوجيه مع إنشاء نسخة احتياطية
 */
function writeRoutingConfigWithBackup(newConfig) {
  const ts = new Date();
  const stamp = ts.toISOString().replace(/[:T]/g, '-').replace(/\..+/, '');
  const backupPath = path.join(__dirname, `routing.config.backup-${stamp}.json`);
  try {
    // إنشاء نسخة احتياطية
    fs.copyFileSync(configPath, backupPath);
  } catch (e) {
    // في حال عدم وجود الملف لأي سبب، نتجاهل النسخ الاحتياطي
  }
  fs.writeFileSync(configPath, JSON.stringify(newConfig, null, 2), 'utf8');
}

/**
 * لقطة حالة حالية للمنافذ والخوادم
 */
function getStatusSnapshot() {
  const routes = routingConfig.routes || {};
  const list = Object.entries(routes).map(([port, route]) => ({
    port,
    route,
    running: !!serverByPort[port],
    next_attached: !!nextProcessByPort[port]
  }));
  return { routes: list, system_settings: systemSettings, default_config: defaultConfig };
}

/**
 * إضافة مسار جديد إلى ملف التوجيه وتشغيله فوراً دون كسر القائم
 */
async function addRouteAndStart(routeInput) {
  const {
    external_port,
    internal_next_port,
    database,
    company_name,
    homepage,
    theme_color,
    welcome_message,
    notification_prefix,
    enabled
  } = routeInput || {};

  if (!external_port || !database) {
    throw new Error('external_port و database حقول مطلوبة');
  }
  const portStr = String(external_port);
  if (routingConfig.routes[portStr]) {
    throw new Error(`المنفذ ${portStr} معرف مسبقاً في ملف التوجيه`);
  }

  // تحقق اتصال قاعدة البيانات (بالاسم فقط وفق الإعدادات الافتراضية)
  if (systemSettings.validate_database_connection) {
    const ok = await checkDatabaseConnection(database);
    if (!ok) throw new Error(`تعذر التحقق من قاعدة البيانات: ${database}`);
  }

  const route = {
    database,
    company_name: company_name || '',
    homepage: homepage || '/',
    theme_color: theme_color || defaultConfig.default_theme,
    welcome_message: welcome_message || defaultConfig.default_welcome,
    notification_prefix: notification_prefix || defaultConfig.default_notification_prefix,
    enabled: enabled !== false,
    internal_next_port: internal_next_port || defaultConfig.next_port
  };

  // تحديث الملف على القرص مع النسخ الاحتياطي
  const newConfig = JSON.parse(JSON.stringify(routingConfig));
  newConfig.routes[portStr] = route;
  writeRoutingConfigWithBackup(newConfig);
  reloadRoutingConfigFromDisk();

  // تشغيل Next.js الداخلي (قد يفشل إن كان المنفذ الداخلي مستخدماً – نكتفي بمحاولة آمنة)
  try {
    const nextProc = startNextServer(route.internal_next_port, route);
    nextProcessByPort[portStr] = nextProc;
  } catch (e) {
    console.warn(`⚠️ تعذر تشغيل Next.js الداخلي للمنفذ ${route.internal_next_port}: ${e.message}`);
  }

  // إنشاء وتشغيل خادم proxy الجديد
  const server = createProxyServer(parseInt(portStr, 10), route);
  await new Promise((resolve, reject) => {
    server.once('error', (err) => reject(err));
    server.listen(parseInt(portStr, 10), '0.0.0.0', () => resolve());
  });
  serverByPort[portStr] = server;
  activeConnections[portStr] = route.database;

  logToFile(`تمت إضافة وتشغيل منفذ جديد ${portStr} لقاعدة ${route.database}`, 'info');
  return { port: portStr, route };
}

/**
 * التحقق من اتصال قاعدة البيانات
 * @param {string} dbName - اسم قاعدة البيانات
 * @returns {Promise<boolean>} - نجاح الاتصال
 */
async function checkDatabaseConnection(dbName) {
  const client = new Client({
    host: defaultConfig.db_host || "localhost",
    port: defaultConfig.db_port || 5432,
    database: dbName,
    user: defaultConfig.db_user || "postgres",
    password: String(defaultConfig.db_password || "yemen123"),
    connectionTimeoutMillis: systemSettings.connection_timeout || 5000
  });

  try {
    await client.connect();
    const result = await client.query('SELECT NOW()');
    await client.end();
    return true;
  } catch (error) {
    console.error(`❌ فشل الاتصال بقاعدة البيانات ${dbName}:`, error.message);
    logToFile(`فشل الاتصال بقاعدة البيانات ${dbName}: ${error.message}`, 'error');

    if (systemSettings.auto_create_missing_databases && error.code === '3D000') {
      console.log(`🔧 محاولة إنشاء قاعدة البيانات ${dbName}...`);
      return await createDatabase(dbName);
    }

    return false;
  }
}

/**
 * إنشاء قاعدة بيانات جديدة
 * @param {string} dbName - اسم قاعدة البيانات
 * @returns {Promise<boolean>} - نجاح الإنشاء
 */
async function createDatabase(dbName) {
  const client = new Client({
    host: defaultConfig.db_host || "localhost",
    port: defaultConfig.db_port || 5432,
    database: 'postgres', // الاتصال بقاعدة البيانات الافتراضية
    user: defaultConfig.db_user || "postgres",
    password: String(defaultConfig.db_password || "yemen123")
  });

  try {
    await client.connect();
    await client.query(`CREATE DATABASE ${dbName} ENCODING 'UTF8'`);
    await client.end();

    console.log(`✅ تم إنشاء قاعدة البيانات ${dbName} بنجاح`);
    logToFile(`تم إنشاء قاعدة البيانات ${dbName} بنجاح`, 'info');

    // تهيئة قاعدة البيانات الجديدة
    return await initializeNewDatabase(dbName);
  } catch (error) {
    console.error(`❌ فشل إنشاء قاعدة البيانات ${dbName}:`, error.message);
    logToFile(`فشل إنشاء قاعدة البيانات ${dbName}: ${error.message}`, 'error');
    return false;
  }
}

/**
 * تهيئة قاعدة بيانات جديدة
 * @param {string} dbName - اسم قاعدة البيانات
 * @returns {Promise<boolean>} - نجاح التهيئة
 */
async function initializeNewDatabase(dbName) {
  try {
    // يمكن استدعاء سكريبت خارجي لتهيئة قاعدة البيانات
    console.log(`🔧 تهيئة قاعدة البيانات ${dbName}...`);

    // مثال: استدعاء سكريبت Node.js
    // execSync(`node setup_database.js --database=${dbName}`, { stdio: 'inherit' });

    console.log(`✅ تم تهيئة قاعدة البيانات ${dbName} بنجاح`);
    logToFile(`تم تهيئة قاعدة البيانات ${dbName} بنجاح`, 'info');
    return true;
  } catch (error) {
    console.error(`❌ فشل تهيئة قاعدة البيانات ${dbName}:`, error.message);
    logToFile(`فشل تهيئة قاعدة البيانات ${dbName}: ${error.message}`, 'error');
    return false;
  }
}

const { spawn } = require('child_process');

/**
 * تشغيل Next.js على منفذ داخلي لكل مسار مع قاعدة بيانات مخصصة وثابتة
 * @param {number} internalPort - رقم المنفذ الداخلي لنسخة Next.js الخاصة بالمسار
 * @param {object} route - إعدادات التوجيه
 */
function startNextServer(internalPort, route) {
  console.log(`🚀 تشغيل Next.js الداخلي على المنفذ ${internalPort} لقاعدة البيانات ${route.database}`);

  // تحديد متغيرات البيئة لقاعدة البيانات
  const env = {
    ...process.env,
    PORT: internalPort,
    X_DATABASE: route.database,
    X_COMPANY: route.company_name,
    X_THEME_COLOR: route.theme_color,
    X_WELCOME_MESSAGE: route.welcome_message,
    X_NOTIFICATION_PREFIX: route.notification_prefix,
    X_HOMEPAGE: route.homepage || '/',
    DATABASE_URL: `postgresql://${defaultConfig.db_user}:${defaultConfig.db_password}@${defaultConfig.db_host}:${defaultConfig.db_port}/${route.database}`
  };

  // تشغيل Next.js على المنفذ الداخلي المحدد
  const nextProcess = spawn('npm', ['run', 'dev', '--', '-p', internalPort.toString(), '--hostname', '127.0.0.1'], {
    env: env,
    stdio: 'inherit',
    shell: true
  });

  nextProcess.on('error', (error) => {
    console.error(`❌ خطأ في تشغيل Next.js على المنفذ الداخلي ${internalPort}:`, error.message);
    logToFile(`خطأ في تشغيل Next.js على المنفذ الداخلي ${internalPort}: ${error.message}`, 'error');
  });

  nextProcess.on('close', (code) => {
    console.log(`⚠️ Next.js على المنفذ الداخلي ${internalPort} توقف بكود: ${code}`);
    if (systemSettings.auto_restart_on_error && code !== 0) {
      console.log(`🔄 إعادة تشغيل Next.js على المنفذ الداخلي ${internalPort}...`);
      setTimeout(() => startNextServer(internalPort, route), 5000);
    }
  });

  return nextProcess;
}

/**
 * إنشاء خادم Next.js مباشر على المنفذ المحدد
 * @param {number} port - رقم المنفذ
 * @param {object} route - إعدادات التوجيه
 * @returns {ChildProcess} - عملية Next.js
 */
function createDirectServer(port, route) {
  console.log(`🔧 إنشاء خادم مباشر على المنفذ ${port} لقاعدة البيانات ${route.database}`);
  console.log(`   🌐 سيستمع على جميع الواجهات (0.0.0.0:${port})`);
  console.log(`   🗄️ قاعدة البيانات: ${route.database}`);
  console.log(`   🏢 الشركة: ${route.company_name}`);
  console.log(`   🎨 لون السمة: ${route.theme_color}`);

  // إنشاء ملف .env مؤقت لهذا المنفذ
  const envContent = `
PORT=${port}
X_DATABASE=${route.database}
X_COMPANY=${route.company_name}
X_THEME_COLOR=${route.theme_color}
X_WELCOME_MESSAGE=${route.welcome_message}
X_NOTIFICATION_PREFIX=${route.notification_prefix}
X_HOMEPAGE=${route.homepage || '/'}
DATABASE_URL=postgresql://${defaultConfig.db_user}:${defaultConfig.db_password}@${defaultConfig.db_host}:${defaultConfig.db_port}/${route.database}
DB_HOST=${defaultConfig.db_host}
DB_PORT=${defaultConfig.db_port}
DB_USER=${defaultConfig.db_user}
DB_PASSWORD=${defaultConfig.db_password}
DB_NAME=${route.database}
JWT_SECRET=${defaultConfig.jwt_secret}
`;

  // كتابة ملف البيئة المؤقت
  const envFile = `.env.${port}`;
  fs.writeFileSync(path.join(__dirname, envFile), envContent);

  console.log(`✅ تم إنشاء ملف البيئة: ${envFile}`);

  return startNextServer(port, route);
}

/**
 * إنشاء خادم Proxy يوجّه إلى نظام Next.js الأصلي على المنفذ المحدد
 * @param {number} port - رقم المنفذ المحلي الذي سنستمع عليه
 * @param {object} route - إعدادات المسار (تحتوي معلومات قاعدة البيانات)
 * @returns {import('http').Server} - خادم HTTP
 */
function createProxyServer(port, route) {
  const NEXT_PORT = (defaultConfig && defaultConfig.next_port) ? defaultConfig.next_port : 3300;

  // إعداد معالج الطلبات المشترك لكلا النمطين HTTP/HTTPS
  const handler = (req, res) => {
    try {
      const pathToUse = (req.url === '/' && route.homepage) ? route.homepage : req.url;
      const targetPort = route.internal_next_port || (defaultConfig && defaultConfig.next_port) || 3300;
      const options = {
        // استخدم localhost لدعم IPv4/IPv6 حسب توافر الخادم الداخلي
        hostname: 'localhost',
        port: targetPort,
        path: pathToUse,
        method: req.method,
        headers: {
          ...req.headers,
          // القيم الآمنة ASCII فقط
          'x-database': sanitizeHeaderValue(route.database),
          'x-company': sanitizeHeaderValue(route.company_name || ''),
          'x-theme-color': sanitizeHeaderValue(route.theme_color || ''),
          'x-homepage': sanitizeHeaderValue(route.homepage || '/'),
          'x-proxied-by': 'advanced-unified-server',
          // تمرير معلومات المنفذ/المضيف الخارجي لتحديد الجلسة الصحيحة داخل التطبيق
          'x-forwarded-host': sanitizeHeaderValue(req.headers.host || `127.0.0.1:${port}`),
          'x-forwarded-port': String(port),
          'x-forwarded-proto': (req.socket && req.socket.encrypted) ? 'https' : 'http',
          // نسخ مشفرة Base64 للقيم النصية الكاملة (تدعم العربية)
          'x-company-b64': encodeBase64Utf8(route.company_name || ''),
          'x-welcome-message-b64': encodeBase64Utf8(route.welcome_message || ''),
          'x-notification-prefix-b64': encodeBase64Utf8(route.notification_prefix || '')
        }
      };

      const proxyReq = http.request(options, (proxyRes) => {
        res.writeHead(proxyRes.statusCode || 502, proxyRes.headers);
        proxyRes.pipe(res);
      });

      proxyReq.on('error', (error) => {
        res.statusCode = 502;
        res.setHeader('Content-Type', 'text/html; charset=utf-8');
        const html = `<!doctype html><html lang="ar"><head><meta charset="utf-8"/><title>خطأ في الاتصال</title></head><body>
        <h3>الخادم الأصلي غير متاح</h3>
        <p>يحاول الخادم الوسيط التوجيه إلى Next.js على المنفذ ${targetPort} لكنه غير متاح حالياً.</p>
        <p><strong>المنفذ الحالي:</strong> ${port}</p>
        <p><strong>قاعدة البيانات:</strong> ${route.database}</p>
        <p><strong>الخطأ:</strong> ${error.message}</p>
        </body></html>`;
        res.end(html);
      });

      req.pipe(proxyReq);
    } catch (error) {
      res.statusCode = 500;
      res.setHeader('Content-Type', 'text/plain; charset=utf-8');
      res.end(`خطأ داخلي في الخادم الوسيط: ${error.message}`);
    }
  };

  // اختيار HTTP أو HTTPS حسب إعدادات ssl في ملف التكوين
  let server;
  try {
    const ssl = (routingConfig && routingConfig.default_config && routingConfig.default_config.ssl) ? routingConfig.default_config.ssl : {};
    if (ssl.enabled && ssl.key_path && ssl.cert_path && fs.existsSync(ssl.key_path) && fs.existsSync(ssl.cert_path)) {
      const credentials = {
        key: fs.readFileSync(ssl.key_path),
        cert: fs.readFileSync(ssl.cert_path)
      };
      if (ssl.ca_path && fs.existsSync(ssl.ca_path)) {
        credentials.ca = fs.readFileSync(ssl.ca_path);
      }
      server = https.createServer(credentials, handler);
      console.log(`🔐 تم تفعيل HTTPS على المنفذ ${port}`);
    } else {
      server = http.createServer(handler);
      console.log(`🌐 يعمل HTTP على المنفذ ${port}`);
    }
  } catch (e) {
    console.warn(`⚠️ تعذّر تهيئة HTTPS بسبب: ${e.message} — سيتم استخدام HTTP بدلاً منه`);
    server = http.createServer(handler);
  }

  return server;
}

/**
 * التحقق من جميع قواعد البيانات
 * @returns {Promise<object>} - نتائج التحقق
 */
async function validateAllDatabases() {
  console.log('\n🔍 التحقق من قواعد البيانات...');

  const results = {};

  for (const [port, route] of Object.entries(routingConfig.routes)) {
    console.log(`\n🗄️ التحقق من قاعدة البيانات ${route.database} للمنفذ ${port}...`);

    const isConnected = await checkDatabaseConnection(route.database);
    results[port] = {
      database: route.database,
      connected: isConnected
    };

    if (isConnected) {
      console.log(`✅ قاعدة البيانات ${route.database} متصلة بنجاح`);
    } else {
      console.error(`❌ فشل الاتصال بقاعدة البيانات ${route.database}`);

      if (!systemSettings.auto_create_missing_databases) {
        console.log(`💡 يمكنك تفعيل إنشاء قواعد البيانات تلقائياً في ملف التوجيه`);
      }
    }
  }

  return results;
}

/**
 * بدء تشغيل جميع الخوادم
 * @param {object} dbResults - نتائج التحقق من قواعد البيانات
 */
async function startAllServers(dbResults) {
console.log('\n🚀 بدء تشغيل الخوادم...');

for (const [port, route] of Object.entries(routingConfig.routes)) {
try {
// تخطي المنافذ المعطلة
if (route && route.enabled === false) {
console.log(`⚠️ تخطي المنفذ ${port} - معطّل في ملف التوجيه`);
continue;
}
      console.log(`\n📡 إعداد المنفذ ${port}...`);
      console.log(`   🗄️ قاعدة البيانات: ${route.database}`);
      console.log(`   🏢 الشركة: ${route.company_name || 'غير محدد'}`);

      // تشغيل نسخة Next.js الداخلية الخاصة بهذا المسار على المنفذ المحدد
      const internalPort = route.internal_next_port || defaultConfig.next_port || 3300;
      try {
        const np = startNextServer(internalPort, route);
        nextProcessByPort[port] = np;
        console.log(`   ▶️ تم تشغيل Next.js الداخلي على ${internalPort} لقاعدة ${route.database}`);
      } catch (e) {
        console.warn(`   ⚠️ تعذّر تشغيل Next.js الداخلي على ${internalPort}: ${e.message}`);
      }

      // إنشاء خادم proxy للنظام الداخلي
      const server = createProxyServer(parseInt(port), route);

      // تشغيل الخادم على جميع الواجهات
      server.listen(parseInt(port), '0.0.0.0', () => {
        console.log(`\n✅ خادم proxy يعمل على المنفذ ${port}`);
        console.log(`   🌐 http://localhost:${port}`);
        console.log(`   🌐 http://127.0.0.1:${port}`);

        // عرض عناوين IP المحلية
        try {
          const { networkInterfaces } = require('os');
          const nets = networkInterfaces();

          console.log(`   🔗 عناوين IP المتاحة:`);
          for (const name of Object.keys(nets)) {
            for (const net of nets[name]) {
              // فقط عناوين IPv4 وليست loopback
              if (net.family === 'IPv4' && !net.internal) {
                console.log(`      🌐 http://${net.address}:${port}`);
              }
            }
          }
        } catch (error) {
          console.log(`   ⚠️ لم يتم العثور على عناوين IP إضافية`);
        }

        console.log(`   🗄️ قاعدة البيانات: ${route.database}`);
        console.log(`   🔄 يوجه إلى النسخة الداخلية على المنفذ ${route.internal_next_port || defaultConfig.next_port}`);

        logToFile(`تم تشغيل خادم على المنفذ ${port} لقاعدة البيانات ${route.database}`, 'info');
      });

      server.on('error', (error) => {
        console.error(`❌ خطأ في المنفذ ${port}:`, error.message);
        logToFile(`خطأ في المنفذ ${port}: ${error.message}`, 'error');

        if (error.code === 'EADDRINUSE') {
          console.log(`💡 المنفذ ${port} مستخدم بالفعل`);
        }
      });

      servers.push(server);
      serverByPort[port] = server;
      activeConnections[port] = route.database;

    } catch (error) {
      console.error(`❌ فشل في إعداد المنفذ ${port}:`, error.message);
      logToFile(`فشل في إعداد المنفذ ${port}: ${error.message}`, 'error');
    }
  }
}

/**
 * عرض معلومات النظام
 */
function displaySystemInfo() {
  console.log('\n🎉 تم تشغيل الخادم الموحد المتقدم بنجاح!');
  console.log('='.repeat(60));

  console.log('\n🌐 الروابط المتاحة:');
  for (const [port, dbName] of Object.entries(activeConnections)) {
    const route = routingConfig.routes[port];
    console.log(`\n📋 ${route.company_name || 'نظام إدارة المحاماة'}:`);
    console.log(`   🌐 http://localhost:${port}`);
    console.log(`   🗄️ قاعدة البيانات: ${dbName}`);
  }

  console.log('\n🏆 المواصفات:');
  console.log('   ✅ خادم موحد متقدم');
  console.log('   ✅ يعتمد على ملف التوجيه');
  console.log('   ✅ توجيه قواعد البيانات يعمل');
  console.log('   ✅ يتصل بالنظام الأصلي');
  console.log('   ✅ يدعم إضافة منافذ وقواعد بيانات جديدة');

  console.log('\n💡 ملاحظات مهمة:');
  console.log('   📋 كل منفذ يستخدم قاعدة البيانات المحددة');
  console.log('   🔄 شغل النظام الأصلي بـ: npm run dev');
  console.log('   🔧 يمكن إضافة منافذ وقواعد بيانات جديدة في ملف التوجيه');
  console.log('   🌐 الخادم جاهز للاستخدام');

  console.log('\n⏳ الخوادم تعمل... اضغط Ctrl+C للإيقاف');

  logToFile('تم تشغيل الخادم الموحد المتقدم بنجاح', 'info');
}

// ======== بدء النظام ========
async function startSystem() {
  try {
    // التحقق من قواعد البيانات
    if (systemSettings.validate_database_connection) {
      const dbResults = await validateAllDatabases();

      // بدء الخوادم
      await startAllServers(dbResults);
    } else {
      // بدء الخوادم بدون التحقق من قواعد البيانات
      const mockResults = {};
      Object.entries(routingConfig.routes).forEach(([port, route]) => {
        mockResults[port] = { database: route.database, connected: true };
      });

      await startAllServers(mockResults);
    }

    // عرض معلومات النظام
    setTimeout(displaySystemInfo, 1000);

  } catch (error) {
    console.error('❌ خطأ في بدء النظام:', error.message);
    logToFile(`خطأ في بدء النظام: ${error.message}`, 'error');

    if (systemSettings.auto_restart_on_error) {
      console.log('🔄 إعادة تشغيل النظام...');
      setTimeout(startSystem, 5000);
    } else {
      process.exit(1);
    }
  }
}

// ======== معالجة الإيقاف ========
process.on('SIGINT', () => {
  console.log('\n🛑 إيقاف الخادم الموحد...');
  logToFile('إيقاف الخادم الموحد', 'info');

  // إيقاف الخوادم
  servers.forEach((server, index) => {
    try {
      server.close();
      console.log(`✅ تم إيقاف خادم ${index + 1}`);
    } catch (error) {
      // تجاهل الأخطاء
    }
  });

  console.log('✅ تم إيقاف جميع الخوادم');
  process.exit(0);
});

// ======== معالجة الأخطاء ========
process.on('uncaughtException', (error) => {
  console.error('❌ خطأ غير متوقع:', error.message);
  logToFile(`خطأ غير متوقع: ${error.message}`, 'error');

  if (systemSettings.auto_restart_on_error) {
    console.log('🔄 إعادة تشغيل النظام بعد خطأ غير متوقع...');
    setTimeout(startSystem, 5000);
  }
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ رفض غير معالج:', reason);
  logToFile(`رفض غير معالج: ${reason}`, 'error');
});

// ======== بدء النظام ========
createAdminServer();
startSystem();