@echo off
echo Setting up Advanced Unified Server for mohaminew...
echo.

REM Check for admin privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

REM Service configuration
set SERVICE_NAME=AdvancedUnifiedServer
set WORKING_DIR=D:\mohaminew
set SERVER_SCRIPT=advanced-unified-server.js
set NODE_PATH=

REM Find Node.js path
echo Looking for Node.js...
if exist "C:\Program Files\nodejs\node.exe" (
    set NODE_PATH=C:\Program Files\nodejs\node.exe
    echo Found Node.js at: C:\Program Files\nodejs\node.exe
    goto :found_node
)

where node >nul 2>&1
if %errorLevel% equ 0 (
    for /f "tokens=*" %%i in ('where node') do (
        set NODE_PATH=%%i
        echo Found Node.js in PATH: %%i
        goto :found_node
    )
)

echo ERROR: Node.js not found. Please install Node.js first.
pause
exit /b 1

:found_node

REM Check if working directory exists
if not exist "%WORKING_DIR%" (
    echo ERROR: Working directory not found: %WORKING_DIR%
    pause
    exit /b 1
)

REM Check if server script exists
if not exist "%WORKING_DIR%\%SERVER_SCRIPT%" (
    echo ERROR: Server script not found: %WORKING_DIR%\%SERVER_SCRIPT%
    pause
    exit /b 1
)

echo All files found successfully.
echo Working Directory: %WORKING_DIR%
echo Server Script: %SERVER_SCRIPT%
echo.

REM Stop and delete existing service if it exists
echo Checking for existing service...
schtasks /query "%SERVICE_NAME%" >nul 2>&1
if %errorLevel% equ 0 (
    echo Stopping existing service...
    schtasks /end "%SERVICE_NAME%" >nul 2>&1
    timeout /t 3 /nobreak >nul
    
    echo Deleting existing service...
    schtasks /delete "%SERVICE_NAME%" /f >nul 2>&1
    timeout /t 2 /nobreak >nul
)

REM Create new service
echo Creating new service for mohaminew...
schtasks /create "%SERVICE_NAME%" binPath= "\"%NODE_PATH%\" \"%WORKING_DIR%\%SERVER_SCRIPT%\"" start= auto DisplayName= "Advanced Unified Server - mohaminew"

if %errorLevel% equ 0 (
    echo Service created successfully.
) else (
    echo ERROR: Failed to create service.
    pause
    exit /b 1
)

REM Set service description
echo Setting service description...
schtasks /create /tn "%SERVICE_NAME%" /tr "\"%NODE_PATH%\" \"%WORKING_DIR%\%SERVER_SCRIPT%\"" /sc onstart /ru "SYSTEM" /rl highest /f

REM Start the service
echo Starting service...
schtasks /run "%SERVICE_NAME%"

REM Wait and check service status
timeout /t 10 /nobreak >nul
echo Checking service status...

echo.
echo SUCCESS: Service setup completed for mohaminew!
echo.
echo Service Information:
echo - Service Name: %SERVICE_NAME%
echo - Node.js Path: %NODE_PATH%
echo - Working Directory: %WORKING_DIR%
echo - Server Script: %SERVER_SCRIPT%
echo.
echo Available Ports (NO PORT 3300):
echo - Port 7443: Mohamed System (Production)
echo - Port 8914: Rabei System
echo.
echo Service Management Commands:
echo - Stop: schtasks /end %SERVICE_NAME%
echo - Start: schtasks /run %SERVICE_NAME%
echo - Delete: schtasks /delete %SERVICE_NAME% /f
echo.
echo The service will start automatically on server reboot.
echo.

pause
