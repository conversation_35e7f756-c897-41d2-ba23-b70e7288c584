import { NextRequest, NextResponse } from 'next/server'
import { Client } from 'pg'

const dbConfig = {
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'mohammidev'
}

// GET - جلب الإشعارات
export async function GET(request: NextRequest) {
  const client = new Client(dbConfig)

  try {
    await client.connect()

    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const recipient_type = searchParams.get('recipient_type')
    const notification_type = searchParams.get('notification_type')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    let whereConditions = ['1=1']
    let queryParams: any[] = []
    let paramIndex = 1

    if (status) {
      whereConditions.push(`n.status = $${paramIndex}`)
      queryParams.push(status)
      paramIndex++
    }

    if (recipient_type) {
      whereConditions.push(`n.recipient_type = $${paramIndex}`)
      queryParams.push(recipient_type)
      paramIndex++
    }

    if (notification_type) {
      whereConditions.push(`n.notification_type = $${paramIndex}`)
      queryParams.push(notification_type)
      paramIndex++
    }

    const whereClause = whereConditions.join(' AND ')

    const notificationsQuery = `
      SELECT
        n.*,
        c.case_number,
        c.title as case_title,
        CASE
          WHEN n.recipient_type = 'client' THEN cl.name
          WHEN n.recipient_type = 'employee' THEN e.name
          ELSE 'غير محدد'
        END as recipient_name
      FROM case_notifications n
      LEFT JOIN cases c ON n.case_id = c.id
      LEFT JOIN clients cl ON n.recipient_type = 'client' AND n.recipient_id = cl.id
      LEFT JOIN employees e ON n.recipient_type = 'employee' AND n.recipient_id = e.id
      WHERE ${whereClause}
      ORDER BY n.scheduled_date DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `

    queryParams.push(limit, offset)

    const notificationsResult = await client.query(notificationsQuery, queryParams)

    // إحصائيات الإشعارات
    const statsQuery = `
      SELECT
        COUNT(*) as total_notifications,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_notifications,
        COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent_notifications,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_notifications,
        COUNT(CASE WHEN scheduled_date <= NOW() AND status = 'pending' THEN 1 END) as overdue_notifications
      FROM case_notifications
    `

    const statsResult = await client.query(statsQuery)

    return NextResponse.json({
      success: true,
      data: {
        notifications: notificationsResult.rows,
        stats: statsResult.rows[0],
        pagination: {
          limit,
          offset,
          total: parseInt(statsResult.rows[0].total_notifications)
        }
      }
    })

  } catch (error) {
    console.error('خطأ في جلب الإشعارات:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب الإشعارات'
    }, { status: 500 })
  } finally {
    await client.end()
  }
}

// POST - إضافة إشعار جديد
export async function POST(request: NextRequest) {
  const client = new Client(dbConfig)

  try {
    await client.connect()

    const body = await request.json()
    const {
      case_id,
      recipient_type,
      recipient_id,
      notification_type,
      title,
      message,
      scheduled_date,
      delivery_method = 'email',
      metadata = {}
    } = body

    // التحقق من صحة البيانات
    if (!case_id || !recipient_type || !recipient_id || !notification_type || !title || !message || !scheduled_date) {
      return NextResponse.json({
        success: false,
        error: 'جميع البيانات الأساسية مطلوبة'
      }, { status: 400 })
    }

    // إدراج الإشعار الجديد
    const insertQuery = `
      INSERT INTO case_notifications (
        case_id, recipient_type, recipient_id, notification_type,
        title, message, scheduled_date, delivery_method, metadata
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING *
    `

    const result = await client.query(insertQuery, [
      case_id, recipient_type, recipient_id, notification_type,
      title, message, scheduled_date, delivery_method, JSON.stringify(metadata)
    ])

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم إضافة الإشعار بنجاح'
    })

  } catch (error) {
    console.error('خطأ في إضافة الإشعار:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في إضافة الإشعار'
    }, { status: 500 })
  } finally {
    await client.end()
  }
}

// PUT - تحديث حالة الإشعار
export async function PUT(request: NextRequest) {
  const client = new Client(dbConfig)

  try {
    await client.connect()

    const body = await request.json()
    const {
      id,
      status,
      sent_date,
      metadata
    } = body

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف الإشعار مطلوب'
      }, { status: 400 })
    }

    // بناء استعلام التحديث
    const updateFields = []
    const queryParams = []
    let paramIndex = 1

    if (status) {
      updateFields.push(`status = $${paramIndex}`)
      queryParams.push(status)
      paramIndex++
    }

    if (sent_date) {
      updateFields.push(`sent_date = $${paramIndex}`)
      queryParams.push(sent_date)
      paramIndex++
    }

    if (metadata) {
      updateFields.push(`metadata = $${paramIndex}`)
      queryParams.push(JSON.stringify(metadata))
      paramIndex++
    }

    if (updateFields.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'لا توجد بيانات للتحديث'
      }, { status: 400 })
    }

    updateFields.push(`updated_at = CURRENT_TIMESTAMP`)
    queryParams.push(id)

    const updateQuery = `
      UPDATE case_notifications
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `

    const result = await client.query(updateQuery, queryParams)

    if (result.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الإشعار غير موجود'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم تحديث الإشعار بنجاح'
    })

  } catch (error) {
    console.error('خطأ في تحديث الإشعار:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث الإشعار'
    }, { status: 500 })
  } finally {
    await client.end()
  }
}
