@echo off
chcp 65001 >nul
echo.
echo 🔍 مراقب الخادم المتقدم
echo ========================
echo.

:check_loop
echo [%date% %time%] فحص حالة الخادم...

REM فحص المنفذ 7443
netstat -an | findstr ":7443" | findstr "LISTENING" >nul
if %errorLevel% equ 0 (
    echo ✅ المنفذ 7443 يعمل ^(نظام محمد^)
) else (
    echo ❌ المنفذ 7443 لا يعمل - محاولة إعادة التشغيل...
    schtasks /run /tn AdvancedUnifiedServer >nul 2>&1
)

REM فحص المنفذ 8914
netstat -an | findstr ":8914" | findstr "LISTENING" >nul
if %errorLevel% equ 0 (
    echo ✅ المنفذ 8914 يعمل ^(نظام الربعي^)
) else (
    echo ❌ المنفذ 8914 لا يعمل
)

REM فحص حالة المهمة المجدولة
schtasks /query /tn AdvancedUnifiedServer | findstr "Running" >nul
if %errorLevel% equ 0 (
    echo ✅ المهمة المجدولة تعمل
) else (
    echo ⚠️ المهمة المجدولة متوقفة - محاولة إعادة التشغيل...
    schtasks /run /tn AdvancedUnifiedServer >nul 2>&1
)

echo.
echo انتظار 30 ثانية قبل الفحص التالي...
timeout /t 30 /nobreak >nul
goto check_loop
