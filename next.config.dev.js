/** @type {import('next').NextConfig} */
const nextConfig = {
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },

  // إعدادات webpack محسنة لتجنب مشاكل SSR
  webpack: (config, { isServer, dev }) => {
    // تجنب تحميل مكتبات Node.js في العميل
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        stream: false,
        url: false,
        zlib: false,
        http: false,
        https: false,
        assert: false,
        os: false,
        path: false,
        child_process: false,
        worker_threads: false,
      }
    }

    return config
  },
  poweredByHeader: false,
  // إعدادات الصور
  images: {
    domains: ['localhost'],
    unoptimized: true
  }
}

module.exports = nextConfig
