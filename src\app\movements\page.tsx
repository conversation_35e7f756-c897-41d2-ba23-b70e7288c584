'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Briefcase,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  ArrowUpRight,
  ArrowDownLeft,
  DollarSign,
  Calendar
} from 'lucide-react'

interface Movement {
  id: number
  case_number: string
  case_title: string
  movement_type: 'income' | 'expense'
  category: string
  amount: number
  description: string
  date: string
  reference_number: string
  status: 'pending' | 'approved' | 'rejected'
  created_by: string
  created_date: string
}

export default function MovementsPage() {
  const [movements, setMovements] = useState<Movement[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingMovement, setEditingMovement] = useState<Movement | null>(null)
  const [dbError, setDbError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const fetchMovements = async () => {
    setIsLoading(true)
    setDbError(null)

    try {
      const response = await fetch('/api/movements')
      const result = await response.json()

      if (result.success) {
        setMovements(result.data)
      } else {
        setDbError(result.error || 'فشل في جلب بيانات الحركات المالية')
        setMovements([])
      }
    } catch (error) {
      console.error('Network error:', error)
      setDbError('فشل في الاتصال بقاعدة البيانات')
      setMovements([])
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchMovements()
  }, [])

  const filteredMovements = movements.filter(movement =>
    (movement.case_number || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (movement.case_title || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (movement.description || '').toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'approved': return 'bg-green-100 text-green-800'
      case 'rejected': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'معلقة'
      case 'approved': return 'موافق عليها'
      case 'rejected': return 'مرفوضة'
      default: return 'غير محدد'
    }
  }

  const getTypeColor = (type: string) => {
    return type === 'income' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
  }

  const getTypeText = (type: string) => {
    return type === 'income' ? 'إيراد' : 'مصروف'
  }

  const handleDelete = (id: number) => {
    if (confirm('هل أنت متأكد من حذف هذه الحركة؟')) {
      setMovements(movements.filter(movement => movement.id !== id))
    }
  }

  const handleView = (movement: Movement) => {
    console.log('عرض تفاصيل الحركة:', movement)
    alert(`تفاصيل الحركة المالية:\n\nالنوع: ${getTypeText(movement.type)}\nالمبلغ: ${movement.amount} ريال\nالوصف: ${movement.description}\nالتاريخ: ${movement.date}`)
  }

  const handleEdit = (movement: Movement) => {
    setEditingMovement(movement)
    setIsModalOpen(true)
  }

  const handleAddNew = () => {
    setEditingMovement(null)
    setIsModalOpen(true)
  }

  const stats = {
    total: movements.length,
    income: movements.filter(m => m.movement_type === 'income').reduce((sum, m) => sum + (m.amount || 0), 0),
    expense: movements.filter(m => m.movement_type === 'expense').reduce((sum, m) => sum + (m.amount || 0), 0),
    pending: movements.filter(m => m.status === 'pending').length
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Briefcase className="h-8 w-8 mr-3 text-blue-600" />
              إدارة حركة القضايا
            </h1>
            <p className="text-gray-600 mt-1">متابعة الحركات المالية للقضايا</p>
          </div>

          <Button onClick={handleAddNew} className="bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            إضافة حركة جديدة
          </Button>
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Briefcase className="h-6 w-6 text-blue-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
                  <div className="text-sm text-gray-600">إجمالي الحركات</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <ArrowUpRight className="h-6 w-6 text-green-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{(stats.income || 0).toLocaleString()}</div>
                  <div className="text-sm text-gray-600">إجمالي الإيرادات</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-red-100 rounded-lg">
                  <ArrowDownLeft className="h-6 w-6 text-red-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{(stats.expense || 0).toLocaleString()}</div>
                  <div className="text-sm text-gray-600">إجمالي المصروفات</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <Calendar className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.pending}</div>
                  <div className="text-sm text-gray-600">حركات معلقة</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardContent className="p-4">
            <div className="flex justify-center">
              <div className="relative w-96">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في الحركات المالية..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10 w-full"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Briefcase className="h-5 w-5 mr-2" />
              قائمة الحركات المالية ({filteredMovements.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="text-right p-3 font-semibold">رقم القضية</th>
                    <th className="text-right p-3 font-semibold">عنوان القضية</th>
                    <th className="text-center p-3 font-semibold">النوع</th>
                    <th className="text-right p-3 font-semibold">الفئة</th>
                    <th className="text-center p-3 font-semibold">المبلغ</th>
                    <th className="text-right p-3 font-semibold">الوصف</th>
                    <th className="text-center p-3 font-semibold">التاريخ</th>
                    <th className="text-center p-3 font-semibold">الحالة</th>
                    <th className="text-center p-3 font-semibold">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredMovements.map((movement) => (
                    <tr key={movement.id} className="border-b hover:bg-gray-50">
                      <td className="p-3 font-medium text-blue-600">{movement.case_number}</td>
                      <td className="p-3">{movement.case_title}</td>
                      <td className="text-center p-3">
                        <Badge className={getTypeColor(movement.movement_type)}>
                          <div className="flex items-center">
                            {movement.movement_type === 'income' ? (
                              <ArrowUpRight className="h-3 w-3 mr-1" />
                            ) : (
                              <ArrowDownLeft className="h-3 w-3 mr-1" />
                            )}
                            {getTypeText(movement.movement_type)}
                          </div>
                        </Badge>
                      </td>
                      <td className="p-3">{movement.category}</td>
                      <td className="text-center p-3 font-medium">
                        <div className="flex items-center justify-center">
                          <DollarSign className="h-4 w-4 mr-1 text-gray-400" />
                          {movement.amount ? movement.amount.toLocaleString() : '0'} ريال
                        </div>
                      </td>
                      <td className="p-3">{movement.description}</td>
                      <td className="text-center p-3">
                        <div className="flex items-center justify-center">
                          <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                          {movement.date}
                        </div>
                      </td>
                      <td className="text-center p-3">
                        <Badge className={getStatusColor(movement.status)}>
                          {getStatusText(movement.status)}
                        </Badge>
                      </td>
                      <td className="text-center p-3">
                        <div className="flex justify-center space-x-2 space-x-reverse">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleView(movement)}
                            className="bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200"
                            title="عرض تفاصيل الحركة"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => handleEdit(movement)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDelete(movement.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Modal للإضافة/التعديل */}
        {isModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">
                  {editingMovement ? 'تعديل الحركة المالية' : 'إضافة حركة مالية جديدة'}
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsModalOpen(false)}
                >
                  ×
                </Button>
              </div>
              <p className="text-gray-600">سيتم إضافة نموذج التعديل هنا</p>
              <div className="flex justify-end mt-4">
                <Button onClick={() => setIsModalOpen(false)}>إغلاق</Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}
