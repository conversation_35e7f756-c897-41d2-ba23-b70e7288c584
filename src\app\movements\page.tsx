'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Search,
  Calendar,
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Plus,
  Filter,
  Bell,
  TrendingUp,
  Users,
  Gavel,
  FileText,
  Eye
} from 'lucide-react'

interface CaseData {
  id: number
  case_number?: string
  title: string
  client_name?: string
  lawyer_name?: string
  issue_type?: string
  judge_name?: string
  status?: string
  last_movement_date?: string
  operation_name?: string
}

// تم إلغاء الإحصائيات لهذه الصفحة حسب المتطلبات الجديدة

// تم إلغاء أقسام الجلسات والتنبيهات لهذه الصفحة

export default function CaseMovementsPage() {
  const [cases, setCases] = useState<CaseData[]>([])
  const [loading, setLoading] = useState(true)
  const [filters, setFilters] = useState({
    title: '',
    client: '',
    lawyer: '',
    judge: '',
    issue_type: '',
    status: '',
    operation_name: '',
    last_movement_date: ''
  })
  const [sortBy, setSortBy] = useState<keyof CaseData>('title')
  const [sortDir, setSortDir] = useState<'asc' | 'desc'>('asc')
  const [selectedCase, setSelectedCase] = useState<CaseData | null>(null)
  const [colMenu, setColMenu] = useState<{open:boolean; key: keyof CaseData | null; x:number; y:number}>({open:false, key:null, x:0, y:0})

  // جلب القضايا (من /api/case-movements لأخذ آخر حركة)
  const fetchData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/case-movements?limit=500&page=1')
      const data = await response.json()
      const list = data?.data?.cases || []
      if (data.success && Array.isArray(list)) {
        const mapped: CaseData[] = list.map((i: any) => ({
          id: i.id,
          case_number: i.case_number,
          title: i.title,
          client_name: i.client_name,
          lawyer_name: i.lawyer_name,
          issue_type: i.issue_type || i.type || '',
          judge_name: i.judge_name || '-',
          status: i.status || '',
          last_movement_date: i.last_movement?.movement_date || i.created_date || null,
          operation_name: i.last_follow_service_name || i.last_movement?.movement_type || 'قضية جديدة'
        }))
        setCases(mapped)
      } else {
        // Fallback إلى /api/issues
        const resp2 = await fetch('/api/issues')
        const data2 = await resp2.json()
        const list2 = data2?.data || []
        if (data2.success && Array.isArray(list2)) {
          const mapped2: CaseData[] = list2.map((i: any) => ({
            id: i.id,
            case_number: i.case_number,
            title: i.title,
            client_name: i.client_name,
            lawyer_name: i.lawyer_name,
            issue_type: i.issue_type || i.type || '',
            judge_name: i.judge_name || '-',
            status: i.status || '',
            last_movement_date: null,
            operation_name: ''
          }))
          setCases(mapped2)
        } else {
          setCases([])
        }
      }
    } catch (error) {
      console.error('خطأ في جلب البيانات:', error)
      // Fallback في حالة الخطأ
      try {
        const resp2 = await fetch('/api/issues')
        const data2 = await resp2.json()
        const list2 = data2?.data || []
        if (data2.success && Array.isArray(list2)) {
          const mapped2: CaseData[] = list2.map((i: any) => ({
            id: i.id,
            case_number: i.case_number,
            title: i.title,
            client_name: i.client_name,
            lawyer_name: i.lawyer_name,
            issue_type: i.issue_type || i.type || '',
            judge_name: i.judge_name || '-',
            status: i.status || '',
            last_movement_date: null,
            operation_name: ''
          }))
          setCases(mapped2)
        } else {
          setCases([])
        }
      } catch (e) {
        setCases([])
      }
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  const getStatusColor = (status?: string) => {
    switch ((status || '').toLowerCase()) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'closed': return 'bg-gray-100 text-gray-800'
      default: return 'bg-blue-100 text-blue-800'
    }
  }

  // وظائف الفرز وفتح منيو العمود
  const openColMenu = (key: keyof CaseData) => (e: React.MouseEvent<HTMLElement>) => {
    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect()
    setColMenu({ open: true, key, x: rect.left + rect.width, y: rect.bottom })
  }
  const applySortAsc = () => { if (colMenu.key) { setSortBy(colMenu.key); setSortDir('asc'); setColMenu(prev=>({...prev, open:false})) } }
  const applySortDesc = () => { if (colMenu.key) { setSortBy(colMenu.key); setSortDir('desc'); setColMenu(prev=>({...prev, open:false})) } }
  // تحويل اسم الحقل إلى مفتاح الفلترة في الحالة
  const mapCaseKeyToFilterKey = (key: keyof CaseData): keyof typeof filters => {
    switch (key) {
      case 'title': return 'title'
      case 'client_name': return 'client'
      case 'lawyer_name': return 'lawyer'
      case 'issue_type': return 'issue_type'
      case 'judge_name': return 'judge'
      case 'operation_name': return 'operation_name'
      case 'last_movement_date': return 'last_movement_date'
      case 'status': return 'status'
      default: return 'title'
    }
  }
  const clearFilterForKey = () => {
    if (!colMenu.key) return
    const fk = mapCaseKeyToFilterKey(colMenu.key)
    setFilters(prev => ({ ...prev, [fk]: '' }))
    setColMenu(prev=>({...prev, open:false}))
  }
  const setFilterForKey = (val: string) => {
    if (!colMenu.key) return
    const fk = mapCaseKeyToFilterKey(colMenu.key)
    setFilters(prev => ({ ...prev, [fk]: val }))
  }
  const getFilterValueForKey = (): string => {
    if (!colMenu.key) return ''
    const fk = mapCaseKeyToFilterKey(colMenu.key)
    return (filters as any)[fk] || ''
  }
  const getHeaderLabel = (key: keyof CaseData) => {
    switch (key) {
      case 'title': return 'اسم القضية'
      case 'client_name': return 'الموكل'
      case 'lawyer_name': return 'المحامي'
      case 'issue_type': return 'نوع القضية'
      case 'judge_name': return 'اسم القاضي'
      case 'operation_name': return 'اسم الحركة'
      case 'status': return 'حالة القضية'
      case 'last_movement_date': return 'تاريخ الحركة'
      default: return ''
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل البيانات...</p>
        </div>
      </div>
    )
  }

  // تجهيز قائمة العرض والعداد
  const visibleCases = cases
    .filter(c => (c.title || '').toLowerCase().includes(filters.title.toLowerCase()))
    .filter(c => (c.client_name || '').toLowerCase().includes(filters.client.toLowerCase()))
    .filter(c => (c.lawyer_name || '').toLowerCase().includes(filters.lawyer.toLowerCase()))
    .filter(c => (c.issue_type || '').toLowerCase().includes(filters.issue_type.toLowerCase()))
    .filter(c => (c.judge_name || '').toLowerCase().includes(filters.judge.toLowerCase()))
    .filter(c => (c.operation_name || '').toLowerCase().includes(filters.operation_name.toLowerCase()))
    .filter(c => (c.last_movement_date ? new Date(c.last_movement_date).toLocaleDateString('ar-SA') : '').includes(filters.last_movement_date))
    .filter(c => (c.status || '').toLowerCase().includes(filters.status.toLowerCase()))
    .sort((a, b) => {
      const va = sortBy === 'last_movement_date'
        ? (a.last_movement_date ? new Date(a.last_movement_date).getTime() : 0)
        : (a[sortBy] || '').toString().toLowerCase()
      const vb = sortBy === 'last_movement_date'
        ? (b.last_movement_date ? new Date(b.last_movement_date).getTime() : 0)
        : (b[sortBy] || '').toString().toLowerCase()
      if (va < vb) return sortDir === 'asc' ? -1 : 1
      if (va > vb) return sortDir === 'asc' ? 1 : -1
      return 0
    })

  return (
    <div className="container mx-auto p-6 space-y-6" dir="rtl">
      {/* العنوان */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Gavel className="h-8 w-8 mr-3 text-blue-600" />
              حركة القضايا ({visibleCases.length})
            </h1>
          </div>
        </div>
        <div className="flex space-x-2 space-x-reverse">
          <Button variant="outline" className="border-blue-600 text-blue-600 hover:bg-blue-50" onClick={fetchData}>
            <Clock className="h-4 w-4 mr-2" /> تحديث
          </Button>
          <Button
            variant="outline"
            className="border-gray-500 text-gray-700 hover:bg-gray-50"
            onClick={() => {
              setFilters({ title: '', client: '', lawyer: '', judge: '', issue_type: '', status: '', operation_name: '', last_movement_date: '' })
              setSortBy('title')
              setSortDir('asc')
              setColMenu({ open:false, key:null, x:0, y:0 })
            }}
            title="مسح جميع الفلاتر"
          >
            مسح الفلاتر
          </Button>
        </div>
      </div>

      {/* جدول القضايا */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-right text-xs font-semibold text-gray-700 cursor-pointer" onMouseDown={openColMenu('title')}>اسم القضية {sortBy==='title' ? (sortDir==='asc' ? '▲' : '▼') : ''}</th>
                  <th className="px-4 py-3 text-right text-xs font-semibold text-gray-700 cursor-pointer" onMouseDown={openColMenu('client_name')}>الموكل {sortBy==='client_name' ? (sortDir==='asc' ? '▲' : '▼') : ''}</th>
                  <th className="px-4 py-3 text-right text-xs font-semibold text-gray-700 cursor-pointer" onMouseDown={openColMenu('lawyer_name')}>المحامي {sortBy==='lawyer_name' ? (sortDir==='asc' ? '▲' : '▼') : ''}</th>
                  <th className="px-4 py-3 text-right text-xs font-semibold text-gray-700 cursor-pointer" onMouseDown={openColMenu('issue_type')}>نوع القضية {sortBy==='issue_type' ? (sortDir==='asc' ? '▲' : '▼') : ''}</th>
                  <th className="px-4 py-3 text-right text-xs font-semibold text-gray-700 cursor-pointer" onMouseDown={openColMenu('judge_name')}>اسم القاضي {sortBy==='judge_name' ? (sortDir==='asc' ? '▲' : '▼') : ''}</th>
                  <th className="px-4 py-3 text-right text-xs font-semibold text-gray-700 cursor-pointer" onMouseDown={openColMenu('operation_name')}>اسم الحركة {sortBy==='operation_name' ? (sortDir==='asc' ? '▲' : '▼') : ''}</th>
                  <th className="px-4 py-3 text-right text-xs font-semibold text-gray-700 cursor-pointer" onMouseDown={openColMenu('last_movement_date')}>تاريخ الحركة {sortBy==='last_movement_date' ? (sortDir==='asc' ? '▲' : '▼') : ''}</th>
                  <th className="px-4 py-3 text-right text-xs font-semibold text-gray-700 cursor-pointer" onMouseDown={openColMenu('status')}>حالة القضية {sortBy==='status' ? (sortDir==='asc' ? '▲' : '▼') : ''}</th>
                  <th className="px-4 py-3 text-right text-xs font-semibold text-gray-700">الإجراءات</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {visibleCases.map((caseItem) => (
                  <tr key={caseItem.id} className="hover:bg-gray-50">
                    <td className="px-4 py-3 whitespace-nowrap">{caseItem.title}</td>
                    <td className="px-4 py-3 whitespace-nowrap">{caseItem.client_name || '-'}</td>
                    <td className="px-4 py-3 whitespace-nowrap">{caseItem.lawyer_name || '-'}</td>
                    <td className="px-4 py-3 whitespace-nowrap">{caseItem.issue_type || '-'}</td>
                    <td className="px-4 py-3 whitespace-nowrap">{caseItem.judge_name || '-'}</td>
                    <td className="px-4 py-3 whitespace-nowrap">{caseItem.operation_name || '-'}</td>
                    <td className="px-4 py-3 whitespace-nowrap">{caseItem.last_movement_date ? new Date(caseItem.last_movement_date).toLocaleDateString('ar-SA') : '-'}</td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <Badge className={getStatusColor(caseItem.status)}>
                        {caseItem.status || 'غير محدد'}
                      </Badge>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm font-medium">
                      <Button size="sm" variant="outline" title="مشاهدة" onClick={() => setSelectedCase(caseItem)}>
                        <Eye className="h-4 w-4" />
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* نافذة عرض تفاصيل القضية */}
      {selectedCase && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-3xl">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold">تفاصيل القضية</h2>
              <Button variant="outline" onClick={() => setSelectedCase(null)}>إغلاق</Button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
              <div><span className="text-gray-500">اسم القضية:</span> <span className="font-medium">{selectedCase.title}</span></div>
              <div><span className="text-gray-500">الموكل:</span> <span className="font-medium">{selectedCase.client_name || '-'}</span></div>
              <div><span className="text-gray-500">المحامي:</span> <span className="font-medium">{selectedCase.lawyer_name || '-'}</span></div>
              <div><span className="text-gray-500">نوع القضية:</span> <span className="font-medium">{selectedCase.issue_type || '-'}</span></div>
              <div><span className="text-gray-500">اسم القاضي:</span> <span className="font-medium">{selectedCase.judge_name || '-'}</span></div>
              <div><span className="text-gray-500">الحالة:</span> <span className="font-medium">{selectedCase.status || '-'}</span></div>
            </div>
            <div className="mt-4 text-sm text-gray-600">
              سيتم عرض تفاصيل الحركة والمهام المرتبطة في هذه النافذة. (يمكننا ربطها لاحقاً بـ /api/case-movements و /api/follows)
            </div>
          </div>
        </div>
      )}

      {/* منيو العمود المنبثقة (مدمجة بحجم مربع البحث) */}
      {colMenu.open && colMenu.key && (
        <div
          className="fixed z-50 bg-white border border-gray-200 rounded-md shadow-lg p-2 w-72"
          style={{ top: colMenu.y + 8, left: Math.max(8, colMenu.x - 288) }}
        >
          <div className="relative">
            {colMenu.key === 'last_movement_date' ? (
              <input
                type="date"
                value={getFilterValueForKey()}
                onChange={(e)=> setFilterForKey(e.target.value)}
                className="h-9 w-full border border-gray-300 rounded-md pr-3 pl-28 text-sm text-black"
              />
            ) : (
              <Input
                value={getFilterValueForKey()}
                onChange={(e)=> setFilterForKey(e.target.value)}
                placeholder={`بحث في ${getHeaderLabel(colMenu.key)}`}
                className="h-9 pr-3 pl-28 text-sm"
              />
            )}
            {/* أزرار داخلية يمين/يسار الحقل */}
            <div className="absolute inset-y-0 left-2 flex items-center gap-1">
              <Button size="sm" variant="outline" className="h-7 px-2" onClick={applySortAsc} title="تصاعدي">↑</Button>
              <Button size="sm" variant="outline" className="h-7 px-2" onClick={applySortDesc} title="تنازلي">↓</Button>
              <Button size="sm" variant="outline" className="h-7 px-2 text-red-600" onClick={clearFilterForKey} title="مسح">×</Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
