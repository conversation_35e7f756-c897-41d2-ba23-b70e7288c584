'use client'

import { useState, useEffect } from 'react'
import MainLayout from '@/components/layout/main-layout'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { toast } from 'react-hot-toast'
import {
  Activity,
  Search,
  Eye,
  Calendar,
  Clock,
  User,
  FileText,
  CheckCircle,
  RefreshCw,
  Filter,
  Bell,
  Gavel,
  AlertTriangle,
  TrendingUp
} from 'lucide-react'

interface CaseMovement {
  id: number
  case_id: number
  case_number: string
  case_title: string
  movement_type: string
  description: string
  details?: any
  hearing_date?: string
  hearing_time?: string
  court_name?: string
  created_by?: number
  created_by_name?: string
  user_role?: string
  movement_date: string
  created_at: string
  priority: 'low' | 'normal' | 'high' | 'urgent'
  status: string
  notes?: string
  reference_id?: number
  reference_type?: string
  client_name?: string
  case_status?: string
  case_next_hearing?: string
  court_full_name?: string
  employee_name?: string
}

interface MovementStats {
  total_count: number
  hearings_count: number
  urgent_count: number
  upcoming_hearings: number
}

export default function CaseMovementsPage() {
  const [movements, setMovements] = useState<CaseMovement[]>([])
  const [stats, setStats] = useState<MovementStats | null>(null)
  const [upcomingHearings, setUpcomingHearings] = useState<CaseMovement[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState('')
  const [filterPriority, setFilterPriority] = useState('')
  const [dbError, setDbError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const fetchCaseMovements = async () => {
    setIsLoading(true)
    setDbError(null)

    try {
      const response = await fetch('/api/case-movements')
      const result = await response.json()

      if (result.success) {
        setMovements(result.data.movements || [])
        setStats(result.data.stats || null)
        setUpcomingHearings(result.data.upcoming_hearings || [])
      } else {
        setDbError(result.error || 'فشل في جلب بيانات حركة القضايا')
        setMovements([])
        toast.error('فشل في جلب بيانات حركة القضايا')
      }
    } catch (error) {
      console.error('Network error:', error)
      setDbError('فشل في الاتصال بقاعدة البيانات')
      setMovements([])
      toast.error('فشل في الاتصال بقاعدة البيانات')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchCaseMovements()
  }, [])

  const filteredMovements = movements.filter(movement => {
    const matchesSearch = movement.case_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      movement.case_title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      movement.description?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesType = !filterType || movement.movement_type === filterType
    const matchesPriority = !filterPriority || movement.priority === filterPriority
    
    return matchesSearch && matchesType && matchesPriority
  })

  // دالة لتحديد لون الأولوية
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800'
      case 'high': return 'bg-orange-100 text-orange-800'
      case 'normal': return 'bg-blue-100 text-blue-800'
      case 'low': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // دالة لتحديد أيقونة نوع الحركة
  const getMovementIcon = (type: string) => {
    switch (type) {
      case 'case_created': return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'case_assigned': return <User className="h-4 w-4 text-blue-600" />
      case 'hearing_scheduled': return <Gavel className="h-4 w-4 text-purple-600" />
      case 'follow_added': return <FileText className="h-4 w-4 text-indigo-600" />
      case 'document_uploaded': return <FileText className="h-4 w-4 text-teal-600" />
      case 'case_status_changed': return <RefreshCw className="h-4 w-4 text-yellow-600" />
      default: return <Activity className="h-4 w-4 text-gray-600" />
    }
  }

  // دالة لتنسيق نوع الحركة
  const formatMovementType = (type: string) => {
    const types: Record<string, string> = {
      'case_created': 'إنشاء قضية',
      'case_assigned': 'توزيع قضية',
      'case_status_changed': 'تغيير حالة',
      'hearing_scheduled': 'تحديد جلسة',
      'hearing_postponed': 'تأجيل جلسة',
      'hearing_completed': 'انتهاء جلسة',
      'follow_added': 'إضافة متابعة',
      'document_uploaded': 'رفع وثيقة',
      'document_updated': 'تحديث وثيقة',
      'payment_received': 'استلام دفعة',
      'case_closed': 'إغلاق قضية',
      'case_reopened': 'إعادة فتح قضية',
      'client_contacted': 'تواصل مع العميل',
      'court_decision': 'قرار محكمة',
      'appeal_filed': 'تقديم استئناف',
      'settlement_reached': 'تسوية ودية'
    }
    return types[type] || type
  }

  // دالة لتنسيق التاريخ
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    } catch {
      return dateString
    }
  }

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>جاري تحميل حركة القضايا...</p>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان والإحصائيات */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">حركة القضايا</h1>
            <p className="text-gray-600 mt-1">تتبع جميع أنشطة وحركات القضايا</p>
          </div>
          
          <div className="flex gap-4">
            <Button
              onClick={fetchCaseMovements}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              تحديث
            </Button>
          </div>
        </div>

        {/* الإحصائيات */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <Activity className="h-8 w-8 text-blue-600" />
                  <div>
                    <p className="text-sm text-gray-600">إجمالي الحركات</p>
                    <p className="text-2xl font-bold">{stats.total_count}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <Gavel className="h-8 w-8 text-purple-600" />
                  <div>
                    <p className="text-sm text-gray-600">الجلسات</p>
                    <p className="text-2xl font-bold">{stats.hearings_count}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <AlertTriangle className="h-8 w-8 text-red-600" />
                  <div>
                    <p className="text-sm text-gray-600">عاجل</p>
                    <p className="text-2xl font-bold">{stats.urgent_count}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <Calendar className="h-8 w-8 text-green-600" />
                  <div>
                    <p className="text-sm text-gray-600">جلسات قادمة</p>
                    <p className="text-2xl font-bold">{stats.upcoming_hearings}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* أدوات البحث والتصفية */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في الحركات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div className="flex gap-2">
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="">جميع الأنواع</option>
                  <option value="case_created">إنشاء قضية</option>
                  <option value="case_assigned">توزيع قضية</option>
                  <option value="hearing_scheduled">تحديد جلسة</option>
                  <option value="follow_added">إضافة متابعة</option>
                  <option value="document_uploaded">رفع وثيقة</option>
                </select>

                <select
                  value={filterPriority}
                  onChange={(e) => setFilterPriority(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="">جميع الأولويات</option>
                  <option value="urgent">عاجل</option>
                  <option value="high">عالي</option>
                  <option value="normal">عادي</option>
                  <option value="low">منخفض</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* رسالة الخطأ */}
        {dbError && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 text-red-800">
                <AlertTriangle className="h-5 w-5" />
                <span>{dbError}</span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* قائمة الحركات */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              سجل الحركات ({filteredMovements.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {filteredMovements.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>لا توجد حركات للعرض</p>
                <p className="text-sm mt-2">سيتم عرض الحركات هنا عند إنشاء الجداول المطلوبة</p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredMovements.map((movement) => (
                  <div
                    key={movement.id}
                    className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3 flex-1">
                        {getMovementIcon(movement.movement_type)}
                        
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="font-medium text-gray-900">
                              {movement.case_number} - {movement.case_title}
                            </h3>
                            <Badge className={getPriorityColor(movement.priority)}>
                              {movement.priority === 'urgent' ? 'عاجل' :
                               movement.priority === 'high' ? 'عالي' :
                               movement.priority === 'normal' ? 'عادي' : 'منخفض'}
                            </Badge>
                          </div>
                          
                          <p className="text-sm text-gray-600 mb-2">
                            {formatMovementType(movement.movement_type)}: {movement.description}
                          </p>
                          
                          <div className="flex items-center gap-4 text-xs text-gray-500">
                            <span className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              {formatDate(movement.created_at)}
                            </span>
                            
                            {movement.created_by_name && (
                              <span className="flex items-center gap-1">
                                <User className="h-3 w-3" />
                                {movement.created_by_name}
                              </span>
                            )}
                            
                            {movement.hearing_date && (
                              <span className="flex items-center gap-1 text-purple-600">
                                <Gavel className="h-3 w-3" />
                                جلسة: {new Date(movement.hearing_date).toLocaleDateString('ar-SA')}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
