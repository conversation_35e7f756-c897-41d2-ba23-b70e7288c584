'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import MainLayout from '@/components/layout/main-layout'
import {
  Search,
  Calendar,
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Plus,
  Filter,
  Bell,
  TrendingUp,
  Users,
  Gavel,
  FileText,
  Eye,
  Edit,
  MoreHorizontal
} from 'lucide-react'

interface CaseData {
  id: number
  case_number: string
  title: string
  client_name: string
  lawyer_name: string
  status: string
  priority: string
  last_movement: any
  next_session: any
  movements_count: number
  activity_status: string
  created_at: string
  updated_at: string
}

interface StatsData {
  total_cases: number
  active_cases: number
  pending_cases: number
  closed_cases: number
  high_priority_cases: number
  inactive_cases: number
  upcoming_sessions: number
  today_sessions: number
}

interface SessionData {
  id: number
  case_number: string
  case_title: string
  client_name: string
  lawyer_name: string
  session_date: string
  court_name: string
  session_type: string
  urgency_level: string
}

export default function CaseMovementsPage() {
  const [cases, setCases] = useState<CaseData[]>([])
  const [stats, setStats] = useState<StatsData | null>(null)
  const [upcomingSessions, setUpcomingSessions] = useState<SessionData[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [priorityFilter, setPriorityFilter] = useState('')
  const [currentPage, setCurrentPage] = useState(1)

  // جلب البيانات
  const fetchData = async () => {
    try {
      setLoading(true)

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter && { status: statusFilter }),
        ...(priorityFilter && { priority: priorityFilter })
      })

      const response = await fetch(`/api/case-movements?${params}`)
      const data = await response.json()

      if (data.success) {
        setCases(data.data.cases)
        setStats(data.data.stats)
        setUpcomingSessions(data.data.upcoming_sessions)
      }
    } catch (error) {
      console.error('خطأ في جلب البيانات:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [currentPage, searchTerm, statusFilter, priorityFilter])

  // دالة للحصول على لون الحالة
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'closed': return 'bg-gray-100 text-gray-800'
      default: return 'bg-blue-100 text-blue-800'
    }
  }

  // دالة للحصول على لون الأولوية
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'low': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // دالة للحصول على لون النشاط
  const getActivityColor = (activity: string) => {
    switch (activity) {
      case 'urgent': return 'bg-red-100 text-red-800'
      case 'active': return 'bg-green-100 text-green-800'
      case 'inactive': return 'bg-gray-100 text-gray-800'
      default: return 'bg-blue-100 text-blue-800'
    }
  }

  // دالة للحصول على لون الجلسة
  const getSessionUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'urgent': return 'bg-red-100 text-red-800'
      case 'soon': return 'bg-yellow-100 text-yellow-800'
      case 'upcoming': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">جاري تحميل البيانات...</p>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="container mx-auto p-6 space-y-6" dir="rtl">
      {/* العنوان والبحث */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Gavel className="h-8 w-8 mr-3 text-blue-600" />
              حركة القضايا
            </h1>
            <p className="text-gray-600 mt-1">تتبع ومراقبة جميع حركات القضايا والجلسات</p>
          </div>

          <div className="flex items-center gap-4">
            <div className="relative w-80">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="البحث في القضايا..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>

            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="">جميع الحالات</option>
              <option value="active">نشط</option>
              <option value="pending">معلق</option>
              <option value="closed">مغلق</option>
            </select>

            <select
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="">جميع الأولويات</option>
              <option value="high">عالي</option>
              <option value="medium">متوسط</option>
              <option value="low">منخفض</option>
            </select>
          </div>
        </div>

        <div className="flex space-x-2 space-x-reverse">
          <Button
            variant="outline"
            className="border-blue-600 text-blue-600 hover:bg-blue-50"
            onClick={fetchData}
          >
            <Clock className="h-4 w-4 mr-2" />
            تحديث
          </Button>
          <Button variant="outline" className="border-gray-600 text-gray-600 hover:bg-gray-50">
            <Filter className="h-4 w-4 mr-2" />
            فلترة متقدمة
          </Button>
        </div>
      </div>

      {/* الإحصائيات السريعة */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="border-r-4 border-r-blue-500">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي القضايا</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.total_cases}</p>
                </div>
                <FileText className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-r-4 border-r-green-500">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">القضايا النشطة</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.active_cases}</p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-r-4 border-r-yellow-500">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">جلسات هذا الأسبوع</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.upcoming_sessions}</p>
                </div>
                <Calendar className="h-8 w-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-r-4 border-r-red-500">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">جلسات اليوم</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.today_sessions}</p>
                </div>
                <Bell className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* التنبيهات والمواعيد المهمة */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* التنبيهات العاجلة */}
        {upcomingSessions.length > 0 && (
          <Card className="border-l-4 border-l-red-500 bg-red-50">
            <CardHeader>
              <CardTitle className="flex items-center text-red-800">
                <AlertTriangle className="h-5 w-5 mr-2" />
                تنبيهات عاجلة - الجلسات القادمة
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {upcomingSessions.slice(0, 3).map((session) => (
                  <div key={session.id} className="flex items-center justify-between p-3 bg-white rounded-lg border">
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <Badge className={getSessionUrgencyColor(session.urgency_level)}>
                        {session.urgency_level === 'urgent' ? 'عاجل' :
                         session.urgency_level === 'soon' ? 'قريب' : 'قادم'}
                      </Badge>
                      <div>
                        <p className="font-medium">{session.case_title}</p>
                        <p className="text-sm text-gray-600">
                          {session.court_name} - {new Date(session.session_date).toLocaleDateString('ar-SA')}
                        </p>
                      </div>
                    </div>
                    <Button size="sm" variant="outline" title="عرض تفاصيل الجلسة">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* القضايا المتوقفة */}
        <Card className="border-l-4 border-l-yellow-500 bg-yellow-50">
          <CardHeader>
            <CardTitle className="flex items-center text-yellow-800">
              <XCircle className="h-5 w-5 mr-2" />
              قضايا تحتاج متابعة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {cases.filter(c => c.activity_status === 'inactive').slice(0, 3).map((caseItem) => (
                <div key={caseItem.id} className="flex items-center justify-between p-3 bg-white rounded-lg border">
                  <div>
                    <p className="font-medium">{caseItem.case_number}</p>
                    <p className="text-sm text-gray-600">
                      آخر نشاط: {caseItem.last_movement ?
                        new Date(caseItem.last_movement.movement_date).toLocaleDateString('ar-SA') :
                        'غير محدد'}
                    </p>
                  </div>
                  <Badge className="bg-yellow-100 text-yellow-800">
                    متوقف
                  </Badge>
                </div>
              ))}
              {cases.filter(c => c.activity_status === 'inactive').length === 0 && (
                <p className="text-center text-gray-500 py-4">
                  جميع القضايا نشطة ✅
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* مراحل القضايا - الخط الزمني */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <TrendingUp className="h-5 w-5 mr-2" />
            مراحل القضايا النشطة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {cases.slice(0, 3).map((caseItem) => (
              <div key={caseItem.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="font-semibold text-lg">{caseItem.case_number}</h3>
                    <p className="text-gray-600">{caseItem.title}</p>
                  </div>
                  <Badge className={getActivityColor(caseItem.activity_status)}>
                    {caseItem.activity_status === 'urgent' ? 'عاجل' :
                     caseItem.activity_status === 'active' ? 'نشط' : 'متوقف'}
                  </Badge>
                </div>

                {/* الخط الزمني للمراحل */}
                <div className="relative">
                  <div className="absolute right-4 top-0 bottom-0 w-0.5 bg-gray-200"></div>

                  <div className="space-y-4">
                    {/* مرحلة مكتملة */}
                    <div className="flex items-center">
                      <div className="relative z-10 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                        <CheckCircle className="h-4 w-4 text-white" />
                      </div>
                      <div className="mr-4 flex-1">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium">تسجيل القضية</h4>
                          <span className="text-sm text-gray-500">مكتمل</span>
                        </div>
                        <p className="text-sm text-gray-600">تم تسجيل القضية وإدخال البيانات الأساسية</p>
                        <p className="text-xs text-gray-400">1 يناير 2025</p>
                      </div>
                    </div>

                    {/* مرحلة جارية */}
                    <div className="flex items-center">
                      <div className="relative z-10 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <Clock className="h-4 w-4 text-white" />
                      </div>
                      <div className="mr-4 flex-1">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium">جمع الأدلة والوثائق</h4>
                          <span className="text-sm text-blue-600">جاري</span>
                        </div>
                        <p className="text-sm text-gray-600">مراجعة الوثائق وجمع الأدلة اللازمة</p>
                        <p className="text-xs text-gray-400">بدأت في 3 يناير 2025</p>
                      </div>
                    </div>

                    {/* مرحلة قادمة */}
                    <div className="flex items-center">
                      <div className="relative z-10 w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                        <Gavel className="h-4 w-4 text-gray-600" />
                      </div>
                      <div className="mr-4 flex-1">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium text-gray-500">المرافعة أمام المحكمة</h4>
                          <span className="text-sm text-gray-400">قادم</span>
                        </div>
                        <p className="text-sm text-gray-400">تقديم المرافعة والدفوع القانونية</p>
                        <p className="text-xs text-gray-400">متوقع في 15 يناير 2025</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* شريط التقدم */}
                <div className="mt-4">
                  <div className="flex items-center justify-between text-sm mb-2">
                    <span className="text-gray-600">تقدم القضية</span>
                    <span className="font-medium">66%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full" style={{ width: '66%' }}></div>
                  </div>
                </div>

                {/* معلومات إضافية */}
                <div className="mt-4 pt-4 border-t flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-4 space-x-reverse">
                    <span className="flex items-center">
                      <Users className="h-4 w-4 mr-1" />
                      {caseItem.client_name}
                    </span>
                    <span className="flex items-center">
                      <FileText className="h-4 w-4 mr-1" />
                      {caseItem.lawyer_name}
                    </span>
                    <span className="flex items-center">
                      <TrendingUp className="h-4 w-4 mr-1" />
                      {caseItem.movements_count} حركة
                    </span>
                  </div>
                  {caseItem.next_session && (
                    <span className="flex items-center text-red-600">
                      <Calendar className="h-4 w-4 mr-1" />
                      جلسة قادمة: {new Date(caseItem.next_session.session_date).toLocaleDateString('ar-SA')}
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* جدول القضايا */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            جميع القضايا ({cases.length})
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    القضية
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    العميل
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الحالة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    آخر حركة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الجلسة القادمة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    النشاط
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الحركات
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {cases.map((caseItem) => (
                  <tr key={caseItem.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {caseItem.case_number}
                        </div>
                        <div className="text-sm text-gray-500">
                          {caseItem.title}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{caseItem.client_name}</div>
                      <div className="text-sm text-gray-500">{caseItem.lawyer_name}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="space-y-1">
                        <Badge className={getStatusColor(caseItem.status)}>
                          {caseItem.status === 'active' ? 'نشط' :
                           caseItem.status === 'pending' ? 'معلق' : 'مغلق'}
                        </Badge>
                        <Badge className={getPriorityColor(caseItem.priority)}>
                          {caseItem.priority === 'high' ? 'عالي' :
                           caseItem.priority === 'medium' ? 'متوسط' : 'منخفض'}
                        </Badge>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {caseItem.last_movement ? (
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {caseItem.last_movement.movement_type}
                          </div>
                          <div className="text-sm text-gray-500">
                            {new Date(caseItem.last_movement.movement_date).toLocaleDateString('ar-SA')}
                          </div>
                        </div>
                      ) : (
                        <span className="text-sm text-gray-400">لا توجد حركات</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {caseItem.next_session ? (
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {new Date(caseItem.next_session.session_date).toLocaleDateString('ar-SA')}
                          </div>
                          <div className="text-sm text-gray-500">
                            {caseItem.next_session.court_name}
                          </div>
                        </div>
                      ) : (
                        <span className="text-sm text-gray-400">غير محدد</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Badge className={getActivityColor(caseItem.activity_status)}>
                        {caseItem.activity_status === 'urgent' ? 'عاجل' :
                         caseItem.activity_status === 'active' ? 'نشط' : 'متوقف'}
                      </Badge>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <TrendingUp className="h-4 w-4 text-blue-500 mr-1" />
                        <span className="font-medium">{caseItem.movements_count}</span>
                        <span className="text-sm text-gray-500 mr-1">حركة</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2 space-x-reverse">
                        <Button
                          size="sm"
                          variant="outline"
                          title="عرض تفاصيل القضية"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          title="عرض المراحل"
                        >
                          <FileText className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          title="تتبع الحركات"
                        >
                          <TrendingUp className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </MainLayout>
  )
}
