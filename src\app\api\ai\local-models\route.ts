import { NextRequest, NextResponse } from 'next/server'

// فهرس النماذج المعروضة في الواجهة
const hasOpenAI = !!process.env.OPENAI_API_KEY
const hasGroq = !!process.env.GROQ_API_KEY
const catalog = [
  {
    key: 'groq-llama-8b',
    name: 'Llama 3.1 8B (Groq)',
    endpoint: '/api/ai/local-models',
    model: 'llama-3.1-8b-instant',
    description: 'نموذج سريع وخفيف للردود العامة (Groq)',
    status: hasGroq ? 'available' : 'api_key_required',
  },
  {
    key: 'openai-gpt-4o-mini',
    name: 'OpenAI GPT-4o mini',
    endpoint: '/api/ai/local-models',
    model: 'gpt-4o-mini',
    description: 'نموذج دقيق واقتصادي (يتطلب API Key)',
    status: 'api_key_required',
  },
  {
    key: 'openai-gpt-5',
    name: 'OpenAI GPT‑5 (مدفوع)',
    endpoint: '/api/ai/local-models',
    model: 'gpt-5',
    description: 'نموذج مدفوع مُحسَّن للجودة العالية. يتطلب مفتاح OpenAI صالح.',
    status: hasOpenAI ? 'available' : 'api_key_required',
  },
]

export async function GET() {
  try {
    return NextResponse.json({
      success: true,
      data: { models: catalog.map(m => ({ ...m, lastChecked: new Date().toISOString() })) }
    })
  } catch (error: any) {
    return NextResponse.json({ success: false, error: error.message }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { message, model, conversationId, context } = body || {}
    if (!message) {
      return NextResponse.json({ success: false, error: 'الرسالة مطلوبة' }, { status: 400 })
    }

    // اختيار النموذج الافتراضي إذا لم يتم تمريره
    const selectedKey: string = model || 'groq-llama-8b'
    const selected = catalog.find(m => m.key === selectedKey)
    const selectedModelId = selected?.model || 'llama-3.1-8b-instant'

    // توجيه الطلب إلى Groq إذا كان النموذج من groq ومتوفّر مفتاح API
    if (selectedKey.startsWith('groq-')) {
      if (!process.env.GROQ_API_KEY) {
        // رد لطيف بدون إعادة السؤال كما هو
        return NextResponse.json({
          success: true,
          response: 'الخدمة السحابية للذكاء الاصطناعي غير مفعّلة حالياً (GROQ_API_KEY مفقود). يمكنك تفعيل المفتاح لاستخدام نموذج Llama 3.1 8B أو المتابعة بإرسال استفساراتك وسيقوم الفريق بالرد.'
        })
      }

      const groqEndpoint = 'https://api.groq.com/openai/v1/chat/completions'
      const sysPrompt = 'أنت مساعد قانوني عربي مختصر ومباشر. أجب باحترافية وبنقاط واضحة عند الحاجة.'

      const groqRes = await fetch(groqEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.GROQ_API_KEY}`,
        },
        body: JSON.stringify({
          model: selectedModelId,
          messages: [
            { role: 'system', content: sysPrompt },
            ...(Array.isArray(context) ? context : []).filter(Boolean),
            { role: 'user', content: String(message) },
          ],
          temperature: 0.3,
          max_tokens: 512,
        })
      })

      if (!groqRes.ok) {
        const errText = await groqRes.text()
        return NextResponse.json({ success: false, error: `Groq API error: ${errText}` }, { status: 502 })
      }

      const groqJson = await groqRes.json()
      const content: string = groqJson?.choices?.[0]?.message?.content || 'تعذّر توليد رد مناسب حالياً.'

      return NextResponse.json({ success: true, response: content, model: selectedKey, conversationId: conversationId })
    }

    // Fallback محلي آمن بدون إعادة نص السؤال الحرفي
    const genericReplies = [
      'تم استلام سؤالك وسنقوم بمعالجته. هل يمكنك إضافة تفاصيل أكثر ليتضح الطلب بشكل أدق؟',
      'أفهم طلبك. سأعطيك رداً موجزاً حال توافر مزيد من المعطيات. ما هي النقاط الأساسية التي تود التركيز عليها؟',
      'شكراً لتواصلك. يرجى تحديد نوع القضية أو المجال القانوني المرتبط لاستكمال الاستشارة.'
    ]
    const reply = genericReplies[Math.floor(Math.random() * genericReplies.length)]
    return NextResponse.json({ success: true, response: reply, model: selectedKey })
  } catch (error: any) {
    return NextResponse.json({ success: false, error: error.message }, { status: 500 })
  }
}
