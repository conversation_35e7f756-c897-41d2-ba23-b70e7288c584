import { NextRequest, NextResponse } from 'next/server'

// نماذج محلية/خارجية افتراضية للفحص السريع
const catalog = [
  {
    key: 'groq-llama-8b',
    name: 'Llama 3.1 8B (Groq)',
    endpoint: '/api/ai/local-models',
    model: 'llama-3.1-8b-instant',
    description: 'نموذج سريع وخفيف للردود العامة',
    status: 'available',
  },
  {
    key: 'openai-gpt-4o-mini',
    name: 'OpenAI GPT-4o mini',
    endpoint: '/api/ai/local-models',
    model: 'gpt-4o-mini',
    description: 'نموذج دقيق واقتصادي (يتطلب API Key)',
    status: 'api_key_required',
  },
]

export async function GET() {
  try {
    // يمكن لاحقًا فحص خدمات فعلية وإرجاع الحالة الفعلية
    return NextResponse.json({
      success: true,
      data: { models: catalog.map(m => ({ ...m, lastChecked: new Date().toISOString() })) }
    })
  } catch (error: any) {
    return NextResponse.json({ success: false, error: error.message }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { message, model } = body || {}
    if (!message) {
      return NextResponse.json({ success: false, error: 'الرسالة مطلوبة' }, { status: 400 })
    }

    // رد بسيط للتجربة المحلية دون مزود خارجي
    const reply = `رد تلقائي (اختباري) من ${model || 'local-model'}:\n\n${message}`

    return NextResponse.json({ success: true, response: reply })
  } catch (error: any) {
    return NextResponse.json({ success: false, error: error.message }, { status: 500 })
  }
}
