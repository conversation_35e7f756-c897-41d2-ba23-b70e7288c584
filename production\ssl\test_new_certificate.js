// اختبار الشهادة الجديدة مع المفتاح الجديد
const fs = require('fs');
const crypto = require('crypto');

console.log('🧪 اختبار الشهادة الجديدة مع المفتاح الجديد');
console.log('='.repeat(60));

// مسارات الملفات الجديدة
const newFiles = {
  cert: 'ssl/mohammi_com_new.crt',
  key: 'ssl/new_mohammi.key'
};

// التحقق من وجود الملفات
console.log('📁 فحص وجود الملفات الجديدة:');

if (!fs.existsSync(newFiles.key)) {
  console.log('❌ المفتاح الجديد غير موجود:', newFiles.key);
  console.log('💡 تأكد من تشغيل: node ssl/create_csr_nodejs.js');
  process.exit(1);
}

if (!fs.existsSync(newFiles.cert)) {
  console.log('❌ الشهادة الجديدة غير موجودة:', newFiles.cert);
  console.log('💡 يرجى تحميل الشهادة الجديدة من Name.com');
  console.log('💡 واحفظها باسم: ssl/mohammi_com_new.crt');
  process.exit(1);
}

console.log('✅ المفتاح الجديد:', newFiles.key);
console.log('✅ الشهادة الجديدة:', newFiles.cert);

// قراءة الملفات
console.log('\n📖 قراءة الملفات الجديدة...');
let certContent, keyContent;

try {
  certContent = fs.readFileSync(newFiles.cert, 'utf8');
  keyContent = fs.readFileSync(newFiles.key, 'utf8');
  console.log('✅ تم قراءة الملفات بنجاح');
} catch (error) {
  console.log('❌ خطأ في قراءة الملفات:', error.message);
  process.exit(1);
}

// اختبار تطابق الشهادة والمفتاح الجديدين
console.log('\n🔍 اختبار تطابق الشهادة والمفتاح الجديدين...');

try {
  // قراءة المفتاح الخاص الجديد
  const privateKey = crypto.createPrivateKey(keyContent);
  console.log('✅ تم قراءة المفتاح الخاص الجديد');
  
  // استخراج المفتاح العام من المفتاح الخاص
  const publicKeyFromPrivate = crypto.createPublicKey(privateKey);
  console.log('✅ تم استخراج المفتاح العام من المفتاح الخاص');
  
  // استخراج المفتاح العام من الشهادة الجديدة
  const publicKeyFromCert = crypto.createPublicKey({
    key: certContent,
    format: 'pem'
  });
  console.log('✅ تم استخراج المفتاح العام من الشهادة الجديدة');
  
  // مقارنة المفاتيح العامة
  const privateKeyPem = publicKeyFromPrivate.export({ format: 'pem', type: 'spki' });
  const certKeyPem = publicKeyFromCert.export({ format: 'pem', type: 'spki' });
  
  if (privateKeyPem === certKeyPem) {
    console.log('✅ المفاتيح العامة متطابقة! الشهادة والمفتاح الجديدان متوافقان');
  } else {
    console.log('❌ المفاتيح العامة غير متطابقة!');
    process.exit(1);
  }
  
} catch (error) {
  console.log('❌ خطأ في اختبار التطابق:', error.message);
  process.exit(1);
}

// اختبار التشفير وفك التشفير
console.log('\n🔒 اختبار التشفير وفك التشفير...');

try {
  const privateKey = crypto.createPrivateKey(keyContent);
  const publicKey = crypto.createPublicKey({ key: certContent, format: 'pem' });
  
  const testMessage = 'SSL Test Message';
  console.log('📤 الرسالة الأصلية:', testMessage);
  
  // تشفير بالمفتاح العام
  const encrypted = crypto.publicEncrypt(publicKey, Buffer.from(testMessage));
  console.log('🔐 تم التشفير بالمفتاح العام');
  
  // فك التشفير بالمفتاح الخاص
  const decrypted = crypto.privateDecrypt(privateKey, encrypted);
  const decryptedMessage = decrypted.toString();
  console.log('📥 الرسالة المفكوكة:', decryptedMessage);
  
  if (testMessage === decryptedMessage) {
    console.log('✅ اختبار التشفير/فك التشفير نجح!');
  } else {
    console.log('❌ اختبار التشفير/فك التشفير فشل!');
    process.exit(1);
  }
  
} catch (error) {
  console.log('❌ خطأ في اختبار التشفير:', error.message);
  process.exit(1);
}

// اختبار إنشاء خيارات SSL
console.log('\n⚙️ اختبار إنشاء خيارات SSL...');

try {
  const sslOptions = {
    key: keyContent,
    cert: certContent
  };
  
  // محاولة إنشاء سياق SSL
  const https = require('https');
  const server = https.createServer(sslOptions);
  
  console.log('✅ تم إنشاء خيارات SSL بنجاح');
  console.log('✅ يمكن استخدام الملفات الجديدة لإنشاء خادم HTTPS');
  
} catch (error) {
  console.log('❌ خطأ في إنشاء خيارات SSL:', error.message);
  process.exit(1);
}

// نسخ الملفات الجديدة كملفات نشطة
console.log('\n📁 نسخ الملفات الجديدة كملفات نشطة...');

try {
  // نسخ الشهادة الجديدة
  fs.copyFileSync(newFiles.cert, 'ssl/mohammi_com.crt');
  console.log('✅ تم نسخ الشهادة الجديدة إلى: ssl/mohammi_com.crt');
  
  // نسخ المفتاح الجديد
  fs.copyFileSync(newFiles.key, 'ssl/correct_mohammi.key');
  console.log('✅ تم نسخ المفتاح الجديد إلى: ssl/correct_mohammi.key');
  
} catch (error) {
  console.log('❌ خطأ في نسخ الملفات:', error.message);
}

// النتيجة النهائية
console.log('\n🎉 النتيجة النهائية:');
console.log('='.repeat(50));
console.log('✅ الشهادة الجديدة والمفتاح الجديد متوافقان تماماً');
console.log('✅ جميع الاختبارات نجحت');
console.log('✅ يمكن الآن تشغيل SSL بنجاح');

console.log('\n📁 الملفات الجاهزة للاستخدام:');
console.log('🔐 الشهادة: ssl/mohammi_com.crt (محدثة)');
console.log('🔑 المفتاح الصحيح: ssl/correct_mohammi.key (جديد)');

console.log('\n🚀 الخطوة التالية:');
console.log('node ssl/start_ssl_with_new_certificate.js');

console.log('\n🌐 بعد تشغيل SSL، ستعمل الروابط:');
console.log('• https://mohammi.com:7443');
console.log('• https://mohammi.com:8914');

console.log('\n🏆 مبروك! تم حل مشكلة SSL بنجاح!');
