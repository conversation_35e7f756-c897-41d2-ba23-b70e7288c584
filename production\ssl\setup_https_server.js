// إعداد خادم HTTPS باستخدام شهادة SSL
const https = require('https');
const http = require('http');
const fs = require('fs');
const path = require('path');

console.log('🔐 إعداد خادم HTTPS لـ mohammi.com');
console.log('='.repeat(50));

// التحقق من وجود ملفات SSL
const sslDir = path.join(__dirname);
const keyPath = path.join(sslDir, 'mohammi.key');
const certPath = path.join(sslDir, 'mohammi.crt');
const signedCertPath = path.join(sslDir, 'mohammi_signed.crt');

console.log('📁 فحص ملفات SSL...');

// استخدام الشهادة الموقعة إذا كانت متاحة، وإلا استخدم الشهادة التجريبية
let certificatePath = certPath;
if (fs.existsSync(signedCertPath)) {
  certificatePath = signedCertPath;
  console.log('✅ تم العثور على الشهادة الموقعة: mohammi_signed.crt');
} else if (fs.existsSync(certPath)) {
  certificatePath = certPath;
  console.log('⚠️ استخدام الشهادة التجريبية: mohammi.crt');
  console.log('   (للإنتاج، يرجى استخدام شهادة موقعة من CA معتمد)');
} else {
  console.error('❌ لم يتم العثور على ملف الشهادة');
  console.log('يرجى تشغيل generate_ssl_nodejs.js أولاً');
  process.exit(1);
}

if (!fs.existsSync(keyPath)) {
  console.error('❌ لم يتم العثور على المفتاح الخاص: mohammi.key');
  console.log('يرجى تشغيل generate_ssl_nodejs.js أولاً');
  process.exit(1);
}

console.log('✅ تم العثور على المفتاح الخاص: mohammi.key');

// قراءة ملفات SSL
let privateKey, certificate;

try {
  privateKey = fs.readFileSync(keyPath, 'utf8');
  certificate = fs.readFileSync(certificatePath, 'utf8');
  console.log('✅ تم تحميل ملفات SSL بنجاح');
} catch (error) {
  console.error('❌ خطأ في قراءة ملفات SSL:', error.message);
  process.exit(1);
}

// إعداد خيارات SSL
const sslOptions = {
  key: privateKey,
  cert: certificate,
  // إعدادات أمان إضافية
  secureProtocol: 'TLSv1_2_method',
  ciphers: [
    'ECDHE-RSA-AES128-GCM-SHA256',
    'ECDHE-RSA-AES256-GCM-SHA384',
    'ECDHE-RSA-AES128-SHA256',
    'ECDHE-RSA-AES256-SHA384'
  ].join(':'),
  honorCipherOrder: true
};

// إنشاء خادم HTTPS بسيط للاختبار
const httpsServer = https.createServer(sslOptions, (req, res) => {
  res.writeHead(200, {
    'Content-Type': 'text/html; charset=utf-8',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block'
  });
  
  const html = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 خادم HTTPS - mohammi.com</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            max-width: 600px;
        }
        .icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }
        h1 {
            margin: 0 0 20px 0;
            font-size: 2.5rem;
        }
        .info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: right;
        }
        .status {
            color: #4ade80;
            font-weight: bold;
        }
        .warning {
            color: #fbbf24;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">🔐</div>
        <h1>خادم HTTPS يعمل بنجاح!</h1>
        <p>مرحباً بك في موقع mohammi.com المحمي بـ SSL</p>
        
        <div class="info">
            <h3>معلومات الاتصال:</h3>
            <p><strong>البروتوكول:</strong> <span class="status">HTTPS ✅</span></p>
            <p><strong>المنفذ:</strong> ${req.socket.localPort}</p>
            <p><strong>الشهادة:</strong> ${certificatePath.includes('signed') ? 'موقعة من CA' : 'موقعة ذاتياً'}</p>
            <p><strong>التشفير:</strong> TLS 1.2+</p>
            <p><strong>الوقت:</strong> ${new Date().toLocaleString('ar-EG')}</p>
        </div>
        
        ${!certificatePath.includes('signed') ? `
        <div class="info">
            <p class="warning">⚠️ تحذير: هذه شهادة موقعة ذاتياً للاختبار فقط</p>
            <p>للإنتاج، يرجى استخدام شهادة موقعة من CA معتمد</p>
        </div>
        ` : ''}
        
        <div class="info">
            <h3>الخطوات التالية:</h3>
            <p>1. تأكد من أن DNS يشير إلى هذا الخادم</p>
            <p>2. قم بتكوين جدار الحماية للسماح بالمنفذ 443</p>
            <p>3. اختبر الموقع من متصفحات مختلفة</p>
        </div>
    </div>
</body>
</html>`;
  
  res.end(html);
});

// إنشاء خادم HTTP لإعادة التوجيه إلى HTTPS
const httpServer = http.createServer((req, res) => {
  const httpsUrl = `https://${req.headers.host}${req.url}`;
  res.writeHead(301, {
    'Location': httpsUrl,
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'
  });
  res.end();
});

// بدء الخوادم
const httpsPort = 443;
const httpPort = 80;

// محاولة بدء خادم HTTPS على المنفذ 443
httpsServer.listen(httpsPort, (err) => {
  if (err) {
    console.error(`❌ فشل في بدء خادم HTTPS على المنفذ ${httpsPort}:`, err.message);
    console.log('💡 جرب تشغيل الأمر كمدير (Run as Administrator)');
    console.log('💡 أو استخدم منفذ آخر مثل 8443');
    
    // محاولة استخدام منفذ بديل
    const altPort = 8443;
    httpsServer.listen(altPort, () => {
      console.log(`✅ خادم HTTPS يعمل على المنفذ ${altPort}`);
      console.log(`🌐 الرابط: https://localhost:${altPort}`);
      console.log(`🌐 أو: https://mohammi.com:${altPort} (إذا كان DNS مكوناً)`);
    });
  } else {
    console.log(`✅ خادم HTTPS يعمل على المنفذ ${httpsPort}`);
    console.log(`🌐 الرابط: https://mohammi.com`);
    console.log(`🌐 أو: https://localhost`);
  }
});

// محاولة بدء خادم HTTP لإعادة التوجيه
httpServer.listen(httpPort, (err) => {
  if (err) {
    console.log(`⚠️ لم يتم بدء خادم إعادة التوجيه HTTP على المنفذ ${httpPort}`);
    console.log('   (هذا طبيعي إذا كان المنفذ مستخدماً)');
  } else {
    console.log(`✅ خادم إعادة التوجيه HTTP يعمل على المنفذ ${httpPort}`);
  }
});

// معالجة إيقاف الخادم
process.on('SIGINT', () => {
  console.log('\n🛑 إيقاف الخوادم...');
  httpsServer.close(() => {
    console.log('✅ تم إيقاف خادم HTTPS');
  });
  httpServer.close(() => {
    console.log('✅ تم إيقاف خادم HTTP');
  });
  process.exit(0);
});

console.log('\n🎯 نصائح:');
console.log('• للاختبار المحلي: https://localhost:8443');
console.log('• للإنتاج: تأكد من تكوين DNS وجدار الحماية');
console.log('• اضغط Ctrl+C لإيقاف الخادم');
console.log('\n⏳ الخادم يعمل... اضغط Ctrl+C للإيقاف');
