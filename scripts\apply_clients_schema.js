#!/usr/bin/env node
/**
 * Ensure clients schema matches app expectations (adds account_id if missing, etc.)
 * Safe to run multiple times.
 */
const fs = require('fs');
const path = require('path');
const { Client } = require('pg');

function loadConfig() {
  const rcPath = path.join(process.cwd(), 'routing.config.json');
  const rc = fs.existsSync(rcPath) ? JSON.parse(fs.readFileSync(rcPath, 'utf8')) : {};
  const dc = rc.default_config || {};
  return {
    host: process.env.DB_HOST || dc.db_host || 'localhost',
    port: Number(process.env.DB_PORT || dc.db_port || 5432),
    user: process.env.DB_USER || dc.db_user || 'postgres',
    password: process.env.DB_PASSWORD || dc.db_password || 'postgres',
    database: process.env.DB_NAME || dc.db_name || 'mohammi',
  };
}

async function run(){
  const conn = loadConfig();
  const client = new Client(conn);
  await client.connect();
  const exec = (sql, params=[]) => client.query(sql, params);
  try {
    await exec(`CREATE TABLE IF NOT EXISTS clients (
      id SERIAL PRIMARY KEY,
      name TEXT NOT NULL,
      phone TEXT,
      email TEXT,
      address TEXT,
      id_number VARCHAR(100),
      account_id INTEGER,
      status TEXT DEFAULT 'active',
      created_date DATE DEFAULT CURRENT_DATE,
      updated_at TIMESTAMP
    );`);

    // Ensure account_id exists
    const res = await exec(`SELECT 1 FROM information_schema.columns WHERE table_name='clients' AND column_name='account_id' AND table_schema='public'`);
    if (res.rowCount === 0) {
      await exec(`ALTER TABLE clients ADD COLUMN account_id INTEGER;`);
      console.log('+ Added clients.account_id');
    }

    console.log('\nClients schema updates applied successfully.');
  } catch (e) {
    console.error('Clients schema update failed:', e.message);
    process.exitCode = 1;
  } finally {
    await client.end().catch(()=>{});
  }
}

run();
