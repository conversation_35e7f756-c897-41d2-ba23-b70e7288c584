import { NextResponse } from 'next/server'
import { query } from '@/lib/db'

// POST - إنشاء الحسابات الأساسية للنظام
export async function POST() {
  try {
    // الحسابات الأساسية المطلوب إنشاؤها
    const systemAccounts = [
      {
        account_code: '4000',
        account_name: 'الإيرادات الرئيسية',
        account_type: 'I',
        account_level: 1,
        parent_id: null,
        is_active: true,
        accepts_transactions: true,
        account_balance: 0,
        linked_table: null,
        auto_create_sub_accounts: false
      },
      {
        account_code: '5000',
        account_name: 'المصروفات الرئيسية',
        account_type: 'E',
        account_level: 1,
        parent_id: null,
        is_active: true,
        accepts_transactions: true,
        account_balance: 0,
        linked_table: null,
        auto_create_sub_accounts: false
      },
      {
        account_code: '3000',
        account_name: 'رأس المال',
        account_type: 'E',
        account_level: 1,
        parent_id: null,
        is_active: true,
        accepts_transactions: true,
        account_balance: 0,
        linked_table: null,
        auto_create_sub_accounts: false
      },
      {
        account_code: '1111',
        account_name: 'الصندوق الرئيسي',
        account_type: 'A',
        account_level: 1,
        parent_id: null,
        is_active: true,
        accepts_transactions: true,
        account_balance: 0,
        linked_table: null,
        auto_create_sub_accounts: false
      },
      {
        account_code: '1121',
        account_name: 'حسابات العملاء',
        account_type: 'A',
        account_level: 1,
        parent_id: null,
        is_active: true,
        accepts_transactions: false,
        account_balance: 0,
        linked_table: 'clients',
        auto_create_sub_accounts: true
      },
      {
        account_code: '1122',
        account_name: 'حسابات الموظفين',
        account_type: 'A',
        account_level: 1,
        parent_id: null,
        is_active: true,
        accepts_transactions: false,
        account_balance: 0,
        linked_table: 'employees',
        auto_create_sub_accounts: true
      },
      {
        account_code: '2500',
        account_name: 'الحسابات الوسيطة',
        account_type: 'L',
        account_level: 1,
        parent_id: null,
        is_active: true,
        accepts_transactions: true,
        account_balance: 0,
        linked_table: null,
        auto_create_sub_accounts: false
      }
    ]

    let createdCount = 0
    let updatedCount = 0
    let errors = []

    // إنشاء أو تحديث كل حساب
    for (const account of systemAccounts) {
      try {
        // التحقق من وجود الحساب
        const existingAccount = await query(
          'SELECT id FROM chart_of_accounts WHERE account_code = $1',
          [account.account_code]
        )

        if (existingAccount.rows.length === 0) {
          // إنشاء حساب جديد
          await query(`
            INSERT INTO chart_of_accounts 
            (account_code, account_name, account_type, account_level, parent_id, 
             is_active, accepts_transactions, account_balance, linked_table, auto_create_sub_accounts,
             created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
          `, [
            account.account_code,
            account.account_name,
            account.account_type,
            account.account_level,
            account.parent_id,
            account.is_active,
            account.accepts_transactions,
            account.account_balance,
            account.linked_table,
            account.auto_create_sub_accounts
          ])
          createdCount++
        } else {
          // تحديث الحساب الموجود
          await query(`
            UPDATE chart_of_accounts 
            SET account_name = $1, account_type = $2, is_active = $3, 
                accepts_transactions = $4, linked_table = $5, 
                auto_create_sub_accounts = $6, updated_at = CURRENT_TIMESTAMP
            WHERE account_code = $7
          `, [
            account.account_name,
            account.account_type,
            account.is_active,
            account.accepts_transactions,
            account.linked_table,
            account.auto_create_sub_accounts,
            account.account_code
          ])
          updatedCount++
        }

        // إنشاء حسابات فرعية للعملاء والموظفين إذا كان مفعلاً
        if (account.auto_create_sub_accounts && account.linked_table) {
          await createSubAccountsForTable(account.account_code, account.linked_table)
        }

      } catch (error) {
        console.error(`Error processing account ${account.account_code}:`, error)
        errors.push(`خطأ في معالجة الحساب ${account.account_code}: ${error.message}`)
      }
    }

    return NextResponse.json({
      success: true,
      message: `تم إنشاء ${createdCount} حساب جديد وتحديث ${updatedCount} حساب موجود`,
      details: {
        created: createdCount,
        updated: updatedCount,
        errors: errors
      }
    })

  } catch (error) {
    console.error('Error creating system accounts:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إنشاء الحسابات الأساسية' },
      { status: 500 }
    )
  }
}

// دالة مساعدة لإنشاء حسابات فرعية
async function createSubAccountsForTable(parentAccountCode: string, tableName: string) {
  try {
    // الحصول على معرف الحساب الأب
    const parentResult = await query(
      'SELECT id FROM chart_of_accounts WHERE account_code = $1',
      [parentAccountCode]
    )

    if (parentResult.rows.length === 0) {
      throw new Error(`Parent account ${parentAccountCode} not found`)
    }

    const parentId = parentResult.rows[0].id

    // جلب السجلات من الجدول المرتبط
    let records = []
    if (tableName === 'clients') {
      const clientsResult = await query('SELECT id, name FROM clients WHERE status = $1', ['active'])
      records = clientsResult.rows
    } else if (tableName === 'employees') {
      const employeesResult = await query('SELECT id, name FROM employees WHERE status = $1', ['active'])
      records = employeesResult.rows
    }

    // إنشاء حساب فرعي لكل سجل
    for (const record of records) {
      const subAccountCode = `${parentAccountCode}-${record.id.toString().padStart(4, '0')}`
      
      // التحقق من عدم وجود الحساب الفرعي
      const existingSubAccount = await query(
        'SELECT id FROM chart_of_accounts WHERE account_code = $1',
        [subAccountCode]
      )

      if (existingSubAccount.rows.length === 0) {
        await query(`
          INSERT INTO chart_of_accounts 
          (account_code, account_name, account_type, account_level, parent_id, 
           is_active, accepts_transactions, account_balance, linked_table, auto_create_sub_accounts,
           created_at, updated_at)
          VALUES ($1, $2, $3, 2, $4, true, true, 0, $5, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        `, [
          subAccountCode,
          record.name,
          'A', // نوع الحساب
          parentId,
          tableName
        ])
      }
    }

  } catch (error) {
    console.error(`Error creating sub-accounts for ${tableName}:`, error)
    throw error
  }
}