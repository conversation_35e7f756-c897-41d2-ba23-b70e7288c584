import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'
import { 
  scheduleNotification, 
  getPendingNotifications, 
  getNotificationStats,
  getUserUpcomingNotifications 
} from '@/lib/notifications'

// GET - جلب التنبيهات المتقدمة
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') // 'pending', 'stats', 'user', 'templates'
    const userId = searchParams.get('user_id')
    const userType = searchParams.get('user_type') as 'client' | 'employee'
    const limit = parseInt(searchParams.get('limit') || '50')

    switch (type) {
      case 'pending':
        const pendingNotifications = await getPendingNotifications(limit)
        return NextResponse.json({
          success: true,
          data: pendingNotifications
        })

      case 'stats':
        const days = parseInt(searchParams.get('days') || '30')
        const stats = await getNotificationStats(days)
        return NextResponse.json({
          success: true,
          data: stats
        })

      case 'user':
        if (!userId || !userType) {
          return NextResponse.json(
            { success: false, error: 'معرف المستخدم ونوعه مطلوبان' },
            { status: 400 }
          )
        }
        const userNotifications = await getUserUpcomingNotifications(
          parseInt(userId), 
          userType, 
          limit
        )
        return NextResponse.json({
          success: true,
          data: userNotifications
        })

      case 'templates':
        const templatesResult = await query(`
          SELECT * FROM notification_templates 
          WHERE is_active = true 
          ORDER BY notification_type, name
        `)
        return NextResponse.json({
          success: true,
          data: templatesResult.rows
        })

      case 'upcoming_hearings':
        const upcomingHearingsResult = await query(`
          SELECT 
            cm.*,
            i.client_name,
            i.case_number,
            i.title as case_title,
            EXTRACT(DAYS FROM (cm.hearing_date - CURRENT_DATE)) as days_until_hearing
          FROM case_movements cm
          LEFT JOIN issues i ON cm.case_id = i.id
          WHERE cm.hearing_date >= CURRENT_DATE 
            AND cm.movement_type = 'hearing_scheduled'
            AND cm.status = 'active'
          ORDER BY cm.hearing_date ASC, cm.hearing_time ASC
          LIMIT $1
        `, [limit])
        return NextResponse.json({
          success: true,
          data: upcomingHearingsResult.rows
        })

      case 'inactive_cases':
        const daysSinceLastMovement = parseInt(searchParams.get('days_inactive') || '30')
        const inactiveCasesResult = await query(`
          SELECT 
            i.id,
            i.case_number,
            i.title,
            i.client_name,
            i.status,
            MAX(cm.created_at) as last_movement_date,
            EXTRACT(DAYS FROM (CURRENT_DATE - MAX(cm.created_at)::date)) as days_since_last_movement
          FROM issues i
          LEFT JOIN case_movements cm ON i.id = cm.case_id
          WHERE i.status NOT IN ('closed', 'completed', 'cancelled')
          GROUP BY i.id, i.case_number, i.title, i.client_name, i.status
          HAVING MAX(cm.created_at) < CURRENT_DATE - INTERVAL '${daysSinceLastMovement} days'
            OR MAX(cm.created_at) IS NULL
          ORDER BY days_since_last_movement DESC NULLS FIRST
          LIMIT $1
        `, [limit])
        return NextResponse.json({
          success: true,
          data: inactiveCasesResult.rows
        })

      default:
        // جلب التنبيهات العامة
        const caseId = searchParams.get('case_id')
        const status = searchParams.get('status')
        const priority = searchParams.get('priority')

        let whereConditions = []
        let queryParams: any[] = []
        let paramIndex = 1

        if (caseId) {
          whereConditions.push(`sn.case_id = $${paramIndex}`)
          queryParams.push(parseInt(caseId))
          paramIndex++
        }

        if (status) {
          whereConditions.push(`sn.status = $${paramIndex}`)
          queryParams.push(status)
          paramIndex++
        }

        if (priority) {
          whereConditions.push(`sn.priority = $${paramIndex}`)
          queryParams.push(priority)
          paramIndex++
        }

        const whereClause = whereConditions.length > 0 
          ? `WHERE ${whereConditions.join(' AND ')}` 
          : ''

        const notificationsQuery = `
          SELECT 
            sn.*,
            nt.name as template_name,
            nt.notification_type,
            i.case_number,
            i.title as case_title
          FROM scheduled_notifications sn
          LEFT JOIN notification_templates nt ON sn.template_id = nt.id
          LEFT JOIN issues i ON sn.case_id = i.id
          ${whereClause}
          ORDER BY sn.created_at DESC
          LIMIT $${paramIndex}
        `

        queryParams.push(limit)
        const result = await query(notificationsQuery, queryParams)

        return NextResponse.json({
          success: true,
          data: result.rows
        })
    }

  } catch (error) {
    console.error('خطأ في جلب التنبيهات المتقدمة:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب التنبيهات المتقدمة' },
      { status: 500 }
    )
  }
}

// POST - جدولة تنبيه جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      case_id,
      recipient_id,
      recipient_type,
      template_name,
      scheduled_for,
      variables,
      priority,
      channels
    } = body

    // التحقق من البيانات المطلوبة
    if (!case_id || !recipient_id || !recipient_type || !template_name || !scheduled_for) {
      return NextResponse.json(
        { success: false, error: 'البيانات المطلوبة مفقودة' },
        { status: 400 }
      )
    }

    // جدولة التنبيه
    const notificationId = await scheduleNotification({
      case_id: parseInt(case_id),
      recipient_id: parseInt(recipient_id),
      recipient_type,
      template_name,
      scheduled_for: new Date(scheduled_for),
      variables,
      priority,
      channels
    })

    if (notificationId) {
      return NextResponse.json({
        success: true,
        data: { notification_id: notificationId },
        message: 'تم جدولة التنبيه بنجاح'
      })
    } else {
      return NextResponse.json(
        { success: false, error: 'فشل في جدولة التنبيه' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('خطأ في جدولة التنبيه:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جدولة التنبيه' },
      { status: 500 }
    )
  }
}

// PUT - تحديث تنبيه
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, status, scheduled_for, priority, notes } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف التنبيه مطلوب' },
        { status: 400 }
      )
    }

    const updateFields = []
    const queryParams = []
    let paramIndex = 1

    if (status) {
      updateFields.push(`status = $${paramIndex}`)
      queryParams.push(status)
      paramIndex++
    }

    if (scheduled_for) {
      updateFields.push(`scheduled_for = $${paramIndex}`)
      queryParams.push(new Date(scheduled_for))
      paramIndex++
    }

    if (priority) {
      updateFields.push(`priority = $${paramIndex}`)
      queryParams.push(priority)
      paramIndex++
    }

    if (notes !== undefined) {
      updateFields.push(`metadata = COALESCE(metadata, '{}') || $${paramIndex}`)
      queryParams.push(JSON.stringify({ notes }))
      paramIndex++
    }

    if (updateFields.length === 0) {
      return NextResponse.json(
        { success: false, error: 'لا توجد بيانات للتحديث' },
        { status: 400 }
      )
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP')
    queryParams.push(id)

    await query(`
      UPDATE scheduled_notifications 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex}
    `, queryParams)

    return NextResponse.json({
      success: true,
      message: 'تم تحديث التنبيه بنجاح'
    })

  } catch (error) {
    console.error('خطأ في تحديث التنبيه:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث التنبيه' },
      { status: 500 }
    )
  }
}

// DELETE - إلغاء تنبيه
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف التنبيه مطلوب' },
        { status: 400 }
      )
    }

    // تحديث الحالة إلى ملغي بدلاً من الحذف
    await query(`
      UPDATE scheduled_notifications 
      SET status = 'cancelled', updated_at = CURRENT_TIMESTAMP
      WHERE id = $1 AND status = 'pending'
    `, [id])

    return NextResponse.json({
      success: true,
      message: 'تم إلغاء التنبيه بنجاح'
    })

  } catch (error) {
    console.error('خطأ في إلغاء التنبيه:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إلغاء التنبيه' },
      { status: 500 }
    )
  }
}
