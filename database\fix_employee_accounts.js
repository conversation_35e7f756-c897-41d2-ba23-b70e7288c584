/**
 * سكريبت لإصلاح حسابات الموظفين - استخدام حساب واحد مشترك
 * جميع الموظفين سيكون لهم نفس رقم الحساب الأب
 */

const { Client } = require('pg');

// إعدادات قاعدة البيانات mohammidev
const dbConfig = {
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'mohammidev'
};

async function fixEmployeeAccounts() {
  const client = new Client(dbConfig);

  try {
    await client.connect();
    console.log('✅ متصل بقاعدة البيانات mohammidev');

    // 1. إنشاء حساب واحد مشترك لجميع الموظفين
    console.log('\n💰 إنشاء حساب مشترك للموظفين...');
    
    const sharedAccountId = 5101; // رواتب المحامين
    const sharedAccount = {
      id: sharedAccountId,
      code: '5101',
      name: 'رواتب وأجور الموظفين',
      type: 'مصاريف'
    };

    try {
      await client.query(`
        INSERT INTO chart_of_accounts (id, account_code, account_name, account_type, account_level, is_active, allow_transactions)
        VALUES ($1, $2, $3, $4, 4, true, true)
        ON CONFLICT (id) DO UPDATE SET
          account_code = EXCLUDED.account_code,
          account_name = EXCLUDED.account_name,
          account_type = EXCLUDED.account_type,
          account_level = EXCLUDED.account_level
      `, [sharedAccount.id, sharedAccount.code, sharedAccount.name, sharedAccount.type]);
      
      console.log(`✅ تم إنشاء/تحديث الحساب المشترك: ${sharedAccount.code} - ${sharedAccount.name}`);
    } catch (error) {
      console.log(`⚠️ خطأ في إنشاء الحساب المشترك: ${error.message}`);
    }

    // 2. فحص الموظفين الموجودين
    console.log('\n👥 فحص الموظفين الموجودين...');
    const employees = await client.query(`
      SELECT id, name, employee_number, account_id, position
      FROM employees
      ORDER BY id
    `);

    console.log(`📊 عدد الموظفين: ${employees.rows.length}`);

    // 3. تحديث جميع الموظفين ليستخدموا الحساب المشترك
    console.log('\n🔄 تحديث حسابات الموظفين...');
    
    let updatedCount = 0;
    let employeeNumberUpdates = 0;

    for (const employee of employees.rows) {
      let updates = [];
      let values = [employee.id];
      let paramIndex = 2;

      // تحديث رقم الموظف إذا كان مفقوداً
      if (!employee.employee_number) {
        const positionCode = getPositionCode(employee.position);
        const employeeNumber = `${positionCode}${String(employee.id).padStart(3, '0')}`;
        updates.push(`employee_number = $${paramIndex}`);
        values.push(employeeNumber);
        paramIndex++;
        employeeNumberUpdates++;
        console.log(`   📝 رقم موظف جديد: ${employee.name} → ${employeeNumber}`);
      }

      // تحديث رقم الحساب ليكون الحساب المشترك
      if (employee.account_id !== sharedAccountId) {
        updates.push(`account_id = $${paramIndex}`);
        values.push(sharedAccountId);
        paramIndex++;
        console.log(`   💰 تحديث حساب: ${employee.name} → ${sharedAccountId}`);
      }

      // تنفيذ التحديث إذا كان هناك تغييرات
      if (updates.length > 0) {
        const updateQuery = `UPDATE employees SET ${updates.join(', ')} WHERE id = $1`;
        await client.query(updateQuery, values);
        updatedCount++;
        console.log(`   ✅ تم تحديث بيانات: ${employee.name}`);
      } else {
        console.log(`   ℹ️ لا يحتاج تحديث: ${employee.name}`);
      }
    }

    // 4. التحقق النهائي
    console.log('\n🔍 التحقق النهائي من التحديثات...');
    const finalCheck = await client.query(`
      SELECT 
        e.id,
        e.name,
        e.employee_number,
        e.account_id,
        c.account_name,
        c.account_code
      FROM employees e
      LEFT JOIN chart_of_accounts c ON e.account_id = c.id
      ORDER BY e.id
    `);

    console.log('\n📋 بيانات الموظفين النهائية:');
    finalCheck.rows.forEach(emp => {
      const accountInfo = emp.account_id ? `${emp.account_id} (${emp.account_code}: ${emp.account_name})` : 'غير محدد';
      console.log(`   - ${emp.name}: رقم=${emp.employee_number || 'غير محدد'}, حساب=${accountInfo}`);
    });

    // 5. إحصائيات نهائية
    const stats = await client.query(`
      SELECT 
        COUNT(*) as total_employees,
        COUNT(employee_number) as employees_with_number,
        COUNT(account_id) as employees_with_account,
        COUNT(CASE WHEN account_id = $1 THEN 1 END) as employees_with_shared_account,
        COUNT(CASE WHEN c.id IS NOT NULL THEN 1 END) as valid_accounts
      FROM employees e
      LEFT JOIN chart_of_accounts c ON e.account_id = c.id
    `, [sharedAccountId]);

    const stat = stats.rows[0];
    console.log('\n📊 إحصائيات نهائية:');
    console.log(`   - إجمالي الموظفين: ${stat.total_employees}`);
    console.log(`   - موظفين لديهم رقم موظف: ${stat.employees_with_number}`);
    console.log(`   - موظفين لديهم رقم حساب: ${stat.employees_with_account}`);
    console.log(`   - موظفين يستخدمون الحساب المشترك (${sharedAccountId}): ${stat.employees_with_shared_account}`);
    console.log(`   - حسابات صحيحة في دليل الحسابات: ${stat.valid_accounts}`);

    console.log('\n📈 ملخص التحديثات:');
    console.log(`   - موظفين تم تحديث بياناتهم: ${updatedCount}`);
    console.log(`   - أرقام موظفين تم إنشاؤها: ${employeeNumberUpdates}`);

    console.log('\n🎉 تم إصلاح حسابات الموظفين بنجاح!');
    console.log(`💡 جميع الموظفين الآن يستخدمون الحساب المشترك: ${sharedAccountId} (${sharedAccount.name})`);

  } catch (error) {
    console.error('❌ خطأ في إصلاح حسابات الموظفين:', error.message);
    throw error;
  } finally {
    await client.end();
  }
}

/**
 * دالة لإنشاء رمز المنصب
 */
function getPositionCode(position) {
  const positionCodes = {
    'محامي رسمي': 'LAW',
    'محامي متدرب': 'TRN',
    'مساعد قانوني': 'AST',
    'سكرتير': 'SEC',
    'محاسب': 'ACC',
    'مدير': 'MGR',
    'موظف إداري': 'ADM',
    'مستشار قانوني': 'CON',
    'رئيس قسم': 'HOD',
    'مدير عام': 'GM'
  };

  return positionCodes[position] || 'EMP';
}

/**
 * دالة لإزالة القيد المرجعي مؤقتاً (إذا لزم الأمر)
 */
async function removeConstraintTemporarily() {
  const client = new Client(dbConfig);
  
  try {
    await client.connect();
    
    // إزالة القيد المرجعي مؤقتاً
    await client.query('ALTER TABLE employees DROP CONSTRAINT IF EXISTS employees_account_id_fkey');
    console.log('⚠️ تم إزالة القيد المرجعي مؤقتاً');
    
    return client;
  } catch (error) {
    console.error('خطأ في إزالة القيد:', error.message);
    await client.end();
    throw error;
  }
}

/**
 * دالة لإعادة إنشاء القيد المرجعي
 */
async function recreateConstraint(client) {
  try {
    // إعادة إنشاء القيد المرجعي
    await client.query(`
      ALTER TABLE employees 
      ADD CONSTRAINT employees_account_id_fkey 
      FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id)
    `);
    console.log('✅ تم إعادة إنشاء القيد المرجعي');
  } catch (error) {
    console.error('خطأ في إعادة إنشاء القيد:', error.message);
  }
}

// تشغيل السكريبت
if (require.main === module) {
  fixEmployeeAccounts().catch(console.error);
}

module.exports = { fixEmployeeAccounts, getPositionCode };
