import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب الإعلانات العامة للموقع الرئيسي
export async function GET() {
  try {
    const result = await query(`
      SELECT id, title, content, type, is_active, created_date
      FROM public_announcements 
      WHERE is_active = true 
      ORDER BY 
        CASE 
          WHEN type = 'public_1' THEN 1
          WHEN type = 'public_2' THEN 2
          ELSE 3
        END,
        created_date DESC
      LIMIT 2
    `)

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching public announcements:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الإعلانات' },
      { status: 500 }
    )
  }
}

// POST - إضافة إعلان عام جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { title, content, type } = body

    if (!title || !content || !type) {
      return NextResponse.json(
        { success: false, error: 'جميع الحقول مطلوبة' },
        { status: 400 }
      )
    }

    if (!['public_1', 'public_2'].includes(type)) {
      return NextResponse.json(
        { success: false, error: 'نوع الإعلان غير صحيح' },
        { status: 400 }
      )
    }

    // إلغاء تفعيل الإعلان السابق من نفس النوع
    await query(`
      UPDATE public_announcements 
      SET is_active = false 
      WHERE type = $1
    `, [type])

    // إضافة الإعلان الجديد
    const result = await query(`
      INSERT INTO public_announcements (title, content, type, is_active, created_date)
      VALUES ($1, $2, $3, true, NOW())
      RETURNING *
    `, [title, content, type])

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم إضافة الإعلان بنجاح'
    })
  } catch (error) {
    console.error('Error creating public announcement:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة الإعلان' },
      { status: 500 }
    )
  }
}