'use client'

import { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'

interface Beneficiary {
  id: number
  name: string
  type: 'client' | 'employee' | 'supplier'
  account_id?: number
  account_code?: string
}

interface WorkingBeneficiarySelectProps {
  value?: string
  onChange: (value: string, beneficiary?: Beneficiary) => void
  label?: string
  placeholder?: string
  required?: boolean
  className?: string
}

export default function WorkingBeneficiarySelect({
  value,
  onChange,
  label,
  placeholder = "اختر المستفيد...",
  required = false,
  className = ""
}: WorkingBeneficiarySelectProps) {
  const [beneficiaries, setBeneficiaries] = useState<Beneficiary[]>([])
  const [loading, setLoading] = useState(false)

  const fetchBeneficiaries = async () => {
    try {
      setLoading(true)

      // جلب العملاء
      const clientsResponse = await fetch('/api/clients')
      const clientsData = await clientsResponse.json()

      const clients = clientsData.success ? clientsData.clients.map((client: any) => ({
        id: client.id,
        name: client.name,
        type: 'client' as const,
        account_id: client.account_id,
        account_code: client.account_code
      })) : []

      // جلب الموظفين
      const employeesResponse = await fetch('/api/employees')
      const employeesData = await employeesResponse.json()

      const employees = employeesData.success ? employeesData.employees.map((employee: any) => ({
        id: employee.id,
        name: employee.name,
        type: 'employee' as const,
        account_id: employee.account_id,
        account_code: employee.account_code
      })) : []

      // جلب الموردين
      const suppliersResponse = await fetch('/api/suppliers')
      const suppliersData = await suppliersResponse.json()

      const suppliers = suppliersData.success ? suppliersData.suppliers.map((supplier: any) => ({
        id: supplier.id,
        name: supplier.name,
        type: 'supplier' as const,
        account_id: supplier.account_id,
        account_code: supplier.account_code
      })) : []

      // دمج جميع المستفيدين
      const allBeneficiaries = [...clients, ...employees, ...suppliers]

      setBeneficiaries(allBeneficiaries)

    } catch (error) {
      console.error('❌ WorkingBeneficiarySelect: خطأ في جلب المستفيدين:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchBeneficiaries()
  }, [])

  // العثور على المستفيد المحدد
  const selectedBeneficiary = beneficiaries.find(b => 
    `${b.type}_${b.id}` === value
  )

  const handleSelect = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedValue = event.target.value
    const beneficiary = beneficiaries.find(b => 
      `${b.type}_${b.id}` === selectedValue
    )
    onChange(selectedValue, beneficiary)
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'client': return '👤 عميل'
      case 'employee': return '👨‍💼 موظف'
      case 'supplier': return '🏢 مورد'
      default: return type
    }
  }

  return (
    <div className={className}>
      {label && (
        <Label className="text-sm font-medium text-gray-700 mb-2 block">
          {label}
          {required && <span className="text-red-500 mr-1">*</span>}
        </Label>
      )}

      <select
        value={value || ''}
        onChange={handleSelect}
        disabled={loading}
        className="w-full p-3 border border-gray-300 rounded-md bg-gray-50 focus:border-blue-500 focus:bg-white transition-colors text-sm"
        required={required}
      >
        <option value="">
          {loading ? "جاري التحميل..." : placeholder}
        </option>

        {beneficiaries.length > 0 && (
          <>
            <optgroup label={`👤 العملاء (${beneficiaries.filter(b => b.type === 'client').length})`}>
              {beneficiaries
                .filter(b => b.type === 'client')
                .map((beneficiary) => (
                  <option 
                    key={`${beneficiary.type}_${beneficiary.id}`} 
                    value={`${beneficiary.type}_${beneficiary.id}`}
                  >
                    {beneficiary.name} - {beneficiary.account_code}
                  </option>
                ))
              }
            </optgroup>

            <optgroup label={`👨‍💼 الموظفين (${beneficiaries.filter(b => b.type === 'employee').length})`}>
              {beneficiaries
                .filter(b => b.type === 'employee')
                .map((beneficiary) => (
                  <option
                    key={`${beneficiary.type}_${beneficiary.id}`}
                    value={`${beneficiary.type}_${beneficiary.id}`}
                  >
                    {beneficiary.name} - {beneficiary.account_code}
                  </option>
                ))
              }
            </optgroup>

            <optgroup label={`🏢 الموردين (${beneficiaries.filter(b => b.type === 'supplier').length})`}>
              {beneficiaries
                .filter(b => b.type === 'supplier')
                .map((beneficiary) => (
                  <option
                    key={`${beneficiary.type}_${beneficiary.id}`}
                    value={`${beneficiary.type}_${beneficiary.id}`}
                  >
                    {beneficiary.name} - {beneficiary.account_code}
                  </option>
                ))
              }
            </optgroup>
          </>
        )}

        {!loading && beneficiaries.length === 0 && (
          <option value="" disabled>
            لا توجد مستفيدين
          </option>
        )}
      </select>

      {/* عرض المستفيد المحدد */}
      {selectedBeneficiary && (
        <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-md text-sm">
          <span className="font-medium text-blue-700">
            المحدد: {getTypeLabel(selectedBeneficiary.type)} - {selectedBeneficiary.name}
          </span>
          {selectedBeneficiary.account_code && (
            <span className="text-blue-600 mr-2">
              (حساب: {selectedBeneficiary.account_code})
            </span>
          )}
        </div>
      )}
    </div>
  )
}
