'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Loader2, Shield, Users, UserPlus, Settings, CheckCircle, XCircle } from 'lucide-react'
import { useAuth } from '@/hooks/useAuth'

interface Role {
  role_name: string
  display_name: string
  description: string
  permissions: string[]
  permissions_count: number
}

interface UserRole {
  role_name: string
  display_name: string
  assigned_date: string
  is_active: boolean
}

interface UserRolesManagerProps {
  isOpen: boolean
  onClose: () => void
  userId: number
  userName: string
  onSuccess?: () => void
}

export function UserRolesManager({ 
  isOpen, 
  onClose, 
  userId, 
  userName, 
  onSuccess 
}: UserRolesManagerProps) {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [availableRoles, setAvailableRoles] = useState<Role[]>([])
  const [userRoles, setUserRoles] = useState<UserRole[]>([])
  const [selectedRoles, setSelectedRoles] = useState<string[]>([])
  const [message, setMessage] = useState('')

  useEffect(() => {
    if (isOpen) {
      fetchData()
    }
  }, [isOpen, userId])

  const fetchData = async () => {
    setLoading(true)
    try {
      // جلب جميع الأدوار المتاحة
      const rolesResponse = await fetch('/api/user-roles')
      const rolesResult = await rolesResponse.json()

      if (rolesResult.success) {
        setAvailableRoles(rolesResult.data)
      }

      // جلب أدوار المستخدم الحالية
      const userRolesResponse = await fetch(`/api/user-roles/assignments?userId=${userId}`)
      const userRolesResult = await userRolesResponse.json()

      if (userRolesResult.success) {
        setUserRoles(userRolesResult.data)
        setSelectedRoles(userRolesResult.data.map((r: UserRole) => r.role_name))
      }

    } catch (error) {
      console.error('خطأ في جلب البيانات:', error)
      setMessage('فشل في جلب البيانات')
    } finally {
      setLoading(false)
    }
  }

  const handleRoleChange = (roleName: string, checked: boolean) => {
    if (checked) {
      setSelectedRoles(prev => [...prev, roleName])
    } else {
      setSelectedRoles(prev => prev.filter(r => r !== roleName))
    }
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      const response = await fetch('/api/user-roles/assignments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId,
          roles: selectedRoles,
          assignedBy: user?.id
        })
      })

      const result = await response.json()

      if (result.success) {
        setMessage('تم تحديث الأدوار بنجاح')
        onSuccess?.()
        setTimeout(() => {
          onClose()
          setMessage('')
        }, 1500)
      } else {
        setMessage('فشل في تحديث الأدوار: ' + result.error)
      }

    } catch (error) {
      console.error('خطأ في حفظ الأدوار:', error)
      setMessage('حدث خطأ أثناء حفظ الأدوار')
    } finally {
      setSaving(false)
    }
  }

  const getRolesByCategory = () => {
    const categories = {
      'أساسي': availableRoles.filter(r => ['admin', 'manager', 'viewer'].includes(r.role_name)),
      'قانوني': availableRoles.filter(r => ['lawyer', 'lawyer_manager'].includes(r.role_name)),
      'إداري': availableRoles.filter(r => ['secretary', 'senior_secretary'].includes(r.role_name)),
      'مالي': availableRoles.filter(r => ['accountant', 'financial_manager'].includes(r.role_name)),
      'مختلط': availableRoles.filter(r => ['secretary_accountant'].includes(r.role_name))
    }
    return categories
  }

  const getSelectedPermissionsCount = () => {
    const selectedRoleObjects = availableRoles.filter(r => selectedRoles.includes(r.role_name))
    const allPermissions = new Set()
    selectedRoleObjects.forEach(role => {
      role.permissions.forEach(perm => allPermissions.add(perm))
    })
    return allPermissions.size
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center text-xl">
            <Users className="h-6 w-6 mr-3 text-blue-600" />
            إدارة أدوار المستخدم: {userName}
          </DialogTitle>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            <span className="mr-3">جاري تحميل الأدوار...</span>
          </div>
        ) : (
          <Tabs defaultValue="roles" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="roles" className="flex items-center">
                <Users className="h-4 w-4 mr-2" />
                إدارة الأدوار
              </TabsTrigger>
              <TabsTrigger value="permissions" className="flex items-center">
                <Shield className="h-4 w-4 mr-2" />
                الصلاحيات التفصيلية
              </TabsTrigger>
            </TabsList>

            <TabsContent value="roles" className="space-y-6">
              {/* إحصائيات */}
              <div className="flex items-center justify-between bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <Badge variant="outline" className="text-blue-700">
                    <Users className="h-4 w-4 mr-1" />
                    {selectedRoles.length} أدوار محددة
                  </Badge>
                  <Badge variant="outline" className="text-green-700">
                    <Shield className="h-4 w-4 mr-1" />
                    {getSelectedPermissionsCount()} صلاحية مجمعة
                  </Badge>
                </div>
                
                <div className="flex space-x-2 space-x-reverse">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedRoles(availableRoles.map(r => r.role_name))}
                    className="text-green-700 border-green-300 hover:bg-green-50"
                  >
                    <CheckCircle className="h-4 w-4 mr-1" />
                    تحديد الكل
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedRoles([])}
                    className="text-red-700 border-red-300 hover:bg-red-50"
                  >
                    <XCircle className="h-4 w-4 mr-1" />
                    إلغاء الكل
                  </Button>
                </div>
              </div>

              {/* الأدوار حسب الفئة */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Object.entries(getRolesByCategory()).map(([category, roles]) => (
                  <Card key={category} className="border-2">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg text-gray-800">
                        {category}
                      </CardTitle>
                      <div className="text-sm text-gray-600">
                        {roles.filter(r => selectedRoles.includes(r.role_name)).length} من {roles.length} محددة
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      {roles.map((role) => (
                        <div key={role.role_name} className="space-y-2">
                          <div className="flex items-start space-x-3 space-x-reverse">
                            <Checkbox
                              id={role.role_name}
                              checked={selectedRoles.includes(role.role_name)}
                              onCheckedChange={(checked) => 
                                handleRoleChange(role.role_name, checked as boolean)
                              }
                              className="mt-1"
                            />
                            <div className="flex-1">
                              <Label 
                                htmlFor={role.role_name}
                                className="text-sm font-medium cursor-pointer"
                              >
                                {role.display_name}
                              </Label>
                              <div className="flex items-center space-x-2 space-x-reverse mt-1">
                                <Badge variant="secondary" className="text-xs">
                                  {role.permissions_count} صلاحية
                                </Badge>
                              </div>
                              {role.description && (
                                <p className="text-xs text-gray-500 mt-1">
                                  {role.description}
                                </p>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="permissions" className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="font-medium text-blue-800 mb-2">الصلاحيات المجمعة من الأدوار المحددة</h3>
                <p className="text-sm text-blue-600">
                  هذه هي الصلاحيات التي سيحصل عليها المستخدم من جميع الأدوار المحددة
                </p>
              </div>
              
              {selectedRoles.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {selectedRoles.map(roleName => {
                    const role = availableRoles.find(r => r.role_name === roleName)
                    if (!role) return null
                    
                    return (
                      <Card key={roleName} className="border border-blue-200">
                        <CardHeader className="pb-2">
                          <CardTitle className="text-base text-blue-800">
                            {role.display_name}
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-1">
                            {role.permissions.map(permission => (
                              <div key={permission} className="text-xs bg-blue-50 px-2 py-1 rounded">
                                {permission}
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    )
                  })}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  لم يتم تحديد أي أدوار بعد
                </div>
              )}
            </TabsContent>
          </Tabs>
        )}

        {/* رسالة النتيجة */}
        {message && (
          <div className={`p-3 rounded-lg text-center ${
            message.includes('نجاح') 
              ? 'bg-green-50 text-green-800 border border-green-200' 
              : 'bg-red-50 text-red-800 border border-red-200'
          }`}>
            {message}
          </div>
        )}

        {/* أزرار الحفظ */}
        <div className="flex justify-end space-x-3 space-x-reverse pt-4 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={saving}
          >
            إلغاء
          </Button>
          <Button
            type="button"
            onClick={handleSave}
            disabled={saving}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {saving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                جاري الحفظ...
              </>
            ) : (
              <>
                <Users className="h-4 w-4 mr-2" />
                حفظ الأدوار
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
