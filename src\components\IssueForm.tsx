'use client'

import { useState, useEffect } from 'react'
import { SimpleErrorBoundary } from './ErrorBoundary'

interface Client {
  id: number
  name: string
  phone?: string
}

interface IssueType {
  id: number
  name: string
}

interface Court {
  id: number
  name: string
  type?: string
}

interface Currency {
  id: number
  currency_code: string
  currency_name: string
  symbol: string
  exchange_rate: number
  is_base_currency: boolean
}

interface IssueFormProps {
  onSubmit: (data: any) => void
  onCancel: () => void
  initialData?: any
  isEditing?: boolean
}

export default function IssueForm({ onSubmit, onCancel, initialData, isEditing = false }: IssueFormProps) {
  const [formData, setFormData] = useState({
    case_number: '',
    title: '',
    description: '',
    client_id: '',
    issue_type_id: '',
    court_id: '',
    status: 'new',
    case_amount: '',
    currency_id: '1', // الريال اليمني كافتراضي
    start_date: new Date().toISOString().split('T')[0], // تاريخ اليوم كافتراضي
    notes: '',
    contract_method: 'بالجلسة',
    contract_date: new Date().toISOString().split('T')[0]
  })

  const [clients, setClients] = useState<Client[]>([])
  const [issueTypes, setIssueTypes] = useState<IssueType[]>([])
  const [courts, setCourts] = useState<Court[]>([])
  const [currencies, setCurrencies] = useState<Currency[]>([])
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [amountYer, setAmountYer] = useState<number>(0)

  // تحميل البيانات المرجعية
  useEffect(() => {
    loadReferenceData()

    // تعبئة النموذج بالبيانات الأولية في حالة التعديل
    if (initialData) {
      setFormData({
        case_number: initialData.case_number || '',
        title: initialData.title || '',
        description: initialData.description || '',
        client_id: initialData.client_id?.toString() || '',
        issue_type_id: initialData.issue_type_id?.toString() || '',
        court_id: initialData.court_id?.toString() || '',
        status: initialData.status || 'new',
        case_amount: initialData.case_amount?.toString() || '',
        currency_id: initialData.currency_id?.toString() || '1',
        start_date: initialData.start_date || new Date().toISOString().split('T')[0],
        notes: initialData.notes || '',
        contract_method: initialData.contract_method || 'بالجلسة',
        contract_date: initialData.contract_date || new Date().toISOString().split('T')[0]
      })
    }
  }, [initialData])

  const loadReferenceData = async () => {
    try {
      // تحميل العملاء
      const clientsResponse = await fetch('/api/clients')
      if (clientsResponse.ok) {
        const clientsData = await clientsResponse.json()
        setClients(clientsData.data || [])
      }

      // تحميل أنواع القضايا
      const typesResponse = await fetch('/api/issue-types')
      if (typesResponse.ok) {
        const typesData = await typesResponse.json()
        setIssueTypes(typesData.data || [])
      }

      // تحميل المحاكم
      const courtsResponse = await fetch('/api/courts')
      if (courtsResponse.ok) {
        const courtsData = await courtsResponse.json()
        setCourts(courtsData.data || [])
      }

      // تحميل العملات
      const currenciesResponse = await fetch('/api/currencies')
      if (currenciesResponse.ok) {
        const currenciesData = await currenciesResponse.json()
        setCurrencies(currenciesData.data || [])
      }
    } catch (error) {
      console.error('Error loading reference data:', error)
    }
  }

  // حساب المبلغ بالريال اليمني
  const calculateAmountYer = (amount: string, currencyId: string) => {
    const numAmount = parseFloat(amount) || 0
    const currency = currencies.find(c => c.id.toString() === currencyId)

    if (!currency) return numAmount

    if (currency.is_base_currency) {
      return numAmount
    }

    return numAmount * currency.exchange_rate
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    const newFormData = {
      ...formData,
      [name]: value
    }

    setFormData(newFormData)

    // حساب المبلغ بالريال اليمني عند تغيير المبلغ أو العملة
    if (name === 'case_amount' || name === 'currency_id') {
      const amount = name === 'case_amount' ? value : newFormData.case_amount
      const currencyId = name === 'currency_id' ? value : newFormData.currency_id
      setAmountYer(calculateAmountYer(amount, currencyId))
    }

    // إزالة رسالة الخطأ عند التعديل
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  // تحديث المبلغ بالريال عند تحميل العملات
  useEffect(() => {
    if (currencies.length > 0) {
      setAmountYer(calculateAmountYer(formData.case_amount, formData.currency_id))
    }
  }, [currencies, formData.case_amount, formData.currency_id])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.case_number.trim()) {
      newErrors.case_number = 'رقم القضية مطلوب'
    }

    if (!formData.title.trim()) {
      newErrors.title = 'عنوان القضية مطلوب'
    }

    if (!formData.client_id) {
      newErrors.client_id = 'العميل مطلوب'
    }

    if (!formData.issue_type_id) {
      newErrors.issue_type_id = 'نوع القضية مطلوب'
    }

    if (!formData.start_date) {
      newErrors.start_date = 'تاريخ بداية القضية مطلوب'
    }

    if (formData.case_amount && isNaN(parseFloat(formData.case_amount))) {
      newErrors.case_amount = 'قيمة القضية يجب أن تكون رقماً صحيحاً'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setLoading(true)

    try {
      const submitData = {
        ...formData,
        client_id: parseInt(formData.client_id),
        issue_type_id: parseInt(formData.issue_type_id),
        court_id: formData.court_id ? parseInt(formData.court_id) : null,
        case_amount: formData.case_amount ? parseFloat(formData.case_amount) : 0
      }

      await onSubmit(submitData)
    } catch (error) {
      console.error('Error submitting form:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <SimpleErrorBoundary>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          <div className="p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              {isEditing ? 'تعديل القضية' : 'إضافة قضية جديدة'}
            </h2>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* الصف الأول: رقم القضية والعنوان */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    رقم القضية *
                  </label>
                  <input
                    type="text"
                    name="case_number"
                    value={formData.case_number}
                    onChange={handleInputChange}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.case_number ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="مثال: CASE-2024-001"
                  />
                  {errors.case_number && (
                    <p className="mt-1 text-sm text-red-600">{errors.case_number}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    عنوان القضية *
                  </label>
                  <input
                    type="text"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.title ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="عنوان مختصر للقضية"
                  />
                  {errors.title && (
                    <p className="mt-1 text-sm text-red-600">{errors.title}</p>
                  )}
                </div>
              </div>

              {/* الصف الثاني: العميل ونوع القضية */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    العميل *
                  </label>
                  <select
                    name="client_id"
                    value={formData.client_id}
                    onChange={handleInputChange}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.client_id ? 'border-red-500' : 'border-gray-300'
                    }`}
                  >
                    <option value="">اختر العميل</option>
                    {clients.map(client => (
                      <option key={client.id} value={client.id}>
                        {client.name} {client.phone && `(${client.phone})`}
                      </option>
                    ))}
                  </select>
                  {errors.client_id && (
                    <p className="mt-1 text-sm text-red-600">{errors.client_id}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    نوع القضية *
                  </label>
                  <select
                    name="issue_type_id"
                    value={formData.issue_type_id}
                    onChange={handleInputChange}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.issue_type_id ? 'border-red-500' : 'border-gray-300'
                    }`}
                  >
                    <option value="">اختر نوع القضية</option>
                    {issueTypes.map(type => (
                      <option key={type.id} value={type.id}>
                        {type.name}
                      </option>
                    ))}
                  </select>
                  {errors.issue_type_id && (
                    <p className="mt-1 text-sm text-red-600">{errors.issue_type_id}</p>
                  )}
                </div>
              </div>

              {/* الصف الثالث: المحكمة والحالة */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    المحكمة
                  </label>
                  <select
                    name="court_id"
                    value={formData.court_id}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">اختر المحكمة (اختياري)</option>
                    {courts.map(court => (
                      <option key={court.id} value={court.id}>
                        {court.name} {court.type && `(${court.type})`}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    حالة القضية
                  </label>
                  <select
                    name="status"
                    value={formData.status}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="new">جديدة</option>
                    <option value="in_progress">قيد المتابعة</option>
                    <option value="completed">مكتملة</option>
                    <option value="closed">مغلقة</option>
                  </select>
                </div>
              </div>

              {/* الصف الرابع: تاريخ البداية والقيمة المالية */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    تاريخ بداية القضية *
                  </label>
                  <input
                    type="date"
                    name="start_date"
                    value={formData.start_date}
                    onChange={handleInputChange}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.start_date ? 'border-red-500' : 'border-gray-300'
                    }`}
                  />
                  {errors.start_date && (
                    <p className="mt-1 text-sm text-red-600">{errors.start_date}</p>
                  )}
                  <p className="mt-1 text-xs text-gray-500">
                    القيمة الافتراضية: تاريخ اليوم
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    العملة
                  </label>
                  <select
                    name="currency_id"
                    value={formData.currency_id}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {currencies.map(currency => (
                      <option key={currency.id} value={currency.id}>
                        {currency.currency_name} ({currency.symbol})
                        {currency.is_base_currency && ' - العملة الأساسية'}
                      </option>
                    ))}
                  </select>
                  <p className="mt-1 text-xs text-gray-500">
                    العملة الافتراضية: الريال اليمني
                  </p>
                </div>
              </div>

              {/* الصف الخامس: المبلغ والمبلغ بالريال */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    القيمة المالية للقضية
                  </label>
                  <div className="relative">
                    <input
                      type="number"
                      name="case_amount"
                      value={formData.case_amount}
                      onChange={handleInputChange}
                      min="0"
                      step="0.01"
                      className={`w-full px-3 py-2 pr-12 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        errors.case_amount ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="0.00"
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 text-sm">
                        {currencies.find(c => c.id.toString() === formData.currency_id)?.symbol || 'ر.ي'}
                      </span>
                    </div>
                  </div>
                  {errors.case_amount && (
                    <p className="mt-1 text-sm text-red-600">{errors.case_amount}</p>
                  )}
                  <p className="mt-1 text-xs text-gray-500">
                    اتركه فارغاً أو 0 إذا لم تكن هناك قيمة مالية
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    المبلغ بالريال اليمني
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      value={amountYer.toLocaleString('ar-YE', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                      })}
                      readOnly
                      className="w-full px-3 py-2 pr-12 border border-gray-300 rounded-md bg-gray-50 text-gray-700"
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 text-sm">ر.ي</span>
                    </div>
                  </div>
                  <p className="mt-1 text-xs text-gray-500">
                    يُحسب تلقائياً باستخدام سعر الصرف
                  </p>
                </div>
              </div>

              {/* وصف القضية */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  وصف القضية
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="وصف تفصيلي للقضية..."
                />
              </div>

              {/* الملاحظات */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  ملاحظات إضافية
                </label>
                <textarea
                  name="notes"
                  value={formData.notes}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="ملاحظات أو تعليقات إضافية..."
                />
              </div>

              {/* أزرار الإجراءات */}
              <div className="flex justify-end space-x-3 space-x-reverse pt-6 border-t">
                <button
                  type="button"
                  onClick={onCancel}
                  className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
                  disabled={loading}
                >
                  إلغاء
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {loading ? 'جاري الحفظ...' : (isEditing ? 'تحديث القضية' : 'إضافة القضية')}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </SimpleErrorBoundary>
  )
}
