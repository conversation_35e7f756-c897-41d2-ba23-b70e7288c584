import { NextResponse } from 'next/server'
import { query } from '@/lib/db'

export async function POST() {
  try {
    // إضافة مستخدمين تجريبيين
    const sampleUsers = [
      {
        username: 'admin',
        email: '<EMAIL>',
        password_hash: Buffer.from('admin123').toString('base64'),
        role: 'admin',
        status: 'active'
      },
      {
        username: 'manager1',
        email: '<EMAIL>',
        password_hash: Buffer.from('manager123').toString('base64'),
        role: 'manager',
        status: 'active'
      },
      {
        username: 'user1',
        email: '<EMAIL>',
        password_hash: Buffer.from('user123').toString('base64'),
        role: 'user',
        status: 'active'
      },
      {
        username: 'user2',
        email: '<EMAIL>',
        password_hash: Buffer.from('user123').toString('base64'),
        role: 'user',
        status: 'inactive'
      }
    ]

    for (const user of sampleUsers) {
      // التحقق من عدم وجود المستخدم مسبقاً
      const existingUser = await query(
        'SELECT id FROM users WHERE username = $1 OR email = $2',
        [user.username, user.email]
      )

      if (existingUser.rows.length === 0) {
        await query(`
          INSERT INTO users (username, email, password_hash, role, status)
          VALUES ($1, $2, $3, $4, $5)
        `, [user.username, user.email, user.password_hash, user.role, user.status])
      }
    }

    return NextResponse.json({
      success: true,
      message: 'تم إضافة المستخدمين التجريبيين بنجاح'
    })
  } catch (error) {
    console.error('Error seeding users:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة المستخدمين التجريبيين' },
      { status: 500 }
    )
  }
}
