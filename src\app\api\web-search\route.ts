import { NextRequest, NextResponse } from 'next/server'
export const runtime = 'nodejs'

// Supports either Bing Web Search API (preferred) or SerpAPI as fallback.
// Env vars:
//  - BING_SEARCH_KEY (required for Bing)
//  - BING_SEARCH_ENDPOINT (optional, default: https://api.bing.microsoft.com/v7.0/search)
//  - SERPAPI_KEY (required for SerpAPI)

async function searchWithBing(query: string, count = 5) {
  const endpoint = process.env.BING_SEARCH_ENDPOINT || 'https://api.bing.microsoft.com/v7.0/search'
  const url = `${endpoint}?q=${encodeURIComponent(query)}&mkt=ar-YE&count=${count}`
  const res = await fetch(url, {
    headers: { 'Ocp-Apim-Subscription-Key': process.env.BING_SEARCH_KEY as string },
    // 10s timeout via AbortController if needed (Next already has limits)
    cache: 'no-store',
  })
  if (!res.ok) throw new Error(`Bing search failed: ${res.status}`)
  const data = await res.json()
  const webPages = data.webPages?.value || []
  return webPages.map((item: any) => ({
    title: item.name,
    url: item.url,
    snippet: item.snippet || item.description || '',
    source: new URL(item.url).hostname,
  }))
}

async function searchWithSerpApi(query: string, count = 5) {
  const key = process.env.SERPAPI_KEY as string
  const url = `https://serpapi.com/search.json?engine=google&q=${encodeURIComponent(query)}&hl=ar&num=${count}&api_key=${encodeURIComponent(key)}`
  const res = await fetch(url, { cache: 'no-store' })
  if (!res.ok) throw new Error(`SerpAPI search failed: ${res.status}`)
  const data = await res.json()
  const organic = data.organic_results || []
  return organic.slice(0, count).map((item: any) => ({
    title: item.title,
    url: item.link,
    snippet: item.snippet || item.snippet_highlighted_words?.join(' ') || '',
    source: item.source || (item.link ? new URL(item.link).hostname : 'web')
  }))
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const query = (body?.query ?? '').toString().trim()
    const topK = Math.min(Math.max(parseInt(body?.topK ?? '5', 10) || 5, 1), 10)
    if (!query) {
      return NextResponse.json({ success: false, error: 'الاستعلام مطلوب' }, { status: 400 })
    }

    let results: Array<{ title: string; url: string; snippet: string; source: string }> = []

    // Prefer Bing if key exists, else SerpAPI
    if (process.env.BING_SEARCH_KEY) {
      results = await searchWithBing(query, topK)
    } else if (process.env.SERPAPI_KEY) {
      results = await searchWithSerpApi(query, topK)
    } else {
      return NextResponse.json({ success: false, error: 'مفاتيح البحث غير متوفرة (BING_SEARCH_KEY أو SERPAPI_KEY)' }, { status: 501 })
    }

    return NextResponse.json({ success: true, query, results })
  } catch (e: any) {
    return NextResponse.json({ success: false, error: e.message }, { status: 500 })
  }
}
