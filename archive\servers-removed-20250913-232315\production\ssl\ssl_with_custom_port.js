// إعداد SSL مع منفذ مخصص لـ mohammi.com
const fs = require('fs');
const https = require('https');
const http = require('http');

console.log('🔐 إعداد SSL مع منفذ مخصص - mohammi.com');
console.log('🌐 IP: ***********');
console.log('='.repeat(50));

// إعدادات المنافذ
const HTTPS_PORT = 8443; // منفذ HTTPS مخصص
const HTTP_PORT = 8080;  // منفذ HTTP مخصص
const PROXY_PORT = 7443; // منفذ تطبيق mohammi

console.log(`🔌 منفذ HTTPS: ${HTTPS_PORT}`);
console.log(`🔌 منفذ HTTP: ${HTTP_PORT}`);
console.log(`🔌 تطبيق mohammi: ${PROXY_PORT}`);

// البحث عن ملفات SSL
const requiredFiles = {
  cert: 'ssl/mohammi_com.crt',
  key: null,
  intermediate: 'ssl/SectigoPublicServerAuthenticationCADVR36.crt'
};

// البحث عن المفتاح الخاص
const possibleKeys = [
  'ssl/mohammi_com.key',
  'ssl/private.key',
  'ssl/privatekey.pem',
  'ssl/mohammi.key',
  'ssl/server.key'
];

console.log('\n🔍 البحث عن المفتاح الخاص...');
for (const keyPath of possibleKeys) {
  if (fs.existsSync(keyPath)) {
    requiredFiles.key = keyPath;
    console.log(`✅ تم العثور على المفتاح الخاص: ${keyPath}`);
    break;
  }
}

if (!requiredFiles.key) {
  console.log('❌ لم يتم العثور على المفتاح الخاص');
  console.log('💡 يرجى استخراج ملف mohammi_com.zip أولاً');
  console.log('💡 أو تأكد من وجود ملف .key في مجلد ssl');
  
  // إنشاء مفتاح تجريبي للاختبار
  console.log('\n🧪 إنشاء مفتاح تجريبي للاختبار...');
  const crypto = require('crypto');
  const { privateKey } = crypto.generateKeyPairSync('rsa', {
    modulusLength: 2048,
    privateKeyEncoding: { type: 'pkcs8', format: 'pem' }
  });
  
  fs.writeFileSync('ssl/test_private.key', privateKey);
  requiredFiles.key = 'ssl/test_private.key';
  console.log('✅ تم إنشاء مفتاح تجريبي: ssl/test_private.key');
  console.log('⚠️ هذا للاختبار فقط - استخدم المفتاح الأصلي للإنتاج');
}

// التحقق من باقي الملفات
for (const [name, path] of Object.entries(requiredFiles)) {
  if (path && !fs.existsSync(path)) {
    console.log(`❌ ملف ${name} غير موجود: ${path}`);
    if (name === 'intermediate') {
      console.log('💡 سيتم المتابعة بدون الشهادة الوسطية');
      requiredFiles.intermediate = null;
    } else {
      process.exit(1);
    }
  }
}

console.log('✅ جميع الملفات المطلوبة متاحة');

// قراءة ملفات SSL
console.log('\n📖 قراءة ملفات SSL...');
let sslOptions;

try {
  const privateKey = fs.readFileSync(requiredFiles.key, 'utf8');
  let certificate = fs.readFileSync(requiredFiles.cert, 'utf8');
  
  // إضافة الشهادة الوسطية إذا كانت متاحة
  if (requiredFiles.intermediate) {
    const intermediate = fs.readFileSync(requiredFiles.intermediate, 'utf8');
    certificate = certificate + '\n' + intermediate;
    console.log('✅ تم دمج الشهادة الوسطية');
  }
  
  sslOptions = {
    key: privateKey,
    cert: certificate,
    secureProtocol: 'TLSv1_2_method',
    ciphers: [
      'ECDHE-RSA-AES128-GCM-SHA256',
      'ECDHE-RSA-AES256-GCM-SHA384',
      'ECDHE-RSA-AES128-SHA256',
      'ECDHE-RSA-AES256-SHA384'
    ].join(':'),
    honorCipherOrder: true
  };
  
  console.log('✅ تم تحميل ملفات SSL بنجاح');
  
} catch (error) {
  console.error('❌ خطأ في قراءة ملفات SSL:', error.message);
  process.exit(1);
}

// إنشاء خادم HTTPS
console.log('\n🔧 إنشاء خادم HTTPS...');

const httpsServer = https.createServer(sslOptions, (req, res) => {
  // Headers الأمان
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // إضافة معلومات المنفذ للـ headers
  res.setHeader('X-SSL-Port', HTTPS_PORT);
  res.setHeader('X-Forwarded-Port', HTTPS_PORT);
  
  // Proxy إلى تطبيق Next.js
  const proxyReq = http.request({
    hostname: 'localhost',
    port: PROXY_PORT,
    path: req.url,
    method: req.method,
    headers: {
      ...req.headers,
      'X-Forwarded-For': req.connection.remoteAddress,
      'X-Forwarded-Proto': 'https',
      'X-Forwarded-Host': req.headers.host,
      'X-Forwarded-Port': HTTPS_PORT,
      'X-Real-IP': req.connection.remoteAddress
    }
  }, (proxyRes) => {
    // نسخ headers من الاستجابة
    Object.keys(proxyRes.headers).forEach(key => {
      res.setHeader(key, proxyRes.headers[key]);
    });
    
    res.writeHead(proxyRes.statusCode);
    proxyRes.pipe(res);
  });
  
  proxyReq.on('error', (error) => {
    console.error('❌ خطأ في Proxy:', error.message);
    res.writeHead(502, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(`
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>🔐 خادم SSL - mohammi.com</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; margin: 0; padding: 50px; text-align: center;
        }
        .container { 
            background: rgba(255,255,255,0.1); padding: 40px; 
            border-radius: 20px; backdrop-filter: blur(10px);
            max-width: 600px; margin: 0 auto;
        }
        .status { color: #4ade80; font-weight: bold; }
        .warning { color: #fbbf24; font-weight: bold; }
        .info { background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 خادم SSL يعمل بنجاح!</h1>
        <p>مرحباً بك في موقع mohammi.com المحمي بـ SSL</p>
        
        <div class="info">
            <h3>معلومات الاتصال:</h3>
            <p><strong>البروتوكول:</strong> <span class="status">HTTPS ✅</span></p>
            <p><strong>المنفذ:</strong> ${HTTPS_PORT}</p>
            <p><strong>IP:</strong> ***********</p>
            <p><strong>الدومين:</strong> mohammi.com</p>
            <p><strong>التشفير:</strong> TLS 1.2+</p>
        </div>
        
        <div class="info">
            <p class="warning">⚠️ تطبيق mohammi غير متاح على المنفذ ${PROXY_PORT}</p>
            <p>يرجى تشغيل التطبيق أولاً</p>
        </div>
        
        <div class="info">
            <h3>روابط الاختبار:</h3>
            <p>🔐 https://mohammi.com:${HTTPS_PORT}</p>
            <p>🔐 https://***********:${HTTPS_PORT}</p>
        </div>
    </div>
</body>
</html>`);
  });
  
  req.pipe(proxyReq);
});

// خادم HTTP لإعادة التوجيه
const httpServer = http.createServer((req, res) => {
  const httpsUrl = `https://${req.headers.host.split(':')[0]}:${HTTPS_PORT}${req.url}`;
  res.writeHead(301, {
    'Location': httpsUrl,
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'
  });
  res.end();
});

// بدء الخوادم
console.log('\n🚀 بدء الخوادم...');

// خادم HTTPS على المنفذ المخصص
httpsServer.listen(HTTPS_PORT, '0.0.0.0', (err) => {
  if (err) {
    console.error('❌ فشل في بدء خادم HTTPS:', err.message);
    process.exit(1);
  }
  
  console.log(`✅ خادم HTTPS يعمل على المنفذ ${HTTPS_PORT}`);
  console.log(`🌐 https://mohammi.com:${HTTPS_PORT}`);
  console.log(`🌐 https://***********:${HTTPS_PORT}`);
});

// خادم HTTP على المنفذ المخصص
httpServer.listen(HTTP_PORT, '0.0.0.0', (err) => {
  if (err) {
    console.log(`⚠️ لم يتم بدء خادم HTTP على المنفذ ${HTTP_PORT}`);
  } else {
    console.log(`✅ خادم HTTP يعمل على المنفذ ${HTTP_PORT} (إعادة توجيه)`);
    console.log(`🔄 http://mohammi.com:${HTTP_PORT} → https://mohammi.com:${HTTPS_PORT}`);
  }
});

// معلومات إضافية
console.log('\n📊 معلومات الشهادة:');
console.log(`🔐 الشهادة: ${requiredFiles.cert}`);
console.log(`🔑 المفتاح الخاص: ${requiredFiles.key}`);
if (requiredFiles.intermediate) {
  console.log(`🔗 الشهادة الوسطية: ${requiredFiles.intermediate}`);
}

console.log('\n🌐 معلومات الشبكة:');
console.log(`📍 IP الخارجي: ***********`);
console.log(`🌍 الدومين: mohammi.com`);
console.log(`🔌 SSL Proxy: ${HTTPS_PORT} → ${PROXY_PORT}`);

console.log('\n🎯 روابط الاختبار:');
console.log(`• https://mohammi.com:${HTTPS_PORT}`);
console.log(`• https://***********:${HTTPS_PORT}`);
console.log(`• http://mohammi.com:${HTTP_PORT} (إعادة توجيه)`);

console.log('\n🧪 اختبار الشهادة مع المنفذ:');
console.log(`• https://www.ssllabs.com/ssltest/analyze.html?d=mohammi.com:${HTTPS_PORT}`);
console.log(`• curl -I https://mohammi.com:${HTTPS_PORT}`);

console.log('\n⚠️ ملاحظات مهمة:');
console.log('1. تأكد من تشغيل تطبيق mohammi على المنفذ 7443');
console.log(`2. فتح المنافذ ${HTTP_PORT} و ${HTTPS_PORT} في جدار الحماية`);
console.log('3. توجيه DNS للدومين mohammi.com إلى IP ***********');
console.log(`4. استخدم الرابط مع المنفذ: https://mohammi.com:${HTTPS_PORT}`);

// معالجة إيقاف الخادم
process.on('SIGINT', () => {
  console.log('\n🛑 إيقاف خوادم SSL...');
  httpsServer.close(() => console.log('✅ تم إيقاف خادم HTTPS'));
  httpServer.close(() => console.log('✅ تم إيقاف خادم HTTP'));
  process.exit(0);
});

console.log('\n⏳ خوادم SSL تعمل... اضغط Ctrl+C للإيقاف');
console.log(`🔗 للاختبار: https://mohammi.com:${HTTPS_PORT}`);
