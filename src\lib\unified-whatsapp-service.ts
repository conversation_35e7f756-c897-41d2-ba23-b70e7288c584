/**
 * خدمة WhatsApp المحسنة للخادم الموحد
 * تتوافق مع نظام التوجيه متعدد المنافذ
 */

import { query } from '@/lib/db'
import * as fs from 'fs'
import * as path from 'path'

// أنواع البيانات
interface WhatsAppConfig {
  enabled: boolean
  phone_number: string
  business_name: string
  session_name: string
  auto_reply: boolean
  auto_reply_message: string
  business_hours: {
    start: string
    end: string
  }
  daily_limits: {
    max_messages: number
    message_delay_seconds: number
  }
}

interface MessageResult {
  success: boolean
  messageId?: string
  error?: string
  timestamp: Date
  recipientPhone: string
}

// فئة خدمة WhatsApp للخادم الموحد
export class UnifiedWhatsAppService {
  private config: WhatsAppConfig | null = null
  private currentPort: string
  private isReady: boolean = false
  private qrCode: string | null = null
  private connectionStatus: string = 'disconnected'

  constructor() {
    // جلب المنفذ الحالي من متغيرات البيئة
    this.currentPort = process.env.PORT || '3000'
    this.loadPortConfig()
  }

  // تحميل إعدادات المنفذ الحالي
  private async loadPortConfig(): Promise<void> {
    try {
      const configPath = path.join(process.cwd(), 'production', 'whatsapp.config.json')
      
      if (!fs.existsSync(configPath)) {
        console.warn('⚠️ ملف إعدادات WhatsApp غير موجود')
        return
      }

      const configData = fs.readFileSync(configPath, 'utf8')
      const whatsappConfig = JSON.parse(configData)
      
      // جلب إعدادات المنفذ الحالي
      const portConfig = whatsappConfig.whatsapp_settings[this.currentPort]
      
      if (portConfig && portConfig.enabled) {
        this.config = portConfig
        console.log(`📱 تم تحميل إعدادات WhatsApp للمنفذ: ${this.currentPort}`)
        console.log(`📞 رقم WhatsApp: ${this.config.phone_number}`)
      } else {
        console.log(`ℹ️ WhatsApp غير مفعل للمنفذ: ${this.currentPort}`)
      }
    } catch (error) {
      console.error('خطأ في تحميل إعدادات WhatsApp:', error)
    }
  }

  // الحصول على حالة الخدمة
  getStatus(): { 
    isReady: boolean
    status: string
    qrCode: string | null
    businessName: string | null
    phoneNumber: string | null
    port: string
  } {
    return {
      isReady: this.isReady,
      status: this.connectionStatus,
      qrCode: this.qrCode,
      businessName: this.config?.business_name || null,
      phoneNumber: this.config?.phone_number || null,
      port: this.currentPort
    }
  }

  // تنسيق رقم الهاتف اليمني
  private formatYemenPhone(phone: string): string {
    let cleanPhone = phone.replace(/[\s\-\(\)]/g, '')
    
    if (cleanPhone.startsWith('0')) {
      cleanPhone = cleanPhone.substring(1)
    }
    
    if (!cleanPhone.startsWith('967')) {
      cleanPhone = '967' + cleanPhone
    }
    
    return '+' + cleanPhone
  }

  // إرسال رسالة نصية (محاكاة للتطوير)
  async sendTextMessage(contactPhone: string, message: string, recipientName?: string): Promise<MessageResult> {
    try {
      if (!this.config || !this.config.enabled) {
        throw new Error('خدمة WhatsApp غير مفعلة لهذا المنفذ')
      }

      const formattedPhone = this.formatYemenPhone(contactPhone)
      
      // محاكاة إرسال الرسالة (للتطوير)
      console.log(`📱 [${this.currentPort}] إرسال رسالة WhatsApp:`)
      console.log(`📞 إلى: ${formattedPhone} (${recipientName || 'غير محدد'})`)
      console.log(`📝 الرسالة: ${message}`)
      console.log(`🏢 من: ${this.config.business_name}`)

      // حفظ سجل الرسالة
      await this.saveMessageLog({
        phone: formattedPhone,
        recipientName,
        message,
        type: 'text',
        status: 'sent',
        messageId: `msg_${Date.now()}`
      })

      return {
        success: true,
        messageId: `msg_${Date.now()}`,
        timestamp: new Date(),
        recipientPhone: formattedPhone
      }

    } catch (error) {
      console.error('خطأ في إرسال الرسالة:', error)
      
      await this.saveMessageLog({
        phone: contactPhone,
        recipientName,
        message,
        type: 'text',
        status: 'failed',
        error: error instanceof Error ? error.message : 'خطأ غير معروف'
      })

      return {
        success: false,
        error: error instanceof Error ? error.message : 'خطأ غير معروف',
        timestamp: new Date(),
        recipientPhone: contactPhone
      }
    }
  }

  // إرسال صورة السند
  async sendVoucherImage(contactPhone: string, imagePath: string, voucherInfo: string, recipientName?: string): Promise<MessageResult> {
    try {
      if (!this.config || !this.config.enabled) {
        throw new Error('خدمة WhatsApp غير مفعلة لهذا المنفذ')
      }

      const formattedPhone = this.formatYemenPhone(contactPhone)
      
      // التحقق من وجود الملف
      if (!fs.existsSync(imagePath)) {
        throw new Error('ملف الصورة غير موجود')
      }

      // تحضير نص مرفق مع الصورة
      const caption = `🧾 ${voucherInfo}\n\n📞 ${this.config.business_name}\n${this.config.phone_number}\n\nشكراً لتعاملكم معنا`
      
      // محاكاة إرسال الصورة (للتطوير)
      console.log(`📱 [${this.currentPort}] إرسال صورة WhatsApp:`)
      console.log(`📞 إلى: ${formattedPhone} (${recipientName || 'غير محدد'})`)
      console.log(`🖼️ الصورة: ${imagePath}`)
      console.log(`📝 التوضيح: ${caption}`)
      console.log(`🏢 من: ${this.config.business_name}`)

      // حفظ سجل الرسالة
      await this.saveMessageLog({
        phone: formattedPhone,
        recipientName,
        message: caption,
        type: 'image',
        status: 'sent',
        messageId: `img_${Date.now()}`,
        mediaPath: imagePath
      })

      return {
        success: true,
        messageId: `img_${Date.now()}`,
        timestamp: new Date(),
        recipientPhone: formattedPhone
      }

    } catch (error) {
      console.error('خطأ في إرسال صورة السند:', error)
      
      await this.saveMessageLog({
        phone: contactPhone,
        recipientName,
        message: voucherInfo,
        type: 'image',
        status: 'failed',
        error: error instanceof Error ? error.message : 'خطأ غير معروف',
        mediaPath: imagePath
      })

      return {
        success: false,
        error: error instanceof Error ? error.message : 'خطأ غير معروف',
        timestamp: new Date(),
        recipientPhone: contactPhone
      }
    }
  }

  // جلب جهات الاتصال
  async getContacts(type?: 'client' | 'employee'): Promise<any[]> {
    try {
      let queryText = ''
      
      if (type === 'client' || !type) {
        queryText = `
          SELECT 
            c.id,
            c.name,
            c.phone,
            'client' as type,
            c.is_active
          FROM clients c
          WHERE c.phone IS NOT NULL AND c.phone != '' AND c.is_active = true
        `
      }
      
      if (type === 'employee' || !type) {
        if (queryText) queryText += ' UNION ALL '
        queryText += `
          SELECT 
            e.id,
            e.name,
            e.phone,
            'employee' as type,
            e.is_active
          FROM employees e
          WHERE e.phone IS NOT NULL AND e.phone != '' AND e.is_active = true
        `
      }

      queryText += ` ORDER BY name`

      const result = await query(queryText)
      
      return result.rows.map(row => ({
        id: row.id,
        name: row.name,
        phone: this.formatYemenPhone(row.phone),
        type: row.type,
        isActive: row.is_active
      }))

    } catch (error) {
      console.error('خطأ في جلب جهات الاتصال:', error)
      return []
    }
  }

  // حفظ سجل الرسائل
  private async saveMessageLog(logData: {
    phone: string
    recipientName?: string
    message: string
    type: string
    status: string
    messageId?: string
    error?: string
    mediaPath?: string
  }): Promise<void> {
    try {
      // جلب معرف الشركة من قاعدة البيانات
      const companyResult = await query(`SELECT id FROM company_info LIMIT 1`)
      const companyId = companyResult.rows[0]?.id || 1

      await query(`
        INSERT INTO whatsapp_local_messages (
          company_id, phone_number, recipient_name, message_content, 
          message_type, media_path, status, whatsapp_message_id, 
          error_message, sent_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      `, [
        companyId,
        logData.phone,
        logData.recipientName || null,
        logData.message,
        logData.type,
        logData.mediaPath || null,
        logData.status,
        logData.messageId || null,
        logData.error || null,
        logData.status === 'sent' ? new Date() : null
      ])
    } catch (error) {
      console.error('خطأ في حفظ سجل الرسالة:', error)
    }
  }
}

// إنشاء مثيل واحد من الخدمة
export const unifiedWhatsAppService = new UnifiedWhatsAppService()

// دوال مساعدة للاستخدام السريع
export async function sendUnifiedWhatsApp(phone: string, message: string, recipientName?: string): Promise<MessageResult> {
  return await unifiedWhatsAppService.sendTextMessage(phone, message, recipientName)
}

export async function sendUnifiedVoucher(phone: string, imagePath: string, voucherInfo: string, recipientName?: string): Promise<MessageResult> {
  return await unifiedWhatsAppService.sendVoucherImage(phone, imagePath, voucherInfo, recipientName)
}

export async function getUnifiedWhatsAppStatus() {
  return unifiedWhatsAppService.getStatus()
}
