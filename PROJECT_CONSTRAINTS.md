# 🔒 ضوابط المشروع الصارمة - مبنية على SERVER_OPERATIONS_GUIDE.md

## ⚠️ تحذير هام
**هذه الضوابط مستخرجة بالكامل من SERVER_OPERATIONS_GUIDE.md وهي إلزامية وغير قابلة للتفاوض. أي انحراف عنها يعتبر مخالفة جسيمة للدليل المعتمد.**

## 📋 مصدر الضوابط
**جميع الضوابط التالية مستخرجة من:** `D:\mohaminew\SERVER_OPERATIONS_GUIDE.md`

## 🚫 المحظورات المطلقة

### **1. الأوامر المحظورة (غير مذكورة في الدليل):**
```
❌ npm run dev (غير مذكور في السطر 44-51)
❌ next dev (غير مذكور في السطر 44-51)
❌ npm start (غير مذكور في السطر 44-51)
❌ next start (غير مذكور في السطر 44-51)
❌ node server.js (خوادم مخصصة غير معتمدة)
❌ nodemon server.js (خوادم مخصصة غير معتمدة)
```

### **2. المنافذ:**
```
✅ 7443, 8914 (المنافذ الخارجية المذكورة في السطر 39-40)
✅ 3000 (المنفذ الداخلي للـ Next.js - السطر 29)
❌ 8080, 8000, 5000, 4000, 9000 (منافذ غير مذكورة)
```

### **3. الملفات المحظورة:**
```
❌ إنشاء خوادم جديدة بدون إذن
❌ تعديل advanced-unified-server.js بدون إذن
❌ تعديل production/routing.config.json بدون إذن
❌ حذف ملفات الضوابط
```

## ✅ المسموحات الوحيدة (من SERVER_OPERATIONS_GUIDE.md)

### **1. الخوادم المسموحة (السطر 7-8):**
```
✅ advanced-unified-server.js (Advanced Unified Proxy)
✅ direct-multi-server.js (Direct Multi-Server)
```

### **2. المنافذ المسموحة:**

#### **المنافذ الخارجية (السطر 39-40):**
```
✅ 7443 → DB mohammi, company "نظام إدارة المحاماة - محمد"
✅ 8914 → DB rubaie, company "نظام إدارة المحاماة - الربعي"
```

#### **المنافذ الداخلية (السطر 29):**
```
✅ 3000 → internal Next.js (default)
✅ 3001 → internal Next.js (alternative)
```

### **3. أوامر التشغيل المسموحة:**

#### **الأوامر المسموحة فقط (السطر 44-51):**
```
✅ node advanced-unified-server.js (السطر 45)
✅ node direct-multi-server.js (السطر 46)
✅ pm2 start ecosystem.config.json (السطر 48)
✅ pm2 status (السطر 49)
✅ pm2 logs (السطر 50)
✅ pm2 restart legal-system-unified (السطر 51)
✅ pm2 stop legal-system-unified (السطر 51)
```

#### **ملاحظة مهمة:**
```
⚠️ Next.js يتم تشغيله تلقائياً داخلياً بواسطة الخوادم المعتمدة
⚠️ لا حاجة لتشغيل npm run dev أو next dev منفصلاً
⚠️ الخوادم المعتمدة تدير Next.js داخلياً حسب السطر 29 و 35
```

### **4. الملفات الإلزامية (السطر 13-24):**
```
✅ D:\mohaminew\advanced-unified-server.js
✅ D:\mohaminew\direct-multi-server.js
✅ D:\mohaminew\production\routing.config.json
✅ D:\mohaminew\ecosystem.config.json
✅ D:\mohaminew\.env.7443
✅ D:\mohaminew\.env.8914
✅ D:\mohaminew\.env.mohammi
✅ D:\mohaminew\.env.rubaie
✅ D:\mohaminew\ssl\ (إذا كان HTTPS مفعل)
✅ D:\mohaminew\logs\
```

### **5. قواعد البيانات المسموحة (السطر 39-41):**
```
✅ mohammi (للمنفذ 7443)
✅ rubaie (للمنفذ 8914)
✅ Host: localhost, Port: 5432, User: postgres
```

### **6. ملفات المرجع المطلوبة (السطر 24):**
```
✅ README-UNIFIED-SERVER.md
✅ README-ADVANCED-SERVER.md
✅ DEPLOYMENT_GUIDE.md
✅ SERVER-POLICY.md
```

## 🛡️ آليات الحماية

### **1. المراقبة التلقائية:**
- فحص دوري كل 30 ثانية
- كشف الخوادم غير المصرح بها
- إيقاف تلقائي للعمليات المخالفة

### **2. التصحيح التلقائي:**
- حذف فوري للخوادم غير المصرح بها
- إيقاف المنافذ المخالفة
- تسجيل جميع المخالفات

### **3. التقارير:**
- تقرير امتثال يومي
- سجل مفصل للمخالفات
- تنبيهات فورية

## 📋 إجراءات الالتزام

### **قبل أي تطوير:**
1. ✅ تشغيل `node project-guardian.js check`
2. ✅ التأكد من عدم وجود مخالفات
3. ✅ استخدام الخوادم المعتمدة فقط

### **أثناء التطوير:**
1. ✅ مراقبة مستمرة: `node project-guardian.js monitor`
2. ✅ عدم تشغيل خوادم إضافية
3. ✅ الالتزام بالمنافذ المحددة

### **بعد التطوير:**
1. ✅ تقرير نهائي: `node project-guardian.js report`
2. ✅ التأكد من نظافة المشروع
3. ✅ توثيق أي تغييرات

## 🚨 عواقب المخالفة

### **المخالفة الأولى:**
- ⚠️ تحذير مسجل
- 🔧 تصحيح تلقائي
- 📝 تقرير مخالفة

### **المخالفة الثانية:**
- 🚫 إيقاف فوري للعمليات
- 🗑️ حذف الملفات المخالفة
- 📋 تقرير مفصل للإدارة

### **المخالفة الثالثة:**
- 🔒 قفل النظام
- 📞 تنبيه الإدارة
- 🛑 إيقاف جميع العمليات

## 🔧 أدوات الالتزام

### **1. حارس المشروع:**
```bash
# فحص الامتثال
node project-guardian.js check

# مراقبة مستمرة
node project-guardian.js monitor

# تقرير الامتثال
node project-guardian.js report
```

### **2. أوامر آمنة للتطوير:**
```bash
# تشغيل الخادم المعتمد
npm run advanced

# تشغيل البديل
npm run direct

# بناء المشروع
npm run build
```

### **3. فحص دوري:**
```bash
# كل صباح
node project-guardian.js check

# قبل أي تغيير
node project-guardian.js report
```

## 📊 مؤشرات الأداء

### **مؤشرات الامتثال:**
- 🟢 **100%** - امتثال كامل
- 🟡 **90-99%** - انحرافات طفيفة
- 🔴 **أقل من 90%** - مخالفات جسيمة

### **أهداف الجودة:**
- ✅ صفر مخالفات يومياً
- ✅ 100% امتثال للضوابط
- ✅ استجابة فورية للتنبيهات

## 🎯 خطة الطوارئ

### **في حالة مخالفة جسيمة:**
1. 🛑 إيقاف فوري لجميع العمليات
2. 🔍 تحليل سبب المخالفة
3. 🔧 تصحيح جذري
4. 📋 تقرير مفصل
5. 🛡️ تعزيز الحماية

### **استعادة النظام:**
1. ✅ فحص شامل للامتثال
2. ✅ تشغيل الخادم المعتمد
3. ✅ مراقبة مكثفة لـ 24 ساعة
4. ✅ تقرير استعادة

## 📞 جهات الاتصال

### **في حالة الطوارئ:**
- 🚨 **مخالفة جسيمة**: تنبيه فوري للإدارة
- ⚠️ **مخالفة عادية**: تسجيل في السجل
- ✅ **امتثال كامل**: تقرير يومي

## 🔐 التوقيع الرقمي

```
تاريخ الإنشاء: 2025-01-02
الإصدار: 1.0
الحالة: نافذ ومُلزم
التوقيع: نظام حماية المشروع
```

---

**⚠️ هذا المستند ملزم قانونياً ولا يجوز تجاهله أو تعديله بدون إذن صريح من الإدارة.**
