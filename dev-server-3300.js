/**
 * خادم التطوير البسيط للمنفذ 3300
 * نظام إدارة المحاماة - تطوير (محمد الحاشدي)
 * يشغل Next.js على المنفذ 3300 مباشرة
 */

const { spawn } = require('child_process');
const http = require('http');
const path = require('path');

console.log('🚀 بدء خادم التطوير - نظام إدارة المحاماة (محمد الحاشدي)');
console.log('🌐 المنفذ: 3300');
console.log('📁 المجلد الحالي:', process.cwd());

// التحقق من وجود Next.js
const nextPath = path.join(__dirname, 'node_modules', '.bin', 'next');
const nextCmd = process.platform === 'win32' ? 'next.cmd' : 'next';

console.log('🔍 البحث عن Next.js...');

// تشغيل Next.js
const nextProcess = spawn('npx', ['next', 'dev', '-p', '3300'], {
  cwd: __dirname,
  stdio: 'inherit',
  shell: true
});

nextProcess.on('spawn', () => {
  console.log('✅ تم بدء نظام إدارة المحاماة - تطوير (محمد الحاشدي)');
  console.log('🌐 يمكنك الوصول للموقع على: http://localhost:3300');
  console.log('👨‍💼 المطور: محمد الحاشدي');
});

nextProcess.on('error', (error) => {
  console.error('❌ خطأ في تشغيل Next.js:', error.message);
  process.exit(1);
});

nextProcess.on('exit', (code) => {
  console.log(`📤 Next.js توقف برمز الخروج: ${code}`);
  if (code !== 0) {
    console.log('🔄 محاولة إعادة التشغيل...');
    setTimeout(() => {
      process.exit(code);
    }, 2000);
  }
});

// التعامل مع إشارات النظام
process.on('SIGINT', () => {
  console.log('\n🛑 إيقاف الخادم...');
  if (nextProcess) {
    nextProcess.kill('SIGINT');
  }
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 إيقاف الخادم...');
  if (nextProcess) {
    nextProcess.kill('SIGTERM');
  }
  process.exit(0);
});

// إبقاء العملية نشطة
process.stdin.resume();
