"use client";

import React, { useEffect, useMemo, useState } from "react";

type RouteConfig = {
  database: string;
  company_name?: string;
  theme_color?: string;
  logo_text?: string;
  description?: string;
  welcome_message?: string;
  notification_prefix?: string;
  enabled?: boolean;
  homepage?: string; // مسار الصفحة الرئيسية (اختياري)
};

type RoutingConfig = {
  routes: Record<string, RouteConfig>;
  default_config?: {
    db_host?: string;
    db_port?: number;
    db_user?: string;
    db_password?: string;
    jwt_secret?: string;
  };
};

export default function ServerManagementPage() {
  const [data, setData] = useState<RoutingConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [info, setInfo] = useState<string | null>(null);

  // نموذج إضافة/تعديل
  const [editPort, setEditPort] = useState<string>("");
  const [form, setForm] = useState<RouteConfig>({ database: "" });
  const isEdit = useMemo(() => !!editPort, [editPort]);

  useEffect(() => {
    (async () => {
      try {
        setLoading(true);
        const res = await fetch("/api/server-management/routes", { cache: "no-store" });
        const json = await res.json();
        if (!res.ok) throw new Error(json?.error || "Failed to load routes");
        // Ensure enabled default true if missing
        if (json?.routes) {
          for (const p of Object.keys(json.routes)) {
            if (json.routes[p].enabled === undefined) json.routes[p].enabled = true;
          }
        }
        setData(json);
      } catch (e: any) {
        setError(e?.message || "حدث خطأ في التحميل");
      } finally {
        setLoading(false);
      }
    })();
  }, []);

  function resetForm() {
    setEditPort("");
    setForm({ database: "", enabled: true });
  }

  function onEdit(port: string) {
    if (!data) return;
    setEditPort(port);
    setForm({ ...data.routes[port] });
  }

  function onDelete(port: string) {
    if (!data) return;
    const confirmed = confirm(`هل تريد حذف المنفذ ${port}؟`);
    if (!confirmed) return;
    const next: RoutingConfig = { ...data, routes: { ...data.routes } };
    delete next.routes[port];
    setData(next);
    setInfo(`تم حذف المنفذ ${port} مؤقتاً، اضغط حفظ لتطبيق التغييرات.`);
  }

  function upsertRoute() {
    if (!data) return;
    if (!editPort || !/^\d{2,5}$/.test(editPort)) {
      setError("رقم المنفذ غير صالح");
      return;
    }
    if (!form.database?.trim()) {
      setError("حقل قاعدة البيانات مطلوب");
      return;
    }
    const next: RoutingConfig = { ...data, routes: { ...data.routes } };
    next.routes[editPort] = { ...form };
    setData(next);
    setInfo(isEdit ? `تم تحديث المنفذ ${editPort} محلياً، اضغط حفظ.` : `تمت إضافة المنفذ ${editPort} محلياً، اضغط حفظ.`);
    resetForm();
  }

  async function saveAll() {
    if (!data) return;
    setSaving(true);
    setError(null);
    setInfo(null);
    try {
      const res = await fetch("/api/server-management/save", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ routes: data.routes, default_config: data.default_config }),
      });
      const json = await res.json();
      if (!res.ok || !json?.ok) throw new Error(json?.error || "فشل الحفظ");
      setInfo("تم حفظ الإعدادات بنجاح.");
    } catch (e: any) {
      setError(e?.message || "فشل الحفظ");
    } finally {
      setSaving(false);
    }
  }

  async function testConnection(port: string) {
    if (!data) return;
    setInfo(null);
    setError(null);
    try {
      const route = data.routes[port];
      const dc = data.default_config || {};
      const res = await fetch("/api/server-management/validate-db", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          dbName: route.database,
          dbHost: dc.db_host || "localhost",
          dbPort: dc.db_port || 5432,
          dbUser: dc.db_user || "postgres",
          dbPassword: dc.db_password || "postgres",
          timeoutMs: 5000,
        }),
      });
      const json = await res.json();
      if (json?.ok) setInfo(`✅ اتصال ناجح بقاعدة البيانات (${route.database})`);
      else setError(`❌ فشل الاتصال: ${json?.error || "غير معروف"}`);
    } catch (e: any) {
      setError(e?.message || "فشل الاختبار");
    }
  }

  function toggleEnable(port: string) {
    if (!data) return;
    const next: RoutingConfig = { ...data, routes: { ...data.routes } };
    next.routes[port] = { ...next.routes[port], enabled: !next.routes[port].enabled };
    setData(next);
  }

  const portsSorted = useMemo(() => {
    return data ? Object.keys(data.routes).sort((a, b) => Number(a) - Number(b)) : [];
  }, [data]);

  return (
    <div className="p-6 max-w-[1100px] mx-auto">
      <h1 className="text-2xl font-bold mb-4">لوحة إدارة الخوادم (المنافذ وقواعد البيانات)</h1>

      {loading && <div>جارِ التحميل...</div>}
      {error && <div className="text-red-600 mb-3">{error}</div>}
      {info && <div className="text-green-700 mb-3">{info}</div>}

      {!loading && data && (
        <>
          <section className="mb-6">
            <h2 className="font-semibold mb-2">الإعدادات الافتراضية لاتصال قاعدة البيانات</h2>
            <div className="rounded border p-3 text-sm bg-gray-50">
              <div>Host: {data.default_config?.db_host || "localhost"}</div>
              <div>Port: {data.default_config?.db_port || 5432}</div>
              <div>User: {data.default_config?.db_user || "postgres"}</div>
            </div>
          </section>

          <section className="mb-8">
            <div className="flex items-center justify-between mb-2">
              <h2 className="font-semibold">المنافذ الحالية</h2>
              <button
                onClick={saveAll}
                disabled={saving}
                className="px-4 py-2 bg-blue-600 text-white rounded disabled:opacity-50"
              >
                {saving ? "جارِ الحفظ..." : "حفظ التغييرات"}
              </button>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full border text-sm">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="p-2 border">المنفذ</th>
                    <th className="p-2 border">قاعدة البيانات</th>
                    <th className="p-2 border">الشركة</th>
                    <th className="p-2 border">لون السمة</th>
                    <th className="p-2 border">Enabled</th>
                    <th className="p-2 border">Homepage</th>
                    <th className="p-2 border">إجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {portsSorted.map((port) => {
                    const r = data.routes[port];
                    return (
                      <tr key={port} className="odd:bg-white even:bg-gray-50">
                        <td className="p-2 border font-mono">{port}</td>
                        <td className="p-2 border">{r.database}</td>
                        <td className="p-2 border">{r.company_name || "-"}</td>
                        <td className="p-2 border">
                          <span className="inline-flex items-center gap-2">
                            <span className="w-4 h-4 inline-block rounded" style={{ background: r.theme_color || "#999" }} />
                            {r.theme_color || "#999"}
                          </span>
                        </td>
                        <td className="p-2 border text-center">
                          <button
                            onClick={() => toggleEnable(port)}
                            className={`px-2 py-1 rounded text-white ${r.enabled === false ? "bg-gray-500" : "bg-green-600"}`}
                          >
                            {r.enabled === false ? "معطّل" : "مفعّل"}
                          </button>
                        </td>
                        <td className="p-2 border">{r.homepage || "/"}</td>
                        <td className="p-2 border">
                          <div className="flex gap-2">
                            <button onClick={() => testConnection(port)} className="px-2 py-1 bg-emerald-600 text-white rounded">
                              اختبار الاتصال
                            </button>
                            <button onClick={() => onEdit(port)} className="px-2 py-1 bg-yellow-600 text-white rounded">
                              تعديل
                            </button>
                            <button onClick={() => onDelete(port)} className="px-2 py-1 bg-red-600 text-white rounded">
                              حذف
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                  {portsSorted.length === 0 && (
                    <tr>
                      <td colSpan={7} className="p-3 text-center text-gray-500">لا توجد منافذ حالياً</td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </section>

          <section className="mb-8">
            <h2 className="font-semibold mb-3">{isEdit ? "تعديل منفذ" : "إضافة منفذ جديد"}</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div>
                <label className="block text-sm mb-1">رقم المنفذ</label>
                <input
                  className="w-full border rounded p-2"
                  value={editPort}
                  onChange={(e) => setEditPort(e.target.value.replace(/[^0-9]/g, ""))}
                  placeholder="مثلاً 9000"
                />
              </div>
              <div>
                <label className="block text-sm mb-1">اسم قاعدة البيانات</label>
                <input
                  className="w-full border rounded p-2"
                  value={form.database || ""}
                  onChange={(e) => setForm((f) => ({ ...f, database: e.target.value }))}
                  placeholder="مثلاً new_company"
                />
              </div>
              <div>
                <label className="block text-sm mb-1">اسم الشركة</label>
                <input
                  className="w-full border rounded p-2"
                  value={form.company_name || ""}
                  onChange={(e) => setForm((f) => ({ ...f, company_name: e.target.value }))}
                  placeholder="اختياري"
                />
              </div>
              <div>
                <label className="block text-sm mb-1">لون السمة</label>
                <input
                  className="w-full border rounded p-2"
                  value={form.theme_color || ""}
                  onChange={(e) => setForm((f) => ({ ...f, theme_color: e.target.value }))}
                  placeholder="#2563eb"
                />
              </div>
              <div>
                <label className="block text-sm mb-1">النص في الشعار (logo_text)</label>
                <input
                  className="w-full border rounded p-2"
                  value={form.logo_text || ""}
                  onChange={(e) => setForm((f) => ({ ...f, logo_text: e.target.value }))}
                />
              </div>
              <div>
                <label className="block text-sm mb-1">الوصف</label>
                <input
                  className="w-full border rounded p-2"
                  value={form.description || ""}
                  onChange={(e) => setForm((f) => ({ ...f, description: e.target.value }))}
                />
              </div>
              <div>
                <label className="block text-sm mb-1">رسالة ترحيب</label>
                <input
                  className="w-full border rounded p-2"
                  value={form.welcome_message || ""}
                  onChange={(e) => setForm((f) => ({ ...f, welcome_message: e.target.value }))}
                />
              </div>
              <div>
                <label className="block text-sm mb-1">بادئة الإشعار</label>
                <input
                  className="w-full border rounded p-2"
                  value={form.notification_prefix || ""}
                  onChange={(e) => setForm((f) => ({ ...f, notification_prefix: e.target.value }))}
                />
              </div>
              <div>
                <label className="block text-sm mb-1">الصفحة الرئيسية (homepage)</label>
                <input
                  className="w-full border rounded p-2"
                  value={form.homepage || ""}
                  onChange={(e) => setForm((f) => ({ ...f, homepage: e.target.value }))}
                  placeholder="/dashboard أو /"
                />
              </div>
              <div className="flex items-center gap-2 mt-6">
                <input
                  id="enabled"
                  type="checkbox"
                  checked={form.enabled !== false}
                  onChange={(e) => setForm((f) => ({ ...f, enabled: e.target.checked }))}
                />
                <label htmlFor="enabled">مفعّل (Enabled)</label>
              </div>
            </div>
            <div className="mt-4 flex gap-2">
              <button onClick={upsertRoute} className="px-4 py-2 bg-blue-600 text-white rounded">
                {isEdit ? "تحديث" : "إضافة"}
              </button>
              {isEdit && (
                <button onClick={resetForm} className="px-4 py-2 bg-gray-600 text-white rounded">إلغاء</button>
              )}
            </div>
          </section>

          <section className="mb-8">
            <h2 className="font-semibold mb-2">تشغيل/إيقاف (ملاحظات)</h2>
            <div className="text-sm rounded border p-3 bg-amber-50">
              • زر التفعيل/التعطيل يقوم بتخزين الحالة داخل ملف routing.config.json (حقل enabled).<br />
              • لكي يعتمدها الخادم، يمكننا لاحقاً تعديل ملفات الخادم ليتجاهل المنافذ المعطّلة أو يوجه الجذر إلى homepage المحدد.
            </div>
          </section>
        </>
      )}
    </div>
  );
}
