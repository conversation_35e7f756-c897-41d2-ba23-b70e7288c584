import { NextRequest, NextResponse } from 'next/server'
import { openDb } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    
    
    const { searchParams } = new URL(request.url)
    const activeOnly = searchParams.get('active') === 'true'
    
    const db = await openDb()
    
    // جلب خدمات الموقع
    let query = 'SELECT * FROM website_services'
    const params: any[] = []

    if (activeOnly) {
      query += ' WHERE is_active = true'
    }

    query += ' ORDER BY sort_order ASC, id ASC'

    const services = await db.all(query, params)

    

    db.close()

    return NextResponse.json({
      success: true,
      data: services,
      count: services.length
    })

  } catch (error) {
    console.error('❌ خطأ في جلب خدمات الموقع:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'فشل في جلب خدمات الموقع',
        details: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    
    
    const body = await request.json()
    const { title, slug, description, content, icon_name, icon_color, image_url, sort_order, meta_title, meta_description } = body

    if (!title || !slug) {
      return NextResponse.json(
        { success: false, error: 'العنوان والرابط مطلوبان' },
        { status: 400 }
      )
    }

    const db = await openDb()

    // التحقق من عدم وجود slug مكرر
    const existingService = await db.get('SELECT id FROM website_services WHERE slug = ?', [slug])
    if (existingService) {
      return NextResponse.json(
        { success: false, error: 'الرابط موجود مسبقاً' },
        { status: 400 }
      )
    }

    // إضافة الخدمة الجديدة
    const result = await db.run(`
      INSERT INTO website_services (
        title, slug, description, content, icon_name, icon_color, 
        image_url, sort_order, meta_title, meta_description
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      title, slug, description || '', content || '', 
      icon_name || 'Scale', icon_color || '#2563eb',
      image_url || null, sort_order || 0, 
      meta_title || null, meta_description || null
    ])

    

    db.close()

    return NextResponse.json({
      success: true,
      message: 'تم إضافة الخدمة بنجاح',
      data: { id: result.lastID, title, slug }
    })

  } catch (error) {
    console.error('❌ خطأ في إضافة خدمة الموقع:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'فشل في إضافة خدمة الموقع',
        details: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    )
  }
}
