const { Client } = require('pg');

async function updateJournalEntryDetailsStructure() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: process.env.PGDATABASE || 'mohammi',
    user: process.env.PGUSER || 'postgres',
    password: process.env.PGPASSWORD || 'yemen123',
  });

  try {
    await client.connect();
    console.log('🔗 متصل بقاعدة البيانات');

    // التأكد من وجود جدول journal_entry_details
    const tableCheck = await client.query(`
      SELECT to_regclass('public.journal_entry_details') as t
    `);
    if (!tableCheck.rows[0].t) {
      throw new Error('جدول journal_entry_details غير موجود. يرجى إنشاء الجداول المحاسبية أولاً.');
    }

    console.log('🛠️ بدء تحديث هيكل جدول journal_entry_details...');

    // 1. التحقق من وجود العمود case_id وإعادة تسميته إلى cost_center_id
    console.log('📝 التحقق من العمود case_id...');
    const caseIdExists = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name='journal_entry_details' AND column_name='case_id'
    `);

    if (caseIdExists.rows.length > 0) {
      console.log('🔄 إعادة تسمية case_id إلى cost_center_id...');
      await client.query(`
        ALTER TABLE journal_entry_details 
        RENAME COLUMN case_id TO cost_center_id
      `);
      console.log('✅ تم إعادة تسمية case_id إلى cost_center_id');
    }

    // 2. التأكد من وجود العمود cost_center_id
    const costCenterIdExists = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name='journal_entry_details' AND column_name='cost_center_id'
    `);

    if (costCenterIdExists.rows.length === 0) {
      console.log('➕ إضافة العمود cost_center_id...');
      await client.query(`
        ALTER TABLE journal_entry_details 
        ADD COLUMN cost_center_id INTEGER REFERENCES cost_centers(id)
      `);
      console.log('✅ تم إضافة العمود cost_center_id');
    }

    // 3. التأكد من وجود العمود issues_id
    const issuesIdExists = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name='journal_entry_details' AND column_name='issues_id'
    `);

    if (issuesIdExists.rows.length === 0) {
      console.log('➕ إضافة العمود issues_id...');
      await client.query(`
        ALTER TABLE journal_entry_details 
        ADD COLUMN issues_id INTEGER REFERENCES issues(id)
      `);
      console.log('✅ تم إضافة العمود issues_id');
    }

    // 4. إضافة الفهارس للأعمدة الجديدة
    console.log('📇 إنشاء الفهارس...');
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_jed_cost_center_id ON journal_entry_details(cost_center_id);
      CREATE INDEX IF NOT EXISTS idx_jed_issues_id ON journal_entry_details(issues_id);
    `);
    console.log('✅ تم إنشاء الفهارس');

    // 5. التحقق من وجود جدول cost_centers وإنشاؤه إذا لم يكن موجوداً
    console.log('🏢 التحقق من جدول cost_centers...');
    const costCentersTableCheck = await client.query(`
      SELECT to_regclass('public.cost_centers') as t
    `);

    if (!costCentersTableCheck.rows[0].t) {
      console.log('🏗️ إنشاء جدول cost_centers...');
      await client.query(`
        CREATE TABLE cost_centers (
          id SERIAL PRIMARY KEY,
          center_code VARCHAR(20) UNIQUE NOT NULL,
          center_name VARCHAR(255) NOT NULL,
          parent_id INTEGER REFERENCES cost_centers(id),
          center_level INTEGER DEFAULT 1,
          is_active BOOLEAN DEFAULT TRUE,
          description TEXT,
          created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `);
      console.log('✅ تم إنشاء جدول cost_centers');

      // إضافة بيانات أساسية لمراكز التكلفة
      console.log('📊 إضافة بيانات أساسية لمراكز التكلفة...');
      await client.query(`
        INSERT INTO cost_centers (center_code, center_name, center_level, description) 
        VALUES 
          ('CC001', 'الإدارة العامة', 1, 'مركز تكلفة الإدارة العامة'),
          ('CC002', 'القسم القانوني', 1, 'مركز تكلفة القسم القانوني'),
          ('CC003', 'المحاسبة والمالية', 1, 'مركز تكلفة المحاسبة والمالية'),
          ('CC004', 'الموارد البشرية', 1, 'مركز تكلفة الموارد البشرية'),
          ('CC005', 'الخدمات العامة', 1, 'مركز تكلفة الخدمات العامة'),
          ('CC006', 'القضايا المدنية', 1, 'مركز تكلفة القضايا المدنية'),
          ('CC007', 'القضايا الجنائية', 1, 'مركز تكلفة القضايا الجنائية'),
          ('CC008', 'القضايا التجارية', 1, 'مركز تكلفة القضايا التجارية'),
          ('CC009', 'الاستشارات القانونية', 1, 'مركز تكلفة الاستشارات القانونية'),
          ('CC010', 'العقود والاتفاقيات', 1, 'مركز تكلفة العقود والاتفاقيات')
        ON CONFLICT (center_code) DO NOTHING
      `);
      console.log('✅ تم إضافة بيانات مراكز التكلفة');
    } else {
      console.log('✅ جدول cost_centers موجود مسبقاً');
    }

    // 6. إضافة قيود المرجعية إذا لم تكن موجودة
    console.log('🔗 إضافة قيود المرجعية...');
    
    // قيد مرجعي لـ cost_center_id
    try {
      await client.query(`
        ALTER TABLE journal_entry_details 
        ADD CONSTRAINT fk_jed_cost_center_id 
        FOREIGN KEY (cost_center_id) REFERENCES cost_centers(id)
      `);
      console.log('✅ تم إضافة قيد مرجعي لـ cost_center_id');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('ℹ️ قيد مرجعي cost_center_id موجود مسبقاً');
      } else {
        console.log('⚠️ تعذر إضافة قيد مرجعي cost_center_id:', error.message);
      }
    }

    // قيد مرجعي لـ issues_id
    try {
      await client.query(`
        ALTER TABLE journal_entry_details 
        ADD CONSTRAINT fk_jed_issues_id 
        FOREIGN KEY (issues_id) REFERENCES issues(id)
      `);
      console.log('✅ تم إضافة قيد مرجعي لـ issues_id');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('ℹ️ قيد مرجعي issues_id موجود مسبقاً');
      } else {
        console.log('⚠️ تعذر إضافة قيد مرجعي issues_id:', error.message);
      }
    }

    // 7. عرض الهيكل النهائي للجدول
    console.log('\n📋 الهيكل النهائي لجدول journal_entry_details:');
    const columns = await client.query(`
      SELECT 
        column_name,
        data_type,
        is_nullable,
        column_default
      FROM information_schema.columns 
      WHERE table_name = 'journal_entry_details' 
      ORDER BY ordinal_position
    `);

    columns.rows.forEach(col => {
      console.log(`   - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? '(NOT NULL)' : ''}`);
    });

    console.log('\n✅ تم تحديث هيكل جدول journal_entry_details بنجاح');
    console.log('\n📝 ملخص التغييرات:');
    console.log('   ✅ case_id → cost_center_id (مرتبط بجدول cost_centers)');
    console.log('   ✅ issues_id (مرتبط بجدول issues)');
    console.log('   ✅ إنشاء/تحديث جدول cost_centers');
    console.log('   ✅ إضافة الفهارس والقيود المرجعية');

  } catch (err) {
    console.error('❌ خطأ أثناء التحديث:', err.message);
    process.exitCode = 1;
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

if (require.main === module) {
  updateJournalEntryDetailsStructure();
}

module.exports = { updateJournalEntryDetailsStructure };
