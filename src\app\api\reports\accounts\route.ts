import { NextRequest, NextResponse } from 'next/server'
import { query, getDatabaseName } from '@/lib/database-router'

// تقارير الحسابات:
// يدعم الأنماط التالية عبر query params:
// - report: 'statement' | 'daily' (افتراضي 'statement')
// - account_id?: number  // كشف حساب مباشر لحساب من دليل الحسابات
// - client_id?: number   // كشف حساب عميل عبر ربطه بحساب في دليل الحسابات
// - employee_id?: number // كشف حساب موظف عبر ربطه بحساب في دليل الحسابات
// - date_from?: string (YYYY-MM-DD)
// - date_to?: string (YYYY-MM-DD)
// - summary?: 'true' | 'false' (افتراضي false)
// - status?: 'approved' | 'draft' | 'all' (افتراضي approved)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)

    const report = (searchParams.get('report') || 'statement') as 'statement' | 'daily'
    const accountId = searchParams.get('account_id')
    const clientId = searchParams.get('client_id')
    const employeeId = searchParams.get('employee_id')
    const dateFrom = searchParams.get('date_from')
    const dateTo = searchParams.get('date_to')
    const summary = (searchParams.get('summary') || 'false') === 'true'
    const status = (searchParams.get('status') || 'approved') as 'approved' | 'draft' | 'all'

    const params: any[] = []
    let p = 1

    // تحديد فلترة الحالة
    let statusClause = ''
    if (status !== 'all') {
      statusClause = ` AND je.status = $${p++}`
      params.push(status)
    }

    // فلترة التاريخ
    if (dateFrom) {
      params.push(dateFrom)
    }
    if (dateTo) {
      params.push(dateTo)
    }
    const dateClause =
      (dateFrom ? ` AND je.entry_date >= $${status !== 'all' ? 2 : 1}` : '') +
      (dateTo ? ` AND je.entry_date <= $${status !== 'all' ? (dateFrom ? 3 : 2) : (dateFrom ? 2 : 1)}` : '')

    // تحديد الحساب المستهدف: account_id مباشرة أو عبر clients/employees
    let accountJoin = 'jed.account_id = coa.id'
    let accountWhere = ''

    if (accountId) {
      accountWhere = ` AND coa.id = $${p++}`
      params.push(parseInt(accountId))
    } else if (clientId) {
      accountWhere = ` AND coa.original_table = 'clients' AND coa.linked_record_id = $${p++}`
      params.push(parseInt(clientId))
    } else if (employeeId) {
      accountWhere = ` AND coa.original_table = 'employees' AND coa.linked_record_id = $${p++}`
      params.push(parseInt(employeeId))
    }

    if (report === 'daily') {
      // تقرير الحركة اليومية: إجماليات اليوم حسب التاريخ
      const sql = `
        SELECT 
          je.entry_date,
          COUNT(DISTINCT je.id) as entries_count,
          COALESCE(SUM(jed.debit_amount), 0) as total_debit,
          COALESCE(SUM(jed.credit_amount), 0) as total_credit
        FROM journal_entries je
        JOIN journal_entry_details jed ON jed.journal_entry_id = je.id
        LEFT JOIN chart_of_accounts coa ON ${accountJoin}
        WHERE 1=1
          ${statusClause}
          ${dateClause}
          ${accountWhere}
        GROUP BY je.entry_date
        ORDER BY je.entry_date ASC
      `

      const result = await query(sql, params)
      return NextResponse.json({
        success: true,
        filter: { report, account_id: accountId, client_id: clientId, employee_id: employeeId, date_from: dateFrom, date_to: dateTo, status },
        data: result.rows,
        total_days: result.rows?.length || 0
      })
    }

    // تقرير كشف الحساب التفصيلي
    const linesSql = `
      SELECT 
        je.entry_number,
        je.entry_date,
        je.description as entry_description,
        jed.id as line_id,
        jed.description as line_description,
        COALESCE(jed.debit_amount, 0) as debit,
        COALESCE(jed.credit_amount, 0) as credit,
        COALESCE(coa.account_name, jed.account_name) as account_name,
        coa.account_code,
        coa.id as account_id
      FROM journal_entries je
      JOIN journal_entry_details jed ON jed.journal_entry_id = je.id
      LEFT JOIN chart_of_accounts coa ON ${accountJoin}
      WHERE 1=1
        ${statusClause}
        ${dateClause}
        ${accountWhere}
      ORDER BY je.entry_date ASC, je.entry_number ASC, jed.id ASC
    `

    const linesRes = await query(linesSql, params)
    const lines = linesRes.rows || []

    // ملخص كشف الحساب
    let summaryData = {
      opening_debit: 0,
      opening_credit: 0,
      period_debit: 0,
      period_credit: 0,
      closing_debit: 0,
      closing_credit: 0,
      net_balance: 0,
      balance_type: 'متوازن'
    }

    // احتساب رصيد افتتاحي قبل date_from إذا توفر
    if (dateFrom) {
      const openingParams = [...params]
      // إعادة بناء معاملات التاريخ للرصد الافتتاحي: نحتاج حد تاريخ فقط (< dateFrom)
      let openIdx = 1
      const openStatusClause = status !== 'all' ? ` AND je.status = $${openIdx++}` : ''
      const openParams: any[] = []
      if (status !== 'all') openParams.push(status)
      openParams.push(dateFrom)

      let openAccountWhere = ''
      const lastFilterParam = (status !== 'all') ? 2 : 1
      if (accountId) {
        openAccountWhere = ` AND coa.id = $${lastFilterParam + 1}`
        openParams.push(parseInt(accountId))
      } else if (clientId) {
        openAccountWhere = ` AND coa.original_table = 'clients' AND coa.linked_record_id = $${lastFilterParam + 1}`
        openParams.push(parseInt(clientId))
      } else if (employeeId) {
        openAccountWhere = ` AND coa.original_table = 'employees' AND coa.linked_record_id = $${lastFilterParam + 1}`
        openParams.push(parseInt(employeeId))
      }

      const openingSql = `
        SELECT 
          COALESCE(SUM(jed.debit_amount), 0) as od,
          COALESCE(SUM(jed.credit_amount), 0) as oc
        FROM journal_entries je
        JOIN journal_entry_details jed ON jed.journal_entry_id = je.id
        LEFT JOIN chart_of_accounts coa ON jed.account_id = coa.id
        WHERE 1=1
          ${openStatusClause}
          AND je.entry_date < $${(status !== 'all') ? 2 : 1}
          ${openAccountWhere}
      `
      const openRes = await query(openingSql, openParams)
      const od = parseFloat(openRes.rows?.[0]?.od || 0)
      const oc = parseFloat(openRes.rows?.[0]?.oc || 0)
      summaryData.opening_debit = od
      summaryData.opening_credit = oc
    }

    // إجمالي الفترة
    const totalsSql = `
      SELECT 
        COALESCE(SUM(jed.debit_amount), 0) as pd,
        COALESCE(SUM(jed.credit_amount), 0) as pc
      FROM journal_entries je
      JOIN journal_entry_details jed ON jed.journal_entry_id = je.id
      LEFT JOIN chart_of_accounts coa ON ${accountJoin}
      WHERE 1=1
        ${statusClause}
        ${dateClause}
        ${accountWhere}
    `
    const totalsRes = await query(totalsSql, params)
    const pd = parseFloat(totalsRes.rows?.[0]?.pd || 0)
    const pc = parseFloat(totalsRes.rows?.[0]?.pc || 0)

    summaryData.period_debit = pd
    summaryData.period_credit = pc

    const closing = (summaryData.opening_debit - summaryData.opening_credit) + (pd - pc)
    summaryData.net_balance = Math.abs(closing)
    if (closing > 0) {
      summaryData.balance_type = 'مدين'
      summaryData.closing_debit = summaryData.net_balance
      summaryData.closing_credit = 0
    } else if (closing < 0) {
      summaryData.balance_type = 'دائن'
      summaryData.closing_credit = summaryData.net_balance
      summaryData.closing_debit = 0
    } else {
      summaryData.balance_type = 'متوازن'
      summaryData.closing_debit = 0
      summaryData.closing_credit = 0
    }

    return NextResponse.json({
      success: true,
      filter: { report, account_id: accountId, client_id: clientId, employee_id: employeeId, date_from: dateFrom, date_to: dateTo, status },
      summary: summary ? summaryData : undefined,
      lines,
      totals: { period_debit: summaryData.period_debit, period_credit: summaryData.period_credit },
      opening: dateFrom ? { debit: summaryData.opening_debit, credit: summaryData.opening_credit } : undefined,
      closing: { debit: summaryData.closing_debit, credit: summaryData.closing_credit, balance_type: summaryData.balance_type },
      count: lines.length
    })
  } catch (error: any) {
    console.error('❌ تقارير الحسابات - خطأ:', error)
    return NextResponse.json({ success: false, error: 'فشل في جلب تقرير الحسابات', details: error?.message || 'خطأ غير معروف' }, { status: 500 })
  }
}
