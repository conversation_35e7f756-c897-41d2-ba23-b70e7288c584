const { Client } = require('pg');

async function simpleTest() {
  console.log('🔍 اختبار بسيط لـ PostgreSQL...\n');

  const configs = [
    { host: 'localhost', port: 5432, user: 'postgres', password: 'yemen123' },
    { host: 'localhost', port: 5432, user: 'postgres', password: 'postgres' },
    { host: 'localhost', port: 5432, user: 'postgres', password: '' },
    { host: 'localhost', port: 5432, user: 'postgres', password: 'admin' },
    { host: 'localhost', port: 5432, user: 'postgres', password: '123456' },
    { host: 'localhost', port: 5433, user: 'postgres', password: 'yemen123' },
    { host: '127.0.0.1', port: 5432, user: 'postgres', password: 'yemen123' },
  ];

  for (let i = 0; i < configs.length; i++) {
    const config = configs[i];
    console.log(`${i + 1}. اختبار: ${config.user}@${config.host}:${config.port} (${config.password || 'فارغة'})`);
    
    const client = new Client({
      ...config,
      database: 'postgres',
      connectTimeoutMillis: 3000
    });

    try {
      await client.connect();
      console.log('   ✅ نجح الاتصال!');
      
      const result = await client.query('SELECT version()');
      console.log(`   📋 الإصدار: ${result.rows[0].version.split(' ')[1]}`);
      
      const dbResult = await client.query(`
        SELECT datname FROM pg_database 
        WHERE datistemplate = false 
        ORDER BY datname
      `);
      
      console.log(`   📊 قواعد البيانات (${dbResult.rows.length}):`);
      dbResult.rows.forEach(row => {
        console.log(`      - ${row.datname}`);
      });

      const targetDbs = ['mohammi', 'mohammidev'];
      const foundDbs = [];
      
      for (const targetDb of targetDbs) {
        const found = dbResult.rows.find(row => row.datname === targetDb);
        if (found) {
          foundDbs.push(targetDb);
          console.log(`   🎯 تم العثور على: ${targetDb}`);
        }
      }

      await client.end();
      
      console.log('\n🎉 تم العثور على اتصال صحيح!');
      console.log('📋 الإعدادات:');
      console.log(`   المضيف: ${config.host}`);
      console.log(`   المنفذ: ${config.port}`);
      console.log(`   المستخدم: ${config.user}`);
      console.log(`   كلمة المرور: ${config.password || 'فارغة'}`);
      
      if (foundDbs.length > 0) {
        console.log(`   قواعد البيانات المطلوبة: ${foundDbs.join(', ')}`);
      } else {
        console.log('   ⚠️ لم يتم العثور على mohammi أو mohammidev');
        console.log('   💡 يمكن إنشاؤها لاحقاً');
      }

      return {
        ...config,
        databases: dbResult.rows.map(row => row.datname),
        targetDatabases: foundDbs
      };
      
    } catch (error) {
      console.log(`   ❌ فشل: ${error.message}`);
    }
  }

  console.log('\n❌ لم يتم العثور على أي اتصال صحيح');
  console.log('\n🔍 تحليل المشكلة:');
  
  // اختبار أساسي للشبكة
  console.log('1. اختبار الشبكة المحلية...');
  try {
    const testClient = new Client({
      host: 'localhost',
      port: 5432,
      user: 'postgres',
      password: 'yemen123',
      database: 'postgres',
      connectTimeoutMillis: 1000
    });
    
    await testClient.connect();
    await testClient.end();
  } catch (error) {
    if (error.message.includes('ECONNREFUSED')) {
      console.log('   ❌ الخادم غير مشغل على المنفذ 5432');
    } else if (error.message.includes('ENOTFOUND')) {
      console.log('   ❌ لا يمكن الوصول لـ localhost');
    } else if (error.message.includes('authentication')) {
      console.log('   ⚠️ الخادم يعمل ولكن كلمة المرور خاطئة');
    } else if (error.message.includes('timeout')) {
      console.log('   ⚠️ الخادم بطيء في الاستجابة');
    } else {
      console.log(`   ❓ خطأ غير معروف: ${error.message}`);
    }
  }

  console.log('\n🛠️ الحلول المقترحة:');
  console.log('1. تحقق من تثبيت PostgreSQL:');
  console.log('   - ابحث عن "PostgreSQL" في قائمة البرامج');
  console.log('   - أو ابحث عن مجلد C:\\Program Files\\PostgreSQL');
  
  console.log('2. إذا كان مثبت، تحقق من الخدمة:');
  console.log('   - اضغط Win+R واكتب services.msc');
  console.log('   - ابحث عن postgresql وشغلها');
  
  console.log('3. إذا لم يكن مثبت:');
  console.log('   - حمل من: https://www.postgresql.org/download/');
  console.log('   - أو استخدم XAMPP مع PostgreSQL');
  console.log('   - أو استخدم Docker');

  return null;
}

simpleTest()
  .then(result => {
    if (result) {
      console.log('\n✅ الاختبار مكتمل بنجاح');
      
      // حفظ النتيجة
      const fs = require('fs');
      fs.writeFileSync('postgres-config.json', JSON.stringify(result, null, 2));
      console.log('💾 تم حفظ الإعدادات في postgres-config.json');
    } else {
      console.log('\n❌ الاختبار فشل - PostgreSQL غير متاح');
    }
  })
  .catch(error => {
    console.error('💥 خطأ في الاختبار:', error.message);
  });
