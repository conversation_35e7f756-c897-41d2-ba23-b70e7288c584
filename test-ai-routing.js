// اختبار توجيه المساعد الذكي
const http = require('http');

console.log('🤖 اختبار توجيه المساعد الذكي');
console.log('='.repeat(50));

// دالة اختبار API
function testAI(port, company) {
  return new Promise((resolve, reject) => {
    const data = JSON.stringify({
      query: "قانون العمل",
      topK: 3
    });

    const options = {
      hostname: 'localhost',
      port: port,
      path: '/api/legal-library/query',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': data.length,
        'Host': `localhost:${port}`
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const result = JSON.parse(responseData);
          console.log(`\n📊 نتائج ${company} (المنفذ ${port}):`);
          console.log(`   - الحالة: ${result.success ? '✅ نجح' : '❌ فشل'}`);
          if (result.success) {
            console.log(`   - قاعدة البيانات: ${result.database || 'غير محدد'}`);
            console.log(`   - الشركة: ${result.company || 'غير محدد'}`);
            console.log(`   - عدد النتائج: ${result.results?.length || 0}`);
            console.log(`   - إجمالي البيانات: ${result.total || 0}`);
          } else {
            console.log(`   - الخطأ: ${result.error}`);
          }
          resolve(result);
        } catch (error) {
          console.log(`   - خطأ في تحليل الاستجابة: ${error.message}`);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.log(`   - خطأ في الطلب: ${error.message}`);
      reject(error);
    });

    req.write(data);
    req.end();
  });
}

// تشغيل الاختبارات
async function runTests() {
  try {
    console.log('\n🔍 اختبار المنافذ المحلية:');
    
    // اختبار المنفذ 3000 (محمد)
    await testAI(3000, 'محمد');
    
    // اختبار المنفذ 3001 (الربعي)
    await testAI(3001, 'الربعي');
    
    console.log('\n🌐 اختبار المنافذ الخارجية:');
    
    // اختبار المنفذ 7443 (محمد - خارجي)
    await testAI(7443, 'محمد (خارجي)');
    
    // اختبار المنفذ 8914 (الربعي - خارجي)
    await testAI(8914, 'الربعي (خارجي)');
    
    console.log('\n✅ انتهت جميع الاختبارات');
    console.log('\n📋 ملخص التوجيه:');
    console.log('   - المنفذ 3000 → قاعدة بيانات mohammi (محمد)');
    console.log('   - المنفذ 3001 → قاعدة بيانات rubaie (الربعي)');
    console.log('   - المنفذ 7443 → قاعدة بيانات mohammi (محمد - خارجي)');
    console.log('   - المنفذ 8914 → قاعدة بيانات rubaie (الربعي - خارجي)');
    
  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
  }
}

// تشغيل الاختبارات
runTests();
