import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database-router'

// تسجيل دخول بسيط
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { username, password } = body

    console.log(`🔍 محاولة تسجيل دخول: ${username}`)

    // التحقق من البيانات المطلوبة
    if (!username || !password) {
      return NextResponse.json(
        { success: false, error: 'اسم المستخدم وكلمة المرور مطلوبان' },
        { status: 400 }
      )
    }

    // البحث عن المستخدم
    const userResult = await query(`
      SELECT u.*, e.name as employee_name
      FROM users u
      LEFT JOIN employees e ON u.employee_id = e.id
      WHERE u.username = $1
    `, [username])

    if (userResult.rows.length === 0) {
      console.log(`❌ المستخدم غير موجود: ${username}`)
      return NextResponse.json(
        { success: false, error: 'اسم المستخدم غير موجود' },
        { status: 401 }
      )
    }

    const user = userResult.rows[0]
    console.log(`✅ تم العثور على المستخدم: ${user.username}`)
    console.log(`   كلمة المرور المحفوظة: ${user.password_hash}`)

    // التحقق من كلمة المرور (يدعم البسيطة والمشفرة)
    let isPasswordValid = false

    // إذا كانت كلمة المرور مشفرة (تبدأ بـ $)
    if (user.password_hash && user.password_hash.startsWith('$')) {
      const bcrypt = require('bcryptjs')
      isPasswordValid = await bcrypt.compare(password, user.password_hash)
      console.log(`🔐 فحص كلمة مرور مشفرة: ${isPasswordValid}`)
    } else {
      // كلمة مرور بسيطة (للاختبار)
      isPasswordValid = password === user.password_hash
      console.log(`🔓 فحص كلمة مرور بسيطة: ${isPasswordValid}`)
    }

    if (!isPasswordValid) {
      console.log(`❌ كلمة المرور غير صحيحة للمستخدم: ${user.username}`)
      console.log(`   المدخلة: "${password}" | المحفوظة: "${user.password_hash}"`)
      return NextResponse.json(
        { success: false, error: 'كلمة المرور غير صحيحة' },
        { status: 401 }
      )
    }

    // التحقق من حالة المستخدم
    if (user.status !== 'active') {
      return NextResponse.json(
        { success: false, error: 'حساب المستخدم غير نشط' },
        { status: 403 }
      )
    }

    // إنشاء جلسة بسيطة
    const crypto = require('crypto')
    const sessionToken = crypto.randomBytes(32).toString('hex')

    // تحديث بيانات المستخدم
    await query(`
      UPDATE users
      SET last_login = CURRENT_TIMESTAMP,
          is_online = true,
          current_session_token = $2
      WHERE id = $1
    `, [user.id, sessionToken])

    console.log(`✅ تم تسجيل دخول المستخدم: ${user.username}`)

    // إرجاع بيانات المستخدم
    const { password_hash, ...userWithoutPassword } = user

    return NextResponse.json({
      success: true,
      user: userWithoutPassword,
      sessionToken: sessionToken,
      message: 'تم تسجيل الدخول بنجاح'
    })

  } catch (error) {
    console.error('❌ خطأ في تسجيل الدخول:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}
