import { NextRequest, NextResponse } from 'next/server'
import { exec } from 'child_process'
import { promisify } from 'util'

const execAsync = promisify(exec)

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 تشغيل سكريبت إعداد قاعدة البيانات...')

    // تشغيل السكريبت
    const { stdout, stderr } = await execAsync('node create-case-movements-mohammidev.js', {
      cwd: process.cwd(),
      timeout: 30000 // 30 ثانية
    })

    console.log('✅ تم تشغيل السكريبت بنجاح')
    console.log('stdout:', stdout)
    
    if (stderr) {
      console.warn('stderr:', stderr)
    }

    return NextResponse.json({
      success: true,
      message: 'تم إعداد قاعدة البيانات بنجاح',
      output: stdout,
      error: stderr || null
    })

  } catch (error: any) {
    console.error('❌ خطأ في تشغيل السكريبت:', error)
    
    return NextResponse.json({
      success: false,
      error: 'فشل في تشغيل سكريبت الإعداد',
      details: error.message,
      output: error.stdout || null,
      stderr: error.stderr || null
    }, { status: 500 })
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'استخدم POST لتشغيل سكريبت إعداد قاعدة البيانات',
    endpoint: '/api/run-setup-script',
    method: 'POST'
  })
}
