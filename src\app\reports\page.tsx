'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Bar<PERSON>hart3,
  <PERSON><PERSON><PERSON>,
  TrendingUp,
  TrendingDown,
  Calendar,
  Download,
  Filter,
  FileText,
  Users,
  DollarSign,
  Clock,
  Target,
  Award,
  AlertTriangle,
  CheckCircle,
  Eye,
  Printer,
  Share2,
  RefreshCw
} from 'lucide-react'

interface ReportData {
  summary: {
    total_cases: number
    active_cases: number
    completed_cases: number
    total_clients: number
    total_revenue: number
    pending_invoices: number
    total_hours: number
    billable_hours: number
  }
  monthly_stats: Array<{
    month: string
    cases: number
    revenue: number
    hours: number
  }>
  case_types: Array<{
    type: string
    count: number
    percentage: number
  }>
  client_stats: Array<{
    client_name: string
    cases_count: number
    total_amount: number
    last_activity: string
  }>
  employee_performance: Array<{
    employee_name: string
    cases_handled: number
    hours_worked: number
    revenue_generated: number
    efficiency_score: number
  }>
  financial_overview: {
    monthly_revenue: Array<{
      month: string
      revenue: number
      expenses: number
      profit: number
    }>
    payment_status: {
      paid: number
      pending: number
      overdue: number
    }
  }
}

export default function ReportsPage() {
  const [data, setData] = useState<ReportData | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedPeriod, setSelectedPeriod] = useState('last_6_months')
  const [selectedReport, setSelectedReport] = useState('overview')
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 6 * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  })

  // جلب بيانات التقارير
  const fetchReportData = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        period: selectedPeriod,
        start_date: dateRange.start,
        end_date: dateRange.end,
        report_type: selectedReport
      })

      const response = await fetch(`/api/reports?${params}`)
      const result = await response.json()

      if (result.success) {
        setData(result.data)
      }
    } catch (error) {
      console.error('خطأ في جلب بيانات التقارير:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchReportData()
  }, [selectedPeriod, selectedReport, dateRange])

  // تنسيق المبلغ
  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  // تنسيق الوقت
  const formatHours = (hours: number) => {
    return `${hours.toFixed(1)} ساعة`
  }

  // تنسيق النسبة المئوية
  const formatPercentage = (percentage: number) => {
    return `${percentage.toFixed(1)}%`
  }

  // تصدير التقرير
  const exportReport = async (format: 'pdf' | 'excel') => {
    try {
      const response = await fetch('/api/reports/export', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          format,
          report_type: selectedReport,
          period: selectedPeriod,
          start_date: dateRange.start,
          end_date: dateRange.end
        })
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `report_${selectedReport}_${Date.now()}.${format}`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      }
    } catch (error) {
      console.error('خطأ في تصدير التقرير:', error)
      alert('فشل في تصدير التقرير')
    }
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان والأدوات */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">التقارير والإحصائيات</h1>
            <p className="text-gray-600 mt-1">تحليل شامل لأداء المكتب والعمليات</p>
          </div>
          <div className="flex gap-3">
            <Button onClick={() => fetchReportData()} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              تحديث
            </Button>
            <Button onClick={() => exportReport('pdf')} variant="outline">
              <Download className="h-4 w-4 mr-2" />
              PDF
            </Button>
            <Button onClick={() => exportReport('excel')} variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Excel
            </Button>
            <Link href="/reports/cases" className="inline-flex items-center px-3 py-2 border rounded-md text-sm hover:bg-gray-50">
              تقارير القضايا
            </Link>
            <Link href="/reports/accounts" className="inline-flex items-center px-3 py-2 border rounded-md text-sm hover:bg-gray-50">
              تقارير الحسابات
            </Link>
          </div>
        </div>

        {/* أدوات التصفية */}
        <Card>
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">نوع التقرير</label>
                <select
                  value={selectedReport}
                  onChange={(e) => setSelectedReport(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="overview">نظرة عامة</option>
                  <option value="financial">التقرير المالي</option>
                  <option value="cases">تقرير القضايا</option>
                  <option value="clients">تقرير العملاء</option>
                  <option value="performance">تقرير الأداء</option>
                  <option value="time_tracking">تقرير الوقت</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">الفترة الزمنية</label>
                <select
                  value={selectedPeriod}
                  onChange={(e) => setSelectedPeriod(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="last_month">الشهر الماضي</option>
                  <option value="last_3_months">آخر 3 أشهر</option>
                  <option value="last_6_months">آخر 6 أشهر</option>
                  <option value="last_year">السنة الماضية</option>
                  <option value="custom">فترة مخصصة</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">من تاريخ</label>
                <Input
                  type="date"
                  value={dateRange.start}
                  onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">إلى تاريخ</label>
                <Input
                  type="date"
                  value={dateRange.end}
                  onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">جاري تحميل التقارير...</p>
          </div>
        ) : data ? (
          <div className="space-y-6">
            {/* الملخص العام */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <FileText className="h-8 w-8 text-blue-600" />
                    <div className="mr-4">
                      <div className="text-2xl font-bold">{data.summary.total_cases}</div>
                      <div className="text-sm text-gray-600">إجمالي القضايا</div>
                      <div className="text-xs text-green-600 mt-1">
                        {data.summary.active_cases} نشطة
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Users className="h-8 w-8 text-green-600" />
                    <div className="mr-4">
                      <div className="text-2xl font-bold">{data.summary.total_clients}</div>
                      <div className="text-sm text-gray-600">إجمالي العملاء</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <DollarSign className="h-8 w-8 text-purple-600" />
                    <div className="mr-4">
                      <div className="text-2xl font-bold">{formatAmount(data.summary.total_revenue)}</div>
                      <div className="text-sm text-gray-600">إجمالي الإيرادات</div>
                      <div className="text-xs text-orange-600 mt-1">
                        {data.summary.pending_invoices} فاتورة معلقة
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Clock className="h-8 w-8 text-orange-600" />
                    <div className="mr-4">
                      <div className="text-2xl font-bold">{formatHours(data.summary.total_hours)}</div>
                      <div className="text-sm text-gray-600">إجمالي الساعات</div>
                      <div className="text-xs text-blue-600 mt-1">
                        {formatHours(data.summary.billable_hours)} قابلة للفوترة
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* الإحصائيات الشهرية */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2" />
                  الأداء الشهري
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {data.monthly_stats.map((stat, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                          <Calendar className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-semibold">{stat.month}</h4>
                          <p className="text-sm text-gray-600">{stat.cases} قضية</p>
                        </div>
                      </div>
                      <div className="text-left">
                        <div className="text-lg font-bold">{formatAmount(stat.revenue)}</div>
                        <div className="text-sm text-gray-600">{formatHours(stat.hours)}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* أنواع القضايا */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <PieChart className="h-5 w-5 mr-2" />
                    توزيع أنواع القضايا
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {data.case_types.map((type, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div 
                            className="w-4 h-4 rounded-full"
                            style={{ backgroundColor: `hsl(${index * 60}, 70%, 50%)` }}
                          ></div>
                          <span className="font-medium">{type.type}</span>
                        </div>
                        <div className="text-left">
                          <span className="font-bold">{type.count}</span>
                          <span className="text-sm text-gray-600 mr-2">
                            ({formatPercentage(type.percentage)})
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* أداء الموظفين */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Award className="h-5 w-5 mr-2" />
                    أداء الموظفين
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {data.employee_performance.map((employee, index) => (
                      <div key={index} className="p-3 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-semibold">{employee.employee_name}</h4>
                          <Badge className={
                            employee.efficiency_score >= 90 ? 'bg-green-100 text-green-800' :
                            employee.efficiency_score >= 70 ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }>
                            {formatPercentage(employee.efficiency_score)}
                          </Badge>
                        </div>
                        <div className="grid grid-cols-3 gap-2 text-sm">
                          <div>
                            <span className="text-gray-600">القضايا:</span>
                            <span className="font-medium mr-1">{employee.cases_handled}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">الساعات:</span>
                            <span className="font-medium mr-1">{formatHours(employee.hours_worked)}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">الإيرادات:</span>
                            <span className="font-medium mr-1">{formatAmount(employee.revenue_generated)}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* إحصائيات العملاء */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="h-5 w-5 mr-2" />
                  أهم العملاء
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-right py-3 px-4">اسم العميل</th>
                        <th className="text-right py-3 px-4">عدد القضايا</th>
                        <th className="text-right py-3 px-4">إجمالي المبلغ</th>
                        <th className="text-right py-3 px-4">آخر نشاط</th>
                        <th className="text-right py-3 px-4">الإجراءات</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.client_stats.map((client, index) => (
                        <tr key={index} className="border-b hover:bg-gray-50">
                          <td className="py-3 px-4 font-medium">{client.client_name}</td>
                          <td className="py-3 px-4">{client.cases_count}</td>
                          <td className="py-3 px-4">{formatAmount(client.total_amount)}</td>
                          <td className="py-3 px-4 text-sm text-gray-600">
                            {new Date(client.last_activity).toLocaleDateString('ar-SA')}
                          </td>
                          <td className="py-3 px-4">
                            <Button size="sm" variant="outline">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>

            {/* النظرة المالية */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2" />
                  النظرة المالية
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* الإيرادات الشهرية */}
                  <div>
                    <h4 className="font-semibold mb-4">الإيرادات والأرباح الشهرية</h4>
                    <div className="space-y-3">
                      {data.financial_overview.monthly_revenue.map((month, index) => (
                        <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                          <span className="font-medium">{month.month}</span>
                          <div className="text-left">
                            <div className="text-green-600 font-bold">
                              {formatAmount(month.revenue)}
                            </div>
                            <div className="text-sm text-gray-600">
                              ربح: {formatAmount(month.profit)}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* حالة المدفوعات */}
                  <div>
                    <h4 className="font-semibold mb-4">حالة المدفوعات</h4>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <CheckCircle className="h-6 w-6 text-green-600" />
                          <span className="font-medium">مدفوعة</span>
                        </div>
                        <span className="font-bold text-green-600">
                          {formatAmount(data.financial_overview.payment_status.paid)}
                        </span>
                      </div>
                      
                      <div className="flex items-center justify-between p-4 bg-yellow-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <Clock className="h-6 w-6 text-yellow-600" />
                          <span className="font-medium">معلقة</span>
                        </div>
                        <span className="font-bold text-yellow-600">
                          {formatAmount(data.financial_overview.payment_status.pending)}
                        </span>
                      </div>
                      
                      <div className="flex items-center justify-between p-4 bg-red-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <AlertTriangle className="h-6 w-6 text-red-600" />
                          <span className="font-medium">متأخرة</span>
                        </div>
                        <span className="font-bold text-red-600">
                          {formatAmount(data.financial_overview.payment_status.overdue)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        ) : (
          <div className="text-center py-12">
            <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">لا توجد بيانات متاحة للفترة المحددة</p>
          </div>
        )}
      </div>
    </MainLayout>
  )
}