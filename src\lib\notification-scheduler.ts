import { query } from '@/lib/db'
import { 
  addCaseMovement, 
  addCaseCreatedMovement, 
  addCaseAssignedMovement,
  addHearingScheduledMovement,
  addFollowAddedMovement,
  addDocumentUploadedMovement,
  addCaseStatusChangedMovement,
  MOVEMENT_TYPES 
} from '@/lib/case-movements'
import { scheduleNotification, scheduleHearingNotifications } from '@/lib/notifications'

// دالة لإضافة حركة إنشاء قضية وجدولة التنبيهات
export async function handleCaseCreated(
  caseId: number,
  createdBy?: number,
  createdByName?: string
) {
  try {
    // إضافة حركة إنشاء القضية
    await addCaseCreatedMovement(caseId, createdBy, createdByName)

    // جدولة تنبيه للمدير بإنشاء قضية جديدة
    const managers = await query(`
      SELECT id FROM employees WHERE role = 'manager' OR role = 'admin'
    `)

    for (const manager of managers.rows) {
      await scheduleNotification({
        case_id: caseId,
        recipient_id: manager.id,
        recipient_type: 'employee',
        template_name: 'case_created_notification',
        scheduled_for: new Date(), // فوري
        priority: 'normal'
      })
    }

    console.log('تم التعامل مع إنشاء القضية وجدولة التنبيهات')
  } catch (error) {
    console.error('خطأ في التعامل مع إنشاء القضية:', error)
  }
}

// دالة لإضافة حركة توزيع قضية وجدولة التنبيهات
export async function handleCaseAssigned(
  caseId: number,
  assignedToId: number,
  assignedToName: string,
  assignedBy?: number,
  assignedByName?: string
) {
  try {
    // إضافة حركة توزيع القضية
    await addCaseAssignedMovement(caseId, assignedToName, assignedBy, assignedByName)

    // جدولة تنبيه للموظف المعين
    await scheduleNotification({
      case_id: caseId,
      recipient_id: assignedToId,
      recipient_type: 'employee',
      template_name: 'case_assigned_notification',
      scheduled_for: new Date(), // فوري
      priority: 'high'
    })

    console.log('تم التعامل مع توزيع القضية وجدولة التنبيهات')
  } catch (error) {
    console.error('خطأ في التعامل مع توزيع القضية:', error)
  }
}

// دالة لإضافة حركة جدولة جلسة وجدولة التنبيهات
export async function handleHearingScheduled(
  caseId: number,
  hearingDate: Date,
  hearingTime: string,
  courtName: string,
  scheduledBy?: number,
  scheduledByName?: string
) {
  try {
    // إضافة حركة جدولة الجلسة
    await addHearingScheduledMovement(
      caseId,
      hearingDate.toISOString().split('T')[0],
      hearingTime,
      courtName,
      scheduledBy,
      scheduledByName
    )

    // جدولة تنبيهات الجلسة للعميل
    await scheduleHearingNotifications(caseId, hearingDate, courtName)

    // جدولة تنبيه للموظف المسؤول
    const caseData = await query(`
      SELECT assigned_employee_id FROM issues WHERE id = $1
    `, [caseId])

    if (caseData.rows.length > 0 && caseData.rows[0].assigned_employee_id) {
      // تنبيه قبل يوم واحد للموظف
      const oneDayBefore = new Date(hearingDate)
      oneDayBefore.setDate(oneDayBefore.getDate() - 1)
      oneDayBefore.setHours(8, 0, 0, 0) // 8 صباحاً

      await scheduleNotification({
        case_id: caseId,
        recipient_id: caseData.rows[0].assigned_employee_id,
        recipient_type: 'employee',
        template_name: 'employee_hearing_reminder',
        scheduled_for: oneDayBefore,
        variables: {
          hearing_date: hearingDate.toLocaleDateString('ar-SA'),
          hearing_time: hearingTime,
          court_name: courtName
        },
        priority: 'high'
      })
    }

    console.log('تم التعامل مع جدولة الجلسة وجدولة التنبيهات')
  } catch (error) {
    console.error('خطأ في التعامل مع جدولة الجلسة:', error)
  }
}

// دالة لإضافة حركة إضافة متابعة وجدولة التنبيهات
export async function handleFollowAdded(
  caseId: number,
  followDescription: string,
  createdBy?: number,
  createdByName?: string,
  referenceId?: number
) {
  try {
    // إضافة حركة إضافة المتابعة
    await addFollowAddedMovement(caseId, followDescription, createdBy, createdByName, referenceId)

    // جدولة تنبيه للعميل بإضافة متابعة جديدة
    const caseData = await query(`
      SELECT client_id FROM issues WHERE id = $1
    `, [caseId])

    if (caseData.rows.length > 0 && caseData.rows[0].client_id) {
      await scheduleNotification({
        case_id: caseId,
        recipient_id: caseData.rows[0].client_id,
        recipient_type: 'client',
        template_name: 'follow_added_notification',
        scheduled_for: new Date(), // فوري
        variables: {
          follow_description: followDescription.substring(0, 100) + '...'
        },
        priority: 'normal'
      })
    }

    console.log('تم التعامل مع إضافة المتابعة وجدولة التنبيهات')
  } catch (error) {
    console.error('خطأ في التعامل مع إضافة المتابعة:', error)
  }
}

// دالة لإضافة حركة رفع وثيقة وجدولة التنبيهات
export async function handleDocumentUploaded(
  caseId: number,
  documentTitle: string,
  createdBy?: number,
  createdByName?: string,
  referenceId?: number
) {
  try {
    // إضافة حركة رفع الوثيقة
    await addDocumentUploadedMovement(caseId, documentTitle, createdBy, createdByName, referenceId)

    // جدولة تنبيه للعميل برفع وثيقة جديدة
    const caseData = await query(`
      SELECT client_id, assigned_employee_id FROM issues WHERE id = $1
    `, [caseId])

    if (caseData.rows.length > 0) {
      const { client_id, assigned_employee_id } = caseData.rows[0]

      // تنبيه للعميل
      if (client_id) {
        await scheduleNotification({
          case_id: caseId,
          recipient_id: client_id,
          recipient_type: 'client',
          template_name: 'document_uploaded',
          scheduled_for: new Date(), // فوري
          variables: {
            document_title: documentTitle
          },
          priority: 'normal'
        })
      }

      // تنبيه للموظف المسؤول (إذا لم يكن هو من رفع الوثيقة)
      if (assigned_employee_id && assigned_employee_id !== createdBy) {
        await scheduleNotification({
          case_id: caseId,
          recipient_id: assigned_employee_id,
          recipient_type: 'employee',
          template_name: 'document_uploaded_employee',
          scheduled_for: new Date(), // فوري
          variables: {
            document_title: documentTitle,
            uploaded_by: createdByName || 'مستخدم'
          },
          priority: 'normal'
        })
      }
    }

    console.log('تم التعامل مع رفع الوثيقة وجدولة التنبيهات')
  } catch (error) {
    console.error('خطأ في التعامل مع رفع الوثيقة:', error)
  }
}

// دالة لإضافة حركة تغيير حالة القضية وجدولة التنبيهات
export async function handleCaseStatusChanged(
  caseId: number,
  oldStatus: string,
  newStatus: string,
  changedBy?: number,
  changedByName?: string
) {
  try {
    // إضافة حركة تغيير الحالة
    await addCaseStatusChangedMovement(caseId, oldStatus, newStatus, changedBy, changedByName)

    // جدولة تنبيه للعميل بتغيير حالة القضية
    const caseData = await query(`
      SELECT client_id, assigned_employee_id FROM issues WHERE id = $1
    `, [caseId])

    if (caseData.rows.length > 0) {
      const { client_id, assigned_employee_id } = caseData.rows[0]

      // تنبيه للعميل
      if (client_id) {
        const priority = ['closed', 'completed', 'won', 'lost'].includes(newStatus) ? 'high' : 'normal'
        
        await scheduleNotification({
          case_id: caseId,
          recipient_id: client_id,
          recipient_type: 'client',
          template_name: 'case_status_changed',
          scheduled_for: new Date(), // فوري
          variables: {
            old_status: oldStatus,
            new_status: newStatus
          },
          priority
        })
      }

      // تنبيه للمدير إذا تم إغلاق القضية
      if (['closed', 'completed'].includes(newStatus)) {
        const managers = await query(`
          SELECT id FROM employees WHERE role = 'manager' OR role = 'admin'
        `)

        for (const manager of managers.rows) {
          await scheduleNotification({
            case_id: caseId,
            recipient_id: manager.id,
            recipient_type: 'employee',
            template_name: 'case_closed_notification',
            scheduled_for: new Date(), // فوري
            variables: {
              old_status: oldStatus,
              new_status: newStatus,
              changed_by: changedByName || 'مستخدم'
            },
            priority: 'normal'
          })
        }
      }
    }

    console.log('تم التعامل مع تغيير حالة القضية وجدولة التنبيهات')
  } catch (error) {
    console.error('خطأ في التعامل مع تغيير حالة القضية:', error)
  }
}

// دالة لفحص القضايا الخاملة وإرسال تنبيهات
export async function checkInactiveCases(daysSinceLastMovement: number = 30) {
  try {
    const inactiveCases = await query(`
      SELECT 
        i.id,
        i.case_number,
        i.title,
        i.client_name,
        i.assigned_employee_id,
        i.status,
        MAX(cm.created_at) as last_movement_date,
        EXTRACT(DAYS FROM (CURRENT_DATE - MAX(cm.created_at)::date)) as days_since_last_movement
      FROM issues i
      LEFT JOIN case_movements cm ON i.id = cm.case_id
      WHERE i.status NOT IN ('closed', 'completed', 'cancelled')
      GROUP BY i.id, i.case_number, i.title, i.client_name, i.assigned_employee_id, i.status
      HAVING MAX(cm.created_at) < CURRENT_DATE - INTERVAL '${daysSinceLastMovement} days'
        OR MAX(cm.created_at) IS NULL
    `)

    for (const caseData of inactiveCases.rows) {
      // إضافة حركة تنبيه بالخمول
      await addCaseMovement({
        case_id: caseData.id,
        movement_type: 'case_inactive_alert',
        description: `تنبيه: القضية خاملة منذ ${caseData.days_since_last_movement || 'أكثر من'} ${daysSinceLastMovement} يوم`,
        created_by_name: 'النظام',
        priority: 'high'
      })

      // تنبيه للموظف المسؤول
      if (caseData.assigned_employee_id) {
        await scheduleNotification({
          case_id: caseData.id,
          recipient_id: caseData.assigned_employee_id,
          recipient_type: 'employee',
          template_name: 'case_inactive_30_days',
          scheduled_for: new Date(), // فوري
          variables: {
            days_inactive: caseData.days_since_last_movement || daysSinceLastMovement
          },
          priority: 'high'
        })
      }

      // تنبيه للمدير
      const managers = await query(`
        SELECT id FROM employees WHERE role = 'manager' OR role = 'admin'
      `)

      for (const manager of managers.rows) {
        await scheduleNotification({
          case_id: caseData.id,
          recipient_id: manager.id,
          recipient_type: 'employee',
          template_name: 'case_inactive_manager_alert',
          scheduled_for: new Date(), // فوري
          variables: {
            days_inactive: caseData.days_since_last_movement || daysSinceLastMovement,
            assigned_employee: caseData.assigned_employee_name || 'غير محدد'
          },
          priority: 'urgent'
        })
      }
    }

    console.log(`تم فحص ${inactiveCases.rows.length} قضية خاملة وإرسال التنبيهات`)
    return inactiveCases.rows.length
  } catch (error) {
    console.error('خطأ في فحص القضايا الخاملة:', error)
    return 0
  }
}

// دالة لتشغيل جميع المهام المجدولة
export async function runScheduledTasks() {
  try {
    console.log('🔄 بدء تشغيل المهام المجدولة...')

    // فحص القضايا الخاملة (30 يوم)
    const inactiveCount = await checkInactiveCases(30)
    
    // فحص القضايا الخاملة جداً (60 يوم)
    const veryInactiveCount = await checkInactiveCases(60)

    console.log(`✅ تم الانتهاء من المهام المجدولة:`)
    console.log(`   - قضايا خاملة (30 يوم): ${inactiveCount}`)
    console.log(`   - قضايا خاملة جداً (60 يوم): ${veryInactiveCount}`)

    return {
      inactive_30_days: inactiveCount,
      inactive_60_days: veryInactiveCount
    }
  } catch (error) {
    console.error('خطأ في تشغيل المهام المجدولة:', error)
    return null
  }
}
