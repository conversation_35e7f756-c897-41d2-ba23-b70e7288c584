import { NextRequest, NextResponse } from 'next/server'
import { query, getDatabaseName } from '@/lib/database-router'

// GET - جلب توزيعات القضايا
export async function GET() {
  try {
    console.log('🔄 API: بداية جلب توزيعات القضايا')
    console.log('   قاعدة البيانات:', getDatabaseName())

    // جلب توزيعات القضايا مع بيانات القضايا
    const result = await query(`
      SELECT
        cd.id,
        cd.issue_id,
        cd.admin_amount,
        cd.remaining_amount,
        cd.created_date,
        i.title as issue_title,
        i.case_number,
        i.case_amount,
        i.description as issue_description,
        i.client_name,
        i.court_name,
        i.status
      FROM case_distribution cd
      LEFT JOIN issues i ON cd.issue_id = i.id
      ORDER BY cd.created_date DESC
    `)

    console.log(`📊 تم جلب ${result.rows.length} توزيع من قاعدة البيانات`)

    // تحويل البيانات للتنسيق المطلوب
    const distributions = result.rows.map(dist => {
      const caseAmount = parseFloat(dist.case_amount || 0)
      const adminAmount = parseFloat(dist.admin_amount || 0)
      const adminPercentage = caseAmount > 0 ? (adminAmount / caseAmount) * 100 : 0

      return {
        id: dist.id,
        issue_id: dist.issue_id,
        issue_title: dist.issue_title || 'قضية غير محددة',
        case_number: dist.case_number || 'غير محدد',
        case_amount: caseAmount,
        admin_amount: adminAmount,
        remaining_amount: parseFloat(dist.remaining_amount || 0),
        lineage_id: null,
        lineage_name: 'غير محدد',
        admin_percentage: Math.round(adminPercentage * 100) / 100,
        commission_percentage: 0,
        created_date: dist.created_date,
        client_name: dist.client_name || 'غير محدد',
        court_name: dist.court_name || 'غير محدد',
        status: dist.status || 'pending',
        service_distributions: [] // سيتم جلبها لاحقاً إذا لزم الأمر
      }
    })

    console.log(`✅ تم إرجاع ${distributions.length} توزيع`)
    return NextResponse.json({
      success: true,
      data: distributions,
      message: `تم جلب ${distributions.length} توزيع قضايا`
    })
  } catch (error) {
    console.error('❌ خطأ في جلب توزيعات القضايا:', error)

    return NextResponse.json(
      {
        success: false,
        error: `فشل في جلب بيانات توزيع القضايا: ${error.message}`
      },
      { status: 500 }
    )
  }
}

// POST - إضافة توزيع قضية جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    console.log('📝 إضافة توزيع جديد:', body)

    const {
      issue_id,
      lineage_id,
      admin_amount,
      remaining_amount,
      court_id,
      service_distributions = []
    } = body

    // التحقق من البيانات المطلوبة
    if (!issue_id || !lineage_id || !admin_amount) {
      return NextResponse.json(
        { success: false, error: 'البيانات المطلوبة مفقودة' },
        { status: 400 }
      )
    }

    // إدراج التوزيع الرئيسي
    console.log('💾 إدراج التوزيع الرئيسي...')
    const distributionResult = await query(`
      INSERT INTO case_distribution (
        issue_id,
        lineage_id,
        admin_amount,
        remaining_amount,
        court_id,
        created_date
      ) VALUES ($1, $2, $3, $4, $5, NOW())
      RETURNING *
    `, [issue_id, lineage_id, admin_amount, remaining_amount, court_id])

    console.log('✅ تم إدراج التوزيع الرئيسي بنجاح')

    const distributionId = distributionResult.rows[0].id

    // إدراج توزيعات الخدمات
    console.log('📋 service_distributions المستلمة:', service_distributions)
    for (const service of service_distributions) {
      console.log('🔍 معالجة خدمة:', {
        service_id: service.service_id,
        employee_id: service.employee_id,
        percentage: service.percentage,
        amount: service.amount,
        amount_client: service.amount_client
      })

      if (service.service_id && service.employee_id) {
        console.log('💾 حفظ خدمة في قاعدة البيانات...')
        try {
          await query(`
            INSERT INTO service_distributions (
              case_distribution_id,
              service_id,
              percentage,
              amount,
              emp_id
            ) VALUES ($1, $2, $3, $4, $5)
          `, [
            distributionId,
            service.service_id,
            service.percentage || 0,
            service.amount || 0,
            service.employee_id
          ])
          console.log('✅ تم حفظ الخدمة بنجاح')
        } catch (serviceError) {
          console.error('❌ خطأ في حفظ الخدمة:', serviceError)
          throw serviceError
        }
      } else {
        console.log('⚠️ تم تخطي الخدمة - بيانات مفقودة:', {
          service_id: service.service_id,
          employee_id: service.employee_id
        })
      }
    }

    return NextResponse.json({
      success: true,
      data: { id: distributionId, ...body },
      message: 'تم إضافة توزيع القضية بنجاح'
    })
  } catch (error) {
    console.error('❌ خطأ في إضافة توزيع القضية:', error)
    console.error('❌ تفاصيل الخطأ:', error.stack)
    return NextResponse.json(
      {
        success: false,
        error: 'فشل في إضافة توزيع القضية: ' + error.message,
        details: error.stack
      },
      { status: 500 }
    )
  }
}

// PUT - تحديث توزيع قضية
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    console.log('📝 تحديث توزيع:', body)

    // إرجاع نجاح مؤقت للاختبار
    return NextResponse.json({
      success: true,
      message: 'تم تحديث توزيع القضية بنجاح (تجريبي)'
    })
  } catch (error) {
    console.error('❌ خطأ في تحديث توزيع القضية:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث توزيع القضية' },
      { status: 500 }
    )
  }
}

// DELETE - حذف توزيع قضية
export async function DELETE(request: NextRequest) {
  try {
    console.log('🗑️ حذف توزيع')

    // إرجاع نجاح مؤقت للاختبار
    return NextResponse.json({
      success: true,
      message: 'تم حذف توزيع القضية بنجاح (تجريبي)'
    })
  } catch (error) {
    console.error('❌ خطأ في حذف توزيع القضية:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف توزيع القضية' },
      { status: 500 }
    )
  }
}
