#!/usr/bin/env node
/**
 * Recreate a clean FK: employees.department_id -> <schema>.departments(id)
 * - Detects the schema where departments table resides (prefers public)
 * - Drops ANY existing FK on employees.department_id
 * - Creates a single FK named fk_employees_department to the detected schema
 */
const fs = require('fs');
const path = require('path');
const { Client } = require('pg');

function loadConfig() {
  const rcPath = path.join(process.cwd(), 'routing.config.json');
  const rc = fs.existsSync(rcPath) ? JSON.parse(fs.readFileSync(rcPath, 'utf8')) : {};
  const dc = rc.default_config || {};
  return {
    host: process.env.DB_HOST || dc.db_host || 'localhost',
    port: Number(process.env.DB_PORT || dc.db_port || 5432),
    user: process.env.DB_USER || dc.db_user || 'postgres',
    password: process.env.DB_PASSWORD || dc.db_password || 'postgres',
    database: process.env.DB_NAME || dc.db_name || 'mohammi',
  };
}

async function run() {
  const conn = loadConfig();
  const client = new Client(conn);
  await client.connect();
  const exec = (sql, params=[]) => client.query(sql, params);

  try {
    // 0) Detect departments schema
    const sch = await exec(`
      SELECT table_schema
      FROM information_schema.tables
      WHERE table_name = 'departments'
      ORDER BY CASE WHEN table_schema='public' THEN 0 ELSE 1 END, table_schema
      LIMIT 1
    `);
    const depSchema = (sch.rows[0] && sch.rows[0].table_schema) || 'public';

    // 1) Ensure employees table exists (no-op if exists)
    await exec(`CREATE TABLE IF NOT EXISTS employees (id SERIAL PRIMARY KEY)`);

    // 2) Drop all existing FK on employees.department_id
    await exec(`
      DO $$
      DECLARE r RECORD; BEGIN
        FOR r IN
          SELECT c.conname
          FROM pg_constraint c
          JOIN pg_class t ON t.oid = c.conrelid AND t.relname = 'employees'
          JOIN pg_namespace n ON n.oid = t.relnamespace AND n.nspname = 'public'
          WHERE c.contype = 'f' AND c.conkey = ARRAY(
            SELECT attnum FROM pg_attribute WHERE attrelid = t.oid AND attname = 'department_id'
          )
        LOOP
          BEGIN
            EXECUTE 'ALTER TABLE employees DROP CONSTRAINT ' || quote_ident(r.conname);
          EXCEPTION WHEN undefined_object THEN NULL; END;
        END LOOP;
      END $$;
    `);

    // 3) Create the canonical FK against detected schema
    await exec(`
      ALTER TABLE employees
      ADD CONSTRAINT fk_employees_department FOREIGN KEY (department_id)
      REFERENCES ${depSchema}.departments(id)
      ON UPDATE CASCADE
      ON DELETE SET NULL;
    `);

    console.log(`FK recreated successfully to ${depSchema}.departments(id)`);
  } catch (e) {
    console.error('FK recreation failed:', e.message);
    process.exitCode = 1;
  } finally {
    await client.end().catch(()=>{});
  }
}

run();
