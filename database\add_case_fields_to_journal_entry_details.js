const { Client } = require('pg');

async function addCaseFields() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: process.env.PGDATABASE || 'mohammi',
    user: process.env.PGUSER || 'postgres',
    password: process.env.PGPASSWORD || 'yemen123',
  });

  try {
    await client.connect();
    console.log('🔗 متصل بقاعدة البيانات');

    // التأكد من وجود جدول التفاصيل
    const tableCheck = await client.query(`
      SELECT to_regclass('public.journal_entry_details') as t
    `);
    if (!tableCheck.rows[0].t) {
      throw new Error('جدول journal_entry_details غير موجود. يرجى إنشاء الجداول المحاسبية أولاً.');
    }

    console.log('🛠️ إضافة الأعمدة case_id و case_number و issues_id و service_id إن لم تكن موجودة...');
    await client.query(`
      DO $$
      BEGIN
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.columns
          WHERE table_name='journal_entry_details' AND column_name='case_id'
        ) THEN
          ALTER TABLE journal_entry_details ADD COLUMN case_id INTEGER;
        END IF;

        IF NOT EXISTS (
          SELECT 1 FROM information_schema.columns
          WHERE table_name='journal_entry_details' AND column_name='case_number'
        ) THEN
          ALTER TABLE journal_entry_details ADD COLUMN case_number VARCHAR(100);
        END IF;

        IF NOT EXISTS (
          SELECT 1 FROM information_schema.columns
          WHERE table_name='journal_entry_details' AND column_name='issues_id'
        ) THEN
          ALTER TABLE journal_entry_details ADD COLUMN issues_id INTEGER;
        END IF;

        IF NOT EXISTS (
          SELECT 1 FROM information_schema.columns
          WHERE table_name='journal_entry_details' AND column_name='service_id'
        ) THEN
          ALTER TABLE journal_entry_details ADD COLUMN service_id INTEGER;
        END IF;
      END$$;
    `);

    console.log('🔗 إضافة/تحديث قيد المفتاح الأجنبي إلى cases(id)...');
    await client.query(`
      DO $$
      BEGIN
        IF EXISTS (
          SELECT 1 FROM information_schema.table_constraints
          WHERE table_name='journal_entry_details' AND constraint_name='fk_jed_case_id'
        ) THEN
          ALTER TABLE journal_entry_details DROP CONSTRAINT fk_jed_case_id;
        END IF;
        
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name='cases') THEN
          ALTER TABLE journal_entry_details
          ADD CONSTRAINT fk_jed_case_id
          FOREIGN KEY (case_id) REFERENCES cases(id) ON DELETE SET NULL;
        END IF;
      END$$;
    `);

    console.log('📇 إنشاء الفهارس إن لم تكن موجودة...');
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_jed_case_id ON journal_entry_details(case_id);
      CREATE INDEX IF NOT EXISTS idx_jed_case_number ON journal_entry_details(case_number);
      CREATE INDEX IF NOT EXISTS idx_jed_issues_id ON journal_entry_details(issues_id);
      CREATE INDEX IF NOT EXISTS idx_jed_service_id ON journal_entry_details(service_id);
    `);

    console.log('🧩 تعبئة case_number تلقائياً عندما يتوفر case_id ويكون case_number فارغاً...');
    const casesTable = await client.query(`SELECT to_regclass('public.cases') as t`);
    if (casesTable.rows[0].t) {
      await client.query(`
        UPDATE journal_entry_details d
        SET case_number = c.case_number
        FROM cases c
        WHERE d.case_id = c.id AND (d.case_number IS NULL OR d.case_number = '');
      `);
    } else {
      console.log('ℹ️ جدول cases غير موجود، سيتم تخطي خطوة تعبئة case_number.');
    }

    console.log('✅ تم تطبيق التحديثات بنجاح');
  } catch (err) {
    console.error('❌ خطأ أثناء التحديث:', err.message);
    process.exitCode = 1;
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

if (require.main === module) {
  addCaseFields();
}

module.exports = { addCaseFields };
