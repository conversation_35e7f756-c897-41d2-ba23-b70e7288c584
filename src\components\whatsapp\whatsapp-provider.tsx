'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'

interface WhatsAppStatus {
  isReady: boolean
  status: string
  qrCode: string | null
  companyName: string | null
  whatsappPhone: string | null
}

interface WhatsAppContextType {
  status: WhatsAppStatus | null
  isLoading: boolean
  error: string | null
  refreshStatus: () => Promise<void>
  startWhatsApp: () => Promise<boolean>
  stopWhatsApp: () => Promise<boolean>
}

const WhatsAppContext = createContext<WhatsAppContextType | undefined>(undefined)

export function WhatsAppProvider({ children }: { children: React.ReactNode }) {
  const [status, setStatus] = useState<WhatsAppStatus | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const refreshStatus = async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      const response = await fetch('/api/whatsapp/status')
      const data = await response.json()
      
      if (data.success) {
        setStatus(data.data)
      } else {
        setError(data.error || 'خطأ في جلب حالة WhatsApp')
      }
    } catch (err) {
      setError('خطأ في الاتصال بالخادم')
      console.error('خطأ في جلب حالة WhatsApp:', err)
    } finally {
      setIsLoading(false)
    }
  }

  const startWhatsApp = async (): Promise<boolean> => {
    try {
      setIsLoading(true)
      setError(null)
      
      const response = await fetch('/api/whatsapp/start', {
        method: 'POST'
      })
      const data = await response.json()
      
      if (data.success) {
        await refreshStatus()
        return true
      } else {
        setError(data.error || 'خطأ في بدء تشغيل WhatsApp')
        return false
      }
    } catch (err) {
      setError('خطأ في بدء تشغيل WhatsApp')
      console.error('خطأ في بدء تشغيل WhatsApp:', err)
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const stopWhatsApp = async (): Promise<boolean> => {
    try {
      setIsLoading(true)
      setError(null)
      
      const response = await fetch('/api/whatsapp/stop', {
        method: 'POST'
      })
      const data = await response.json()
      
      if (data.success) {
        await refreshStatus()
        return true
      } else {
        setError(data.error || 'خطأ في إيقاف WhatsApp')
        return false
      }
    } catch (err) {
      setError('خطأ في إيقاف WhatsApp')
      console.error('خطأ في إيقاف WhatsApp:', err)
      return false
    } finally {
      setIsLoading(false)
    }
  }

  // تحديث الحالة كل 10 ثوان
  useEffect(() => {
    refreshStatus()
    
    const interval = setInterval(refreshStatus, 10000)
    return () => clearInterval(interval)
  }, [])

  const value: WhatsAppContextType = {
    status,
    isLoading,
    error,
    refreshStatus,
    startWhatsApp,
    stopWhatsApp
  }

  return (
    <WhatsAppContext.Provider value={value}>
      {children}
    </WhatsAppContext.Provider>
  )
}

export function useWhatsApp() {
  const context = useContext(WhatsAppContext)
  if (context === undefined) {
    throw new Error('useWhatsApp must be used within a WhatsAppProvider')
  }
  return context
}
