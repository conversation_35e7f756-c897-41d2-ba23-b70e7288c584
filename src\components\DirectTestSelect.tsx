'use client'

import { useState, useEffect } from 'react'

export function DirectTestSelect() {
  const [data, setData] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        
        
        const clientsResponse = await fetch('/api/clients')
        const clientsData = await clientsResponse.json()
        
        
        const employeesResponse = await fetch('/api/employees')
        const employeesData = await employeesResponse.json()
        
        
        setData({
          clients: clientsData.success ? clientsData.clients : [],
          employees: employeesData.success ? employeesData.employees : []
        })
        
      } catch (error) {
        console.error('❌ DirectTestSelect: خطأ:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return (
      <div className="p-4 border border-blue-300 rounded bg-blue-50">
        <p className="text-blue-700">🔄 جاري تحميل البيانات...</p>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="p-4 border border-red-300 rounded bg-red-50">
        <p className="text-red-700">❌ فشل في تحميل البيانات</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="p-4 border border-green-300 rounded bg-green-50">
        <h3 className="font-bold text-green-700 mb-2">✅ تم تحميل البيانات بنجاح!</h3>
        <p className="text-green-600">العملاء: {data.clients.length}</p>
        <p className="text-green-600">الموظفين: {data.employees.length}</p>
      </div>

      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          اختبار قائمة بسيطة
        </label>
        <select className="w-full p-3 border border-gray-300 rounded-md bg-white">
          <option value="">اختر...</option>
          
          <optgroup label={`👤 العملاء (${data.clients.length})`}>
            {data.clients.slice(0, 3).map((client: any) => (
              <option key={client.id} value={`client_${client.id}`}>
                {client.name} - {client.account_code || 'بدون كود'}
              </option>
            ))}
          </optgroup>
          
          <optgroup label={`👨‍💼 الموظفين (${data.employees.length})`}>
            {data.employees.slice(0, 3).map((employee: any) => (
              <option key={employee.id} value={`employee_${employee.id}`}>
                {employee.name} - {employee.account_code || 'بدون كود'}
              </option>
            ))}
          </optgroup>
        </select>
      </div>
    </div>
  )
}
