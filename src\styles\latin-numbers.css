/**
 * ضمان استخدام الأرقام اللاتينية في جميع أنحاء النظام
 * يطبق على جميع العناصر التي تحتوي على أرقام
 */

/* تطبيق الأرقام اللاتينية على جميع العناصر */
* {
  font-variant-numeric: lining-nums;
  -webkit-font-feature-settings: "lnum";
  font-feature-settings: "lnum";
}

/* تطبيق خاص على الجداول المالية */
.financial-table,
.financial-table td,
.financial-table th,
.financial-table input,
.financial-table span {
  font-variant-numeric: lining-nums tabular-nums;
  -webkit-font-feature-settings: "lnum", "tnum";
  font-feature-settings: "lnum", "tnum";
  direction: ltr;
  text-align: right;
}

/* تطبيق على حقول الإدخال المالية */
input[type="number"],
input.financial-input,
.currency-input,
.amount-input {
  font-variant-numeric: lining-nums tabular-nums;
  -webkit-font-feature-settings: "lnum", "tnum";
  font-feature-settings: "lnum", "tnum";
  direction: ltr;
  text-align: right;
}

/* تطبيق على النصوص المالية */
.currency-text,
.amount-text,
.balance-text,
.financial-number {
  font-variant-numeric: lining-nums tabular-nums;
  -webkit-font-feature-settings: "lnum", "tnum";
  font-feature-settings: "lnum", "tnum";
  direction: ltr;
  unicode-bidi: embed;
}

/* تطبيق على التقارير */
.report-table,
.report-table td,
.report-table th {
  font-variant-numeric: lining-nums tabular-nums;
  -webkit-font-feature-settings: "lnum", "tnum";
  font-feature-settings: "lnum", "tnum";
}

/* تطبيق على السندات */
.voucher-amount,
.receipt-amount,
.payment-amount {
  font-variant-numeric: lining-nums tabular-nums;
  -webkit-font-feature-settings: "lnum", "tnum";
  font-feature-settings: "lnum", "tnum";
  direction: ltr;
  text-align: right;
}

/* تطبيق على الإجماليات */
.totals-row,
.summary-row,
.total-amount {
  font-variant-numeric: lining-nums tabular-nums;
  -webkit-font-feature-settings: "lnum", "tnum";
  font-feature-settings: "lnum", "tnum";
  font-weight: bold;
}

/* تطبيق على الأرصدة */
.balance-positive,
.balance-negative,
.running-balance {
  font-variant-numeric: lining-nums tabular-nums;
  -webkit-font-feature-settings: "lnum", "tnum";
  font-feature-settings: "lnum", "tnum";
  direction: ltr;
}

/* تطبيق على التواريخ */
.date-display,
.financial-date {
  font-variant-numeric: lining-nums;
  -webkit-font-feature-settings: "lnum";
  font-feature-settings: "lnum";
  direction: ltr;
}

/* تطبيق على الأرقام في الجداول */
table td.number-cell,
table th.number-cell,
.data-table .number-column {
  font-variant-numeric: lining-nums tabular-nums;
  -webkit-font-feature-settings: "lnum", "tnum";
  font-feature-settings: "lnum", "tnum";
  text-align: right;
  direction: ltr;
}

/* تطبيق على البطاقات المالية */
.financial-card .amount,
.summary-card .value,
.stat-card .number {
  font-variant-numeric: lining-nums tabular-nums;
  -webkit-font-feature-settings: "lnum", "tnum";
  font-feature-settings: "lnum", "tnum";
  direction: ltr;
}

/* تطبيق على النماذج المالية */
.financial-form input[type="number"],
.financial-form .amount-field,
.accounting-form .currency-field {
  font-variant-numeric: lining-nums;
  -webkit-font-feature-settings: "lnum";
  font-feature-settings: "lnum";
  direction: ltr;
  text-align: right;
}

/* تطبيق على الرسوم البيانية والإحصائيات */
.chart-label,
.chart-value,
.statistics-number {
  font-variant-numeric: lining-nums;
  -webkit-font-feature-settings: "lnum";
  font-feature-settings: "lnum";
}

/* تطبيق على الفواتير */
.invoice-amount,
.invoice-total,
.invoice-line-amount {
  font-variant-numeric: lining-nums tabular-nums;
  -webkit-font-feature-settings: "lnum", "tnum";
  font-feature-settings: "lnum", "tnum";
  direction: ltr;
  text-align: right;
}

/* تطبيق على كشوف الحسابات */
.account-statement .amount-column,
.account-statement .balance-column,
.account-statement .debit-column,
.account-statement .credit-column {
  font-variant-numeric: lining-nums tabular-nums;
  -webkit-font-feature-settings: "lnum", "tnum";
  font-feature-settings: "lnum", "tnum";
  direction: ltr;
  text-align: right;
}

/* تطبيق على ميزان المراجعة */
.trial-balance .amount,
.trial-balance .debit,
.trial-balance .credit,
.trial-balance .balance {
  font-variant-numeric: lining-nums tabular-nums;
  -webkit-font-feature-settings: "lnum", "tnum";
  font-feature-settings: "lnum", "tnum";
  direction: ltr;
  text-align: right;
}

/* تطبيق على قائمة الدخل */
.income-statement .amount,
.profit-loss .value {
  font-variant-numeric: lining-nums tabular-nums;
  -webkit-font-feature-settings: "lnum", "tnum";
  font-feature-settings: "lnum", "tnum";
  direction: ltr;
  text-align: right;
}

/* تطبيق على الميزانية العمومية */
.balance-sheet .amount,
.balance-sheet .total {
  font-variant-numeric: lining-nums tabular-nums;
  -webkit-font-feature-settings: "lnum", "tnum";
  font-feature-settings: "lnum", "tnum";
  direction: ltr;
  text-align: right;
}

/* تطبيق على التقارير المالية */
.financial-report table,
.financial-report .amount,
.financial-report .total,
.financial-report .percentage {
  font-variant-numeric: lining-nums tabular-nums;
  -webkit-font-feature-settings: "lnum", "tnum";
  font-feature-settings: "lnum", "tnum";
}

/* تطبيق على الأرقام في الواجهة */
.ui-number,
.display-number,
.formatted-number {
  font-variant-numeric: lining-nums;
  -webkit-font-feature-settings: "lnum";
  font-feature-settings: "lnum";
  direction: ltr;
}

/* تطبيق خاص على الأرقام الكبيرة */
.large-number,
.headline-number,
.dashboard-stat {
  font-variant-numeric: lining-nums;
  -webkit-font-feature-settings: "lnum";
  font-feature-settings: "lnum";
  direction: ltr;
  font-weight: bold;
}

/* ضمان عدم تحويل الأرقام اللاتينية إلى عربية */
.latin-numbers,
.latin-only {
  unicode-bidi: embed;
  direction: ltr;
  font-variant-numeric: lining-nums;
  -webkit-font-feature-settings: "lnum";
  font-feature-settings: "lnum";
}

/* تطبيق على جميع الجداول بشكل عام */
table.data-table td,
table.data-table th,
.table-responsive td,
.table-responsive th {
  font-variant-numeric: lining-nums;
  -webkit-font-feature-settings: "lnum";
  font-feature-settings: "lnum";
}

/* تطبيق على النصوص التي تحتوي على أرقام */
.contains-numbers {
  font-variant-numeric: lining-nums;
  -webkit-font-feature-settings: "lnum";
  font-feature-settings: "lnum";
}
