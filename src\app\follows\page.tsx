'use client'

import { useState, useEffect, useRef } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { useAuth } from '@/hooks/useAuth'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { VoiceInput } from '@/components/ui/voice-input'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Calendar,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  Clock,
  User,
  FileText,
  Crown,
  Printer, Paperclip, X
} from 'lucide-react'

interface Follow {
  id: number
  case_id: number
  case_number: string
  case_title: string
  client_name: string
  service_id: number
  service_name: string
  report: string
  date_field: string
  status: 'pending' | 'ready'
  user_id: number
  user_name?: string
  next_hearing_date?: string
  next_hearing_time?: string
  judge_id?: number
  judge_name?: string
  earned_amount: number
  is_approved: boolean
  approved_by?: number
  approved_date?: string
  created_date: string
  client_amount?: number
  employee_amount?: number
}

interface Issue {
  id: number
  case_number: string
  title: string
  client_name: string
  court_name: string
  issue_type: string
  status: string
  amount: number
}

interface Service {
  id: number
  name: string
  lineage_name?: string
}

interface ServiceAllocation {
  case_distribution_id: number
  service_distribution_id: number
  case_id: number
  case_number: string
  case_title: string
  total_case_amount: number
  admin_amount: number
  remaining_amount: number
  service_id: number
  service_name: string
  lawyer_id: number
  percentage: number
  allocated_amount: number
  total_earned: number
  remaining_amount_for_service: number | string
  can_earn: boolean
  service_type?: 'percentage' | 'fixed_amount'
}

interface Hearing {
  id: number
  issue_id: number
  hearing_date: string
  hearing_time: string
  court_name: string
  hearing_type: string
}

function FollowsPageContent() {
  // استخدام hook المصادقة
  const { 
    user: currentUser, 
    canAddFollows, 
    canEditFollows, 
    canDeleteFollows, 
    canApproveFollows 
  } = useAuth()

  const [follows, setFollows] = useState<Follow[]>([])
  const [issues, setIssues] = useState<Issue[]>([])
  const [services, setServices] = useState<Service[]>([])
  const [filteredServices, setFilteredServices] = useState<Service[]>([])
  const [hearings, setHearings] = useState<Hearing[]>([])
  const [judges, setJudges] = useState<Array<{id:number;name:string}>>([])
  const [judgeFilter, setJudgeFilter] = useState<string>('all')
  const [searchTerm, setSearchTerm] = useState('')
  // فلاتر الأعمدة والفرز
  const [colFilters, setColFilters] = useState({
    case_number: '',
    case_title: '',
    client_name: '',
    service_name: '',
    judge_name: '',
    date_field: ''
  })
  const [sortBy, setSortBy] = useState<keyof typeof colFilters>('date_field')
  const [sortDir, setSortDir] = useState<'asc'|'desc'>('desc')
  const [colMenu, setColMenu] = useState<{open:boolean; key: keyof typeof colFilters | null; x:number; y:number}>({open:false, key:null, x:0, y:0})
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalType, setModalType] = useState<'add' | 'edit' | 'view'>('add')
  const [editingFollow, setEditingFollow] = useState<Follow | null>(null)
  const [selectedIssue, setSelectedIssue] = useState<Issue | null>(null)
  const [serviceAllocation, setServiceAllocation] = useState<ServiceAllocation | null>(null)
  const [isLoadingAllocation, setIsLoadingAllocation] = useState(false)
  const [dbError, setDbError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [showNextHearing, setShowNextHearing] = useState(false)

  // وثائق القضية المختارة
  const [caseDocuments, setCaseDocuments] = useState<Array<{id:number;file_name:string;mime_type:string;size_bytes:number;created_at:string}>>([])

  // سيتم وضع useEffect لاحقاً بعد تعريف دوال الجلب كلها

  // بيانات النموذج
  const [formData, setFormData] = useState({
    case_id: '',
    service_id: '',
    report: '',
    date_field: new Date().toISOString().split('T')[0],
    status: 'pending' as 'pending' | 'ready',
    next_hearing_date: '',
    judge_id: '',
    client_amount: '',
    employee_amount: ''
  })

  const fetchFollows = async () => {
    setIsLoading(true)
    setDbError(null)

    try {
      const response = await fetch(`/api/follows?user_id=${currentUser?.id || 1}`)
      const result = await response.json()

      if (result.success) {
        setFollows(result.data)
      } else {
        setDbError(result.error || 'فشل في جلب بيانات المتابعات')
        setFollows([])
      }
    } catch (error) {
      console.error('Network error:', error)
      setDbError('فشل في الاتصال بقاعدة البيانات')
      setFollows([])
    } finally {
      setIsLoading(false)
    }
  }

  // أدوات منيو الفلاترة للأعمدة (خارج أي دالة أخرى)
  const openColMenu = (key: keyof typeof colFilters) => (e: React.MouseEvent<HTMLElement>) => {
    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect()
    setColMenu({ open:true, key, x: rect.left + rect.width, y: rect.bottom })
  }
  const getFilterValueForKey = () => {
    if (!colMenu.key) return ''
    return colFilters[colMenu.key]
  }
  const setFilterForKey = (v: string) => {
    if (!colMenu.key) return
    setColFilters(prev => ({ ...prev, [colMenu.key!]: v }))
  }
  const clearFilterForKey = () => {
    if (!colMenu.key) return
    setColFilters(prev => ({ ...prev, [colMenu.key!]: '' }))
  }
  const applySortAsc = () => { if (colMenu.key){ setSortBy(colMenu.key); setSortDir('asc'); setColMenu(prev=>({...prev, open:false})) } }
  const applySortDesc = () => { if (colMenu.key){ setSortBy(colMenu.key); setSortDir('desc'); setColMenu(prev=>({...prev, open:false})) } }

  const fetchUserIssues = async () => {
    try {
      const response = await fetch(`/api/follows/user-issues?user_id=${currentUser?.id || 1}`)
      const result = await response.json()
      if (result.success && Array.isArray(result.data) && result.data.length > 0) {
        setIssues(result.data)
      } else {
        // fallback: جلب جميع القضايا عند عدم توفر قضايا مخصصة للمستخدم
        const allIssuesRes = await fetch('/api/issues')
        const allIssuesJson = await allIssuesRes.json()
        if (allIssuesJson.success && Array.isArray(allIssuesJson.data)) {
          // تحويل الحقول لتتوافق مع الواجهة هنا
          const mapped = allIssuesJson.data.map((i: any) => ({
            id: i.id,
            case_number: i.case_number,
            title: i.title,
            client_name: i.client_name,
            court_name: i.court_name,
            issue_type: i.issue_type,
            status: i.status,
            amount: i.case_amount || 0
          }))
          setIssues(mapped)
        } else {
          setIssues([])
        }
      }
    } catch (error) {
      console.error('Error fetching user issues:', error)
      // fallback في حال حدوث خطأ
      try {
        const allIssuesRes = await fetch('/api/issues')
        const allIssuesJson = await allIssuesRes.json()
        if (allIssuesJson.success && Array.isArray(allIssuesJson.data)) {
          const mapped = allIssuesJson.data.map((i: any) => ({
            id: i.id,
            case_number: i.case_number,
            title: i.title,
            client_name: i.client_name,
            court_name: i.court_name,
            issue_type: i.issue_type,
            status: i.status,
            amount: i.case_amount || 0
          }))
          setIssues(mapped)
        } else {
          setIssues([])
        }
      } catch (e) {
        setIssues([])
      }
    }
  }

  const fetchServices = async () => {
    try {
      const response = await fetch('/api/services')
      const result = await response.json()
      if (result.success) {
        setServices(result.data)
        setFilteredServices(result.data)
      }
    } catch (error) {
      console.error('Error fetching services:', error)
    }
  }

  const fetchJudges = async () => {
    try {
      const res = await fetch('/api/judges')
      const data = await res.json()
      if (data.success) setJudges(data.data || [])
    } catch (e) { console.error('Error fetching judges:', e) }
  }

  const fetchHearings = async (issueId?: number) => {
    try {
      const url = issueId ? `/api/hearings?issue_id=${issueId}` : '/api/hearings'
      const response = await fetch(url)
      const result = await response.json()
      if (result.success) {
        setHearings(result.data)
      }
    } catch (error) {
      console.error('Error fetching hearings:', error)
    }
  }

  const [allocationError, setAllocationError] = useState<string | null>(null)

  // دالة تعميد المتابعة
  const handleApprove = async (id: number) => {
    try {
      const res = await fetch('/api/follows', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id, action: 'approve' })
      })
      const data = await res.json()
      if (data.success) {
        fetchFollows()
      } else {
        alert(data.error || 'فشل في تعميد المتابعة')
      }
    } catch (e) { alert('خطأ في الاتصال بالخادم') }
  }

  const fetchServiceAllocation = async (caseId: string, serviceId: string) => {
    if (!caseId || !serviceId) return

    setIsLoadingAllocation(true)
    setAllocationError(null)
    try {
      const url = `/api/follows/service-allocation?case_id=${caseId}&service_id=${serviceId}&lawyer_id=${currentUser?.id || 1}`

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${currentUser?.token}`
        }
      })
      const result = await response.json()

      if (result.success) {
        setServiceAllocation(result.data)
      } else {
        setServiceAllocation(null)
        setAllocationError(result.error || 'خطأ في جلب بيانات التوزيع')
      }
    } catch (error) {
      console.error('Error fetching service allocation:', error)
      setServiceAllocation(null)
      setAllocationError('خطأ في الاتصال بالخادم')
    } finally {
      setIsLoadingAllocation(false)
    }
  }

  // تم نقل هذا إلى useEffect الذي يعتمد على currentUser

  // تحميل احتياطي لضمان ظهور القضايا حتى عند عدم توفر مستخدم
  useEffect(() => {
    if (!currentUser) {
      fetchUserIssues()
      fetchServices()
      fetchJudges()
    }
  }, [currentUser])

  // دوال التعامل مع النموذج
  const handleAddNew = () => {
    const today = new Date().toISOString().split('T')[0]
    setFormData({
      case_id: '',
      service_id: '',
      report: '',
      date_field: today,
      status: 'pending',
      next_hearing_date: '',
      judge_id: '',
      client_amount: '',
      employee_amount: ''
    })
    setSelectedIssue(null)
    setServiceAllocation(null)
    setAllocationError(null)
    setModalType('add')
    setIsModalOpen(true)
    setShowNextHearing(false)
  }

  const handleView = (follow: Follow) => {
    setFormData({
      case_id: follow.case_id.toString(),
      service_id: follow.service_id.toString(),
      report: follow.report || '',
      date_field: follow.date_field || '',
      status: follow.status || 'pending',
      next_hearing_date: follow.next_hearing_date || '',
      judge_id: (follow.judge_id?.toString() || ''),
      client_amount: (follow.client_amount ?? '').toString(),
      employee_amount: (follow.employee_amount ?? '').toString()
    })
    setEditingFollow(follow)
    setSelectedIssue(issues.find(i => i.id === follow.case_id) || null)
    setModalType('view')
    setIsModalOpen(true)
    setShowNextHearing(!!follow.next_hearing_date)
  }

  const handleEdit = (follow: Follow) => {
    setFormData({
      case_id: follow.case_id.toString(),
      service_id: follow.service_id.toString(),
      report: follow.report || '',
      date_field: follow.date_field || '',
      status: follow.status || 'pending',
      next_hearing_date: follow.next_hearing_date || '',
      judge_id: (follow.judge_id?.toString() || ''),
      client_amount: (follow.client_amount ?? '').toString(),
      employee_amount: (follow.employee_amount ?? '').toString()
    })
    setEditingFollow(follow)
    setSelectedIssue(issues.find(i => i.id === follow.case_id) || null)
    setModalType('edit')
    setIsModalOpen(true)
    setShowNextHearing(!!follow.next_hearing_date)
  }

  const handleAddTaskFromRow = (follow: Follow) => {
    const today = new Date().toISOString().split('T')[0]
    setFormData({
      case_id: follow.case_id.toString(),
      service_id: '',
      report: '',
      date_field: today,
      status: 'pending',
      next_hearing_date: '',
      judge_id: (follow.judge_id?.toString() || ''),
      client_amount: '',
      employee_amount: ''
    })
    setSelectedIssue(issues.find(i => i.id === follow.case_id) || null)
    setModalType('add')
    setIsModalOpen(true)
    setShowNextHearing(false)
  }

  const handleIssueChange = (issueId: string) => {
    const issue = issues.find(i => i.id.toString() === issueId)
    setSelectedIssue(issue || null)
    setServiceAllocation(null) // إعادة تعيين معلومات التوزيع

    setFormData(prev => {
      const newFormData = { ...prev, case_id: issueId }

      // جلب الجلسات الخاصة بهذه القضية
      if (issueId) {
        fetchHearings(parseInt(issueId))
      }

      // جلب الخدمات الخاصة بهذه القضية فقط
      if (issueId) {
        fetch(`/api/follows/services-for-case?case_id=${issueId}`)
          .then(r => r.json())
          .then(data => {
            if (data.success) {
              setFilteredServices(data.data)
            } else {
              // fallback لجميع الخدمات
              setFilteredServices(services)
            }
          })
          .catch(() => setFilteredServices(services))
      } else {
        setFilteredServices(services)
      }

      // جلب معلومات توزيع الخدمة إذا كانت الخدمة محددة
      if (issueId && newFormData.service_id) {
        setTimeout(() => {
          fetchServiceAllocation(issueId, newFormData.service_id)
        }, 100)
      }

      // جلب وثائق القضية المختارة
      if (issueId) {
        fetch(`/api/case-documents?case_id=${issueId}`)
          .then(r => r.json())
          .then(data => {
            if (data.success) setCaseDocuments(data.data || [])
            else setCaseDocuments([])
          })
          .catch(() => setCaseDocuments([]))
      } else {
        setCaseDocuments([])
      }

      return newFormData
    })
  }

  const handleServiceChange = (serviceId: string) => {
    setFormData(prev => {
      const newFormData = { ...prev, service_id: serviceId }

      // جلب معلومات توزيع الخدمة إذا كانت القضية محددة
      if (newFormData.case_id && serviceId) {
        // استخدام setTimeout لضمان تحديث state أولاً
        setTimeout(() => {
          fetchServiceAllocation(newFormData.case_id, serviceId)
        }, 100)
      }

      return newFormData
    })

    setServiceAllocation(null) // إعادة تعيين معلومات التوزيع
  }

  // رفع الوثائق
  const [uploadFiles, setUploadFiles] = useState<FileList | null>(null)
  const handleFilesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUploadFiles(e.target.files)
  }
  const uploadInputRef = useRef<HTMLInputElement | null>(null)

  // حذف ملف من القائمة المختارة قبل الحفظ
  const handleRemoveSelectedFile = (index: number) => {
    if (!uploadFiles) return
    const dt = new DataTransfer()
    Array.from(uploadFiles).forEach((file, idx) => {
      if (idx !== index) dt.items.add(file)
    })
    const newFiles = dt.files
    setUploadFiles(newFiles)
    if (uploadInputRef.current) {
      // تحديث قيمة input.files يدوياً
      ;(uploadInputRef.current as any).files = newFiles
    }
  }

  // عند فتح النافذة للتعديل، اجلب وثائق القضية المرتبطة
  useEffect(() => {
    if (isModalOpen && formData.case_id) {
      fetch(`/api/case-documents?case_id=${formData.case_id}`)
        .then(r => r.json())
        .then(data => {
          if (data.success) setCaseDocuments(data.data || [])
          else setCaseDocuments([])
        })
        .catch(() => setCaseDocuments([]))
    }
  }, [isModalOpen])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      // إعداد بيانات المتابعة
      const dataToSubmit: any = {
        ...formData,
        case_id: parseInt(formData.case_id),
        service_id: parseInt(formData.service_id),
        user_id: currentUser?.id || 1
      }

      if (modalType === 'edit' && editingFollow) {
        dataToSubmit.id = editingFollow.id
      }

      // إضافة الجلسة الجديدة إذا تم تحديد تاريخ
      let hearingId = null
      if (formData.next_hearing_date && formData.case_id && selectedIssue) {
        try {
          const hearingData = {
            issue_id: parseInt(formData.case_id),
            hearing_date: formData.next_hearing_date,
            court_name: selectedIssue.court_name || null,
            hearing_type: 'جلسة قادمة'
          }

          const hearingResponse = await fetch('/api/hearings', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(hearingData)
          })

          const hearingResult = await hearingResponse.json()
          if (hearingResult.success) {
            hearingId = hearingResult.data.id

          } else {
            console.warn('فشل في إضافة الجلسة:', hearingResult.error)
          }
        } catch (hearingError) {
          console.warn('خطأ في إضافة الجلسة:', hearingError)
        }
      }

      // إضافة معرف الجلسة إلى بيانات المتابعة
      dataToSubmit.next_hearing_id = hearingId
      if (formData.judge_id) dataToSubmit.judge_id = parseInt(formData.judge_id)

      // تحويل مبالغ العميل/الموظف إلى أرقام
      if (formData.client_amount) dataToSubmit.client_amount = parseFloat(formData.client_amount)
      if (formData.employee_amount) dataToSubmit.employee_amount = parseFloat(formData.employee_amount)

      // حفظ المتابعة
      const url = '/api/follows'
      const method = modalType === 'edit' ? 'PUT' : 'POST'

      console.log('إرسال المتابعة:', {
        url,
        method,
        user: currentUser?.username,
        token: currentUser?.token,
        dataToSubmit
      })

      const response = await fetch(url, {
        method,
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${currentUser?.token}`
        },
        body: JSON.stringify(dataToSubmit)
      })

      const result = await response.json()

      if (result.success) {
        const followId = result.data?.id || editingFollow?.id
        // رفع الوثائق إن وُجدت
        if (uploadFiles && followId && formData.case_id) {
          const arrayBufferToBase64 = (buffer: ArrayBuffer) => {
            let binary = ''
            const bytes = new Uint8Array(buffer)
            const len = bytes.byteLength
            for (let i = 0; i < len; i++) binary += String.fromCharCode(bytes[i])
            return btoa(binary)
          }
          const filesPayload: any[] = []
          for (const f of Array.from(uploadFiles)) {
            const content = await f.arrayBuffer()
            const base64 = arrayBufferToBase64(content)
            filesPayload.push({ file_name: f.name, mime_type: f.type || 'application/octet-stream', base64 })
          }
          await fetch('/api/case-documents', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ case_id: parseInt(formData.case_id), follow_id: followId, uploaded_by: currentUser?.id || 1, files: filesPayload })
          })
        }
        setIsModalOpen(false)
        fetchFollows()
        fetchHearings() // تحديث قائمة الجلسات

        if (hearingId) {
          alert('تم حفظ المتابعة وإضافة الجلسة الجديدة بنجاح')
        }
      } else {
        setDbError(result.error)
      }
    } catch (error) {
      console.error('Error submitting follow:', error)
      setDbError('فشل في حفظ البيانات')
    }
  }

  const handleVoiceTranscript = (transcript: string) => {
    // إضافة النص المحول من الصوت إلى مربع التقرير
    setFormData(prev => ({
      ...prev,
      report: prev.report ? `${prev.report} ${transcript}` : transcript
    }))
  }

  const handleDelete = async (id: number) => {
    if (confirm('هل أنت متأكد من حذف هذه المتابعة؟')) {
      try {
        const response = await fetch(`/api/follows?id=${id}&user_id=${currentUser?.id || 1}`, {
          method: 'DELETE'
        })

        const result = await response.json()
        if (result.success) {
          alert('تم حذف المتابعة بنجاح')
          fetchFollows()
        } else {
          alert(result.error || 'فشل في حذف المتابعة')
        }
      } catch (error) {
        console.error('Error deleting follow:', error)
        alert('حدث خطأ في حذف المتابعة')
      }
    }
  }

  const filteredFollows = follows
    .filter(follow => {
      const matchesSearch =
        (follow.case_number || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        (follow.case_title || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        (follow.client_name || '').toLowerCase().includes(searchTerm.toLowerCase())
      const matchesJudge = judgeFilter === 'all' || (follow.judge_id?.toString() === judgeFilter)
      // فلاتر الأعمدة
      const f1 = colFilters.case_number ? (follow.case_number||'').toLowerCase().includes(colFilters.case_number.toLowerCase()) : true
      const f2 = colFilters.case_title ? (follow.case_title||'').toLowerCase().includes(colFilters.case_title.toLowerCase()) : true
      const f3 = colFilters.client_name ? (follow.client_name||'').toLowerCase().includes(colFilters.client_name.toLowerCase()) : true
      const f4 = colFilters.service_name ? (follow.service_name||'').toLowerCase().includes(colFilters.service_name.toLowerCase()) : true
      const f5 = colFilters.judge_name ? (follow.judge_name||'').toLowerCase().includes(colFilters.judge_name.toLowerCase()) : true
      const f6 = colFilters.date_field ? (new Date(follow.date_field).toISOString().slice(0,10).includes(colFilters.date_field)) : true
      return matchesSearch && matchesJudge && f1 && f2 && f3 && f4 && f5 && f6
    })
    .sort((a, b) => {
      const key = sortBy
      if (key === 'date_field') {
        const av = new Date(a.date_field).getTime()
        const bv = new Date(b.date_field).getTime()
        if (av < bv) return sortDir==='asc' ? -1 : 1
        if (av > bv) return sortDir==='asc' ? 1 : -1
        return 0
      }
      const av = (a[key] as any ?? '').toString().toLowerCase()
      const bv = (b[key] as any ?? '').toString().toLowerCase()
      if (av < bv) return sortDir==='asc' ? -1 : 1
      if (av > bv) return sortDir==='asc' ? 1 : -1
      return 0
    })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'ready': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'معلق'
      case 'ready': return 'جاهز'
      default: return 'غير محدد'
    }
  }

  // الإحصائيات
  const stats = {
    total: filteredFollows.length,
    pending: filteredFollows.filter(f => f.status === 'pending').length,
    ready: filteredFollows.filter(f => f.status === 'ready').length
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Calendar className="h-8 w-8 mr-3 text-blue-600" />
              إدارة المهام
            </h1>
            <p className="text-gray-600 mt-1">إدارة المهام والمواعيد المتعلقة بالقضايا</p>
          </div>

          <div className="flex items-center gap-4">
            {canAddFollows ? (
              <Button onClick={handleAddNew} className="bg-blue-600 hover:bg-blue-700">
                <Plus className="h-4 w-4 mr-2" />
                إضافة مهمة جديدة
              </Button>
            ) : (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <div className="flex items-center">
                  <div className="text-yellow-600 mr-2">🔒</div>
                  <div>
                    <div className="font-medium text-yellow-800 text-sm">صلاحية محدودة</div>
                    <div className="text-yellow-700 text-xs">تحتاج إلى صلاحية إضافة المتابعات</div>
                  </div>
                </div>
              </div>
            )}

            {/* عرض معلومات المستخدم والصلاحيات */}
            {currentUser && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="flex items-center">
                  <User className="h-4 w-4 text-blue-600 mr-2" />
                  <div>
                    <div className="font-medium text-blue-800 text-sm">
                      {currentUser.name} ({currentUser.role_display_name})
                    </div>
                    <div className="text-blue-700 text-xs">
                      الصلاحيات: {currentUser?.permissions?.length > 0 ? currentUser.permissions.join(', ') : 'لا توجد صلاحيات'}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Calendar className="h-6 w-6 text-blue-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
                  <div className="text-sm text-gray-600">إجمالي المتابعات</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <Clock className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.pending}</div>
                  <div className="text-sm text-gray-600">معلق</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <FileText className="h-6 w-6 text-green-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.ready}</div>
                  <div className="text-sm text-gray-600">جاهز</div>
                </div>
              </div>
            </CardContent>
          </Card>

        </div>

        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
              <div className="relative flex-1">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في المهام..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
              <div className="w-full md:w-64">
                <Label className="text-xs text-gray-600">فلترة حسب القاضي</Label>
                <select
                  value={judgeFilter}
                  onChange={(e)=> setJudgeFilter(e.target.value)}
                  className="w-full h-10 px-3 py-2 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-black"
                >
                  <option value="all">جميع القضاة</option>
                  {judges.map(j => (
                    <option key={j.id} value={j.id.toString()}>{j.name}</option>
                  ))}
                </select>
              </div>
              <Button
                variant="outline"
                className="border-gray-500 text-gray-700 hover:bg-gray-50"
                onClick={() => { setColFilters({ case_number:'', case_title:'', client_name:'', service_name:'', judge_name:'', date_field:'' }); setSortBy('date_field'); setSortDir('desc'); setColMenu({open:false, key:null, x:0, y:0}) }}
              >
                مسح الفلاتر
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              قائمة المهام ({filteredFollows.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="text-right p-3 font-semibold cursor-pointer" onMouseDown={openColMenu('case_number')}>رقم القضية {sortBy==='case_number' ? (sortDir==='asc' ? '▲' : '▼') : ''}</th>
                    <th className="text-right p-3 font-semibold cursor-pointer" onMouseDown={openColMenu('case_title')}>عنوان القضية {sortBy==='case_title' ? (sortDir==='asc' ? '▲' : '▼') : ''}</th>
                    <th className="text-right p-3 font-semibold cursor-pointer" onMouseDown={openColMenu('client_name')}>الموكل {sortBy==='client_name' ? (sortDir==='asc' ? '▲' : '▼') : ''}</th>
                    <th className="text-right p-3 font-semibold cursor-pointer" onMouseDown={openColMenu('service_name')}>نوع الخدمة {sortBy==='service_name' ? (sortDir==='asc' ? '▲' : '▼') : ''}</th>
                    <th className="text-right p-3 font-semibold cursor-pointer" onMouseDown={openColMenu('judge_name')}>القاضي {sortBy==='judge_name' ? (sortDir==='asc' ? '▲' : '▼') : ''}</th>
                    <th className="text-right p-3 font-semibold">التقرير</th>
                    <th className="text-center p-3 font-semibold cursor-pointer" onMouseDown={openColMenu('date_field')}>التاريخ {sortBy==='date_field' ? (sortDir==='asc' ? '▲' : '▼') : ''}</th>
                    <th className="text-center p-3 font-semibold">الجلسة القادمة</th>
                    <th className="text-center p-3 font-semibold">الحالة</th>
                    <th className="text-center p-3 font-semibold">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredFollows.map((follow) => (
                    <tr key={follow.id} className="border-b hover:bg-gray-50">
                      <td className="p-3 font-medium text-blue-600">{follow.case_number}</td>
                      <td className="p-3">{follow.case_title}</td>
                      <td className="p-3 flex items-center">
                        <User className="h-4 w-4 mr-2 text-gray-400" />
                        {follow.client_name}
                      </td>
                      <td className="p-3">
                        <Badge variant="outline">{follow.service_name}</Badge>
                      </td>
                      <td className="p-3">{follow.judge_name || '-'}</td>
                      <td className="p-3 max-w-xs truncate" title={follow.report}>
                        {follow.report}
                      </td>
                      <td className="text-center p-3">
                        <div className="flex items-center justify-center">
                          <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                          {new Date(follow.date_field).toLocaleDateString('ar-SA')}
                        </div>
                      </td>
                      <td className="text-center p-3">
                        {follow.next_hearing_date ? (
                          <div className="text-sm">
                            <div>{new Date(follow.next_hearing_date).toLocaleDateString('ar-SA')}</div>
                            {follow.next_hearing_time && <div className="text-gray-500">{follow.next_hearing_time}</div>}
                          </div>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </td>
                      <td className="text-center p-3">
                        <Badge className={getStatusColor(follow.status)}>
                          {getStatusText(follow.status)}
                        </Badge>
                      </td>
                      <td className="text-center p-3">
                        <div className="flex justify-center space-x-2 space-x-reverse">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleView(follow)}
                            className="bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200"
                            title="عرض تفاصيل المتابعة"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEdit(follow)}
                            className="bg-yellow-50 hover:bg-yellow-100 text-yellow-700 border-yellow-200 hover:border-yellow-300"
                            title="تعديل المتابعة"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDelete(follow.id)}
                            className="bg-red-50 hover:bg-red-100 text-red-700 border-red-200 hover:border-red-300"
                            title="حذف المتابعة"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleAddTaskFromRow(follow)}
                            className="bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200"
                            title="إضافة مهمة جديدة"
                          >
                            مهمة
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => window.print()}
                            className="bg-green-50 hover:bg-green-100 text-green-700 border-green-200 hover:border-green-300"
                            title="طباعة المتابعة"
                          >
                            <Printer className="h-4 w-4" />
                          </Button>
                          {follow.status === 'pending' && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleApprove(follow.id)}
                              className="bg-purple-50 hover:bg-purple-100 text-purple-700 border-purple-200 hover:border-purple-300"
                              title="تعميد المتابعة"
                            >
                              تعميد
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* منيو العمود المنبثقة للفلاترة */}
        {colMenu.open && colMenu.key && (
          <div
            className="fixed z-50 bg-white border border-gray-200 rounded-md shadow-lg p-2 w-72"
            style={{ top: colMenu.y + 8, left: Math.max(8, colMenu.x - 288) }}
          >
            <div className="relative">
              {colMenu.key === 'date_field' ? (
                <input
                  type="date"
                  value={getFilterValueForKey()}
                  onChange={(e)=> setFilterForKey(e.target.value)}
                  className="h-9 w-full border border-gray-300 rounded-md pr-3 pl-28 text-sm text-black"
                />
              ) : (
                <Input
                  value={getFilterValueForKey()}
                  onChange={(e)=> setFilterForKey(e.target.value)}
                  placeholder={`بحث في العمود`}
                  className="h-9 pr-3 pl-28 text-sm"
                />
              )}
              <div className="absolute inset-y-0 left-2 flex items-center gap-1">
                <Button size="sm" variant="outline" className="h-7 px-2" onClick={applySortAsc} title="تصاعدي">↑</Button>
                <Button size="sm" variant="outline" className="h-7 px-2" onClick={applySortDesc} title="تنازلي">↓</Button>
                <Button size="sm" variant="outline" className="h-7 px-2 text-red-600" onClick={clearFilterForKey} title="مسح">×</Button>
              </div>
            </div>
          </div>
        )}

        {/* نافذة إضافة/تعديل المتابعة */}
        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {modalType === 'add' ? 'إضافة مهمة جديدة' : 'تعديل المهمة'}
              </DialogTitle>
              <DialogDescription>
                {modalType === 'add' ? 'أدخل تفاصيل المتابعة الجديدة' : 'تعديل تفاصيل المتابعة'}
              </DialogDescription>
            </DialogHeader>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {/* اختيار القضية */}
                <div>
                  <Label htmlFor="case_id">القضية *</Label>
                  <Select value={formData.case_id} onValueChange={handleIssueChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر القضية..." />
                    </SelectTrigger>
                    <SelectContent>
                      {issues.map((issue) => (
                        <SelectItem key={issue.id} value={issue.id.toString()}>
                          {issue.case_number} - {issue.title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* نوع الخدمة */}
                <div>
                  <Label htmlFor="service_id">نوع الخدمة *</Label>
                  <Select
                    value={formData.service_id}
                    onValueChange={handleServiceChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر نوع الخدمة..." />
                    </SelectTrigger>
                    <SelectContent>
                      {filteredServices.map((service) => (
                        <SelectItem key={service.id} value={service.id.toString()}>
                          {service.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* اختيار القاضي */}
                <div>
                  <Label htmlFor="judge_id">لدى القاضي</Label>
                  <select
                    id="judge_id"
                    value={formData.judge_id}
                    onChange={(e)=> setFormData(prev => ({ ...prev, judge_id: e.target.value }))}
                    className="w-full h-10 px-3 py-2 bg-rose-50 border border-rose-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rose-500 focus:border-rose-500 focus:bg-white transition-colors text-black"
                  >
                    <option value="">اختر القاضي...</option>
                    {judges.map(j => (
                      <option key={j.id} value={j.id}>{j.name}</option>
                    ))}
                  </select>
                </div>
              </div>

              {/* عرض معلومات الموكل */}
              {selectedIssue && (
                <div className="bg-gray-50 p-3 rounded-lg">
                  <div className="text-sm text-gray-600">
                    <strong>الموكل:</strong> {selectedIssue.client_name}
                  </div>
                  <div className="text-sm text-gray-600">
                    <strong>المحكمة:</strong> {selectedIssue.court_name}
                  </div>
                  <div className="text-sm text-gray-600">
                    <strong>نوع القضية:</strong> {selectedIssue.issue_type}
                  </div>
                </div>
              )}

              {/* عرض معلومات توزيع الخدمة */}
              {isLoadingAllocation && (
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                    <span className="text-blue-800">جاري تحميل معلومات التوزيع...</span>
                  </div>
                </div>
              )}

              {!isLoadingAllocation && serviceAllocation && (
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <h4 className="font-semibold text-blue-800 mb-2">معلومات توزيع الخدمة</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">الخدمة:</span>
                      <span className="font-medium mr-2">{serviceAllocation.service_name}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">النسبة المخصصة:</span>
                      <span className="font-medium mr-2">{serviceAllocation.percentage}%</span>
                    </div>
                    <div>
                      <span className="text-gray-600">المبلغ المخصص:</span>
                      <span className="font-medium mr-2 text-green-600">
                        {serviceAllocation.allocated_amount.toLocaleString()} ريال
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600">المبلغ المكتسب:</span>
                      <span className="font-medium mr-2 text-blue-600">
                        {serviceAllocation.total_earned.toLocaleString()} ريال
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600">المبلغ المتبقي:</span>
                      <span className={`font-medium mr-2 ${typeof serviceAllocation.remaining_amount_for_service === 'number' && serviceAllocation.remaining_amount_for_service > 0 ? 'text-orange-600' : 'text-gray-500'}`}>
                        {typeof serviceAllocation.remaining_amount_for_service === 'number' ? serviceAllocation.remaining_amount_for_service.toLocaleString() : String(serviceAllocation.remaining_amount_for_service)} ريال
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600">نوع الخدمة:</span>
                      <span className="font-medium mr-2 text-purple-600">
                        {serviceAllocation.service_type === 'percentage' ? 'نسبة مئوية' : 'مبلغ ثابت'}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600">حالة الاستحقاق:</span>
                      <span className={`font-medium mr-2 ${serviceAllocation.can_earn ? 'text-green-600' : 'text-red-600'}`}>
                        {serviceAllocation.can_earn ? 'يمكن الاستحقاق' : 'مكتمل الاستحقاق'}
                      </span>
                    </div>
                  </div>
                  {!serviceAllocation.can_earn && (
                    <div className="mt-2 p-2 bg-yellow-100 border border-yellow-300 rounded text-sm text-yellow-800">
                      <strong>تنبيه:</strong> {serviceAllocation.service_type === 'percentage'
                        ? 'تم تسجيل مبلغ لهذه الخدمة مسبقاً. الخدمات ذات النسبة المئوية تسمح بمتابعة واحدة فقط.'
                        : 'تم استحقاق كامل المبلغ المخصص لهذه الخدمة'}
                    </div>
                  )}

                  {serviceAllocation.can_earn && serviceAllocation.service_type && (
                    <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-sm text-blue-800">
                      💡 <strong>نوع الخدمة:</strong> {serviceAllocation.service_type === 'percentage'
                        ? 'نسبة مئوية - سيتم تسجيل النسبة كاملة عند إضافة المتابعة (متابعة واحدة فقط مسموحة)'
                        : 'مبلغ ثابت - يمكن إضافة متابعات متعددة، كل متابعة تحصل على المبلغ المخصص'}
                    </div>
                  )}
                </div>
              )}

              {!isLoadingAllocation && !serviceAllocation && formData.case_id && formData.service_id && allocationError && (
                <div className="bg-red-50 p-4 rounded-lg border border-red-200">
                  <div className="flex items-center">
                    <div className="text-red-600 mr-2">⚠️</div>
                    <div>
                      <div className="font-medium text-red-800">مشكلة في توزيع الخدمة</div>
                      <div className="text-sm text-red-700 mt-1">
                        {allocationError}
                      </div>
                      <div className="text-xs text-red-600 mt-2">
                        💡 يمكنك الذهاب إلى <a href="/case-distribution" className="underline font-medium">صفحة توزيع القضايا</a> لإضافة أو تعديل التوزيع
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                {/* التاريخ */}
                <div>
                  <Label htmlFor="date_field">التاريخ *</Label>
                  <Input
                    id="date_field"
                    type="date"
                    value={formData.date_field}
                    onChange={(e) => setFormData(prev => ({ ...prev, date_field: e.target.value }))}
                    required
                  />
                </div>

                {/* الحالة */}
                <div>
                  <Label htmlFor="status">الحالة</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value: 'pending' | 'ready') => setFormData(prev => ({ ...prev, status: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pending">معلق</SelectItem>
                      <SelectItem value="ready">جاهز</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* الجلسة القادمة */}
              <div>
                <div className="flex items-center justify-between">
                  <Label>الجلسة القادمة</Label>
                  <button
                    type="button"
                    onClick={() => setShowNextHearing(prev => !prev)}
                    className="text-xs text-blue-600 hover:text-blue-700 underline"
                  >
                    {showNextHearing ? 'إخفاء' : 'إضافة جلسة قادمة'}
                  </button>
                </div>
                {showNextHearing && (
                  <Input
                    id="next_hearing_date"
                    type="date"
                    value={formData.next_hearing_date}
                    onChange={(e) => setFormData(prev => ({ ...prev, next_hearing_date: e.target.value }))}
                    min={new Date().toISOString().split('T')[0]}
                    placeholder="اختر تاريخ الجلسة القادمة"
                  />
                )}
              </div>

              {/* التقرير */}
              <div>
                <Label htmlFor="report">التقرير *</Label>
                <div className="relative">
                  <Textarea
                    id="report"
                    value={formData.report}
                    onChange={(e) => setFormData(prev => ({ ...prev, report: e.target.value }))}
                    placeholder="اكتب تقرير المتابعة أو اضغط على الميكروفون للتسجيل الصوتي..."
                    rows={6}
                    required
                    className="pr-12"
                  />
                  <VoiceInput
                    onTranscript={handleVoiceTranscript}
                    language="ar-SA"
                    className="absolute inset-0 pointer-events-none"
                  />
                </div>
              </div>

              {/* مبالغ العميل والموظف */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="client_amount">مبلغ العميل</Label>
                  <Input
                    id="client_amount"
                    type="number"
                    inputMode="decimal"
                    value={formData.client_amount}
                    onChange={(e) => setFormData(prev => ({ ...prev, client_amount: e.target.value }))}
                    placeholder="0.00"
                  />
                </div>
                <div>
                  <Label htmlFor="employee_amount">مبلغ الموظف</Label>
                  <Input
                    id="employee_amount"
                    type="number"
                    inputMode="decimal"
                    value={formData.employee_amount}
                    onChange={(e) => setFormData(prev => ({ ...prev, employee_amount: e.target.value }))}
                    placeholder="0.00"
                  />
                </div>
              </div>

              {/* رفع الوثائق - حقل مخفي وزر ضمن أزرار الإجراءات */}
              <input
                ref={uploadInputRef}
                type="file"
                multiple
                accept="image/*,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                onChange={handleFilesChange}
                className="hidden"
              />
              {uploadFiles && uploadFiles.length > 0 && (
                <div className="mt-2 bg-gray-50 border border-gray-200 rounded p-2 text-sm">
                  <div className="font-semibold mb-2">الملفات المختارة ({uploadFiles.length}):</div>
                  <div className="flex flex-wrap gap-2">
                    {Array.from(uploadFiles).map((f, idx) => (
                      <span key={idx} className="inline-flex items-center gap-1 px-2 py-1 rounded bg-white border border-gray-200 text-gray-800">
                        <span>
                          {f.name}
                          <span className="text-gray-400 ml-1">({(f.size/1024).toFixed(1)} ك.ب)</span>
                        </span>
                        <button
                          type="button"
                          onClick={() => handleRemoveSelectedFile(idx)}
                          className="ml-1 inline-flex items-center justify-center text-red-600 hover:text-red-700"
                          title="حذف الملف"
                          aria-label={`حذف ${f.name}`}
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                  <div className="text-xs text-gray-500 mt-2">تنبيه: المتصفح لا يعرض المسار الكامل للملف لأسباب أمنية.</div>
                </div>
              )}

              {/* عرض وثائق القضية الحالية */}
              {formData.case_id && (
                <div className="mt-3">
                  <Label className="text-sm font-semibold text-indigo-700 bg-indigo-50 px-3 py-1 rounded-md inline-block mb-2">
                    📂 وثائق القضية
                  </Label>
                  {caseDocuments.length === 0 ? (
                    <div className="text-sm text-gray-500">لا توجد وثائق محفوظة لهذه القضية.</div>
                  ) : (
                    <ul className="list-disc pr-5 space-y-1 text-sm">
                      {caseDocuments.map(doc => (
                        <li key={doc.id}>
                          <a className="text-blue-700 hover:underline" href={`/api/case-documents/${doc.id}?download=1`}>
                            {doc.file_name}
                          </a>
                          <span className="text-gray-500 mr-2">({(doc.size_bytes/1024).toFixed(1)} ك.ب)</span>
                          <span className="text-gray-400"> — {new Date(doc.created_at).toLocaleString('ar-SA')}</span>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              )}

              {/* أزرار الإجراءات */}
              <div className="flex justify-end space-x-2 space-x-reverse pt-4">
                {/* زر إرفاق وثائق */}
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => uploadInputRef.current?.click()}
                  className="flex items-center gap-2"
                  title="إرفاق وثائق"
                >
                  <Paperclip className="h-4 w-4" />
                  إرفاق وثائق
                </Button>
                <Button type="button" variant="outline" onClick={() => setIsModalOpen(false)}>
                  إلغاء
                </Button>
                <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
                  {modalType === 'add' ? 'حفظ' : 'تحديث'}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  )
}

export default function FollowsPage() {

  return <FollowsPageContent />

  // الكود الأصلي (معطل مؤقتاً):
  /*
  return (
    <ProtectedRoute
      userType="user"
      requiredPermissions={['add_follows', 'manage_follows', 'view_cases']}
    >
      <FollowsPageContent />
    </ProtectedRoute>
  )
  */
}
