const { Client } = require('pg');

async function setupCaseMovementsDatabase() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    user: 'postgres',
    password: 'yemen123',
    database: 'mohammidev'
  });

  try {
    await client.connect();
    console.log('🔗 متصل بقاعدة البيانات mohammi');

    // 1. إنشاء جدول حركة القضايا
    console.log('📋 إنشاء جدول case_movements...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS case_movements (
        id SERIAL PRIMARY KEY,
        case_id INTEGER NOT NULL,
        case_number VARCHAR(50),
        case_title VARCHAR(255),
        movement_type VARCHAR(100) NOT NULL,
        description TEXT NOT NULL,
        details JSONB,
        hearing_date DATE,
        hearing_time TIME,
        court_name VA<PERSON><PERSON><PERSON>(255),
        created_by INTEGER,
        created_by_name VA<PERSON><PERSON><PERSON>(255),
        user_role VARCHAR(100),
        movement_date DATE DEFAULT CURRENT_DATE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
        status VARCHAR(50) DEFAULT 'active',
        notes TEXT,
        reference_id INTEGER,
        reference_type VARCHAR(50),
        client_name VARCHAR(255),
        case_status VARCHAR(100),
        case_next_hearing DATE,
        court_full_name VARCHAR(500),
        employee_name VARCHAR(255)
      )
    `);
    console.log('✅ تم إنشاء جدول case_movements');

    // 2. إنشاء الفهارس لتحسين الأداء
    console.log('🔍 إنشاء الفهارس...');
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_case_movements_case_id ON case_movements(case_id);
      CREATE INDEX IF NOT EXISTS idx_case_movements_type ON case_movements(movement_type);
      CREATE INDEX IF NOT EXISTS idx_case_movements_date ON case_movements(movement_date);
      CREATE INDEX IF NOT EXISTS idx_case_movements_priority ON case_movements(priority);
      CREATE INDEX IF NOT EXISTS idx_case_movements_created_at ON case_movements(created_at);
    `);
    console.log('✅ تم إنشاء الفهارس');

    // 3. إنشاء جدول قوالب التنبيهات
    console.log('📧 إنشاء جدول notification_templates...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS notification_templates (
        id SERIAL PRIMARY KEY,
        name VARCHAR(100) UNIQUE NOT NULL,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        variables JSONB,
        channels JSONB DEFAULT '["system"]',
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ تم إنشاء جدول notification_templates');

    // 4. إنشاء جدول التنبيهات المجدولة
    console.log('⏰ إنشاء جدول scheduled_notifications...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS scheduled_notifications (
        id SERIAL PRIMARY KEY,
        case_id INTEGER,
        recipient_id INTEGER NOT NULL,
        recipient_type VARCHAR(20) NOT NULL CHECK (recipient_type IN ('client', 'employee', 'admin')),
        template_name VARCHAR(100) NOT NULL,
        title VARCHAR(255),
        content TEXT,
        variables JSONB,
        channels JSONB DEFAULT '["system"]',
        scheduled_for TIMESTAMP NOT NULL,
        sent_at TIMESTAMP,
        status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed', 'cancelled')),
        priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
        attempts INTEGER DEFAULT 0,
        last_error TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ تم إنشاء جدول scheduled_notifications');

    // 5. إنشاء جدول سجل التنبيهات
    console.log('📝 إنشاء جدول notification_logs...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS notification_logs (
        id SERIAL PRIMARY KEY,
        notification_id INTEGER REFERENCES scheduled_notifications(id),
        case_id INTEGER,
        recipient_id INTEGER NOT NULL,
        recipient_type VARCHAR(20) NOT NULL,
        channel VARCHAR(50) NOT NULL,
        title VARCHAR(255),
        content TEXT,
        sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status VARCHAR(20) DEFAULT 'sent' CHECK (status IN ('sent', 'failed', 'bounced')),
        response_data JSONB,
        error_message TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ تم إنشاء جدول notification_logs');

    // 6. إنشاء جدول تفضيلات التنبيهات
    console.log('⚙️ إنشاء جدول user_notification_preferences...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS user_notification_preferences (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        user_type VARCHAR(20) NOT NULL CHECK (user_type IN ('client', 'employee', 'admin')),
        notification_type VARCHAR(100) NOT NULL,
        channels JSONB DEFAULT '["system"]',
        is_enabled BOOLEAN DEFAULT true,
        timing_preferences JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(user_id, user_type, notification_type)
      )
    `);
    console.log('✅ تم إنشاء جدول user_notification_preferences');

    // 7. إنشاء جدول قواعد التنبيهات
    console.log('📋 إنشاء جدول notification_rules...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS notification_rules (
        id SERIAL PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        trigger_event VARCHAR(100) NOT NULL,
        conditions JSONB,
        template_name VARCHAR(100) NOT NULL,
        target_recipients JSONB NOT NULL,
        timing_offset INTERVAL,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ تم إنشاء جدول notification_rules');

    // 8. إدراج قوالب التنبيهات الأساسية
    console.log('📄 إدراج قوالب التنبيهات الأساسية...');
    await client.query(`
      INSERT INTO notification_templates (name, title, content, variables, channels) VALUES
      ('case_created_notification', 'تم إنشاء قضية جديدة', 'تم إنشاء قضية جديدة برقم {{case_number}} بعنوان "{{case_title}}"', '{"case_number": "", "case_title": ""}', '["system", "email"]'),
      ('case_assigned_notification', 'تم توزيع قضية عليك', 'تم توزيع القضية رقم {{case_number}} "{{case_title}}" عليك للمتابعة', '{"case_number": "", "case_title": ""}', '["system", "email"]'),
      ('hearing_reminder_3_days', 'تذكير: جلسة خلال 3 أيام', 'تذكير: لديك جلسة للقضية {{case_number}} يوم {{hearing_date}} في {{court_name}}', '{"case_number": "", "hearing_date": "", "court_name": ""}', '["system", "email", "sms"]'),
      ('hearing_reminder_1_day', 'تذكير عاجل: جلسة غداً', 'تذكير عاجل: لديك جلسة غداً للقضية {{case_number}} في {{court_name}} الساعة {{hearing_time}}', '{"case_number": "", "court_name": "", "hearing_time": ""}', '["system", "email", "sms", "whatsapp"]'),
      ('follow_added_notification', 'تم إضافة متابعة جديدة', 'تم إضافة متابعة جديدة للقضية {{case_number}}: {{follow_description}}', '{"case_number": "", "follow_description": ""}', '["system", "email"]'),
      ('document_uploaded', 'تم رفع وثيقة جديدة', 'تم رفع وثيقة جديدة "{{document_title}}" للقضية {{case_number}}', '{"case_number": "", "document_title": ""}', '["system", "email"]'),
      ('case_status_changed', 'تم تغيير حالة القضية', 'تم تغيير حالة القضية {{case_number}} من "{{old_status}}" إلى "{{new_status}}"', '{"case_number": "", "old_status": "", "new_status": ""}', '["system", "email"]'),
      ('case_inactive_30_days', 'تنبيه: قضية خاملة', 'القضية {{case_number}} خاملة منذ {{days_inactive}} يوم. يرجى المتابعة.', '{"case_number": "", "days_inactive": ""}', '["system", "email"]'),
      ('employee_hearing_reminder', 'تذكير جلسة للموظف', 'تذكير: لديك جلسة للقضية {{case_number}} يوم {{hearing_date}} في {{court_name}}', '{"case_number": "", "hearing_date": "", "court_name": ""}', '["system", "email"]'),
      ('case_closed_notification', 'تم إغلاق قضية', 'تم إغلاق القضية {{case_number}} بواسطة {{changed_by}}', '{"case_number": "", "changed_by": ""}', '["system", "email"]')
      ON CONFLICT (name) DO NOTHING
    `);
    console.log('✅ تم إدراج قوالب التنبيهات');

    // 9. إدراج بيانات تجريبية لحركة القضايا
    console.log('🎯 إدراج بيانات تجريبية...');
    await client.query(`
      INSERT INTO case_movements (
        case_id, case_number, case_title, movement_type, description, 
        created_by_name, priority, movement_date, client_name, case_status
      ) VALUES 
      (1, '2024/001', 'قضية تجارية - شركة الأمل', 'case_created', 'تم إنشاء القضية في النظام', 'النظام', 'normal', CURRENT_DATE, 'شركة الأمل للتجارة', 'نشطة'),
      (1, '2024/001', 'قضية تجارية - شركة الأمل', 'case_assigned', 'تم توزيع القضية على المحامي أحمد محمد', 'المدير العام', 'high', CURRENT_DATE, 'شركة الأمل للتجارة', 'موزعة'),
      (2, '2024/002', 'قضية عمالية - محمد علي', 'case_created', 'تم إنشاء القضية في النظام', 'النظام', 'normal', CURRENT_DATE - INTERVAL '1 day', 'محمد علي أحمد', 'نشطة'),
      (2, '2024/002', 'قضية عمالية - محمد علي', 'hearing_scheduled', 'تم تحديد جلسة للقضية في المحكمة العمالية', 'أحمد محمد', 'high', CURRENT_DATE - INTERVAL '1 day', 'محمد علي أحمد', 'جلسة مجدولة'),
      (3, '2024/003', 'قضية مدنية - فاطمة سالم', 'case_created', 'تم إنشاء القضية في النظام', 'النظام', 'normal', CURRENT_DATE - INTERVAL '2 days', 'فاطمة سالم محمد', 'نشطة'),
      (3, '2024/003', 'قضية مدنية - فاطمة سالم', 'document_uploaded', 'تم رفع وثيقة "عقد الإيجار الأصلي" للقضية', 'سارة أحمد', 'normal', CURRENT_DATE - INTERVAL '1 day', 'فاطمة سالم محمد', 'قيد المراجعة'),
      (4, '2024/004', 'قضية جنائية - خالد يوسف', 'case_created', 'تم إنشاء القضية في النظام', 'النظام', 'urgent', CURRENT_DATE - INTERVAL '3 days', 'خالد يوسف علي', 'نشطة'),
      (4, '2024/004', 'قضية جنائية - خالد يوسف', 'follow_added', 'تم إضافة متابعة: تم تقديم الاستئناف للمحكمة العليا', 'محمد الحاشدي', 'urgent', CURRENT_DATE - INTERVAL '2 days', 'خالد يوسف علي', 'استئناف'),
      (5, '2024/005', 'قضية إدارية - شركة النور', 'case_created', 'تم إنشاء القضية في النظام', 'النظام', 'normal', CURRENT_DATE - INTERVAL '4 days', 'شركة النور للمقاولات', 'نشطة'),
      (5, '2024/005', 'قضية إدارية - شركة النور', 'case_status_changed', 'تم تغيير حالة القضية من "نشطة" إلى "قيد المراجعة"', 'أحمد محمد', 'normal', CURRENT_DATE - INTERVAL '3 days', 'شركة النور للمقاولات', 'قيد المراجعة'),
      (6, '2024/006', 'قضية تجارية - مؤسسة الرياض', 'case_created', 'تم إنشاء القضية في النظام', 'النظام', 'normal', CURRENT_DATE - INTERVAL '5 days', 'مؤسسة الرياض التجارية', 'نشطة'),
      (6, '2024/006', 'قضية تجارية - مؤسسة الرياض', 'hearing_scheduled', 'تم تحديد جلسة للقضية في المحكمة التجارية', 'سارة أحمد', 'high', CURRENT_DATE - INTERVAL '4 days', 'مؤسسة الرياض التجارية', 'جلسة مجدولة'),
      (7, '2024/007', 'قضية أسرة - عبدالله حسن', 'case_created', 'تم إنشاء القضية في النظام', 'النظام', 'normal', CURRENT_DATE - INTERVAL '6 days', 'عبدالله حسن محمد', 'نشطة'),
      (7, '2024/007', 'قضية أسرة - عبدالله حسن', 'document_uploaded', 'تم رفع وثيقة "شهادة الزواج" للقضية', 'محمد الحاشدي', 'normal', CURRENT_DATE - INTERVAL '5 days', 'عبدالله حسن محمد', 'قيد المراجعة'),
      (8, '2024/008', 'قضية عقارية - منى سعد', 'case_created', 'تم إنشاء القضية في النظام', 'النظام', 'normal', CURRENT_DATE - INTERVAL '7 days', 'منى سعد أحمد', 'نشطة'),
      (8, '2024/008', 'قضية عقارية - منى سعد', 'follow_added', 'تم إضافة متابعة: تم الحصول على تقرير الخبير العقاري', 'أحمد محمد', 'normal', CURRENT_DATE - INTERVAL '6 days', 'منى سعد أحمد', 'قيد المراجعة')
      ON CONFLICT DO NOTHING
    `);
    console.log('✅ تم إدراج البيانات التجريبية');

    // 10. إنشاء فهارس إضافية للتنبيهات
    console.log('🔍 إنشاء فهارس التنبيهات...');
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_recipient ON scheduled_notifications(recipient_id, recipient_type);
      CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_scheduled_for ON scheduled_notifications(scheduled_for);
      CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_status ON scheduled_notifications(status);
      CREATE INDEX IF NOT EXISTS idx_notification_logs_recipient ON notification_logs(recipient_id, recipient_type);
      CREATE INDEX IF NOT EXISTS idx_notification_logs_sent_at ON notification_logs(sent_at);
    `);
    console.log('✅ تم إنشاء فهارس التنبيهات');

    console.log('\n🎉 تم إعداد نظام حركة القضايا والتنبيهات بنجاح!');
    console.log('📊 الإحصائيات:');
    
    // عرض إحصائيات الجداول
    const movementsCount = await client.query('SELECT COUNT(*) FROM case_movements');
    const templatesCount = await client.query('SELECT COUNT(*) FROM notification_templates');
    
    console.log(`   - حركات القضايا: ${movementsCount.rows[0].count}`);
    console.log(`   - قوالب التنبيهات: ${templatesCount.rows[0].count}`);
    console.log('\n🌐 يمكنك الآن زيارة: http://localhost:3300/movements');

  } catch (error) {
    console.error('❌ خطأ في إعداد قاعدة البيانات:', error);
    process.exit(1);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل السكريبت
setupCaseMovementsDatabase().catch(console.error);
