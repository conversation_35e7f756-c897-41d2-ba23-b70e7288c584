-- إضافة جدول الجلسات النشطة لتطبيق نظام الجلسة الواحدة
-- يسمح بجلسة واحدة فقط لكل مستخدم في نفس الوقت

-- ج<PERSON><PERSON><PERSON> الجلسات النشطة
CREATE TABLE IF NOT EXISTS active_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) NOT NULL UNIQUE,
    device_info TEXT,
    ip_address INET,
    user_agent TEXT,
    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- فهرس لتسريع البحث
CREATE INDEX IF NOT EXISTS idx_active_sessions_user_id ON active_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_active_sessions_token ON active_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_active_sessions_active ON active_sessions(is_active);

-- فهرس مركب للبحث السريع
CREATE INDEX IF NOT EXISTS idx_active_sessions_user_active ON active_sessions(user_id, is_active);

-- دالة لتنظيف الجلسات المنتهية الصلاحية (أكثر من 24 ساعة)
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS void AS $$
BEGIN
    DELETE FROM active_sessions 
    WHERE last_activity < NOW() - INTERVAL '24 hours'
       OR is_active = false;
END;
$$ LANGUAGE plpgsql;

-- دالة لإنهاء جميع الجلسات الأخرى للمستخدم
CREATE OR REPLACE FUNCTION terminate_other_sessions(p_user_id INTEGER, p_current_token VARCHAR(255))
RETURNS void AS $$
BEGIN
    UPDATE active_sessions 
    SET is_active = false,
        updated_at = CURRENT_TIMESTAMP
    WHERE user_id = p_user_id 
      AND session_token != p_current_token
      AND is_active = true;
END;
$$ LANGUAGE plpgsql;

-- دالة لتحديث آخر نشاط للجلسة
CREATE OR REPLACE FUNCTION update_session_activity(p_session_token VARCHAR(255))
RETURNS void AS $$
BEGIN
    UPDATE active_sessions 
    SET last_activity = CURRENT_TIMESTAMP,
        updated_at = CURRENT_TIMESTAMP
    WHERE session_token = p_session_token 
      AND is_active = true;
END;
$$ LANGUAGE plpgsql;

-- دالة للتحقق من صحة الجلسة
CREATE OR REPLACE FUNCTION is_session_valid(p_session_token VARCHAR(255))
RETURNS BOOLEAN AS $$
DECLARE
    session_exists BOOLEAN := false;
BEGIN
    SELECT EXISTS(
        SELECT 1 FROM active_sessions 
        WHERE session_token = p_session_token 
          AND is_active = true
          AND last_activity > NOW() - INTERVAL '24 hours'
    ) INTO session_exists;
    
    RETURN session_exists;
END;
$$ LANGUAGE plpgsql;

-- إضافة عمود session_token إلى جدول المستخدمين إذا لم يكن موجوداً
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'current_session_token'
    ) THEN
        ALTER TABLE users ADD COLUMN current_session_token VARCHAR(255);
        CREATE INDEX IF NOT EXISTS idx_users_session_token ON users(current_session_token);
    END IF;
END $$;

-- تنظيف الجلسات المنتهية الصلاحية عند بدء التشغيل
SELECT cleanup_expired_sessions();

-- إضافة تعليقات للتوضيح
COMMENT ON TABLE active_sessions IS 'جدول الجلسات النشطة - يسمح بجلسة واحدة فقط لكل مستخدم';
COMMENT ON COLUMN active_sessions.user_id IS 'معرف المستخدم';
COMMENT ON COLUMN active_sessions.session_token IS 'رمز الجلسة الفريد';
COMMENT ON COLUMN active_sessions.device_info IS 'معلومات الجهاز';
COMMENT ON COLUMN active_sessions.ip_address IS 'عنوان IP';
COMMENT ON COLUMN active_sessions.user_agent IS 'معلومات المتصفح';
COMMENT ON COLUMN active_sessions.login_time IS 'وقت تسجيل الدخول';
COMMENT ON COLUMN active_sessions.last_activity IS 'آخر نشاط';
COMMENT ON COLUMN active_sessions.is_active IS 'حالة الجلسة (نشطة/غير نشطة)';

-- رسالة نجاح
SELECT 'تم إنشاء نظام الجلسات النشطة بنجاح - جلسة واحدة فقط لكل مستخدم' AS result;
