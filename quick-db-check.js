const { Client } = require('pg');

async function quickDatabaseCheck() {
  console.log('🔍 فحص سريع لقاعدة البيانات...\n');

  // الإعدادات المختلفة للاختبار
  const configs = [
    { host: 'localhost', port: 5432, user: 'postgres', password: 'yemen123', database: 'postgres' },
    { host: 'localhost', port: 5432, user: 'postgres', password: 'postgres', database: 'postgres' },
    { host: 'localhost', port: 5432, user: 'postgres', password: '', database: 'postgres' },
    { host: 'localhost', port: 5432, user: 'postgres', password: 'admin', database: 'postgres' },
    { host: 'localhost', port: 5433, user: 'postgres', password: 'yemen123', database: 'postgres' },
    { host: '127.0.0.1', port: 5432, user: 'postgres', password: 'yemen123', database: 'postgres' },
    { host: 'localhost', port: 3306, user: 'root', password: 'yemen123', database: 'mysql' }, // MySQL
    { host: 'localhost', port: 3306, user: 'root', password: '', database: 'mysql' }, // MySQL XAMPP
  ];

  let foundConnection = null;

  for (let i = 0; i < configs.length; i++) {
    const config = configs[i];
    console.log(`${i + 1}. اختبار: ${config.user}@${config.host}:${config.port} (${config.database})`);
    
    const client = new Client({
      ...config,
      connectTimeoutMillis: 3000
    });

    try {
      await client.connect();
      console.log('   ✅ نجح الاتصال!');
      
      // جلب معلومات الخادم
      try {
        const versionResult = await client.query('SELECT version()');
        console.log(`   📋 الإصدار: ${versionResult.rows[0].version.substring(0, 50)}...`);
      } catch (e) {
        console.log('   📋 تم الاتصال ولكن لا يمكن جلب معلومات الإصدار');
      }

      // جلب قائمة قواعد البيانات
      try {
        const dbResult = await client.query(`
          SELECT datname FROM pg_database 
          WHERE datistemplate = false 
          ORDER BY datname
        `);
        
        console.log(`   📊 قواعد البيانات المتاحة (${dbResult.rows.length}):`);
        dbResult.rows.forEach(row => {
          console.log(`      - ${row.datname}`);
        });

        foundConnection = config;
        
        // فحص قواعد البيانات للبحث عن الجداول
        for (const dbRow of dbResult.rows) {
          const dbName = dbRow.datname;
          if (dbName === 'postgres' || dbName === 'template0' || dbName === 'template1') continue;
          
          const dbClient = new Client({
            ...config,
            database: dbName,
            connectTimeoutMillis: 3000
          });

          try {
            await dbClient.connect();
            
            const tablesResult = await dbClient.query(`
              SELECT table_name 
              FROM information_schema.tables 
              WHERE table_schema = 'public' 
              ORDER BY table_name
            `);

            if (tablesResult.rows.length > 0) {
              console.log(`\n   🎯 قاعدة البيانات ${dbName} تحتوي على ${tablesResult.rows.length} جدول`);
              
              // البحث عن جداول مهمة
              const importantTables = ['issues', 'case_movements', 'clients', 'employees', 'users'];
              const foundTables = [];
              
              for (const tableName of importantTables) {
                const tableExists = tablesResult.rows.find(t => t.table_name === tableName);
                if (tableExists) {
                  try {
                    const countResult = await dbClient.query(`SELECT COUNT(*) FROM ${tableName}`);
                    foundTables.push(`${tableName} (${countResult.rows[0].count} سجل)`);
                  } catch (e) {
                    foundTables.push(`${tableName} (خطأ في العد)`);
                  }
                }
              }
              
              if (foundTables.length > 0) {
                console.log(`      الجداول المهمة: ${foundTables.join(', ')}`);
              }
            }
            
            await dbClient.end();
          } catch (dbError) {
            // تجاهل أخطاء قواعد البيانات الفردية
          }
        }
        
      } catch (e) {
        console.log('   ⚠️ لا يمكن جلب قائمة قواعد البيانات');
      }

      await client.end();
      break; // توقف عند أول اتصال ناجح
      
    } catch (error) {
      console.log(`   ❌ فشل: ${error.message}`);
    }
  }

  if (foundConnection) {
    console.log('\n🎉 تم العثور على اتصال صحيح!');
    console.log('📋 الإعدادات الصحيحة:');
    console.log(`   المضيف: ${foundConnection.host}`);
    console.log(`   المنفذ: ${foundConnection.port}`);
    console.log(`   المستخدم: ${foundConnection.user}`);
    console.log(`   كلمة المرور: ${foundConnection.password || 'فارغة'}`);
    
    console.log('\n🔧 لتحديث المشروع:');
    console.log('1. حدث ملف routing.config.json بهذه الإعدادات');
    console.log('2. أعد تشغيل الخادم');
    console.log('3. جرب إنشاء الجداول مرة أخرى');
    
  } else {
    console.log('\n❌ لم يتم العثور على أي اتصال صحيح');
    console.log('\n🛠️ الحلول المقترحة:');
    console.log('1. تثبيت PostgreSQL من: https://www.postgresql.org/download/');
    console.log('2. أو تثبيت XAMPP مع PostgreSQL');
    console.log('3. أو استخدام SQLite كبديل');
    console.log('4. تحقق من تشغيل خدمة PostgreSQL في Services');
  }
}

quickDatabaseCheck().catch(console.error);
