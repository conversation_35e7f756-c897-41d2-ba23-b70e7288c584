import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// PUT - تحديث بيانات الشركة
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    const body = await request.json()
    const {
      name, legal_name, registration_number, address, city, country,
      phone, email, website, tax_number, commercial_register,
      logo_url, logo_right_text, logo_left_text, logo_image_url,
      established_date, legal_form, capital, description, is_active
    } = body
    
    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, error: 'معرف الشركة غير صحيح' },
        { status: 400 }
      )
    }

    if (!name) {
      return NextResponse.json(
        { success: false, error: 'اسم الشركة مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(`
      UPDATE companies SET
        name = $1, legal_name = $2, registration_number = $3, address = $4, 
        city = $5, country = $6, phone = $7, email = $8, website = $9, 
        tax_number = $10, commercial_register = $11, logo_url = $12, 
        logo_right_text = $13, logo_left_text = $14, logo_image_url = $15,
        established_date = $16, legal_form = $17, capital = $18, 
        description = $19, is_active = $20, updated_date = NOW()
      WHERE id = $21
      RETURNING *
    `, [
      name, legal_name, registration_number, address, city, country,
      phone, email, website, tax_number, commercial_register,
      logo_url, logo_right_text, logo_left_text, logo_image_url,
      established_date, legal_form, capital, description, is_active ?? true, id
    ])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الشركة غير موجودة' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم تحديث بيانات الشركة بنجاح'
    })
  } catch (error) {
    console.error('Error updating company:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث بيانات الشركة' },
      { status: 500 }
    )
  }
}

// DELETE - حذف شركة
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    
    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, error: 'معرف الشركة غير صحيح' },
        { status: 400 }
      )
    }

    await query('DELETE FROM companies WHERE id = $1', [id])

    return NextResponse.json({
      success: true,
      message: 'تم حذف الشركة بنجاح'
    })
  } catch (error) {
    console.error('Error deleting company:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الشركة' },
      { status: 500 }
    )
  }
}