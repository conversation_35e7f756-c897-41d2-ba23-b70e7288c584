import { NextResponse } from 'next/server'
import { query } from '@/lib/db'

export async function POST() {
  try {
    // إضافة بيانات العملاء
    await query(`
      INSERT INTO clients (name, phone, email, address, id_number, status) VALUES
      ('أحمد محمد علي', '01234567890', '<EMAIL>', 'الرياض - المملكة العربية السعودية', '1234567890', 'active'),
      ('فاطمة سعد العتيبي', '01987654321', '<EMAIL>', 'جدة - المملكة العربية السعودية', '1987654321', 'active'),
      ('خالد عبدالله الزهراني', '01555666777', '<EMAIL>', 'الدمام - المملكة العربية السعودية', '1555666777', 'active')
      ON CONFLICT (id_number) DO NOTHING
    `)

    // إضافة بيانات الموظفين
    await query(`
      INSERT INTO employees (name, position, department, phone, email, salary, hire_date, id_number, status) VALUES
      ('سارة أحمد المطيري', 'محامي أول', 'القسم القانوني', '01122334455', '<EMAIL>', 8000.00, '2023-01-15', '2234567891', 'active'),
      ('محمد علي الغامدي', 'محامي', 'القسم المدني', '01556677888', '<EMAIL>', 6500.00, '2023-03-20', '2987654322', 'active'),
      ('نورا سلمان القحطاني', 'سكرتيرة قانونية', 'الإدارة', '01444555666', '<EMAIL>', 4500.00, '2023-02-10', '2555666778', 'active')
      ON CONFLICT (id_number) DO NOTHING
    `)

    // إضافة أنواع القضايا
    await query(`
      INSERT INTO issue_types (name, description, color) VALUES
      ('قضايا مدنية', 'القضايا المدنية والتجارية', '#3B82F6'),
      ('قضايا جنائية', 'القضايا الجنائية والعقوبات', '#EF4444'),
      ('قضايا أسرة', 'قضايا الأحوال الشخصية والأسرة', '#10B981'),
      ('قضايا عمالية', 'قضايا العمل والعمال', '#F59E0B'),
      ('قضايا إدارية', 'القضايا الإدارية والحكومية', '#8B5CF6')
      ON CONFLICT (name) DO NOTHING
    `)

    // إضافة قضايا تجريبية
    await query(`
      INSERT INTO issues (case_number, title, description, client_name, court_name, issue_type, status, amount, next_hearing) VALUES
      ('2024/001', 'قضية مطالبة مالية', 'مطالبة بمبلغ مالي مستحق', 'أحمد محمد علي', 'محكمة الرياض العامة', 'قضايا مدنية', 'جاري', 150000.00, '2024-08-15'),
      ('2024/002', 'قضية طلاق', 'دعوى طلاق للضرر', 'فاطمة سعد العتيبي', 'محكمة جدة الشرعية', 'قضايا أسرة', 'منتهية', 0, NULL),
      ('2024/003', 'قضية عمالية', 'مطالبة بمستحقات عمالية', 'خالد عبدالله الزهراني', 'لجنة المنازعات العمالية', 'قضايا عمالية', 'معلقة', 45000.00, '2024-09-01')
      ON CONFLICT (case_number) DO NOTHING
    `)

    // إضافة متابعات
    await query(`
      INSERT INTO follows (case_number, case_title, client_name, follow_type, description, due_date, status) VALUES
      ('2024/001', 'قضية مطالبة مالية', 'أحمد محمد علي', 'جلسة محكمة', 'الحضور لجلسة المرافعة النهائية', '2024-08-15', 'معلق'),
      ('2024/003', 'قضية عمالية', 'خالد عبدالله الزهراني', 'تقديم مستندات', 'تقديم المستندات المطلوبة للجنة', '2024-08-20', 'معلق')
      ON CONFLICT DO NOTHING
    `)

    // إضافة النسب المالية
    await query(`
      INSERT INTO lineages (name, management_share, court_share, commission_share, other_share) VALUES
      ('النسبة العامة', 60.00, 15.00, 20.00, 5.00),
      ('قضايا كبرى', 50.00, 20.00, 25.00, 5.00),
      ('قضايا بسيطة', 70.00, 10.00, 15.00, 5.00)
      ON CONFLICT (name) DO NOTHING
    `)

    return NextResponse.json({
      success: true,
      message: 'تم إضافة البيانات التجريبية بنجاح'
    })

  } catch (error) {
    console.error('Error seeding data:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة البيانات التجريبية: ' + error },
      { status: 500 }
    )
  }
}
