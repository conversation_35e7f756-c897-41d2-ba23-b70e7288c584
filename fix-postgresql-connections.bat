@echo off
chcp 65001 >nul
echo ========================================
echo   Fix PostgreSQL Connection Issues
echo ========================================
echo.

echo 1. Killing any stuck PostgreSQL processes...
taskkill /f /im postgres.exe 2>nul
taskkill /f /im pg_ctl.exe 2>nul
echo.

echo 2. Clearing connection locks...
REM Clear any connection locks that might exist
echo Waiting 3 seconds for processes to terminate...
timeout /t 3 /nobreak >nul

echo.
echo 3. Restarting PostgreSQL service...
echo.

REM Stop services first
echo Stopping services...
net stop postgresql-x64-15 2>nul
net stop postgresql-x64-14 2>nul
net stop postgresql-x64-13 2>nul
net stop postgresql 2>nul

echo Waiting 5 seconds...
timeout /t 5 /nobreak >nul

REM Start services
echo Starting services...
net start postgresql-x64-15 2>nul && goto :success
net start postgresql-x64-14 2>nul && goto :success
net start postgresql-x64-13 2>nul && goto :success
net start postgresql 2>nul && goto :success

echo.
echo ❌ Failed to start PostgreSQL service
echo.
echo Manual steps:
echo 1. Open Services.msc
echo 2. Find postgresql service
echo 3. Right-click and select Restart
echo 4. If disabled, set to Automatic and Start
echo.
goto :end

:success
echo ✅ PostgreSQL service restarted successfully!
echo.
echo 4. Testing connection with yemen123...
echo.

node -e "
const { Client } = require('pg');

async function testConnection() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    user: 'postgres',
    password: 'yemen123',
    database: 'postgres',
    connectTimeoutMillis: 10000
  });

  try {
    console.log('Connecting...');
    await client.connect();
    console.log('✅ Connected successfully!');
    
    const result = await client.query('SELECT datname FROM pg_database WHERE datistemplate = false ORDER BY datname');
    console.log('📊 Databases found:', result.rows.length);
    
    result.rows.forEach(row => {
      if (row.datname === 'mohammi' || row.datname === 'mohammidev') {
        console.log('🎯 FOUND:', row.datname);
      } else {
        console.log('   -', row.datname);
      }
    });
    
    await client.end();
    console.log('');
    console.log('🎉 PostgreSQL is working! You can now:');
    console.log('1. Open: http://localhost:3300/test-postgres');
    console.log('2. Run the connection test');
    console.log('3. Update project settings');
    
  } catch (error) {
    console.log('❌ Still failed:', error.message);
    console.log('');
    console.log('Try these steps:');
    console.log('1. Check if password is correct');
    console.log('2. Try different passwords: postgres, admin, (empty)');
    console.log('3. Check pg_hba.conf file');
    console.log('4. Reinstall PostgreSQL if needed');
  }
}

testConnection();
"

:end
echo.
pause
