'use client'

import { useState, useEffect } from 'react'

export function UltraSimpleSelect() {
  const [options, setOptions] = useState<string[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchData = async () => {
      try {

        const response = await fetch('/api/clients')

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const data = await response.json()

        if (data.success && data.clients) {
          const clientOptions = data.clients.map((client: any) => 
            `${client.name} (${client.account_code || 'بدون كود'})`
          )
          setOptions(clientOptions)

        } else {
          setError('البيانات غير صحيحة')
        }

      } catch (err: any) {
        console.error('❌ UltraSimpleSelect: خطأ:', err)
        setError(err.message)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  return (
    <div className="space-y-2">
      <h4 className="font-medium text-purple-700">🧪 اختبار فائق البساطة</h4>

      {loading && (
        <div className="p-2 bg-yellow-100 border border-yellow-300 rounded text-yellow-700">
          ⏳ جاري التحميل...
        </div>
      )}

      {error && (
        <div className="p-2 bg-red-100 border border-red-300 rounded text-red-700">
          ❌ خطأ: {error}
        </div>
      )}

      {!loading && !error && (
        <div className="space-y-2">
          <div className="p-2 bg-green-100 border border-green-300 rounded text-green-700">
            ✅ تم تحميل {options.length} خيار بنجاح
          </div>

          <select className="w-full p-2 border border-gray-300 rounded">
            <option value="">اختر عميل...</option>
            {options.map((option, index) => (
              <option key={index} value={index.toString()}>
                {option}
              </option>
            ))}
          </select>

          <div className="text-xs text-gray-500">
            الخيارات المتاحة: {options.join(', ')}
          </div>
        </div>
      )}
    </div>
  )
}
