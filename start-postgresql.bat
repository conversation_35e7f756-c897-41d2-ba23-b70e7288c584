@echo off
chcp 65001 >nul
echo ========================================
echo   Starting PostgreSQL Service
echo ========================================
echo.

echo 1. Checking current PostgreSQL services...
sc query | findstr /i postgres
echo.

echo 2. Attempting to start PostgreSQL services...
echo.

REM Try different service names
echo Trying postgresql-x64-15...
net start postgresql-x64-15
if %errorlevel% equ 0 (
    echo SUCCESS: postgresql-x64-15 started
    goto :success
)

echo Trying postgresql-x64-14...
net start postgresql-x64-14
if %errorlevel% equ 0 (
    echo SUCCESS: postgresql-x64-14 started
    goto :success
)

echo Trying postgresql-x64-13...
net start postgresql-x64-13
if %errorlevel% equ 0 (
    echo SUCCESS: postgresql-x64-13 started
    goto :success
)

echo Trying postgresql-x64-12...
net start postgresql-x64-12
if %errorlevel% equ 0 (
    echo SUCCESS: postgresql-x64-12 started
    goto :success
)

echo Trying postgresql...
net start postgresql
if %errorlevel% equ 0 (
    echo SUCCESS: postgresql started
    goto :success
)

echo.
echo ========================================
echo   Service Start Failed
echo ========================================
echo.
echo PostgreSQL service not found or failed to start.
echo.
echo Possible solutions:
echo 1. PostgreSQL is not installed
echo 2. Service name is different
echo 3. Service is disabled
echo 4. Permission issues
echo.
echo Next steps:
echo 1. Check if PostgreSQL is installed in Control Panel
echo 2. Open Services.msc and look for postgresql manually
echo 3. Install PostgreSQL if not found
echo.
goto :end

:success
echo.
echo ========================================
echo   PostgreSQL Started Successfully!
echo ========================================
echo.
echo Testing connection...
node simple-postgres-test.js
echo.

:end
pause
