param(
  [ValidateSet('start','stop','restart','unregister','status')]
  [string]$Action = 'status',
  [string[]]$Services = @('LegalSystemApp3000','LegalSystemApp3001','LegalSystemDev3300')
)

function Do-Start($n){ try { Start-ScheduledTask -TaskName $n } catch { Write-Warning "Start ${n}: $($_.Exception.Message)" } }
function Do-Stop($n){ try { Stop-ScheduledTask -TaskName $n } catch { } }
function Do-Unregister($n){ try { Unregister-ScheduledTask -TaskName $n -Confirm:$false } catch { } }
function Do-Status($n){ $t = Get-ScheduledTask -TaskName $n -ErrorAction SilentlyContinue; if($t){ Write-Host ("${n}: " + $t.State) } else { Write-Host "${n}: not found" } }

switch($Action){
  'start'     { foreach($s in $Services){ Do-Start $s } }
  'stop'      { foreach($s in $Services){ Do-Stop $s } }
  'restart'   { foreach($s in $Services){ Do-Stop $s; Start-Sleep -Seconds 1; Do-Start $s } }
  'unregister'{ foreach($s in $Services){ Do-Unregister $s } }
  'status'    { foreach($s in $Services){ Do-Status $s } }
}
