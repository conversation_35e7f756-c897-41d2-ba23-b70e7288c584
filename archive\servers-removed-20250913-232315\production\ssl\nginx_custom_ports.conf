# تكوين Nginx لـ mohammi.com مع المنافذ المخصصة
# المنفذ 7443: قاعدة البيانات mohammi
# المنفذ 8914: قاعدة البيانات rubaie
# المنفذ 443 محجوز لنظام آخر

# خادم SSL للمنفذ 7443 (قاعدة البيانات mohammi)
server {
    listen 7443 ssl http2;
    listen [::]:7443 ssl http2;
    server_name mohammi.com www.mohammi.com ***********;
    
    # مسارات شهادات SSL
    ssl_certificate /etc/ssl/mohammi/mohammi_com.crt;
    ssl_certificate_key /etc/ssl/mohammi/mohammi_com.key;
    
    # إعدادات SSL الأمنية
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # HSTS (HTTP Strict Transport Security)
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Headers الأمان
    add_header X-Content-Type-Options nosniff always;
    add_header X-Frame-Options DENY always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header X-Database "mohammi" always;
    add_header X-SSL-Port "7443" always;
    
    # إعدادات الضغط
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # إعدادات التخزين المؤقت للملفات الثابتة
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    }
    
    # الصفحة الرئيسية - عرض معلومات SSL
    location = / {
        return 200 '<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>🔐 SSL - mohammi.com:7443</title>
    <style>
        body { font-family: Arial, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; margin: 0; padding: 50px; text-align: center; }
        .container { background: rgba(255,255,255,0.1); padding: 40px; border-radius: 20px; backdrop-filter: blur(10px); max-width: 600px; margin: 0 auto; }
        .status { color: #4ade80; font-weight: bold; }
        .info { background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; margin: 20px 0; text-align: right; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 شهادة SSL تعمل بنجاح!</h1>
        <h2>المنفذ 7443 - قاعدة البيانات mohammi</h2>
        <div class="info">
            <h3>معلومات الاتصال:</h3>
            <p><strong>البروتوكول:</strong> <span class="status">HTTPS ✅</span></p>
            <p><strong>المنفذ:</strong> 7443</p>
            <p><strong>قاعدة البيانات:</strong> mohammi</p>
            <p><strong>IP:</strong> ***********</p>
            <p><strong>الدومين:</strong> mohammi.com</p>
            <p><strong>التشفير:</strong> TLS 1.2+</p>
        </div>
        <div class="info">
            <h3>🔗 روابط الوصول:</h3>
            <p>• https://mohammi.com:7443</p>
            <p>• https://***********:7443</p>
        </div>
        <div class="info">
            <p class="status">✅ شهادة SSL صالحة ومُفعلة</p>
        </div>
    </div>
</body>
</html>';
        add_header Content-Type text/html;
    }
    
    # باقي المسارات - يمكن إضافة proxy هنا لاحقاً
    location / {
        # مؤقتاً - عرض صفحة معلومات
        return 200 '{"status":"SSL Active","port":7443,"database":"mohammi","ssl":"enabled"}';
        add_header Content-Type application/json;
    }
    
    # سجلات الوصول والأخطاء
    access_log /var/log/nginx/mohammi-7443.access.log;
    error_log /var/log/nginx/mohammi-7443.error.log;
}

# خادم SSL للمنفذ 8914 (قاعدة البيانات rubaie)
server {
    listen 8914 ssl http2;
    listen [::]:8914 ssl http2;
    server_name mohammi.com www.mohammi.com ***********;
    
    # مسارات شهادات SSL (نفس الشهادة)
    ssl_certificate /etc/ssl/mohammi/mohammi_com.crt;
    ssl_certificate_key /etc/ssl/mohammi/mohammi_com.key;
    
    # إعدادات SSL الأمنية
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # HSTS (HTTP Strict Transport Security)
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Headers الأمان
    add_header X-Content-Type-Options nosniff always;
    add_header X-Frame-Options DENY always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header X-Database "rubaie" always;
    add_header X-SSL-Port "8914" always;
    
    # إعدادات الضغط
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # الصفحة الرئيسية - عرض معلومات SSL
    location = / {
        return 200 '<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>🔐 SSL - mohammi.com:8914</title>
    <style>
        body { font-family: Arial, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; margin: 0; padding: 50px; text-align: center; }
        .container { background: rgba(255,255,255,0.1); padding: 40px; border-radius: 20px; backdrop-filter: blur(10px); max-width: 600px; margin: 0 auto; }
        .status { color: #4ade80; font-weight: bold; }
        .info { background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; margin: 20px 0; text-align: right; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 شهادة SSL تعمل بنجاح!</h1>
        <h2>المنفذ 8914 - قاعدة البيانات rubaie</h2>
        <div class="info">
            <h3>معلومات الاتصال:</h3>
            <p><strong>البروتوكول:</strong> <span class="status">HTTPS ✅</span></p>
            <p><strong>المنفذ:</strong> 8914</p>
            <p><strong>قاعدة البيانات:</strong> rubaie</p>
            <p><strong>IP:</strong> ***********</p>
            <p><strong>الدومين:</strong> mohammi.com</p>
            <p><strong>التشفير:</strong> TLS 1.2+</p>
        </div>
        <div class="info">
            <h3>🔗 روابط الوصول:</h3>
            <p>• https://mohammi.com:8914</p>
            <p>• https://***********:8914</p>
        </div>
        <div class="info">
            <p class="status">✅ شهادة SSL صالحة ومُفعلة</p>
        </div>
    </div>
</body>
</html>';
        add_header Content-Type text/html;
    }
    
    # باقي المسارات - يمكن إضافة proxy هنا لاحقاً
    location / {
        # مؤقتاً - عرض صفحة معلومات
        return 200 '{"status":"SSL Active","port":8914,"database":"rubaie","ssl":"enabled"}';
        add_header Content-Type application/json;
    }
    
    # سجلات الوصول والأخطاء
    access_log /var/log/nginx/mohammi-8914.access.log;
    error_log /var/log/nginx/mohammi-8914.error.log;
}

# خادم HTTP لإعادة التوجيه (المنفذ 80)
server {
    listen 80;
    listen [::]:80;
    server_name mohammi.com www.mohammi.com ***********;
    
    # إعادة توجيه إلى HTTPS مع المنفذ المناسب
    location / {
        # توجيه افتراضي للمنفذ 7443
        return 301 https://$server_name:7443$request_uri;
    }
    
    # مسار خاص للمنفذ 8914
    location /rubaie {
        return 301 https://$server_name:8914$request_uri;
    }
}

# خادم HTTP للمنفذ 7080 (إعادة توجيه للمنفذ 7443)
server {
    listen 7080;
    listen [::]:7080;
    server_name mohammi.com www.mohammi.com ***********;
    
    location / {
        return 301 https://$server_name:7443$request_uri;
    }
}

# خادم HTTP للمنفذ 8080 (إعادة توجيه للمنفذ 8914)
server {
    listen 8080;
    listen [::]:8080;
    server_name mohammi.com www.mohammi.com ***********;
    
    location / {
        return 301 https://$server_name:8914$request_uri;
    }
}
