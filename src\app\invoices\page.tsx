'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  FileText,
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  DollarSign,
  Calendar,
  User,
  Clock,
  CheckCircle,
  AlertCircle,
  Send,
  Printer
} from 'lucide-react'

interface Invoice {
  id: number
  invoice_number: string
  client_id: number
  client_name: string
  client_phone: string
  client_email: string
  invoice_date: string
  due_date: string
  subtotal: number
  tax_rate: number
  tax_amount: number
  discount_amount: number
  total_amount: number
  paid_amount: number
  status: string
  payment_status: string
  payment_date: string
  notes: string
  created_by_name: string
  created_date: string
  items: InvoiceItem[]
}

interface InvoiceItem {
  id: number
  description: string
  quantity: number
  unit_price: number
  total_price: number
  item_type: string
  task_description: string
  case_title: string
  case_number: string
}

interface InvoiceStats {
  total_invoices: number
  total_amount: number
  paid_amount: number
  draft_count: number
  sent_count: number
  paid_count: number
  overdue_count: number
}

export default function InvoicesPage() {
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [stats, setStats] = useState<InvoiceStats>({
    total_invoices: 0,
    total_amount: 0,
    paid_amount: 0,
    draft_count: 0,
    sent_count: 0,
    paid_count: 0,
    overdue_count: 0
  })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [selectedPaymentStatus, setSelectedPaymentStatus] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null)

  // جلب الفواتير
  const fetchInvoices = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20'
      })

      if (selectedStatus) params.append('status', selectedStatus)
      if (selectedPaymentStatus) params.append('payment_status', selectedPaymentStatus)

      const response = await fetch(`/api/invoices?${params}`)
      const data = await response.json()

      if (data.success) {
        setInvoices(data.data.invoices)
        setStats(data.data.statistics)
      }
    } catch (error) {
      console.error('خطأ في جلب الفواتير:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchInvoices()
  }, [currentPage, selectedStatus, selectedPaymentStatus])

  // تنسيق المبلغ
  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  // تنسيق التاريخ
  const formatDate = (dateString: string) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // الحصول على لون الحالة
  const getStatusColor = (status: string) => {
    const colors: { [key: string]: string } = {
      'draft': 'bg-gray-100 text-gray-800',
      'sent': 'bg-blue-100 text-blue-800',
      'paid': 'bg-green-100 text-green-800',
      'overdue': 'bg-red-100 text-red-800',
      'cancelled': 'bg-red-100 text-red-800'
    }
    return colors[status] || 'bg-gray-100 text-gray-800'
  }

  // الحصول على لون حالة الدفع
  const getPaymentStatusColor = (paymentStatus: string) => {
    const colors: { [key: string]: string } = {
      'unpaid': 'bg-yellow-100 text-yellow-800',
      'partial': 'bg-orange-100 text-orange-800',
      'paid': 'bg-green-100 text-green-800'
    }
    return colors[paymentStatus] || 'bg-gray-100 text-gray-800'
  }

  // إنشاء فاتورة تلقائية
  const createAutoInvoice = async (clientId: number) => {
    try {
      const response = await fetch('/api/invoices', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          clientId,
          autoGenerate: true,
          createdBy: 1 // سيتم تحديثه لاحقاً من الجلسة
        })
      })

      const data = await response.json()
      if (data.success) {
        fetchInvoices()
        alert('تم إنشاء الفاتورة بنجاح')
      } else {
        alert(data.error || 'فشل في إنشاء الفاتورة')
      }
    } catch (error) {
      console.error('خطأ في إنشاء الفاتورة:', error)
      alert('خطأ في إنشاء الفاتورة')
    }
  }

  // تحديث حالة الفاتورة
  const updateInvoiceStatus = async (invoiceId: number, status: string, paymentStatus?: string, paidAmount?: number) => {
    try {
      const response = await fetch('/api/invoices', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: invoiceId,
          status,
          paymentStatus,
          paidAmount,
          paymentDate: paymentStatus === 'paid' ? new Date().toISOString().split('T')[0] : null
        })
      })

      const data = await response.json()
      if (data.success) {
        fetchInvoices()
        alert('تم تحديث الفاتورة بنجاح')
      } else {
        alert(data.error || 'فشل في تحديث الفاتورة')
      }
    } catch (error) {
      console.error('خطأ في تحديث الفاتورة:', error)
      alert('خطأ في تحديث الفاتورة')
    }
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان والأدوات */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة الفواتير</h1>
            <p className="text-gray-600 mt-1">
              إجمالي الفواتير: {stats.total_invoices} • المبلغ الإجمالي: {formatAmount(stats.total_amount)}
            </p>
          </div>
          <div className="flex gap-3">
            <Button 
              onClick={() => setShowCreateModal(true)}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Plus className="h-4 w-4 mr-2" />
              فاتورة جديدة
            </Button>
            <Button 
              variant="outline"
              className="bg-green-600 text-white hover:bg-green-700"
            >
              <FileText className="h-4 w-4 mr-2" />
              فاتورة تلقائية
            </Button>
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-blue-600" />
                <div className="mr-4">
                  <div className="text-2xl font-bold">{stats.total_invoices}</div>
                  <div className="text-sm text-gray-600">إجمالي الفواتير</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <DollarSign className="h-8 w-8 text-green-600" />
                <div className="mr-4">
                  <div className="text-2xl font-bold">{formatAmount(stats.total_amount)}</div>
                  <div className="text-sm text-gray-600">إجمالي المبلغ</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-purple-600" />
                <div className="mr-4">
                  <div className="text-2xl font-bold">{formatAmount(stats.paid_amount)}</div>
                  <div className="text-sm text-gray-600">المبلغ المدفوع</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <AlertCircle className="h-8 w-8 text-red-600" />
                <div className="mr-4">
                  <div className="text-2xl font-bold">{stats.overdue_count}</div>
                  <div className="text-sm text-gray-600">فواتير متأخرة</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* أدوات البحث والتصفية */}
        <Card>
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في الفواتير..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>
              <div>
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">جميع الحالات</option>
                  <option value="draft">مسودة</option>
                  <option value="sent">مرسلة</option>
                  <option value="paid">مدفوعة</option>
                  <option value="overdue">متأخرة</option>
                  <option value="cancelled">ملغية</option>
                </select>
              </div>
              <div>
                <select
                  value={selectedPaymentStatus}
                  onChange={(e) => setSelectedPaymentStatus(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">جميع حالات الدفع</option>
                  <option value="unpaid">غير مدفوعة</option>
                  <option value="partial">مدفوعة جزئياً</option>
                  <option value="paid">مدفوعة بالكامل</option>
                </select>
              </div>
              <div>
                <Button variant="outline" className="w-full">
                  <Filter className="h-4 w-4 mr-2" />
                  تصفية متقدمة
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* قائمة الفواتير */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              الفواتير
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">جاري التحميل...</p>
              </div>
            ) : invoices.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">لا توجد فواتير</p>
              </div>
            ) : (
              <div className="space-y-4">
                {invoices.map((invoice) => (
                  <div
                    key={invoice.id}
                    className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <div className="flex items-center gap-2">
                            <FileText className="h-6 w-6 text-blue-600" />
                            <div>
                              <h3 className="font-semibold text-lg">{invoice.invoice_number}</h3>
                              <p className="text-sm text-gray-600">{invoice.client_name}</p>
                            </div>
                          </div>
                        </div>

                        <div className="flex flex-wrap gap-2 mb-3">
                          <Badge className={getStatusColor(invoice.status)}>
                            {invoice.status === 'draft' ? 'مسودة' :
                             invoice.status === 'sent' ? 'مرسلة' :
                             invoice.status === 'paid' ? 'مدفوعة' :
                             invoice.status === 'overdue' ? 'متأخرة' : 'ملغية'}
                          </Badge>
                          <Badge className={getPaymentStatusColor(invoice.payment_status)}>
                            {invoice.payment_status === 'unpaid' ? 'غير مدفوعة' :
                             invoice.payment_status === 'partial' ? 'مدفوعة جزئياً' : 'مدفوعة بالكامل'}
                          </Badge>
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">تاريخ الفاتورة:</span>
                            <span className="font-semibold mr-2">{formatDate(invoice.invoice_date)}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">تاريخ الاستحقاق:</span>
                            <span className="font-semibold mr-2">{formatDate(invoice.due_date)}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">المبلغ الإجمالي:</span>
                            <span className="font-semibold mr-2">{formatAmount(invoice.total_amount)}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">المبلغ المدفوع:</span>
                            <span className="font-semibold mr-2">{formatAmount(invoice.paid_amount)}</span>
                          </div>
                        </div>

                        {invoice.items && invoice.items.length > 0 && (
                          <div className="mt-3 p-3 bg-gray-50 rounded-md">
                            <h4 className="font-medium text-sm mb-2">عناصر الفاتورة:</h4>
                            <div className="space-y-1">
                              {invoice.items.slice(0, 3).map((item, index) => (
                                <div key={index} className="text-xs text-gray-600 flex justify-between">
                                  <span>{item.description}</span>
                                  <span>{formatAmount(item.total_price)}</span>
                                </div>
                              ))}
                              {invoice.items.length > 3 && (
                                <div className="text-xs text-gray-500">
                                  و {invoice.items.length - 3} عنصر آخر...
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>

                      <div className="flex flex-col gap-2">
                        <div className="flex gap-2">
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="outline">
                            <Printer className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="outline">
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                        
                        <div className="flex gap-2">
                          {invoice.status === 'draft' && (
                            <Button 
                              size="sm" 
                              className="bg-blue-600 hover:bg-blue-700 text-xs"
                              onClick={() => updateInvoiceStatus(invoice.id, 'sent')}
                            >
                              <Send className="h-3 w-3 mr-1" />
                              إرسال
                            </Button>
                          )}
                          
                          {invoice.payment_status !== 'paid' && (
                            <Button 
                              size="sm" 
                              className="bg-green-600 hover:bg-green-700 text-xs"
                              onClick={() => updateInvoiceStatus(invoice.id, invoice.status, 'paid', invoice.total_amount)}
                            >
                              <CheckCircle className="h-3 w-3 mr-1" />
                              دفع
                            </Button>
                          )}
                        </div>
                        
                        <div className="flex gap-2">
                          <Button size="sm" variant="outline">
                            <Edit className="h-4 w-4" />
                          </Button>
                          {invoice.status === 'draft' && (
                            <Button size="sm" variant="outline" className="text-red-600 hover:text-red-700">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* نافذة إنشاء فاتورة */}
        {showCreateModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <h3 className="text-lg font-semibold mb-4">إنشاء فاتورة جديدة</h3>
              
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">العميل</label>
                    <select className="w-full p-2 border border-gray-300 rounded-md">
                      <option value="">اختر العميل</option>
                      {/* سيتم جلب قائمة العملاء من API */}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">تاريخ الفاتورة</label>
                    <Input type="date" />
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">تاريخ الاستحقاق</label>
                    <Input type="date" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">معدل الضريبة (%)</label>
                    <Input type="number" placeholder="15" />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">ملاحظات</label>
                  <textarea 
                    className="w-full p-2 border border-gray-300 rounded-md"
                    rows={3}
                    placeholder="ملاحظات إضافية"
                  />
                </div>
                
                <div className="flex items-center gap-2">
                  <input type="checkbox" id="autoGenerate" />
                  <label htmlFor="autoGenerate" className="text-sm">
                    إنشاء تلقائي من تسجيلات الوقت غير المفوترة
                  </label>
                </div>
              </div>
              
              <div className="flex gap-3 mt-6">
                <Button className="flex-1 bg-blue-600 hover:bg-blue-700">
                  <Plus className="h-4 w-4 mr-2" />
                  إنشاء الفاتورة
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => setShowCreateModal(false)}
                  className="flex-1"
                >
                  إلغاء
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}