'use client'

import { useState, useEffect } from 'react'
import { X, Volume2, VolumeX, ChevronLeft, ChevronRight } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface Announcement {
  id: number
  announcement_1?: string
  announcement_2?: string
  announcement_3?: string
  announcement_4?: string
  is_active: boolean
}

export function AnnouncementsBar() {
  const [announcements, setAnnouncements] = useState<string[]>([])
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isVisible, setIsVisible] = useState(true)
  const [isPaused, setIsPaused] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchAnnouncements()
  }, [])

  useEffect(() => {
    if (announcements.length > 1 && !isPaused) {
      const interval = setInterval(() => {
        setCurrentIndex((prev) => (prev + 1) % announcements.length)
      }, 5000) // تغيير كل 5 ثواني

      return () => clearInterval(interval)
    }
  }, [announcements.length, isPaused])

  const fetchAnnouncements = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/announcements')
      const data = await response.json()
      
      if (data.success && data.data.length > 0) {
        const announcement = data.data[0]
        if (announcement.is_active) {
          const activeAnnouncements = [
            announcement.announcement_1,
            announcement.announcement_2,
            announcement.announcement_3,
            announcement.announcement_4
          ].filter(Boolean) // إزالة القيم الفارغة
          
          setAnnouncements(activeAnnouncements)
        }
      }
    } catch (error) {
      console.error('Error fetching announcements:', error)
    } finally {
      setLoading(false)
    }
  }

  const nextAnnouncement = () => {
    setCurrentIndex((prev) => (prev + 1) % announcements.length)
  }

  const prevAnnouncement = () => {
    setCurrentIndex((prev) => (prev - 1 + announcements.length) % announcements.length)
  }

  if (loading || !isVisible || announcements.length === 0) {
    return null
  }

  return (
    <div className="relative overflow-hidden shadow-lg" style={{ background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)' }}>
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          {/* أيقونة الصوت */}
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsPaused(!isPaused)}
              className="text-gray-900 hover:bg-white/20 p-2"
            >
              {isPaused ? (
                <VolumeX className="h-4 w-4" />
              ) : (
                <Volume2 className="h-4 w-4" />
              )}
            </Button>
          </div>

          {/* محتوى الإعلان */}
          <div className="flex-1 mx-4">
            <div className="text-center">
              <p className="text-gray-900 font-medium text-sm md:text-base animate-fade-in">
                {announcements[currentIndex]}
              </p>
            </div>
          </div>

          {/* أزرار التنقل وإغلاق */}
          <div className="flex items-center space-x-2 space-x-reverse">
            {announcements.length > 1 && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={prevAnnouncement}
                  className="text-gray-900 hover:bg-white/20 p-2"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={nextAnnouncement}
                  className="text-gray-900 hover:bg-white/20 p-2"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
              </>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsVisible(false)}
              className="text-gray-900 hover:bg-white/20 p-2"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* مؤشر النقاط */}
        {announcements.length > 1 && (
          <div className="flex justify-center mt-2 space-x-1 space-x-reverse">
            {announcements.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  index === currentIndex 
                    ? 'bg-gray-900 scale-125' 
                    : 'bg-gray-900/40 hover:bg-gray-900/60'
                }`}
              />
            ))}
          </div>
        )}
      </div>

      {/* تأثير الحركة */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent transform -skew-x-12 -translate-x-full animate-shimmer"></div>
      
      <style jsx>{`
        @keyframes fade-in {
          from { opacity: 0; transform: translateY(-10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes shimmer {
          0% { transform: translateX(-100%) skewX(-12deg); }
          100% { transform: translateX(200%) skewX(-12deg); }
        }
        
        .animate-fade-in {
          animation: fade-in 0.5s ease-out;
        }
        
        .animate-shimmer {
          animation: shimmer 3s infinite;
        }
      `}</style>
    </div>
  )
}
