#!/bin/bash
# تثبيت شهادة SSL على Nginx لـ mohammi.com
# IP: ***********

echo "🔐 تثبيت شهادة SSL على Nginx - mohammi.com"
echo "🌐 IP: ***********"
echo "=================================================="

# التحقق من صلاحيات المدير
if [ "$EUID" -ne 0 ]; then
    echo "❌ يرجى تشغيل هذا السكريپت كمدير (sudo)"
    exit 1
fi

# التحقق من وجود Nginx
if ! command -v nginx &> /dev/null; then
    echo "❌ Nginx غير مثبت"
    echo "💡 لتثبيت Nginx: sudo apt update && sudo apt install nginx"
    exit 1
fi

echo "✅ تم العثور على Nginx"

# إنشاء مجلد SSL إذا لم يكن موجوداً
SSL_DIR="/etc/ssl/mohammi"
mkdir -p $SSL_DIR
echo "✅ تم إنشاء مجلد SSL: $SSL_DIR"

# نسخ ملفات الشهادة (يجب تعديل المسارات حسب موقع الملفات)
CERT_SOURCE="/path/to/mohammi_com.crt"
KEY_SOURCE="/path/to/mohammi_com.key"
INTERMEDIATE_SOURCE="/path/to/SectigoPublicServerAuthenticationCADVR36.crt"

# التحقق من وجود ملفات الشهادة
echo "🔍 البحث عن ملفات الشهادة..."

# البحث في مجلدات محتملة
SEARCH_DIRS=(
    "/home/<USER>/ssl"
    "/var/www/ssl"
    "/opt/ssl"
    "/root/ssl"
    "/tmp/ssl"
)

CERT_FILE=""
KEY_FILE=""
INTERMEDIATE_FILE=""

for dir in "${SEARCH_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "🔍 البحث في: $dir"
        
        # البحث عن الشهادة
        if [ -z "$CERT_FILE" ]; then
            CERT_FILE=$(find $dir -name "*.crt" -o -name "*certificate*" | grep -i mohammi | head -1)
        fi
        
        # البحث عن المفتاح الخاص
        if [ -z "$KEY_FILE" ]; then
            KEY_FILE=$(find $dir -name "*.key" | grep -i mohammi | head -1)
        fi
        
        # البحث عن الشهادة الوسطية
        if [ -z "$INTERMEDIATE_FILE" ]; then
            INTERMEDIATE_FILE=$(find $dir -name "*intermediate*" -o -name "*chain*" -o -name "*Sectigo*" | head -1)
        fi
    fi
done

# عرض النتائج
if [ -n "$CERT_FILE" ]; then
    echo "✅ تم العثور على الشهادة: $CERT_FILE"
else
    echo "❌ لم يتم العثور على ملف الشهادة"
    echo "💡 يرجى وضع ملف .crt في أحد المجلدات المذكورة"
    exit 1
fi

if [ -n "$KEY_FILE" ]; then
    echo "✅ تم العثور على المفتاح الخاص: $KEY_FILE"
else
    echo "❌ لم يتم العثور على المفتاح الخاص"
    echo "💡 يرجى وضع ملف .key في أحد المجلدات المذكورة"
    exit 1
fi

if [ -n "$INTERMEDIATE_FILE" ]; then
    echo "✅ تم العثور على الشهادة الوسطية: $INTERMEDIATE_FILE"
fi

# نسخ الملفات إلى مجلد SSL
echo "📁 نسخ ملفات الشهادة..."
cp "$CERT_FILE" "$SSL_DIR/mohammi.crt"
cp "$KEY_FILE" "$SSL_DIR/mohammi.key"

if [ -n "$INTERMEDIATE_FILE" ]; then
    cp "$INTERMEDIATE_FILE" "$SSL_DIR/intermediate.crt"
    # دمج الشهادة مع الوسطية
    cat "$SSL_DIR/mohammi.crt" "$SSL_DIR/intermediate.crt" > "$SSL_DIR/fullchain.crt"
    echo "✅ تم دمج الشهادة مع الوسطية"
else
    cp "$SSL_DIR/mohammi.crt" "$SSL_DIR/fullchain.crt"
fi

# تعيين صلاحيات آمنة
chmod 644 "$SSL_DIR/mohammi.crt" "$SSL_DIR/fullchain.crt"
chmod 600 "$SSL_DIR/mohammi.key"
if [ -f "$SSL_DIR/intermediate.crt" ]; then
    chmod 644 "$SSL_DIR/intermediate.crt"
fi

echo "✅ تم تعيين صلاحيات الملفات"

# إنشاء تكوين Nginx
NGINX_CONFIG="/etc/nginx/sites-available/mohammi.com"

echo "⚙️ إنشاء تكوين Nginx..."

cat > $NGINX_CONFIG << 'EOF'
# تكوين SSL لـ mohammi.com
# إعادة توجيه HTTP إلى HTTPS
server {
    listen 80;
    listen [::]:80;
    server_name mohammi.com www.mohammi.com;
    
    # إعادة توجيه دائمة إلى HTTPS
    return 301 https://$server_name$request_uri;
}

# خادم HTTPS الرئيسي
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name mohammi.com www.mohammi.com;
    
    # مسارات شهادات SSL
    ssl_certificate /etc/ssl/mohammi/fullchain.crt;
    ssl_certificate_key /etc/ssl/mohammi/mohammi.key;
    
    # إعدادات SSL الأمنية
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # HSTS (HTTP Strict Transport Security)
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    
    # Headers الأمان
    add_header X-Content-Type-Options nosniff always;
    add_header X-Frame-Options DENY always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # إعدادات الضغط
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # إعدادات التخزين المؤقت للملفات الثابتة
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    }
    
    # Proxy إلى تطبيق Next.js
    location / {
        proxy_pass http://127.0.0.1:7443;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_cache_bypass $http_upgrade;
        proxy_redirect off;
        
        # إعدادات timeout
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # حجم buffer
        proxy_buffer_size 4k;
        proxy_buffers 4 32k;
        proxy_busy_buffers_size 64k;
    }
    
    # معالجة خاصة لـ API routes
    location /api/ {
        proxy_pass http://127.0.0.1:7443;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # إعدادات خاصة للـ API
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # حماية من الملفات الحساسة
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(env|log|sql|bak)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # صفحة خطأ مخصصة
    error_page 502 503 504 /50x.html;
    location = /50x.html {
        root /var/www/html;
        internal;
    }
    
    # سجلات الوصول والأخطاء
    access_log /var/log/nginx/mohammi.com.access.log;
    error_log /var/log/nginx/mohammi.com.error.log;
}
EOF

echo "✅ تم إنشاء تكوين Nginx: $NGINX_CONFIG"

# تفعيل الموقع
echo "🔗 تفعيل الموقع..."
ln -sf $NGINX_CONFIG /etc/nginx/sites-enabled/
echo "✅ تم تفعيل الموقع"

# إزالة التكوين الافتراضي إذا كان موجوداً
if [ -f "/etc/nginx/sites-enabled/default" ]; then
    rm /etc/nginx/sites-enabled/default
    echo "✅ تم إزالة التكوين الافتراضي"
fi

# اختبار تكوين Nginx
echo "🧪 اختبار تكوين Nginx..."
if nginx -t; then
    echo "✅ تكوين Nginx صحيح"
else
    echo "❌ خطأ في تكوين Nginx"
    exit 1
fi

# إعادة تحميل Nginx
echo "🔄 إعادة تحميل Nginx..."
systemctl reload nginx
if [ $? -eq 0 ]; then
    echo "✅ تم إعادة تحميل Nginx بنجاح"
else
    echo "❌ فشل في إعادة تحميل Nginx"
    exit 1
fi

# التحقق من حالة Nginx
echo "📊 التحقق من حالة Nginx..."
systemctl status nginx --no-pager -l

echo ""
echo "🎉 تم تثبيت شهادة SSL بنجاح!"
echo "=================================================="
echo "🌐 الروابط:"
echo "   • https://mohammi.com"
echo "   • https://www.mohammi.com"
echo "   • https://***********"
echo ""
echo "🧪 اختبار الشهادة:"
echo "   • https://www.ssllabs.com/ssltest/analyze.html?d=mohammi.com"
echo "   • curl -I https://mohammi.com"
echo ""
echo "📁 ملفات الشهادة:"
echo "   • الشهادة: /etc/ssl/mohammi/fullchain.crt"
echo "   • المفتاح الخاص: /etc/ssl/mohammi/mohammi.key"
echo ""
echo "⚠️ تأكد من:"
echo "   1. تشغيل تطبيق mohammi على المنفذ 7443"
echo "   2. فتح المنافذ 80 و 443 في جدار الحماية"
echo "   3. توجيه DNS صحيح للدومين"
echo ""
echo "✅ SSL جاهز للاستخدام!"
