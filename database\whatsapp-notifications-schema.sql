-- إنشا<PERSON> جداول خدمة إشعارات WhatsApp

-- جدول إعدادات WhatsApp
CREATE TABLE whatsapp_settings (
    id SERIAL PRIMARY KEY,
    provider VARCHAR(50) NOT NULL, -- twilio, 360dialog, etc
    api_key TEXT NOT NULL,
    api_secret TEXT,
    phone_number_id VARCHAR(50),
    business_account_id VARCHAR(50),
    webhook_url TEXT,
    webhook_token VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول قوالب الرسائل
CREATE TABLE whatsapp_templates (
    id SERIAL PRIMARY KEY,
    template_name VARCHAR(100) NOT NULL UNIQUE,
    template_category VARCHAR(50) NOT NULL, -- UTILITY, MARKETING, AUTHENTICATION
    language_code VARCHAR(10) DEFAULT 'ar',
    header_type VARCHAR(20), -- TEXT, IMAGE, VIDEO, DOCUMENT
    header_content TEXT,
    body_text TEXT NOT NULL,
    footer_text TEXT,
    buttons JSONB, -- أزرار التفاعل
    variables JSONB, -- متغيرات القالب
    status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, APPROVED, REJECTED
    facebook_template_id VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول أرقام الهواتف
CREATE TABLE whatsapp_contacts (
    id SERIAL PRIMARY KEY,
    user_id INTEGER, -- ربط بجدول المستخدمين
    client_id INTEGER, -- ربط بجدول العملاء
    employee_id INTEGER, -- ربط بجدول الموظفين
    phone_number VARCHAR(20) NOT NULL,
    country_code VARCHAR(5) DEFAULT '+967',
    full_phone VARCHAR(25) GENERATED ALWAYS AS (country_code || phone_number) STORED,
    is_verified BOOLEAN DEFAULT false,
    opt_in BOOLEAN DEFAULT true, -- موافقة على استقبال الرسائل
    opt_in_date TIMESTAMP,
    opt_out_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT unique_phone UNIQUE (full_phone),
    CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES users(id),
    CONSTRAINT fk_client FOREIGN KEY (client_id) REFERENCES clients(id),
    CONSTRAINT fk_employee FOREIGN KEY (employee_id) REFERENCES employees(id)
);

-- جدول أنواع الإشعارات
CREATE TABLE notification_types (
    id SERIAL PRIMARY KEY,
    type_code VARCHAR(50) NOT NULL UNIQUE,
    type_name VARCHAR(100) NOT NULL,
    description TEXT,
    template_id INTEGER,
    is_active BOOLEAN DEFAULT true,
    send_to_clients BOOLEAN DEFAULT false,
    send_to_employees BOOLEAN DEFAULT false,
    priority_level INTEGER DEFAULT 1, -- 1=عادي, 2=مهم, 3=عاجل
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_template FOREIGN KEY (template_id) REFERENCES whatsapp_templates(id)
);

-- جدول سجل الرسائل المرسلة
CREATE TABLE whatsapp_messages (
    id SERIAL PRIMARY KEY,
    message_id VARCHAR(100), -- معرف الرسالة من WhatsApp
    contact_id INTEGER NOT NULL,
    template_id INTEGER,
    notification_type_id INTEGER,
    message_type VARCHAR(20) DEFAULT 'template', -- template, text, media
    message_content TEXT,
    media_url TEXT,
    variables JSONB, -- قيم المتغيرات المرسلة
    status VARCHAR(20) DEFAULT 'pending', -- pending, sent, delivered, read, failed
    error_message TEXT,
    sent_at TIMESTAMP,
    delivered_at TIMESTAMP,
    read_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_contact FOREIGN KEY (contact_id) REFERENCES whatsapp_contacts(id),
    CONSTRAINT fk_template_msg FOREIGN KEY (template_id) REFERENCES whatsapp_templates(id),
    CONSTRAINT fk_notification_type FOREIGN KEY (notification_type_id) REFERENCES notification_types(id)
);

-- جدول قائمة انتظار الرسائل
CREATE TABLE whatsapp_queue (
    id SERIAL PRIMARY KEY,
    contact_id INTEGER NOT NULL,
    template_id INTEGER,
    notification_type_id INTEGER,
    message_data JSONB NOT NULL,
    priority INTEGER DEFAULT 1,
    scheduled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    status VARCHAR(20) DEFAULT 'pending', -- pending, processing, sent, failed, cancelled
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_queue_contact FOREIGN KEY (contact_id) REFERENCES whatsapp_contacts(id),
    CONSTRAINT fk_queue_template FOREIGN KEY (template_id) REFERENCES whatsapp_templates(id),
    CONSTRAINT fk_queue_notification FOREIGN KEY (notification_type_id) REFERENCES notification_types(id)
);

-- جدول إحصائيات الرسائل
CREATE TABLE whatsapp_statistics (
    id SERIAL PRIMARY KEY,
    date DATE NOT NULL,
    total_sent INTEGER DEFAULT 0,
    total_delivered INTEGER DEFAULT 0,
    total_read INTEGER DEFAULT 0,
    total_failed INTEGER DEFAULT 0,
    cost_amount DECIMAL(10,2) DEFAULT 0,
    template_id INTEGER,
    notification_type_id INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT unique_daily_stats UNIQUE (date, template_id, notification_type_id),
    CONSTRAINT fk_stats_template FOREIGN KEY (template_id) REFERENCES whatsapp_templates(id),
    CONSTRAINT fk_stats_notification FOREIGN KEY (notification_type_id) REFERENCES notification_types(id)
);

-- إدراج بيانات أولية
INSERT INTO notification_types (type_code, type_name, description, send_to_clients, send_to_employees, priority_level) VALUES
('case_created', 'إنشاء قضية جديدة', 'إشعار عند إنشاء قضية جديدة', true, true, 2),
('case_updated', 'تحديث حالة القضية', 'إشعار عند تحديث حالة القضية', true, true, 2),
('hearing_reminder', 'تذكير بجلسة محكمة', 'تذكير بموعد جلسة محكمة قادمة', true, true, 3),
('payment_due', 'استحقاق دفعة', 'إشعار باستحقاق دفعة مالية', true, false, 2),
('payment_received', 'تأكيد استلام دفعة', 'تأكيد استلام دفعة مالية', true, false, 1),
('document_ready', 'جاهزية وثيقة', 'إشعار بجاهزية وثيقة للاستلام', true, false, 2),
('appointment_reminder', 'تذكير بموعد', 'تذكير بموعد مع المحامي', true, true, 2),
('task_assigned', 'تكليف مهمة', 'إشعار بتكليف مهمة جديدة', false, true, 2),
('deadline_reminder', 'تذكير بموعد نهائي', 'تذكير بموعد نهائي لمهمة', false, true, 3);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX idx_whatsapp_contacts_phone ON whatsapp_contacts(full_phone);
CREATE INDEX idx_whatsapp_contacts_user ON whatsapp_contacts(user_id);
CREATE INDEX idx_whatsapp_contacts_client ON whatsapp_contacts(client_id);
CREATE INDEX idx_whatsapp_contacts_employee ON whatsapp_contacts(employee_id);
CREATE INDEX idx_whatsapp_messages_status ON whatsapp_messages(status);
CREATE INDEX idx_whatsapp_messages_sent_at ON whatsapp_messages(sent_at);
CREATE INDEX idx_whatsapp_queue_status ON whatsapp_queue(status);
CREATE INDEX idx_whatsapp_queue_scheduled ON whatsapp_queue(scheduled_at);
CREATE INDEX idx_whatsapp_statistics_date ON whatsapp_statistics(date);

-- إنشاء triggers لتحديث updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_whatsapp_settings_updated_at BEFORE UPDATE ON whatsapp_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_whatsapp_templates_updated_at BEFORE UPDATE ON whatsapp_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_whatsapp_contacts_updated_at BEFORE UPDATE ON whatsapp_contacts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_whatsapp_queue_updated_at BEFORE UPDATE ON whatsapp_queue FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
