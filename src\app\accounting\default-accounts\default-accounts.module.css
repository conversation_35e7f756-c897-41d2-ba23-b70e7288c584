/* تصميم صفحة الحسابات الافتراضية */

.defaultAccountsContainer {
  direction: rtl;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
  min-height: 100vh;
  padding: 20px;
}

.accountRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  border-bottom: 1px solid #e9ecef;
  background-color: white;
  margin-bottom: 2px;
  transition: background-color 0.2s ease;
}

.accountRow:hover {
  background-color: #f8f9fa;
}

.accountName {
  flex: 1;
  text-align: right;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  padding-right: 20px;
}

.selectContainer {
  display: flex;
  align-items: center;
  gap: 15px;
  min-width: 400px;
}

.accountSelect {
  min-width: 300px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background-color: white;
  direction: rtl;
}

.accountSelect:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.currentAccount {
  font-size: 12px;
  color: #28a745;
  font-weight: 500;
  min-width: 80px;
  text-align: center;
}

.sectionHeader {
  background-color: #e9ecef;
  padding: 12px 20px;
  font-weight: 600;
  font-size: 14px;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
  margin-top: 20px;
  margin-bottom: 0;
}

.sectionHeader:first-child {
  margin-top: 0;
}

.saveButton {
  position: fixed;
  top: 20px;
  left: 20px;
  background-color: #28a745;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: background-color 0.2s ease;
  z-index: 1000;
}

.saveButton:hover {
  background-color: #218838;
}

.saveButton:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.pageTitle {
  text-align: center;
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 30px;
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.accountsCard {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 16px;
  color: #6c757d;
}

.errorMessage {
  background-color: #f8d7da;
  color: #721c24;
  padding: 12px 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  border: 1px solid #f5c6cb;
}

.successMessage {
  background-color: #d4edda;
  color: #155724;
  padding: 12px 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  border: 1px solid #c3e6cb;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .accountRow {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .accountName {
    text-align: center;
    padding-right: 0;
  }
  
  .selectContainer {
    min-width: auto;
    flex-direction: column;
    gap: 10px;
  }
  
  .accountSelect {
    min-width: auto;
    width: 100%;
  }
  
  .saveButton {
    position: static;
    width: 100%;
    margin-bottom: 20px;
  }
}
