# إنشاء CSR صحيح لـ mohammi.com باستخدام PowerShell
Write-Host "🔐 إنشاء CSR صحيح لـ mohammi.com" -ForegroundColor Green

# إنشاء ملف INF للشهادة
$infContent = @"
[Version]
Signature="`$Windows NT`$"

[NewRequest]
Subject = "C=YE, ST=Sanaa, L=Sanaa, O=Mohammi Legal Services, OU=IT Department, CN=mohammi.com"
KeySpec = 1
KeyLength = 2048
Exportable = TRUE
MachineKeySet = FALSE
SMIME = False
PrivateKeyArchive = FALSE
UserProtected = FALSE
UseExistingKeySet = FALSE
ProviderName = "Microsoft RSA SChannel Cryptographic Provider"
ProviderType = 12
RequestType = PKCS10
KeyUsage = 0xa0

[EnhancedKeyUsageExtension]
OID=*******.*******.1 ; Server Authentication

[Extensions]
********* = "{text}"
_continue_ = "dns=mohammi.com&"
_continue_ = "dns=www.mohammi.com&"
_continue_ = "dns=api.mohammi.com&"
_continue_ = "dns=admin.mohammi.com&"
"@

# حفظ ملف INF
$infContent | Out-File -FilePath "ssl\mohammi.inf" -Encoding ASCII
Write-Host "✅ تم إنشاء ملف التكوين: ssl\mohammi.inf" -ForegroundColor Green

# إنشاء CSR باستخدام certreq
Write-Host "🔄 إنشاء CSR..." -ForegroundColor Yellow
try {
    & certreq -new ssl\mohammi.inf ssl\mohammi_valid.csr
    Write-Host "✅ تم إنشاء CSR بنجاح: ssl\mohammi_valid.csr" -ForegroundColor Green
    
    # عرض محتوى CSR
    Write-Host "`n📋 محتوى CSR:" -ForegroundColor Cyan
    Write-Host "=" * 60
    Get-Content ssl\mohammi_valid.csr
    Write-Host "=" * 60
    
    # نسخ CSR إلى الحافظة
    Get-Content ssl\mohammi_valid.csr | Set-Clipboard
    Write-Host "`n✅ تم نسخ CSR إلى الحافظة!" -ForegroundColor Green
    Write-Host "يمكنك الآن لصقه مباشرة في Name.com" -ForegroundColor Yellow
    
} catch {
    Write-Host "❌ فشل في إنشاء CSR: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "💡 تأكد من تشغيل PowerShell كمدير" -ForegroundColor Yellow
}

Write-Host "`n🎯 الخطوات التالية:" -ForegroundColor Yellow
Write-Host "1. الصق CSR في Name.com (تم نسخه للحافظة)" -ForegroundColor White
Write-Host "2. أكمل عملية التحقق من الملكية" -ForegroundColor White
Write-Host "3. حمل الشهادة الموقعة" -ForegroundColor White
