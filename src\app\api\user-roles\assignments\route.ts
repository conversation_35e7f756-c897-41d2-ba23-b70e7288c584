import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب أدوار مستخدم معين
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'معرف المستخدم مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(`
      SELECT 
        ura.role_name,
        ur.display_name,
        ur.description,
        ura.assigned_date,
        ura.is_active
      FROM user_role_assignments ura
      JOIN user_roles ur ON ura.role_name = ur.role_name
      WHERE ura.user_id = $1 AND ura.is_active = true
      ORDER BY ura.assigned_date DESC
    `, [userId])

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('❌ خطأ في جلب أدوار المستخدم:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب أدوار المستخدم' },
      { status: 500 }
    )
  }
}

// POST - تحديث أدوار مستخدم
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { userId, roles, assignedBy } = body

    if (!userId || !Array.isArray(roles)) {
      return NextResponse.json(
        { success: false, error: 'بيانات غير صحيحة' },
        { status: 400 }
      )
    }

    // بداية المعاملة
    await query('BEGIN')

    try {
      // حذف الأدوار الحالية
      await query(`
        DELETE FROM user_role_assignments 
        WHERE user_id = $1
      `, [userId])

      // إضافة الأدوار الجديدة
      for (const roleName of roles) {
        await query(`
          INSERT INTO user_role_assignments (user_id, role_name, assigned_by)
          VALUES ($1, $2, $3)
        `, [userId, roleName, assignedBy || 1])
      }

      // تحديث الصلاحيات المجمعة للمستخدم
      const combinedPermissions = await query(`
        SELECT get_user_combined_permissions($1) as permissions
      `, [userId])

      // تحديث جدول user_permissions بالصلاحيات المجمعة
      await query(`
        DELETE FROM user_permissions WHERE user_id = $1
      `, [userId])

      if (combinedPermissions.rows[0]?.permissions) {
        for (const permission of combinedPermissions.rows[0].permissions) {
          await query(`
            INSERT INTO user_permissions (user_id, permission_key, granted_by)
            VALUES ($1, $2, $3)
            ON CONFLICT (user_id, permission_key) DO NOTHING
          `, [userId, permission, assignedBy || 1])
        }
      }

      // تأكيد المعاملة
      await query('COMMIT')

      return NextResponse.json({
        success: true,
        message: 'تم تحديث الأدوار بنجاح',
        data: { 
          userId, 
          rolesCount: roles.length,
          permissionsCount: combinedPermissions.rows[0]?.permissions?.length || 0
        }
      })

    } catch (error) {
      // إلغاء المعاملة في حالة الخطأ
      await query('ROLLBACK')
      throw error
    }

  } catch (error) {
    console.error('❌ خطأ في تحديث أدوار المستخدم:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الأدوار' },
      { status: 500 }
    )
  }
}
