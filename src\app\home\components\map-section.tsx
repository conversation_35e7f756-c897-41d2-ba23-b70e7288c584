'use client'

import { useState, useEffect } from 'react'
import { MapPin, Navigation, Phone, Mail, Clock } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

interface CompanyData {
  name: string
  address: string
  city?: string
  phone: string
  email: string
  working_hours?: string
  latitude?: number
  longitude?: number
}

interface MapSectionProps {
  companyData: CompanyData
}

export function MapSection({ companyData }: MapSectionProps) {
  const [isMapLoaded, setIsMapLoaded] = useState(false)

  // إحداثيات افتراضية (صنعاء، اليمن)
  const defaultLat = 15.3694
  const defaultLng = 44.1910

  // تحويل آمن للإحداثيات
  const latitude = (companyData.latitude && !isNaN(Number(companyData.latitude)))
    ? Number(companyData.latitude)
    : defaultLat
  const longitude = (companyData.longitude && !isNaN(Number(companyData.longitude)))
    ? Number(companyData.longitude)
    : defaultLng

  const openInGoogleMaps = () => {
    const url = `https://www.google.com/maps?q=${latitude},${longitude}`
    window.open(url, '_blank')
  }

  const openDirections = () => {
    const url = `https://www.google.com/maps/dir/?api=1&destination=${latitude},${longitude}`
    window.open(url, '_blank')
  }

  return (
    <section className="py-16" style={{ background: 'rgba(204, 169, 103, 0.05)' }}>
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4" style={{ color: '#cca967' }}>
              موقعنا ومعلومات التواصل
            </h2>
            <p className="text-gray-300 text-lg">
              نحن في خدمتكم، تفضلوا بزيارتنا أو تواصلوا معنا
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* معلومات التواصل */}
            <div className="space-y-6">
              <Card className="shadow-xl border-0" style={{ background: 'linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%)', borderColor: 'rgba(204, 169, 103, 0.2)' }}>
                <CardHeader>
                  <CardTitle className="flex items-center" style={{ color: '#cca967' }}>
                    <MapPin className="h-6 w-6 mr-3" />
                    معلومات التواصل
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-start space-x-4 space-x-reverse">
                    <div className="w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0" style={{ background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)' }}>
                      <MapPin className="h-5 w-5 text-gray-900" />
                    </div>
                    <div>
                      <h3 className="font-semibold mb-1" style={{ color: '#cca967' }}>العنوان</h3>
                      <p className="text-gray-300">
                        {companyData.address && companyData.city
                          ? `${companyData.address}, ${companyData.city}`
                          : companyData.address || 'صنعاء، الجمهورية اليمنية'
                        }
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4 space-x-reverse">
                    <div className="w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0" style={{ background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)' }}>
                      <Phone className="h-5 w-5 text-gray-900" />
                    </div>
                    <div>
                      <h3 className="font-semibold mb-1" style={{ color: '#cca967' }}>الهاتف</h3>
                      <a 
                        href={`tel:${companyData.phone?.replace(/\D/g, '') || '+967-1-234567'}`}
                        className="text-gray-300 hover:text-yellow-400 transition-colors"
                      >
                        {companyData.phone || '+967 1 234 567'}
                      </a>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4 space-x-reverse">
                    <div className="w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0" style={{ background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)' }}>
                      <Mail className="h-5 w-5 text-gray-900" />
                    </div>
                    <div>
                      <h3 className="font-semibold mb-1" style={{ color: '#cca967' }}>البريد الإلكتروني</h3>
                      <a 
                        href={`mailto:${companyData.email || '<EMAIL>'}`}
                        className="text-gray-300 hover:text-yellow-400 transition-colors"
                      >
                        {companyData.email || '<EMAIL>'}
                      </a>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4 space-x-reverse">
                    <div className="w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0" style={{ background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)' }}>
                      <Clock className="h-5 w-5 text-gray-900" />
                    </div>
                    <div>
                      <h3 className="font-semibold mb-1" style={{ color: '#cca967' }}>ساعات العمل</h3>
                      <p className="text-gray-300">
                        {companyData.working_hours || 'الأحد - الخميس: 8 صباحاً - 6 مساءً'}
                      </p>
                    </div>
                  </div>

                  <div className="flex space-x-3 space-x-reverse pt-4">
                    <Button
                      onClick={openInGoogleMaps}
                      className="flex-1 text-gray-900 hover:text-gray-800 shadow-lg"
                      style={{ background: 'linear-gradient(135deg, #cca967 0%, #d4b876 100%)' }}
                    >
                      <MapPin className="h-4 w-4 mr-2" />
                      عرض على الخريطة
                    </Button>
                    <Button
                      onClick={openDirections}
                      variant="outline"
                      className="flex-1 text-yellow-400 border-yellow-400 hover:bg-yellow-400/10"
                    >
                      <Navigation className="h-4 w-4 mr-2" />
                      الاتجاهات
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* الخريطة */}
            <div>
              <Card className="shadow-xl border-0 overflow-hidden" style={{ background: 'linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%)', borderColor: 'rgba(204, 169, 103, 0.2)' }}>
                <CardHeader>
                  <CardTitle className="flex items-center" style={{ color: '#cca967' }}>
                    <MapPin className="h-6 w-6 mr-3" />
                    خريطة الموقع
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="relative h-80 bg-gray-800 rounded-lg overflow-hidden">
                    {/* خريطة تفاعلية */}
                    <iframe
                      src={`https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3000!2d${longitude}!3d${latitude}!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMTXCsDIyJzA5LjgiTiA0NMKwMTEnMjcuNiJF!5e0!3m2!1sar!2s!4v1635000000000!5m2!1sar!2s`}
                      width="100%"
                      height="100%"
                      style={{ border: 0 }}
                      allowFullScreen
                      loading="lazy"
                      referrerPolicy="no-referrer-when-downgrade"
                      className="w-full h-full"
                      onLoad={() => setIsMapLoaded(true)}
                    />
                    
                    {/* طبقة تفاعلية */}
                    <div 
                      className="absolute inset-0 bg-transparent cursor-pointer"
                      onClick={openInGoogleMaps}
                      title="انقر لفتح الخريطة في نافذة جديدة"
                    />
                    
                    {/* مؤشر التحميل */}
                    {!isMapLoaded && (
                      <div className="absolute inset-0 flex items-center justify-center bg-gray-800">
                        <div className="text-center">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto mb-2" style={{ borderColor: '#cca967' }}></div>
                          <p className="text-gray-400 text-sm">جاري تحميل الخريطة...</p>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
