// استخراج وإدراج البيانات من ملف SQL
const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

async function extractAndInsertData() {
  console.log('📥 بدء استخراج وإدراج البيانات...');
  
  try {
    // 1. قراءة ملف النسخة الاحتياطية
    const backupFile = path.join(__dirname, '..', 'mohammi.sql');
    let sqlContent = fs.readFileSync(backupFile, 'utf8');
    
    console.log('✅ تم قراءة ملف النسخة الاحتياطية');

    // 2. الاتصال بقاعدة البيانات
    const client = new Client({
      host: 'localhost',
      port: 5432,
      database: 'mohammi',
      user: 'postgres',
      password: 'yemen123'
    });
    
    await client.connect();
    console.log('✅ متصل بقاعدة البيانات');

    // 3. استخراج أوامر COPY وتحويلها إلى INSERT
    console.log('🔍 استخراج البيانات من أوامر COPY...');
    
    // البحث عن أوامر COPY
    const copyRegex = /COPY\s+(\w+)\s*\([^)]+\)\s+FROM\s+stdin;([\s\S]*?)\\./gm;
    let match;
    let totalInserts = 0;
    
    while ((match = copyRegex.exec(sqlContent)) !== null) {
      const tableName = match[1];
      const dataSection = match[2].trim();
      
      if (dataSection && dataSection.length > 0) {
        console.log(`📋 معالجة جدول: ${tableName}`);
        
        // تقسيم البيانات إلى أسطر
        const dataLines = dataSection.split('\n').filter(line => line.trim().length > 0);
        
        if (dataLines.length > 0) {
          // الحصول على أعمدة الجدول
          const columnsResult = await client.query(`
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = $1 AND table_schema = 'public'
            ORDER BY ordinal_position
          `, [tableName]);
          
          if (columnsResult.rows.length > 0) {
            const columns = columnsResult.rows.map(row => row.column_name);
            const columnsList = columns.join(', ');
            
            console.log(`   📊 ${dataLines.length} سجل في ${columns.length} عمود`);
            
            // إدراج البيانات سطر بسطر
            let insertedCount = 0;
            
            for (const line of dataLines) {
              try {
                // تحليل البيانات (مفصولة بـ tab)
                const values = line.split('\t');
                
                if (values.length === columns.length) {
                  // تحضير القيم للإدراج
                  const placeholders = values.map((_, index) => `$${index + 1}`).join(', ');
                  const processedValues = values.map(value => {
                    if (value === '\\N' || value === '') {
                      return null;
                    }
                    return value;
                  });
                  
                  // إدراج السجل
                  const insertQuery = `INSERT INTO ${tableName} (${columnsList}) VALUES (${placeholders})`;
                  await client.query(insertQuery, processedValues);
                  insertedCount++;
                }
              } catch (error) {
                // تجاهل أخطاء الإدراج المكررة
                if (!error.message.includes('duplicate key') && 
                    !error.message.includes('violates unique constraint')) {
                  console.log(`   ⚠️ خطأ في إدراج سجل: ${error.message.substring(0, 100)}`);
                }
              }
            }
            
            console.log(`   ✅ تم إدراج ${insertedCount} سجل في جدول ${tableName}`);
            totalInserts += insertedCount;
          }
        }
      }
    }
    
    console.log(`✅ تم إدراج ${totalInserts} سجل إجمالي`);

    // 4. إضافة بيانات أساسية إضافية إذا لزم الأمر
    console.log('\n🔧 إضافة بيانات أساسية إضافية...');
    
    // التحقق من وجود بيانات الشركة
    const companyCount = await client.query('SELECT COUNT(*) as count FROM companies');
    if (companyCount.rows[0].count === '0') {
      console.log('📋 إضافة بيانات الشركة الافتراضية...');
      try {
        await client.query(`
          INSERT INTO companies (
            name, legal_name, registration_number, tax_number, address, city, country,
            phone, email, website, established_date, legal_form, capital, description,
            logo_url, logo_right_text, logo_left_text, logo_image_url, is_active
          ) VALUES (
            'مؤسسة الجرافي للمحاماة والاستشارات القانونية',
            'مؤسسة الجرافي للمحاماة والاستشارات القانونية المحدودة',
            'CR-2024-001',
            'TAX-*********',
            'صنعاء- شارع مجاهد- عمارة الحاشدي',
            'صنعاء',
            'اليمن',
            '+967-1-123456',
            '<EMAIL>',
            'www.legalfirm.ye',
            '2020-01-14',
            'شركة محدودة المسؤولية',
            1000000.00,
            'مكتب متخصص في تقديم الخدمات القانونية والاستشارات القانونية في جميع المجالات',
            '/images/company-logo.png',
            'مؤسسة الجرافي للمحاماة والاستشارات القانونية',
            'صنعاء - شارع تعز - امام بريد شميلة',
            '/images/logo.png',
            true
          )
        `);
        console.log('   ✅ تم إضافة بيانات الشركة');
      } catch (error) {
        console.log(`   ⚠️ خطأ في إضافة بيانات الشركة: ${error.message}`);
      }
    }

    // التحقق من وجود المستخدم admin
    const adminCount = await client.query("SELECT COUNT(*) as count FROM users WHERE username = 'admin'");
    if (adminCount.rows[0].count === '0') {
      console.log('👤 إضافة المستخدم admin...');
      try {
        await client.query(`
          INSERT INTO users (
            username, password_hash, email, role, status, is_online, login_attempts
          ) VALUES (
            'admin',
            'admin123',
            '<EMAIL>',
            'admin',
            'active',
            false,
            0
          )
        `);
        console.log('   ✅ تم إضافة المستخدم admin');
      } catch (error) {
        console.log(`   ⚠️ خطأ في إضافة المستخدم admin: ${error.message}`);
      }
    }

    // 5. التحقق النهائي من البيانات
    console.log('\n🔍 التحقق النهائي من البيانات...');
    
    const importantTables = ['companies', 'users', 'clients', 'issues', 'employees', 'services'];
    
    for (const tableName of importantTables) {
      try {
        const result = await client.query(`SELECT COUNT(*) as count FROM ${tableName}`);
        console.log(`   📊 ${tableName}: ${result.rows[0].count} سجل`);
      } catch (error) {
        console.log(`   ❌ ${tableName}: خطأ`);
      }
    }

    // فحص بيانات الشركة
    try {
      const companyResult = await client.query('SELECT name, email, phone FROM companies LIMIT 1');
      if (companyResult.rows.length > 0) {
        const company = companyResult.rows[0];
        console.log('\n🏢 بيانات الشركة:');
        console.log(`   الاسم: ${company.name}`);
        console.log(`   البريد: ${company.email}`);
        console.log(`   الهاتف: ${company.phone}`);
      }
    } catch (error) {
      console.log('\n❌ لا يمكن الوصول لبيانات الشركة');
    }

    // فحص المستخدمين
    try {
      const usersResult = await client.query('SELECT username, email, role FROM users LIMIT 5');
      if (usersResult.rows.length > 0) {
        console.log('\n👥 المستخدمين:');
        usersResult.rows.forEach((user, index) => {
          console.log(`   ${index + 1}. ${user.username} (${user.role}) - ${user.email}`);
        });
      }
    } catch (error) {
      console.log('\n❌ لا يمكن الوصول لبيانات المستخدمين');
    }

    await client.end();
    
    console.log('\n🎉 تم استخراج وإدراج البيانات بنجاح!');
    console.log('✅ قاعدة البيانات جاهزة مع البيانات الأصلية');
    console.log('🔄 يمكنك الآن إعادة تشغيل النظام');
    
  } catch (error) {
    console.error('❌ خطأ في استخراج البيانات:', error.message);
  }
}

extractAndInsertData();
