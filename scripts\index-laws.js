#!/usr/bin/env node
/**
 * Index Laws Script
 * - Scans d:/mohammi/laws/ (or process.env.LAWS_DIR) for TXT/PDF/DOCX files
 * - Extracts text with proper encoding and RTL handling
 * - Splits into chunks and saves database/laws-index.json
 *
 * Note: Requires dependencies (install first):
 *   npm i chardet iconv-lite pdf-parse mammoth
 */

const fs = require('fs');
const path = require('path');
const { spawnSync } = require('child_process');
const chardet = require('chardet');
const iconv = require('iconv-lite');
const pdfParse = require('pdf-parse');
const mammoth = require('mammoth');

const LAWS_DIR = process.env.LAWS_DIR || path.resolve(process.cwd(), 'laws');
const OUT_DIR = path.resolve(process.cwd(), 'database');
const OUT_FILE = path.join(OUT_DIR, 'laws-index.json');
const TMP_DIR = path.join(process.cwd(), '.tmp_laws');

function ensureDir(p) {
  if (!fs.existsSync(p)) fs.mkdirSync(p, { recursive: true });
}

function isArabic(text) {
  // Basic check: presence of Arabic unicode range
  return /[\u0600-\u06FF]/.test(text);
}

function applyRTLGuard(text) {
  // Prepend RTL mark for Arabic paragraphs to improve display in UIs
  const RLM = '\u200F';
  return text
    .split(/\r?\n/)
    .map((line) => (isArabic(line.trim()) ? RLM + line : line))
    .join('\n');
}

async function readTXT(filePath) {
  const buf = fs.readFileSync(filePath);
  const enc = chardet.detect(buf) || 'UTF-8';
  const txt = iconv.decode(buf, enc);
  return applyRTLGuard(txt);
}

async function readPDF(filePath) {
  const buf = fs.readFileSync(filePath);
  const data = await pdfParse(buf);
  const txt = (data.text || '').trim();
  return applyRTLGuard(txt);
}

async function readDOCX(filePath) {
  const buffer = fs.readFileSync(filePath);
  const result = await mammoth.extractRawText({ buffer });
  const txt = (result.value || '').trim();
  return applyRTLGuard(txt);
}

function commandExists(cmd) {
  try {
    const res = spawnSync(process.platform === 'win32' ? 'where' : 'which', [cmd], { encoding: 'utf-8' });
    return res.status === 0;
  } catch {
    return false;
  }
}

function convertDocToDocx(docPath) {
  // Requires LibreOffice installed and 'soffice' available in PATH
  if (!commandExists('soffice')) return null;
  ensureDir(TMP_DIR);
  const outDir = TMP_DIR;
  const args = ['--headless', '--convert-to', 'docx', docPath, '--outdir', outDir];
  const res = spawnSync('soffice', args, { encoding: 'utf-8' });
  if (res.status !== 0) {
    console.warn('LibreOffice conversion failed for', docPath, res.stderr || res.stdout);
    return null;
  }
  const base = path.basename(docPath, path.extname(docPath));
  const outPath = path.join(outDir, base + '.docx');
  return fs.existsSync(outPath) ? outPath : null;
}

function listFiles(dir) {
  const results = [];
  const walk = (p) => {
    const entries = fs.readdirSync(p, { withFileTypes: true });
    for (const e of entries) {
      const full = path.join(p, e.name);
      if (e.isDirectory()) walk(full);
      else results.push(full);
    }
  };
  walk(dir);
  return results;
}

function chunkText(text, chunkSize = 1000, overlap = 150) {
  const chunks = [];
  let i = 0;
  while (i < text.length) {
    const end = Math.min(i + chunkSize, text.length);
    const chunk = text.slice(i, end).trim();
    if (chunk) chunks.push(chunk);
    if (end === text.length) break;
    i = end - overlap;
    if (i < 0) i = 0;
  }
  return chunks;
}

async function extractText(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  try {
    if (ext === '.txt') return await readTXT(filePath);
    if (ext === '.pdf') return await readPDF(filePath);
    if (ext === '.docx') return await readDOCX(filePath);
    if (ext === '.doc') {
      const converted = convertDocToDocx(filePath);
      if (converted) return await readDOCX(converted);
      console.warn('Skipping .doc (LibreOffice not available):', filePath);
      return '';
    }
    return '';
  } catch (e) {
    console.error('Extract error for', filePath, e.message);
    return '';
  }
}

async function main() {
  console.log('📚 Indexing laws from:', LAWS_DIR);
  if (!fs.existsSync(LAWS_DIR)) {
    console.error('Laws directory not found:', LAWS_DIR);
    process.exit(1);
  }

  const files = listFiles(LAWS_DIR).filter((f) => /\.(txt|pdf|docx|doc)$/i.test(f));
  console.log('Found files:', files.length);

  const index = [];
  let id = 1;

  for (const fp of files) {
    const rel = path.relative(LAWS_DIR, fp);
    const text = await extractText(fp);
    if (!text || text.length < 20) continue;

    const chunks = chunkText(text, 1200, 200);
    for (let i = 0; i < chunks.length; i++) {
      index.push({
        id: id++,
        source: rel,
        chunk: i + 1,
        text: chunks[i],
      });
    }
  }

  ensureDir(OUT_DIR);
  fs.writeFileSync(OUT_FILE, JSON.stringify({ createdAt: new Date().toISOString(), count: index.length, items: index }, null, 2), 'utf-8');
  console.log('✅ Index saved to', OUT_FILE, 'chunks:', index.length);
}

main().catch((e) => {
  console.error('Indexing failed:', e);
  process.exit(1);
});
