/**
 * سكريبت للتحقق من الحسابات الأب للموظفين والعملاء
 */

const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'mohammidev'
};

async function checkParentAccounts() {
  const client = new Client(dbConfig);

  try {
    await client.connect();
    console.log('✅ متصل بقاعدة البيانات mohammidev');

    // البحث عن الحسابات الأب
    const parentAccounts = await client.query(`
      SELECT id, account_code, account_name, account_name_en
      FROM chart_of_accounts 
      WHERE account_name_en IN ('employees', 'clients') 
         OR account_name LIKE '%موظف%' 
         OR account_name LIKE '%عميل%'
         OR account_name LIKE '%عملاء%'
      ORDER BY id
    `);

    console.log('\n📋 الحسابات الأب المحتملة:');
    parentAccounts.rows.forEach(row => {
      console.log(`   - ${row.id}: ${row.account_code} - ${row.account_name} (EN: ${row.account_name_en || 'غير محدد'})`);
    });

    // البحث في الحسابات التي تحتوي على أرقام 1121 أو 1151
    const specificAccounts = await client.query(`
      SELECT id, account_code, account_name, account_name_en
      FROM chart_of_accounts 
      WHERE account_code IN ('1121', '1151', '5101')
      ORDER BY id
    `);

    console.log('\n🔍 الحسابات المحددة (1121, 1151, 5101):');
    specificAccounts.rows.forEach(row => {
      console.log(`   - ${row.id}: ${row.account_code} - ${row.account_name} (EN: ${row.account_name_en || 'غير محدد'})`);
    });

    // فحص جميع الحسابات التي تحتوي على account_name_en
    const allWithEn = await client.query(`
      SELECT id, account_code, account_name, account_name_en
      FROM chart_of_accounts 
      WHERE account_name_en IS NOT NULL AND account_name_en != ''
      ORDER BY id
    `);

    console.log('\n🌐 جميع الحسابات التي لها account_name_en:');
    allWithEn.rows.forEach(row => {
      console.log(`   - ${row.id}: ${row.account_code} - ${row.account_name} (EN: ${row.account_name_en})`);
    });

  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await client.end();
  }
}

checkParentAccounts().catch(console.error);
