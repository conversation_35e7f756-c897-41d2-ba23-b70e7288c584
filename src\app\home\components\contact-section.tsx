"use client";

import { useState } from 'react';
import { MapPin, Phone, Mail, Clock, Send, CheckCircle, XCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

export function ContactSection() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState<boolean | null>(null);
  const [error, setError] = useState('');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Reset form
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: ''
      });
      
      setIsSuccess(true);
      setTimeout(() => setIsSuccess(null), 5000);
    } catch (err) {
      setError('حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.');
      setIsSuccess(false);
    } finally {
      setIsSubmitting(false);
    }
  };

  const contactInfo = [
    {
      icon: MapPin,
      title: 'العنوان',
      description: 'الرياض، المملكة العربية السعودية، شارع العليا، ص.ب 12345',
      link: 'https://maps.google.com',
      linkText: 'عرض على الخريطة'
    },
    {
      icon: Phone,
      title: 'الهاتف',
      description: '+966 11 234 5678',
      link: 'tel:+966112345678',
      linkText: 'اتصل بنا'
    },
    {
      icon: Mail,
      title: 'البريد الإلكتروني',
      description: '<EMAIL>',
      link: 'mailto:<EMAIL>',
      linkText: 'أرسل بريداً إلكترونياً'
    },
    {
      icon: Clock,
      title: 'ساعات العمل',
      description: 'الأحد - الخميس: 8 صباحاً - 4 مساءً',
      subDescription: 'الجمعة والسبت: مغلق',
      link: '#',
      linkText: 'احجز موعداً'
    }
  ];

  return (
    <section id="contact" className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <span className="inline-block bg-blue-100 text-blue-600 text-sm font-semibold px-4 py-1 rounded-full mb-4">
            اتصل بنا
          </span>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">تواصل مع فريقنا</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            نحن هنا لمساعدتك. تواصل معنا للحصول على استشارة قانونية أو لطرح أي استفسار.
          </p>
          <div className="w-20 h-1 bg-blue-600 mx-auto mt-6"></div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Information */}
          <div>
            <div className="bg-gray-50 rounded-2xl p-8 h-full">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">معلومات التواصل</h3>
              <p className="text-gray-600 mb-8">
                يمكنك التواصل معنا عبر المعلومات التالية أو تعبئة النموذج وسنقوم بالرد عليك في أسرع وقت ممكن.
              </p>
              
              <div className="space-y-6">
                {contactInfo.map((item, index) => {
                  const Icon = item.icon;
                  return (
                    <div key={index} className="flex">
                      <div className="bg-blue-100 p-3 rounded-lg text-blue-600 ml-4 flex-shrink-0 h-12 w-12 flex items-center justify-center">
                        <Icon className="w-5 h-5" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">{item.title}</h4>
                        <p className="text-gray-600">{item.description}</p>
                        {item.subDescription && (
                          <p className="text-gray-600 text-sm">{item.subDescription}</p>
                        )}
                        <a 
                          href={item.link} 
                          className="text-blue-600 hover:text-blue-800 text-sm font-medium inline-flex items-center mt-1"
                        >
                          {item.linkText}
                          <svg className="w-3 h-3 mr-1 rtl:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                          </svg>
                        </a>
                      </div>
                    </div>
                  );
                })}
              </div>
              
              <div className="mt-8">
                <h4 className="font-semibold text-gray-900 mb-4">تابعنا على وسائل التواصل الاجتماعي</h4>
                <div className="flex space-x-4 space-x-reverse">
                  {[
                    { name: 'تويتر', icon: 'twitter', url: '#' },
                    { name: 'فيسبوك', icon: 'facebook', url: '#' },
                    { name: 'لينكد إن', icon: 'linkedin', url: '#' },
                    { name: 'إنستغرام', icon: 'instagram', url: '#' },
                    { name: 'يوتيوب', icon: 'youtube', url: '#' },
                  ].map((social, index) => (
                    <a 
                      key={index} 
                      href={social.url} 
                      className="w-10 h-10 rounded-full bg-white border border-gray-200 flex items-center justify-center text-gray-600 hover:bg-blue-50 hover:text-blue-600 transition-colors"
                      aria-label={social.name}
                    >
                      <span className="sr-only">{social.name}</span>
                      <i className={`fab fa-${social.icon} text-lg`}></i>
                    </a>
                  ))}
                </div>
              </div>
            </div>
          </div>
          
          {/* Contact Form */}
          <div>
            <div className="bg-white border border-gray-200 rounded-2xl p-8 shadow-sm">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">أرسل لنا رسالة</h3>
              
              {isSuccess === true && (
                <div className="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg mb-6 flex items-start">
                  <CheckCircle className="w-5 h-5 ml-2 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium">تم إرسال رسالتك بنجاح!</p>
                    <p className="text-sm">سنقوم بالرد عليك في أقرب وقت ممكن.</p>
                  </div>
                </div>
              )}
              
              {isSuccess === false && (
                <div className="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg mb-6 flex items-start">
                  <XCircle className="w-5 h-5 ml-2 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium">حدث خطأ أثناء إرسال الرسالة</p>
                    <p className="text-sm">{error || 'يرجى المحاولة مرة أخرى لاحقاً.'}</p>
                  </div>
                </div>
              )}
              
              <form onSubmit={handleSubmit} className="space-y-5">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">الاسم الكامل *</label>
                    <Input
                      id="name"
                      name="name"
                      type="text"
                      required
                      value={formData.name}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="أدخل اسمك الكامل"
                      dir="rtl"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني *</label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      required
                      value={formData.email}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="<EMAIL>"
                      dir="ltr"
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">رقم الجوال *</label>
                    <Input
                      id="phone"
                      name="phone"
                      type="tel"
                      required
                      value={formData.phone}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="05XXXXXXXX"
                      dir="ltr"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-1">الموضوع *</label>
                    <Input
                      id="subject"
                      name="subject"
                      type="text"
                      required
                      value={formData.subject}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="موضوع الرسالة"
                      dir="rtl"
                    />
                  </div>
                </div>
                
                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">الرسالة *</label>
                  <Textarea
                    id="message"
                    name="message"
                    required
                    rows={5}
                    value={formData.message}
                    onChange={handleChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="اكتب رسالتك هنا..."
                    dir="rtl"
                  />
                </div>
                
                <div className="pt-2">
                  <Button
                    type="submit"
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg font-medium text-lg flex items-center justify-center gap-2"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        جاري الإرسال...
                      </>
                    ) : (
                      <>
                        <Send className="w-5 h-5" />
                        إرسال الرسالة
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </div>
            
            {/* Map */}
            <div className="mt-8 rounded-2xl overflow-hidden h-64 bg-gray-100">
              <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3622.082292882423!2d46.67529261500296!3d24.82221898407852!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3e2ee3b9e8caf2f9%3A0x44d1e9f1cf1d9e5!2sRiyadh%20Saudi%20Arabia!5e0!3m2!1sen!2sus!4v1620000000000!5m2!1sen!2sus"
                width="100%"
                height="100%"
                style={{ border: 0 }}
                allowFullScreen
                loading="lazy"
                title="موقعنا على الخريطة"
              ></iframe>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
