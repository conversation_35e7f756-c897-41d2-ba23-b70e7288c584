'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import {
  Smartphone,
  Plus,
  Trash2,
  Shield,
  Users,
  AlertTriangle,
  CheckCircle,
  Clock,
  Monitor
} from 'lucide-react'

interface User {
  id: number
  username: string
  device_ids: string | null
  device_login_required: boolean
  is_active: boolean
  current_device_id: string | null
  last_login: string | null
}

interface LoginAttempt {
  id: number
  user_id: number
  username: string
  device_id: string
  device_info: string
  ip_address: string
  login_time: string
  login_status: string
  failure_reason: string | null
}

export default function DeviceManagementPage() {
  const [users, setUsers] = useState<User[]>([])
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [newDeviceIds, setNewDeviceIds] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [message, setMessage] = useState('')
  const [loginAttempts, setLoginAttempts] = useState<LoginAttempt[]>([])
  const [showAttempts, setShowAttempts] = useState(false)

  // جلب قائمة المستخدمين
  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/admin/device-management')
      const result = await response.json()

      if (result.success) {
        setUsers(result.users)
      } else {
        setMessage('فشل في جلب بيانات المستخدمين')
      }
    } catch (error) {
      console.error('خطأ في جلب المستخدمين:', error)
      setMessage('خطأ في الاتصال بالخادم')
    } finally {
      setIsLoading(false)
    }
  }

  // جلب محاولات تسجيل الدخول
  const fetchLoginAttempts = async () => {
    try {
      const response = await fetch('/api/admin/device-management/login-attempts')
      const result = await response.json()

      if (result.success) {
        setLoginAttempts(result.attempts)
      } else {
        setMessage('فشل في جلب محاولات تسجيل الدخول')
      }
    } catch (error) {
      console.error('خطأ في جلب محاولات الدخول:', error)
      setMessage('خطأ في الاتصال بالخادم')
    }
  }

  // تحديث معرفات الأجهزة للمستخدم
  const updateUserDevices = async (userId: number, deviceIds: string) => {
    setIsSaving(true)
    try {
      const response = await fetch('/api/admin/device-management', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'update_devices',
          userId,
          deviceIds: deviceIds.trim()
        }),
      })

      const result = await response.json()

      if (result.success) {
        setMessage('تم تحديث معرفات الأجهزة بنجاح')
        fetchUsers() // إعادة جلب البيانات
        fetchLoginAttempts() // إعادة جلب محاولات الدخول
        setSelectedUser(null)
        setNewDeviceIds('')
      } else {
        setMessage(result.error || 'فشل في تحديث معرفات الأجهزة')
      }
    } catch (error) {
      console.error('خطأ في تحديث الأجهزة:', error)
      setMessage('خطأ في الاتصال بالخادم')
    } finally {
      setIsSaving(false)
    }
  }

  // تبديل حالة تطلب معرف الجهاز
  const toggleDeviceRequirement = async (userId: number, required: boolean) => {
    try {
      const response = await fetch('/api/admin/device-management', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'toggle_requirement',
          userId,
          required
        }),
      })

      const result = await response.json()

      if (result.success) {
        setMessage(`تم ${required ? 'تفعيل' : 'إلغاء'} تطلب معرف الجهاز`)
        fetchUsers()
      } else {
        setMessage(result.error || 'فشل في تحديث الإعداد')
      }
    } catch (error) {
      console.error('خطأ في تحديث الإعداد:', error)
      setMessage('خطأ في الاتصال بالخادم')
    }
  }

  useEffect(() => {
    fetchUsers()
    fetchLoginAttempts()
  }, [])

  // عرض رسالة مؤقتة
  useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setMessage(''), 5000)
      return () => clearTimeout(timer)
    }
  }, [message])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  return (
    <MainLayout>
      <div className="container mx-auto p-6 space-y-6" dir="rtl">
      {/* العنوان */}
      <div className="flex items-center gap-3">
        <Shield className="h-8 w-8 text-blue-600" />
        <div>
          <h1 className="text-3xl font-bold">إدارة معرفات الأجهزة</h1>
          <p className="text-gray-600">إدارة الأجهزة المصرح بها لكل مستخدم</p>
        </div>
      </div>

      {/* رسالة الحالة */}
      {message && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <p className="text-blue-700">{message}</p>
        </div>
      )}

      {/* معلومات النظام */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            معلومات مهمة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <p>• يمكن إضافة عدة معرفات أجهزة لكل مستخدم مفصولة بفاصلة (,)</p>
            <p>• المستخدم admin معفى من تطلب معرف الجهاز</p>
            <p>• يتم منع تسجيل الدخول من الأجهزة غير المصرح بها</p>
            <p>• يسمح النظام بجلسة واحدة فقط لكل مستخدم</p>
          </div>
        </CardContent>
      </Card>

      {/* أزرار التبديل */}
      <div className="flex gap-4">
        <Button
          variant={!showAttempts ? 'default' : 'outline'}
          onClick={() => setShowAttempts(false)}
          className="flex items-center gap-2"
        >
          <Users className="h-4 w-4" />
          إدارة المستخدمين ({users.length})
        </Button>
        <Button
          variant={showAttempts ? 'default' : 'outline'}
          onClick={() => setShowAttempts(true)}
          className="flex items-center gap-2"
        >
          <AlertTriangle className="h-4 w-4" />
          محاولات الدخول ({loginAttempts.filter(a => a.login_status === 'blocked').length})
        </Button>
      </div>

      {/* قائمة المستخدمين */}
      {!showAttempts && (
        <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            المستخدمين ({users.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {users.map((user) => (
              <div key={user.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Monitor className="h-5 w-5 text-gray-500" />
                    <div>
                      <h3 className="font-semibold">{user.username}</h3>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant={user.is_active ? 'default' : 'secondary'}>
                          {user.is_active ? 'نشط' : 'غير نشط'}
                        </Badge>
                        <Badge variant={user.device_login_required ? 'destructive' : 'outline'}>
                          {user.device_login_required ? 'يتطلب الجهاز' : 'لا يتطلب الجهاز'}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {user.username !== 'admin' && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => toggleDeviceRequirement(user.id, !user.device_login_required)}
                      >
                        {user.device_login_required ? 'إلغاء التطلب' : 'يتطلب الجهاز'}
                      </Button>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSelectedUser(user)
                        setNewDeviceIds(user.device_ids || '')
                      }}
                    >
                      إدارة الأجهزة
                    </Button>
                  </div>
                </div>

                {/* معلومات الأجهزة */}
                <div className="bg-gray-50 rounded p-3 space-y-2">
                  <div className="text-sm">
                    <strong>الأجهزة المصرح بها:</strong>
                    {user.device_ids ? (
                      <div className="mt-1">
                        {user.device_ids.split(',').map((deviceId, index) => (
                          <Badge key={index} variant="outline" className="mr-1 mb-1">
                            {deviceId.trim()}
                          </Badge>
                        ))}
                      </div>
                    ) : (
                      <span className="text-gray-500 mr-2">لا توجد أجهزة مصرح بها</span>
                    )}
                  </div>

                  {user.current_device_id && (
                    <div className="text-sm">
                      <strong>الجهاز الحالي:</strong>
                      <Badge variant="default" className="mr-2">
                        {user.current_device_id}
                      </Badge>
                    </div>
                  )}

                  {user.last_login && (
                    <div className="text-sm text-gray-600">
                      <Clock className="h-4 w-4 inline mr-1" />
                      آخر دخول: {new Date(user.last_login).toLocaleString('ar-SA')}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
      )}

      {/* محاولات تسجيل الدخول */}
      {showAttempts && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              محاولات تسجيل الدخول المحظورة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {loginAttempts.filter(attempt => attempt.login_status === 'blocked').length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
                  <p>لا توجد محاولات دخول محظورة</p>
                </div>
              ) : (
                loginAttempts
                  .filter(attempt => attempt.login_status === 'blocked')
                  .slice(0, 20) // عرض آخر 20 محاولة
                  .map((attempt) => (
                    <div key={attempt.id} className="border rounded-lg p-4 bg-red-50 border-red-200">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <AlertTriangle className="h-4 w-4 text-red-500" />
                          <span className="font-semibold text-red-700">{attempt.username}</span>
                          <Badge variant="destructive">محظور</Badge>
                        </div>
                        <span className="text-sm text-gray-600">
                          {new Date(attempt.login_time).toLocaleString('ar-SA')}
                        </span>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                        <div>
                          <strong>معرف الجهاز:</strong>
                          <code className="bg-gray-100 px-1 rounded mr-1">{attempt.device_id}</code>
                        </div>
                        <div>
                          <strong>عنوان IP:</strong>
                          <span className="mr-1">{attempt.ip_address}</span>
                        </div>
                        <div className="md:col-span-2">
                          <strong>السبب:</strong>
                          <span className="mr-1 text-red-600">{attempt.failure_reason}</span>
                        </div>
                        <div className="md:col-span-2">
                          <strong>معلومات الجهاز:</strong>
                          <span className="mr-1 text-xs text-gray-600">{attempt.device_info}</span>
                        </div>
                      </div>

                      <div className="mt-3 flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            const user = users.find(u => u.username === attempt.username)
                            if (user) {
                              const currentDevices = user.device_ids ? user.device_ids.split(',') : []
                              const newDevices = [...currentDevices, attempt.device_id].join(',')
                              updateUserDevices(user.id, newDevices)
                            }
                          }}
                          className="text-green-600 border-green-300 hover:bg-green-50"
                        >
                          <Plus className="h-3 w-3 mr-1" />
                          السماح بهذا الجهاز
                        </Button>
                      </div>
                    </div>
                  ))
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* نموذج تحرير معرفات الأجهزة */}
      {selectedUser && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Smartphone className="h-5 w-5" />
              إدارة أجهزة المستخدم: {selectedUser.username}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <Label htmlFor="deviceIds">معرفات الأجهزة (مفصولة بفاصلة)</Label>
                <Textarea
                  id="deviceIds"
                  value={newDeviceIds}
                  onChange={(e) => setNewDeviceIds(e.target.value)}
                  placeholder="DEV_ABC123, DEV_XYZ789, ..."
                  className="mt-1"
                  rows={3}
                />
                <p className="text-sm text-gray-500 mt-1">
                  أدخل معرفات الأجهزة مفصولة بفاصلة. مثال: DEV_ABC123, DEV_XYZ789
                </p>
              </div>

              <div className="flex items-center gap-2">
                <Button
                  onClick={() => updateUserDevices(selectedUser.id, newDeviceIds)}
                  disabled={isSaving}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {isSaving ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      جاري الحفظ...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      حفظ التغييرات
                    </>
                  )}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setSelectedUser(null)
                    setNewDeviceIds('')
                  }}
                >
                  إلغاء
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
      </div>
    </MainLayout>
  )
}
