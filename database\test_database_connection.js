// اختبار شامل لاتصال النظام بقاعدة البيانات
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function testDatabaseConnection() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔄 اختبار الاتصال بقاعدة البيانات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 1. اختبار الجداول الأساسية
    console.log('\n📋 اختبار الجداول الأساسية:');
    const basicTables = ['users', 'employees', 'clients', 'issues', 'lineages', 'services'];
    
    for (const table of basicTables) {
      try {
        const result = await client.query(`SELECT COUNT(*) FROM ${table}`);
        console.log(`   ✅ ${table}: ${result.rows[0].count} سجل`);
      } catch (error) {
        console.log(`   ❌ ${table}: خطأ - ${error.message}`);
      }
    }

    // 2. اختبار الجداول الجديدة
    console.log('\n📋 اختبار الجداول الجديدة:');
    const newTables = ['case_distribution', 'service_distributions', 'serviceslow', 'footer_links'];
    
    for (const table of newTables) {
      try {
        const result = await client.query(`SELECT COUNT(*) FROM ${table}`);
        console.log(`   ✅ ${table}: ${result.rows[0].count} سجل`);
      } catch (error) {
        console.log(`   ❌ ${table}: خطأ - ${error.message}`);
      }
    }

    // 3. اختبار الجداول المحاسبية
    console.log('\n📋 اختبار الجداول المحاسبية:');
    const accountingTables = ['chart_of_accounts', 'journal_entries', 'companies'];
    
    for (const table of accountingTables) {
      try {
        const result = await client.query(`SELECT COUNT(*) FROM ${table}`);
        console.log(`   ✅ ${table}: ${result.rows[0].count} سجل`);
      } catch (error) {
        console.log(`   ❌ ${table}: خطأ - ${error.message}`);
      }
    }

    // 4. اختبار العلاقات بين الجداول
    console.log('\n🔗 اختبار العلاقات بين الجداول:');
    
    // اختبار علاقة الخدمات مع النسب المالية
    try {
      const result = await client.query(`
        SELECT s.name as service_name, l.name as lineage_name 
        FROM services s 
        LEFT JOIN lineages l ON s.lineage_id = l.id 
        LIMIT 3
      `);
      console.log(`   ✅ علاقة الخدمات مع النسب المالية: ${result.rows.length} سجل`);
    } catch (error) {
      console.log(`   ❌ علاقة الخدمات مع النسب المالية: خطأ - ${error.message}`);
    }

    // 5. اختبار إدراج بيانات تجريبية
    console.log('\n📝 اختبار إدراج بيانات تجريبية:');
    
    // إدراج مستخدم تجريبي
    try {
      await client.query(`
        INSERT INTO users (username, password_hash, email, role) 
        VALUES ('test_user', '$2b$10$example', '<EMAIL>', 'user')
        ON CONFLICT (username) DO NOTHING
      `);
      console.log('   ✅ تم إدراج مستخدم تجريبي');
    } catch (error) {
      console.log(`   ❌ خطأ في إدراج المستخدم: ${error.message}`);
    }

    // إدراج موظف تجريبي
    try {
      await client.query(`
        INSERT INTO employees (name, position, phone, email) 
        VALUES ('موظف تجريبي', 'محامي', '777000000', '<EMAIL>')
        ON CONFLICT DO NOTHING
      `);
      console.log('   ✅ تم إدراج موظف تجريبي');
    } catch (error) {
      console.log(`   ❌ خطأ في إدراج الموظف: ${error.message}`);
    }

    // إدراج موكل تجريبي
    try {
      await client.query(`
        INSERT INTO clients (name, phone, email, id_number) 
        VALUES ('موكل تجريبي', '777111111', '<EMAIL>', 'TEST123456')
        ON CONFLICT (id_number) DO NOTHING
      `);
      console.log('   ✅ تم إدراج موكل تجريبي');
    } catch (error) {
      console.log(`   ❌ خطأ في إدراج الموكل: ${error.message}`);
    }

    // إدراج قضية تجريبية
    try {
      await client.query(`
        INSERT INTO issues (case_number, title, description, status) 
        VALUES ('TEST-2024-001', 'قضية تجريبية', 'وصف القضية التجريبية', 'active')
        ON CONFLICT (case_number) DO NOTHING
      `);
      console.log('   ✅ تم إدراج قضية تجريبية');
    } catch (error) {
      console.log(`   ❌ خطأ في إدراج القضية: ${error.message}`);
    }

    // 6. اختبار APIs الأساسية
    console.log('\n🌐 اختبار APIs الأساسية:');
    
    // اختبار API النسب المالية
    try {
      const result = await client.query('SELECT * FROM lineages LIMIT 1');
      if (result.rows.length > 0) {
        console.log('   ✅ API النسب المالية جاهز للعمل');
      } else {
        console.log('   ⚠️ API النسب المالية: لا توجد بيانات');
      }
    } catch (error) {
      console.log(`   ❌ API النسب المالية: خطأ - ${error.message}`);
    }

    // اختبار API الخدمات
    try {
      const result = await client.query('SELECT * FROM services LIMIT 1');
      if (result.rows.length > 0) {
        console.log('   ✅ API الخدمات جاهز للعمل');
      } else {
        console.log('   ⚠️ API الخدمات: لا توجد بيانات');
      }
    } catch (error) {
      console.log(`   ❌ API الخدمات: خطأ - ${error.message}`);
    }

    // 7. التحقق النهائي من النتائج
    console.log('\n📊 التحقق النهائي من قاعدة البيانات:');
    
    const finalResults = await Promise.all([
      client.query('SELECT COUNT(*) FROM users'),
      client.query('SELECT COUNT(*) FROM employees'),
      client.query('SELECT COUNT(*) FROM clients'),
      client.query('SELECT COUNT(*) FROM issues'),
      client.query('SELECT COUNT(*) FROM lineages'),
      client.query('SELECT COUNT(*) FROM services'),
      client.query('SELECT COUNT(*) FROM serviceslow'),
      client.query('SELECT COUNT(*) FROM footer_links')
    ]);

    console.log(`   - المستخدمين: ${finalResults[0].rows[0].count} سجل`);
    console.log(`   - الموظفين: ${finalResults[1].rows[0].count} سجل`);
    console.log(`   - الموكلين: ${finalResults[2].rows[0].count} سجل`);
    console.log(`   - القضايا: ${finalResults[3].rows[0].count} سجل`);
    console.log(`   - النسب المالية: ${finalResults[4].rows[0].count} سجل`);
    console.log(`   - الخدمات: ${finalResults[5].rows[0].count} سجل`);
    console.log(`   - خدمات الموقع: ${finalResults[6].rows[0].count} سجل`);
    console.log(`   - روابط التذييل: ${finalResults[7].rows[0].count} سجل`);

    console.log('\n✅ جميع الاختبارات مكتملة!');
    console.log('🔗 النظام متصل بقاعدة البيانات بنجاح');
    console.log('🌐 النظام متاح على: http://localhost:7443');

  } catch (error) {
    console.error('❌ خطأ في اختبار قاعدة البيانات:', error.message);
  } finally {
    await client.end();
    console.log('🔄 تم قطع الاتصال بقاعدة البيانات');
  }
}

testDatabaseConnection();
