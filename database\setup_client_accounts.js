/**
 * سكريبت لإعداد حسابات العملاء في دليل الحسابات
 * وتحديث العملاء الموجودين بأرقام الحسابات الصحيحة
 */

const { Client } = require('pg');

// إعدادات قاعدة البيانات mohammidev
const dbConfig = {
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'mohammidev'
};

async function setupClientAccounts() {
  const client = new Client(dbConfig);

  try {
    await client.connect();
    console.log('✅ متصل بقاعدة البيانات mohammidev');

    // 1. إنشاء حسابات العملاء في دليل الحسابات
    console.log('\n💰 إنشاء حسابات العملاء في دليل الحسابات...');

    const clientAccounts = [
      // حساب رئيسي للعملاء
      { id: 1121, code: '1121', name: 'العملاء والموكلين', type: 'أصول' },

      // حسابات فرعية للعملاء
      { id: 1121001, code: '1121001', name: 'عميل رقم 1', type: 'أصول' },
      { id: 1121002, code: '1121002', name: 'عميل رقم 2', type: 'أصول' },
      { id: 1121003, code: '1121003', name: 'عميل رقم 3', type: 'أصول' },
      { id: 1121004, code: '1121004', name: 'عميل رقم 4', type: 'أصول' },
      { id: 1121005, code: '1121005', name: 'عميل رقم 5', type: 'أصول' },
      { id: 1121006, code: '1121006', name: 'عميل رقم 6', type: 'أصول' },
      { id: 1121007, code: '1121007', name: 'عميل رقم 7', type: 'أصول' },
      { id: 1121008, code: '1121008', name: 'عميل رقم 8', type: 'أصول' },
      { id: 1121009, code: '1121009', name: 'عميل رقم 9', type: 'أصول' },
      { id: 1121010, code: '1121010', name: 'عميل رقم 10', type: 'أصول' },
      { id: 1121011, code: '1121011', name: 'عميل رقم 11', type: 'أصول' },
      { id: 1121012, code: '1121012', name: 'عميل رقم 12', type: 'أصول' },
      { id: 1121013, code: '1121013', name: 'عميل رقم 13', type: 'أصول' },
      { id: 1121014, code: '1121014', name: 'عميل رقم 14', type: 'أصول' },
      { id: 1121015, code: '1121015', name: 'عميل رقم 15', type: 'أصول' },
      { id: 1121016, code: '1121016', name: 'عميل رقم 16', type: 'أصول' },
      { id: 1121017, code: '1121017', name: 'عميل رقم 17', type: 'أصول' },
      { id: 1121018, code: '1121018', name: 'عميل رقم 18', type: 'أصول' },
      { id: 1121019, code: '1121019', name: 'عميل رقم 19', type: 'أصول' },
      { id: 1121020, code: '1121020', name: 'عميل رقم 20', type: 'أصول' }
    ];

    // إنشاء الحساب الرئيسي أولاً
    const mainAccount = clientAccounts.find(acc => acc.id === 1121);
    if (mainAccount) {
      try {
        await client.query(`
          INSERT INTO chart_of_accounts (id, account_code, account_name, account_type, account_level, is_active, allow_transactions)
          VALUES ($1, $2, $3, $4, 3, true, true)
          ON CONFLICT (id) DO UPDATE SET
            account_code = EXCLUDED.account_code,
            account_name = EXCLUDED.account_name,
            account_type = EXCLUDED.account_type
        `, [mainAccount.id, mainAccount.code, mainAccount.name, mainAccount.type]);

        console.log(`   ✅ ${mainAccount.code}: ${mainAccount.name} (حساب رئيسي)`);
      } catch (error) {
        console.log(`   ⚠️ خطأ في إضافة الحساب الرئيسي: ${error.message}`);
      }
    }

    // ثم إنشاء الحسابات الفرعية
    const subAccounts = clientAccounts.filter(acc => acc.id !== 1121);
    for (const account of subAccounts) {
      try {
        await client.query(`
          INSERT INTO chart_of_accounts (id, account_code, account_name, account_type, account_level, parent_id, is_active, allow_transactions)
          VALUES ($1, $2, $3, $4, 5, $5, true, true)
          ON CONFLICT (id) DO UPDATE SET
            account_code = EXCLUDED.account_code,
            account_name = EXCLUDED.account_name,
            account_type = EXCLUDED.account_type,
            parent_id = EXCLUDED.parent_id
        `, [account.id, account.code, account.name, account.type, 1121]);

        console.log(`   ✅ ${account.code}: ${account.name}`);
      } catch (error) {
        if (error.code !== '23505') { // تجاهل خطأ التكرار
          console.log(`   ⚠️ خطأ في إضافة ${account.code}: ${error.message}`);
        }
      }
    }

    // 2. فحص العملاء الموجودين
    console.log('\n👥 فحص العملاء الموجودين...');
    const clients = await client.query(`
      SELECT id, name, account_id, phone, email
      FROM clients
      ORDER BY id
    `);

    console.log(`📊 عدد العملاء: ${clients.rows.length}`);

    // 3. تحديث العملاء الموجودين
    console.log('\n🔄 تحديث حسابات العملاء...');

    let updatedCount = 0;

    for (const clientData of clients.rows) {
      // تحديد رقم الحساب للعميل
      let accountId;

      if (clientData.id <= 20) {
        // استخدام حساب فردي للعملاء من 1-20
        accountId = 1121000 + clientData.id; // 1121001-1121020
      } else {
        // للعملاء الإضافيين، استخدم الحساب الرئيسي
        accountId = 1121; // حساب العملاء العام
      }

      // تحديث العميل إذا لم يكن له حساب أو كان الحساب مختلف
      if (!clientData.account_id || clientData.account_id !== accountId) {
        await client.query(
          'UPDATE clients SET account_id = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
          [accountId, clientData.id]
        );

        updatedCount++;
        console.log(`   💰 تحديث حساب العميل ${clientData.name}: ${accountId}`);
      } else {
        console.log(`   ℹ️ العميل ${clientData.name} لديه حساب صحيح بالفعل: ${accountId}`);
      }
    }

    // 4. تحديث أسماء الحسابات لتتطابق مع أسماء العملاء
    console.log('\n📝 تحديث أسماء الحسابات لتتطابق مع أسماء العملاء...');

    const clientsWithAccounts = await client.query(`
      SELECT c.id, c.name, c.account_id
      FROM clients c
      WHERE c.account_id IS NOT NULL AND c.account_id != 1121
      ORDER BY c.id
    `);

    for (const clientData of clientsWithAccounts.rows) {
      const accountName = `${clientData.name} - عميل`;

      await client.query(`
        UPDATE chart_of_accounts
        SET account_name = $1, updated_date = CURRENT_TIMESTAMP
        WHERE id = $2
      `, [accountName, clientData.account_id]);

      console.log(`   📝 تحديث اسم الحساب ${clientData.account_id}: ${accountName}`);
    }

    // 5. التحقق النهائي
    console.log('\n🔍 التحقق النهائي من التحديثات...');
    const finalCheck = await client.query(`
      SELECT
        c.id,
        c.name,
        c.account_id,
        coa.account_name,
        coa.account_code
      FROM clients c
      LEFT JOIN chart_of_accounts coa ON c.account_id = coa.id
      ORDER BY c.id
    `);

    console.log('\n📋 بيانات العملاء النهائية:');
    finalCheck.rows.forEach(client => {
      const accountInfo = client.account_id ? `${client.account_id} (${client.account_code}: ${client.account_name})` : 'غير محدد';
      console.log(`   - ${client.name}: حساب=${accountInfo}`);
    });

    // 6. إحصائيات نهائية
    const stats = await client.query(`
      SELECT
        COUNT(*) as total_clients,
        COUNT(account_id) as clients_with_account,
        COUNT(CASE WHEN coa.id IS NOT NULL THEN 1 END) as valid_accounts
      FROM clients c
      LEFT JOIN chart_of_accounts coa ON c.account_id = coa.id
    `);

    const stat = stats.rows[0];
    console.log('\n📊 إحصائيات نهائية:');
    console.log(`   - إجمالي العملاء: ${stat.total_clients}`);
    console.log(`   - عملاء لديهم رقم حساب: ${stat.clients_with_account}`);
    console.log(`   - حسابات صحيحة في دليل الحسابات: ${stat.valid_accounts}`);

    console.log('\n📈 ملخص التحديثات:');
    console.log(`   - عملاء تم تحديث حساباتهم: ${updatedCount}`);

    console.log('\n🎉 تم إعداد حسابات العملاء بنجاح!');
    console.log('💡 الآن يمكن إضافة وتحديث العملاء مع ربطهم بدليل الحسابات');

  } catch (error) {
    console.error('❌ خطأ في إعداد حسابات العملاء:', error.message);
    throw error;
  } finally {
    await client.end();
  }
}

// تشغيل السكريبت
if (require.main === module) {
  setupClientAccounts().catch(console.error);
}

module.exports = { setupClientAccounts };
