/**
 * سكريبت للتحقق من بنية جدول العملاء
 */

const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'mohammidev'
};

async function checkClientsStructure() {
  const client = new Client(dbConfig);

  try {
    await client.connect();
    console.log('✅ متصل بقاعدة البيانات mohammidev');

    // فحص بنية جدول العملاء
    const structure = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_name = 'clients' AND table_schema = 'public'
      ORDER BY ordinal_position
    `);

    console.log('\n📋 بنية جدول clients:');
    structure.rows.forEach(col => {
      console.log(`   - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? '(مطلوب)' : '(اختياري)'} ${col.column_default ? `[افتراضي: ${col.column_default}]` : ''}`);
    });

    // فحص عينة من البيانات
    const sample = await client.query('SELECT * FROM clients LIMIT 3');
    console.log('\n📊 عينة من البيانات:');
    sample.rows.forEach(row => {
      console.log(`   - ID: ${row.id}, Name: ${row.name}, Account ID: ${row.account_id || 'غير محدد'}`);
    });

    // فحص العملاء بدون account_id
    const missingAccounts = await client.query('SELECT COUNT(*) as count FROM clients WHERE account_id IS NULL');
    console.log(`\n⚠️ عملاء بدون account_id: ${missingAccounts.rows[0].count}`);

  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await client.end();
  }
}

checkClientsStructure().catch(console.error);
