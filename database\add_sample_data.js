// إضافة بيانات تجريبية للجداول الفارغة
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'mohammi',
  user: 'postgres',
  password: 'yemen123'
};

async function addSampleData() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔄 جاري الاتصال بقاعدة البيانات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 1. إضافة بيانات المستخدمين
    console.log('🔄 جاري إضافة بيانات المستخدمين...');
    const usersData = [
      { username: 'admin', password_hash: '$2b$10$example', name: 'المدير العام', email: '<EMAIL>', role: 'admin' },
      { username: 'lawyer1', password_hash: '$2b$10$example', name: 'م<PERSON><PERSON><PERSON> أحمد', email: '<EMAIL>', role: 'lawyer' },
      { username: 'secretary', password_hash: '$2b$10$example', name: 'فاطمة علي', email: '<EMAIL>', role: 'secretary' }
    ];

    for (const user of usersData) {
      try {
        await client.query(`
          INSERT INTO users (username, password_hash, name, email, role)
          SELECT $1, $2, $3, $4, $5
          WHERE NOT EXISTS (SELECT 1 FROM users WHERE username = $1)
        `, [user.username, user.password_hash, user.name, user.email, user.role]);
        console.log(`   ✅ تم إدراج المستخدم: ${user.name}`);
      } catch (error) {
        console.log(`   ⚠️ خطأ في إدراج ${user.name}: ${error.message}`);
      }
    }

    // 2. إضافة بيانات الموظفين
    console.log('🔄 جاري إضافة بيانات الموظفين...');
    const employeesData = [
      { name: 'أحمد محمد الصالح', position: 'محامي أول', phone: '777123456', email: '<EMAIL>', salary: 150000 },
      { name: 'سارة علي الحسن', position: 'محامية', phone: '777234567', email: '<EMAIL>', salary: 120000 },
      { name: 'محمد عبدالله القاسم', position: 'مساعد قانوني', phone: '777345678', email: '<EMAIL>', salary: 80000 },
      { name: 'نور الدين يحيى', position: 'سكرتير', phone: '777456789', email: '<EMAIL>', salary: 60000 }
    ];

    for (const employee of employeesData) {
      try {
        await client.query(`
          INSERT INTO employees (name, position, phone, email, salary)
          SELECT $1, $2, $3, $4, $5
          WHERE NOT EXISTS (SELECT 1 FROM employees WHERE email = $4)
        `, [employee.name, employee.position, employee.phone, employee.email, employee.salary]);
        console.log(`   ✅ تم إدراج الموظف: ${employee.name}`);
      } catch (error) {
        console.log(`   ⚠️ خطأ في إدراج ${employee.name}: ${error.message}`);
      }
    }

    // 3. إضافة بيانات الموكلين
    console.log('🔄 جاري إضافة بيانات الموكلين...');
    const clientsData = [
      { name: 'شركة الأمل التجارية', phone: '*********', email: '<EMAIL>', address: 'صنعاء - شارع الزبيري', id_number: '12345678901', client_type: 'company' },
      { name: 'عبدالرحمن محمد الشامي', phone: '*********', email: '<EMAIL>', address: 'عدن - كريتر', id_number: '23456789012', client_type: 'individual' },
      { name: 'مؤسسة النور للتجارة', phone: '*********', email: '<EMAIL>', address: 'تعز - شارع جمال', id_number: '34567890123', client_type: 'company' },
      { name: 'فاطمة أحمد الحداد', phone: '*********', email: '<EMAIL>', address: 'الحديدة - شارع الكورنيش', id_number: '45678901234', client_type: 'individual' }
    ];

    for (const client of clientsData) {
      try {
        await client.query(`
          INSERT INTO clients (name, phone, email, address, id_number, client_type)
          SELECT $1, $2, $3, $4, $5, $6
          WHERE NOT EXISTS (SELECT 1 FROM clients WHERE id_number = $5)
        `, [client.name, client.phone, client.email, client.address, client.id_number, client.client_type]);
        console.log(`   ✅ تم إدراج الموكل: ${client.name}`);
      } catch (error) {
        console.log(`   ⚠️ خطأ في إدراج ${client.name}: ${error.message}`);
      }
    }

    // 4. إضافة بيانات القضايا
    console.log('🔄 جاري إضافة بيانات القضايا...');
    const issuesData = [
      { title: 'قضية تجارية - نزاع عقد', description: 'نزاع حول تنفيذ عقد توريد بضائع', status: 'active', priority: 'high', client_id: 1, assigned_to: 1 },
      { title: 'قضية عمالية - فصل تعسفي', description: 'دعوى فصل تعسفي من العمل', status: 'active', priority: 'medium', client_id: 2, assigned_to: 2 },
      { title: 'قضية مدنية - تعويض أضرار', description: 'دعوى تعويض عن أضرار مادية ومعنوية', status: 'pending', priority: 'high', client_id: 3, assigned_to: 1 },
      { title: 'قضية أحوال شخصية - طلاق', description: 'دعوى طلاق للضرر والشقاق', status: 'active', priority: 'low', client_id: 4, assigned_to: 2 }
    ];

    for (const issue of issuesData) {
      try {
        await client.query(`
          INSERT INTO issues (title, description, status, priority, client_id, assigned_to)
          SELECT $1, $2, $3, $4, $5, $6
          WHERE NOT EXISTS (SELECT 1 FROM issues WHERE title = $1)
        `, [issue.title, issue.description, issue.status, issue.priority, issue.client_id, issue.assigned_to]);
        console.log(`   ✅ تم إدراج القضية: ${issue.title}`);
      } catch (error) {
        console.log(`   ⚠️ خطأ في إدراج ${issue.title}: ${error.message}`);
      }
    }

    // 5. التحقق من النتائج النهائية
    console.log('🔄 جاري التحقق من النتائج النهائية...');
    
    const results = await Promise.all([
      client.query('SELECT COUNT(*) FROM users'),
      client.query('SELECT COUNT(*) FROM employees'),
      client.query('SELECT COUNT(*) FROM clients'),
      client.query('SELECT COUNT(*) FROM issues'),
      client.query('SELECT COUNT(*) FROM lineages'),
      client.query('SELECT COUNT(*) FROM services')
    ]);

    console.log('📊 ملخص البيانات النهائي:');
    console.log(`   - المستخدمين: ${results[0].rows[0].count} سجل`);
    console.log(`   - الموظفين: ${results[1].rows[0].count} سجل`);
    console.log(`   - الموكلين: ${results[2].rows[0].count} سجل`);
    console.log(`   - القضايا: ${results[3].rows[0].count} سجل`);
    console.log(`   - النسب المالية: ${results[4].rows[0].count} سجل`);
    console.log(`   - الخدمات: ${results[5].rows[0].count} سجل`);

    console.log('✅ تم إضافة البيانات التجريبية بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في إضافة البيانات التجريبية:', error.message);
  } finally {
    await client.end();
    console.log('🔄 تم قطع الاتصال بقاعدة البيانات');
  }
}

addSampleData();
