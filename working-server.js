// خادم واحد بسيط - مضمون 100%
const http = require('http');

console.log('🚀 بدء الخادم البسيط...');

// خادم واحد فقط على المنفذ 7443
const server = http.createServer((req, res) => {
  res.writeHead(200, { 
    'Content-Type': 'text/html; charset=utf-8',
    'Access-Control-Allow-Origin': '*'
  });
  
  res.end(`
    <!DOCTYPE html>
    <html dir="rtl">
    <head>
      <meta charset="utf-8">
      <title>نظام إدارة المحاماة</title>
      <style>
        body { 
          font-family: Arial; 
          text-align: center; 
          padding: 50px; 
          background: #f0f8ff;
        }
        .container { 
          background: white; 
          padding: 40px; 
          border-radius: 10px; 
          box-shadow: 0 4px 8px rgba(0,0,0,0.1);
          max-width: 600px;
          margin: 0 auto;
        }
        h1 { color: #2563eb; margin-bottom: 30px; }
        .success { 
          background: #d4edda; 
          color: #155724; 
          padding: 20px; 
          border-radius: 5px; 
          margin: 20px 0;
        }
        .info { 
          background: #e3f2fd; 
          padding: 20px; 
          border-radius: 5px; 
          margin: 20px 0;
        }
        .btn {
          background: #2563eb;
          color: white;
          padding: 15px 30px;
          border: none;
          border-radius: 5px;
          font-size: 16px;
          cursor: pointer;
          margin: 10px;
        }
        .btn:hover { background: #1d4ed8; }
      </style>
    </head>
    <body>
      <div class="container">
        <h1>🏢 نظام إدارة المحاماة</h1>
        
        <div class="success">
          ✅ الخادم يعمل بنجاح!
        </div>
        
        <div class="info">
          <h3>معلومات الخادم:</h3>
          <p>📅 الوقت: ${new Date().toLocaleString('ar-SA')}</p>
          <p>🌐 المنفذ: 7443</p>
          <p>🔗 الرابط: http://localhost:7443</p>
          <p>🌍 خارجي: https://mohammi.com:7443</p>
        </div>
        
        <button class="btn" onclick="location.reload()">
          🔄 تحديث
        </button>
        
        <button class="btn" onclick="window.open('/api/test', '_blank')">
          🧪 اختبار API
        </button>
      </div>
    </body>
    </html>
  `);
});

// تشغيل الخادم
server.listen(7443, '0.0.0.0', () => {
  console.log('✅ الخادم يعمل على المنفذ 7443');
  console.log('🌐 محلي: http://localhost:7443');
  console.log('🌍 خارجي: https://mohammi.com:7443');
  console.log('');
  console.log('🎉 الخادم جاهز للاستخدام!');
});

server.on('error', (error) => {
  console.error('❌ خطأ:', error.message);
});

// معالجة الإيقاف
process.on('SIGINT', () => {
  console.log('\n🛑 إيقاف الخادم...');
  server.close();
  process.exit(0);
});
