
import { NextRequest, NextResponse } from 'next/server'
export const runtime = 'nodejs'
import path from 'path'
import fs from 'fs'

// دالة للحصول على قاعدة البيانات حسب المنفذ
function getDatabaseFromPort(request: NextRequest): string {
  const host = request.headers.get('host') || ''
  const referer = request.headers.get('referer') || ''

  // استخراج المنفذ من الـ host أو referer
  let port = '3000' // افتراضي

  if (host.includes(':7443') || referer.includes(':7443')) {
    port = '7443'
  } else if (host.includes(':8914') || referer.includes(':8914')) {
    port = '8914'
  } else if (host.includes(':3000') || referer.includes(':3000')) {
    port = '3000'
  } else if (host.includes(':3001') || referer.includes(':3001')) {
    port = '3001'
  }

  // توجيه قاعدة البيانات حسب المنفذ
  switch (port) {
    case '7443':
    case '3000':
      return 'mohammi'
    case '8914':
    case '3001':
      return 'rubaie'
    default:
      return 'mohammi' // افتراضي
  }
}

// دالة للحصول على اسم الشركة حسب قاعدة البيانات
function getCompanyName(database: string): string {
  switch (database) {
    case 'mohammi':
      return 'نظام إدارة المحاماة - محمد'
    case 'rubaie':
      return 'نظام إدارة المحاماة - الربعي'
    default:
      return 'نظام إدارة المحاماة'
  }
}

// بسيط: بحث قائم على تداخل الكلمات مع دعم العربية بدون مكتبات إضافية
function normalize(text: string) {
  return (text || '')
    .replace(/[\u200f\u200e]/g, '') // إزالة علامات الاتجاه
    .replace(/[\u0610-\u061a\u064b-\u065f\u06d6-\u06ed]/g, '') // تشكيل
    // إبقاء الأحرف العربية + الإنجليزية والأرقام والمسافات فقط
    .replace(/[^A-Za-z0-9\u0600-\u06FF\s]/g, ' ')
    .replace(/\s+/g, ' ')
    .trim()
    .toLowerCase()
}

// تطبيع إضافي خفيف للجذور العربية
function normalizeArabicWord(w: string) {
  let s = w
    .replace(/[\u0610-\u061a\u064b-\u065f\u06d6-\u06ed]/g, '') // إزالة التشكيل
    .replace(/[أإآٱ]/g, 'ا')
    .replace(/ى/g, 'ي')
    .replace(/ة/g, 'ه')
    .replace(/ؤ/g, 'و')
    .replace(/ئ/g, 'ي')
    .replace(/\u0640/g, '') // تطويل
    .toLowerCase()
    .trim()

  // إزالة "ال" التعريفية من البداية إن وجدت
  if (s.startsWith('ال') && s.length > 2) s = s.slice(2)
  return s
}

function tokenize(text: string) {
  const toks = normalize(text).split(' ').filter(Boolean)
  return toks.map(normalizeArabicWord)
}

function scoreSnippet(queryTokens: string[], text: string, rawQuery: string) {
  const tokens = tokenize(text)
  if (tokens.length === 0) return 0
  const set = new Map<string, number>()
  for (const t of tokens) set.set(t, (set.get(t) || 0) + 1)
  let score = 0
  for (const q of queryTokens) {
    const freq = set.get(q) || 0
    if (freq > 0) score += 1 + Math.log(1 + freq)
  }
  // تعزيز إذا كان النص يحتوي الاستعلام الخام أو شكله المطبّع
  const raw = text.toLowerCase()
  const rawNorm = normalize(raw)
  const qNorm = normalize(rawQuery)
  const qStem = normalizeArabicWord(qNorm)
  if (raw.includes(rawQuery.toLowerCase())) score += 1.2
  if (rawNorm.includes(qNorm)) score += 1.0
  if (tokens.includes(qStem)) score += 0.8

  return score / Math.log(2 + tokens.length) // تطبيع بسيط حسب طول النص
}

function makeSnippet(text: string, query: string, maxLen = 320) {
  const idx = text.toLowerCase().indexOf(query.toLowerCase())
  if (idx === -1) return text.slice(0, maxLen) + (text.length > maxLen ? '…' : '')
  const start = Math.max(0, idx - Math.floor(maxLen / 2))
  const end = Math.min(text.length, start + maxLen)
  return (start > 0 ? '…' : '') + text.slice(start, end) + (end < text.length ? '…' : '')
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const query: string = (body?.query ?? '').toString()
    const topK: number = Math.min(Math.max(parseInt(body?.topK ?? '5', 10) || 5, 1), 10)

    if (!query.trim()) {
      return NextResponse.json({ success: false, error: 'الاستعلام مطلوب' }, { status: 400 })
    }

    // الحصول على قاعدة البيانات والشركة حسب المنفذ
    const database = getDatabaseFromPort(request)
    const companyName = getCompanyName(database)

    // مسار الفهرس الخاص بكل شركة
    const indexPath = path.resolve(process.cwd(), 'database', `${database}-laws-index.json`)
    if (!fs.existsSync(indexPath)) {
      return NextResponse.json({ success: false, error: 'لم يتم بناء الفهرس بعد. شغّل سكربت الفهرسة.' }, { status: 404 })
    }

    const raw = fs.readFileSync(indexPath, 'utf-8')
    const data = JSON.parse(raw)
    const items: Array<{ id: number; source: string; chunk: number; text: string }> = data.items || []

    let qTokens = tokenize(query)
    if (qTokens.length === 0 && query.trim().length > 0) {
      // Fallback: قسّم مباشرة بدون Normalize (تحسّباً لأي مشكلات Unicode على بعض البيئات)
      qTokens = query.trim().split(/\s+/)
    }
    if (qTokens.length === 0) {
      return NextResponse.json({ success: false, error: 'استعلام غير صالح' }, { status: 400 })
    }

    let scored = items.map((it) => ({
      id: it.id,
      source: it.source,
      chunk: it.chunk,
      text: it.text,
      score: scoreSnippet(qTokens, it.text, query),
    }))
    .filter((s) => s.score > 0)
    .sort((a, b) => b.score - a.score)
    .slice(0, topK)

    // Fallback: في حال النتيجة فارغة، جب مطابقة جزئية بسيطة على النص الخام
    if (scored.length === 0) {
      const qn = normalize(query)
      const qs = normalizeArabicWord(qn)
      const lower = query.toLowerCase()
      scored = items
        .map((it) => ({
          id: it.id,
          source: it.source,
          chunk: it.chunk,
          text: it.text,
          score:
            (it.text.toLowerCase().includes(lower) ? 1 : 0) +
            (normalize(it.text).includes(qn) ? 0.8 : 0) +
            (tokenize(it.text).includes(qs) ? 0.6 : 0),
        }))
        .filter((s) => s.score > 0)
        .sort((a, b) => b.score - a.score)
        .slice(0, topK)
    }

    return NextResponse.json({
      success: true,
      total: items.length,
      database: database,
      company: companyName,
      results: scored.map(s => ({
        id: s.id,
        source: s.source,
        chunk: s.chunk,
        score: Number(s.score.toFixed(4)),
        snippet: makeSnippet(s.text, query),
        text: s.text,
      }))
    })
  } catch (e: any) {
    return NextResponse.json({ success: false, error: e.message }, { status: 500 })
  }
}

