import { NextRequest, NextResponse } from 'next/server'
export const runtime = 'nodejs'
import path from 'path'
import fs from 'fs'

// بسيط: بحث قائم على تداخل الكلمات مع دعم العربية بدون مكتبات إضافية
function normalize(text: string) {
  return (text || '')
    .replace(/[\u200f\u200e]/g, '') // إزالة علامات الاتجاه
    .replace(/[\u0610-\u061a\u064b-\u065f\u06d6-\u06ed]/g, '') // تشكيل
    // إبقاء الأحرف العربية + الإنجليزية والأرقام والمسافات فقط
    .replace(/[^A-Za-z0-9\u0600-\u06FF\s]/g, ' ')
    .replace(/\s+/g, ' ')
    .trim()
    .toLowerCase()
}

function tokenize(text: string) {
  return normalize(text).split(' ').filter(Boolean)
}

function scoreSnippet(queryTokens: string[], text: string) {
  const tokens = tokenize(text)
  if (tokens.length === 0) return 0
  const set = new Map<string, number>()
  for (const t of tokens) set.set(t, (set.get(t) || 0) + 1)
  let score = 0
  for (const q of queryTokens) {
    const freq = set.get(q) || 0
    if (freq > 0) score += 1 + Math.log(1 + freq)
  }
  return score / Math.log(2 + tokens.length) // تطبيع بسيط حسب طول النص
}

function makeSnippet(text: string, query: string, maxLen = 320) {
  const idx = text.toLowerCase().indexOf(query.toLowerCase())
  if (idx === -1) return text.slice(0, maxLen) + (text.length > maxLen ? '…' : '')
  const start = Math.max(0, idx - Math.floor(maxLen / 2))
  const end = Math.min(text.length, start + maxLen)
  return (start > 0 ? '…' : '') + text.slice(start, end) + (end < text.length ? '…' : '')
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const query: string = body?.query || ''
    const topK: number = Math.min(Math.max(parseInt(body?.topK ?? '5', 10) || 5, 1), 10)

    if (!query.trim()) {
      return NextResponse.json({ success: false, error: 'الاستعلام مطلوب' }, { status: 400 })
    }

    const indexPath = path.resolve(process.cwd(), 'database', 'laws-index.json')
    if (!fs.existsSync(indexPath)) {
      return NextResponse.json({ success: false, error: 'لم يتم بناء الفهرس بعد. شغّل سكربت الفهرسة.' }, { status: 404 })
    }

    const raw = fs.readFileSync(indexPath, 'utf-8')
    const data = JSON.parse(raw)
    const items: Array<{ id: number; source: string; chunk: number; text: string }> = data.items || []

    const qTokens = tokenize(query)
    if (qTokens.length === 0) {
      return NextResponse.json({ success: false, error: 'استعلام غير صالح' }, { status: 400 })
    }

    const scored = items.map((it) => ({
      id: it.id,
      source: it.source,
      chunk: it.chunk,
      text: it.text,
      score: scoreSnippet(qTokens, it.text),
    }))
    .filter((s) => s.score > 0)
    .sort((a, b) => b.score - a.score)
    .slice(0, topK)

    return NextResponse.json({ success: true, total: items.length, results: scored.map(s => ({
      id: s.id,
      source: s.source,
      chunk: s.chunk,
      score: Number(s.score.toFixed(4)),
      snippet: makeSnippet(s.text, query),
      text: s.text,
    })) })
  } catch (e: any) {
    return NextResponse.json({ success: false, error: e.message }, { status: 500 })
  }
}
