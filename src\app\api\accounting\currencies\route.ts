import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب جميع العملات
export async function GET() {
  try {
    const result = await query(`
      SELECT 
        id,
        currency_code,
        currency_name,
        symbol,
        exchange_rate,
        is_base_currency,
        is_active,
        created_date
      FROM currencies
      WHERE is_active = true
      ORDER BY is_base_currency DESC, currency_name
    `)

    return NextResponse.json({
      success: true,
      currencies: result.rows,
      total: result.rows.length,
      message: 'تم جلب العملات بنجاح'
    })

  } catch (error) {
    console.error('خطأ في جلب العملات:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب العملات',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// POST - إضافة عملة جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      currency_code,
      currency_name,
      symbol,
      exchange_rate = 1.0000,
      is_base_currency = false
    } = body

    // التحقق من صحة البيانات
    if (!currency_code || !currency_name) {
      return NextResponse.json({
        success: false,
        error: 'رمز العملة واسم العملة مطلوبان'
      }, { status: 400 })
    }

    // التحقق من عدم تكرار رمز العملة
    const existingCurrency = await query(
      'SELECT id FROM currencies WHERE currency_code = $1',
      [currency_code]
    )

    if (existingCurrency.rows.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'رمز العملة موجود مسبقاً'
      }, { status: 400 })
    }

    // إذا كانت العملة الأساسية، تحديث العملات الأخرى
    if (is_base_currency) {
      await query('UPDATE currencies SET is_base_currency = false')
    }

    const result = await query(`
      INSERT INTO currencies (currency_code, currency_name, symbol, exchange_rate, is_base_currency)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `, [currency_code, currency_name, symbol, exchange_rate, is_base_currency])

    return NextResponse.json({
      success: true,
      currency: result.rows[0],
      message: 'تم إضافة العملة بنجاح'
    })

  } catch (error) {
    console.error('خطأ في إضافة العملة:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في إضافة العملة',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
