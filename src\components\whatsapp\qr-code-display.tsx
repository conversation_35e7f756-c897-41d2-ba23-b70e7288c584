'use client'

import React, { useState, useEffect } from 'react'
import dynamic from 'next/dynamic'

// تحميل QRCode بشكل ديناميكي لتجنب مشاكل SSR
const QRCode = dynamic(() => import('qrcode.react'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center w-64 h-64 bg-gray-100 border rounded-lg">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
        <p className="text-sm text-gray-600">جاري تحميل QR Code...</p>
      </div>
    </div>
  )
})

interface QRCodeDisplayProps {
  value: string
  size?: number
  className?: string
}

export default function QRCodeDisplay({ value, size = 256, className = '' }: QRCodeDisplayProps) {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  if (!isClient) {
    return (
      <div className={`flex items-center justify-center bg-gray-100 border rounded-lg ${className}`} 
           style={{ width: size, height: size }}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-sm text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`p-4 bg-white border rounded-lg ${className}`}>
      <QRCode value={value} size={size} />
    </div>
  )
}
