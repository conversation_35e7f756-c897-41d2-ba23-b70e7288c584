'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Plus, Edit, Trash2 } from 'lucide-react'

interface Opponent { id: number; name: string; phone?: string; address?: string; notes?: string }

export default function OpponentsPage() {
  const [items, setItems] = useState<Opponent[]>([])
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [editing, setEditing] = useState<Opponent | null>(null)
  const [form, setForm] = useState({ name: '', phone: '', address: '', notes: '' })
  const [error, setError] = useState<string | null>(null)

  const fetchOpponents = async () => {
    try {
      const res = await fetch('/api/opponents')
      const data = await res.json()
      if (data.success) setItems(data.data || [])
    } catch (e) { console.error('Error fetching opponents:', e) }
  }

  useEffect(() => { fetchOpponents() }, [])

  const openAdd = () => { setEditing(null); setForm({ name: '', phone: '', address: '', notes: '' }); setIsModalOpen(true) }
  const openEdit = (o: Opponent) => { setEditing(o); setForm({ name: o.name || '', phone: o.phone || '', address: o.address || '', notes: o.notes || '' }); setIsModalOpen(true) }

  const save = async () => {
    if (!form.name.trim()) { setError('اسم الخصم مطلوب'); return }
    setError(null); setIsSaving(true)
    try {
      if (editing) {
        const res = await fetch(`/api/opponents?id=${editing.id}`, { method: 'PUT', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(form) })
        const data = await res.json(); if (!data.success) throw new Error(data.error || 'فشل')
      } else {
        const res = await fetch('/api/opponents', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(form) })
        const data = await res.json(); if (!data.success) throw new Error(data.error || 'فشل')
      }
      setIsModalOpen(false)
      fetchOpponents()
    } catch (e:any) { setError(e.message || 'فشل في الحفظ') } finally { setIsSaving(false) }
  }

  const remove = async (o: Opponent) => {
    if (!confirm(`هل تريد حذف الخصم "${o.name}"؟`)) return
    try {
      const res = await fetch(`/api/opponents?id=${o.id}`, { method: 'DELETE' })
      const data = await res.json()
      if (!data.success) { alert(data.error || 'فشل في الحذف'); return }
      fetchOpponents()
    } catch (e) { alert('خطأ في الحذف') }
  }

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">إدارة الخصوم</h1>
        <Button onClick={openAdd} className="bg-indigo-600 hover:bg-indigo-700 text-white">
          <Plus className="h-4 w-4 mr-2" /> إضافة خصم جديد
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>القائمة ({items.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b bg-gray-50">
                  <th className="text-right p-3 font-semibold">#</th>
                  <th className="text-right p-3 font-semibold">اسم الخصم</th>
                  <th className="text-right p-3 font-semibold">الهاتف</th>
                  <th className="text-right p-3 font-semibold">العنوان</th>
                  <th className="text-right p-3 font-semibold">ملاحظات</th>
                  <th className="text-center p-3 font-semibold">الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {items.map((o) => (
                  <tr key={o.id} className="border-b hover:bg-gray-50">
                    <td className="p-3">{o.id}</td>
                    <td className="p-3">{o.name}</td>
                    <td className="p-3">{o.phone || '-'}</td>
                    <td className="p-3">{o.address || '-'}</td>
                    <td className="p-3">{o.notes || '-'}</td>
                    <td className="p-3">
                      <div className="flex justify-center gap-2">
                        <Button size="sm" variant="outline" onClick={() => openEdit(o)} className="text-blue-600 border-blue-200">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => remove(o)} className="text-red-600 border-red-200">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {isModalOpen && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-lg">
            <h2 className="text-xl font-bold mb-4">{editing ? 'تعديل الخصم' : 'إضافة خصم جديد'}</h2>
            <div className="grid grid-cols-1 gap-3">
              <div>
                <Label htmlFor="op_id">رقم الخصم</Label>
                <Input id="op_id" value={editing ? editing.id : 'سيُولد تلقائياً عند الحفظ'} readOnly disabled className="text-black" />
              </div>
              <div>
                <Label htmlFor="op_name">اسم الخصم</Label>
                <Input id="op_name" value={form.name} onChange={(e)=> setForm({...form, name: e.target.value})} placeholder="أدخل اسم الخصم" className="text-black" />
              </div>
              <div>
                <Label htmlFor="op_phone">رقم الهاتف</Label>
                <Input id="op_phone" value={form.phone} onChange={(e)=> setForm({...form, phone: e.target.value})} placeholder="أدخل رقم الهاتف" className="text-black" />
              </div>
              <div>
                <Label htmlFor="op_address">العنوان</Label>
                <Input id="op_address" value={form.address} onChange={(e)=> setForm({...form, address: e.target.value})} placeholder="أدخل عنوان الخصم" className="text-black" />
              </div>
              <div>
                <Label htmlFor="op_notes">ملاحظات</Label>
                <Input id="op_notes" value={form.notes} onChange={(e)=> setForm({...form, notes: e.target.value})} placeholder="ملاحظات أخرى" className="text-black" />
              </div>
              {error && <div className="text-red-600 text-sm">{error}</div>}
            </div>
            <div className="flex justify-end gap-2 mt-6">
              <Button variant="outline" onClick={()=> setIsModalOpen(false)}>إلغاء</Button>
              <Button onClick={save} disabled={isSaving} className="bg-indigo-600 hover:bg-indigo-700 text-white">{isSaving ? 'جاري الحفظ...' : 'حفظ'}</Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
