@echo off
echo.
echo Production Server Port Check
echo ============================
echo.

echo Checking PRODUCTION ports (NO 3300):
echo.

echo Port 7443 (Mohamed System):
netstat -an | findstr ":7443" | findstr "LISTENING"
if %errorLevel% equ 0 (
    echo ✅ Port 7443: RUNNING
) else (
    echo ❌ Port 7443: STOPPED
)

echo.
echo Port 8914 (Rabei System):
netstat -an | findstr ":8914" | findstr "LISTENING"
if %errorLevel% equ 0 (
    echo ✅ Port 8914: RUNNING
) else (
    echo ❌ Port 8914: STOPPED
)

echo.
echo Port 3300 (Development - Should NOT be used in production):
netstat -an | findstr ":3300" | findstr "LISTENING"
if %errorLevel% equ 0 (
    echo ⚠️ WARNING: Port 3300 is RUNNING (This should be development only!)
) else (
    echo ✅ Port 3300: CORRECTLY NOT RUNNING in production
)

echo.
echo Production Server Status:
echo - Working Directory: D:\mohaminew
echo - Configuration: routing.config.json (NO port 3300)
echo - SSL Certificates: D:\mohaminew\production\ssl\
echo.

echo Access URLs (Production Only):
echo - Mohamed System: http://localhost:7443
echo - Rabei System: http://localhost:8914
echo.
echo Development URL (Separate server):
echo - Development: http://localhost:3300 (Run separately with 'next dev -p 3300')
echo.

pause
