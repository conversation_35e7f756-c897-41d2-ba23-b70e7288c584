/**
 * إعدادات عامة لتنسيق العملة في جميع أنحاء النظام
 * يضمن عرض رقمين عشريين في جميع التقارير والسندات والصفحات
 */

import { formatCurrency, formatNumber, formatBalance, roundToTwoDecimals, toFixedTwo } from './currency-formatter'

/**
 * تطبيق تنسيق العملة على جميع APIs في النظام
 */
export function applyCurrencyFormatting() {
  // تطبيق التنسيق على جميع الاستجابات المالية
  console.log('🔧 تطبيق تنسيق العملة على جميع أنحاء النظام...')
}

/**
 * معالج عام للبيانات المالية قبل الإرسال
 * @param data البيانات المالية
 * @returns البيانات مع تنسيق صحيح
 */
export function formatFinancialData(data: any): any {
  if (!data) return data

  // إذا كان رقم
  if (typeof data === 'number') {
    return roundToTwoDecimals(data)
  }

  // إذا كان نص يحتوي على رقم
  if (typeof data === 'string' && !isNaN(parseFloat(data))) {
    return roundToTwoDecimals(parseFloat(data))
  }

  // إذا كان مصفوفة
  if (Array.isArray(data)) {
    return data.map(item => formatFinancialData(item))
  }

  // إذا كان كائن
  if (typeof data === 'object' && data !== null) {
    const formatted: any = {}

    for (const [key, value] of Object.entries(data)) {
      // الحقول المالية المعروفة
      const financialFields = [
        'amount', 'balance', 'total', 'debit', 'credit', 'price', 'cost',
        'debit_amount', 'credit_amount', 'running_balance', 'opening_balance',
        'current_balance', 'final_balance', 'total_debit', 'total_credit',
        'net_amount', 'gross_amount', 'tax_amount', 'discount_amount'
      ]

      if (financialFields.some(field => key.toLowerCase().includes(field))) {
        formatted[key] = formatFinancialData(value)
      } else {
        formatted[key] = formatFinancialData(value)
      }
    }

    return formatted
  }

  return data
}

/**
 * دوال مساعدة للاستخدام في المكونات
 */
export const CurrencyUtils = {
  format: formatCurrency,
  formatNumber: formatNumber,
  formatBalance: formatBalance,
  round: roundToTwoDecimals,
  toFixed: toFixedTwo,

  // دوال خاصة للجداول
  formatForTable: (amount: number | string | null | undefined, showZero: boolean = true) => {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : (amount || 0)

    if (numAmount === 0 && !showZero) {
      return '-'
    }

    return formatNumber(numAmount)
  },

  // دوال خاصة للنماذج
  formatForInput: (amount: number | string | null | undefined) => {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : (amount || 0)
    return numAmount.toFixed(2)
  },

  // دوال خاصة للتقارير
  formatForReport: (amount: number | string | null | undefined) => {
    return formatCurrency(amount)
  }
}

/**
 * إعدادات النظام للعملة
 */
export const SYSTEM_CURRENCY_CONFIG = {
  symbol: 'ر.س',
  locale: 'en-US', // استخدام الأرقام اللاتينية في جميع أنحاء النظام
  decimals: 2,
  thousandsSeparator: ',',
  decimalSeparator: '.',

  // قواعد التقريب
  roundingMode: 'round', // round, floor, ceil

  // إعدادات العرض
  showSymbol: true,
  showZeroValues: true,

  // ألوان الأرصدة
  colors: {
    positive: '#28a745',
    negative: '#dc3545',
    zero: '#6c757d'
  }
}

/**
 * تطبيق التنسيق على استجابة API
 * @param response استجابة API
 * @returns الاستجابة مع تنسيق صحيح
 */
export function formatAPIResponse(response: any): any {
  if (!response || !response.data) return response

  return {
    ...response,
    data: formatFinancialData(response.data)
  }
}

/**
 * middleware لتطبيق التنسيق على جميع APIs
 */
export function createCurrencyMiddleware() {
  return (req: any, res: any, next: any) => {
    const originalJson = res.json

    res.json = function(data: any) {
      const formattedData = formatAPIResponse(data)
      return originalJson.call(this, formattedData)
    }

    next()
  }
}

/**
 * دالة للتحقق من صحة البيانات المالية
 * @param data البيانات
 * @returns true إذا كانت البيانات صحيحة
 */
export function validateFinancialData(data: any): boolean {
  if (typeof data === 'number') {
    return !isNaN(data) && isFinite(data)
  }

  if (typeof data === 'string') {
    const parsed = parseFloat(data)
    return !isNaN(parsed) && isFinite(parsed)
  }

  return false
}

/**
 * تنظيف البيانات المالية من الأخطاء الشائعة
 * @param data البيانات
 * @returns البيانات منظفة
 */
export function cleanFinancialData(data: any): any {
  if (!data) return 0

  if (typeof data === 'number') {
    if (isNaN(data) || !isFinite(data)) return 0
    return roundToTwoDecimals(data)
  }

  if (typeof data === 'string') {
    // إزالة الرموز والمسافات
    const cleaned = data.replace(/[^\d.-]/g, '')
    const parsed = parseFloat(cleaned)

    if (isNaN(parsed) || !isFinite(parsed)) return 0
    return roundToTwoDecimals(parsed)
  }

  return 0
}

// تصدير الدوال الأساسية للاستخدام المباشر
export {
  formatCurrency,
  formatNumber,
  formatBalance,
  roundToTwoDecimals,
  toFixedTwo
}
