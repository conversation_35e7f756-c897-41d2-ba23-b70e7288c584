/**
 * نظام الأتمتة لإشعارات WhatsApp
 * يتعامل مع الإشعارات التلقائية والمجدولة
 */

import { query } from '@/lib/db'
import { sendWhatsAppNotification, queueWhatsAppMessage } from './whatsapp-service'

// أنواع الأحداث التي تستدعي إشعارات
export enum NotificationTrigger {
  CASE_CREATED = 'case_created',
  CASE_UPDATED = 'case_updated',
  HEARING_SCHEDULED = 'hearing_scheduled',
  PAYMENT_DUE = 'payment_due',
  PAYMENT_RECEIVED = 'payment_received',
  DOCUMENT_READY = 'document_ready',
  APPOINTMENT_SCHEDULED = 'appointment_scheduled',
  TASK_ASSIGNED = 'task_assigned',
  DEADLINE_APPROACHING = 'deadline_approaching'
}

interface NotificationContext {
  caseId?: number
  clientId?: number
  employeeId?: number
  hearingId?: number
  paymentId?: number
  documentId?: number
  appointmentId?: number
  taskId?: number
  customData?: Record<string, any>
}

interface NotificationRule {
  trigger: NotificationTrigger
  notificationTypeId: number
  sendToClients: boolean
  sendToEmployees: boolean
  delayMinutes?: number
  conditions?: Record<string, any>
}

// فئة إدارة الأتمتة
export class WhatsAppAutomation {
  
  // إرسال إشعار تلقائي عند حدث معين
  static async triggerNotification(
    trigger: NotificationTrigger,
    context: NotificationContext
  ): Promise<void> {
    try {
      // جلب قواعد الإشعارات للحدث
      const rules = await this.getNotificationRules(trigger)
      
      for (const rule of rules) {
        // التحقق من الشروط
        if (rule.conditions && !this.checkConditions(rule.conditions, context)) {
          continue
        }

        // جلب جهات الاتصال المستهدفة
        const contacts = await this.getTargetContacts(rule, context)
        
        // تحضير متغيرات الرسالة
        const variables = await this.prepareMessageVariables(trigger, context)

        // إرسال أو جدولة الإشعارات
        for (const contact of contacts) {
          if (rule.delayMinutes && rule.delayMinutes > 0) {
            // جدولة الإشعار
            const scheduledAt = new Date(Date.now() + rule.delayMinutes * 60 * 1000)
            await queueWhatsAppMessage({
              contactId: contact.id,
              notificationTypeId: rule.notificationTypeId,
              variables
            }, scheduledAt)
          } else {
            // إرسال فوري
            await sendWhatsAppNotification(
              contact.id,
              rule.notificationTypeId,
              variables
            )
          }
        }
      }
    } catch (error) {
      console.error('خطأ في تشغيل الإشعار التلقائي:', error)
    }
  }

  // جلب قواعد الإشعارات للحدث
  private static async getNotificationRules(trigger: NotificationTrigger): Promise<NotificationRule[]> {
    try {
      const result = await query(`
        SELECT nt.id as notification_type_id, nt.send_to_clients, nt.send_to_employees,
               ar.delay_minutes, ar.conditions
        FROM notification_types nt
        LEFT JOIN automation_rules ar ON ar.notification_type_id = nt.id
        WHERE nt.type_code = $1 AND nt.is_active = true
      `, [trigger])

      return result.rows.map(row => ({
        trigger,
        notificationTypeId: row.notification_type_id,
        sendToClients: row.send_to_clients,
        sendToEmployees: row.send_to_employees,
        delayMinutes: row.delay_minutes,
        conditions: row.conditions ? JSON.parse(row.conditions) : undefined
      }))
    } catch (error) {
      console.error('خطأ في جلب قواعد الإشعارات:', error)
      return []
    }
  }

  // جلب جهات الاتصال المستهدفة
  private static async getTargetContacts(
    rule: NotificationRule,
    context: NotificationContext
  ): Promise<Array<{ id: number; phoneNumber: string }>> {
    const contacts: Array<{ id: number; phoneNumber: string }> = []

    try {
      // إشعار العملاء
      if (rule.sendToClients && context.clientId) {
        const clientContacts = await query(`
          SELECT wc.id, wc.full_phone as phone_number
          FROM whatsapp_contacts wc
          WHERE wc.client_id = $1 AND wc.opt_in = true
        `, [context.clientId])
        
        contacts.push(...clientContacts.rows)
      }

      // إشعار الموظفين
      if (rule.sendToEmployees) {
        let employeeQuery = `
          SELECT wc.id, wc.full_phone as phone_number
          FROM whatsapp_contacts wc
          WHERE wc.employee_id IS NOT NULL AND wc.opt_in = true
        `
        let queryParams: any[] = []

        // إشعار موظف محدد
        if (context.employeeId) {
          employeeQuery += ` AND wc.employee_id = $1`
          queryParams = [context.employeeId]
        }
        // إشعار الموظفين المرتبطين بالقضية
        else if (context.caseId) {
          employeeQuery = `
            SELECT DISTINCT wc.id, wc.full_phone as phone_number
            FROM whatsapp_contacts wc
            INNER JOIN case_assignments ca ON ca.employee_id = wc.employee_id
            WHERE ca.case_id = $1 AND wc.opt_in = true
          `
          queryParams = [context.caseId]
        }

        const employeeContacts = await query(employeeQuery, queryParams)
        contacts.push(...employeeContacts.rows)
      }

      return contacts
    } catch (error) {
      console.error('خطأ في جلب جهات الاتصال:', error)
      return []
    }
  }

  // تحضير متغيرات الرسالة
  private static async prepareMessageVariables(
    trigger: NotificationTrigger,
    context: NotificationContext
  ): Promise<Record<string, string>> {
    const variables: Record<string, string> = {}

    try {
      switch (trigger) {
        case NotificationTrigger.CASE_CREATED:
          if (context.caseId) {
            const caseData = await query(`
              SELECT case_number, case_title, client_name
              FROM cases c
              LEFT JOIN clients cl ON cl.id = c.client_id
              WHERE c.id = $1
            `, [context.caseId])
            
            if (caseData.rows.length > 0) {
              const row = caseData.rows[0]
              variables.case_number = row.case_number
              variables.case_title = row.case_title
              variables.client_name = row.client_name
            }
          }
          break

        case NotificationTrigger.HEARING_SCHEDULED:
          if (context.hearingId) {
            const hearingData = await query(`
              SELECT h.hearing_date, h.hearing_time, h.court_name,
                     c.case_number, c.case_title
              FROM hearings h
              INNER JOIN cases c ON c.id = h.case_id
              WHERE h.id = $1
            `, [context.hearingId])
            
            if (hearingData.rows.length > 0) {
              const row = hearingData.rows[0]
              variables.hearing_date = new Date(row.hearing_date).toLocaleDateString('ar-SA')
              variables.hearing_time = row.hearing_time
              variables.court_name = row.court_name
              variables.case_number = row.case_number
              variables.case_title = row.case_title
            }
          }
          break

        case NotificationTrigger.PAYMENT_DUE:
          if (context.paymentId) {
            const paymentData = await query(`
              SELECT p.amount, p.due_date, p.description,
                     c.case_number, cl.client_name
              FROM payments p
              INNER JOIN cases c ON c.id = p.case_id
              INNER JOIN clients cl ON cl.id = c.client_id
              WHERE p.id = $1
            `, [context.paymentId])
            
            if (paymentData.rows.length > 0) {
              const row = paymentData.rows[0]
              variables.amount = row.amount.toString()
              variables.due_date = new Date(row.due_date).toLocaleDateString('ar-SA')
              variables.description = row.description
              variables.case_number = row.case_number
              variables.client_name = row.client_name
            }
          }
          break

        case NotificationTrigger.DOCUMENT_READY:
          if (context.documentId) {
            const documentData = await query(`
              SELECT d.document_name, d.document_type,
                     c.case_number, cl.client_name
              FROM documents d
              INNER JOIN cases c ON c.id = d.case_id
              INNER JOIN clients cl ON cl.id = c.client_id
              WHERE d.id = $1
            `, [context.documentId])
            
            if (documentData.rows.length > 0) {
              const row = documentData.rows[0]
              variables.document_name = row.document_name
              variables.document_type = row.document_type
              variables.case_number = row.case_number
              variables.client_name = row.client_name
            }
          }
          break
      }

      // إضافة متغيرات عامة
      variables.current_date = new Date().toLocaleDateString('ar-SA')
      variables.current_time = new Date().toLocaleTimeString('ar-SA')
      
      // إضافة بيانات مخصصة
      if (context.customData) {
        Object.assign(variables, context.customData)
      }

      return variables
    } catch (error) {
      console.error('خطأ في تحضير متغيرات الرسالة:', error)
      return variables
    }
  }

  // فحص الشروط
  private static checkConditions(
    conditions: Record<string, any>,
    context: NotificationContext
  ): boolean {
    // تنفيذ منطق فحص الشروط
    // مثال: فحص نوع القضية، حالة العميل، إلخ
    return true
  }

  // معالجة قائمة انتظار الرسائل
  static async processMessageQueue(): Promise<void> {
    try {
      const pendingMessages = await query(`
        SELECT id, contact_id, template_id, notification_type_id, message_data
        FROM whatsapp_queue
        WHERE status = 'pending' 
          AND scheduled_at <= CURRENT_TIMESTAMP
          AND attempts < max_attempts
        ORDER BY priority DESC, scheduled_at ASC
        LIMIT 50
      `)

      for (const message of pendingMessages.rows) {
        try {
          // تحديث حالة الرسالة إلى "قيد المعالجة"
          await query(`
            UPDATE whatsapp_queue 
            SET status = 'processing', updated_at = CURRENT_TIMESTAMP
            WHERE id = $1
          `, [message.id])

          const messageData = JSON.parse(message.message_data)
          
          // إرسال الرسالة
          const result = await sendWhatsAppNotification(
            message.contact_id,
            message.notification_type_id,
            messageData.variables
          )

          // تحديث حالة الرسالة
          if (result.success) {
            await query(`
              UPDATE whatsapp_queue 
              SET status = 'sent', updated_at = CURRENT_TIMESTAMP
              WHERE id = $1
            `, [message.id])
          } else {
            await query(`
              UPDATE whatsapp_queue 
              SET status = 'failed', attempts = attempts + 1, 
                  error_message = $2, updated_at = CURRENT_TIMESTAMP
              WHERE id = $1
            `, [message.id, result.error])
          }

        } catch (error) {
          // تحديث عدد المحاولات
          await query(`
            UPDATE whatsapp_queue 
            SET status = 'failed', attempts = attempts + 1,
                error_message = $2, updated_at = CURRENT_TIMESTAMP
            WHERE id = $1
          `, [message.id, error instanceof Error ? error.message : 'خطأ غير معروف'])
        }
      }
    } catch (error) {
      console.error('خطأ في معالجة قائمة انتظار الرسائل:', error)
    }
  }
}

// دوال مساعدة للاستخدام السريع
export async function notifyNewCase(caseId: number, clientId: number): Promise<void> {
  await WhatsAppAutomation.triggerNotification(NotificationTrigger.CASE_CREATED, {
    caseId,
    clientId
  })
}

export async function notifyHearingScheduled(hearingId: number, caseId: number, clientId: number): Promise<void> {
  await WhatsAppAutomation.triggerNotification(NotificationTrigger.HEARING_SCHEDULED, {
    hearingId,
    caseId,
    clientId
  })
}

export async function notifyPaymentDue(paymentId: number, caseId: number, clientId: number): Promise<void> {
  await WhatsAppAutomation.triggerNotification(NotificationTrigger.PAYMENT_DUE, {
    paymentId,
    caseId,
    clientId
  })
}

export async function notifyDocumentReady(documentId: number, caseId: number, clientId: number): Promise<void> {
  await WhatsAppAutomation.triggerNotification(NotificationTrigger.DOCUMENT_READY, {
    documentId,
    caseId,
    clientId
  })
}

export async function notifyTaskAssigned(taskId: number, employeeId: number): Promise<void> {
  await WhatsAppAutomation.triggerNotification(NotificationTrigger.TASK_ASSIGNED, {
    taskId,
    employeeId
  })
}
