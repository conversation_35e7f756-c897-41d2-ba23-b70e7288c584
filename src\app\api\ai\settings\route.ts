import { NextRequest, NextResponse } from 'next/server'

// تحديد قاعدة البيانات حسب المنفذ (للتوافق مع chat route)
function getDatabaseByPort(port: string): string {
  switch (port) {
    case '3000':
      return 'mohammi'
    case '3001':
      return 'rubaie'
    case '7443':
      return 'mohammi'
    case '8914':
      return 'rubaie'
    default:
      return process.env.DB_NAME || 'mohammi'
  }
}

function getPortFromRequest(request: NextRequest): string {
  const host = request.headers.get('host') || ''
  return host.split(':')[1] || '7443'
}

async function ensureTable(dbName: string) {
  const { Pool } = require('pg')
  const pool = new Pool({ user: 'postgres', host: 'localhost', database: dbName, password: 'yemen123', port: 5432 })
  try {
    await pool.query(`
      CREATE TABLE IF NOT EXISTS ai_settings (
        id INTEGER PRIMARY KEY,
        is_enabled BOOLEAN DEFAULT TRUE,
        welcome_message TEXT,
        model TEXT,
        delay_seconds INTEGER,
        working_hours_only BOOLEAN,
        working_hours_start TEXT,
        working_hours_end TEXT,
        working_days TEXT[],
        max_responses_per_conversation INTEGER,
        keywords_trigger TEXT[],
        excluded_keywords TEXT[]
      );
    `)
    await pool.query(`
      INSERT INTO ai_settings (id, is_enabled, welcome_message, model, delay_seconds, working_hours_only, working_hours_start, working_hours_end, working_days, max_responses_per_conversation, keywords_trigger, excluded_keywords)
      SELECT 1, TRUE, 'مرحباً! كيف يمكنني مساعدتك اليوم؟', 'groq-llama-8b', 3, FALSE, '00:00', '23:59', ARRAY['الأحد','الاثنين','الثلاثاء','الأربعاء','الخميس','الجمعة','السبت'], 20, ARRAY['مساعدة','استفسار'], ARRAY[]::TEXT[]
      WHERE NOT EXISTS (SELECT 1 FROM ai_settings WHERE id = 1);
    `)
  } finally {
    await pool.end()
  }
}

export async function GET(request: NextRequest) {
  try {
    const port = getPortFromRequest(request)
    const dbName = getDatabaseByPort(port)
    await ensureTable(dbName)
    const { Pool } = require('pg')
    const pool = new Pool({ user: 'postgres', host: 'localhost', database: dbName, password: 'yemen123', port: 5432 })
    const res = await pool.query('SELECT * FROM ai_settings WHERE id = 1')
    await pool.end()
    const row = res.rows?.[0] || null
    if (!row) return NextResponse.json({ success: false, error: 'no settings' }, { status: 404 })
    // بناء استجابة متوافقة مع صفحة الإعدادات
    const data = {
      enabled: row.is_enabled ?? true,
      model: row.model || 'groq-llama-8b',
      delay_seconds: row.delay_seconds ?? 3,
      working_hours_only: row.working_hours_only ?? false,
      working_hours_start: row.working_hours_start || '00:00',
      working_hours_end: row.working_hours_end || '23:59',
      working_days: row.working_days || ['الأحد','الاثنين','الثلاثاء','الأربعاء','الخميس','الجمعة','السبت'],
      max_responses_per_conversation: row.max_responses_per_conversation ?? 20,
      keywords_trigger: row.keywords_trigger || [],
      excluded_keywords: row.excluded_keywords || [],
      auto_responses: {
        greeting: row.welcome_message || 'مرحباً بك! كيف يمكنني مساعدتك؟',
        working_hours: 'أنا متاح للمساعدة على مدار 24 ساعة.',
        max_reached: 'تم الوصول للحد الأقصى من الردود التلقائية.'
      }
    }
    return NextResponse.json({ success: true, data })
  } catch (e: any) {
    return NextResponse.json({ success: false, error: e.message }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const port = getPortFromRequest(request)
    const dbName = getDatabaseByPort(port)
    await ensureTable(dbName)
    const body = await request.json()
    const { Pool } = require('pg')
    const pool = new Pool({ user: 'postgres', host: 'localhost', database: dbName, password: 'yemen123', port: 5432 })

    const settings = body || {}
    const greeting = settings?.auto_responses?.greeting

    await pool.query(`
      UPDATE ai_settings SET
        is_enabled = COALESCE($1, is_enabled),
        model = COALESCE($2, model),
        delay_seconds = COALESCE($3, delay_seconds),
        working_hours_only = COALESCE($4, working_hours_only),
        working_hours_start = COALESCE($5, working_hours_start),
        working_hours_end = COALESCE($6, working_hours_end),
        working_days = COALESCE($7, working_days),
        max_responses_per_conversation = COALESCE($8, max_responses_per_conversation),
        keywords_trigger = COALESCE($9, keywords_trigger),
        excluded_keywords = COALESCE($10, excluded_keywords),
        welcome_message = COALESCE($11, welcome_message)
      WHERE id = 1
    `, [
      settings.enabled,
      settings.model,
      settings.delay_seconds,
      settings.working_hours_only,
      settings.working_hours_start,
      settings.working_hours_end,
      settings.working_days,
      settings.max_responses_per_conversation,
      settings.keywords_trigger,
      settings.excluded_keywords,
      greeting,
    ])

    await pool.end()
    return NextResponse.json({ success: true, message: 'تم حفظ الإعدادات' })
  } catch (e: any) {
    return NextResponse.json({ success: false, error: e.message }, { status: 500 })
  }
}
