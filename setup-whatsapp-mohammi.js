/**
 * سكريبت لإعداد WhatsApp في قاعدة البيانات mohammi
 * يستخدم جدول companies الموجود وجداول clients و employees
 */

const { Client } = require('pg')

// إعدادات قاعدة البيانات mohammi
const dbConfig = {
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'mohammi'
}

async function setupWhatsAppMohammi() {
  const client = new Client(dbConfig)

  try {
    await client.connect()
    console.log('🔗 تم الاتصال بقاعدة البيانات mohammi')

    // 1. فحص جدول companies الموجود
    console.log('🏢 فحص جدول companies...')
    const companiesCheck = await client.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'companies' AND table_schema = 'public'
    `)

    if (companiesCheck.rows.length === 0) {
      console.log('❌ جدول companies غير موجود')
      return
    }

    console.log('✅ جدول companies موجود')

    // 2. إضافة حقول WhatsApp لجدول companies
    console.log('📱 إضافة حقول WhatsApp لجدول companies...')

    const whatsappColumns = [
      'whatsapp_phone VARCHAR(20)',
      'whatsapp_business_name VARCHAR(100)',
      'whatsapp_enabled BOOLEAN DEFAULT false',
      'whatsapp_session_name VARCHAR(50)',
      'whatsapp_auto_reply BOOLEAN DEFAULT true',
      'whatsapp_business_hours_start TIME DEFAULT \'08:00\'',
      'whatsapp_business_hours_end TIME DEFAULT \'17:00\'',
      'whatsapp_auto_reply_message TEXT DEFAULT \'مرحباً بك في {{company_name}}. سيتم الرد عليك خلال ساعات العمل.\''
    ]

    for (const column of whatsappColumns) {
      const columnName = column.split(' ')[0]
      try {
        await client.query(`ALTER TABLE companies ADD COLUMN IF NOT EXISTS ${column}`)
        console.log(`   ✅ تم إضافة العمود: ${columnName}`)
      } catch (error) {
        console.log(`   ⚠️ العمود ${columnName} موجود مسبقاً`)
      }
    }

    // 3. فحص وتحديث بيانات الشركة الموجودة
    console.log('🔄 فحص بيانات الشركة الموجودة...')
    const existingCompany = await client.query('SELECT * FROM companies WHERE is_active = true LIMIT 1')

    if (existingCompany.rows.length > 0) {
      const company = existingCompany.rows[0]
      console.log(`✅ تم العثور على شركة: ${company.name}`)

      // تحديث بيانات WhatsApp للشركة الموجودة
      await client.query(`
        UPDATE companies SET
          whatsapp_phone = COALESCE(whatsapp_phone, $1),
          whatsapp_business_name = COALESCE(whatsapp_business_name, $2),
          whatsapp_enabled = true,
          whatsapp_session_name = COALESCE(whatsapp_session_name, $3),
          whatsapp_auto_reply = true,
          whatsapp_auto_reply_message = COALESCE(
            whatsapp_auto_reply_message,
            $4
          ),
          updated_at = CURRENT_TIMESTAMP
        WHERE id = $5
      `, [
        company.phone || '+967771234567',
        company.name || 'مكتب محمد للمحاماة',
        'mohammi_whatsapp',
        `مرحباً بك في ${company.name || 'مكتب محمد للمحاماة'}. سيتم الرد عليك خلال ساعات العمل من 8 صباحاً إلى 5 مساءً.`,
        company.id
      ])

      console.log('✅ تم تحديث بيانات WhatsApp للشركة')
    } else {
      console.log('⚠️ لا توجد شركة نشطة، سيتم إنشاء شركة افتراضية')

      // إنشاء شركة افتراضية
      await client.query(`
        INSERT INTO companies (
          name, legal_name, phone, email, address,
          whatsapp_phone, whatsapp_business_name, whatsapp_enabled,
          whatsapp_session_name, whatsapp_auto_reply, whatsapp_auto_reply_message,
          is_active
        ) VALUES (
          'نظام إدارة المحاماة - محمد',
          'مكتب محمد للمحاماة والاستشارات القانونية',
          '+967771234567',
          '<EMAIL>',
          'صنعاء، اليمن',
          '+967771234567',
          'مكتب محمد للمحاماة',
          true,
          'mohammi_whatsapp',
          true,
          'مرحباً بك في مكتب محمد للمحاماة. سيتم الرد عليك خلال ساعات العمل من 8 صباحاً إلى 5 مساءً.',
          true
        )
      `)

      console.log('✅ تم إنشاء شركة افتراضية مع إعدادات WhatsApp')
    }

    // 4. إنشاء جدول إعدادات WhatsApp المتقدمة
    console.log('⚙️ إنشاء جدول إعدادات WhatsApp...')
    await client.query(`
      CREATE TABLE IF NOT EXISTS whatsapp_company_settings (
        id SERIAL PRIMARY KEY,
        company_id INTEGER REFERENCES companies(id) ON DELETE CASCADE,
        setting_key VARCHAR(50) NOT NULL,
        setting_value TEXT,
        setting_type VARCHAR(20) DEFAULT 'string',
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT unique_company_setting UNIQUE (company_id, setting_key)
      )
    `)

    // 5. إنشاء جدول جهات اتصال WhatsApp (يربط مع clients و employees)
    console.log('📞 إنشاء جدول جهات اتصال WhatsApp...')
    await client.query(`
      CREATE TABLE IF NOT EXISTS whatsapp_contacts (
        id SERIAL PRIMARY KEY,
        company_id INTEGER REFERENCES companies(id) ON DELETE CASCADE,
        contact_type VARCHAR(20) NOT NULL, -- 'client' or 'employee'
        contact_id INTEGER NOT NULL, -- client_id or employee_id
        phone_number VARCHAR(20) NOT NULL,
        name VARCHAR(255) NOT NULL,
        is_active BOOLEAN DEFAULT true,
        opt_in BOOLEAN DEFAULT true,
        opt_in_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT unique_contact UNIQUE (company_id, contact_type, contact_id)
      )
    `)

    // إنشاء الفهارس منفصلة
    await client.query(`CREATE INDEX IF NOT EXISTS idx_whatsapp_contacts_company_type ON whatsapp_contacts (company_id, contact_type)`)
    await client.query(`CREATE INDEX IF NOT EXISTS idx_whatsapp_contacts_phone ON whatsapp_contacts (phone_number)`)

    // 6. ملء جهات الاتصال من جداول clients و employees
    console.log('👥 ملء جهات الاتصال من جداول العملاء والموظفين...')

    // الحصول على معرف الشركة
    const companyResult = await client.query('SELECT id FROM companies WHERE is_active = true LIMIT 1')
    const companyId = companyResult.rows[0]?.id || 1

    // إضافة العملاء
    await client.query(`
      INSERT INTO whatsapp_contacts (company_id, contact_type, contact_id, phone_number, name, is_active)
      SELECT
        $1 as company_id,
        'client' as contact_type,
        c.id as contact_id,
        c.phone as phone_number,
        c.name,
        CASE WHEN c.status = 'active' THEN true ELSE false END as is_active
      FROM clients c
      WHERE c.phone IS NOT NULL AND c.phone != ''
      ON CONFLICT (company_id, contact_type, contact_id) DO UPDATE SET
        phone_number = EXCLUDED.phone_number,
        name = EXCLUDED.name,
        is_active = EXCLUDED.is_active,
        updated_at = CURRENT_TIMESTAMP
    `, [companyId])

    // إضافة الموظفين
    await client.query(`
      INSERT INTO whatsapp_contacts (company_id, contact_type, contact_id, phone_number, name, is_active)
      SELECT
        $1 as company_id,
        'employee' as contact_type,
        e.id as contact_id,
        e.phone as phone_number,
        e.name,
        CASE WHEN e.status = 'active' THEN true ELSE false END as is_active
      FROM employees e
      WHERE e.phone IS NOT NULL AND e.phone != ''
      ON CONFLICT (company_id, contact_type, contact_id) DO UPDATE SET
        phone_number = EXCLUDED.phone_number,
        name = EXCLUDED.name,
        is_active = EXCLUDED.is_active,
        updated_at = CURRENT_TIMESTAMP
    `, [companyId])

    // 7. إدراج الإعدادات الافتراضية
    console.log('📝 إدراج الإعدادات الافتراضية...')
    const defaultSettings = [
      ['max_daily_messages', '500', 'number', 'الحد الأقصى للرسائل اليومية'],
      ['message_delay_seconds', '2', 'number', 'التأخير بين الرسائل (بالثواني)'],
      ['notification_templates', JSON.stringify({
        case_created: 'تم إنشاء قضية جديدة برقم {{case_number}}',
        hearing_reminder: 'تذكير: لديك جلسة محكمة يوم {{date}} في {{court}}',
        payment_reminder: 'تذكير: استحقاق دفعة بمبلغ {{amount}} في {{date}}',
        document_ready: 'المستند {{document_name}} جاهز للاستلام'
      }), 'json', 'قوالب الإشعارات']
    ]

    for (const [key, value, type, description] of defaultSettings) {
      await client.query(`
        INSERT INTO whatsapp_company_settings (company_id, setting_key, setting_value, setting_type, description)
        VALUES ($1, $2, $3, $4, $5)
        ON CONFLICT (company_id, setting_key) DO NOTHING
      `, [companyId, key, value, type, description])
    }

    // 8. إنشاء باقي الجداول المطلوبة
    console.log('📊 إنشاء جداول السجلات والإحصائيات...')

    // جدول الرسائل المحلية
    await client.query(`
      CREATE TABLE IF NOT EXISTS whatsapp_local_messages (
        id SERIAL PRIMARY KEY,
        company_id INTEGER REFERENCES companies(id) ON DELETE CASCADE,
        contact_id INTEGER REFERENCES whatsapp_contacts(id),
        phone_number VARCHAR(20) NOT NULL,
        recipient_name VARCHAR(100),
        recipient_type VARCHAR(20),
        message_content TEXT NOT NULL,
        message_type VARCHAR(20) DEFAULT 'text',
        media_path TEXT,
        status VARCHAR(20) DEFAULT 'pending',
        whatsapp_message_id VARCHAR(100),
        error_message TEXT,
        sent_at TIMESTAMP,
        delivered_at TIMESTAMP,
        read_at TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // إنشاء فهارس الرسائل
    await client.query(`CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_company_status ON whatsapp_local_messages (company_id, status)`)
    await client.query(`CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_phone_date ON whatsapp_local_messages (phone_number, sent_at)`)

    // جدول الإحصائيات اليومية
    await client.query(`
      CREATE TABLE IF NOT EXISTS whatsapp_daily_stats (
        id SERIAL PRIMARY KEY,
        company_id INTEGER REFERENCES companies(id) ON DELETE CASCADE,
        stat_date DATE NOT NULL,
        messages_sent INTEGER DEFAULT 0,
        messages_delivered INTEGER DEFAULT 0,
        messages_read INTEGER DEFAULT 0,
        messages_failed INTEGER DEFAULT 0,
        messages_received INTEGER DEFAULT 0,
        unique_contacts INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT unique_company_date UNIQUE (company_id, stat_date)
      )
    `)

    // 9. التحقق من النتائج
    const finalCompanyData = await client.query(`
      SELECT
        id, name, whatsapp_phone, whatsapp_business_name, whatsapp_enabled
      FROM companies
      WHERE is_active = true
      LIMIT 1
    `)

    const contactsCount = await client.query(`
      SELECT
        contact_type,
        COUNT(*) as count
      FROM whatsapp_contacts
      WHERE company_id = $1
      GROUP BY contact_type
    `, [companyId])

    const settingsCount = await client.query(`
      SELECT COUNT(*) as count
      FROM whatsapp_company_settings
      WHERE company_id = $1
    `, [companyId])

    console.log('\n✅ تم إعداد WhatsApp في قاعدة البيانات mohammi بنجاح!')
    console.log('📊 النتائج:')

    if (finalCompanyData.rows.length > 0) {
      const company = finalCompanyData.rows[0]
      console.log(`   🏢 الشركة: ${company.name}`)
      console.log(`   📱 رقم WhatsApp: ${company.whatsapp_phone}`)
      console.log(`   🔧 WhatsApp مفعل: ${company.whatsapp_enabled ? 'نعم' : 'لا'}`)
    }

    console.log(`   ⚙️ عدد الإعدادات: ${settingsCount.rows[0]?.count || 0}`)
    console.log('   👥 جهات الاتصال:')

    contactsCount.rows.forEach(row => {
      const type = row.contact_type === 'client' ? 'العملاء' : 'الموظفين'
      console.log(`      - ${type}: ${row.count}`)
    })

    console.log('\n🎉 يمكنك الآن الذهاب إلى http://localhost:3000/settings/whatsapp')
    console.log('   وستجد أن جميع الإعدادات مفعلة مع بيانات الشركة الحقيقية!')

  } catch (error) {
    console.error('❌ خطأ في إعداد WhatsApp:', error)
    throw error
  } finally {
    await client.end()
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات')
  }
}

// تشغيل السكريبت
if (require.main === module) {
  setupWhatsAppMohammi()
    .then(() => {
      console.log('\n🎯 تم الانتهاء من إعداد WhatsApp في قاعدة البيانات mohammi!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('\n💥 فشل في إعداد WhatsApp:', error.message)
      process.exit(1)
    })
}

module.exports = { setupWhatsAppMohammi }
