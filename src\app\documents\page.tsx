'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import MainLayout from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { toast } from 'react-hot-toast'
import {
  FileText,
  Upload,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  FolderOpen,
  Calendar,
  User,
  File,
  Grid,
  List,
  Plus,
  RefreshCw
} from 'lucide-react'

interface Document {
  id: number
  title: string
  description: string
  file_name: string
  file_path: string
  file_size: number
  file_type: string
  mime_type: string
  category: string
  subcategory: string
  tags: string[]
  access_level: string
  is_confidential: boolean
  created_date: string
  updated_at: string
  uploaded_by: number
  is_active: boolean
}

interface DocumentStats {
  totalCount: number
  categories: { category: string; count: number }[]
}

export default function DocumentsPage() {
  const router = useRouter()
  const [documents, setDocuments] = useState<Document[]>([])
  const [stats, setStats] = useState<DocumentStats>({ totalCount: 0, categories: [] })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [currentPage] = useState(1)

  // جلب الوثائق
  const fetchDocuments = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20'
      })

      if (searchTerm) params.append('search', searchTerm)
      if (selectedCategory) params.append('category', selectedCategory)

      const response = await fetch(`/api/documents?${params}`)
      const data = await response.json()

      if (data.success) {
        setDocuments(data.data.documents || data.data || [])
        setStats(data.data.stats || data.stats || { totalCount: 0, categories: [] })
      } else {
        toast.error(data.error || 'فشل في جلب الوثائق')
      }
    } catch (error) {
      console.error('خطأ في جلب الوثائق:', error)
      toast.error('حدث خطأ في جلب الوثائق')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchDocuments()
  }, [currentPage, searchTerm, selectedCategory])

  // تنسيق التاريخ
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  // الحصول على لون الفئة
  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      'contract': 'bg-blue-100 text-blue-800',
      'evidence': 'bg-green-100 text-green-800',
      'correspondence': 'bg-yellow-100 text-yellow-800',
      'template': 'bg-purple-100 text-purple-800',
      'guide': 'bg-gray-100 text-gray-800',
      'general': 'bg-orange-100 text-orange-800'
    }
    return colors[category] || 'bg-gray-100 text-gray-800'
  }

  // الحصول على أيقونة نوع الملف
  const getFileIcon = (fileType: string) => {
    if (fileType?.includes('pdf')) return <FileText className="h-5 w-5 text-red-500" />
    if (fileType?.includes('word') || fileType?.includes('doc')) return <FileText className="h-5 w-5 text-blue-500" />
    if (fileType?.includes('image')) return <File className="h-5 w-5 text-green-500" />
    if (fileType?.includes('excel') || fileType?.includes('sheet')) return <FileText className="h-5 w-5 text-green-600" />
    return <File className="h-5 w-5 text-gray-500" />
  }

  // تنسيق حجم الملف
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 بايت'
    const k = 1024
    const sizes = ['بايت', 'ك.ب', 'م.ب', 'ج.ب']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // تحميل الملف
  const downloadDocument = async (documentId: number, fileName: string) => {
    try {
      const response = await fetch(`/api/documents/download/${documentId}`)
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = fileName
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        toast.success('تم تحميل الملف بنجاح')
      } else {
        toast.error('فشل في تحميل الملف')
      }
    } catch (error) {
      console.error('خطأ في تحميل الملف:', error)
      toast.error('حدث خطأ في تحميل الملف')
    }
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان والإحصائيات */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة الوثائق</h1>
            <p className="text-gray-600 mt-1">
              إجمالي الوثائق: {stats.totalCount}
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              onClick={() => router.push('/documents/upload')}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Upload className="h-4 w-4 ml-2" />
              رفع وثيقة جديدة
            </Button>
            <Button
              variant="outline"
              onClick={fetchDocuments}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 ml-2 ${loading ? 'animate-spin' : ''}`} />
              تحديث
            </Button>
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-blue-600" />
                <div className="mr-4">
                  <p className="text-2xl font-bold">{stats.totalCount}</p>
                  <p className="text-gray-600 text-sm">إجمالي الوثائق</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {stats.categories.slice(0, 3).map((cat, index) => (
            <Card key={cat.category}>
              <CardContent className="p-4">
                <div className="flex items-center">
                  <FolderOpen className="h-8 w-8 text-green-600" />
                  <div className="mr-4">
                    <p className="text-2xl font-bold">{cat.count}</p>
                    <p className="text-gray-600 text-sm">{cat.category}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* أدوات البحث والتصفية */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في الوثائق..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>
              
              <div className="md:w-48">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">جميع الفئات</option>
                  {stats.categories.map(cat => (
                    <option key={cat.category} value={cat.category}>
                      {cat.category} ({cat.count})
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* قائمة الوثائق */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 ml-2" />
              الوثائق
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">جاري تحميل الوثائق...</p>
              </div>
            ) : documents.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">لا توجد وثائق</p>
              </div>
            ) : (
              <div className="space-y-4">
                {documents.map((doc) => (
                  <div key={doc.id} className="border rounded-lg p-4 hover:bg-gray-50">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 space-x-reverse">
                        <div className="text-2xl">
                          {getFileIcon(doc.file_type)}
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900">{doc.title}</h3>
                          <p className="text-sm text-gray-600">{doc.description}</p>
                          <div className="flex items-center space-x-4 space-x-reverse mt-2">
                            <Badge className={getCategoryColor(doc.category)}>
                              {doc.category}
                            </Badge>
                            <span className="text-xs text-gray-500">
                              <Calendar className="h-3 w-3 inline ml-1" />
                              {formatDate(doc.created_at)}
                            </span>
                            <span className="text-xs text-gray-500">
                              {formatFileSize(doc.file_size)}
                            </span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => window.open(doc.file_path, '_blank')}
                          title="عرض الملف"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => downloadDocument(doc.id, doc.file_name)}
                          title="تحميل الملف"
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          title="تعديل الوثيقة"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-red-600 hover:text-red-700"
                          title="حذف الوثيقة"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>


      </div>
    </MainLayout>
  )
}
