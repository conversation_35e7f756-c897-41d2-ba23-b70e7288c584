import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database-router'

// GET - جلب جميع الإعدادات
export async function GET() {
  try {
    const result = await query(`
      SELECT 
        setting_key,
        setting_value,
        setting_type,
        description,
        is_editable,
        created_date,
        updated_date
      FROM system_settings 
      ORDER BY setting_key
    `)

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching settings:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الإعدادات' },
      { status: 500 }
    )
  }
}

// POST - إضافة إعداد جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { setting_key, setting_value, setting_type, description, is_editable } = body

    if (!setting_key || setting_value === undefined) {
      return NextResponse.json(
        { success: false, error: 'مفتاح الإعداد والقيمة مطلوبان' },
        { status: 400 }
      )
    }

    const result = await query(`
      INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_editable)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `, [setting_key, setting_value, setting_type || 'string', description || '', is_editable !== false])

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم إضافة الإعداد بنجاح'
    })
  } catch (error) {
    console.error('Error creating setting:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة الإعداد' },
      { status: 500 }
    )
  }
}

// PUT - تحديث إعداد موجود
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { setting_key, setting_value, setting_type, description } = body

    if (!setting_key || setting_value === undefined) {
      return NextResponse.json(
        { success: false, error: 'مفتاح الإعداد والقيمة مطلوبان' },
        { status: 400 }
      )
    }

    const result = await query(`
      UPDATE system_settings 
      SET setting_value = $2, setting_type = $3, description = $4, updated_date = CURRENT_TIMESTAMP
      WHERE setting_key = $1
      RETURNING *
    `, [setting_key, setting_value, setting_type || 'string', description || ''])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الإعداد غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم تحديث الإعداد بنجاح'
    })
  } catch (error) {
    console.error('Error updating setting:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الإعداد' },
      { status: 500 }
    )
  }
}

// DELETE - حذف إعداد
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const setting_key = searchParams.get('setting_key')

    if (!setting_key) {
      return NextResponse.json(
        { success: false, error: 'مفتاح الإعداد مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من أن الإعداد قابل للحذف
    const checkResult = await query(
      'SELECT is_editable FROM system_settings WHERE setting_key = $1',
      [setting_key]
    )

    if (checkResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الإعداد غير موجود' },
        { status: 404 }
      )
    }

    if (!checkResult.rows[0].is_editable) {
      return NextResponse.json(
        { success: false, error: 'هذا الإعداد غير قابل للحذف' },
        { status: 403 }
      )
    }

    await query('DELETE FROM system_settings WHERE setting_key = $1', [setting_key])

    return NextResponse.json({
      success: true,
      message: 'تم حذف الإعداد بنجاح'
    })
  } catch (error) {
    console.error('Error deleting setting:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الإعداد' },
      { status: 500 }
    )
  }
}
