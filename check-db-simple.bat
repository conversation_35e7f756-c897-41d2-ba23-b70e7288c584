@echo off
chcp 65001 >nul
echo ========================================
echo   Database Connection Check
echo ========================================
echo.

echo 1. Checking PostgreSQL services...
sc query | findstr /i postgres
echo.

echo 2. Checking port 5432...
netstat -an | findstr :5432
echo.

echo 3. Checking PostgreSQL processes...
tasklist | findstr /i postgres
echo.

echo 4. Checking common installation folders...
if exist "C:\Program Files\PostgreSQL" (
    echo Found PostgreSQL in: C:\Program Files\PostgreSQL
    dir "C:\Program Files\PostgreSQL" /b
) else (
    echo PostgreSQL not found in default location
)
echo.

echo 5. Running database connection test...
node quick-db-check.js
echo.

echo ========================================
echo   Solutions if PostgreSQL not found:
echo ========================================
echo 1. Download PostgreSQL: https://www.postgresql.org/download/
echo 2. Or install XAMPP with PostgreSQL
echo 3. Or use SQLite as alternative
echo.
pause
