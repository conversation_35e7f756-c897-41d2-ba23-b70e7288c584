// اختبار الخادم المتقدم
console.log('🚀 بدء اختبار الخادم المتقدم...');

const fs = require('fs');
const path = require('path');

try {
  // 1. فحص ملف التوجيه
  const configPath = path.join(__dirname, 'routing.config.json');
  console.log('📋 فحص ملف التوجيه:', configPath);
  
  if (!fs.existsSync(configPath)) {
    console.log('❌ ملف التوجيه غير موجود');
    process.exit(1);
  }
  
  const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
  console.log('✅ ملف التوجيه موجود');
  console.log('📊 المنافذ المحددة:', Object.keys(config.routes));
  
  // 2. فحص إعدادات SSL
  if (config.default_config && config.default_config.ssl) {
    const ssl = config.default_config.ssl;
    console.log('\n🔒 إعدادات SSL:');
    console.log('   - مفعل:', ssl.enabled);
    console.log('   - المفتاح الخاص:', ssl.key_path);
    console.log('   - الشهادة:', ssl.cert_path);
    
    // فحص وجود ملفات SSL
    if (ssl.enabled) {
      console.log('\n🔍 فحص ملفات SSL:');
      console.log('   - المفتاح:', fs.existsSync(ssl.key_path) ? '✅ موجود' : '❌ مفقود');
      console.log('   - الشهادة:', fs.existsSync(ssl.cert_path) ? '✅ موجود' : '❌ مفقود');
      if (ssl.ca_path) {
        console.log('   - CA:', fs.existsSync(ssl.ca_path) ? '✅ موجود' : '❌ مفقود');
      }
    }
  }
  
  // 3. فحص قواعد البيانات
  console.log('\n🗄️ قواعد البيانات:');
  Object.entries(config.routes).forEach(([port, route]) => {
    console.log(`   - المنفذ ${port}: قاعدة بيانات ${route.database}`);
  });
  
  // 4. محاولة تشغيل الخادم
  console.log('\n🔧 محاولة تحميل الخادم...');
  
  // تحميل الخادم بدون تشغيل
  const serverPath = path.join(__dirname, 'advanced-unified-server.js');
  if (!fs.existsSync(serverPath)) {
    console.log('❌ ملف الخادم غير موجود');
    process.exit(1);
  }
  
  console.log('✅ ملف الخادم موجود');
  console.log('📁 مجلد العمل:', __dirname);
  
  // 5. فحص المنافذ المطلوبة
  console.log('\n🌐 المنافذ المطلوبة:');
  console.log('   - للوصول المحلي: 3000, 3001 (Next.js)');
  console.log('   - للوصول الخارجي: 7443, 8914 (HTTPS)');
  console.log('   - الدومين: https://mohammi.com:7443');
  console.log('   - الدومين: https://mohammi.com:8914');
  
  console.log('\n✅ جميع الفحوصات اكتملت بنجاح');
  console.log('🚀 يمكن الآن تشغيل الخادم بأمان');
  
} catch (error) {
  console.error('❌ خطأ في الاختبار:', error.message);
  process.exit(1);
}
