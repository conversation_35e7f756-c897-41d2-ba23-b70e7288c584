/**
 * سكريپت تشفير كلمات المرور غير المشفرة
 */

const { Client } = require('pg')
const bcrypt = require('bcryptjs')

const dbConfig = {
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'mohammi'
}

async function encryptUnencryptedPasswords() {
  const client = new Client(dbConfig)
  
  try {
    await client.connect()
    console.log('🔗 تم الاتصال بقاعدة البيانات mohammi')

    // 1. جلب المستخدمين بكلمات مرور غير مشفرة
    console.log('\n🔍 البحث عن كلمات المرور غير المشفرة...')
    const usersResult = await client.query(`
      SELECT id, username, password_hash
      FROM users 
      WHERE password_hash IS NOT NULL 
      AND password_hash NOT LIKE '$2a$%' 
      AND password_hash NOT LIKE '$2b$%'
      ORDER BY id
    `)

    if (usersResult.rows.length === 0) {
      console.log('✅ جميع كلمات المرور مشفرة بالفعل!')
      return
    }

    console.log(`⚠️ تم العثور على ${usersResult.rows.length} مستخدم بكلمات مرور غير مشفرة:`)
    
    // 2. تشفير كل كلمة مرور
    for (const user of usersResult.rows) {
      console.log(`\n🔒 تشفير كلمة مرور المستخدم: ${user.username}`)
      console.log(`   📝 كلمة المرور الحالية: ${user.password_hash}`)
      
      // تشفير كلمة المرور باستخدام bcrypt
      const hashedPassword = await bcrypt.hash(user.password_hash, 12)
      console.log(`   🔐 كلمة المرور المشفرة: ${hashedPassword.substring(0, 30)}...`)
      
      // تحديث قاعدة البيانات
      await client.query(`
        UPDATE users 
        SET password_hash = $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
      `, [hashedPassword, user.id])
      
      console.log(`   ✅ تم تحديث كلمة مرور ${user.username} في قاعدة البيانات`)
    }

    console.log('\n🎉 تم تشفير جميع كلمات المرور بنجاح!')

    // 3. التحقق من النتائج
    console.log('\n🔍 التحقق من النتائج...')
    const verifyResult = await client.query(`
      SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN password_hash LIKE '$2%' THEN 1 END) as encrypted_count,
        COUNT(CASE WHEN password_hash NOT LIKE '$2%' AND password_hash IS NOT NULL THEN 1 END) as unencrypted_count
      FROM users
    `)

    const stats = verifyResult.rows[0]
    console.log(`📊 إحصائيات النهائية:`)
    console.log(`   👥 إجمالي المستخدمين: ${stats.total_users}`)
    console.log(`   🔒 كلمات مرور مشفرة: ${stats.encrypted_count}`)
    console.log(`   ⚠️ كلمات مرور غير مشفرة: ${stats.unencrypted_count}`)

    if (stats.unencrypted_count === '0') {
      console.log('\n✅ جميع كلمات المرور مشفرة الآن!')
    } else {
      console.log('\n⚠️ لا تزال هناك كلمات مرور غير مشفرة')
    }

  } catch (error) {
    console.error('❌ خطأ في تشفير كلمات المرور:', error)
    throw error
  } finally {
    await client.end()
    console.log('\n🔌 تم قطع الاتصال بقاعدة البيانات')
  }
}

// تشغيل التشفير
if (require.main === module) {
  encryptUnencryptedPasswords()
    .then(() => {
      console.log('\n🎯 انتهى تشفير كلمات المرور')
      process.exit(0)
    })
    .catch((error) => {
      console.error('\n💥 فشل في تشفير كلمات المرور:', error.message)
      process.exit(1)
    })
}

module.exports = { encryptUnencryptedPasswords }
