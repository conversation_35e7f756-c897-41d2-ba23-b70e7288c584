import { NextRequest, NextResponse } from 'next/server'
import { query, getDatabaseName } from '@/lib/database-router'

// تقارير القضايا: ملخص أو تفصيلي حسب الفلاتر
// الفلاتر المدعومة عبر query params:
// - case_id?: number
// - client_id?: number
// - employee_id?: number  // اختياري إن كانت هناك علاقة توزيع قضايا على موظفين
// - date_from?: string (YYYY-MM-DD)
// - date_to?: string (YYYY-MM-DD)
// - summary?: 'true' | 'false' (افتراضي false)
// - date_field?: 'created' | 'hearing' (افتراضي 'created')
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const caseId = searchParams.get('case_id')
    const clientId = searchParams.get('client_id')
    const employeeId = searchParams.get('employee_id')
    const dateFrom = searchParams.get('date_from')
    const dateTo = searchParams.get('date_to')
    const summary = (searchParams.get('summary') || 'false') === 'true'
    const dateField = searchParams.get('date_field') === 'hearing' ? 'hearing' : 'created'

    // تحديد حقل التاريخ الذي سنفلتر عليه
    // نستخدم COALESCE لضمان وجود تاريخ حتى إن لم يوجد next_hearing
    const dateExpr = dateField === 'hearing'
      ? 'COALESCE(i.next_hearing, i.created_date)'
      : 'i.created_date'

    // بناء الاستعلام ديناميكياً
    const params: any[] = []
    let p = 1

    let baseSelect = `
      SELECT
        i.id,
        COALESCE(i.case_number, '') AS case_number,
        COALESCE(i.title, '') AS title,
        COALESCE(i.status, '') AS status,
        COALESCE(i.case_amount, 0) AS case_amount,
        i.currency_id,
        i.created_date,
        i.next_hearing,
        c.id as client_id,
        COALESCE(c.name, '') AS client_name,
        COALESCE(it.name, '') AS issue_type,
        COALESCE(ct.name, '') AS court_name
      FROM issues i
      LEFT JOIN clients c ON i.client_id = c.id
      LEFT JOIN issue_types it ON i.issue_type_id = it.id
      LEFT JOIN courts ct ON i.court_id = ct.id
      WHERE 1=1
    `

    if (caseId) {
      baseSelect += ` AND i.id = $${p++}`
      params.push(parseInt(caseId))
    }
    if (clientId) {
      baseSelect += ` AND i.client_id = $${p++}`
      params.push(parseInt(clientId))
    }

    // employeeId اختياري: في حال وجود جدول توزيع القضايا على موظفين
    if (employeeId) {
      baseSelect += ` AND EXISTS (
        SELECT 1 FROM case_distributions cd
        WHERE cd.issue_id = i.id AND cd.employee_id = $${p}
      )`
      params.push(parseInt(employeeId)); p++
    }

    if (dateFrom) {
      baseSelect += ` AND ${dateExpr} >= $${p++}`
      params.push(dateFrom)
    }
    if (dateTo) {
      baseSelect += ` AND ${dateExpr} <= $${p++}`
      params.push(dateTo)
    }

    baseSelect += ` ORDER BY ${dateExpr} DESC, i.id DESC`

    const result = await query(baseSelect, params)
    const rows = result.rows || []

    if (summary) {
      // ملخص: عدد القضايا والحالة والإجمالي حسب العميل/المحكمة/النوع
      const summarySql = `
        SELECT
          COUNT(*) as total_issues,
          COALESCE(SUM(COALESCE(i.case_amount, 0)), 0) as total_amount,
          COUNT(CASE WHEN i.status = 'new' THEN 1 END) as new_count,
          COUNT(CASE WHEN i.status = 'in_progress' THEN 1 END) as in_progress_count,
          COUNT(CASE WHEN i.status = 'closed' THEN 1 END) as closed_count
        FROM issues i
        LEFT JOIN clients c ON i.client_id = c.id
        LEFT JOIN issue_types it ON i.issue_type_id = it.id
        LEFT JOIN courts ct ON i.court_id = ct.id
        WHERE 1=1
          ${caseId ? ' AND i.id = $1' : ''}
          ${clientId ? (caseId ? ' AND i.client_id = $2' : ' AND i.client_id = $1') : ''}
      `
      // لصعوبة إعادة استخدام نفس params ديناميكياً هنا دون تعقيد، سنعيد حساب المعاملات الأساسية للملخص فقط لأكثر الحالات شيوعاً (case_id/client_id)
      const summaryParams: any[] = []
      if (caseId) summaryParams.push(parseInt(caseId))
      if (clientId) summaryParams.push(parseInt(clientId))

      const summaryRes = await query(summarySql, summaryParams)
      return NextResponse.json({
        success: true,
        filter: { case_id: caseId, client_id: clientId, employee_id: employeeId, date_from: dateFrom, date_to: dateTo, date_field: dateField },
        summary: summaryRes.rows[0] || { total_issues: 0, total_amount: 0, new_count: 0, in_progress_count: 0, closed_count: 0 },
        data: rows,
        total: rows.length
      })
    }

    return NextResponse.json({
      success: true,
      filter: { case_id: caseId, client_id: clientId, employee_id: employeeId, date_from: dateFrom, date_to: dateTo, date_field: dateField },
      data: rows,
      total: rows.length
    })
  } catch (error: any) {
    console.error('❌ تقارير القضايا - خطأ:', error)
    return NextResponse.json({ success: false, error: 'فشل في جلب تقرير القضايا', details: error?.message || 'خطأ غير معروف' }, { status: 500 })
  }
}
