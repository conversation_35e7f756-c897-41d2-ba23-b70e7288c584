import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

function getConfigPath() {
  return path.resolve(process.cwd(), 'production', 'routing.config.json');
}

function isValidPort(p: any) {
  const n = Number(p);
  return Number.isInteger(n) && n > 0 && n < 65536;
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { routes, default_config } = body || {};

    if (!routes || typeof routes !== 'object') {
      return NextResponse.json({ ok: false, error: 'routes object is required' }, { status: 400 });
    }

    // Basic validation
    for (const [port, route] of Object.entries<any>(routes)) {
      if (!isValidPort(port)) {
        return NextResponse.json({ ok: false, error: `Invalid port: ${port}` }, { status: 400 });
      }
      if (!route?.database) {
        return NextResponse.json({ ok: false, error: `Missing database for port ${port}` }, { status: 400 });
      }
      if (route?.theme_color && !/^#([0-9a-fA-F]{3}){1,2}$/.test(route.theme_color)) {
        return NextResponse.json({ ok: false, error: `Invalid theme_color for port ${port}` }, { status: 400 });
      }
      if (route?.enabled != null && typeof route.enabled !== 'boolean') {
        return NextResponse.json({ ok: false, error: `enabled must be boolean for port ${port}` }, { status: 400 });
      }
    }

    const configPath = getConfigPath();
    if (!fs.existsSync(path.dirname(configPath))) {
      return NextResponse.json({ ok: false, error: 'production directory not found' }, { status: 500 });
    }

    // Read current for backup
    const now = new Date();
    const stamp = now.toISOString().replace(/[:.]/g, '-');
    let currentRaw = '{}';
    if (fs.existsSync(configPath)) {
      currentRaw = fs.readFileSync(configPath, 'utf8');
      const backupPath = path.resolve(path.dirname(configPath), `routing.config.backup-${stamp}.json`);
      fs.writeFileSync(backupPath, currentRaw, 'utf8');
    }

    // Prepare new config
    const newConfig = {
      routes,
      ...(default_config ? { default_config } : JSON.parse(currentRaw).default_config ? { default_config: JSON.parse(currentRaw).default_config } : {}),
    };

    fs.writeFileSync(configPath, JSON.stringify(newConfig, null, 2), 'utf8');

    return NextResponse.json({ ok: true, path: configPath });
  } catch (error: any) {
    return NextResponse.json({ ok: false, error: error?.message || 'Unknown error' }, { status: 500 });
  }
}
