/**
 * سكريبت لإعداد حسابات الموظفين في دليل الحسابات
 * وتحديث الموظفين الموجودين بأرقام الحسابات الصحيحة
 */

const { Client } = require('pg');

// إعدادات قاعدة البيانات mohammidev
const dbConfig = {
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'mohammidev'
};

async function setupEmployeeAccounts() {
  const client = new Client(dbConfig);

  try {
    await client.connect();
    console.log('✅ متصل بقاعدة البيانات mohammidev');

    // 1. التحقق من وجود جدول دليل الحسابات
    console.log('\n🔍 فحص جدول دليل الحسابات...');

    try {
      const chartCheck = await client.query(`
        SELECT COUNT(*) as count
        FROM information_schema.tables
        WHERE table_name = 'chart_of_accounts' AND table_schema = 'public'
      `);

      if (parseInt(chartCheck.rows[0].count) === 0) {
        console.log('📋 إنشاء جدول دليل الحسابات...');
        await client.query(`
          CREATE TABLE chart_of_accounts (
            id SERIAL PRIMARY KEY,
            account_code VARCHAR(20) UNIQUE NOT NULL,
            account_name VARCHAR(200) NOT NULL,
            account_type VARCHAR(50) NOT NULL,
            parent_id INTEGER,
            is_active BOOLEAN DEFAULT TRUE,
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
          )
        `);
        console.log('✅ تم إنشاء جدول دليل الحسابات');
      } else {
        console.log('✅ جدول دليل الحسابات موجود');
      }

      // فحص الحسابات الموجودة
      const existingAccounts = await client.query('SELECT id, account_code, account_name FROM chart_of_accounts ORDER BY id');
      console.log(`📊 عدد الحسابات الموجودة: ${existingAccounts.rows.length}`);

    } catch (error) {
      console.log('📋 إنشاء جدول دليل الحسابات...');
      await client.query(`
        CREATE TABLE chart_of_accounts (
          id SERIAL PRIMARY KEY,
          account_code VARCHAR(20) UNIQUE NOT NULL,
          account_name VARCHAR(200) NOT NULL,
          account_type VARCHAR(50) NOT NULL,
          parent_id INTEGER,
          is_active BOOLEAN DEFAULT TRUE,
          created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
      console.log('✅ تم إنشاء جدول دليل الحسابات');
    }

    // 2. إنشاء حسابات الموظفين الأساسية
    console.log('\n💰 إنشاء حسابات الموظفين في دليل الحسابات...');

    const employeeAccounts = [
      // حسابات الرواتب والأجور
      { id: 5100, code: '5100', name: 'رواتب وأجور الموظفين', type: 'مصاريف' },
      { id: 5101, code: '5101', name: 'رواتب المحامين', type: 'مصاريف' },
      { id: 5102, code: '5102', name: 'رواتب الموظفين الإداريين', type: 'مصاريف' },
      { id: 5103, code: '5103', name: 'رواتب المحاسبين', type: 'مصاريف' },
      { id: 5104, code: '5104', name: 'رواتب السكرتارية', type: 'مصاريف' },
      { id: 5105, code: '5105', name: 'رواتب المساعدين القانونيين', type: 'مصاريف' },

      // حسابات المكافآت والبدلات
      { id: 5110, code: '5110', name: 'مكافآت الموظفين', type: 'مصاريف' },
      { id: 5111, code: '5111', name: 'بدل مواصلات', type: 'مصاريف' },
      { id: 5112, code: '5112', name: 'بدل طعام', type: 'مصاريف' },
      { id: 5113, code: '5113', name: 'بدل سكن', type: 'مصاريف' },
      { id: 5114, code: '5114', name: 'بدل اتصالات', type: 'مصاريف' },
      { id: 5115, code: '5115', name: 'مكافآت أداء', type: 'مصاريف' },

      // حسابات التأمينات والاستقطاعات
      { id: 5120, code: '5120', name: 'تأمينات اجتماعية', type: 'مصاريف' },
      { id: 5121, code: '5121', name: 'تأمين صحي', type: 'مصاريف' },
      { id: 5122, code: '5122', name: 'صندوق التقاعد', type: 'مصاريف' },

      // حسابات فردية للموظفين (للمتابعة التفصيلية)
      { id: 5200, code: '5200', name: 'حسابات الموظفين الفردية', type: 'مصاريف' },
      { id: 5201, code: '5201', name: 'حساب موظف رقم 1', type: 'مصاريف' },
      { id: 5202, code: '5202', name: 'حساب موظف رقم 2', type: 'مصاريف' },
      { id: 5203, code: '5203', name: 'حساب موظف رقم 3', type: 'مصاريف' },
      { id: 5204, code: '5204', name: 'حساب موظف رقم 4', type: 'مصاريف' },
      { id: 5205, code: '5205', name: 'حساب موظف رقم 5', type: 'مصاريف' },
      { id: 5206, code: '5206', name: 'حساب موظف رقم 6', type: 'مصاريف' },
      { id: 5207, code: '5207', name: 'حساب موظف رقم 7', type: 'مصاريف' },
      { id: 5208, code: '5208', name: 'حساب موظف رقم 8', type: 'مصاريف' },
      { id: 5209, code: '5209', name: 'حساب موظف رقم 9', type: 'مصاريف' },
      { id: 5210, code: '5210', name: 'حساب موظف رقم 10', type: 'مصاريف' },
      { id: 5211, code: '5211', name: 'حساب موظف رقم 11', type: 'مصاريف' },
      { id: 5212, code: '5212', name: 'حساب موظف رقم 12', type: 'مصاريف' },
      { id: 5213, code: '5213', name: 'حساب موظف رقم 13', type: 'مصاريف' },
      { id: 5214, code: '5214', name: 'حساب موظف رقم 14', type: 'مصاريف' },
      { id: 5215, code: '5215', name: 'حساب موظف رقم 15', type: 'مصاريف' },
      { id: 5216, code: '5216', name: 'حساب موظف رقم 16', type: 'مصاريف' },
      { id: 5217, code: '5217', name: 'حساب موظف رقم 17', type: 'مصاريف' },
      { id: 5218, code: '5218', name: 'حساب موظف رقم 18', type: 'مصاريف' },
      { id: 5219, code: '5219', name: 'حساب موظف رقم 19', type: 'مصاريف' },
      { id: 5220, code: '5220', name: 'حساب موظف رقم 20', type: 'مصاريف' }
    ];

    for (const account of employeeAccounts) {
      try {
        await client.query(`
          INSERT INTO chart_of_accounts (id, account_code, account_name, account_type, account_level, is_active, allow_transactions)
          VALUES ($1, $2, $3, $4, $5, true, true)
          ON CONFLICT (id) DO UPDATE SET
            account_code = EXCLUDED.account_code,
            account_name = EXCLUDED.account_name,
            account_type = EXCLUDED.account_type,
            account_level = EXCLUDED.account_level
        `, [account.id, account.code, account.name, account.type, 4]); // مستوى 4 للحسابات التفصيلية

        console.log(`   ✅ ${account.code}: ${account.name}`);
      } catch (error) {
        if (error.code !== '23505') { // تجاهل خطأ التكرار
          console.log(`   ⚠️ خطأ في إضافة ${account.code}: ${error.message}`);
        }
      }
    }

    // 3. فحص الموظفين الموجودين
    console.log('\n👥 فحص الموظفين الموجودين...');
    const employees = await client.query(`
      SELECT id, name, employee_number, account_id, position, department_id
      FROM employees
      ORDER BY id
    `);

    console.log(`📊 عدد الموظفين: ${employees.rows.length}`);

    // 4. تحديث أرقام الموظفين والحسابات
    console.log('\n🔄 تحديث بيانات الموظفين...');

    for (const employee of employees.rows) {
      let updates = [];
      let values = [employee.id];
      let paramIndex = 2;

      // تحديث رقم الموظف إذا كان مفقوداً
      if (!employee.employee_number) {
        const positionCode = getPositionCode(employee.position);
        const employeeNumber = `${positionCode}${String(employee.id).padStart(3, '0')}`;
        updates.push(`employee_number = $${paramIndex}`);
        values.push(employeeNumber);
        paramIndex++;
        console.log(`   📝 رقم موظف جديد للموظف ${employee.name}: ${employeeNumber}`);
      }

      // تحديث رقم الحساب إذا كان مفقوداً أو غير صحيح
      if (!employee.account_id) {
        // استخدام حساب فردي من الحسابات المتوفرة (5201-5220)
        let accountId;
        if (employee.id <= 20) {
          accountId = 5200 + employee.id; // 5201-5220
        } else {
          // للموظفين الإضافيين، استخدم حساب عام
          accountId = 5101; // رواتب المحامين كحساب افتراضي
        }
        updates.push(`account_id = $${paramIndex}`);
        values.push(accountId);
        paramIndex++;
        console.log(`   💰 رقم حساب جديد للموظف ${employee.name}: ${accountId}`);
      }

      // تنفيذ التحديث إذا كان هناك تغييرات
      if (updates.length > 0) {
        const updateQuery = `UPDATE employees SET ${updates.join(', ')} WHERE id = $1`;
        await client.query(updateQuery, values);
        console.log(`   ✅ تم تحديث بيانات الموظف: ${employee.name}`);
      }
    }

    // 5. التحقق النهائي
    console.log('\n🔍 التحقق النهائي...');
    const finalCheck = await client.query(`
      SELECT
        e.id,
        e.name,
        e.employee_number,
        e.account_id,
        c.account_name
      FROM employees e
      LEFT JOIN chart_of_accounts c ON e.account_id = c.id
      ORDER BY e.id
    `);

    console.log('\n📋 بيانات الموظفين النهائية:');
    finalCheck.rows.forEach(emp => {
      console.log(`   - ${emp.name}: رقم=${emp.employee_number}, حساب=${emp.account_id} (${emp.account_name || 'غير موجود'})`);
    });

    // 6. إحصائيات نهائية
    const stats = await client.query(`
      SELECT
        COUNT(*) as total_employees,
        COUNT(employee_number) as employees_with_number,
        COUNT(account_id) as employees_with_account,
        COUNT(CASE WHEN c.id IS NOT NULL THEN 1 END) as valid_accounts
      FROM employees e
      LEFT JOIN chart_of_accounts c ON e.account_id = c.id
    `);

    const stat = stats.rows[0];
    console.log('\n📊 إحصائيات نهائية:');
    console.log(`   - إجمالي الموظفين: ${stat.total_employees}`);
    console.log(`   - موظفين لديهم رقم موظف: ${stat.employees_with_number}`);
    console.log(`   - موظفين لديهم رقم حساب: ${stat.employees_with_account}`);
    console.log(`   - حسابات صحيحة في دليل الحسابات: ${stat.valid_accounts}`);

    console.log('\n🎉 تم إعداد حسابات الموظفين بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في إعداد حسابات الموظفين:', error.message);
    throw error;
  } finally {
    await client.end();
  }
}

/**
 * دالة لإنشاء رمز المنصب
 */
function getPositionCode(position) {
  const positionCodes = {
    'محامي رسمي': 'LAW',
    'محامي متدرب': 'TRN',
    'مساعد قانوني': 'AST',
    'سكرتير': 'SEC',
    'محاسب': 'ACC',
    'مدير': 'MGR',
    'موظف إداري': 'ADM',
    'مستشار قانوني': 'CON',
    'رئيس قسم': 'HOD',
    'مدير عام': 'GM'
  };

  return positionCodes[position] || 'EMP';
}

// تشغيل السكريبت
if (require.main === module) {
  setupEmployeeAccounts().catch(console.error);
}

module.exports = { setupEmployeeAccounts, getPositionCode };
