/**
 * سكريبت لبدء تشغيل جدولة إشعارات WhatsApp
 * يجب تشغيله عند بدء تشغيل الخادم
 */

import { startWhatsAppScheduler } from '../lib/whatsapp-scheduler'
import { companyWhatsAppService } from '../lib/company-whatsapp-service'

async function initializeWhatsAppServices() {
  try {
    console.log('🚀 بدء تهيئة خدمات WhatsApp...')
    
    // بدء تشغيل خدمة WhatsApp للشركة
    console.log('📱 بدء تشغيل خدمة WhatsApp...')
    await companyWhatsAppService.start()
    
    // انتظار حتى يصبح WhatsApp جاهز
    let attempts = 0
    const maxAttempts = 30 // 5 دقائق
    
    while (!companyWhatsAppService.getStatus().isReady && attempts < maxAttempts) {
      console.log(`⏳ انتظار اتصال WhatsApp... (${attempts + 1}/${maxAttempts})`)
      await new Promise(resolve => setTimeout(resolve, 10000)) // انتظار 10 ثوان
      attempts++
    }
    
    if (companyWhatsAppService.getStatus().isReady) {
      console.log('✅ WhatsApp متصل وجاهز!')
      
      // بدء تشغيل جدولة الإشعارات
      console.log('⏰ بدء تشغيل جدولة الإشعارات...')
      await startWhatsAppScheduler()
      
      console.log('🎉 تم تهيئة جميع خدمات WhatsApp بنجاح!')
    } else {
      console.warn('⚠️ لم يتم الاتصال بـ WhatsApp. يرجى مسح QR Code من الواجهة.')
      
      // بدء الجدولة حتى لو لم يتم الاتصال (ستعمل عند الاتصال)
      await startWhatsAppScheduler()
    }
    
  } catch (error) {
    console.error('❌ خطأ في تهيئة خدمات WhatsApp:', error)
  }
}

// تشغيل التهيئة
initializeWhatsAppServices()

// معالجة إيقاف التطبيق بشكل صحيح
process.on('SIGINT', async () => {
  console.log('🛑 إيقاف خدمات WhatsApp...')
  
  try {
    const { stopWhatsAppScheduler } = await import('../lib/whatsapp-scheduler')
    stopWhatsAppScheduler()
    
    await companyWhatsAppService.stop()
    console.log('✅ تم إيقاف جميع خدمات WhatsApp بنجاح')
  } catch (error) {
    console.error('❌ خطأ في إيقاف الخدمات:', error)
  }
  
  process.exit(0)
})

process.on('SIGTERM', async () => {
  console.log('🛑 إيقاف خدمات WhatsApp (SIGTERM)...')
  
  try {
    const { stopWhatsAppScheduler } = await import('../lib/whatsapp-scheduler')
    stopWhatsAppScheduler()
    
    await companyWhatsAppService.stop()
    console.log('✅ تم إيقاف جميع خدمات WhatsApp بنجاح')
  } catch (error) {
    console.error('❌ خطأ في إيقاف الخدمات:', error)
  }
  
  process.exit(0)
})
