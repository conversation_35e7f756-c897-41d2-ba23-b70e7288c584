import { NextRequest, NextResponse } from 'next/server'
import { openDb } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    
    
    const db = await openDb()
    
    // جلب خدمات النظام الداخلي
    const services = await db.all(`
      SELECT
        s.id,
        s.name,
        s.lineage_id,
        s.created_date,
        l.name as lineage_name
      FROM services s
      LEFT JOIN lineages l ON s.lineage_id = l.id
      ORDER BY s.name ASC
    `)

    

    db.close()

    return NextResponse.json({
      success: true,
      data: services,
      count: services.length
    })

  } catch (error) {
    console.error('❌ خطأ في جلب خدمات النظام الداخلي:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'فشل في جلب خدمات النظام الداخلي',
        details: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    
    
    const body = await request.json()
    const { name, lineage_id } = body

    if (!name) {
      return NextResponse.json(
        { success: false, error: 'اسم الخدمة مطلوب' },
        { status: 400 }
      )
    }

    const db = await openDb()

    // إضافة الخدمة الجديدة
    const result = await db.run(`
      INSERT INTO services (name, lineage_id) 
      VALUES (?, ?)
    `, [name, lineage_id || null])

    

    db.close()

    return NextResponse.json({
      success: true,
      message: 'تم إضافة الخدمة بنجاح',
      data: { id: result.lastID, name }
    })

  } catch (error) {
    console.error('❌ خطأ في إضافة الخدمة الداخلية:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'فشل في إضافة الخدمة الداخلية',
        details: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    )
  }
}
