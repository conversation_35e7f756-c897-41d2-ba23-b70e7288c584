// تحديث بسيط وآمن لقاعدة البيانات
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function updateDatabase() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔄 جاري الاتصال بقاعدة البيانات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 1. إنشاء جدول النسب المالية إذا لم يكن موجوداً
    console.log('🔄 جاري التحقق من جدول النسب المالية...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS lineages (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        admin_percentage DECIMAL(5,2) DEFAULT 0,
        description TEXT,
        is_active BOOLEAN DEFAULT true,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 2. إنشاء جدول الخدمات إذا لم يكن موجوداً
    console.log('🔄 جاري التحقق من جدول الخدمات...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS services (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        price DECIMAL(10,2) DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 3. إنشاء جدول توزيع القضايا إذا لم يكن موجوداً
    console.log('🔄 جاري التحقق من جدول توزيع القضايا...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS case_distribution (
        id SERIAL PRIMARY KEY,
        issue_id INTEGER,
        lineage_id INTEGER,
        percentage DECIMAL(5,2) DEFAULT 0,
        amount DECIMAL(10,2) DEFAULT 0,
        notes TEXT,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 4. إنشاء جدول تفاصيل توزيع الخدمات إذا لم يكن موجوداً
    console.log('🔄 جاري التحقق من جدول تفاصيل توزيع الخدمات...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS service_distributions (
        id SERIAL PRIMARY KEY,
        service_id INTEGER,
        lineage_id INTEGER,
        percentage DECIMAL(5,2) DEFAULT 0,
        amount DECIMAL(10,2) DEFAULT 0,
        notes TEXT,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 5. إدراج بيانات تجريبية للنسب المالية
    console.log('🔄 جاري إدراج بيانات النسب المالية...');
    const lineagesData = [
      { name: 'نسبة الإدارة', admin_percentage: 30.00, description: 'نسبة الإدارة من الأتعاب' },
      { name: 'نسبة المحكمة', admin_percentage: 20.00, description: 'نسبة رسوم المحكمة' },
      { name: 'نسبة العمولة', admin_percentage: 15.00, description: 'نسبة العمولة للوسطاء' },
      { name: 'نسبة أخرى', admin_percentage: 10.00, description: 'نسب أخرى متنوعة' }
    ];

    for (const lineage of lineagesData) {
      await client.query(`
        INSERT INTO lineages (name, admin_percentage, description)
        SELECT $1, $2, $3
        WHERE NOT EXISTS (SELECT 1 FROM lineages WHERE name = $1)
      `, [lineage.name, lineage.admin_percentage, lineage.description]);
    }

    // 6. إدراج بيانات تجريبية للخدمات
    console.log('🔄 جاري إدراج بيانات الخدمات...');
    const servicesData = [
      { name: 'استشارة قانونية', description: 'استشارة قانونية عامة', price: 500.00 },
      { name: 'صياغة عقد', description: 'صياغة العقود القانونية', price: 1000.00 },
      { name: 'تمثيل قضائي', description: 'التمثيل أمام المحاكم', price: 2000.00 },
      { name: 'مراجعة قانونية', description: 'مراجعة الوثائق القانونية', price: 750.00 }
    ];

    for (const service of servicesData) {
      await client.query(`
        INSERT INTO services (name, description, price)
        SELECT $1, $2, $3
        WHERE NOT EXISTS (SELECT 1 FROM services WHERE name = $1)
      `, [service.name, service.description, service.price]);
    }

    // 7. التحقق من النتائج
    console.log('🔄 جاري التحقق من النتائج...');
    
    const results = await Promise.all([
      client.query('SELECT COUNT(*) FROM lineages'),
      client.query('SELECT COUNT(*) FROM services'),
      client.query('SELECT COUNT(*) FROM case_distribution'),
      client.query('SELECT COUNT(*) FROM service_distributions')
    ]);

    console.log('📊 ملخص الجداول:');
    console.log(`   - النسب المالية: ${results[0].rows[0].count} سجل`);
    console.log(`   - الخدمات: ${results[1].rows[0].count} سجل`);
    console.log(`   - توزيع القضايا: ${results[2].rows[0].count} سجل`);
    console.log(`   - تفاصيل توزيع الخدمات: ${results[3].rows[0].count} سجل`);

    console.log('✅ تم تحديث قاعدة البيانات بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في تحديث قاعدة البيانات:', error.message);
  } finally {
    await client.end();
    console.log('🔄 تم قطع الاتصال بقاعدة البيانات');
  }
}

updateDatabase();
