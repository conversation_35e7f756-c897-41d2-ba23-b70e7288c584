'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import {
  Scale,
  Users,
  FileText,
  Award,
  Shield,
  Building,
  Gavel,
  BookOpen,
  Download,
  MessageCircle,
  LogIn,
  ChevronRight,
  Phone,
  Mail,
  MapPin,
  Star,
  TrendingUp,
  CheckCircle,
  ArrowRight
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

interface CompanyData {
  id: number
  name: string
  logo_url?: string
  phone?: string
  email?: string
  address?: string
  description?: string
}

interface Announcement {
  id: number
  title: string
  content: string
  type: string
  is_active: boolean
}

interface Stats {
  lawyers: number
  cases: number
  clients: number
  completed_cases: number
  new_cases: number
  success_rate: number
}

export default function HomePage() {
  const [companyData, setCompanyData] = useState<CompanyData | null>(null)
  const [announcements, setAnnouncements] = useState<Announcement[]>([])
  const [stats, setStats] = useState<Stats>({
    lawyers: 25,
    cases: 1250,
    clients: 850,
    completed_cases: 980,
    new_cases: 45,
    success_rate: 95
  })
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchCompanyData()
    fetchAnnouncements()
  }, [])

  const fetchCompanyData = async () => {
    try {
      const response = await fetch('/api/company')
      const result = await response.json()
      if (result.success && result.data.length > 0) {
        setCompanyData(result.data[0])
      }
    } catch (error) {
      console.error('Error fetching company data:', error)
    }
  }

  const fetchAnnouncements = async () => {
    try {
      const response = await fetch('/api/settings/announcements')
      const result = await response.json()
      if (result.success) {
        // فلترة الإعلانات للزوار فقط (النوع visitor)
        const visitorAnnouncements = result.data
          .filter((ann: Announcement) => ann.type === 'visitor' && ann.is_active)
          .slice(0, 2) // أول إعلانين فقط
        setAnnouncements(visitorAnnouncements)
      }
    } catch (error) {
      console.error('Error fetching announcements:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const services = [
    { id: 1, title: 'تنفيذ الأحكام القضائية الدولية', icon: Scale, description: 'تنفيذ وتطبيق الأحكام القضائية على المستوى الدولي' },
    { id: 2, title: 'إدارة العقود والاتفاقيات', icon: FileText, description: 'صياغة ومراجعة وإدارة جميع أنواع العقود والاتفاقيات' },
    { id: 3, title: 'قانون الشركات التجارية في دبي', icon: Building, description: 'استشارات قانونية متخصصة في قوانين الشركات التجارية' },
    { id: 4, title: 'الامتثال واللوائح التنظيمية', icon: CheckCircle, description: 'ضمان الامتثال للقوانين واللوائح التنظيمية المحلية والدولية' },
    { id: 5, title: 'القوانين العمالية واستشارات التوظيف', icon: Users, description: 'استشارات شاملة في قوانين العمل وعلاقات التوظيف' },
    { id: 6, title: 'قوانين الأمن السيبراني وحماية البيانات', icon: Shield, description: 'حماية قانونية شاملة للبيانات والأمن السيبراني' },
    { id: 7, title: 'حماية الملكية الفكرية', icon: Award, description: 'حماية وتسجيل حقوق الملكية الفكرية والعلامات التجارية' },
    { id: 8, title: 'حل النزاعات والتحكيم', icon: Gavel, description: 'حل النزاعات بالطرق الودية والتحكيم التجاري' },
    { id: 9, title: 'قوانين الضرائب المصرية', icon: TrendingUp, description: 'استشارات ضريبية متخصصة وفقاً للقوانين المصرية' },
    { id: 10, title: 'الاستشارات الضريبية وإعادة الهيكلة المالية', icon: TrendingUp, description: 'إعادة هيكلة مالية واستشارات ضريبية متقدمة' },
    { id: 11, title: 'تأسيس الشركات', icon: Building, description: 'خدمات شاملة لتأسيس وتسجيل الشركات بجميع أنواعها' }
  ]

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm border-b sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {/* Logo */}
            <div className="flex items-center space-x-3 space-x-reverse">
              {companyData?.logo_url ? (
                <Image
                  src={companyData.logo_url}
                  alt="شعار الشركة"
                  width={60}
                  height={60}
                  className="rounded-lg"
                />
              ) : (
                <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                  <Scale className="h-6 w-6 text-white" />
                </div>
              )}
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  {companyData?.name || 'مؤسسة المحاماة والاستشارات القانونية'}
                </h1>
                <p className="text-sm text-gray-600">Excellence in Legal Services</p>
              </div>
            </div>

            {/* Navigation */}
            <nav className="hidden md:flex items-center space-x-8 space-x-reverse">
              <Link href="#home" className="text-gray-700 hover:text-blue-600 font-medium">الرئيسية</Link>
              <Link href="#services" className="text-gray-700 hover:text-blue-600 font-medium">خدماتنا</Link>
              <Link href="#about" className="text-gray-700 hover:text-blue-600 font-medium">من نحن</Link>
              <Link href="#library" className="text-gray-700 hover:text-blue-600 font-medium">المكتبة القانونية</Link>
              <Link href="#contact" className="text-gray-700 hover:text-blue-600 font-medium">اتصل بنا</Link>
            </nav>

            {/* Login Button */}
            <Link href="/dashboard">
              <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                <LogIn className="h-4 w-4 mr-2" />
                تسجيل الدخول
              </Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Announcements Banner */}
      {announcements.length > 0 && (
        <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-2">
          <div className="container mx-auto px-4">
            <div className="flex items-center justify-center space-x-8 space-x-reverse">
              {announcements.map((announcement, index) => (
                <div key={announcement.id} className="flex items-center">
                  <span className="text-sm font-medium">
                    إعلان {index + 1}: {announcement.title}
                  </span>
                  {index < announcements.length - 1 && (
                    <div className="w-px h-4 bg-blue-300 mx-4"></div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Hero Section */}
      <section id="home" className="relative bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 py-24 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Content */}
            <div className="text-white">
              <div className="inline-flex items-center bg-blue-600/20 backdrop-blur-sm border border-blue-400/30 rounded-full px-4 py-2 mb-6">
                <Scale className="h-4 w-4 mr-2 text-blue-300" />
                <span className="text-sm font-medium text-blue-100">مكتب محاماة معتمد</span>
              </div>

              <h1 className="text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                <span className="text-white">العدالة</span>
                <span className="text-blue-400 block">والاحترافية</span>
                <span className="text-white">في خدمة القانون</span>
              </h1>

              <p className="text-xl text-gray-300 mb-8 leading-relaxed max-w-lg">
                {companyData?.description?.substring(0, 150) ||
                  'نقدم خدمات قانونية شاملة ومتخصصة بأعلى معايير الجودة والاحترافية لضمان حماية حقوقك وتحقيق أهدافك القانونية'
                }...
              </p>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg font-semibold shadow-xl">
                  <MessageCircle className="h-5 w-5 mr-2" />
                  استشارة مجانية
                </Button>
                <Button size="lg" variant="outline" className="border-2 border-white/30 text-white hover:bg-white/10 backdrop-blur-sm px-8 py-4 text-lg font-semibold">
                  <Phone className="h-5 w-5 mr-2" />
                  {companyData?.phone || '+967-1-234567'}
                </Button>
              </div>

              {/* Trust Indicators */}
              <div className="flex items-center gap-6 mt-8 pt-8 border-t border-white/20">
                <div className="flex items-center text-white/80">
                  <CheckCircle className="h-5 w-5 mr-2 text-green-400" />
                  <span className="text-sm">مرخص رسمياً</span>
                </div>
                <div className="flex items-center text-white/80">
                  <Award className="h-5 w-5 mr-2 text-yellow-400" />
                  <span className="text-sm">خبرة +{new Date().getFullYear() - (companyData?.established_date ? new Date(companyData.established_date).getFullYear() : 2020)} سنوات</span>
                </div>
                <div className="flex items-center text-white/80">
                  <Star className="h-5 w-5 mr-2 text-yellow-400" />
                  <span className="text-sm">تقييم 5 نجوم</span>
                </div>
              </div>
            </div>

            {/* Visual Element */}
            <div className="relative">
              <div className="relative bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20 shadow-2xl">
                <div className="absolute -top-4 -right-4 w-24 h-24 bg-amber-600 rounded-2xl flex items-center justify-center shadow-xl">
                  <Scale className="h-12 w-12 text-white" />
                </div>

                <div className="mt-8 space-y-6">
                  <div className="flex items-center justify-between p-4 bg-white/10 rounded-xl backdrop-blur-sm">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-green-400 rounded-full mr-3"></div>
                      <span className="text-white font-medium">قضايا منجزة بنجاح</span>
                    </div>
                    <span className="text-2xl font-bold text-white">{stats.completed_cases}+</span>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-white/10 rounded-xl backdrop-blur-sm">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-blue-400 rounded-full mr-3"></div>
                      <span className="text-white font-medium">عملاء راضون</span>
                    </div>
                    <span className="text-2xl font-bold text-white">{stats.clients}+</span>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-white/10 rounded-xl backdrop-blur-sm">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-yellow-400 rounded-full mr-3"></div>
                      <span className="text-white font-medium">نسبة النجاح</span>
                    </div>
                    <span className="text-2xl font-bold text-white">{stats.success_rate}%</span>
                  </div>
                </div>
              </div>

              {/* Floating Elements */}
              <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full opacity-20 blur-xl"></div>
              <div className="absolute -top-6 -left-6 w-20 h-20 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full opacity-20 blur-xl"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-gradient-to-r from-gray-50 to-blue-50 relative overflow-hidden">
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="%23f1f5f9" fill-opacity="0.4"%3E%3Cpath d="M20 20c0 11.046-8.954 20-20 20v20h40V20H20z"/%3E%3C/g%3E%3C/svg%3E')] opacity-30"></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">أرقام تتحدث عن نفسها</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              إنجازاتنا وخبرتنا في أرقام تعكس التزامنا بتقديم أفضل الخدمات القانونية
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8">
            <div className="text-center group">
              <div className="relative">
                <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                  <Users className="h-10 w-10 text-white" />
                </div>
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                  <CheckCircle className="h-4 w-4 text-white" />
                </div>
              </div>
              <div className="text-4xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">{stats.lawyers}+</div>
              <div className="text-gray-600 font-medium">محامي متخصص</div>
              <div className="text-sm text-gray-500 mt-1">معتمد ومرخص</div>
            </div>

            <div className="text-center group">
              <div className="relative">
                <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                  <FileText className="h-10 w-10 text-white" />
                </div>
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                  <TrendingUp className="h-4 w-4 text-white" />
                </div>
              </div>
              <div className="text-4xl font-bold text-gray-900 mb-2 group-hover:text-green-600 transition-colors">{stats.cases}+</div>
              <div className="text-gray-600 font-medium">قضية</div>
              <div className="text-sm text-gray-500 mt-1">في جميع المجالات</div>
            </div>

            <div className="text-center group">
              <div className="relative">
                <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                  <Users className="h-10 w-10 text-white" />
                </div>
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center">
                  <Star className="h-4 w-4 text-white" />
                </div>
              </div>
              <div className="text-4xl font-bold text-gray-900 mb-2 group-hover:text-purple-600 transition-colors">{stats.clients}+</div>
              <div className="text-gray-600 font-medium">عميل راضٍ</div>
              <div className="text-sm text-gray-500 mt-1">ثقة متبادلة</div>
            </div>

            <div className="text-center group">
              <div className="relative">
                <div className="w-20 h-20 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                  <CheckCircle className="h-10 w-10 text-white" />
                </div>
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                  <Award className="h-4 w-4 text-white" />
                </div>
              </div>
              <div className="text-4xl font-bold text-gray-900 mb-2 group-hover:text-emerald-600 transition-colors">{stats.completed_cases}+</div>
              <div className="text-gray-600 font-medium">قضية منجزة</div>
              <div className="text-sm text-gray-500 mt-1">بنجاح تام</div>
            </div>

            <div className="text-center group">
              <div className="relative">
                <div className="w-20 h-20 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                  <TrendingUp className="h-10 w-10 text-white" />
                </div>
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                  <ArrowRight className="h-4 w-4 text-white" />
                </div>
              </div>
              <div className="text-4xl font-bold text-gray-900 mb-2 group-hover:text-orange-600 transition-colors">{stats.new_cases}+</div>
              <div className="text-gray-600 font-medium">قضية جديدة</div>
              <div className="text-sm text-gray-500 mt-1">هذا الشهر</div>
            </div>

            <div className="text-center group">
              <div className="relative">
                <div className="w-20 h-20 bg-gradient-to-br from-yellow-500 to-amber-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                  <Star className="h-10 w-10 text-white" />
                </div>
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                  <CheckCircle className="h-4 w-4 text-white" />
                </div>
              </div>
              <div className="text-4xl font-bold text-gray-900 mb-2 group-hover:text-yellow-600 transition-colors">{stats.success_rate}%</div>
              <div className="text-gray-600 font-medium">نسبة النجاح</div>
              <div className="text-sm text-gray-500 mt-1">معدل متميز</div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center mt-16">
            <div className="bg-white rounded-2xl shadow-xl p-8 max-w-2xl mx-auto border border-gray-100">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">هل تحتاج لاستشارة قانونية؟</h3>
              <p className="text-gray-600 mb-6">احصل على استشارة مجانية من فريقنا المتخصص</p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3">
                  <MessageCircle className="h-5 w-5 mr-2" />
                  استشارة فورية
                </Button>
                <Button size="lg" variant="outline" className="border-blue-600 text-blue-600 hover:bg-blue-50 px-8 py-3">
                  <Phone className="h-5 w-5 mr-2" />
                  احجز موعد
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-20 bg-white relative">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="inline-flex items-center bg-blue-50 border border-blue-200 rounded-full px-6 py-2 mb-6">
              <Scale className="h-4 w-4 mr-2 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">خدماتنا القانونية</span>
            </div>
            <h2 className="text-5xl font-bold text-gray-900 mb-6">أهم خدماتنا المتخصصة</h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              نقدم مجموعة شاملة من الخدمات القانونية المتخصصة بأعلى معايير الجودة والاحترافية
              لضمان حماية حقوقكم وتحقيق أهدافكم القانونية بأفضل الطرق الممكنة
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.slice(0, 6).map((service, index) => (
              <Card key={service.id} className="group hover:shadow-2xl transition-all duration-500 border-0 bg-gradient-to-br from-white to-gray-50 hover:from-blue-50 hover:to-white relative overflow-hidden">
                {/* Background Pattern */}
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-100 to-transparent rounded-full -translate-y-16 translate-x-16 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                <CardContent className="p-8 relative z-10">
                  <div className="flex items-start mb-6">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300">
                      <service.icon className="h-8 w-8 text-white" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center mb-2">
                        <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-800 border-0">
                          {String(index + 1).padStart(2, '0')}
                        </Badge>
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 group-hover:text-blue-900 transition-colors leading-tight">
                        {service.title}
                      </h3>
                    </div>
                  </div>

                  <p className="text-gray-600 leading-relaxed mb-6 group-hover:text-gray-700 transition-colors">
                    {service.description}
                  </p>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-sm text-blue-600 font-medium group-hover:text-blue-700 transition-colors">
                      <span>اعرف المزيد</span>
                      <ArrowRight className="h-4 w-4 mr-2 group-hover:translate-x-1 transition-transform" />
                    </div>
                    <div className="flex items-center space-x-1 space-x-reverse">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="h-3 w-3 text-yellow-400 fill-current" />
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Additional Services Preview */}
          <div className="mt-16 bg-gradient-to-r from-blue-600 to-blue-800 rounded-3xl p-8 text-white relative overflow-hidden">
            <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>

            <div className="relative z-10 grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              <div>
                <h3 className="text-3xl font-bold mb-4">خدمات قانونية إضافية</h3>
                <p className="text-blue-100 mb-6 leading-relaxed">
                  نقدم أيضاً خدمات متخصصة في {services.slice(6).map(s => s.title).join('، ')} وغيرها من المجالات القانونية المتنوعة
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600 bg-transparent">
                    عرض جميع الخدمات
                    <ArrowRight className="h-5 w-5 mr-2" />
                  </Button>
                  <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50">
                    <MessageCircle className="h-5 w-5 mr-2" />
                    استشارة مخصصة
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                {services.slice(6, 10).map((service, index) => (
                  <div key={service.id} className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                    <service.icon className="h-8 w-8 text-white mb-3" />
                    <h4 className="font-semibold text-white text-sm leading-tight">{service.title}</h4>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Legal Library Section */}
      <section id="library" className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="inline-flex items-center bg-green-50 border border-green-200 rounded-full px-6 py-2 mb-6">
              <BookOpen className="h-4 w-4 mr-2 text-green-600" />
              <span className="text-sm font-medium text-green-800">المكتبة القانونية</span>
            </div>
            <h2 className="text-5xl font-bold text-gray-900 mb-6">مكتبة قانونية شاملة</h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              مجموعة شاملة من القوانين والتشريعات والوثائق القانونية المهمة متاحة للتحميل والمراجعة
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Sample Legal Documents */}
            <Card className="group hover:shadow-xl transition-all duration-300 border-0 bg-white">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mr-4">
                    <FileText className="h-6 w-6 text-red-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">القانون المدني</h3>
                    <p className="text-sm text-gray-500">PDF • 2.5 MB</p>
                  </div>
                </div>
                <p className="text-gray-600 text-sm mb-4">
                  النص الكامل للقانون المدني مع التعديلات الأخيرة والشروحات القانونية
                </p>
                <Button size="sm" variant="outline" className="w-full group-hover:bg-red-50 group-hover:border-red-200">
                  <Download className="h-4 w-4 mr-2" />
                  تحميل الملف
                </Button>
              </CardContent>
            </Card>

            <Card className="group hover:shadow-xl transition-all duration-300 border-0 bg-white">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                    <FileText className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">قانون التجارة</h3>
                    <p className="text-sm text-gray-500">PDF • 1.8 MB</p>
                  </div>
                </div>
                <p className="text-gray-600 text-sm mb-4">
                  قانون التجارة والشركات التجارية مع اللوائح التنفيذية المحدثة
                </p>
                <Button size="sm" variant="outline" className="w-full group-hover:bg-blue-50 group-hover:border-blue-200">
                  <Download className="h-4 w-4 mr-2" />
                  تحميل الملف
                </Button>
              </CardContent>
            </Card>

            <Card className="group hover:shadow-xl transition-all duration-300 border-0 bg-white">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                    <FileText className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">قانون العمل</h3>
                    <p className="text-sm text-gray-500">PDF • 1.2 MB</p>
                  </div>
                </div>
                <p className="text-gray-600 text-sm mb-4">
                  قانون العمل وحقوق العمال مع التعديلات الأخيرة والضوابط الجديدة
                </p>
                <Button size="sm" variant="outline" className="w-full group-hover:bg-green-50 group-hover:border-green-200">
                  <Download className="h-4 w-4 mr-2" />
                  تحميل الملف
                </Button>
              </CardContent>
            </Card>

            <Card className="group hover:shadow-xl transition-all duration-300 border-0 bg-white">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                    <FileText className="h-6 w-6 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">قانون الأحوال الشخصية</h3>
                    <p className="text-sm text-gray-500">PDF • 900 KB</p>
                  </div>
                </div>
                <p className="text-gray-600 text-sm mb-4">
                  قانون الأحوال الشخصية والأسرة مع الشروحات والتفسيرات القانونية
                </p>
                <Button size="sm" variant="outline" className="w-full group-hover:bg-purple-50 group-hover:border-purple-200">
                  <Download className="h-4 w-4 mr-2" />
                  تحميل الملف
                </Button>
              </CardContent>
            </Card>

            <Card className="group hover:shadow-xl transition-all duration-300 border-0 bg-white">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mr-4">
                    <FileText className="h-6 w-6 text-orange-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">قانون الإجراءات المدنية</h3>
                    <p className="text-sm text-gray-500">PDF • 2.1 MB</p>
                  </div>
                </div>
                <p className="text-gray-600 text-sm mb-4">
                  قانون الإجراءات المدنية والتجارية مع النماذج والاستمارات القانونية
                </p>
                <Button size="sm" variant="outline" className="w-full group-hover:bg-orange-50 group-hover:border-orange-200">
                  <Download className="h-4 w-4 mr-2" />
                  تحميل الملف
                </Button>
              </CardContent>
            </Card>

            <Card className="group hover:shadow-xl transition-all duration-300 border-0 bg-white">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mr-4">
                    <FileText className="h-6 w-6 text-indigo-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">قانون الضرائب</h3>
                    <p className="text-sm text-gray-500">PDF • 1.5 MB</p>
                  </div>
                </div>
                <p className="text-gray-600 text-sm mb-4">
                  قانون الضرائب والرسوم مع اللوائح التنفيذية والتعليمات الضريبية
                </p>
                <Button size="sm" variant="outline" className="w-full group-hover:bg-indigo-50 group-hover:border-indigo-200">
                  <Download className="h-4 w-4 mr-2" />
                  تحميل الملف
                </Button>
              </CardContent>
            </Card>
          </div>

          <div className="text-center mt-12">
            <div className="bg-white rounded-2xl shadow-lg p-8 max-w-2xl mx-auto border border-gray-100">
              <BookOpen className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-gray-900 mb-4">مكتبة شاملة ومحدثة</h3>
              <p className="text-gray-600 mb-6">
                تحتوي مكتبتنا على أكثر من 200 وثيقة قانونية محدثة ومراجعة من قبل خبرائنا القانونيين
              </p>
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white">
                <BookOpen className="h-5 w-5 mr-2" />
                تصفح المكتبة الكاملة
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl font-bold text-gray-900 mb-6">من نحن</h2>
              <div className="space-y-4 text-gray-600 leading-relaxed">
                <p>
                  {companyData?.description ||
                    'نحن مؤسسة قانونية متخصصة تقدم خدمات قانونية شاملة ومتميزة للأفراد والشركات. نتميز بخبرتنا الواسعة وفريقنا المتخصص من المحامين والمستشارين القانونيين.'
                  }
                </p>
                <p>
                  تأسست مؤسستنا في عام {companyData?.established_date ? new Date(companyData.established_date).getFullYear() : '2020'}
                  ومنذ ذلك الحين ونحن نعمل بجد لتقديم أفضل الخدمات القانونية وحماية حقوق عملائنا.
                </p>
              </div>

              <div className="mt-8 grid grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">رؤيتنا</h4>
                  <p className="text-gray-600 text-sm">أن نكون الخيار الأول للخدمات القانونية المتميزة</p>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">رسالتنا</h4>
                  <p className="text-gray-600 text-sm">تقديم خدمات قانونية عالية الجودة بأعلى معايير الاحترافية</p>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 rounded-2xl p-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">معلومات التواصل</h3>
              <div className="space-y-4">
                <div className="flex items-center">
                  <Phone className="h-5 w-5 text-blue-600 mr-3" />
                  <span className="text-gray-700">{companyData?.phone || '+967-1-234567'}</span>
                </div>
                <div className="flex items-center">
                  <Mail className="h-5 w-5 text-blue-600 mr-3" />
                  <span className="text-gray-700">{companyData?.email || '<EMAIL>'}</span>
                </div>
                <div className="flex items-center">
                  <MapPin className="h-5 w-5 text-blue-600 mr-3" />
                  <span className="text-gray-700">
                    {companyData?.address || 'العنوان غير محدد'}, {companyData?.city || 'المدينة'}
                  </span>
                </div>
              </div>

              <div className="mt-6 pt-6 border-t border-blue-200">
                <h4 className="font-semibold text-gray-900 mb-2">ساعات العمل</h4>
                <p className="text-gray-600 text-sm">الأحد - الخميس: 8:00 ص - 6:00 م</p>
                <p className="text-gray-600 text-sm">الجمعة: 8:00 ص - 12:00 م</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="md:col-span-2">
              <div className="flex items-center mb-4">
                {companyData?.logo_url ? (
                  <Image
                    src={companyData.logo_url}
                    alt="شعار الشركة"
                    width={40}
                    height={40}
                    className="rounded-lg mr-3"
                  />
                ) : (
                  <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                    <Scale className="h-5 w-5 text-white" />
                  </div>
                )}
                <h3 className="text-xl font-bold">
                  {companyData?.name || 'مؤسسة المحاماة والاستشارات القانونية'}
                </h3>
              </div>
              <p className="text-gray-300 mb-4 leading-relaxed">
                {companyData?.description?.substring(0, 200) || 'مؤسسة قانونية متخصصة تقدم خدمات قانونية شاملة ومتميزة'}...
              </p>
              <div className="flex space-x-4 space-x-reverse">
                <Button size="sm" variant="outline" className="border-gray-600 text-gray-300 hover:bg-gray-800">
                  <Phone className="h-4 w-4 mr-2" />
                  اتصل بنا
                </Button>
                <Button size="sm" variant="outline" className="border-gray-600 text-gray-300 hover:bg-gray-800">
                  <Mail className="h-4 w-4 mr-2" />
                  راسلنا
                </Button>
              </div>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">خدماتنا</h4>
              <ul className="space-y-2 text-gray-300">
                <li>الاستشارات القانونية</li>
                <li>التمثيل القضائي</li>
                <li>صياغة العقود</li>
                <li>التحكيم والوساطة</li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">معلومات التواصل</h4>
              <div className="space-y-2 text-gray-300">
                <p>{companyData?.phone || '+967-1-234567'}</p>
                <p>{companyData?.email || '<EMAIL>'}</p>
                <p>{companyData?.address || 'العنوان غير محدد'}</p>
                <p>{companyData?.city || 'المدينة'}, {companyData?.country || 'البلد'}</p>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; {new Date().getFullYear()} {companyData?.name || 'مؤسسة المحاماة والاستشارات القانونية'}. جميع الحقوق محفوظة.</p>
          </div>
        </div>
      </footer>

      {/* Chat Widget */}
      <div className="fixed bottom-6 left-6 z-50">
        <Button
          size="lg"
          className="bg-green-600 hover:bg-green-700 text-white rounded-full shadow-lg"
          onClick={() => alert('ميزة الدردشة قيد التطوير')}
        >
          <MessageCircle className="h-6 w-6" />
        </Button>
      </div>
    </div>
  )
}