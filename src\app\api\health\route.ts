import { NextResponse } from 'next/server'
import { getDatabaseInfo, getFullDatabaseInfo, testConnection } from '@/lib/database-router'

export async function GET() {
  try {
    const info = getDatabaseInfo()
    const fullInfo = await getFullDatabaseInfo()
    const ok = await testConnection(fullInfo.database)

    return NextResponse.json({
      ok,
      database: info.name,
      company: fullInfo.company,
      themeColor: info.themeColor,
      welcomeMessage: info.welcomeMessage,
      notificationPrefix: info.notificationPrefix,
      port: fullInfo.port,
      timestamp: fullInfo.timestamp,
    })
  } catch (error: any) {
    return NextResponse.json({ ok: false, error: error?.message || 'health_error' }, { status: 500 })
  }
}
