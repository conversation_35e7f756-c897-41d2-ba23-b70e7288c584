import jwt from 'jsonwebtoken'
import { query } from '@/lib/db'

const JWT_SECRET = process.env.JWT_SECRET || 'change_me_in_env'

export interface DecodedToken {
  userId?: number
  sessionToken?: string
  [key: string]: any
}

const SessionManager = {
  verifyJWT(token: string): DecodedToken | null {
    try {
      const decoded = jwt.verify(token, JWT_SECRET) as DecodedToken
      return decoded || null
    } catch {
      return null
    }
  },

  async terminateSession(sessionToken: string): Promise<void> {
    try {
      // محاولة إنهاء الجلسة في جدول الجلسات النشطة إن كان موجوداً
      await query?.(`
        UPDATE active_sessions
        SET is_active = false, terminated_at = CURRENT_TIMESTAMP
        WHERE session_token = $1
      `, [sessionToken])
    } catch {
      // إذا لم يوجد الجدول أو فشلت العملية نتجاهل بصمت حفاظاً على الإنتاج
    }
  },

  async validateSession(sessionToken: string): Promise<null | {
    userId: number | null
    loginTime: string | null
    lastActivity: string | null
    deviceInfo: string | null
    ipAddress: string | null
  }> {
    try {
      const result = await query?.(`
        SELECT user_id, login_time, last_activity, device_info, ip_address
        FROM active_sessions
        WHERE session_token = $1 AND (is_active = true OR is_active IS NULL)
        ORDER BY login_time DESC
        LIMIT 1
      `, [sessionToken])

      const row = result?.rows?.[0]
      if (!row) return null

      return {
        userId: row.user_id ?? null,
        loginTime: row.login_time ? String(row.login_time) : null,
        lastActivity: row.last_activity ? String(row.last_activity) : null,
        deviceInfo: row.device_info ?? null,
        ipAddress: row.ip_address ?? null,
      }
    } catch {
      return null
    }
  }
}

export default SessionManager
