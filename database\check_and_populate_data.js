// فحص وملء البيانات في الجداول
const { Client } = require('pg');

async function checkAndPopulateData() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'moham<PERSON>',
    user: 'postgres',
    password: 'yemen123'
  });
  
  try {
    await client.connect();
    console.log('✅ متصل بقاعدة البيانات');

    // 1. فحص الجداول الموجودة
    console.log('\n📊 فحص الجداول الموجودة...');
    
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    console.log(`📋 عدد الجداول: ${tablesResult.rows.length}`);
    
    // 2. فحص البيانات في الجداول المهمة
    const importantTables = [
      'companies', 'users', 'employees', 'clients', 'issues', 
      'services', 'courts', 'issue_types', 'lineages', 'footer_links'
    ];
    
    console.log('\n🔍 فحص البيانات في الجداول المهمة...');
    
    for (const tableName of importantTables) {
      try {
        const result = await client.query(`SELECT COUNT(*) as count FROM ${tableName}`);
        console.log(`   📊 ${tableName}: ${result.rows[0].count} سجل`);
      } catch (error) {
        console.log(`   ❌ ${tableName}: غير موجود أو خطأ`);
      }
    }

    // 3. إضافة البيانات الأساسية
    console.log('\n📥 إضافة البيانات الأساسية...');

    // إضافة بيانات الشركة
    console.log('🏢 إضافة بيانات الشركة...');
    try {
      await client.query(`
        INSERT INTO companies (
          name, legal_name, registration_number, tax_number, address, city, country,
          phone, email, website, established_date, legal_form, capital, description,
          logo_url, logo_right_text, logo_left_text, logo_image_url, is_active, created_date
        ) VALUES (
          'مؤسسة الجرافي للمحاماة والاستشارات القانونية',
          'مؤسسة الجرافي للمحاماة والاستشارات القانونية المحدودة',
          'CR-2024-001',
          'TAX-*********',
          'صنعاء- شارع مجاهد- عمارة الحاشدي',
          'صنعاء',
          'اليمن',
          '+967-1-123456',
          '<EMAIL>',
          'www.legalfirm.ye',
          '2020-01-14',
          'شركة محدودة المسؤولية',
          1000000.00,
          'مكتب متخصص في تقديم الخدمات القانونية والاستشارات القانونية في جميع المجالات',
          '/images/company-logo.png',
          'مؤسسة الجرافي للمحاماة والاستشارات القانونية',
          'صنعاء - شارع تعز - امام بريد شميلة',
          '/images/logo.png',
          true,
          CURRENT_DATE
        )
      `);
      console.log('   ✅ تم إضافة بيانات الشركة');
    } catch (error) {
      console.log(`   ⚠️ خطأ في إضافة بيانات الشركة: ${error.message}`);
    }

    // إضافة أدوار المستخدمين
    console.log('👥 إضافة أدوار المستخدمين...');
    try {
      await client.query(`
        INSERT INTO user_roles (role_name, display_name, description, created_date)
        VALUES 
          ('admin', 'مدير النظام', 'صلاحيات كاملة للنظام', CURRENT_DATE),
          ('manager', 'مدير', 'صلاحيات إدارية', CURRENT_DATE),
          ('lawyer', 'محامي', 'صلاحيات المحامي', CURRENT_DATE),
          ('secretary', 'سكرتير', 'صلاحيات السكرتارية', CURRENT_DATE),
          ('user', 'مستخدم', 'صلاحيات أساسية', CURRENT_DATE)
      `);
      console.log('   ✅ تم إضافة أدوار المستخدمين');
    } catch (error) {
      console.log(`   ⚠️ خطأ في إضافة أدوار المستخدمين: ${error.message}`);
    }

    // إضافة المستخدم admin
    console.log('🔑 إضافة المستخدم admin...');
    try {
      await client.query(`
        INSERT INTO users (
          username, password_hash, email, role, status, is_online, login_attempts,
          created_date, user_type, is_active
        ) VALUES (
          'admin',
          'admin123',
          '<EMAIL>',
          'admin',
          'active',
          false,
          0,
          CURRENT_DATE,
          'admin',
          true
        )
      `);
      console.log('   ✅ تم إضافة المستخدم admin');
    } catch (error) {
      console.log(`   ⚠️ خطأ في إضافة المستخدم admin: ${error.message}`);
    }

    // إضافة الموظفين
    console.log('👨‍💼 إضافة الموظفين...');
    try {
      await client.query(`
        INSERT INTO employees (name, position, phone, email, hire_date, is_active, created_date)
        VALUES 
          ('أحمد محمد علي', 'محامي أول', '+967-777-123456', '<EMAIL>', CURRENT_DATE, true, CURRENT_DATE),
          ('فاطمة سالم', 'محامية', '+967-777-234567', '<EMAIL>', CURRENT_DATE, true, CURRENT_DATE),
          ('محمد حسن', 'سكرتير', '+967-777-345678', '<EMAIL>', CURRENT_DATE, true, CURRENT_DATE)
      `);
      console.log('   ✅ تم إضافة الموظفين');
    } catch (error) {
      console.log(`   ⚠️ خطأ في إضافة الموظفين: ${error.message}`);
    }

    // إضافة الخدمات
    console.log('⚖️ إضافة الخدمات القانونية...');
    try {
      await client.query(`
        INSERT INTO services (name, description, price, is_active, created_date)
        VALUES 
          ('استشارة قانونية', 'تقديم استشارة قانونية شاملة', 100.00, true, CURRENT_DATE),
          ('صياغة عقود', 'صياغة ومراجعة العقود القانونية', 500.00, true, CURRENT_DATE),
          ('تمثيل قضائي', 'تمثيل الموكل أمام المحاكم', 1000.00, true, CURRENT_DATE),
          ('تأسيس شركات', 'خدمات تأسيس الشركات والمؤسسات', 2000.00, true, CURRENT_DATE)
      `);
      console.log('   ✅ تم إضافة الخدمات القانونية');
    } catch (error) {
      console.log(`   ⚠️ خطأ في إضافة الخدمات: ${error.message}`);
    }

    // إضافة المحاكم
    console.log('🏛️ إضافة المحاكم...');
    try {
      await client.query(`
        INSERT INTO courts (name, type, address, phone, is_active, created_date)
        VALUES 
          ('المحكمة الابتدائية بصنعاء', 'ابتدائية', 'صنعاء - شارع الستين', '+967-1-111111', true, CURRENT_DATE),
          ('محكمة الاستئناف بصنعاء', 'استئناف', 'صنعاء - شارع الزبيري', '+967-1-222222', true, CURRENT_DATE),
          ('المحكمة التجارية', 'تجارية', 'صنعاء - شارع الحصبة', '+967-1-333333', true, CURRENT_DATE)
      `);
      console.log('   ✅ تم إضافة المحاكم');
    } catch (error) {
      console.log(`   ⚠️ خطأ في إضافة المحاكم: ${error.message}`);
    }

    // إضافة أنواع القضايا
    console.log('📋 إضافة أنواع القضايا...');
    try {
      await client.query(`
        INSERT INTO issue_types (name, description, is_active, created_date)
        VALUES 
          ('مدني', 'قضايا مدنية وتجارية', true, CURRENT_DATE),
          ('جنائي', 'قضايا جنائية', true, CURRENT_DATE),
          ('أحوال شخصية', 'قضايا الأحوال الشخصية', true, CURRENT_DATE),
          ('إداري', 'قضايا إدارية', true, CURRENT_DATE),
          ('عمالي', 'قضايا عمالية', true, CURRENT_DATE)
      `);
      console.log('   ✅ تم إضافة أنواع القضايا');
    } catch (error) {
      console.log(`   ⚠️ خطأ في إضافة أنواع القضايا: ${error.message}`);
    }

    // إضافة النسب المالية
    console.log('💰 إضافة النسب المالية...');
    try {
      await client.query(`
        INSERT INTO lineages (service_name, percentage, description, is_active, created_date)
        VALUES 
          ('استشارة قانونية', 10.00, 'نسبة المكتب من الاستشارات', true, CURRENT_DATE),
          ('صياغة عقود', 15.00, 'نسبة المكتب من صياغة العقود', true, CURRENT_DATE),
          ('تمثيل قضائي', 20.00, 'نسبة المكتب من التمثيل القضائي', true, CURRENT_DATE),
          ('تأسيس شركات', 25.00, 'نسبة المكتب من تأسيس الشركات', true, CURRENT_DATE)
      `);
      console.log('   ✅ تم إضافة النسب المالية');
    } catch (error) {
      console.log(`   ⚠️ خطأ في إضافة النسب المالية: ${error.message}`);
    }

    // إضافة روابط التذييل
    console.log('🔗 إضافة روابط التذييل...');
    try {
      await client.query(`
        INSERT INTO footer_links (title, url, category, is_active, sort_order, created_date)
        VALUES 
          ('الرئيسية', '/', 'main', true, 1, CURRENT_DATE),
          ('من نحن', '/about', 'main', true, 2, CURRENT_DATE),
          ('خدماتنا', '/services', 'main', true, 3, CURRENT_DATE),
          ('اتصل بنا', '/contact', 'main', true, 4, CURRENT_DATE),
          ('سياسة الخصوصية', '/privacy', 'legal', true, 5, CURRENT_DATE),
          ('شروط الاستخدام', '/terms', 'legal', true, 6, CURRENT_DATE)
      `);
      console.log('   ✅ تم إضافة روابط التذييل');
    } catch (error) {
      console.log(`   ⚠️ خطأ في إضافة روابط التذييل: ${error.message}`);
    }

    // 4. التحقق النهائي من البيانات
    console.log('\n🔍 التحقق النهائي من البيانات...');
    
    for (const tableName of importantTables) {
      try {
        const result = await client.query(`SELECT COUNT(*) as count FROM ${tableName}`);
        console.log(`   📊 ${tableName}: ${result.rows[0].count} سجل`);
      } catch (error) {
        console.log(`   ❌ ${tableName}: خطأ`);
      }
    }

    // 5. فحص المستخدم admin
    console.log('\n👤 فحص المستخدم admin...');
    
    try {
      const adminResult = await client.query(`
        SELECT username, email, role, status 
        FROM users 
        WHERE username = 'admin'
      `);
      
      if (adminResult.rows.length > 0) {
        const admin = adminResult.rows[0];
        console.log('✅ المستخدم admin موجود:');
        console.log(`   اسم المستخدم: ${admin.username}`);
        console.log(`   البريد: ${admin.email}`);
        console.log(`   الدور: ${admin.role}`);
        console.log(`   الحالة: ${admin.status}`);
      } else {
        console.log('❌ المستخدم admin غير موجود');
      }
    } catch (error) {
      console.log(`❌ خطأ في فحص المستخدم admin: ${error.message}`);
    }

    // 6. فحص بيانات الشركة
    console.log('\n🏢 فحص بيانات الشركة...');
    
    try {
      const companyResult = await client.query('SELECT * FROM companies LIMIT 1');
      
      if (companyResult.rows.length > 0) {
        const company = companyResult.rows[0];
        console.log('✅ بيانات الشركة موجودة:');
        console.log(`   الاسم: ${company.name}`);
        console.log(`   البريد: ${company.email}`);
        console.log(`   الهاتف: ${company.phone}`);
        console.log(`   العنوان: ${company.address}`);
      } else {
        console.log('❌ بيانات الشركة غير موجودة');
      }
    } catch (error) {
      console.log(`❌ خطأ في فحص بيانات الشركة: ${error.message}`);
    }

    await client.end();
    
    console.log('\n🎉 تم فحص وإضافة البيانات بنجاح!');
    console.log('✅ قاعدة البيانات جاهزة للاستخدام');
    console.log('🔑 بيانات تسجيل الدخول: admin / admin123');
    
  } catch (error) {
    console.error('❌ خطأ في فحص البيانات:', error.message);
  }
}

checkAndPopulateData();
