// إعداد SSL للمنافذ المخصصة - mohammi.com
// المنفذ 7443: قاعدة البيانات mohammi
// المنفذ 8914: قاعدة البيانات rubaie

const fs = require('fs');
const https = require('https');
const http = require('http');
const { spawn } = require('child_process');

console.log('🔐 إعداد SSL للمنافذ المخصصة - mohammi.com');
console.log('🌐 IP: ***********');
console.log('🔌 المنفذ 7443: قاعدة البيانات mohammi');
console.log('🔌 المنفذ 8914: قاعدة البيانات rubaie');
console.log('='.repeat(60));

// إعدادات المنافذ
const PORTS = {
  mohammi: {
    https: 7443,
    http: 7080,
    app: 7443  // نفس المنفذ لأن التطبيق يعمل عليه
  },
  rubaie: {
    https: 8914,
    http: 8080,
    app: 8914  // نفس المنفذ لأن التطبيق يعمل عليه
  }
};

// البحث عن ملفات SSL
const requiredFiles = {
  cert: 'ssl/mohammi_com.crt',
  key: null,
  intermediate: 'ssl/SectigoPublicServerAuthenticationCADVR36.crt'
};

// البحث عن المفتاح الخاص
const possibleKeys = [
  'ssl/mohammi_com.key',
  'ssl/private.key',
  'ssl/privatekey.pem',
  'ssl/mohammi.key',
  'ssl/server.key'
];

console.log('🔍 البحث عن المفتاح الخاص...');
for (const keyPath of possibleKeys) {
  if (fs.existsSync(keyPath)) {
    requiredFiles.key = keyPath;
    console.log(`✅ تم العثور على المفتاح الخاص: ${keyPath}`);
    break;
  }
}

if (!requiredFiles.key) {
  console.log('❌ لم يتم العثور على المفتاح الخاص');
  console.log('💡 يرجى استخراج ملف mohammi_com.zip أولاً');
  console.log('💡 أو تأكد من وجود ملف .key في مجلد ssl');
  process.exit(1);
}

// التحقق من باقي الملفات
for (const [name, path] of Object.entries(requiredFiles)) {
  if (path && !fs.existsSync(path)) {
    console.log(`❌ ملف ${name} غير موجود: ${path}`);
    if (name === 'intermediate') {
      console.log('💡 سيتم المتابعة بدون الشهادة الوسطية');
      requiredFiles.intermediate = null;
    } else {
      process.exit(1);
    }
  }
}

console.log('✅ جميع الملفات المطلوبة متاحة');

// قراءة ملفات SSL
console.log('\n📖 قراءة ملفات SSL...');
let sslOptions;

try {
  const privateKey = fs.readFileSync(requiredFiles.key, 'utf8');
  let certificate = fs.readFileSync(requiredFiles.cert, 'utf8');
  
  // إضافة الشهادة الوسطية إذا كانت متاحة
  if (requiredFiles.intermediate) {
    const intermediate = fs.readFileSync(requiredFiles.intermediate, 'utf8');
    certificate = certificate + '\n' + intermediate;
    console.log('✅ تم دمج الشهادة الوسطية');
  }
  
  sslOptions = {
    key: privateKey,
    cert: certificate,
    secureProtocol: 'TLSv1_2_method',
    ciphers: [
      'ECDHE-RSA-AES128-GCM-SHA256',
      'ECDHE-RSA-AES256-GCM-SHA384',
      'ECDHE-RSA-AES128-SHA256',
      'ECDHE-RSA-AES256-SHA384'
    ].join(':'),
    honorCipherOrder: true
  };
  
  console.log('✅ تم تحميل ملفات SSL بنجاح');
  
} catch (error) {
  console.error('❌ خطأ في قراءة ملفات SSL:', error.message);
  process.exit(1);
}

// دالة لإنشاء خادم HTTPS مع SSL
function createSSLServer(dbName, ports) {
  console.log(`\n🔧 إنشاء خادم SSL لـ ${dbName}...`);
  
  const httpsServer = https.createServer(sslOptions, (req, res) => {
    // Headers الأمان
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    
    // إضافة معلومات قاعدة البيانات
    res.setHeader('X-Database', dbName);
    res.setHeader('X-SSL-Port', ports.https);
    
    // عرض صفحة معلومات SSL
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(`
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>🔐 SSL - ${dbName} - mohammi.com</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; margin: 0; padding: 50px; text-align: center;
        }
        .container { 
            background: rgba(255,255,255,0.1); padding: 40px; 
            border-radius: 20px; backdrop-filter: blur(10px);
            max-width: 700px; margin: 0 auto;
        }
        .status { color: #4ade80; font-weight: bold; }
        .warning { color: #fbbf24; font-weight: bold; }
        .info { background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; margin: 20px 0; text-align: right; }
        .links { background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; margin: 20px 0; }
        .links a { color: #60a5fa; text-decoration: none; display: block; margin: 5px 0; }
        .links a:hover { color: #93c5fd; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 خادم SSL يعمل بنجاح!</h1>
        <h2>قاعدة البيانات: ${dbName}</h2>
        <p>مرحباً بك في موقع mohammi.com المحمي بـ SSL</p>
        
        <div class="info">
            <h3>معلومات الاتصال:</h3>
            <p><strong>البروتوكول:</strong> <span class="status">HTTPS ✅</span></p>
            <p><strong>المنفذ:</strong> ${ports.https}</p>
            <p><strong>قاعدة البيانات:</strong> ${dbName}</p>
            <p><strong>IP:</strong> ***********</p>
            <p><strong>الدومين:</strong> mohammi.com</p>
            <p><strong>التشفير:</strong> TLS 1.2+</p>
            <p><strong>الوقت:</strong> ${new Date().toLocaleString('ar-EG')}</p>
        </div>
        
        <div class="info">
            <h3>معلومات الشهادة:</h3>
            <p><strong>المُصدر:</strong> Sectigo</p>
            <p><strong>النطاق:</strong> mohammi.com</p>
            <p><strong>الحالة:</strong> <span class="status">صالحة ✅</span></p>
        </div>
        
        <div class="links">
            <h3>🔗 روابط الوصول:</h3>
            <a href="https://mohammi.com:${ports.https}">https://mohammi.com:${ports.https}</a>
            <a href="https://***********:${ports.https}">https://***********:${ports.https}</a>
            <a href="http://mohammi.com:${ports.http}">http://mohammi.com:${ports.http} (إعادة توجيه)</a>
        </div>
        
        <div class="info">
            <h3>🧪 اختبار الشهادة:</h3>
            <p>• SSL Labs: <a href="https://www.ssllabs.com/ssltest/analyze.html?d=mohammi.com:${ports.https}" target="_blank">اختبار الأمان</a></p>
            <p>• SSL Checker: <a href="https://www.sslshopper.com/ssl-checker.html#hostname=mohammi.com:${ports.https}" target="_blank">فحص الشهادة</a></p>
        </div>
        
        <div class="info">
            <p class="status">✅ شهادة SSL تعمل بنجاح على المنفذ ${ports.https}</p>
            <p>يمكنك الآن الوصول للموقع بأمان عبر HTTPS</p>
        </div>
    </div>
</body>
</html>`);
  });
  
  // خادم HTTP لإعادة التوجيه
  const httpServer = http.createServer((req, res) => {
    const httpsUrl = `https://${req.headers.host.split(':')[0]}:${ports.https}${req.url}`;
    res.writeHead(301, {
      'Location': httpsUrl,
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'
    });
    res.end();
  });
  
  return { httpsServer, httpServer };
}

// إنشاء خوادم SSL للمنافذ المختلفة
const servers = {};

for (const [dbName, ports] of Object.entries(PORTS)) {
  servers[dbName] = createSSLServer(dbName, ports);
}

// بدء الخوادم
console.log('\n🚀 بدء خوادم SSL...');

// بدء خوادم HTTPS
for (const [dbName, ports] of Object.entries(PORTS)) {
  const { httpsServer, httpServer } = servers[dbName];
  
  // خادم HTTPS
  httpsServer.listen(ports.https, '0.0.0.0', (err) => {
    if (err) {
      console.error(`❌ فشل في بدء خادم HTTPS لـ ${dbName} على المنفذ ${ports.https}:`, err.message);
    } else {
      console.log(`✅ خادم HTTPS لـ ${dbName} يعمل على المنفذ ${ports.https}`);
      console.log(`   🌐 https://mohammi.com:${ports.https}`);
      console.log(`   🌐 https://***********:${ports.https}`);
    }
  });
  
  // خادم HTTP لإعادة التوجيه
  httpServer.listen(ports.http, '0.0.0.0', (err) => {
    if (err) {
      console.log(`⚠️ لم يتم بدء خادم HTTP لـ ${dbName} على المنفذ ${ports.http}`);
    } else {
      console.log(`✅ خادم HTTP لـ ${dbName} يعمل على المنفذ ${ports.http} (إعادة توجيه)`);
    }
  });
}

// معلومات إضافية
console.log('\n📊 ملخص الإعداد:');
console.log('='.repeat(60));
console.log(`🔐 الشهادة: ${requiredFiles.cert}`);
console.log(`🔑 المفتاح الخاص: ${requiredFiles.key}`);
if (requiredFiles.intermediate) {
  console.log(`🔗 الشهادة الوسطية: ${requiredFiles.intermediate}`);
}

console.log('\n🌐 الخوادم النشطة:');
for (const [dbName, ports] of Object.entries(PORTS)) {
  console.log(`📍 ${dbName}:`);
  console.log(`   🔐 HTTPS: ${ports.https}`);
  console.log(`   🔄 HTTP: ${ports.http} → ${ports.https}`);
}

console.log('\n🎯 روابط الاختبار:');
console.log('🔐 HTTPS:');
for (const [dbName, ports] of Object.entries(PORTS)) {
  console.log(`   • https://mohammi.com:${ports.https} (${dbName})`);
  console.log(`   • https://***********:${ports.https} (${dbName})`);
}

console.log('\n🔄 HTTP (إعادة توجيه):');
for (const [dbName, ports] of Object.entries(PORTS)) {
  console.log(`   • http://mohammi.com:${ports.http} → HTTPS:${ports.https} (${dbName})`);
}

console.log('\n🧪 اختبار الشهادات:');
for (const [dbName, ports] of Object.entries(PORTS)) {
  console.log(`📋 ${dbName}:`);
  console.log(`   • SSL Labs: https://www.ssllabs.com/ssltest/analyze.html?d=mohammi.com:${ports.https}`);
  console.log(`   • curl: curl -I https://mohammi.com:${ports.https}`);
}

console.log('\n⚠️ ملاحظات مهمة:');
console.log('1. المنفذ 443 محجوز لنظام آخر');
console.log('2. استخدم المنافذ المخصصة: 7443 و 8914');
console.log('3. فتح المنافذ في جدار الحماية');
console.log('4. توجيه DNS للدومين mohammi.com إلى IP ***********');
console.log('5. استخدم الروابط مع المنافذ المخصصة');

// معالجة إيقاف الخوادم
process.on('SIGINT', () => {
  console.log('\n🛑 إيقاف خوادم SSL...');
  
  for (const [dbName, { httpsServer, httpServer }] of Object.entries(servers)) {
    httpsServer.close(() => console.log(`✅ تم إيقاف خادم HTTPS لـ ${dbName}`));
    httpServer.close(() => console.log(`✅ تم إيقاف خادم HTTP لـ ${dbName}`));
  }
  
  process.exit(0);
});

console.log('\n⏳ خوادم SSL تعمل... اضغط Ctrl+C للإيقاف');
console.log('🔗 للاختبار: https://mohammi.com:7443 أو https://mohammi.com:8914');
