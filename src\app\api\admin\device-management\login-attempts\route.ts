import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database-router'

// GET - جلب محاولات تسجيل الدخول
export async function GET(request: NextRequest) {
  try {
    console.log('📋 جلب محاولات تسجيل الدخول...')

    const attemptsResult = await query(`
      SELECT 
        id, user_id, username, device_id, device_info, 
        ip_address, user_agent, login_time, login_status, 
        failure_reason, session_token, is_active
      FROM device_login_log 
      WHERE login_time >= NOW() - INTERVAL '7 days'
      ORDER BY login_time DESC
      LIMIT 100
    `)

    console.log(`✅ تم جلب ${attemptsResult.rows.length} محاولة دخول`)

    return NextResponse.json({
      success: true,
      attempts: attemptsResult.rows
    })

  } catch (error) {
    console.error('❌ خطأ في جلب محاولات تسجيل الدخول:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب محاولات تسجيل الدخول'
    }, { status: 500 })
  }
}

// DELETE - حذف محاولات تسجيل الدخول القديمة
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const days = parseInt(searchParams.get('days') || '30')

    console.log(`🗑️ حذف محاولات الدخول الأقدم من ${days} يوم...`)

    const deleteResult = await query(`
      DELETE FROM device_login_log 
      WHERE login_time < NOW() - INTERVAL '${days} days'
    `)

    console.log(`✅ تم حذف ${deleteResult.rowCount} محاولة دخول قديمة`)

    return NextResponse.json({
      success: true,
      message: `تم حذف ${deleteResult.rowCount} محاولة دخول قديمة`,
      deletedCount: deleteResult.rowCount
    })

  } catch (error) {
    console.error('❌ خطأ في حذف محاولات تسجيل الدخول:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في حذف محاولات تسجيل الدخول'
    }, { status: 500 })
  }
}
