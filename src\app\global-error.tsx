'use client'

import { useEffect } from 'react'

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    console.error('خطأ عام في التطبيق:', error)
  }, [error])

  return (
    <html>
      <body>
        <div style={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#f9fafb',
          fontFamily: 'system-ui, sans-serif',
          direction: 'rtl'
        }}>
          <div style={{
            backgroundColor: 'white',
            padding: '2rem',
            borderRadius: '0.5rem',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
            textAlign: 'center',
            maxWidth: '400px',
            width: '100%'
          }}>
            <div style={{
              width: '48px',
              height: '48px',
              backgroundColor: '#fee2e2',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 1rem',
              fontSize: '24px',
              color: '#dc2626'
            }}>
              ⚠️
            </div>
            
            <h2 style={{
              fontSize: '1.25rem',
              fontWeight: 'bold',
              color: '#111827',
              marginBottom: '1rem'
            }}>
              حدث خطأ في النظام
            </h2>
            
            <p style={{
              color: '#6b7280',
              marginBottom: '1.5rem'
            }}>
              نعتذر، حدث خطأ غير متوقع في النظام. يرجى إعادة تحميل الصفحة.
            </p>
            
            {error.digest && (
              <div style={{
                backgroundColor: '#f3f4f6',
                padding: '0.75rem',
                borderRadius: '0.375rem',
                marginBottom: '1.5rem'
              }}>
                <p style={{
                  fontSize: '0.75rem',
                  color: '#6b7280'
                }}>
                  معرف الخطأ: {error.digest}
                </p>
              </div>
            )}
            
            <div style={{
              display: 'flex',
              gap: '0.5rem'
            }}>
              <button
                onClick={reset}
                style={{
                  flex: 1,
                  backgroundColor: '#3b82f6',
                  color: 'white',
                  padding: '0.5rem 1rem',
                  borderRadius: '0.375rem',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '0.875rem'
                }}
              >
                إعادة المحاولة
              </button>
              <button
                onClick={() => window.location.href = '/'}
                style={{
                  flex: 1,
                  backgroundColor: 'white',
                  color: '#374151',
                  padding: '0.5rem 1rem',
                  borderRadius: '0.375rem',
                  border: '1px solid #d1d5db',
                  cursor: 'pointer',
                  fontSize: '0.875rem'
                }}
              >
                الصفحة الرئيسية
              </button>
            </div>
          </div>
        </div>
      </body>
    </html>
  )
}
