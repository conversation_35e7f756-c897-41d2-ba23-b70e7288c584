'use client'

import { useState, useEffect } from 'react'
import { UnifiedSidebar } from './unified-sidebar'
import { UnifiedHeader } from './unified-header'
import { AnnouncementBanner } from './announcement-banner'
import { ChatWidget } from '@/components/chat/chat-widget'
import { useAuth } from '@/hooks/useAuth'

interface UnifiedLayoutProps {
  children: React.ReactNode
}

export function UnifiedLayout({ children }: UnifiedLayoutProps) {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false)
  const { user } = useAuth()

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed)
  }

  return (
    <div 
      className="flex h-screen text-white" 
      dir="rtl" 
      style={{ background: 'linear-gradient(135deg, #333333 0%, #171717 100%)' }}
    >
      <UnifiedSidebar isCollapsed={isSidebarCollapsed} onToggle={toggleSidebar} />
      <div className="flex-1 flex flex-col overflow-hidden transition-all duration-300">
        <AnnouncementBanner />
        <UnifiedHeader />
        <main 
          className="flex-1 overflow-x-hidden overflow-y-auto p-6" 
          style={{ background: 'linear-gradient(135deg, #333333 0%, #171717 100%)' }}
        >
          {children}
        </main>
      </div>

      {/* مكون المحادثات */}
      <ChatWidget
        userType={user?.type || 'user'}
        userId={user?.id || 1}
        userName={user?.name || 'مستخدم'}
      />
    </div>
  )
}

export default UnifiedLayout
