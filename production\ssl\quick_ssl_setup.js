// إعداد سريع لـ SSL بعد استخراج المفتاح الخاص
const fs = require('fs');
const https = require('https');
const http = require('http');

console.log('🚀 إعداد سريع لـ SSL - mohammi.com');
console.log('🌐 IP: ***********');
console.log('='.repeat(50));

// التحقق من الملفات المطلوبة
const requiredFiles = {
  cert: 'ssl/mohammi_com.crt',
  key: null, // سيتم البحث عنه
  intermediate: 'ssl/SectigoPublicServerAuthenticationCADVR36.crt'
};

// البحث عن المفتاح الخاص
const possibleKeys = [
  'ssl/mohammi_com.key',
  'ssl/private.key',
  'ssl/privatekey.pem',
  'ssl/mohammi.key'
];

console.log('🔍 البحث عن المفتاح الخاص...');
for (const keyPath of possibleKeys) {
  if (fs.existsSync(keyPath)) {
    requiredFiles.key = keyPath;
    console.log(`✅ تم العثور على المفتاح الخاص: ${keyPath}`);
    break;
  }
}

if (!requiredFiles.key) {
  console.log('❌ لم يتم العثور على المفتاح الخاص');
  console.log('💡 يرجى استخراج ملف mohammi_com.zip أولاً');
  console.log('💡 ابحث عن ملف .key في الملفات المستخرجة');
  process.exit(1);
}

// التحقق من باقي الملفات
for (const [name, path] of Object.entries(requiredFiles)) {
  if (path && !fs.existsSync(path)) {
    console.log(`❌ ملف ${name} غير موجود: ${path}`);
    process.exit(1);
  }
}

console.log('✅ جميع الملفات المطلوبة موجودة');

// قراءة ملفات SSL
console.log('\n📖 قراءة ملفات SSL...');
let sslOptions;

try {
  const privateKey = fs.readFileSync(requiredFiles.key, 'utf8');
  const certificate = fs.readFileSync(requiredFiles.cert, 'utf8');
  const intermediate = fs.readFileSync(requiredFiles.intermediate, 'utf8');
  
  // دمج الشهادة مع الوسطية
  const fullChain = certificate + '\n' + intermediate;
  
  sslOptions = {
    key: privateKey,
    cert: fullChain,
    secureProtocol: 'TLSv1_2_method',
    ciphers: 'ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384',
    honorCipherOrder: true
  };
  
  console.log('✅ تم تحميل ملفات SSL بنجاح');
  
} catch (error) {
  console.error('❌ خطأ في قراءة ملفات SSL:', error.message);
  process.exit(1);
}

// إنشاء خادم HTTPS
console.log('\n🔧 إنشاء خادم HTTPS...');

const httpsServer = https.createServer(sslOptions, (req, res) => {
  // Headers الأمان
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // Proxy إلى تطبيق Next.js على المنفذ 7443
  const proxyReq = http.request({
    hostname: 'localhost',
    port: 7443,
    path: req.url,
    method: req.method,
    headers: {
      ...req.headers,
      'X-Forwarded-For': req.connection.remoteAddress,
      'X-Forwarded-Proto': 'https',
      'X-Forwarded-Host': req.headers.host,
      'X-Real-IP': req.connection.remoteAddress
    }
  }, (proxyRes) => {
    // نسخ headers من الاستجابة
    Object.keys(proxyRes.headers).forEach(key => {
      res.setHeader(key, proxyRes.headers[key]);
    });
    
    res.writeHead(proxyRes.statusCode);
    proxyRes.pipe(res);
  });
  
  proxyReq.on('error', (error) => {
    console.error('❌ خطأ في Proxy:', error.message);
    res.writeHead(502, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(`
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>خطأ في الاتصال</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; margin: 0; padding: 50px; text-align: center;
        }
        .error { 
            background: rgba(255,255,255,0.1); padding: 30px; 
            border-radius: 15px; backdrop-filter: blur(10px);
        }
    </style>
</head>
<body>
    <div class="error">
        <h1>🔧 خطأ في الاتصال</h1>
        <p>لا يمكن الوصول إلى تطبيق mohammi على المنفذ 7443</p>
        <p>يرجى التأكد من تشغيل التطبيق أولاً</p>
        <hr>
        <small>خادم SSL يعمل بنجاح على المنفذ 443</small>
    </div>
</body>
</html>`);
  });
  
  // إرسال البيانات
  req.pipe(proxyReq);
});

// خادم HTTP لإعادة التوجيه
const httpServer = http.createServer((req, res) => {
  const httpsUrl = `https://${req.headers.host}${req.url}`;
  res.writeHead(301, {
    'Location': httpsUrl,
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload'
  });
  res.end();
});

// بدء الخوادم
console.log('\n🚀 بدء الخوادم...');

// خادم HTTPS على المنفذ 443
httpsServer.listen(443, '0.0.0.0', (err) => {
  if (err) {
    console.error('❌ فشل في بدء خادم HTTPS:', err.message);
    console.log('💡 تأكد من:');
    console.log('   - تشغيل الأمر كمدير (Run as Administrator)');
    console.log('   - عدم استخدام المنفذ 443 من قبل تطبيق آخر');
    console.log('   - فتح المنفذ 443 في جدار الحماية');
    process.exit(1);
  }
  
  console.log('✅ خادم HTTPS يعمل على المنفذ 443');
  console.log('🌐 https://mohammi.com');
  console.log('🌐 https://***********');
});

// خادم HTTP على المنفذ 80
httpServer.listen(80, '0.0.0.0', (err) => {
  if (err) {
    console.log('⚠️ لم يتم بدء خادم HTTP (المنفذ 80 مستخدم)');
  } else {
    console.log('✅ خادم HTTP يعمل على المنفذ 80 (إعادة توجيه)');
  }
});

// معلومات إضافية
console.log('\n📊 معلومات الشهادة:');
console.log(`🔐 الشهادة: ${requiredFiles.cert}`);
console.log(`🔑 المفتاح الخاص: ${requiredFiles.key}`);
console.log(`🔗 الشهادة الوسطية: ${requiredFiles.intermediate}`);

console.log('\n🌐 معلومات الشبكة:');
console.log('📍 IP الخارجي: ***********');
console.log('🌍 الدومين: mohammi.com');
console.log('🔌 SSL Proxy: 443 → 7443');

console.log('\n🎯 اختبار الشهادة:');
console.log('• https://www.ssllabs.com/ssltest/analyze.html?d=mohammi.com');
console.log('• https://www.sslshopper.com/ssl-checker.html#hostname=mohammi.com');

console.log('\n⚠️ تأكد من:');
console.log('1. تشغيل تطبيق mohammi على المنفذ 7443');
console.log('2. فتح المنافذ 80 و 443 في جدار الحماية');
console.log('3. توجيه DNS للدومين mohammi.com إلى IP ***********');

// معالجة إيقاف الخادم
process.on('SIGINT', () => {
  console.log('\n🛑 إيقاف خوادم SSL...');
  httpsServer.close(() => console.log('✅ تم إيقاف خادم HTTPS'));
  httpServer.close(() => console.log('✅ تم إيقاف خادم HTTP'));
  process.exit(0);
});

console.log('\n⏳ خوادم SSL تعمل... اضغط Ctrl+C للإيقاف');
