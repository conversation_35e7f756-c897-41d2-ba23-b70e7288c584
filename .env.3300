# ملف البيئة للمنفذ الداخلي 3300 - نسخة التطوير mohammidev
PORT=3300
X_DATABASE=mohammidev
X_COMPANY=نظام إدارة المحاماة - محمد (التطوير)
X_THEME_COLOR=#ff6b35
X_WELCOME_MESSAGE=مرحباً بكم في نظام إدارة المحاماة - محمد (نسخة التطوير)
X_NOTIFICATION_PREFIX=محمد (تطوير): 
X_HOMEPAGE=/home
DATABASE_URL=postgresql://postgres:yemen123@localhost:5432/mohammidev

# إعدادات قاعدة البيانات
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=yemen123
DB_NAME=mohammidev

# إعدادات JWT
JWT_SECRET=your-secret-key-here

# إعدادات النظام
NODE_ENV=development
NEXT_PUBLIC_API_URL=http://localhost:3300/api

# مسار Google Chrome لاستخدامه مع Puppeteer (عدّل المسار إذا لزم)
CHROME_PATH=C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe

# إعدادات التطوير
NEXT_PUBLIC_DEV_MODE=true
NEXT_PUBLIC_SHOW_DEBUG=true
