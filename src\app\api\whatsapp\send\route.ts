/**
 * API لإرسال رسائل WhatsApp
 */

import { NextRequest, NextResponse } from 'next/server'

export async function POST(_request: NextRequest) {
  return NextResponse.json({
    success: false,
    error: 'تم إيقاف مسار الإرسال القديم. استخدم /api/whatsapp/cloud/send للإرسال عبر WhatsApp Cloud API.'
  }, { status: 410 })
}

// API لإرسال رسائل جماعية
export async function PUT(_request: NextRequest) {
  return NextResponse.json({
    success: false,
    error: 'تم إيقاف مسار الإرسال الجماعي القديم. استخدم /api/whatsapp/cloud/send مع منطقك الخاص للتجزئة.'
  }, { status: 410 })
}
