#!/bin/bash
# تثبيت شهادة SSL على المنافذ المخصصة لـ mohammi.com
# المنفذ 7443: قاعدة البيانات mohammi
# المنفذ 8914: قاعدة البيانات rubaie
# IP: ***********

echo "🔐 تثبيت شهادة SSL على المنافذ المخصصة"
echo "🌐 الدومين: mohammi.com"
echo "📍 IP: ***********"
echo "🔌 المنافذ: 7443 (mohammi) و 8914 (rubaie)"
echo "⚠️ المنفذ 443 محجوز لنظام آخر"
echo "=================================================="

# التحقق من صلاحيات المدير
if [ "$EUID" -ne 0 ]; then
    echo "❌ يرجى تشغيل هذا السكريپت كمدير (sudo)"
    exit 1
fi

# التحقق من وجود Nginx
if ! command -v nginx &> /dev/null; then
    echo "❌ Nginx غير مثبت"
    echo "💡 لتثبيت Nginx: sudo apt update && sudo apt install nginx"
    exit 1
fi

echo "✅ تم العثور على Nginx"

# إنشاء مجلد SSL
SSL_DIR="/etc/ssl/mohammi"
mkdir -p $SSL_DIR
echo "✅ تم إنشاء مجلد SSL: $SSL_DIR"

# البحث عن ملفات الشهادة في مجلدات محتملة
echo "🔍 البحث عن ملفات الشهادة..."

SEARCH_DIRS=(
    "/home/<USER>/ssl"
    "/var/www/ssl"
    "/opt/ssl"
    "/root/ssl"
    "/tmp/ssl"
    "/home/<USER>/mohaminew/ssl"
    "/var/www/mohaminew/ssl"
)

CERT_FILE=""
KEY_FILE=""
INTERMEDIATE_FILE=""

for dir in "${SEARCH_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "🔍 البحث في: $dir"
        
        # البحث عن الشهادة
        if [ -z "$CERT_FILE" ]; then
            CERT_FILE=$(find $dir -name "mohammi_com.crt" -o -name "mohammi.crt" -o -name "certificate.crt" | head -1)
        fi
        
        # البحث عن المفتاح الخاص
        if [ -z "$KEY_FILE" ]; then
            KEY_FILE=$(find $dir -name "mohammi_com.key" -o -name "mohammi.key" -o -name "private.key" | head -1)
        fi
        
        # البحث عن الشهادة الوسطية
        if [ -z "$INTERMEDIATE_FILE" ]; then
            INTERMEDIATE_FILE=$(find $dir -name "*Sectigo*" -o -name "*intermediate*" -o -name "*chain*" | head -1)
        fi
    fi
done

# التحقق من النتائج
if [ -n "$CERT_FILE" ]; then
    echo "✅ تم العثور على الشهادة: $CERT_FILE"
else
    echo "❌ لم يتم العثور على ملف الشهادة"
    echo "💡 يرجى وضع ملف mohammi_com.crt في أحد المجلدات"
    exit 1
fi

if [ -n "$KEY_FILE" ]; then
    echo "✅ تم العثور على المفتاح الخاص: $KEY_FILE"
else
    echo "❌ لم يتم العثور على المفتاح الخاص"
    echo "💡 يرجى استخراج ملف mohammi_com.zip والبحث عن ملف .key"
    exit 1
fi

if [ -n "$INTERMEDIATE_FILE" ]; then
    echo "✅ تم العثور على الشهادة الوسطية: $INTERMEDIATE_FILE"
fi

# نسخ الملفات إلى مجلد SSL
echo "📁 نسخ ملفات الشهادة..."
cp "$CERT_FILE" "$SSL_DIR/mohammi_com.crt"
cp "$KEY_FILE" "$SSL_DIR/mohammi_com.key"

if [ -n "$INTERMEDIATE_FILE" ]; then
    cp "$INTERMEDIATE_FILE" "$SSL_DIR/intermediate.crt"
    # دمج الشهادة مع الوسطية
    cat "$SSL_DIR/mohammi_com.crt" "$SSL_DIR/intermediate.crt" > "$SSL_DIR/fullchain.crt"
    echo "✅ تم دمج الشهادة مع الوسطية"
else
    cp "$SSL_DIR/mohammi_com.crt" "$SSL_DIR/fullchain.crt"
fi

# تعيين صلاحيات آمنة
chmod 644 "$SSL_DIR/mohammi_com.crt" "$SSL_DIR/fullchain.crt"
chmod 600 "$SSL_DIR/mohammi_com.key"
if [ -f "$SSL_DIR/intermediate.crt" ]; then
    chmod 644 "$SSL_DIR/intermediate.crt"
fi

echo "✅ تم تعيين صلاحيات الملفات"

# نسخ تكوين Nginx
NGINX_CONFIG="/etc/nginx/sites-available/mohammi-ssl"

echo "⚙️ إنشاء تكوين Nginx للمنافذ المخصصة..."

# نسخ ملف التكوين الجاهز
if [ -f "ssl/nginx_custom_ports.conf" ]; then
    cp "ssl/nginx_custom_ports.conf" $NGINX_CONFIG
    echo "✅ تم نسخ تكوين Nginx"
else
    echo "❌ ملف تكوين Nginx غير موجود"
    echo "💡 يرجى التأكد من وجود ssl/nginx_custom_ports.conf"
    exit 1
fi

# تفعيل الموقع
echo "🔗 تفعيل الموقع..."
ln -sf $NGINX_CONFIG /etc/nginx/sites-enabled/
echo "✅ تم تفعيل الموقع"

# إزالة التكوين الافتراضي إذا كان موجوداً
if [ -f "/etc/nginx/sites-enabled/default" ]; then
    rm /etc/nginx/sites-enabled/default
    echo "✅ تم إزالة التكوين الافتراضي"
fi

# اختبار تكوين Nginx
echo "🧪 اختبار تكوين Nginx..."
if nginx -t; then
    echo "✅ تكوين Nginx صحيح"
else
    echo "❌ خطأ في تكوين Nginx"
    exit 1
fi

# إعادة تحميل Nginx
echo "🔄 إعادة تحميل Nginx..."
systemctl reload nginx
if [ $? -eq 0 ]; then
    echo "✅ تم إعادة تحميل Nginx بنجاح"
else
    echo "❌ فشل في إعادة تحميل Nginx"
    exit 1
fi

# فتح المنافذ في جدار الحماية
echo "🔥 فتح المنافذ في جدار الحماية..."
ufw allow 7443/tcp comment "HTTPS mohammi database"
ufw allow 8914/tcp comment "HTTPS rubaie database"
ufw allow 7080/tcp comment "HTTP redirect to 7443"
ufw allow 8080/tcp comment "HTTP redirect to 8914"
echo "✅ تم فتح المنافذ في جدار الحماية"

# التحقق من حالة Nginx
echo "📊 التحقق من حالة Nginx..."
systemctl status nginx --no-pager -l

echo ""
echo "🎉 تم تثبيت شهادة SSL بنجاح على المنافذ المخصصة!"
echo "=================================================="
echo "🌐 الروابط:"
echo "   🔐 HTTPS:"
echo "      • https://mohammi.com:7443 (قاعدة البيانات mohammi)"
echo "      • https://mohammi.com:8914 (قاعدة البيانات rubaie)"
echo "      • https://***********:7443 (IP مباشر - mohammi)"
echo "      • https://***********:8914 (IP مباشر - rubaie)"
echo ""
echo "   🔄 HTTP (إعادة توجيه):"
echo "      • http://mohammi.com:7080 → https://mohammi.com:7443"
echo "      • http://mohammi.com:8080 → https://mohammi.com:8914"
echo ""
echo "🧪 اختبار الشهادات:"
echo "   • SSL Labs: https://www.ssllabs.com/ssltest/analyze.html?d=mohammi.com:7443"
echo "   • SSL Labs: https://www.ssllabs.com/ssltest/analyze.html?d=mohammi.com:8914"
echo "   • curl: curl -I https://mohammi.com:7443"
echo "   • curl: curl -I https://mohammi.com:8914"
echo ""
echo "📁 ملفات الشهادة:"
echo "   • الشهادة: /etc/ssl/mohammi/mohammi_com.crt"
echo "   • المفتاح الخاص: /etc/ssl/mohammi/mohammi_com.key"
echo "   • الشهادة المجمعة: /etc/ssl/mohammi/fullchain.crt"
echo ""
echo "🔌 المنافذ المفتوحة:"
echo "   • 7443 (HTTPS - mohammi)"
echo "   • 8914 (HTTPS - rubaie)"
echo "   • 7080 (HTTP redirect)"
echo "   • 8080 (HTTP redirect)"
echo ""
echo "⚠️ ملاحظات:"
echo "   1. المنفذ 443 محجوز لنظام آخر"
echo "   2. استخدم المنافذ المخصصة في الروابط"
echo "   3. تأكد من توجيه DNS للدومين إلى IP ***********"
echo "   4. يمكن إضافة proxy للتطبيقات لاحقاً"
echo ""
echo "✅ SSL جاهز للاستخدام على المنافذ المخصصة!"
