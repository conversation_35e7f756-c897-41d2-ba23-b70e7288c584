import { NextRequest, NextResponse } from 'next/server'
import { Client } from 'pg'

const dbConfig = {
  host: 'localhost',
  port: 5432,
  user: 'postgres',
  password: 'yemen123',
  database: 'mohammidev'
}

// GET - تشغيل نظام التنبيهات التلقائية
export async function GET(request: NextRequest) {
  const client = new Client(dbConfig)
  
  try {
    await client.connect()
    
    const notifications = []
    
    // 1. تنبيهات الجلسات القادمة
    const upcomingSessionsQuery = `
      SELECT 
        cs.*,
        c.case_number,
        c.title as case_title,
        c.client_id,
        c.assigned_lawyer_id,
        cl.name as client_name,
        cl.email as client_email,
        cl.phone as client_phone,
        e.name as lawyer_name,
        e.email as lawyer_email,
        e.phone as lawyer_phone
      FROM court_sessions cs
      JOIN cases c ON cs.case_id = c.id
      LEFT JOIN clients cl ON c.client_id = cl.id
      LEFT JOIN employees e ON c.assigned_lawyer_id = e.id
      WHERE cs.session_date BETWEEN NOW() AND NOW() + INTERVAL '7 days'
        AND cs.status = 'scheduled'
        AND (cs.client_reminder_sent = false OR cs.employee_reminder_sent = false)
    `
    
    const upcomingSessions = await client.query(upcomingSessionsQuery)
    
    for (const session of upcomingSessions.rows) {
      const sessionDate = new Date(session.session_date)
      const now = new Date()
      const daysUntilSession = Math.ceil((sessionDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
      
      // تنبيه العميل
      if (!session.client_reminder_sent && daysUntilSession <= session.reminder_days) {
        const clientNotification = {
          case_id: session.case_id,
          recipient_type: 'client',
          recipient_id: session.client_id,
          notification_type: 'session_reminder',
          title: `تذكير بموعد الجلسة - ${session.case_number}`,
          message: `عزيزي ${session.client_name}،\n\nنذكركم بموعد جلسة قضيتكم:\n\nرقم القضية: ${session.case_number}\nتاريخ الجلسة: ${sessionDate.toLocaleDateString('ar-SA')}\nالوقت: ${sessionDate.toLocaleTimeString('ar-SA')}\nالمحكمة: ${session.court_name}\n\nيرجى الحضور في الموعد المحدد.\n\nمع تحيات فريق المكتب`,
          scheduled_date: new Date(),
          delivery_method: 'email'
        }
        
        await client.query(`
          INSERT INTO case_notifications (
            case_id, recipient_type, recipient_id, notification_type,
            title, message, scheduled_date, delivery_method
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        `, [
          clientNotification.case_id,
          clientNotification.recipient_type,
          clientNotification.recipient_id,
          clientNotification.notification_type,
          clientNotification.title,
          clientNotification.message,
          clientNotification.scheduled_date,
          clientNotification.delivery_method
        ])
        
        // تحديث حالة الإرسال
        await client.query(
          'UPDATE court_sessions SET client_reminder_sent = true WHERE id = $1',
          [session.id]
        )
        
        notifications.push({ type: 'client_session_reminder', ...clientNotification })
      }
      
      // تنبيه الموظف
      if (!session.employee_reminder_sent && daysUntilSession <= session.reminder_days) {
        const employeeNotification = {
          case_id: session.case_id,
          recipient_type: 'employee',
          recipient_id: session.assigned_lawyer_id,
          notification_type: 'session_reminder',
          title: `تذكير بموعد الجلسة - ${session.case_number}`,
          message: `تذكير بموعد جلسة القضية:\n\nرقم القضية: ${session.case_number}\nعنوان القضية: ${session.case_title}\nالعميل: ${session.client_name}\nتاريخ الجلسة: ${sessionDate.toLocaleDateString('ar-SA')}\nالوقت: ${sessionDate.toLocaleTimeString('ar-SA')}\nالمحكمة: ${session.court_name}\nنوع الجلسة: ${session.session_type}`,
          scheduled_date: new Date(),
          delivery_method: 'email'
        }
        
        await client.query(`
          INSERT INTO case_notifications (
            case_id, recipient_type, recipient_id, notification_type,
            title, message, scheduled_date, delivery_method
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        `, [
          employeeNotification.case_id,
          employeeNotification.recipient_type,
          employeeNotification.recipient_id,
          employeeNotification.notification_type,
          employeeNotification.title,
          employeeNotification.message,
          employeeNotification.scheduled_date,
          employeeNotification.delivery_method
        ])
        
        // تحديث حالة الإرسال
        await client.query(
          'UPDATE court_sessions SET employee_reminder_sent = true WHERE id = $1',
          [session.id]
        )
        
        notifications.push({ type: 'employee_session_reminder', ...employeeNotification })
      }
    }
    
    // 2. تنبيهات القضايا المتوقفة
    const inactiveCasesQuery = `
      SELECT 
        c.*,
        cl.name as client_name,
        e.name as lawyer_name,
        EXTRACT(DAY FROM NOW() - c.updated_at) as days_inactive
      FROM cases c
      LEFT JOIN clients cl ON c.client_id = cl.id
      LEFT JOIN employees e ON c.assigned_lawyer_id = e.id
      WHERE c.status = 'active'
        AND c.updated_at < NOW() - INTERVAL '30 days'
        AND NOT EXISTS (
          SELECT 1 FROM case_notifications cn 
          WHERE cn.case_id = c.id 
            AND cn.notification_type = 'case_inactive_warning'
            AND cn.created_at > NOW() - INTERVAL '7 days'
        )
    `
    
    const inactiveCases = await client.query(inactiveCasesQuery)
    
    for (const caseItem of inactiveCases.rows) {
      const inactiveNotification = {
        case_id: caseItem.id,
        recipient_type: 'employee',
        recipient_id: caseItem.assigned_lawyer_id,
        notification_type: 'case_inactive_warning',
        title: `تنبيه: قضية متوقفة - ${caseItem.case_number}`,
        message: `تنبيه: القضية التالية لم يتم تحديثها منذ ${Math.floor(caseItem.days_inactive)} يوم:\n\nرقم القضية: ${caseItem.case_number}\nعنوان القضية: ${caseItem.title}\nالعميل: ${caseItem.client_name}\n\nيرجى مراجعة القضية واتخاذ الإجراء المناسب.`,
        scheduled_date: new Date(),
        delivery_method: 'email'
      }
      
      await client.query(`
        INSERT INTO case_notifications (
          case_id, recipient_type, recipient_id, notification_type,
          title, message, scheduled_date, delivery_method
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      `, [
        inactiveNotification.case_id,
        inactiveNotification.recipient_type,
        inactiveNotification.recipient_id,
        inactiveNotification.notification_type,
        inactiveNotification.title,
        inactiveNotification.message,
        inactiveNotification.scheduled_date,
        inactiveNotification.delivery_method
      ])
      
      notifications.push({ type: 'case_inactive_warning', ...inactiveNotification })
    }
    
    // 3. تنبيهات المواعيد النهائية
    const deadlineWarningsQuery = `
      SELECT 
        c.*,
        e.name as lawyer_name,
        EXTRACT(DAY FROM c.expected_end_date - NOW()) as days_until_deadline
      FROM cases c
      LEFT JOIN employees e ON c.assigned_lawyer_id = e.id
      WHERE c.status = 'active'
        AND c.expected_end_date IS NOT NULL
        AND c.expected_end_date BETWEEN NOW() AND NOW() + INTERVAL '7 days'
        AND NOT EXISTS (
          SELECT 1 FROM case_notifications cn 
          WHERE cn.case_id = c.id 
            AND cn.notification_type = 'deadline_warning'
            AND cn.created_at > NOW() - INTERVAL '3 days'
        )
    `
    
    const deadlineWarnings = await client.query(deadlineWarningsQuery)
    
    for (const caseItem of deadlineWarnings.rows) {
      const deadlineNotification = {
        case_id: caseItem.id,
        recipient_type: 'employee',
        recipient_id: caseItem.assigned_lawyer_id,
        notification_type: 'deadline_warning',
        title: `تحذير: اقتراب موعد نهائي - ${caseItem.case_number}`,
        message: `تحذير: يقترب الموعد النهائي للقضية:\n\nرقم القضية: ${caseItem.case_number}\nعنوان القضية: ${caseItem.title}\nالموعد النهائي: ${new Date(caseItem.expected_end_date).toLocaleDateString('ar-SA')}\nالأيام المتبقية: ${Math.ceil(caseItem.days_until_deadline)}\n\nيرجى اتخاذ الإجراءات اللازمة قبل انتهاء الموعد.`,
        scheduled_date: new Date(),
        delivery_method: 'email'
      }
      
      await client.query(`
        INSERT INTO case_notifications (
          case_id, recipient_type, recipient_id, notification_type,
          title, message, scheduled_date, delivery_method
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      `, [
        deadlineNotification.case_id,
        deadlineNotification.recipient_type,
        deadlineNotification.recipient_id,
        deadlineNotification.notification_type,
        deadlineNotification.title,
        deadlineNotification.message,
        deadlineNotification.scheduled_date,
        deadlineNotification.delivery_method
      ])
      
      notifications.push({ type: 'deadline_warning', ...deadlineNotification })
    }
    
    return NextResponse.json({
      success: true,
      data: {
        notifications_created: notifications.length,
        notifications: notifications,
        summary: {
          session_reminders: notifications.filter(n => n.type.includes('session_reminder')).length,
          inactive_warnings: notifications.filter(n => n.type === 'case_inactive_warning').length,
          deadline_warnings: notifications.filter(n => n.type === 'deadline_warning').length
        }
      },
      message: `تم إنشاء ${notifications.length} إشعار تلقائي`
    })
    
  } catch (error) {
    console.error('خطأ في نظام التنبيهات التلقائية:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في تشغيل نظام التنبيهات التلقائية'
    }, { status: 500 })
  } finally {
    await client.end()
  }
}

// POST - تخصيص إعدادات التنبيهات
export async function POST(request: NextRequest) {
  const client = new Client(dbConfig)
  
  try {
    await client.connect()
    
    const body = await request.json()
    const {
      session_reminder_days = 3,
      inactive_case_threshold = 30,
      deadline_warning_days = 7,
      auto_notifications_enabled = true
    } = body
    
    // تحديث إعدادات التنبيهات
    const settings = [
      { name: 'session_reminder_days', value: session_reminder_days.toString() },
      { name: 'inactive_case_threshold', value: inactive_case_threshold.toString() },
      { name: 'deadline_warning_days', value: deadline_warning_days.toString() },
      { name: 'auto_notifications_enabled', value: auto_notifications_enabled.toString() }
    ]
    
    for (const setting of settings) {
      await client.query(`
        INSERT INTO tracking_settings (setting_name, setting_value, setting_type, category)
        VALUES ($1, $2, 'number', 'notifications')
        ON CONFLICT (setting_name) 
        DO UPDATE SET setting_value = $2, updated_at = CURRENT_TIMESTAMP
      `, [setting.name, setting.value])
    }
    
    return NextResponse.json({
      success: true,
      message: 'تم تحديث إعدادات التنبيهات بنجاح'
    })
    
  } catch (error) {
    console.error('خطأ في تحديث إعدادات التنبيهات:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث إعدادات التنبيهات'
    }, { status: 500 })
  } finally {
    await client.end()
  }
}
