const { Client } = require('pg')

const DB_NAMES = (process.env.DB_NAMES ? process.env.DB_NAMES.split(',').map(s=>s.trim()).filter(Boolean) : ['mohammidev'])
const base = { host: 'localhost', port: 5432, user: 'postgres', password: 'yemen123' }

async function run(db){
  const client = new Client({ ...base, database: db })
  await client.connect()
  try {
    console.log(`\n=== Database: ${db} ===`)
    await client.query(`
      CREATE TABLE IF NOT EXISTS judges (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT NOW()
      );
    `)
    console.log('✅ ensured judges table')

    // Add judge_id to case_distribution
    const cdCols = await client.query(`SELECT column_name FROM information_schema.columns WHERE table_name='case_distribution'`)
    const cdHasJudge = cdCols.rows.some(r=>r.column_name==='judge_id')
    if(!cdHasJudge){
      await client.query(`ALTER TABLE case_distribution ADD COLUMN judge_id INTEGER NULL REFERENCES judges(id)`)
      console.log('✅ added judge_id to case_distribution')
    } else {
      console.log('ℹ️ judge_id exists in case_distribution')
    }

    // Add judge_id to follows
    await client.query(`
      CREATE TABLE IF NOT EXISTS follows (
        id SERIAL PRIMARY KEY,
        case_id INTEGER NOT NULL,
        service_id INTEGER NOT NULL,
        user_id INTEGER NOT NULL,
        report TEXT,
        date_field DATE NOT NULL DEFAULT CURRENT_DATE,
        status TEXT NOT NULL DEFAULT 'pending',
        next_hearing_id INTEGER NULL,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      );
    `)
    const fCols = await client.query(`SELECT column_name FROM information_schema.columns WHERE table_name='follows'`)
    const fHasJudge = fCols.rows.some(r=>r.column_name==='judge_id')
    if(!fHasJudge){
      await client.query(`ALTER TABLE follows ADD COLUMN judge_id INTEGER NULL REFERENCES judges(id)`)
      console.log('✅ added judge_id to follows')
    } else {
      console.log('ℹ️ judge_id exists in follows')
    }

    // Seed minimal judges if empty
    const count = await client.query('SELECT COUNT(*)::int as c FROM judges')
    if(count.rows[0].c === 0){
      await client.query(`INSERT INTO judges(name) VALUES ('غير محدد')`)
      console.log('🌱 seeded default judge (غير محدد)')
    }
  } finally {
    await client.end()
  }
}

;(async () => {
  for(const db of DB_NAMES){
    try{ await run(db) } catch(e){ console.error('❌ error on', db, e.message) }
  }
  console.log('\nDone.')
})()
