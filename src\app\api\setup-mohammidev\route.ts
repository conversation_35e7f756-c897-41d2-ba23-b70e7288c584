import { NextRequest, NextResponse } from 'next/server'
import { Client } from 'pg'

export async function POST() {
  try {
    console.log('🔧 إعداد جدول case_movements في قاعدة بيانات mohammidev...')

    const client = new Client({
      host: 'localhost',
      port: 5432,
      user: 'postgres',
      password: 'yemen123',
      database: 'mohammidev',
      connectTimeoutMillis: 10000
    })

    await client.connect()
    console.log('✅ متصل بقاعدة البيانات mohammidev')

    // 1. التحقق من وجود الجدول
    const tableExists = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'case_movements'
      );
    `)

    let tableCreated = false
    let dataAdded = false

    if (tableExists.rows[0].exists) {
      console.log('✅ جدول case_movements موجود بالفعل')
      
      // فحص البيانات الموجودة
      const countResult = await client.query('SELECT COUNT(*) as count FROM case_movements')
      const count = parseInt(countResult.rows[0].count)
      console.log(`📊 عدد السجلات الموجودة: ${count}`)
      
      if (count > 0) {
        await client.end()
        return NextResponse.json({
          success: true,
          message: 'جدول case_movements موجود ويحتوي على بيانات',
          details: {
            table_exists: true,
            records_count: count,
            action: 'no_action_needed'
          }
        })
      }
    } else {
      console.log('⚠️ جدول case_movements غير موجود، سيتم إنشاؤه...')
      
      // إنشاء الجدول
      await client.query(`
        CREATE TABLE case_movements (
          id SERIAL PRIMARY KEY,
          case_id INTEGER NOT NULL,
          case_number VARCHAR(50) NOT NULL,
          case_title VARCHAR(500) NOT NULL,
          movement_type VARCHAR(100) NOT NULL,
          description TEXT,
          created_by_id INTEGER,
          created_by_name VARCHAR(255),
          priority VARCHAR(20) DEFAULT 'normal',
          client_name VARCHAR(255),
          case_status VARCHAR(100),
          metadata JSONB,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          movement_date DATE DEFAULT CURRENT_DATE
        );
      `)
      
      console.log('✅ تم إنشاء جدول case_movements')
      tableCreated = true
      
      // إنشاء الفهارس
      await client.query(`
        CREATE INDEX idx_case_movements_case_id ON case_movements(case_id);
        CREATE INDEX idx_case_movements_case_number ON case_movements(case_number);
        CREATE INDEX idx_case_movements_movement_type ON case_movements(movement_type);
        CREATE INDEX idx_case_movements_priority ON case_movements(priority);
        CREATE INDEX idx_case_movements_created_at ON case_movements(created_at);
      `)
      
      console.log('✅ تم إنشاء الفهارس')
    }

    // إضافة بيانات تجريبية
    console.log('📝 إضافة بيانات تجريبية...')
    
    const sampleData = [
      {
        case_id: 1, case_number: '2024/001', case_title: 'قضية تجارية - شركة الأمل للتجارة',
        movement_type: 'case_created', description: 'تم إنشاء القضية في النظام',
        created_by_name: 'النظام', priority: 'normal', client_name: 'شركة الأمل للتجارة', case_status: 'نشطة'
      },
      {
        case_id: 1, case_number: '2024/001', case_title: 'قضية تجارية - شركة الأمل للتجارة',
        movement_type: 'case_assigned', description: 'تم توزيع القضية على المحامي أحمد محمد',
        created_by_name: 'المدير العام', priority: 'high', client_name: 'شركة الأمل للتجارة', case_status: 'موزعة'
      },
      {
        case_id: 2, case_number: '2024/002', case_title: 'قضية عمالية - محمد علي أحمد',
        movement_type: 'case_created', description: 'تم إنشاء القضية في النظام',
        created_by_name: 'النظام', priority: 'normal', client_name: 'محمد علي أحمد', case_status: 'نشطة'
      },
      {
        case_id: 2, case_number: '2024/002', case_title: 'قضية عمالية - محمد علي أحمد',
        movement_type: 'hearing_scheduled', description: 'تم تحديد جلسة للقضية في المحكمة العمالية بتاريخ 2024-12-15',
        created_by_name: 'أحمد محمد', priority: 'high', client_name: 'محمد علي أحمد', case_status: 'جلسة مجدولة'
      },
      {
        case_id: 3, case_number: '2024/003', case_title: 'قضية مدنية - فاطمة سالم محمد',
        movement_type: 'case_created', description: 'تم إنشاء القضية في النظام',
        created_by_name: 'النظام', priority: 'normal', client_name: 'فاطمة سالم محمد', case_status: 'نشطة'
      },
      {
        case_id: 3, case_number: '2024/003', case_title: 'قضية مدنية - فاطمة سالم محمد',
        movement_type: 'document_uploaded', description: 'تم رفع وثيقة "عقد الإيجار الأصلي" للقضية',
        created_by_name: 'سارة أحمد', priority: 'normal', client_name: 'فاطمة سالم محمد', case_status: 'قيد المراجعة'
      },
      {
        case_id: 4, case_number: '2024/004', case_title: 'قضية جنائية - خالد يوسف علي',
        movement_type: 'case_created', description: 'تم إنشاء القضية في النظام',
        created_by_name: 'النظام', priority: 'urgent', client_name: 'خالد يوسف علي', case_status: 'نشطة'
      },
      {
        case_id: 4, case_number: '2024/004', case_title: 'قضية جنائية - خالد يوسف علي',
        movement_type: 'follow_added', description: 'تم إضافة متابعة: تم تقديم الاستئناف للمحكمة العليا',
        created_by_name: 'محمد الحاشدي', priority: 'urgent', client_name: 'خالد يوسف علي', case_status: 'استئناف'
      },
      {
        case_id: 5, case_number: '2024/005', case_title: 'قضية إدارية - شركة النور للمقاولات',
        movement_type: 'case_created', description: 'تم إنشاء القضية في النظام',
        created_by_name: 'النظام', priority: 'normal', client_name: 'شركة النور للمقاولات', case_status: 'نشطة'
      },
      {
        case_id: 5, case_number: '2024/005', case_title: 'قضية إدارية - شركة النور للمقاولات',
        movement_type: 'case_status_changed', description: 'تم تغيير حالة القضية من "نشطة" إلى "قيد المراجعة"',
        created_by_name: 'أحمد محمد', priority: 'normal', client_name: 'شركة النور للمقاولات', case_status: 'قيد المراجعة'
      },
      {
        case_id: 6, case_number: '2024/006', case_title: 'قضية تأمينات - علي حسن محمد',
        movement_type: 'case_created', description: 'تم إنشاء القضية في النظام',
        created_by_name: 'النظام', priority: 'normal', client_name: 'علي حسن محمد', case_status: 'نشطة'
      },
      {
        case_id: 6, case_number: '2024/006', case_title: 'قضية تأمينات - علي حسن محمد',
        movement_type: 'client_meeting', description: 'تم عقد اجتماع مع العميل لمناقشة تفاصيل القضية',
        created_by_name: 'سارة أحمد', priority: 'normal', client_name: 'علي حسن محمد', case_status: 'قيد الدراسة'
      }
    ]

    for (const data of sampleData) {
      await client.query(`
        INSERT INTO case_movements (
          case_id, case_number, case_title, movement_type, description,
          created_by_name, priority, client_name, case_status,
          created_at, movement_date
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, 
          NOW() - INTERVAL '${Math.floor(Math.random() * 30)} days' - INTERVAL '${Math.floor(Math.random() * 24)} hours',
          (NOW() - INTERVAL '${Math.floor(Math.random() * 30)} days')::date
        )
      `, [
        data.case_id, data.case_number, data.case_title, data.movement_type,
        data.description, data.created_by_name, data.priority, data.client_name, data.case_status
      ])
    }

    console.log(`✅ تم إضافة ${sampleData.length} سجل تجريبي`)
    dataAdded = true

    // التحقق من النتيجة النهائية
    const finalCount = await client.query('SELECT COUNT(*) as count FROM case_movements')
    const totalRecords = parseInt(finalCount.rows[0].count)
    console.log(`📊 إجمالي السجلات في الجدول: ${totalRecords}`)

    // عرض عينة من البيانات
    const sampleResult = await client.query(`
      SELECT case_number, case_title, movement_type, priority, created_at 
      FROM case_movements 
      ORDER BY created_at DESC 
      LIMIT 5
    `)

    await client.end()

    return NextResponse.json({
      success: true,
      message: 'تم إعداد جدول case_movements بنجاح في قاعدة بيانات mohammidev',
      details: {
        table_created: tableCreated,
        data_added: dataAdded,
        total_records: totalRecords,
        sample_data: sampleResult.rows.map(row => ({
          case_number: row.case_number,
          movement_type: row.movement_type,
          priority: row.priority,
          created_at: row.created_at
        }))
      },
      next_steps: [
        'تم ربط النظام بقاعدة بيانات mohammidev',
        'يمكنك الآن الوصول لصفحة حركة القضايا',
        'البيانات التجريبية متاحة للاختبار',
        'النظام جاهز للاستخدام'
      ]
    })

  } catch (error) {
    console.error('❌ خطأ في إعداد قاعدة البيانات:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في إعداد قاعدة البيانات mohammidev',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
