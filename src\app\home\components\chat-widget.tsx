'use client';

import { useState, useRef, useEffect } from 'react';
import { MessageSquare, X, Send, User, Bot, AlertCircle, Loader2, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';

type Message = {
  id: string;
  content: string;
  sender: 'user' | 'assistant';
  timestamp: Date;
  status: 'sending' | 'delivered' | 'error';
};

interface ChatWidgetProps {
  isOpen: boolean;
  onClose: () => void;
  onOpen: () => void;
}

export function ChatWidget({ isOpen, onClose, onOpen }: ChatWidgetProps) {
  const [isMinimized, setIsMinimized] = useState(false);
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [error, setError] = useState('');
  const [conversationId, setConversationId] = useState('');
  const [guestId, setGuestId] = useState('');
  const [companyData, setCompanyData] = useState<any>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const [messages, setMessages] = useState<Message[]>([]);

  // إنشاء معرف زائر فريد وجلب بيانات الشركة
  useEffect(() => {
    let guestIdentifier = localStorage.getItem('guestId');
    if (!guestIdentifier) {
      guestIdentifier = `guest_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      localStorage.setItem('guestId', guestIdentifier);
    }
    setGuestId(guestIdentifier);

    // جلب بيانات الشركة
    fetchCompanyData();
  }, []);

  // جلب بيانات الشركة ورسالة الترحيب من المساعد الذكي
  const fetchCompanyData = async () => {
    try {
      // جلب رسالة الترحيب من المساعد الذكي (يقرأ من قاعدة البيانات المناسبة)
      const chatResponse = await fetch('/api/chat');
      if (chatResponse.ok) {
        const chatResult = await chatResponse.json();
        if (chatResult.success) {
          // استخدام رسالة الترحيب من المساعد الذكي
          const welcomeMessages: Message[] = [
            {
              id: '1',
              content: chatResult.data.welcomeMessage,
              sender: 'assistant',
              timestamp: new Date(),
              status: 'delivered'
            }
          ];

          setMessages(welcomeMessages);

          // حفظ بيانات الشركة
          if (chatResult.data.companyInfo) {
            setCompanyData(chatResult.data.companyInfo);
          }
          return;
        }
      }

      // في حالة فشل جلب رسالة المساعد، استخدم بيانات الشركة العادية
      const response = await fetch('/api/companies');
      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data.length > 0) {
          const company = result.data[0];
          setCompanyData(company);

          // إنشاء رسالة ترحيب افتراضية
          const welcomeMessages: Message[] = [
            {
              id: '1',
              content: `مرحباً بك في ${company.name}! 🏛️`,
              sender: 'assistant',
              timestamp: new Date(),
              status: 'delivered'
            }
          ];

          setMessages(welcomeMessages);
        }
      }
    } catch (error) {
      console.error('Error fetching company data:', error);
      // رسائل افتراضية في حالة فشل جلب البيانات
      const defaultMessages: Message[] = [
        {
          id: '1',
          content: 'مرحباً بك في مؤسسة الجرافي للمحاماة والاستشارات القانونية! 🏛️',
          sender: 'assistant',
          timestamp: new Date(),
          status: 'delivered'
        }
      ];
      setMessages(defaultMessages);
    }
  };

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Focus input when chat is opened
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!message.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      content: message,
      sender: 'user',
      timestamp: new Date(),
      status: 'delivered'
    };

    setMessages(prev => [...prev, userMessage]);
    setMessage('');
    setIsTyping(true);
    setError('');

    try {
      // إرسال الرسالة للذكاء الاصطناعي
      const response = await sendToAI(message, conversationId);

      // Update conversation ID if this is the first message
      if (!conversationId && response.conversationId) {
        setConversationId(response.conversationId);
      }

      // Add assistant's response
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: response.message,
        sender: 'assistant',
        timestamp: new Date(),
        status: 'delivered'
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (err) {
      setError('عذراً، حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى لاحقاً.');
      console.error('Chat error:', err);
    } finally {
      setIsTyping(false);
      // إعادة التركيز على مربع النص بعد الإرسال
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus();
        }
      }, 100);
    }
  };

  const sendToAI = async (userMessage: string, currentConversationId: string): Promise<{message: string, conversationId: string}> => {
    try {

      // إرسال الرسالة للذكاء الاصطناعي
      const response = await fetch('/api/ai/local-models', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage,
          model: 'groq-llama-8b', // استخدام Llama 3.1 8B (مجاني)
          conversationId: currentConversationId || `guest_${guestId}_${Date.now()}`,
          context: []
        })
      });

      if (!response.ok) {
        throw new Error('فشل في الحصول على رد من الذكاء الاصطناعي');
      }

      const result = await response.json();

      if (result.success && result.response) {
        return {
          message: result.response,
          conversationId: currentConversationId || `guest_${guestId}_${Date.now()}`
        };
      } else {
        throw new Error(result.error || 'خطأ في الاستجابة');
      }
    } catch (error) {
      console.error('AI Error:', error);
      // رد احتياطي في حالة فشل الذكاء الاصطناعي
      const fallbackResponses = [
        `أعتذر، أواجه مشكلة تقنية مؤقتة. للتواصل المباشر: ${companyData?.phone || '+967-1-123456'}`,
        'نعم، يمكنني مساعدتك في ذلك. هل يمكنك تقديم المزيد من التفاصيل حول استفسارك القانوني؟',
        'هذا سؤال مهم. بناءً على المعلومات المتوفرة، أنصحك باستشارة محامٍ متخصص للحصول على إجابة دقيقة.',
        'شكراً لسؤالك. للإجابة على استفسارك، أحتاج إلى بعض المعلومات الإضافية حول حالتك.',
        `حسب القوانين المعمول بها، فإن الإجابة تعتمد على عدة عوامل. للاستشارة المتخصصة اتصل بنا: ${companyData?.phone || '+967-1-123456'}`
      ];

      const randomResponse = fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)];

      return {
        message: randomResponse,
        conversationId: currentConversationId || `guest_${guestId}_${Date.now()}`
      };
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
  };

  if (!isOpen) {
    return (
      <div className="fixed bottom-6 left-6 z-50">
        <Button
          onClick={onOpen}
          className="h-16 w-16 rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 relative transform hover:scale-105 bg-blue-600 hover:bg-blue-700"
          title="المحادثات مع الذكاء الاصطناعي"
        >
          <Bot className="h-7 w-7 text-white" />
          <Badge className="absolute -top-2 -right-2 h-6 w-6 rounded-full bg-green-500 text-white text-xs flex items-center justify-center animate-pulse">
            <Sparkles className="h-3 w-3" />
          </Badge>
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-24 left-6 w-96 h-[500px] bg-white rounded-lg shadow-2xl border z-50 flex flex-col">
      {/* رأس النافذة */}
      <div className="flex items-center justify-between p-4 border-b bg-blue-600 text-white rounded-t-lg">
        <div className="flex items-center">
          <Bot className="h-5 w-5 mr-2" />
          <div>
            <h3 className="font-semibold text-sm">
              {companyData?.name || 'المحادثات'}
            </h3>
            <div className="flex items-center">
              <Badge className="bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                <Sparkles className="h-3 w-3 mr-1" />
                AI
              </Badge>
              <span className="text-xs text-blue-100 mr-2">متصل الآن</span>
            </div>
          </div>
        </div>
          <div className="flex items-center space-x-2 space-x-reverse">
            <button
              onClick={() => setIsMinimized(!isMinimized)}
              className="p-1 rounded-full hover:bg-white/20"
              aria-label={isMinimized ? 'تكبير' : 'تصغير'}
            >
              {isMinimized ? (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                </svg>
              ) : (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              )}
            </button>
            <button
              onClick={onClose}
              className="p-1 rounded-full hover:bg-white/20"
              aria-label="إغلاق"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {!isMinimized && (
          <>
            {/* Chat Messages */}
            <div className="flex-1 p-4 overflow-y-auto bg-gray-50">
              <div className="space-y-4">
                {messages.map((msg) => (
                  <div
                    key={msg.id}
                    className={`flex ${msg.sender === 'user' ? 'justify-start' : 'justify-end'}`}
                  >
                    <div
                      className={`max-w-[80%] rounded-2xl p-3 ${msg.sender === 'user'
                        ? 'bg-blue-600 text-white rounded-tr-none'
                        : 'bg-white border border-gray-200 text-gray-800 rounded-tl-none'}`}
                    >
                      <div className="text-sm">{msg.content}</div>
                      <div className={`text-xs mt-1 flex items-center ${msg.sender === 'user' ? 'text-blue-100 justify-end' : 'text-gray-500 justify-start'}`}>
                        {formatTime(msg.timestamp)}
                        {msg.status === 'sending' && (
                          <Loader2 className="w-3 h-3 animate-spin mr-1" />
                        )}
                        {msg.status === 'error' && (
                          <AlertCircle className="w-3 h-3 text-red-400 mr-1" />
                        )}
                      </div>
                    </div>
                  </div>
                ))}
                {isTyping && (
                  <div className="flex justify-end">
                    <div className="bg-white border border-gray-200 rounded-2xl rounded-tl-none p-3 max-w-[80%]">
                      <div className="flex space-x-1 space-x-reverse">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                      </div>
                    </div>
                  </div>
                )}
                {error && (
                  <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start">
                    <AlertCircle className="w-5 h-5 ml-2 mt-0.5 flex-shrink-0" />
                    <div>{error}</div>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>
            </div>

            {/* Chat Input */}
            <div className="border-t border-gray-200 p-4 bg-white">
              <form onSubmit={handleSendMessage} className="flex items-end gap-2">
                <div className="flex-1 relative">
                  <Input
                    ref={inputRef}
                    type="text"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder="اكتب رسالتك هنا..."
                    className="w-full pr-10"
                    dir="rtl"
                    disabled={isTyping}
                  />
                  <button
                    type="button"
                    className="absolute left-3 bottom-2.5 text-gray-400 hover:text-gray-600"
                    onClick={() => {
                      // Handle attachment click
                    }}
                    disabled={isTyping}
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                    </svg>
                  </button>
                </div>
                <Button
                  type="submit"
                  className="bg-blue-600 hover:bg-blue-700 text-white p-2 h-10 w-10 flex-shrink-0"
                  disabled={!message.trim() || isTyping}
                >
                  {isTyping ? (
                    <Loader2 className="w-5 h-5 animate-spin" />
                  ) : (
                    <Send className="w-5 h-5" />
                  )}
                </Button>
              </form>
              <p className="text-xs text-gray-500 mt-2 text-center">
                قد يتم تسجيل المحادثة لأغراض تحسين الجودة والتدريب
              </p>

              {/* معلومات التواصل */}
              {companyData && (
                <div className="mt-4 p-3 bg-blue-50 rounded-lg border-t">
                  <h4 className="text-sm font-semibold text-blue-800 mb-2 flex items-center">
                    <Bot className="h-4 w-4 mr-1" />
                    معلومات التواصل
                  </h4>
                  <div className="space-y-2 text-xs text-blue-700">
                    <div className="flex items-center">
                      <span className="font-medium">📍 العنوان:</span>
                      <span className="mr-2">{companyData.address}, {companyData.city}</span>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium">📞 الهاتف:</span>
                      <a href={`tel:${companyData.phone}`} className="mr-2 text-blue-600 hover:underline">
                        {companyData.phone}
                      </a>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium">📧 البريد:</span>
                      <a href={`mailto:${companyData.email}`} className="mr-2 text-blue-600 hover:underline">
                        {companyData.email}
                      </a>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium">⏰ ساعات العمل:</span>
                      <span className="mr-2">الأحد - الخميس: 8 صباحاً - 4 مساءً</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </>
        )}
      </div>
  );
}
