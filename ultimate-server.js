/**
 * الخادم النهائي - حل جذري لجميع مشاكل الخادم
 * - يعمل على جميع واجهات الشبكة (0.0.0.0)
 * - يدعم HTTP و HTTPS
 * - يتعامل مع جميع أنواع الطلبات
 * - يسجل جميع الأخطاء
 * - يعيد التشغيل التلقائي
 */

const http = require('http');
const https = require('https');
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// إعدادات الخادم
const CONFIG = {
  ports: {
    7443: { name: 'محمد', nextPort: 3000, database: 'mohammi' },
    8914: { name: 'الربعي', nextPort: 3001, database: 'rubaie' }
  },
  host: '0.0.0.0', // مهم جداً: للوصول الخارجي
  ssl: {
    enabled: false, // سنفعله لاحقاً
    keyPath: 'D:\\server\\correct_private_key.key',
    certPath: 'D:\\server\\mohammi_com.crt'
  }
};

// متغيرات عامة
const nextProcesses = new Map();
let logFile = path.join(__dirname, 'ultimate-server.log');

// دالة التسجيل
function log(message, type = 'INFO') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${type}] ${message}\n`;
  
  console.log(logMessage.trim());
  
  try {
    fs.appendFileSync(logFile, logMessage);
  } catch (error) {
    console.error('خطأ في كتابة السجل:', error.message);
  }
}

// دالة بدء Next.js
function startNextJS(port, name) {
  if (nextProcesses.has(port)) {
    log(`Next.js للمنفذ ${port} يعمل بالفعل`, 'WARN');
    return;
  }

  log(`بدء تشغيل Next.js للمنفذ ${port} (${name})`);
  
  const nextProcess = spawn('npx', ['next', 'dev', '-p', port.toString()], {
    cwd: __dirname,
    stdio: ['ignore', 'pipe', 'pipe']
  });

  nextProcess.stdout.on('data', (data) => {
    log(`Next.js ${port}: ${data.toString().trim()}`, 'NEXT');
  });

  nextProcess.stderr.on('data', (data) => {
    log(`Next.js ${port} ERROR: ${data.toString().trim()}`, 'ERROR');
  });

  nextProcess.on('close', (code) => {
    log(`Next.js ${port} توقف بالكود: ${code}`, 'WARN');
    nextProcesses.delete(port);
    
    // إعادة تشغيل تلقائي
    setTimeout(() => {
      log(`إعادة تشغيل Next.js ${port}`, 'INFO');
      startNextJS(port, name);
    }, 5000);
  });

  nextProcesses.set(port, nextProcess);
}

// دالة إنشاء الخادم الرئيسي
function createMainServer(port, config) {
  const server = http.createServer((req, res) => {
    // إعداد CORS للوصول الخارجي
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    if (req.method === 'OPTIONS') {
      res.writeHead(200);
      res.end();
      return;
    }

    // تسجيل الطلب
    log(`${req.method} ${req.url} من ${req.connection.remoteAddress}`, 'REQUEST');

    // توجيه الطلب إلى Next.js
    const proxyReq = http.request({
      hostname: 'localhost',
      port: config.nextPort,
      path: req.url,
      method: req.method,
      headers: req.headers
    }, (proxyRes) => {
      res.writeHead(proxyRes.statusCode, proxyRes.headers);
      proxyRes.pipe(res);
    });

    proxyReq.on('error', (error) => {
      log(`خطأ في التوجيه للمنفذ ${config.nextPort}: ${error.message}`, 'ERROR');
      res.writeHead(502, { 'Content-Type': 'text/html; charset=utf-8' });
      res.end(`
        <html>
          <head><title>خطأ في الخادم</title></head>
          <body style="font-family: Arial; text-align: center; padding: 50px;">
            <h1>🔧 الخادم قيد الصيانة</h1>
            <p>نظام ${config.name} غير متاح حالياً</p>
            <p>يرجى المحاولة بعد قليل</p>
            <hr>
            <small>المنفذ: ${port} | الوقت: ${new Date().toLocaleString('ar-SA')}</small>
          </body>
        </html>
      `);
    });

    req.pipe(proxyReq);
  });

  // ربط الخادم بجميع واجهات الشبكة
  server.listen(port, CONFIG.host, () => {
    log(`✅ خادم ${config.name} يعمل على المنفذ ${port} (جميع الواجهات)`);
    log(`🌐 الوصول المحلي: http://localhost:${port}`);
    log(`🌍 الوصول الخارجي: http://[your-ip]:${port}`);
  });

  server.on('error', (error) => {
    log(`❌ خطأ في خادم المنفذ ${port}: ${error.message}`, 'ERROR');
    
    if (error.code === 'EADDRINUSE') {
      log(`المنفذ ${port} مستخدم، محاولة إيقاف العملية المتداخلة`, 'WARN');
      // يمكن إضافة منطق لإيقاف العملية المتداخلة
    }
  });

  return server;
}

// دالة بدء جميع الخوادم
function startAllServers() {
  log('🚀 بدء تشغيل الخادم النهائي');
  log(`📍 المضيف: ${CONFIG.host} (جميع الواجهات)`);
  
  // بدء Next.js لكل منفذ
  Object.entries(CONFIG.ports).forEach(([port, config]) => {
    startNextJS(config.nextPort, config.name);
  });

  // انتظار قليل لبدء Next.js
  setTimeout(() => {
    // بدء الخوادم الرئيسية
    Object.entries(CONFIG.ports).forEach(([port, config]) => {
      createMainServer(parseInt(port), config);
    });
  }, 10000);
}

// دالة إيقاف جميع الخوادم
function stopAllServers() {
  log('🛑 إيقاف جميع الخوادم');
  
  nextProcesses.forEach((process, port) => {
    log(`إيقاف Next.js ${port}`);
    process.kill();
  });
  
  nextProcesses.clear();
}

// معالجة إشارات النظام
process.on('SIGINT', () => {
  log('تلقي إشارة إيقاف (SIGINT)');
  stopAllServers();
  process.exit(0);
});

process.on('SIGTERM', () => {
  log('تلقي إشارة إنهاء (SIGTERM)');
  stopAllServers();
  process.exit(0);
});

// معالجة الأخطاء غير المتوقعة
process.on('uncaughtException', (error) => {
  log(`خطأ غير متوقع: ${error.message}`, 'FATAL');
  log(`Stack: ${error.stack}`, 'FATAL');
});

process.on('unhandledRejection', (reason, promise) => {
  log(`Promise مرفوض: ${reason}`, 'FATAL');
});

// بدء الخادم
if (require.main === module) {
  startAllServers();
}

module.exports = { startAllServers, stopAllServers, CONFIG };
