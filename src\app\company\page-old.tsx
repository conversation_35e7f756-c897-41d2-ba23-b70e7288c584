'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  Building2,
  Edit,
  Save,
  X,
  Phone,
  Mail,
  MapPin,
  Globe,
  FileText,
  Calendar
} from 'lucide-react'

interface Company {
  id: number
  name: string
  legal_name: string
  registration_number: string
  tax_number: string
  address: string
  city: string
  country: string
  phone: string
  email: string
  website: string
  logo_url: string
  logo_right_text: string
  logo_left_text: string
  logo_image_url: string
  established_date: string
  legal_form: string
  capital: number
  description: string
  is_active: boolean
  created_date: string
}

export default function CompanyPage() {
  const [company, setCompany] = useState<Company | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [dbError, setDbError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [formData, setFormData] = useState({
    name: '',
    legal_name: '',
    registration_number: '',
    tax_number: '',
    address: '',
    city: '',
    country: '',
    phone: '',
    email: '',
    website: '',
    logo_url: '',
    logo_right_text: '',
    logo_left_text: '',
    logo_image_url: '',
    established_date: '',
    legal_form: '',
    capital: '',
    description: '',
    is_active: true
  })

  const fetchCompany = async () => {
    setIsLoading(true)
    setDbError(null)

    try {
      // جلب بيانات الشركة من قاعدة البيانات
      const response = await fetch('/api/company')
      const data = await response.json()

      if (data.success && data.data && data.data.length > 0) {
        // أخذ أول شركة من القائمة
        const companyData = data.data[0]
        setCompany(companyData)
        setFormData({
          name: companyData.name || '',
          legal_name: companyData.legal_name || '',
          registration_number: companyData.registration_number || '',
          tax_number: companyData.tax_number || '',
          address: companyData.address || '',
          city: companyData.city || '',
          country: companyData.country || '',
          phone: companyData.phone || '',
          email: companyData.email || '',
          website: companyData.website || '',
          logo_url: companyData.logo_url || '',
          logo_right_text: companyData.logo_right_text || '',
          logo_left_text: companyData.logo_left_text || '',
          logo_image_url: companyData.logo_image_url || '',
          established_date: companyData.established_date || '',
          legal_form: companyData.legal_form || '',
          capital: companyData.capital?.toString() || '',
          description: companyData.description || '',
          is_active: companyData.is_active !== undefined ? companyData.is_active : true
        })
      } else {
        // إذا لم توجد بيانات، إنشاء شركة افتراضية
        const defaultCompany = {
          name: 'مكتب المحاماة والاستشارات القانونية',
          legal_name: 'شركة المحاماة والاستشارات القانونية المحدودة',
          registration_number: 'CR-2024-001',
          tax_number: 'TAX-*********',
          address: 'شارع الزبيري، مبنى رقم 15، الطابق الثالث',
          city: 'صنعاء',
          country: 'اليمن',
          phone: '+967-1-123456',
          email: '<EMAIL>',
          website: 'www.legalfirm.ye',
          logo_url: '/images/company-logo.png',
          logo_right_text: 'مكتب المحاماة والاستشارات القانونية',
          logo_left_text: 'العدالة والنزاهة في خدمة القانون',
          logo_image_url: '/images/simple-logo.svg',
          established_date: '2020-01-15',
          legal_form: 'شركة محدودة المسؤولية',
          capital: 1000000,
          description: 'مكتب متخصص في تقديم الخدمات القانونية والاستشارات القانونية في جميع المجالات',
          is_active: true
        }

        // إنشاء الشركة في قاعدة البيانات
        const createResponse = await fetch('/api/company', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(defaultCompany)
        })

        if (createResponse.ok) {
          const createData = await createResponse.json()
          setCompany(createData.data)
          setFormData({
            name: createData.data.name,
            legal_name: createData.data.legal_name,
            registration_number: createData.data.registration_number,
            tax_number: createData.data.tax_number,
            address: createData.data.address,
            city: createData.data.city,
            country: createData.data.country,
            phone: createData.data.phone,
            email: createData.data.email,
            website: createData.data.website,
            logo_url: createData.data.logo_url,
            logo_right_text: createData.data.logo_right_text,
            logo_left_text: createData.data.logo_left_text,
            logo_image_url: createData.data.logo_image_url,
            established_date: createData.data.established_date,
            legal_form: createData.data.legal_form,
            capital: createData.data.capital?.toString() || '',
            description: createData.data.description,
            is_active: createData.data.is_active
          })
        }
      }
    } catch (error) {
      console.error('Error fetching company data:', error)
      setDbError('فشل في جلب بيانات الشركة')
      setCompany(null)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchCompany()
  }, [])

  const handleEdit = () => {
    setIsEditing(true)
  }

  const handleCancel = () => {
    setIsEditing(false)
    if (company) {
      setFormData({
        name: company.name,
        legal_name: company.legal_name,
        registration_number: company.registration_number,
        tax_number: company.tax_number,
        address: company.address,
        city: company.city,
        country: company.country,
        phone: company.phone,
        email: company.email,
        website: company.website,
        logo_url: company.logo_url,
        logo_right_text: company.logo_right_text,
        logo_left_text: company.logo_left_text,
        logo_image_url: company.logo_image_url,
        established_date: company.established_date,
        legal_form: company.legal_form,
        capital: company.capital ? company.capital.toString() : '',
        description: company.description,
        is_active: company.is_active
      })
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (company) {
      try {
        const updateData = {
          id: company.id,
          ...formData,
          capital: Number(formData.capital) || 0
        }

        const response = await fetch('/api/company', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(updateData)
        })

        const data = await response.json()

        if (data.success) {
          setCompany(data.data)
          setIsEditing(false)
          alert('تم تحديث بيانات الشركة بنجاح')
        } else {
          alert(`خطأ في تحديث البيانات: ${data.error}`)
        }
      } catch (error) {
        console.error('Error updating company:', error)
        alert('حدث خطأ أثناء تحديث البيانات')
      }
    }
  }

  const legalForms = [
    'شركة محدودة المسؤولية',
    'شركة مساهمة',
    'شركة تضامن',
    'مؤسسة فردية',
    'شركة توصية بسيطة',
    'شركة توصية بالأسهم'
  ]

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
        {/* Header Section */}
        <div className="bg-white border-b border-gray-200 shadow-sm">
          <div className="max-w-7xl mx-auto px-6 lg:px-8 py-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-6 space-x-reverse">
                {/* Company Logo */}
                {!isLoading && company && company.logo_image_url && (
                  <div className="flex-shrink-0">
                    <div className="relative">
                      <img
                        src={company.logo_image_url}
                        alt="شعار الشركة"
                        className="h-20 w-20 object-contain border-2 border-blue-100 rounded-2xl bg-white p-3 shadow-lg"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none'
                        }}
                      />
                      <div className="absolute -bottom-2 -right-2 bg-green-500 w-6 h-6 rounded-full border-2 border-white flex items-center justify-center">
                        <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    </div>
                  </div>
                )}

                <div>
                  <div className="flex items-center mb-2">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-4">
                      <Building2 className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h1 className="text-3xl font-bold text-gray-900">بيانات الشركة</h1>
                      <p className="text-gray-600">إدارة ومتابعة المعلومات الأساسية للشركة</p>
                    </div>
                  </div>
                  {!isLoading && company && (
                    <div className="flex items-center mt-3">
                      <div className="bg-blue-50 text-blue-700 px-4 py-2 rounded-lg text-sm font-medium">
                        {company.name}
                      </div>
                      <div className="bg-green-50 text-green-700 px-3 py-1 rounded-full text-xs font-medium mr-3">
                        نشط
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {!isEditing && (
                <Button
                  onClick={handleEdit}
                  className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
                >
                  <Edit className="h-5 w-5 mr-2" />
                  تعديل البيانات
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Content Section */}
        <div className="max-w-7xl mx-auto px-6 lg:px-8 py-8 space-y-8">

          {/* Error Message */}
          {dbError && (
            <div className="bg-red-50 border-l-4 border-red-400 rounded-xl p-6 shadow-lg">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
                    <svg className="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
                <div className="mr-4 flex-1">
                  <h3 className="text-lg font-semibold text-red-800 mb-2">خطأ في الاتصال بقاعدة البيانات</h3>
                  <p className="text-red-700 mb-4">{dbError}</p>
                  <Button
                    onClick={fetchCompany}
                    variant="outline"
                    className="border-red-300 text-red-700 hover:bg-red-50 rounded-lg"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    إعادة المحاولة
                  </Button>
                </div>
              </div>
            </div>
          )}

          {!dbError && !isLoading && company && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* معلومات أساسية */}
            <Card className="lg:col-span-2 shadow-xl border-0 bg-white rounded-2xl overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-blue-50 to-cyan-50 border-b border-blue-100 pb-6">
                <CardTitle className="flex items-center text-xl font-bold text-gray-900">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-3">
                    <Building2 className="h-5 w-5 text-white" />
                  </div>
                  المعلومات الأساسية للشركة
                </CardTitle>
                <p className="text-gray-600 mt-2">إدارة البيانات الأساسية والمعلومات القانونية للشركة</p>
              </CardHeader>
              <CardContent className="p-8">
                {isEditing ? (
                  <form onSubmit={handleSubmit} className="space-y-8">
                    {/* Basic Information Section */}
                    <div className="space-y-6">
                      <div className="border-b border-gray-200 pb-4">
                        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                          <div className="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center mr-2">
                            <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                            </svg>
                          </div>
                          المعلومات الأساسية
                        </h3>
                        <p className="text-gray-600 text-sm mt-1">البيانات الأساسية والقانونية للشركة</p>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                          <Label htmlFor="name" className="text-sm font-medium text-gray-700 flex items-center">
                            اسم الشركة التجاري *
                            <span className="text-red-500 mr-1">*</span>
                          </Label>
                          <Input
                            id="name"
                            value={formData.name}
                            onChange={(e) => setFormData({...formData, name: e.target.value})}
                            required
                            className="border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 transition-all duration-300"
                            placeholder="أدخل اسم الشركة التجاري"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="legal_name" className="text-sm font-medium text-gray-700 flex items-center">
                            الاسم القانوني *
                            <span className="text-red-500 mr-1">*</span>
                          </Label>
                          <Input
                            id="legal_name"
                            value={formData.legal_name}
                            onChange={(e) => setFormData({...formData, legal_name: e.target.value})}
                            required
                            className="border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 transition-all duration-300"
                            placeholder="أدخل الاسم القانوني للشركة"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="registration_number" className="text-sm font-medium text-gray-700">
                            رقم التسجيل التجاري
                          </Label>
                          <Input
                            id="registration_number"
                            value={formData.registration_number}
                            onChange={(e) => setFormData({...formData, registration_number: e.target.value})}
                            className="border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 transition-all duration-300"
                            placeholder="أدخل رقم التسجيل التجاري"
                          />
                        </div>
                      <div>
                        <Label htmlFor="tax_number">الرقم الضريبي</Label>
                        <Input
                          id="tax_number"
                          value={formData.tax_number}
                          onChange={(e) => setFormData({...formData, tax_number: e.target.value})}
                        />
                      </div>
                      <div>
                        <Label htmlFor="legal_form">الشكل القانوني</Label>
                        <select
                          id="legal_form"
                          value={formData.legal_form}
                          onChange={(e) => setFormData({...formData, legal_form: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md"
                        >
                          <option value="">اختر الشكل القانوني</option>
                          {legalForms.map((form) => (
                            <option key={form} value={form}>{form}</option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <Label htmlFor="established_date">تاريخ التأسيس</Label>
                        <Input
                          id="established_date"
                          type="date"
                          value={formData.established_date}
                          onChange={(e) => setFormData({...formData, established_date: e.target.value})}
                        />
                      </div>
                      <div>
                        <Label htmlFor="capital">رأس المال</Label>
                        <Input
                          id="capital"
                          type="number"
                          value={formData.capital}
                          onChange={(e) => setFormData({...formData, capital: e.target.value})}
                        />
                      </div>
                    </div>

                    {/* حقول الشعارات */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium text-gray-900">معلومات الشعار</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="logo_right_text">شعار يمين (نص طويل)</Label>
                          <textarea
                            id="logo_right_text"
                            value={formData.logo_right_text}
                            onChange={(e) => setFormData({...formData, logo_right_text: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md"
                            rows={3}
                            placeholder="النص الذي يظهر على يمين الشعار"
                          />
                        </div>
                        <div>
                          <Label htmlFor="logo_left_text">شعار يسار (نص طويل)</Label>
                          <textarea
                            id="logo_left_text"
                            value={formData.logo_left_text}
                            onChange={(e) => setFormData({...formData, logo_left_text: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md"
                            rows={3}
                            placeholder="النص الذي يظهر على يسار الشعار"
                          />
                        </div>
                        <div className="md:col-span-2">
                          <Label htmlFor="logo_image_url">رابط صورة الشعار</Label>
                          <Input
                            id="logo_image_url"
                            value={formData.logo_image_url}
                            onChange={(e) => setFormData({...formData, logo_image_url: e.target.value})}
                            placeholder="/images/simple-logo.svg"
                          />
                          {/* معاينة الشعار */}
                          {formData.logo_image_url && (
                            <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                              <p className="text-sm text-gray-600 mb-2">معاينة الشعار:</p>
                              <div className="flex justify-center">
                                <img
                                  src={formData.logo_image_url}
                                  alt="معاينة الشعار"
                                  className="h-16 w-16 object-contain border border-gray-200 rounded-lg bg-white p-2"
                                  onError={(e) => {
                                    e.currentTarget.style.display = 'none'
                                  }}
                                />
                              </div>
                            </div>
                          )}
                        </div>
                        <div>
                          <Label htmlFor="logo_url">رابط الشعار القديم</Label>
                          <Input
                            id="logo_url"
                            value={formData.logo_url}
                            onChange={(e) => setFormData({...formData, logo_url: e.target.value})}
                            placeholder="/images/company-logo.png"
                          />
                        </div>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="description">وصف النشاط</Label>
                      <textarea
                        id="description"
                        value={formData.description}
                        onChange={(e) => setFormData({...formData, description: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                        rows={3}
                      />
                    </div>

                    <div className="flex space-x-3 space-x-reverse">
                      <Button type="submit" className="flex-1">
                        <Save className="h-4 w-4 mr-2" />
                        حفظ التغييرات
                      </Button>
                      <Button type="button" variant="outline" onClick={handleCancel} className="flex-1">
                        <X className="h-4 w-4 mr-2" />
                        إلغاء
                      </Button>
                    </div>
                  </form>
                ) : (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label>اسم الشركة التجاري</Label>
                        <p className="mt-1 p-2 bg-gray-50 rounded font-medium">{company.name}</p>
                      </div>
                      <div>
                        <Label>الاسم القانوني</Label>
                        <p className="mt-1 p-2 bg-gray-50 rounded">{company.legal_name}</p>
                      </div>
                      <div>
                        <Label>رقم التسجيل</Label>
                        <p className="mt-1 p-2 bg-gray-50 rounded flex items-center">
                          <FileText className="h-4 w-4 mr-2 text-gray-400" />
                          {company.registration_number}
                        </p>
                      </div>
                      <div>
                        <Label>الرقم الضريبي</Label>
                        <p className="mt-1 p-2 bg-gray-50 rounded">{company.tax_number}</p>
                      </div>
                      <div>
                        <Label>الشكل القانوني</Label>
                        <p className="mt-1 p-2 bg-gray-50 rounded">{company.legal_form}</p>
                      </div>
                      <div>
                        <Label>تاريخ التأسيس</Label>
                        <p className="mt-1 p-2 bg-gray-50 rounded flex items-center">
                          <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                          {company.established_date}
                        </p>
                      </div>
                      <div>
                        <Label>رأس المال</Label>
                        <p className="mt-1 p-2 bg-gray-50 rounded font-medium">
                          {company.capital ? Number(company.capital).toLocaleString() : '0'} ريال
                        </p>
                      </div>
                      <div>
                        <Label>الحالة</Label>
                        <div className="mt-1">
                          <Badge className={company.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                            {company.is_active ? 'نشطة' : 'غير نشطة'}
                          </Badge>
                        </div>
                      </div>
                    </div>

                    {/* عرض معلومات الشعار */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium text-gray-900">معلومات الشعار</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label>شعار يمين</Label>
                          <p className="mt-1 p-2 bg-gray-50 rounded text-sm">
                            {company.logo_right_text || 'غير محدد'}
                          </p>
                        </div>
                        <div>
                          <Label>شعار يسار</Label>
                          <p className="mt-1 p-2 bg-gray-50 rounded text-sm">
                            {company.logo_left_text || 'غير محدد'}
                          </p>
                        </div>
                        <div className="md:col-span-2">
                          <Label>صورة الشعار</Label>
                          <div className="mt-1 p-4 bg-gray-50 rounded">
                            {company.logo_image_url ? (
                              <div className="space-y-3">
                                <div className="flex justify-center">
                                  <img
                                    src={company.logo_image_url}
                                    alt="شعار الشركة"
                                    className="h-24 w-24 object-contain border border-gray-200 rounded-lg bg-white p-2"
                                    onError={(e) => {
                                      e.currentTarget.style.display = 'none'
                                    }}
                                  />
                                </div>
                                <div className="text-center">
                                  <span className="text-sm text-blue-600 break-all">{company.logo_image_url}</span>
                                </div>
                              </div>
                            ) : (
                              <div className="text-center py-8">
                                <div className="h-24 w-24 mx-auto bg-gray-200 rounded-lg flex items-center justify-center">
                                  <svg className="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                  </svg>
                                </div>
                                <span className="text-sm text-gray-500 mt-2 block">لا توجد صورة شعار</span>
                              </div>
                            )}
                          </div>
                        </div>
                        <div>
                          <Label>رابط الشعار القديم</Label>
                          <p className="mt-1 p-2 bg-gray-50 rounded text-sm">
                            {company.logo_url || 'غير محدد'}
                          </p>
                        </div>
                      </div>
                    </div>

                    <div>
                      <Label>وصف النشاط</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{company.description}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* معلومات الاتصال */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Phone className="h-5 w-5 mr-2" />
                  معلومات الاتصال
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isEditing ? (
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="phone">الهاتف</Label>
                      <Input
                        id="phone"
                        value={formData.phone}
                        onChange={(e) => setFormData({...formData, phone: e.target.value})}
                      />
                    </div>
                    <div>
                      <Label htmlFor="email">البريد الإلكتروني</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => setFormData({...formData, email: e.target.value})}
                      />
                    </div>
                    <div>
                      <Label htmlFor="website">الموقع الإلكتروني</Label>
                      <Input
                        id="website"
                        value={formData.website}
                        onChange={(e) => setFormData({...formData, website: e.target.value})}
                      />
                    </div>
                    <div>
                      <Label htmlFor="address">العنوان</Label>
                      <Input
                        id="address"
                        value={formData.address}
                        onChange={(e) => setFormData({...formData, address: e.target.value})}
                      />
                    </div>
                    <div>
                      <Label htmlFor="city">المدينة</Label>
                      <Input
                        id="city"
                        value={formData.city}
                        onChange={(e) => setFormData({...formData, city: e.target.value})}
                      />
                    </div>
                    <div>
                      <Label htmlFor="country">البلد</Label>
                      <Input
                        id="country"
                        value={formData.country}
                        onChange={(e) => setFormData({...formData, country: e.target.value})}
                      />
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="flex items-center p-2 bg-gray-50 rounded">
                      <Phone className="h-4 w-4 mr-2 text-gray-400" />
                      <span>{company.phone}</span>
                    </div>
                    <div className="flex items-center p-2 bg-gray-50 rounded">
                      <Mail className="h-4 w-4 mr-2 text-gray-400" />
                      <span>{company.email}</span>
                    </div>
                    <div className="flex items-center p-2 bg-gray-50 rounded">
                      <Globe className="h-4 w-4 mr-2 text-gray-400" />
                      <span>{company.website}</span>
                    </div>
                    <div className="flex items-start p-2 bg-gray-50 rounded">
                      <MapPin className="h-4 w-4 mr-2 text-gray-400 mt-0.5" />
                      <div>
                        <p>{company.address}</p>
                        <p className="text-sm text-gray-600">{company.city}, {company.country}</p>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* بطاقة عرض الشعار */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <svg className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  شعار الشركة
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  {company.logo_image_url ? (
                    <div className="space-y-4">
                      <div className="flex justify-center">
                        <img
                          src={company.logo_image_url}
                          alt="شعار الشركة"
                          className="h-32 w-32 object-contain border border-gray-200 rounded-lg bg-white p-4"
                          onError={(e) => {
                            e.currentTarget.style.display = 'none'
                          }}
                        />
                      </div>
                      <div className="text-sm text-gray-600">
                        <p className="font-medium mb-1">مسار الصورة:</p>
                        <p className="break-all bg-gray-50 p-2 rounded text-xs">{company.logo_image_url}</p>
                      </div>
                    </div>
                  ) : (
                    <div className="py-8">
                      <div className="h-32 w-32 mx-auto bg-gray-100 rounded-lg flex items-center justify-center mb-4">
                        <svg className="h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <p className="text-gray-500 text-sm">لا يوجد شعار محدد</p>
                      <p className="text-gray-400 text-xs mt-1">يمكنك إضافة شعار من خلال تعديل البيانات</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {!dbError && !isLoading && !company && (
          <Card>
            <CardContent className="p-8">
              <div className="text-center">
                <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد بيانات شركة</h3>
                <p className="text-gray-600">لم يتم العثور على بيانات الشركة في النظام</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </MainLayout>
  )
}
