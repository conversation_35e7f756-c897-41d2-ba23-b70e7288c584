import { NextRequest, NextResponse } from 'next/server';
import { getDatabaseName, getCompanyName, getThemeColor, getWelcomeMessage, getNotificationPrefix, getFullDatabaseInfo } from '@/lib/database-router';

export async function GET(request: NextRequest) {
  try {

    // استخدام الدالة الجديدة للحصول على البيانات من قاعدة البيانات
    const databaseInfo = await getFullDatabaseInfo();
    
    const response = {
      ...databaseInfo,
      success: true
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('❌ خطأ في API معلومات قاعدة البيانات:', error);
    
    // محاولة أخيرة باستخدام الدوال القديمة
    try {
      const fallbackInfo = {
        database: getDatabaseName(),
        company: getCompanyName(),
        themeColor: getThemeColor(),
        welcomeMessage: getWelcomeMessage(),
        notificationPrefix: getNotificationPrefix(),
        port: process.env.PORT || 'غير محدد',
        timestamp: new Date().toISOString(),
        success: true,
        note: 'استخدم البيانات الاحتياطية'
      };

      return NextResponse.json(fallbackInfo);
    } catch (fallbackError) {
      console.error('❌ فشلت حتى البيانات الاحتياطية:', fallbackError);
      
      return NextResponse.json({
        success: false,
        error: 'فشل في الحصول على معلومات قاعدة البيانات',
        details: error.message,
        timestamp: new Date().toISOString()
      }, { status: 500 });
    }
  }
}

export async function POST(request: NextRequest) {
  return NextResponse.json({
    message: 'يدعم فقط طلبات GET',
    method: 'GET'
  }, { status: 405 });
}