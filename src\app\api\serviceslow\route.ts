import { NextRequest, NextResponse } from 'next/server'
import { query, getDatabaseName } from '@/lib/database-router'

// GET - جلب جميع الخدمات للموقع الرئيسي
export async function GET(request: NextRequest) {
  try {

    const { searchParams } = new URL(request.url)
    const active = searchParams.get('active')

    let sql = 'SELECT * FROM serviceslow'
    const params: any[] = []

    if (active === 'true') {
      sql += ' WHERE is_active = $1'
      params.push(true)
    }

    sql += ' ORDER BY sort_order ASC, created_date DESC'

    const result = await query(sql, params)

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('خطأ في جلب الخدمات:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الخدمات' },
      { status: 500 }
    )
  }
}

// POST - إضافة خدمة جديدة للموقع الرئيسي
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      title,
      slug,
      description,
      content,
      icon_name = 'Scale',
      icon_color = '#2563eb',
      image_url,
      is_active = true,
      sort_order = 0,
      meta_title,
      meta_description
    } = body

    if (!title || !slug) {
      return NextResponse.json(
        { success: false, error: 'العنوان والرابط مطلوبان' },
        { status: 400 }
      )
    }

    // التحقق من عدم تكرار الرابط
    const existingService = await query(
      'SELECT id FROM serviceslow WHERE slug = $1',
      [slug]
    )

    if (existingService.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'الرابط موجود مسبقاً' },
        { status: 400 }
      )
    }

    const result = await query(`
      INSERT INTO serviceslow (
        title, slug, description, content, icon_name, icon_color,
        image_url, is_active, sort_order, meta_title, meta_description
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING *
    `, [
      title, slug, description, content, icon_name, icon_color,
      image_url, is_active, sort_order, meta_title, meta_description
    ])

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم إضافة الخدمة بنجاح'
    })
  } catch (error) {
    console.error('خطأ في إضافة الخدمة:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة الخدمة' },
      { status: 500 }
    )
  }
}

// PUT - تحديث جميع الخدمات
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { services } = body

    if (!Array.isArray(services)) {
      return NextResponse.json(
        { success: false, error: 'البيانات يجب أن تكون مصفوفة من الخدمات' },
        { status: 400 }
      )
    }

    // بدء معاملة قاعدة البيانات
    await query('BEGIN')

    try {
      for (const service of services) {
        const {
          id,
          title,
          slug,
          description,
          content,
          icon_name,
          icon_color,
          image_url,
          is_active,
          sort_order,
          meta_title,
          meta_description
        } = service

        if (!id || !title || !slug) {
          throw new Error('معرف الخدمة والعنوان والرابط مطلوبة')
        }

        await query(`
          UPDATE serviceslow SET
            title = $1,
            slug = $2,
            description = $3,
            content = $4,
            icon_name = $5,
            icon_color = $6,
            image_url = $7,
            is_active = $8,
            sort_order = $9,
            meta_title = $10,
            meta_description = $11,
            updated_at = CURRENT_TIMESTAMP
          WHERE id = $12
        `, [
          title, slug, description, content, icon_name, icon_color,
          image_url, is_active, sort_order, meta_title, meta_description, id
        ])
      }

      await query('COMMIT')

      return NextResponse.json({
        success: true,
        message: 'تم تحديث الخدمات بنجاح'
      })
    } catch (error) {
      await query('ROLLBACK')
      throw error
    }
  } catch (error) {
    console.error('خطأ في تحديث الخدمات:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الخدمات' },
      { status: 500 }
    )
  }
}
