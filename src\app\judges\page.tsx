'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Plus, Edit, Trash2 } from 'lucide-react'

interface Judge { id: number; name: string }

export default function JudgesPage() {
  const [judges, setJudges] = useState<Judge[]>([])
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [editing, setEditing] = useState<Judge | null>(null)
  const [name, setName] = useState('')
  const [error, setError] = useState<string | null>(null)

  const fetchJudges = async () => {
    try {
      const res = await fetch('/api/judges')
      const data = await res.json()
      if (data.success) setJudges(data.data || [])
    } catch (e) { console.error('Error fetching judges:', e) }
  }

  useEffect(() => { fetchJudges() }, [])

  const openAdd = () => { setEditing(null); setName(''); setIsModalOpen(true) }
  const openEdit = (j: Judge) => { setEditing(j); setName(j.name); setIsModalOpen(true) }

  const save = async () => {
    if (!name.trim()) { setError('اسم القاضي مطلوب'); return }
    setError(null); setIsSaving(true)
    try {
      if (editing) {
        const res = await fetch(`/api/judges?id=${editing.id}`, { method: 'PUT', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ name }) })
        const data = await res.json(); if (!data.success) throw new Error(data.error || 'فشل')
      } else {
        const res = await fetch('/api/judges', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ name }) })
        const data = await res.json(); if (!data.success) throw new Error(data.error || 'فشل')
      }
      setIsModalOpen(false)
      fetchJudges()
    } catch (e:any) { setError(e.message || 'فشل في الحفظ') } finally { setIsSaving(false) }
  }

  const remove = async (j: Judge) => {
    if (!confirm(`هل تريد حذف القاضي "${j.name}"؟`)) return
    try {
      const res = await fetch(`/api/judges?id=${j.id}`, { method: 'DELETE' })
      const data = await res.json()
      if (!data.success) { alert(data.error || 'فشل في الحذف'); return }
      fetchJudges()
    } catch (e) { alert('خطأ في الحذف') }
  }

  return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">إدارة القضاة</h1>
          <Button onClick={openAdd} className="bg-indigo-600 hover:bg-indigo-700 text-white">
            <Plus className="h-4 w-4 mr-2" /> إضافة قاضٍ جديد
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>القائمة ({judges.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b bg-gray-50">
                    <th className="text-right p-3 font-semibold">#</th>
                    <th className="text-right p-3 font-semibold">اسم القاضي</th>
                    <th className="text-center p-3 font-semibold">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {judges.map((j) => (
                    <tr key={j.id} className="border-b hover:bg-gray-50">
                      <td className="p-3">{j.id}</td>
                      <td className="p-3">{j.name}</td>
                      <td className="p-3">
                        <div className="flex justify-center gap-2">
                          <Button size="sm" variant="outline" onClick={() => openEdit(j)} className="text-blue-600 border-blue-200"> 
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => remove(j)} className="text-red-600 border-red-200">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {isModalOpen && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <h2 className="text-xl font-bold mb-4">{editing ? 'تعديل القاضي' : 'إضافة قاضٍ جديد'}</h2>
              <div className="space-y-2">
                <Label htmlFor="judge_name">اسم القاضي</Label>
                <Input id="judge_name" value={name} onChange={(e)=> setName(e.target.value)} placeholder="أدخل اسم القاضي" className="text-black" />
                {error && <div className="text-red-600 text-sm">{error}</div>}
              </div>
              <div className="flex justify-end gap-2 mt-6">
                <Button variant="outline" onClick={()=> setIsModalOpen(false)}>إلغاء</Button>
                <Button onClick={save} disabled={isSaving} className="bg-indigo-600 hover:bg-indigo-700 text-white">{isSaving ? 'جاري الحفظ...' : 'حفظ'}</Button>
              </div>
            </div>
          </div>
        )}

        {/* زر عائم لإضافة قاضٍ */}
        <button
          onClick={openAdd}
          className="fixed bottom-6 left-6 w-12 h-12 rounded-full bg-indigo-600 hover:bg-indigo-700 text-white flex items-center justify-center shadow-lg z-40"
          title="إضافة قاضٍ جديد"
          aria-label="إضافة قاضٍ جديد"
        >
          <Plus className="h-6 w-6" />
        </button>
      </div>
  )
}
