'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Link2, 
  RefreshCw, 
  CheckCircle, 
  AlertCircle,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Wallet,
  Users,
  UserCheck,
  Building,
  Settings,
  Plus
} from 'lucide-react'

interface AccountLinkingSetting {
  id: number
  setting_name: string
  setting_description: string
  account_code: string
  account_name: string
  account_type: string
  is_enabled: boolean
  is_created: boolean
  current_balance: number
  linked_table?: string
  auto_create_sub_accounts?: boolean
  icon_name: string
  color_class: string
}

export default function AccountLinkingPage() {
  const [settings, setSettings] = useState<AccountLinkingSetting[]>([])
  const [loading, setLoading] = useState(true)
  const [creating, setCreating] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)

  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/accounting/account-linking')
      const data = await response.json()
      
      if (data.success) {
        setSettings(data.data || [])
      } else {
        setMessage({
          type: 'error',
          text: data.error || 'فشل في جلب إعدادات الربط'
        })
      }
    } catch (error) {
      console.error('Error fetching settings:', error)
      setMessage({
        type: 'error',
        text: 'حدث خطأ في جلب البيانات'
      })
    } finally {
      setLoading(false)
    }
  }

  const createSystemAccounts = async () => {
    setCreating(true)
    setMessage(null)
    
    try {
      const response = await fetch('/api/accounting/account-linking/create-system-accounts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })

      const result = await response.json()

      if (result.success) {
        setMessage({
          type: 'success',
          text: 'تم إنشاء الحسابات الأساسية بنجاح'
        })
        fetchSettings() // إعادة جلب البيانات
      } else {
        setMessage({
          type: 'error',
          text: result.error || 'فشل في إنشاء الحسابات'
        })
      }
    } catch (error) {
      console.error('Error creating accounts:', error)
      setMessage({
        type: 'error',
        text: 'حدث خطأ في إنشاء الحسابات'
      })
    } finally {
      setCreating(false)
    }
  }

  const getIcon = (iconName: string) => {
    switch (iconName) {
      case 'TrendingUp':
        return <TrendingUp className="h-6 w-6" />
      case 'TrendingDown':
        return <TrendingDown className="h-6 w-6" />
      case 'Wallet':
        return <Wallet className="h-6 w-6" />
      case 'DollarSign':
        return <DollarSign className="h-6 w-6" />
      case 'Users':
        return <Users className="h-6 w-6" />
      case 'UserCheck':
        return <UserCheck className="h-6 w-6" />
      case 'Building':
        return <Building className="h-6 w-6" />
      default:
        return <Settings className="h-6 w-6" />
    }
  }

  const getStatusColor = (isCreated: boolean, isEnabled: boolean) => {
    if (isCreated && isEnabled) return 'text-green-600'
    if (isCreated && !isEnabled) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getStatusText = (isCreated: boolean, isEnabled: boolean) => {
    if (isCreated && isEnabled) return 'نشط'
    if (isCreated && !isEnabled) return 'موجود - غير مفعل'
    return 'غير موجود'
  }

  const formatBalance = (balance: number) => {
    return balance.toLocaleString('ar-SA', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })
  }

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
          <span className="mr-2">جاري التحميل...</span>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان والأزرار */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 space-x-reverse">
            <Link2 className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">ربط الحسابات</h1>
              <p className="text-gray-600">إدارة الحسابات الأساسية للنظام المحاسبي مع الربط التلقائي</p>
            </div>
          </div>
          <div className="flex space-x-2 space-x-reverse">
            <Button
              onClick={fetchSettings}
              variant="outline"
              disabled={loading}
            >
              <RefreshCw className="h-4 w-4 ml-2" />
              تحديث
            </Button>
            <Button
              onClick={createSystemAccounts}
              disabled={creating}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {creating ? (
                <RefreshCw className="h-4 w-4 ml-2 animate-spin" />
              ) : (
                <Plus className="h-4 w-4 ml-2" />
              )}
              إنشاء الحسابات الأساسية
            </Button>
          </div>
        </div>

        {/* رسائل التنبيه */}
        {message && (
          <Alert className={message.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
            {message.type === 'success' ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <AlertCircle className="h-4 w-4" />
            )}
            <AlertDescription className={message.type === 'success' ? 'text-green-800' : 'text-red-800'}>
              {message.text}
            </AlertDescription>
          </Alert>
        )}

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-blue-600">
                {settings.length}
              </div>
              <div className="text-sm text-gray-600">إجمالي الإعدادات</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-green-600">
                {settings.filter(s => s.is_created && s.is_enabled).length}
              </div>
              <div className="text-sm text-gray-600">الحسابات النشطة</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {settings.filter(s => s.is_created && !s.is_enabled).length}
              </div>
              <div className="text-sm text-gray-600">حسابات غير مفعلة</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-red-600">
                {settings.filter(s => !s.is_created).length}
              </div>
              <div className="text-sm text-gray-600">حسابات غير موجودة</div>
            </CardContent>
          </Card>
        </div>

        {/* جدول الحسابات */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Link2 className="h-5 w-5 ml-2" />
              الحسابات الأساسية للنظام ({settings.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {settings.length === 0 ? (
              <div className="text-center py-8">
                <Link2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">لا توجد إعدادات ربط</p>
                <p className="text-sm text-gray-400">انقر على "إنشاء الحسابات الأساسية" لبدء الإعداد</p>
              </div>
            ) : (
              <div className="space-y-4">
                {settings.map((setting) => (
                  <Card key={setting.id} className={`border-l-4 ${setting.color_class}`}>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4 space-x-reverse">
                          <div className={`p-3 rounded-lg ${setting.color_class.replace('border-l-', 'bg-').replace('-500', '-100')}`}>
                            {getIcon(setting.icon_name)}
                          </div>
                          <div>
                            <div className="font-medium text-lg">{setting.setting_name}</div>
                            <div className="text-sm text-gray-500 mb-1">{setting.setting_description}</div>
                            <div className="text-sm font-mono text-blue-600">
                              {setting.account_code} - {setting.account_name}
                            </div>
                            {setting.linked_table && (
                              <div className="text-xs text-purple-600 mt-1">
                                مربوط بجدول: {setting.linked_table}
                                {setting.auto_create_sub_accounts && ' (إنشاء تلقائي)'}
                              </div>
                            )}
                          </div>
                        </div>
                        
                        <div className="text-left space-y-2">
                          <Badge 
                            variant={setting.is_created && setting.is_enabled ? 'default' : 'secondary'}
                            className={getStatusColor(setting.is_created, setting.is_enabled)}
                          >
                            {getStatusText(setting.is_created, setting.is_enabled)}
                          </Badge>
                          
                          {setting.is_created && (
                            <div className="text-sm">
                              <div className="text-gray-500">الرصيد الحالي:</div>
                              <div className="font-mono font-medium">
                                {formatBalance(setting.current_balance)} ر.ي
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* معلومات إضافية */}
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-start space-x-3 space-x-reverse">
              <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h3 className="font-medium text-blue-900 mb-2">معلومات مهمة</h3>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• يتم إنشاء الحسابات الأساسية تلقائياً عند النقر على الزر أعلاه</li>
                  <li>• الحسابات المربوطة بالعملاء والموظفين تنشئ حسابات فرعية تلقائياً</li>
                  <li>• يمكن مراقبة حالة كل حساب والرصيد الحالي</li>
                  <li>• جميع الحسابات تتبع المعايير المحاسبية المعتمدة</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}