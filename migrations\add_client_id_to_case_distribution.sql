-- إضا<PERSON>ة عمود client_id إلى جدول case_distribution وربطه بجدول العملاء
-- يمكن تشغيل هذا الملف على قاعدة البيانات الهدف

BEGIN;

-- إض<PERSON><PERSON>ة العمود إن لم يكن موجوداً
ALTER TABLE case_distribution
  ADD COLUMN IF NOT EXISTS client_id INTEGER;

-- إنشاء فهرس لتحسين الاستعلامات
CREATE INDEX IF NOT EXISTS idx_case_distribution_client_id
  ON case_distribution (client_id);

-- إضافة/تأكيد القيد المرجعي إلى جدول العملاء
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints tc
    WHERE tc.table_name = 'case_distribution'
      AND tc.constraint_type = 'FOREIGN KEY'
      AND tc.constraint_name = 'fk_case_distribution_client_id'
  ) THEN
    ALTER TABLE case_distribution
      ADD CONSTRAINT fk_case_distribution_client_id
      FOREIGN KEY (client_id)
      REFERENCES clients(id)
      ON DELETE SET NULL;
  END IF;
END $$;

COMMIT;
