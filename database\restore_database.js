// استعادة قاعدة البيانات من النسخة الاحتياطية
const { Client } = require('pg');
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

async function restoreDatabase() {
  console.log('🔄 بدء عملية استعادة قاعدة البيانات...');

  // التحقق من وجود ملف النسخة الاحتياطية
  const backupFile = path.join(__dirname, '..', 'mohammi.sql');

  if (!fs.existsSync(backupFile)) {
    console.error('❌ ملف النسخة الاحتياطية غير موجود:', backupFile);
    return;
  }

  console.log('✅ تم العثور على ملف النسخة الاحتياطية');
  console.log('📁 المسار:', backupFile);

  try {
    // 1. إيقاف الاتصالات الحالية
    console.log('\n🔌 إنهاء الاتصالات الحالية...');

    const adminClient = new Client({
      host: 'localhost',
      port: 5432,
      database: 'postgres', // الاتصال بقاعدة postgres الافتراضية
      user: 'postgres',
      password: 'yemen123'
    });

    await adminClient.connect();

    // إنهاء جميع الاتصالات بقاعدة mohammi
    await adminClient.query(`
      SELECT pg_terminate_backend(pid)
      FROM pg_stat_activity
      WHERE datname = 'mohammi' AND pid <> pg_backend_pid()
    `);

    console.log('✅ تم إنهاء الاتصالات الحالية');

    await adminClient.end();

    // 2. استعادة قاعدة البيانات باستخدام psql
    console.log('\n📥 بدء استعادة قاعدة البيانات...');

    const restoreProcess = spawn('psql', [
      '-h', 'localhost',
      '-p', '5432',
      '-U', 'postgres',
      '-f', backupFile
    ], {
      env: { ...process.env, PGPASSWORD: 'yemen123' },
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';
    let errorOutput = '';

    restoreProcess.stdout.on('data', (data) => {
      output += data.toString();
    });

    restoreProcess.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    await new Promise((resolve, reject) => {
      restoreProcess.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`عملية الاستعادة فشلت برمز الخطأ: ${code}\nOutput: ${output}\nError: ${errorOutput}`));
        }
      });

      restoreProcess.on('error', (error) => {
        reject(error);
      });
    });

    console.log('✅ تم استعادة قاعدة البيانات بنجاح');

    // 3. التحقق من نجاح الاستعادة
    console.log('\n🔍 التحقق من نجاح الاستعادة...');

    const testClient = new Client({
      host: 'localhost',
      port: 5432,
      database: 'mohammi',
      user: 'postgres',
      password: 'yemen123'
    });

    await testClient.connect();

    // فحص الجداول
    const tablesResult = await testClient.query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      ORDER BY table_name
    `);

    console.log(`📊 عدد الجداول: ${tablesResult.rows.length}`);
    console.log('📋 الجداول الموجودة:');
    tablesResult.rows.forEach((row, index) => {
      console.log(`   ${index + 1}. ${row.table_name}`);
    });

    // فحص بيانات الشركة
    try {
      const companyResult = await testClient.query('SELECT * FROM companies LIMIT 1');
      if (companyResult.rows.length > 0) {
        const company = companyResult.rows[0];
        console.log('\n🏢 بيانات الشركة:');
        console.log(`   الاسم: ${company.name}`);
        console.log(`   البريد: ${company.email}`);
        console.log(`   الهاتف: ${company.phone}`);
      }
    } catch (error) {
      console.log('⚠️ لا يمكن الوصول لجدول companies');
    }

    // فحص المستخدمين
    try {
      const usersResult = await testClient.query('SELECT COUNT(*) as count FROM users');
      console.log(`\n👥 عدد المستخدمين: ${usersResult.rows[0].count}`);
    } catch (error) {
      console.log('⚠️ لا يمكن الوصول لجدول users');
    }

    await testClient.end();

    console.log('\n🎉 تمت استعادة قاعدة البيانات بنجاح!');
    console.log('✅ جميع الجداول والبيانات تم استعادتها');
    console.log('🔄 يمكنك الآن إعادة تشغيل النظام');

    // عرض معلومات مهمة
    console.log('\n📋 معلومات مهمة:');
    console.log('   - تم استعادة جميع الجداول والبيانات');
    console.log('   - قد تحتاج لإعادة إنشاء المستخدم admin');
    console.log('   - تأكد من إعادة تشغيل الخادم');

  } catch (error) {
    console.error('❌ خطأ في استعادة قاعدة البيانات:', error.message);
  }
}

restoreDatabase();
