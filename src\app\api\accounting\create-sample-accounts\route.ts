import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {

    // إنشاء جدول دليل الحسابات إذا لم يكن موجوداً
    await query(`
      CREATE TABLE IF NOT EXISTS chart_of_accounts (
        id SERIAL PRIMARY KEY,
        account_code VARCHAR(20) UNIQUE NOT NULL,
        account_name VARCHAR(255) NOT NULL,
        account_name_en VARCHAR(255),
        account_type VARCHAR(50) NOT NULL,
        parent_id INTEGER REFERENCES chart_of_accounts(id),
        account_level INTEGER DEFAULT 1,
        is_main_account BOOLEAN DEFAULT FALSE,
        is_sub_account BOOLEAN DEFAULT FALSE,
        is_control_account BOOLEAN DEFAULT FALSE,
        linked_table VARCHAR(100),
        auto_create_sub_accounts BOOLEAN DEFAULT FALSE,
        sub_account_prefix VARCHAR(10),
        account_nature VARCHAR(20) DEFAULT 'مدين',
        opening_balance DECIMAL(15,2) DEFAULT 0,
        current_balance DECIMAL(15,2) DEFAULT 0,
        allow_posting BOOLEAN DEFAULT TRUE,
        allow_transactions BOOLEAN DEFAULT TRUE,
        is_active BOOLEAN DEFAULT TRUE,
        description TEXT,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_linked_record BOOLEAN DEFAULT FALSE,
        original_table VARCHAR(100),
        linked_record_id INTEGER
      )
    `)

    // حذف البيانات الموجودة
    await query('DELETE FROM chart_of_accounts')

    // إنشاء الحسابات الرئيسية
    const accounts = [
      // الأصول
      { code: '********', name: 'الأصول', type: 'أصول', level: 1, nature: 'مدين' },
      { code: '********', name: 'الأصول المتداولة', type: 'أصول', level: 2, nature: 'مدين' },
      { code: '********', name: 'النقدية والبنوك', type: 'أصول', level: 3, nature: 'مدين' },
      { code: '********', name: 'الصندوق الرئيسي', type: 'أصول', level: 4, nature: 'مدين' },
      { code: '********', name: 'البنك الأهلي', type: 'أصول', level: 4, nature: 'مدين' },
      { code: '********', name: 'بنك الراجحي', type: 'أصول', level: 4, nature: 'مدين' },

      // العملاء
      { code: '********', name: 'المدينون', type: 'أصول', level: 2, nature: 'مدين' },
      { code: '********', name: 'العملاء', type: 'أصول', level: 3, nature: 'مدين', control: true, linked: 'clients' },

      // الأصول الثابتة
      { code: '13000000', name: 'الأصول الثابتة', type: 'أصول', level: 2, nature: 'مدين' },
      { code: '13010000', name: 'الأثاث والمعدات', type: 'أصول', level: 3, nature: 'مدين' },
      { code: '13010001', name: 'أثاث المكتب', type: 'أصول', level: 4, nature: 'مدين' },
      { code: '13010002', name: 'أجهزة الكمبيوتر', type: 'أصول', level: 4, nature: 'مدين' },

      // الخصوم
      { code: '20000000', name: 'الخصوم', type: 'خصوم', level: 1, nature: 'دائن' },
      { code: '21000000', name: 'الخصوم المتداولة', type: 'خصوم', level: 2, nature: 'دائن' },
      { code: '21010000', name: 'الموظفين', type: 'خصوم', level: 3, nature: 'دائن', control: true, linked: 'employees' },
      { code: '21020000', name: 'الموردين', type: 'خصوم', level: 3, nature: 'دائن', control: true, linked: 'suppliers' },
      { code: '21030000', name: 'الضرائب المستحقة', type: 'خصوم', level: 3, nature: 'دائن' },

      // حقوق الملكية
      { code: '30000000', name: 'حقوق الملكية', type: 'حقوق ملكية', level: 1, nature: 'دائن' },
      { code: '31000000', name: 'رأس المال', type: 'حقوق ملكية', level: 2, nature: 'دائن' },
      { code: '31010001', name: 'رأس المال المدفوع', type: 'حقوق ملكية', level: 3, nature: 'دائن' },
      { code: '32000000', name: 'الأرباح المحتجزة', type: 'حقوق ملكية', level: 2, nature: 'دائن' },

      // الإيرادات
      { code: '40000000', name: 'الإيرادات', type: 'إيرادات', level: 1, nature: 'دائن' },
      { code: '41000000', name: 'إيرادات الخدمات القانونية', type: 'إيرادات', level: 2, nature: 'دائن' },
      { code: '41010001', name: 'أتعاب الاستشارات', type: 'إيرادات', level: 3, nature: 'دائن' },
      { code: '41010002', name: 'أتعاب التقاضي', type: 'إيرادات', level: 3, nature: 'دائن' },
      { code: '41010003', name: 'أتعاب صياغة العقود', type: 'إيرادات', level: 3, nature: 'دائن' },

      // المصروفات
      { code: '50000000', name: 'المصروفات', type: 'مصروفات', level: 1, nature: 'مدين' },
      { code: '51000000', name: 'مصروفات التشغيل', type: 'مصروفات', level: 2, nature: 'مدين' },
      { code: '51010001', name: 'رواتب الموظفين', type: 'مصروفات', level: 3, nature: 'مدين' },
      { code: '51010002', name: 'إيجار المكتب', type: 'مصروفات', level: 3, nature: 'مدين' },
      { code: '********', name: 'الكهرباء والماء', type: 'مصروفات', level: 3, nature: 'مدين' },
      { code: '********', name: 'الاتصالات والإنترنت', type: 'مصروفات', level: 3, nature: 'مدين' },
      { code: '********', name: 'القرطاسية والمطبوعات', type: 'مصروفات', level: 3, nature: 'مدين' }
    ]

    let insertedCount = 0
    for (const account of accounts) {
      await query(`
        INSERT INTO chart_of_accounts (
          account_code, account_name, account_type, account_level,
          account_nature, is_control_account, linked_table,
          allow_posting, allow_transactions, is_active
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      `, [
        account.code,
        account.name,
        account.type,
        account.level,
        account.nature,
        account.control || false,
        account.linked || null,
        account.level === 4, // فقط المستوى الرابع يقبل قيود
        account.level === 4, // فقط المستوى الرابع يقبل معاملات
        true
      ])
      insertedCount++
    }

    return NextResponse.json({
      success: true,
      message: `تم إنشاء ${insertedCount} حساب تجريبي بنجاح`,
      accountsCreated: insertedCount
    })

  } catch (error) {
    console.error('❌ خطأ في إنشاء البيانات التجريبية:', error)
    return NextResponse.json({
      success: false,
      message: 'فشل في إنشاء البيانات التجريبية',
      error: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
