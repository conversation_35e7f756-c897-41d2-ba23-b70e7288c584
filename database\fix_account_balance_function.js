// إصلاح دالة get_account_balance
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function fixAccountBalanceFunction() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔧 إصلاح دالة get_account_balance...');
    await client.connect();

    // حذف الدالة القديمة
    await client.query('DROP FUNCTION IF EXISTS get_account_balance(VARCHAR)');

    // إنشاء الدالة الجديدة مع تصحيح المشكلة
    await client.query(`
      CREATE OR REPLACE FUNCTION get_account_balance(p_account_code VARCHAR)
      RETURNS DECIMAL(15,2) AS $$
      BEGIN
        RETURN (SELECT COALESCE(current_balance, 0) FROM chart_of_accounts WHERE account_code = p_account_code);
      END;
      $$ LANGUAGE plpgsql;
    `);

    // اختبار الدالة
    const testResult = await client.query("SELECT get_account_balance('1111') as balance");
    console.log(`✅ تم إصلاح الدالة بنجاح. اختبار: ${testResult.rows[0].balance}`);

  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await client.end();
  }
}

fixAccountBalanceFunction();