import { NextRequest, NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'

export async function POST(request: NextRequest) {
  try {
    const { connection } = await request.json()
    
    if (!connection) {
      return NextResponse.json({
        success: false,
        error: 'لم يتم توفير بيانات الاتصال'
      }, { status: 400 })
    }

    // قراءة ملف routing.config.json الحالي
    const configPath = path.join(process.cwd(), 'routing.config.json')
    let config = {}
    
    try {
      const configData = fs.readFileSync(configPath, 'utf8')
      config = JSON.parse(configData)
    } catch (error) {
      // إنشاء ملف جديد إذا لم يكن موجوداً
      config = {
        routes: {},
        default_config: {}
      }
    }

    // تحديث الإعدادات
    const updatedConfig = {
      ...config,
      default_config: {
        ...config.default_config,
        db_host: connection.host,
        db_port: connection.port,
        db_user: connection.user,
        db_password: connection.password
      },
      routes: {
        ...config.routes,
        "3300": {
          database: connection.targetDatabases && connection.targetDatabases.length > 0 
            ? (connection.targetDatabases.find((db: any) => typeof db === 'object' && db.name === 'mohammidev') 
               ? 'mohammidev' 
               : connection.targetDatabases.find((db: any) => typeof db === 'object' && db.name === 'mohammi')
               ? 'mohammi'
               : connection.targetDatabases[0].name || connection.targetDatabases[0])
            : 'mohammidev',
          company_name: "نظام إدارة المحاماة - تطوير (محمد الحاشدي)"
        }
      }
    }

    // حفظ الملف المحدث
    fs.writeFileSync(configPath, JSON.stringify(updatedConfig, null, 2), 'utf8')

    // تحديث ملف db.ts إذا كان موجوداً
    const dbPath = path.join(process.cwd(), 'src/lib/db.ts')
    if (fs.existsSync(dbPath)) {
      try {
        let dbContent = fs.readFileSync(dbPath, 'utf8')
        
        // تحديث إعدادات الاتصال في ملف db.ts
        dbContent = dbContent
          .replace(/host:\s*['"][^'"]*['"]/, `host: '${connection.host}'`)
          .replace(/port:\s*\d+/, `port: ${connection.port}`)
          .replace(/user:\s*['"][^'"]*['"]/, `user: '${connection.user}'`)
          .replace(/password:\s*['"][^'"]*['"]/, `password: '${connection.password}'`)
        
        fs.writeFileSync(dbPath, dbContent, 'utf8')
      } catch (error) {
        console.log('تحذير: لم يتم تحديث ملف db.ts:', error.message)
      }
    }

    console.log('✅ تم تحديث إعدادات قاعدة البيانات بنجاح')
    console.log('📋 الإعدادات الجديدة:')
    console.log(`   المضيف: ${connection.host}`)
    console.log(`   المنفذ: ${connection.port}`)
    console.log(`   المستخدم: ${connection.user}`)
    console.log(`   قاعدة البيانات: ${updatedConfig.routes["3300"].database}`)

    return NextResponse.json({
      success: true,
      message: 'تم تحديث الإعدادات بنجاح',
      config: updatedConfig,
      nextSteps: [
        'إعادة تشغيل الخادم',
        'إنشاء جدول case_movements',
        'اختبار صفحة /movements'
      ]
    })

  } catch (error) {
    console.error('❌ خطأ في تحديث الإعدادات:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث الإعدادات',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
