import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database-router'

async function ensureTable() {
  // إنشاء الجدول إن لم يكن موجوداً بالهيكل الجديد
  await query(`
    CREATE TABLE IF NOT EXISTS departments (
      id SERIAL PRIMARY KEY,
      dept_code VARCHAR(50) UNIQUE,
      dept_name VARCHAR(200) NOT NULL,
      description TEXT,
      is_active BOOLEAN DEFAULT TRUE,
      created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_date TIMESTAMP
    )
  `)

  // دعم التوافق العكسي: إذا كان الجدول القديم يحتوي على عمود name فقط
  // أضف الأعمدة الجديدة إن لزم، وانسخ القيم من name إلى dept_name
  await query(`
    DO $$
    BEGIN
      IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'departments' AND column_name = 'dept_name'
      ) THEN
        ALTER TABLE departments ADD COLUMN dept_name VARCHAR(200);
      END IF;

      IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'departments' AND column_name = 'dept_code'
      ) THEN
        ALTER TABLE departments ADD COLUMN dept_code VARCHAR(50);
      END IF;

      IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'departments' AND column_name = 'created_date'
      ) THEN
        ALTER TABLE departments ADD COLUMN created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
      END IF;

      IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'departments' AND column_name = 'updated_date'
      ) THEN
        ALTER TABLE departments ADD COLUMN updated_date TIMESTAMP;
      END IF;

      IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'departments' AND column_name = 'name'
      ) THEN
        -- نسخ البيانات من name إلى dept_name إن كانت dept_name فارغة
        UPDATE departments SET dept_name = COALESCE(dept_name, name) WHERE dept_name IS NULL;
      END IF;
    END $$;
  `)
}

export async function GET() {
  try {
    await ensureTable()

    // فحص الأعمدة الموجودة أولاً
    const columnsResult = await query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'departments' AND table_schema = 'public'
    `)

    const columns = columnsResult.rows.map(row => row.column_name)
    console.log('📋 أعمدة جدول departments:', columns)

    // بناء الاستعلام بناءً على الأعمدة الموجودة
    let nameColumn = 'dept_name'
    if (columns.includes('name') && !columns.includes('dept_name')) {
      nameColumn = 'name'
    } else if (columns.includes('dept_name')) {
      nameColumn = 'dept_name'
    }

    const result = await query(`
      SELECT
        id,
        COALESCE(dept_code, '') AS dept_code,
        COALESCE(${nameColumn}, '') AS dept_name,
        COALESCE(description, '') AS description,
        COALESCE(is_active, true) AS is_active,
        created_date,
        updated_date
      FROM departments
      ORDER BY id DESC
    `)

    console.log(`✅ تم جلب ${result.rows.length} قسم`)
    return NextResponse.json({ success: true, departments: result.rows })
  } catch (error: any) {
    console.error('❌ خطأ في جلب الأقسام:', error)
    return NextResponse.json({ success: false, error: error.message }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    await ensureTable()
    const body = await request.json()
    const { dept_code, dept_name, description, is_active } = body
    if (!dept_name || !String(dept_name).trim()) {
      return NextResponse.json({ success: false, error: 'اسم القسم مطلوب' }, { status: 400 })
    }
    const result = await query(
      `INSERT INTO departments(dept_code, dept_name, description, is_active)
       VALUES($1, $2, $3, COALESCE($4, true))
       RETURNING id, dept_code, dept_name, COALESCE(description,'') AS description, COALESCE(is_active, true) AS is_active, created_date, updated_date`,
      [dept_code?.toString().trim() || null, String(dept_name).trim(), description || null, typeof is_active === 'boolean' ? is_active : null]
    )
    return NextResponse.json({ success: true, department: result.rows[0], message: 'تم إنشاء القسم بنجاح' })
  } catch (error: any) {
    return NextResponse.json({ success: false, error: error.message }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    await ensureTable()
    const body = await request.json()
    const { id, dept_code, dept_name, description, is_active } = body
    if (!id) return NextResponse.json({ success: false, error: 'المعرف مطلوب' }, { status: 400 })
    const result = await query(
      `UPDATE departments SET
        dept_code = COALESCE($2, dept_code),
        dept_name = COALESCE($3, dept_name),
        description = COALESCE($4, description),
        is_active = COALESCE($5, is_active),
        updated_date = CURRENT_TIMESTAMP
       WHERE id = $1
       RETURNING id, dept_code, dept_name, COALESCE(description,'') AS description, COALESCE(is_active, true) AS is_active, created_date, updated_date`,
      [id, dept_code?.toString().trim() || null, dept_name?.toString().trim() || null, description ?? null, typeof is_active === 'boolean' ? is_active : null]
    )
    return NextResponse.json({ success: true, department: result.rows[0], message: 'تم تحديث القسم بنجاح' })
  } catch (error: any) {
    return NextResponse.json({ success: false, error: error.message }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    await ensureTable()
    const { searchParams } = new URL(request.url)
    const id = Number(searchParams.get('id'))
    if (!id) return NextResponse.json({ success: false, error: 'المعرف مطلوب' }, { status: 400 })
    await query(`DELETE FROM departments WHERE id = $1`, [id])
    return NextResponse.json({ success: true, message: 'تم حذف القسم بنجاح' })
  } catch (error: any) {
    return NextResponse.json({ success: false, error: error.message }, { status: 500 })
  }
}
