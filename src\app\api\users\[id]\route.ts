import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// PUT - تحديث مستخدم
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'رمز المصادقة مطلوب' },
        { status: 401 }
      )
    }

    const userId = params.id
    const body = await request.json()
    const { username, employee_id, role, user_type, permissions, is_active } = body

    // التحقق من البيانات المطلوبة
    if (!username || !role || !user_type) {
      return NextResponse.json(
        { success: false, error: 'جميع الحقول المطلوبة يجب ملؤها' },
        { status: 400 }
      )
    }

    // تحديث المستخدم
    const result = await query(`
      UPDATE users 
      SET username = $1, employee_id = $2, role = $3, user_type = $4, 
          permissions = $5, is_active = $6, updated_at = CURRENT_TIMESTAMP
      WHERE id = $7
      RETURNING id, username, role, user_type, permissions, is_active
    `, [
      username,
      employee_id || null,
      role,
      user_type,
      permissions || [],
      is_active !== false,
      userId
    ])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'المستخدم غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم تحديث المستخدم بنجاح',
      data: result.rows[0]
    })

  } catch (error) {
    console.error('خطأ في تحديث المستخدم:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في تحديث المستخدم' },
      { status: 500 }
    )
  }
}

// DELETE - حذف مستخدم
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'رمز المصادقة مطلوب' },
        { status: 401 }
      )
    }

    const userId = params.id

    // التحقق من وجود المستخدم
    const userCheck = await query(
      'SELECT id, username FROM users WHERE id = $1',
      [userId]
    )

    if (userCheck.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'المستخدم غير موجود' },
        { status: 404 }
      )
    }

    // حذف المستخدم (أو تعطيله)
    await query(
      'UPDATE users SET is_active = false, status = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
      ['deleted', userId]
    )

    return NextResponse.json({
      success: true,
      message: 'تم حذف المستخدم بنجاح'
    })

  } catch (error) {
    console.error('خطأ في حذف المستخدم:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في حذف المستخدم' },
      { status: 500 }
    )
  }
}

// GET - جلب مستخدم واحد
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'رمز المصادقة مطلوب' },
        { status: 401 }
      )
    }

    const userId = params.id

    // جلب بيانات المستخدم
    const result = await query(`
      SELECT 
        u.id, u.username, u.employee_id, u.role, u.user_type, u.permissions, 
        u.status, u.is_active, u.last_login, u.created_date,
        e.name, e.position, e.department, e.phone, e.email,
        ur.display_name as role_display_name
      FROM users u
      LEFT JOIN employees e ON u.employee_id = e.id
      LEFT JOIN user_roles ur ON u.role = ur.role_name
      WHERE u.id = $1
    `, [userId])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'المستخدم غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0]
    })

  } catch (error) {
    console.error('خطأ في جلب المستخدم:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في جلب المستخدم' },
      { status: 500 }
    )
  }
}