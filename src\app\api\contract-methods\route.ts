import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب طرق التعاقد
export async function GET() {
  try {

    // جلب طرق التعاقد من قاعدة البيانات
    const result = await query(`
      SELECT 
        method_code as value,
        method_name as label,
        description,
        is_active
      FROM contract_methods 
      WHERE is_active = true
      ORDER BY sort_order, method_name
    `)

    // إذا لم توجد بيانات في قاعدة البيانات، إرجاع البيانات الافتراضية
    if (result.rows.length === 0) {
      const defaultMethods = [
        { value: 'بالجلسة', label: 'بالجلسة', description: 'تعاقد بالجلسة', is_active: true },
        { value: 'بالعقد', label: 'بالعقد', description: 'تعاقد بالعقد', is_active: true }
      ]

      return NextResponse.json({
        success: true,
        data: defaultMethods,
        message: 'تم جلب طرق التعاقد الافتراضية'
      })
    }

    return NextResponse.json({
      success: true,
      data: result.rows,
      message: 'تم جلب طرق التعاقد بنجاح'
    })
  } catch (error) {
    console.error('GET Contract Methods API: Error:', error)

    // في حالة الخطأ، إرجاع البيانات الافتراضية
    const defaultMethods = [
      { value: 'بالجلسة', label: 'بالجلسة', description: 'تعاقد بالجلسة', is_active: true },
      { value: 'بالعقد', label: 'بالعقد', description: 'تعاقد بالعقد', is_active: true }
    ]

    return NextResponse.json({
      success: true,
      data: defaultMethods,
      message: 'تم جلب طرق التعاقد الافتراضية (خطأ في قاعدة البيانات)'
    })
  }
}

// POST - إضافة طريقة تعاقد جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { method_code, method_name, description, sort_order } = body

    // التحقق من البيانات المطلوبة
    if (!method_code || !method_name) {
      return NextResponse.json(
        { success: false, error: 'رمز الطريقة واسم الطريقة مطلوبان' },
        { status: 400 }
      )
    }

    // التحقق من عدم تكرار رمز الطريقة
    const duplicateCheck = await query(
      'SELECT id FROM contract_methods WHERE method_code = $1',
      [method_code]
    )

    if (duplicateCheck.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'رمز الطريقة موجود مسبقاً' },
        { status: 400 }
      )
    }

    // إدراج طريقة التعاقد الجديدة
    const result = await query(`
      INSERT INTO contract_methods (method_code, method_name, description, sort_order, is_active, created_at, updated_at)
      VALUES ($1, $2, $3, $4, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING *
    `, [method_code, method_name, description || '', sort_order || 0])

    return NextResponse.json({
      success: true,
      message: 'تم إضافة طريقة التعاقد بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('POST Contract Methods API: Error creating method:', error)
    return NextResponse.json(
      { success: false, error: `فشل في إضافة طريقة التعاقد: ${error.message}` },
      { status: 500 }
    )
  }
}
