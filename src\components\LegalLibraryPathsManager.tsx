'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { 
  FolderPlus, 
  Edit, 
  Trash2, 
  Star,
  StarOff,
  Eye,
  EyeOff,
  Plus,
  Save,
  X
} from 'lucide-react'
import toast from 'react-hot-toast'

interface LibraryPath {
  id: number
  path_name: string
  path_value: string
  description: string
  is_active: boolean
  is_default: boolean
  scan_enabled: boolean
  created_at: string
  updated_at: string
}

interface PathsManagerProps {
  onPathsChange?: () => void
}

export function LegalLibraryPathsManager({ onPathsChange }: PathsManagerProps) {
  const [paths, setPaths] = useState<LibraryPath[]>([])
  const [loading, setLoading] = useState(false)
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingPath, setEditingPath] = useState<LibraryPath | null>(null)
  const [formData, setFormData] = useState({
    path_name: '',
    path_value: '',
    description: '',
    is_default: false,
    scan_enabled: true
  })

  // تحميل المسارات
  useEffect(() => {
    loadPaths()
  }, [])

  const loadPaths = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/legal-library/paths')
      const result = await response.json()

      if (result.success) {
        setPaths(result.data || [])
      } else {
        toast.error(result.error || 'خطأ في تحميل المسارات')
      }
    } catch (error) {
      console.error('خطأ في تحميل المسارات:', error)
      toast.error('خطأ في الاتصال بالخادم')
    } finally {
      setLoading(false)
    }
  }

  // إضافة مسار جديد
  const handleAddPath = async () => {
    if (!formData.path_name.trim() || !formData.path_value.trim()) {
      toast.error('اسم المسار والقيمة مطلوبان')
      return
    }

    try {
      const response = await fetch('/api/legal-library/paths', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      const result = await response.json()

      if (result.success) {
        toast.success('تم إضافة المسار بنجاح')
        resetForm()
        await loadPaths()
        onPathsChange?.()
      } else {
        toast.error(result.error || 'خطأ في إضافة المسار')
      }
    } catch (error) {
      console.error('خطأ في إضافة المسار:', error)
      toast.error('خطأ في الاتصال بالخادم')
    }
  }

  // تحديث مسار
  const handleUpdatePath = async () => {
    if (!editingPath) return

    try {
      const response = await fetch('/api/legal-library/paths', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ id: editingPath.id, ...formData })
      })

      const result = await response.json()

      if (result.success) {
        toast.success('تم تحديث المسار بنجاح')
        resetForm()
        await loadPaths()
        onPathsChange?.()
      } else {
        toast.error(result.error || 'خطأ في تحديث المسار')
      }
    } catch (error) {
      console.error('خطأ في تحديث المسار:', error)
      toast.error('خطأ في الاتصال بالخادم')
    }
  }

  // حذف مسار
  const handleDeletePath = async (pathId: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا المسار؟')) {
      return
    }

    try {
      const response = await fetch(`/api/legal-library/paths?id=${pathId}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (result.success) {
        toast.success('تم حذف المسار بنجاح')
        await loadPaths()
        onPathsChange?.()
      } else {
        toast.error(result.error || 'خطأ في حذف المسار')
      }
    } catch (error) {
      console.error('خطأ في حذف المسار:', error)
      toast.error('خطأ في الاتصال بالخادم')
    }
  }

  // تبديل حالة المسار
  const togglePathStatus = async (pathId: number, field: 'is_active' | 'is_default' | 'scan_enabled', value: boolean) => {
    try {
      const response = await fetch('/api/legal-library/paths', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ id: pathId, [field]: value })
      })

      const result = await response.json()

      if (result.success) {
        await loadPaths()
        onPathsChange?.()
      } else {
        toast.error(result.error || 'خطأ في تحديث المسار')
      }
    } catch (error) {
      console.error('خطأ في تحديث المسار:', error)
      toast.error('خطأ في الاتصال بالخادم')
    }
  }

  // إعداد النموذج للتعديل
  const startEdit = (path: LibraryPath) => {
    setEditingPath(path)
    setFormData({
      path_name: path.path_name,
      path_value: path.path_value,
      description: path.description,
      is_default: path.is_default,
      scan_enabled: path.scan_enabled
    })
    setShowAddForm(true)
  }

  // إعادة تعيين النموذج
  const resetForm = () => {
    setFormData({
      path_name: '',
      path_value: '',
      description: '',
      is_default: false,
      scan_enabled: true
    })
    setEditingPath(null)
    setShowAddForm(false)
  }

  return (
    <div className="space-y-6">
      {/* رأس القسم */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">إدارة مسارات المكتبة القانونية</h3>
        <Button
          onClick={() => setShowAddForm(true)}
          size="sm"
          className="bg-blue-600 hover:bg-blue-700"
        >
          <Plus className="h-4 w-4 mr-2" />
          إضافة مسار جديد
        </Button>
      </div>

      {/* نموذج إضافة/تعديل المسار */}
      {showAddForm && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>{editingPath ? 'تعديل المسار' : 'إضافة مسار جديد'}</span>
              <Button
                onClick={resetForm}
                variant="ghost"
                size="sm"
              >
                <X className="h-4 w-4" />
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="path_name">اسم المسار *</Label>
                <Input
                  id="path_name"
                  value={formData.path_name}
                  onChange={(e) => setFormData({...formData, path_name: e.target.value})}
                  placeholder="مثال: المسار الرئيسي"
                />
              </div>
              
              <div>
                <Label htmlFor="path_value">مسار المجلد *</Label>
                <Input
                  id="path_value"
                  value={formData.path_value}
                  onChange={(e) => setFormData({...formData, path_value: e.target.value})}
                  placeholder="D:\mohaminew\legal-documents"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="description">الوصف</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                placeholder="وصف مختصر للمسار"
                rows={2}
              />
            </div>

            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="flex items-center space-x-2 space-x-reverse">
                <Switch
                  checked={formData.is_default}
                  onCheckedChange={(checked) => setFormData({...formData, is_default: checked})}
                />
                <Label>مسار افتراضي</Label>
              </div>

              <div className="flex items-center space-x-2 space-x-reverse">
                <Switch
                  checked={formData.scan_enabled}
                  onCheckedChange={(checked) => setFormData({...formData, scan_enabled: checked})}
                />
                <Label>تفعيل الفحص</Label>
              </div>
            </div>

            <div className="flex items-center space-x-2 space-x-reverse">
              <Button
                onClick={editingPath ? handleUpdatePath : handleAddPath}
                className="bg-green-600 hover:bg-green-700"
              >
                <Save className="h-4 w-4 mr-2" />
                {editingPath ? 'تحديث المسار' : 'إضافة المسار'}
              </Button>
              
              <Button
                onClick={resetForm}
                variant="outline"
              >
                إلغاء
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* قائمة المسارات */}
      <Card>
        <CardHeader>
          <CardTitle>المسارات المتاحة ({paths.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <p className="text-gray-500">جاري تحميل المسارات...</p>
            </div>
          ) : paths.length === 0 ? (
            <div className="text-center py-8">
              <FolderPlus className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <p className="text-gray-500">لا توجد مسارات محددة</p>
              <p className="text-sm text-gray-400 mt-1">أضف مسار جديد للبدء</p>
            </div>
          ) : (
            <div className="space-y-3">
              {paths.map((path) => (
                <div
                  key={path.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                >
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 space-x-reverse mb-2">
                      <h4 className="font-medium text-gray-900">{path.path_name}</h4>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        {path.is_default && (
                          <Badge variant="default" className="bg-yellow-100 text-yellow-800">
                            افتراضي
                          </Badge>
                        )}
                        {path.is_active ? (
                          <Badge variant="default" className="bg-green-100 text-green-800">
                            نشط
                          </Badge>
                        ) : (
                          <Badge variant="secondary">
                            غير نشط
                          </Badge>
                        )}
                        {path.scan_enabled && (
                          <Badge variant="outline">
                            فحص مفعل
                          </Badge>
                        )}
                      </div>
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-1">{path.path_value}</p>
                    {path.description && (
                      <p className="text-xs text-gray-500">{path.description}</p>
                    )}
                  </div>

                  <div className="flex items-center space-x-2 space-x-reverse">
                    <Button
                      onClick={() => togglePathStatus(path.id, 'is_default', !path.is_default)}
                      variant="ghost"
                      size="sm"
                      title={path.is_default ? 'إلغاء الافتراضي' : 'جعل افتراضي'}
                    >
                      {path.is_default ? (
                        <Star className="h-4 w-4 text-yellow-500" />
                      ) : (
                        <StarOff className="h-4 w-4 text-gray-400" />
                      )}
                    </Button>

                    <Button
                      onClick={() => togglePathStatus(path.id, 'is_active', !path.is_active)}
                      variant="ghost"
                      size="sm"
                      title={path.is_active ? 'إلغاء التفعيل' : 'تفعيل'}
                    >
                      {path.is_active ? (
                        <Eye className="h-4 w-4 text-green-500" />
                      ) : (
                        <EyeOff className="h-4 w-4 text-gray-400" />
                      )}
                    </Button>

                    <Button
                      onClick={() => startEdit(path)}
                      variant="ghost"
                      size="sm"
                      title="تعديل"
                    >
                      <Edit className="h-4 w-4 text-blue-500" />
                    </Button>

                    <Button
                      onClick={() => handleDeletePath(path.id)}
                      variant="ghost"
                      size="sm"
                      title="حذف"
                    >
                      <Trash2 className="h-4 w-4 text-red-500" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
