'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { CheckCircle, AlertCircle, Loader2, Users, Building } from 'lucide-react'
import MainLayout from '@/components/layout/main-layout'

interface CreateResult {
  success: boolean
  message: string
  data?: {
    clientsInserted: number
    employeesInserted: number
  }
  error?: string
}

export default function CreateSampleClientsEmployeesPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<CreateResult | null>(null)

  const handleCreateSampleData = async () => {
    setIsLoading(true)
    setResult(null)

    try {
      const response = await fetch('/api/create-sample-clients-employees', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const data = await response.json()
      setResult(data)
    } catch (error) {
      setResult({
        success: false,
        message: 'فشل في الاتصال بالخادم',
        error: error instanceof Error ? error.message : 'خطأ غير معروف'
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50">
        <div className="space-y-6 p-6 bg-white min-h-screen">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">إنشاء بيانات تجريبية للعملاء والموظفين</h1>
              <p className="text-gray-600 mt-2">
                إنشاء عملاء وموظفين تجريبيين لاختبار النظام
              </p>
            </div>
          </div>

          <div className="grid gap-6">
            {/* شرح البيانات التجريبية */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="h-6 w-6 mr-3 text-blue-600" />
                  البيانات التجريبية
                </CardTitle>
                <CardDescription>
                  سيتم إنشاء عملاء وموظفين تجريبيين مع بيانات كاملة
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h4 className="font-semibold text-green-800 flex items-center">
                      <Users className="h-4 w-4 mr-2" />
                      العملاء (5 عملاء):
                    </h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• فاطمة أحمد حسن</li>
                      <li>• محمد عبدالله صالح</li>
                      <li>• عائشة علي محمد</li>
                      <li>• شركة النور للتجارة</li>
                      <li>• أحمد سعد الغامدي</li>
                    </ul>
                  </div>
                  
                  <div className="space-y-2">
                    <h4 className="font-semibold text-blue-800 flex items-center">
                      <Building className="h-4 w-4 mr-2" />
                      الموظفين (5 موظفين):
                    </h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• خالد أحمد المحامي</li>
                      <li>• سارة محمد المستشارة</li>
                      <li>• عبدالرحمن علي الكاتب</li>
                      <li>• نورا سالم المحاسبة</li>
                      <li>• فهد عبدالله المدير</li>
                    </ul>
                  </div>
                </div>
                
                <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-semibold text-blue-800 mb-2">البيانات المتضمنة:</h4>
                  <div className="grid md:grid-cols-2 gap-4 text-sm text-blue-700">
                    <div>
                      <strong>للعملاء:</strong>
                      <ul className="list-disc list-inside">
                        <li>الاسم الكامل</li>
                        <li>رقم الهاتف</li>
                        <li>البريد الإلكتروني</li>
                        <li>العنوان</li>
                        <li>رقم الهوية</li>
                        <li>نوع العميل (فرد/شركة)</li>
                      </ul>
                    </div>
                    <div>
                      <strong>للموظفين:</strong>
                      <ul className="list-disc list-inside">
                        <li>الاسم الكامل</li>
                        <li>المنصب</li>
                        <li>رقم الهاتف</li>
                        <li>البريد الإلكتروني</li>
                        <li>الراتب</li>
                        <li>رقم الموظف</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* زر الإنشاء */}
            <Card>
              <CardHeader>
                <CardTitle>إنشاء البيانات</CardTitle>
                <CardDescription>
                  اضغط على الزر أدناه لإنشاء العملاء والموظفين التجريبيين
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button 
                  onClick={handleCreateSampleData} 
                  disabled={isLoading}
                  className="w-full md:w-auto"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      جاري الإنشاء...
                    </>
                  ) : (
                    <>
                      <Users className="h-4 w-4 mr-2" />
                      إنشاء العملاء والموظفين التجريبيين
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            {/* النتائج */}
            {result && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    {result.success ? (
                      <CheckCircle className="h-6 w-6 mr-3 text-green-600" />
                    ) : (
                      <AlertCircle className="h-6 w-6 mr-3 text-red-600" />
                    )}
                    نتائج الإنشاء
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Alert className={result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
                    <AlertDescription className={result.success ? 'text-green-800' : 'text-red-800'}>
                      {result.message}
                    </AlertDescription>
                  </Alert>

                  {result.success && result.data && (
                    <div className="mt-4 grid md:grid-cols-2 gap-4">
                      <div className="p-4 bg-gray-50 rounded-lg">
                        <h4 className="font-semibold mb-2">العملاء:</h4>
                        <p className="text-sm">تم إنشاء {result.data.clientsInserted} عميل بنجاح</p>
                      </div>
                      
                      <div className="p-4 bg-gray-50 rounded-lg">
                        <h4 className="font-semibold mb-2">الموظفين:</h4>
                        <p className="text-sm">تم إنشاء {result.data.employeesInserted} موظف بنجاح</p>
                      </div>
                    </div>
                  )}

                  {!result.success && result.error && (
                    <div className="mt-4 p-4 bg-red-50 rounded-lg">
                      <h4 className="font-semibold text-red-800 mb-2">تفاصيل الخطأ:</h4>
                      <p className="text-sm text-red-600">{result.error}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* الخطوات التالية */}
            {result?.success && (
              <Card>
                <CardHeader>
                  <CardTitle>الخطوات التالية</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <p>✅ تم إنشاء العملاء والموظفين التجريبيين بنجاح</p>
                    <p>✅ يمكنك الآن مراجعة البيانات في صفحات العملاء والموظفين</p>
                    <p>✅ يمكنك تطبيق التصميم المحاسبي لربطهم بدليل الحسابات</p>
                  </div>
                  
                  <div className="mt-4 space-x-2 space-x-reverse">
                    <Button 
                      onClick={() => window.location.href = '/clients'}
                      variant="outline"
                    >
                      عرض العملاء
                    </Button>
                    <Button 
                      onClick={() => window.location.href = '/employees'}
                      variant="outline"
                    >
                      عرض الموظفين
                    </Button>
                    <Button 
                      onClick={() => window.location.href = '/accounting/implement-structure'}
                      variant="outline"
                    >
                      تطبيق التصميم المحاسبي
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
