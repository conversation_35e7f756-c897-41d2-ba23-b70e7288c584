{"routes": {"7443": {"database": "moham<PERSON>", "company_name": "نظام إدارة المحاماة - محمد", "theme_color": "#cca967", "logo_text": "نظام إدارة المحاماة - محمد", "description": "نظام إدارة المحاماة - محمد", "internal_next_port": 3000, "enabled": true, "homepage": "/", "welcome_message": "مرحباً بك في نظام إدارة المحاماة - محمد", "notification_prefix": "نظام المحاماة - محمد"}, "8914": {"database": "rubaie", "company_name": "نظام إدارة المحاماة - الربعي", "theme_color": "#2563eb", "logo_text": "نظام إدارة المحاماة - الربعي", "description": "نظام إدارة المحاماة - الربعي", "internal_next_port": 3001, "enabled": true, "homepage": "/", "welcome_message": "مرحباً بك في نظام إدارة المحاماة - الربعي", "notification_prefix": "نظام المحاماة - الربعي"}, "3300": {"database": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "company_name": "نظام إدارة المحاماة - محم<PERSON> (التطوير)", "theme_color": "#ff6b35", "logo_text": "نظام إدارة المحاماة - محم<PERSON> (التطوير)", "description": "نظام إدارة المحاماة - محمد (نسخة التطوير)", "internal_next_port": 3300, "enabled": true, "homepage": "/", "welcome_message": "مرحباً بك في نظام إدارة المحاماة - محمد (نسخة التطوير)", "notification_prefix": "نظام المحاماة - محم<PERSON> (تطوير)"}}, "default_config": {"db_host": "localhost", "db_port": 5432, "db_user": "postgres", "db_password": "yemen123", "jwt_secret": "your-secret-key-here", "next_port": 3000, "default_theme": "#2563eb", "default_welcome": "مرحباً بك في نظام إدارة المحاماة", "default_notification_prefix": "نظام المحاماة", "ssl": {"enabled": true, "key_path": "D:\\mohaminew\\production\\ssl\\correct_private_key.key", "cert_path": "D:\\mohaminew\\production\\ssl\\mohammi_com_final.crt", "ca_path": "D:\\mohaminew\\production\\ssl\\SectigoPublicServerAuthenticationCADVR36.crt"}}, "system_settings": {"validate_database_connection": false, "auto_create_missing_databases": true, "auto_restart_on_error": true, "connection_timeout": 5000, "log_requests": true, "log_errors": true}}