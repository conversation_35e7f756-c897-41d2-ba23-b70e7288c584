-- إنشاء جدول إعدادات النظام
CREATE TABLE IF NOT EXISTS system_settings (
    id SERIAL PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type VARCHAR(50) DEFAULT 'string',
    description TEXT,
    is_editable BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إدراج الإعدادات الافتراضية
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_editable) VALUES
('legal_library_path', '/home/<USER>/Downloads/legal-system/laws', 'path', 'مسار مجلد المكتبة القانونية', true),
('legal_library_max_file_size', '50', 'number', 'الحد الأقصى لحجم الملف بالميجابايت', true),
('legal_library_allowed_extensions', '.pdf,.doc,.docx,.txt', 'string', 'امتدادات الملفات المسموحة', true),
('legal_library_auto_scan', 'true', 'boolean', 'فحص المجلد تلقائياً للملفات الجديدة', true),
('legal_library_backup_enabled', 'false', 'boolean', 'تفعيل النسخ الاحتياطي للملفات', true)
ON CONFLICT (setting_key) DO NOTHING;

-- إنشاء فهرس للبحث السريع
CREATE INDEX IF NOT EXISTS idx_system_settings_key ON system_settings(setting_key);

-- إنشاء trigger لتحديث updated_at
CREATE OR REPLACE FUNCTION update_system_settings_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_system_settings_timestamp ON system_settings;
CREATE TRIGGER trigger_update_system_settings_timestamp
    BEFORE UPDATE ON system_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_system_settings_timestamp();
