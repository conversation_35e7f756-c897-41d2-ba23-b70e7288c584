import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// كشف مخطط جدول الأقسام ديناميكياً (مع تفضيل public)
async function getDepartmentsSchema(): Promise<string> {
  try {
    const res = await query(
      `SELECT table_schema
       FROM information_schema.tables
       WHERE table_name = 'departments'
       ORDER BY CASE WHEN table_schema = 'public' THEN 0 ELSE 1 END, table_schema
       LIMIT 1`
    )
    return res.rows?.[0]?.table_schema || 'public'
  } catch {
    return 'public'
  }
}
async function ensureTable() {
  // إنشاء جدول employees إذا لم يكن موجودًا مع أعمدة أساسية مستخدمة في الواجهة
  await query(`
    CREATE TABLE IF NOT EXISTS employees (
      id SERIAL PRIMARY KEY,
      employee_number VARCHAR(50),
      name VARCHAR(200) NOT NULL,
      position VARCHAR(200),
      department_id INTEGER,
      branch_id INTEGER,
      governorate_id INTEGER,
      court_id INTEGER,
      phone VARCHAR(50),
      email VARCHAR(200),
      address TEXT,
      id_number VARCHAR(100),
      salary NUMERIC,
      hire_date DATE,
      status VARCHAR(50) DEFAULT 'active',
      is_active BOOLEAN DEFAULT TRUE,
      created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `)

  // ضمان وجود الأعمدة إذا كانت قاعدة قديمة
  const columnsResult = await query(`
    SELECT column_name FROM information_schema.columns
    WHERE table_name = 'employees' AND table_schema = 'public'
  `)
  const cols = columnsResult.rows.map((r: any) => r.column_name)
  const maybeAdd = async (col: string, ddl: string) => {
    if (!cols.includes(col)) {
      await query(`ALTER TABLE employees ADD COLUMN ${ddl}`)
    }
  }
  await maybeAdd('employee_number', 'employee_number VARCHAR(50)')
  await maybeAdd('account_id', 'account_id INTEGER')
  await maybeAdd('department_id', 'department_id INTEGER')
  await maybeAdd('department', 'department VARCHAR(200)')
  await maybeAdd('branch_id', 'branch_id INTEGER')
  await maybeAdd('governorate_id', 'governorate_id INTEGER')
  await maybeAdd('court_id', 'court_id INTEGER')
  await maybeAdd('id_number', 'id_number VARCHAR(100)')
  await maybeAdd('salary', 'salary NUMERIC')
  await maybeAdd('hire_date', 'hire_date DATE')
  await maybeAdd('status', "status VARCHAR(50) DEFAULT 'active'")
  await maybeAdd('is_active', 'is_active BOOLEAN DEFAULT TRUE')

  // ضمان وجود تسلسل (sequence) صحيح للعمود id وضبطه على أكبر قيمة حالية
  await query(`
    DO $$
    DECLARE
      seq_name text := 'employees_id_seq';
      has_default boolean := false;
    BEGIN
      -- إنشاء التسلسل إن لم يكن موجودًا
      IF NOT EXISTS (
        SELECT 1 FROM pg_class WHERE relkind = 'S' AND relname = seq_name
      ) THEN
        EXECUTE 'CREATE SEQUENCE ' || seq_name;
      END IF;

      -- تعيين الملكية للتسلسل للعمود id
      BEGIN
        EXECUTE 'ALTER SEQUENCE ' || seq_name || ' OWNED BY employees.id';
      EXCEPTION WHEN OTHERS THEN NULL; END;

      -- التحقق إن كان للعمود id قيمة افتراضية nextval
      SELECT (pg_get_expr(adbin, adrelid) LIKE ('nextval(%' || seq_name || '%)'))
      INTO has_default
      FROM pg_attrdef d
      JOIN pg_attribute a ON a.attrelid = d.adrelid AND a.attnum = d.adnum
      JOIN pg_class c ON c.oid = d.adrelid
      WHERE c.relname = 'employees' AND a.attname = 'id';

      IF NOT has_default THEN
        EXECUTE 'ALTER TABLE employees ALTER COLUMN id SET DEFAULT nextval(''' || seq_name || ''')';
      END IF;

      -- مواءمة قيمة التسلسل مع أكبر معرف موجود
      PERFORM setval(seq_name, COALESCE((SELECT MAX(id) FROM employees), 0));
    END $$;
  `)


  // إضافة قيود العلاقات إن لم تكن موجودة (مراجع للأقسام/الفروع/المحافظات/المحاكم)
  await query(`
    DO $$
    DECLARE
      r RECORD;
    BEGIN
      -- إزالة أي قيود قديمة على employees.department_id بغض النظر عن الاسم
      FOR r IN
        SELECT c.conname
        FROM pg_constraint c
        JOIN pg_class t ON t.oid = c.conrelid AND t.relname = 'employees'
        JOIN pg_namespace n ON n.oid = t.relnamespace AND n.nspname = 'public'
        WHERE c.contype = 'f' AND c.conkey = ARRAY(
          SELECT attnum FROM pg_attribute
          WHERE attrelid = t.oid AND attname = 'department_id'
        )
      LOOP
        BEGIN
          EXECUTE 'ALTER TABLE employees DROP CONSTRAINT ' || quote_ident(r.conname);
        EXCEPTION WHEN undefined_object THEN NULL; END;
      END LOOP;
      -- لا نقوم بإعادة إنشاء قيد FK على department_id حالياً،
      -- سنعتمد على التحقق الخادمي (SELECT EXISTS) لتفادي مشاكل اختلاف المخطط أثناء التطوير.

      IF NOT EXISTS (
        SELECT 1 FROM pg_constraint WHERE conname = 'fk_employees_branch'
      ) THEN
        BEGIN
          ALTER TABLE employees
          ADD CONSTRAINT fk_employees_branch FOREIGN KEY (branch_id)
          REFERENCES branches(id) ON UPDATE CASCADE ON DELETE SET NULL;
        EXCEPTION WHEN undefined_table THEN NULL; END;
      END IF;

      IF NOT EXISTS (
        SELECT 1 FROM pg_constraint WHERE conname = 'fk_employees_governorate'
      ) THEN
        BEGIN
          ALTER TABLE employees
          ADD CONSTRAINT fk_employees_governorate FOREIGN KEY (governorate_id)
          REFERENCES governorates(id) ON UPDATE CASCADE ON DELETE SET NULL;
        EXCEPTION WHEN undefined_table THEN NULL; END;
      END IF;

      IF NOT EXISTS (
        SELECT 1 FROM pg_constraint WHERE conname = 'fk_employees_court'
      ) THEN
        BEGIN
          ALTER TABLE employees
          ADD CONSTRAINT fk_employees_court FOREIGN KEY (court_id)
          REFERENCES courts(id) ON UPDATE CASCADE ON DELETE SET NULL;
        EXCEPTION WHEN undefined_table THEN NULL; END;
      END IF;
    END $$;
  `)
}

// GET - جلب جميع الموظفين من قاعدة البيانات
export async function GET() {
  try {
    await ensureTable()
    // كشف مخطط الأقسام واستخدامه في كل عمليات التحقق
    let depSchema = await getDepartmentsSchema()
    const result = await query(`
      SELECT
        e.*,
        d.dept_name AS department_name,
        b.name AS branch_name,
        g.name AS governorate_name,
        c.name AS court_name
      FROM employees e
      LEFT JOIN ${depSchema}.departments d ON d.id = e.department_id
      LEFT JOIN branches b ON b.id = e.branch_id
      LEFT JOIN governorates g ON g.id = e.governorate_id
      LEFT JOIN courts c ON c.id = e.court_id
      ORDER BY e.id DESC
    `)
    return NextResponse.json({ success: true, employees: result.rows, data: result.rows })
  } catch (error) {
    console.error('Error fetching الموظفين:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات الموظفين', message: 'تأكد من وجود الجدول في قاعدة البيانات' },
      { status: 500 }
    )
  }
}

// POST - إضافة موظف جديد
export async function POST(request: NextRequest) {
  let depIdNum: number | undefined
  try {
    await ensureTable()
    // اكتشاف مخطط جدول الأقسام للاستخدام داخل هذا الطلب
    const depSchema = await getDepartmentsSchema()
    const body = await request.json()

    const {
      name,
      employee_number,
      position,
      department_id,
      account_id,
      branch_id,
      governorate_id,
      court_id,
      phone,
      email,
      address,
      id_number,
      salary,
      hire_date,
      status
    } = body

    if (!name || !String(name).trim()) {
      return NextResponse.json({ success: false, error: 'اسم الموظف مطلوب' }, { status: 400 })
    }

    // تحقق من رقم الهوية: 11 رقم
    if (!id_number || !/^\d{11}$/.test(String(id_number))) {
      return NextResponse.json({ success: false, error: 'رقم الهوية يجب أن يتكون من 11 رقمًا' }, { status: 400 })
    }

    // تحقق من اختيار القسم
    if (!department_id || String(department_id).trim() === '') {
      return NextResponse.json({ success: false, error: 'يجب اختيار القسم' }, { status: 400 })
    }
    // التحقق أن المعرف رقمي صالح
    depIdNum = Number(department_id)
    if (Number.isNaN(depIdNum)) {
      return NextResponse.json({ success: false, error: 'معرف القسم غير صالح' }, { status: 400 })
    }
    // تحقق من وجود القسم المختار لتجنب خطأ FK
    let depCheck = await query(`SELECT 1 FROM ${depSchema}.departments WHERE id = $1`, [depIdNum])
    // في حال عدم وجود المعرف، نحاول المطابقة بالاسم إذا أُرسل من الواجهة
    if (depCheck.rows.length === 0) {
      const fallbackDeptName: string | undefined = body?.department_name
      if (fallbackDeptName && fallbackDeptName.trim()) {
        const byName = await query(`SELECT id, dept_name FROM ${depSchema}.departments WHERE TRIM(dept_name) ILIKE TRIM($1) LIMIT 1`, [fallbackDeptName.trim()])
        if (byName.rows.length > 0) {
          depIdNum = Number(byName.rows[0].id)
          depCheck = await query(`SELECT 1 FROM ${depSchema}.departments WHERE id = $1`, [depIdNum])
        }
      }
    }
    if (depCheck.rows.length === 0) {
      const fallbackDeptName: string | undefined = body?.department_name
      if (fallbackDeptName && fallbackDeptName.trim()) {
        // إنشاء القسم تلقائياً ثم استخدام معرفه
        const ins = await query(`
          INSERT INTO ${depSchema}.departments (dept_name)
          VALUES ($1)
          ON CONFLICT DO NOTHING
          RETURNING id
        `, [fallbackDeptName.trim()])
        if (ins.rows.length > 0) {
          depIdNum = Number(ins.rows[0].id)
          depCheck = await query(`SELECT 1 FROM ${depSchema}.departments WHERE id = $1`, [depIdNum])
        }
      }
      if (depCheck.rows.length === 0) {
        const diag = await query(`
          SELECT
            current_database() AS db,
            current_schema() AS schema,
            (SELECT COUNT(*) FROM ${depSchema}.departments) AS dep_count,
            (SELECT array_agg(id ORDER BY id LIMIT 10) FROM ${depSchema}.departments) AS dep_ids,
            $1::text AS dep_schema
        `, [depSchema])
        const d = diag.rows?.[0]
        return NextResponse.json({
          success: false,
          error: 'القسم المحدد غير موجود في قاعدة البيانات',
          context: { department_id: depIdNum, db: d?.db, schema: d?.schema, detected_schema: d?.dep_schema, dep_count: d?.dep_count, dep_ids: d?.dep_ids }
        }, { status: 400 })
      }
    }

    const isActive = (status ?? 'active') === 'active'
    // جلب اسم القسم لتخزينه نصيًا في جدول الموظفين
    const depNameRes = await query(`SELECT COALESCE(dept_name, '') AS dep_name FROM ${depSchema}.departments WHERE id = $1`, [depIdNum])
    const department_name_to_save = depNameRes.rows?.[0]?.dep_name || null

    // محاولة ضبط قيد العلاقة الأجنبية (FK) على مخطط الأقسام المحدد
    try {
      await query(`
        ALTER TABLE employees
        DROP CONSTRAINT IF EXISTS fk_employees_department;
        ALTER TABLE employees
        ADD CONSTRAINT fk_employees_department FOREIGN KEY (department_id)
        REFERENCES ${depSchema}.departments(id) ON UPDATE CASCADE ON DELETE SET NULL;
      `)
    } catch (error) {
      console.error('Error setting FK constraint:', error)
    }

    const result = await query(
      `INSERT INTO employees
        (name, employee_number, position, department_id, department, account_id, branch_id, governorate_id, court_id, phone, email, address, id_number, salary, hire_date, status, is_active)
       VALUES
        ($1,   $2,             $3,       $4,            $5,        $6,         $7,        $8,            $9,       $10,   $11,   $12,     $13,       $14,    $15,      $16,    $17)
       RETURNING *`,
      [
        String(name).trim(),
        employee_number ?? null,
        position ?? null,
        department_id ? depIdNum : null,
        department_name_to_save,
        account_id ? Number(account_id) : null,
        branch_id ? Number(branch_id) : null,
        governorate_id ? Number(governorate_id) : null,
        court_id ? Number(court_id) : null,
        phone ?? null,
        email ?? null,
        address ?? null,
        id_number ?? null,
        salary != null && salary !== '' ? Number(salary) : null,
        hire_date ? new Date(hire_date) : null,
        status ?? 'active',
        isActive
      ]
    )

    return NextResponse.json({ success: true, message: 'تم إضافة الموظف بنجاح', data: result.rows[0] })
  } catch (error) {
    const err: any = error
    console.error('Error creating الموظفين:', err)
    if (err?.code === '23503') {
      // انتهاك مفتاح أجنبي
      return NextResponse.json({
        success: false,
        error: `فشل ربط القسم بالموظف: القسم غير موجود أو غير متاح (department_id=${typeof depIdNum === 'number' ? depIdNum : 'غير معلوم'}).`
      }, { status: 400 })
    }
    if (err?.code === '23505') {
      return NextResponse.json({ success: false, error: 'فشل في إضافة الموظف: تعارض في قيمة فريدة' }, { status: 400 })
    }
    const msg = err?.message || 'فشل في إضافة الموظف'
    return NextResponse.json({ success: false, error: msg }, { status: 500 })
  }
}

// PUT - تحديث موظف
export async function PUT(request: NextRequest) {
  try {
    await ensureTable()
    // اكتشاف مخطط جدول الأقسام للاستخدام داخل هذا الطلب
    const depSchema = await getDepartmentsSchema()
    const body = await request.json()

    const {
      id,
      name,
      employee_number,
      position,
      department, // قد تأتي بهذا الاسم من الواجهة
      department_id,
      account_id,
      branch_id,
      governorate_id,
      court_id,
      phone,
      email,
      address,
      id_number,
      salary,
      hire_date,
      status
    } = body

    if (!id) return NextResponse.json({ success: false, error: 'المعرف مطلوب' }, { status: 400 })

    const deptId = department_id ?? department
    const isActive = (status ?? 'active') === 'active'

    // تحقق من رقم الهوية: إذا أُرسلت يجب أن تكون 11 رقمًا
    if (id_number != null && id_number !== '' && !/^\d{11}$/.test(String(id_number))) {
      return NextResponse.json({ success: false, error: 'رقم الهوية يجب أن يتكون من 11 رقمًا' }, { status: 400 })
    }

    // تحقق من القسم إذا أُرسل أن يكون رقمًا صالحًا
    if (deptId != null && deptId !== '' && Number.isNaN(Number(deptId))) {
      return NextResponse.json({ success: false, error: 'معرف القسم غير صالح' }, { status: 400 })
    }
    // إذا أُرسل قسم، تحقق من وجوده أو حاوِل إيجاده بالاسم القادم من الواجهة
    let deptIdResolved: number | null = null
    if (deptId != null && deptId !== '') {
      const depIdNum2 = Number(deptId)
      if (Number.isNaN(depIdNum2)) {
        return NextResponse.json({ success: false, error: 'معرف القسم غير صالح' }, { status: 400 })
      }
      let depCheck2 = await query(`SELECT 1 FROM ${depSchema}.departments WHERE id = $1`, [depIdNum2])
      if (depCheck2.rows.length === 0) {
        const fallbackDeptName2: string | undefined = body?.department_name
        if (fallbackDeptName2 && fallbackDeptName2.trim()) {
          const byName2 = await query(`SELECT id, dept_name FROM ${depSchema}.departments WHERE TRIM(dept_name) ILIKE TRIM($1) LIMIT 1`, [fallbackDeptName2.trim()])
          if (byName2.rows.length > 0) {
            deptIdResolved = Number(byName2.rows[0].id)
            depCheck2 = await query(`SELECT 1 FROM ${depSchema}.departments WHERE id = $1`, [deptIdResolved])
          } else {
            // إنشاء القسم تلقائياً إذا لم يوجد
            const ins2 = await query(`
              INSERT INTO ${depSchema}.departments (dept_name)
              VALUES ($1)
              ON CONFLICT DO NOTHING
              RETURNING id
            `, [fallbackDeptName2.trim()])
            if (ins2.rows.length > 0) {
              deptIdResolved = Number(ins2.rows[0].id)
              depCheck2 = await query(`SELECT 1 FROM ${depSchema}.departments WHERE id = $1`, [deptIdResolved])
            }
          }
        }
        if (depCheck2.rows.length === 0) {
          const diag2 = await query(`
            SELECT
              current_database() AS db,
              current_schema() AS schema,
              (SELECT COUNT(*) FROM ${depSchema}.departments) AS dep_count,
              (SELECT array_agg(id ORDER BY id LIMIT 10) FROM ${depSchema}.departments) AS dep_ids,
              $1::text AS dep_schema
          `, [depSchema])
          const d2 = diag2.rows?.[0]
          return NextResponse.json({ success: false, error: 'القسم المحدد غير موجود في قاعدة البيانات', context: { department_id: deptIdResolved ?? depIdNum2, db: d2?.db, schema: d2?.schema, detected_schema: d2?.dep_schema, dep_count: d2?.dep_count, dep_ids: d2?.dep_ids } }, { status: 400 })
        }
      } else {
        deptIdResolved = depIdNum2
      }
    }

    let department_name_update: string | null = null
    if (deptIdResolved != null) {
      const dn = await query(`SELECT COALESCE(dept_name, '') AS dep_name FROM ${depSchema}.departments WHERE id = $1`, [deptIdResolved])
      department_name_update = dn.rows?.[0]?.dep_name || null
    }

    // محاولة ضبط قيد العلاقة الأجنبية (FK) على مخطط الأقسام المحدد
    try {
      await query(`
        ALTER TABLE employees
        DROP CONSTRAINT IF EXISTS fk_employees_department;
        ALTER TABLE employees
        ADD CONSTRAINT fk_employees_department FOREIGN KEY (department_id)
        REFERENCES ${depSchema}.departments(id) ON UPDATE CASCADE ON DELETE SET NULL;
      `)
    } catch (error) {
      console.error('Error setting FK constraint:', error)
    }

    const result = await query(
      `UPDATE employees SET
        name = $2,
        employee_number = $3,
        position = $4,
        department_id = $5,
        department = $6,
        account_id = $7,
        branch_id = $8,
        governorate_id = $9,
        court_id = $10,
        phone = $11,
        email = $12,
        address = $13,
        id_number = $14,
        salary = $15,
        hire_date = $16,
        status = $17,
        is_active = $18
       WHERE id = $1
       RETURNING *`,
      [
        Number(id),
        name || null,
        employee_number || null,
        position || null,
        deptIdResolved,
        department_name_update,
        account_id != null && account_id !== '' ? Number(account_id) : null,
        branch_id != null && branch_id !== '' ? Number(branch_id) : null,
        governorate_id != null && governorate_id !== '' ? Number(governorate_id) : null,
        court_id != null && court_id !== '' ? Number(court_id) : null,
        phone || null,
        email || null,
        address || null,
        id_number || null,
        salary != null && salary !== '' ? Number(salary) : null,
        hire_date ? new Date(hire_date) : null,
        status || null,
        isActive
      ]
    )

    return NextResponse.json({ success: true, message: 'تم تحديث الموظف بنجاح', data: result.rows[0] })
  } catch (error) {
    const err: any = error
    console.error('Error updating الموظفين:', err)
    if (err?.code === '23503') {
      return NextResponse.json({ success: false, error: 'فشل تحديث الموظف: القسم المحدد غير موجود' }, { status: 400 })
    }
    if (err?.code === '23505') {
      return NextResponse.json({ success: false, error: 'فشل تحديث الموظف: تعارض في قيمة فريدة' }, { status: 400 })
    }
    const msg = err?.message || 'فشل في تحديث الموظف'
    return NextResponse.json({ success: false, error: msg }, { status: 500 })
  }
}

// DELETE - حذف موظف
export async function DELETE(request: NextRequest) {
  try {
    await ensureTable()
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json({ success: false, error: 'معرف الموظف مطلوب' }, { status: 400 })
    }

    await query('DELETE FROM employees WHERE id = $1', [id])

    return NextResponse.json({ success: true, message: 'تم حذف الموظف بنجاح' })
  } catch (error) {
    console.error('Error deleting الموظفين:', error)
    return NextResponse.json({ success: false, error: 'فشل في حذف الموظف' }, { status: 500 })
  }
}