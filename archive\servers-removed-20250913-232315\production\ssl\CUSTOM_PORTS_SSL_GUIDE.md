# 🔐 دليل تثبيت SSL للمنافذ المخصصة - mohammi.com

## 📋 نظرة عامة
تثبيت شهادة SSL لـ `mohammi.com` على المنافذ المخصصة بدلاً من المنفذ الافتراضي 443 المحجوز.

### 🔌 **المنافذ المستخدمة:**
- **7443**: HTTPS لقاعدة البيانات `mohammi`
- **8914**: HTTPS لقاعدة البيانات `rubaie`
- **7080**: HTTP إعادة توجيه → 7443
- **8080**: HTTP إعادة توجيه → 8914

---

## 🚀 **التثبيت السريع**

### 1. **استخراج المفتاح الخاص:**
```bash
# استخرج ملف mohammi_com.zip في مجلد ssl
cd ssl
unzip mohammi_com.zip
# ابحث عن ملف .key
```

### 2. **تشغيل خادم SSL بـ Node.js:**
```bash
# تشغيل خادم SSL للمنافذ المخصصة
node ssl/ssl_custom_ports.js
```

### 3. **أو تثبيت على Nginx:**
```bash
# تشغيل سكريپت التثبيت (Linux)
sudo chmod +x ssl/install_ssl_custom_ports.sh
sudo ./ssl/install_ssl_custom_ports.sh
```

---

## 🌐 **الروابط بعد التثبيت**

### 🔐 **HTTPS (آمن):**
- `https://mohammi.com:7443` ← قاعدة البيانات mohammi
- `https://mohammi.com:8914` ← قاعدة البيانات rubaie
- `https://***********:7443` ← IP مباشر (mohammi)
- `https://***********:8914` ← IP مباشر (rubaie)

### 🔄 **HTTP (إعادة توجيه):**
- `http://mohammi.com:7080` → `https://mohammi.com:7443`
- `http://mohammi.com:8080` → `https://mohammi.com:8914`

---

## 🧪 **اختبار الشهادة**

### **اختبار SSL Labs:**
```
https://www.ssllabs.com/ssltest/analyze.html?d=mohammi.com:7443
https://www.ssllabs.com/ssltest/analyze.html?d=mohammi.com:8914
```

### **اختبار curl:**
```bash
curl -I https://mohammi.com:7443
curl -I https://mohammi.com:8914
```

### **اختبار المتصفح:**
- افتح `https://mohammi.com:7443`
- تحقق من أيقونة القفل 🔒
- اعرض تفاصيل الشهادة

---

## ⚙️ **تكوين Nginx (إضافي)**

إذا كنت تريد إضافة proxy للتطبيقات:

```nginx
# في ملف /etc/nginx/sites-available/mohammi-ssl
location / {
    proxy_pass http://127.0.0.1:7443;  # للتطبيق الفعلي
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

---

## 🔥 **إعدادات جدار الحماية**

### **Ubuntu/Debian:**
```bash
sudo ufw allow 7443/tcp
sudo ufw allow 8914/tcp
sudo ufw allow 7080/tcp
sudo ufw allow 8080/tcp
```

### **CentOS/RHEL:**
```bash
sudo firewall-cmd --permanent --add-port=7443/tcp
sudo firewall-cmd --permanent --add-port=8914/tcp
sudo firewall-cmd --permanent --add-port=7080/tcp
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --reload
```

---

## 🛠️ **استكشاف الأخطاء**

### **خطأ: "المفتاح الخاص غير موجود"**
```bash
# ابحث عن ملف .key في مجلد ssl
find ssl/ -name "*.key"
# أو استخرج ملف ZIP
unzip ssl/mohammi_com.zip -d ssl/
```

### **خطأ: "المنفذ مستخدم"**
```bash
# تحقق من المنافذ المستخدمة
netstat -tlnp | grep :7443
netstat -tlnp | grep :8914
# أوقف العملية المستخدمة للمنفذ
sudo kill -9 [PID]
```

### **خطأ: "تكوين Nginx خاطئ"**
```bash
# اختبار تكوين Nginx
sudo nginx -t
# عرض سجلات الأخطاء
sudo tail -f /var/log/nginx/error.log
```

---

## 📊 **مراقبة الأداء**

### **فحص حالة SSL:**
```bash
# فحص انتهاء صلاحية الشهادة
openssl x509 -in /etc/ssl/mohammi/mohammi_com.crt -noout -dates

# فحص الاتصال
openssl s_client -connect mohammi.com:7443 -servername mohammi.com
```

### **مراقبة سجلات Nginx:**
```bash
# سجلات الوصول
sudo tail -f /var/log/nginx/mohammi-7443.access.log
sudo tail -f /var/log/nginx/mohammi-8914.access.log

# سجلات الأخطاء
sudo tail -f /var/log/nginx/mohammi-7443.error.log
sudo tail -f /var/log/nginx/mohammi-8914.error.log
```

---

## 🔄 **تجديد الشهادة**

### **خطوات التجديد:**
1. احصل على شهادة جديدة من Sectigo/Name.com
2. استبدل الملفات في `/etc/ssl/mohammi/`
3. أعد تحميل Nginx: `sudo systemctl reload nginx`
4. اختبر الشهادة الجديدة

### **أتمتة التجديد:**
```bash
# إنشاء cron job للتحقق من انتهاء الصلاحية
echo "0 0 1 * * /path/to/check_ssl_expiry.sh" | sudo crontab -
```

---

## 📞 **الدعم والمساعدة**

### **إذا واجهت مشاكل:**
1. **تحقق من سجلات النظام**
2. **تأكد من صحة ملفات الشهادة**
3. **اختبر الاتصال محلياً أولاً**
4. **تحقق من إعدادات DNS**

### **معلومات مهمة:**
- **IP الخارجي**: ***********
- **الدومين**: mohammi.com
- **المنافذ**: 7443, 8914 (بدلاً من 443)
- **الشهادة**: Sectigo SSL

---

## ✅ **قائمة التحقق النهائية**

- [ ] تم استخراج المفتاح الخاص من ZIP
- [ ] تم تثبيت الشهادة على المنافذ المخصصة
- [ ] تم اختبار الروابط: https://mohammi.com:7443
- [ ] تم اختبار الروابط: https://mohammi.com:8914
- [ ] تم فتح المنافذ في جدار الحماية
- [ ] تم اختبار SSL Labs
- [ ] تم التحقق من انتهاء صلاحية الشهادة

🎉 **تهانينا! SSL يعمل على المنافذ المخصصة بنجاح!**
