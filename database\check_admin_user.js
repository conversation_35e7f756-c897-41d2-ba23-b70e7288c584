// فحص بيانات المستخدم admin
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'mohammi',
  user: 'postgres',
  password: 'yemen123'
};

async function checkAdminUser() {
  const client = new Client(dbConfig);
  
  try {
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات');

    // البحث عن المستخدم admin
    console.log('\n🔍 البحث عن المستخدم admin...');
    const adminUser = await client.query(`
      SELECT id, username, email, role, created_date 
      FROM users 
      WHERE username = 'admin' OR role = 'admin'
    `);
    
    if (adminUser.rows.length > 0) {
      console.log('\n👤 بيانات المستخدم admin:');
      adminUser.rows.forEach((user, index) => {
        console.log(`\n   ${index + 1}. ID: ${user.id}`);
        console.log(`      اسم المستخدم: ${user.username}`);
        console.log(`      البريد الإلكتروني: ${user.email || 'غير محدد'}`);
        console.log(`      الدور: ${user.role}`);
        console.log(`      تاريخ الإنشاء: ${user.created_date}`);
      });
    } else {
      console.log('\n❌ لم يتم العثور على مستخدم admin');
      
      // إنشاء مستخدم admin جديد
      console.log('\n🔧 إنشاء مستخدم admin جديد...');
      const bcrypt = require('bcrypt');
      const hashedPassword = await bcrypt.hash('admin123', 10);
      
      const newAdmin = await client.query(`
        INSERT INTO users (username, password_hash, email, role)
        VALUES ('admin', $1, '<EMAIL>', 'admin')
        RETURNING id, username, email, role
      `, [hashedPassword]);
      
      console.log('✅ تم إنشاء مستخدم admin جديد:');
      console.log(`   اسم المستخدم: ${newAdmin.rows[0].username}`);
      console.log(`   كلمة المرور: admin123`);
      console.log(`   البريد الإلكتروني: ${newAdmin.rows[0].email}`);
      console.log(`   الدور: ${newAdmin.rows[0].role}`);
    }

    // عرض جميع المستخدمين
    console.log('\n📋 جميع المستخدمين في النظام:');
    const allUsers = await client.query(`
      SELECT id, username, email, role, created_date 
      FROM users 
      ORDER BY created_date DESC
    `);
    
    if (allUsers.rows.length > 0) {
      allUsers.rows.forEach((user, index) => {
        console.log(`\n   ${index + 1}. ${user.username} (${user.role})`);
        console.log(`      البريد: ${user.email || 'غير محدد'}`);
        console.log(`      تاريخ الإنشاء: ${user.created_date}`);
      });
    } else {
      console.log('   لا توجد مستخدمين في النظام');
    }

    console.log(`\n📊 إجمالي المستخدمين: ${allUsers.rowCount}`);

  } catch (error) {
    console.error('❌ خطأ:', error.message);
    
    // في حالة عدم وجود bcrypt، استخدم كلمة مرور بسيطة
    if (error.message.includes('bcrypt')) {
      console.log('\n⚠️ bcrypt غير متوفر، سيتم استخدام كلمة مرور بسيطة...');
      
      try {
        const simpleAdmin = await client.query(`
          INSERT INTO users (username, password_hash, email, role)
          VALUES ('admin', 'admin123', '<EMAIL>', 'admin')
          ON CONFLICT (username) DO NOTHING
          RETURNING id, username, email, role
        `);
        
        if (simpleAdmin.rows.length > 0) {
          console.log('✅ تم إنشاء مستخدم admin:');
          console.log(`   اسم المستخدم: admin`);
          console.log(`   كلمة المرور: admin123`);
        }
      } catch (insertError) {
        console.error('❌ خطأ في إنشاء المستخدم:', insertError.message);
      }
    }
  } finally {
    await client.end();
  }
}

checkAdminUser();
