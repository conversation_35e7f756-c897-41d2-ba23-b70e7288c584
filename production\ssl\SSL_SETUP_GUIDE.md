# 🔐 دليل إعداد شهادة SSL لـ mohammi.com

## 📋 نظرة عامة
هذا الدليل يوضح كيفية إنشاء وتثبيت شهادة SSL لموقع `mohammi.com` باستخدام Name.com أو أي مزود شهادات آخر.

---

## 🛠️ الطريقة الأولى: استخدام PowerShell + OpenSSL

### المتطلبات:
- Windows PowerShell
- OpenSSL مثبت ([تحميل من هنا](https://slproweb.com/products/Win32OpenSSL.html))

### الخطوات:
```powershell
# 1. تشغيل سكريپت إنشاء الشهادة
.\ssl\generate_csr.ps1

# 2. نسخ محتوى ملف CSR
Get-Content ssl\mohammi.csr | clip
```

---

## 🛠️ الطريقة الثانية: استخدام Node.js (الأسهل)

### المتطلبات:
- Node.js مثبت

### الخطوات:
```bash
# 1. تشغيل سكريپت إنشاء الشهادة
node ssl/generate_ssl_nodejs.js

# 2. نسخ محتوى CSR من الإخراج
```

---

## 📝 الملفات المنشأة

بعد تشغيل أي من السكريپتين، ستحصل على:

| الملف | الوصف | الاستخدام |
|-------|--------|-----------|
| `mohammi.key` | المفتاح الخاص | ⚠️ **سري جداً** - لا تشاركه |
| `mohammi.csr` | طلب الشهادة | 📤 أرسله إلى Name.com |
| `mohammi.crt` | شهادة تجريبية | 🧪 للاختبار المحلي |
| `mohammi.conf` | ملف التكوين | 📋 للمرجع |
| `mohammi.pem` | ملف مجمع | 🔗 للخوادم التي تتطلب ملف واحد |

---

## 🌐 خطوات Name.com

### 1. تسجيل الدخول إلى Name.com
- اذهب إلى [Name.com](https://name.com)
- سجل دخولك إلى حسابك

### 2. طلب شهادة SSL
- اذهب إلى قسم "SSL Certificates"
- اختر نوع الشهادة المناسب
- أدخل النطاق: `mohammi.com`

### 3. إدخال CSR
- انسخ محتوى ملف `mohammi.csr`
- الصقه في حقل "Certificate Signing Request"
- تأكد من تضمين الأسطر:
  ```
  -----BEGIN CERTIFICATE REQUEST-----
  [محتوى الشهادة]
  -----END CERTIFICATE REQUEST-----
  ```

### 4. التحقق من الملكية
Name.com سيطلب التحقق من ملكية النطاق بإحدى الطرق:
- **البريد الإلكتروني**: إرسال رسالة إلى <EMAIL>
- **ملف DNS**: إضافة سجل TXT
- **ملف HTTP**: رفع ملف تحقق

### 5. تحميل الشهادة
بعد التحقق، ستحصل على:
- `mohammi.crt` - الشهادة الرئيسية
- `intermediate.crt` - الشهادة الوسطية
- `bundle.crt` - الشهادة المجمعة

---

## 💾 حفظ الشهادة الموقعة

```bash
# احفظ الشهادة الموقعة من Name.com
# في ملف mohammi_signed.crt
notepad ssl/mohammi_signed.crt
```

---

## 🚀 تشغيل خادم HTTPS

```bash
# تشغيل خادم اختبار HTTPS
node ssl/setup_https_server.js
```

سيعمل الخادم على:
- **HTTPS**: `https://localhost:8443` أو `https://mohammi.com:8443`
- **HTTP**: إعادة توجيه تلقائي إلى HTTPS

---

## 🔧 تكوين خادم الإنتاج

### Next.js (الحالي)
```javascript
// في ملف server.js
const https = require('https');
const fs = require('fs');
const next = require('next');

const app = next({ dev: false });
const handle = app.getRequestHandler();

const httpsOptions = {
  key: fs.readFileSync('ssl/mohammi.key'),
  cert: fs.readFileSync('ssl/mohammi_signed.crt')
};

app.prepare().then(() => {
  https.createServer(httpsOptions, (req, res) => {
    handle(req, res);
  }).listen(443, () => {
    console.log('✅ HTTPS Server running on port 443');
  });
});
```

### Nginx (إعداد Reverse Proxy)
```nginx
server {
    listen 443 ssl;
    server_name mohammi.com www.mohammi.com;
    
    ssl_certificate /path/to/ssl/mohammi_signed.crt;
    ssl_certificate_key /path/to/ssl/mohammi.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    
    location / {
        proxy_pass http://localhost:7443;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# إعادة توجيه HTTP إلى HTTPS
server {
    listen 80;
    server_name mohammi.com www.mohammi.com;
    return 301 https://$server_name$request_uri;
}
```

---

## 🔍 اختبار الشهادة

### 1. اختبار محلي
```bash
# اختبار الاتصال
curl -k https://localhost:8443

# فحص الشهادة
openssl s_client -connect localhost:8443 -servername mohammi.com
```

### 2. اختبار عبر الإنترنت
- [SSL Labs Test](https://www.ssllabs.com/ssltest/)
- [SSL Checker](https://www.sslshopper.com/ssl-checker.html)

---

## 🛡️ أفضل الممارسات الأمنية

### 1. حماية المفتاح الخاص
```bash
# تعيين صلاحيات محدودة للمفتاح الخاص
chmod 600 ssl/mohammi.key
```

### 2. تجديد الشهادة
- معظم الشهادات صالحة لسنة واحدة
- اضبط تذكير قبل انتهاء الصلاحية بـ 30 يوم
- استخدم نفس CSR للتجديد

### 3. النسخ الاحتياطي
```bash
# إنشاء نسخة احتياطية مشفرة
tar -czf ssl_backup_$(date +%Y%m%d).tar.gz ssl/
gpg -c ssl_backup_$(date +%Y%m%d).tar.gz
```

---

## 🚨 استكشاف الأخطاء

### خطأ: "Private key does not match certificate"
```bash
# التحقق من تطابق المفتاح والشهادة
openssl rsa -in mohammi.key -pubout -outform PEM | sha256sum
openssl x509 -in mohammi_signed.crt -pubkey -noout -outform PEM | sha256sum
# يجب أن تكون النتيجتان متطابقتين
```

### خطأ: "Certificate chain incomplete"
```bash
# دمج الشهادة مع الشهادة الوسطية
cat mohammi_signed.crt intermediate.crt > mohammi_full_chain.crt
```

### خطأ: "Port 443 already in use"
```bash
# العثور على العملية التي تستخدم المنفذ
netstat -ano | findstr :443
# إيقاف العملية
taskkill /PID [رقم_العملية] /F
```

---

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من سجلات الأخطاء
2. تأكد من صحة ملفات SSL
3. اختبر الاتصال المحلي أولاً
4. تحقق من إعدادات DNS وجدار الحماية

---

## ✅ قائمة التحقق النهائية

- [ ] تم إنشاء CSR بنجاح
- [ ] تم إرسال CSR إلى Name.com
- [ ] تم التحقق من ملكية النطاق
- [ ] تم تحميل الشهادة الموقعة
- [ ] تم حفظ الشهادة في `mohammi_signed.crt`
- [ ] تم اختبار الخادم محلياً
- [ ] تم تكوين خادم الإنتاج
- [ ] تم اختبار الموقع عبر HTTPS
- [ ] تم إعداد إعادة التوجيه من HTTP إلى HTTPS

🎉 **تهانينا! موقع mohammi.com الآن محمي بـ SSL!**
